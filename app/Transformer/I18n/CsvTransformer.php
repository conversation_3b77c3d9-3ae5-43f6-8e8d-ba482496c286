<?php

declare(strict_types=1);

namespace app\Transformer\I18n;

use app\Transformer\Transformer;
use League\Csv\Writer;
use SplTempFileObject;

class CsvTransformer implements Transformer
{
    private const CSV_HEADERS = ['key', 'type', 'domain', 'translation'];

    /** @readonly */
    private Writer $writer;

    public function __construct()
    {
        $writer = Writer::createFromFileObject(new SplTempFileObject());
        $writer->setOutputBOM(Writer::BOM_UTF8);
        $this->writer = $this->writeHeader($writer);
    }

    public function transform(array $data): string
    {
        $this->writer->insertAll(array_map(function ($row): array {
            return $this->item($row);
        }, $data));

        return $this->writer->toString();
    }

    public function item(array $item): array
    {
        return [
            'key' => $item['key'],
            'type' => $item['type'],
            'domain' => $item['domain'],
            'translation' => $item['translation'],
        ];
    }

    private function writeHeader(Writer $writer): Writer
    {
        $writer->insertOne(self::CSV_HEADERS);

        return $writer;
    }
}
