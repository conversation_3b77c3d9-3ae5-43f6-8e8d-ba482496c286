<?php

namespace app\Transformer\I18n;

use app\models\multiLanguage\entities\Translation;
use app\Traits\I18n\TranslationTrait;
use app\Transformer\Transformer;
use Symfony\Component\Yaml\Yaml;

class YamlTransformer implements Transformer
{
    use TranslationTrait;

    /** @var string */
    private $locale;

    public function __construct(string $locale)
    {
        $this->locale = strtolower($locale);
    }

    public function transform(array $data): string
    {
        return Yaml::dump([
            'entity' => Translation::class,
            'priority' => 2,
            'data' => array_map(function (array $row) {
                return $this->item($row);
            }, $data),
        ]);
    }

    public function item(array $item): array
    {
        $key = $this->escapeKey($item['key']);

        return [
            'field' => [
                'language' => '@' . $this->locale,
                'placeholder' => '@' . strtolower(implode(':', [$item['type'], $item['domain'], $key])),
                'system_translation' => $item['translation'],
            ],
        ];
    }
}
