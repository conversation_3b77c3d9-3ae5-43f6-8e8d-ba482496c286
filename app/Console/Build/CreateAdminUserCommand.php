<?php

declare(strict_types=1);

namespace app\Console\Build;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

#[AsCommand('build:create-admin')]
final class CreateAdminUserCommand extends Command
{
    /** @readonly */
    private Connection $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
        parent::__construct();
    }

    protected function configure()
    {
        $this->addArgument('login', InputArgument::REQUIRED, 'The login for the user.');
        $this->addArgument('email', InputArgument::REQUIRED, 'The email for the user.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->userExists()) {
            $output->writeln('User with recordid 1 already exists. Aborting.');

            return self::FAILURE;
        }

        $login = $input->getArgument('login');
        $email = $input->getArgument('email');

        $output->writeln("Adding {$login} ({$email})...");

        $this->db->beginTransaction();

        try {
            $this->createUser($login, $email);
            $this->createUserParameter($input->getArgument('login'));
        } catch (Throwable $e) {
            $this->db->rollBack();

            throw $e;
        }

        $this->db->commit();

        $output->writeln('Successfully added user as an administrator.');

        return self::SUCCESS;
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    private function createUser(string $login, string $email): void
    {
        $qb = $this->db->createQueryBuilder();

        $qb->insert('users_main')
            ->values([
                'recordid' => 1,
                'use_title' => $qb->expr()->literal('Mr'),
                'use_forenames' => $qb->expr()->literal('Datix'),
                'use_surname' => $qb->expr()->literal('Administrator'),
                'login' => ':login',
                'initials' => $qb->expr()->literal('DA'),
                'use_email' => ':email',
                'lockout' => $qb->expr()->literal('N'),
            ])
            ->setParameters([
                'login' => $login,
                'email' => $email,
            ])
            ->executeStatement();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    private function createUserParameter(string $login): void
    {
        $qb = $this->db->createQueryBuilder();

        $qb->insert('user_parms')
            ->values([
                'parameter' => $qb->expr()->literal('FULL_ADMIN'),
                'login' => ':login',
                'parmvalue' => $qb->expr()->literal('Y'),
            ])
            ->setParameter('login', $login)
            ->executeStatement();
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function userExists(): bool
    {
        $qb = $this->db->createQueryBuilder();

        $count = (int) $qb->select('count(*)')
            ->from('users_main')
            ->where('recordid = 1')
            ->executeQuery()
            ->fetchOne();

        return $count > 0;
    }
}
