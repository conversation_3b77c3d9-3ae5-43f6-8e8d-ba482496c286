<?php

declare(strict_types=1);

namespace app\Console\Build;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

#[AsCommand('build:initialise-database')]
final class InitialiseDatabaseCommand extends Command
{
    private const INSERT_COMMANDS = [
        'INSERT INTO globals (parameter, parmvalue) VALUES (\'FORM_MIGRATE_VERSION\', \'16.0\')',
        'INSERT INTO module_licence (module_id, access) SELECT 1, \'false\' UNION ALL SELECT 2, \'false\' UNION ALL SELECT 3, \'false\' UNION ALL SELECT 30, \'false\' UNION ALL SELECT 48, \'false\'',
    ];

    /** @readonly */
    private Connection $db;
    private array $schemaDeployCommands;
    private array $migrationSeedCommands;

    public function __construct(Connection $db)
    {
        $this->schemaDeployCommands = require __DIR__ . '/../../../build/schema-deploy-sql-commands.php';
        $this->migrationSeedCommands = require __DIR__ . '/../../../build/migration-seed-sql-commands.php';

        $this->db = $db;
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $count = $this->getTableCount();

        if ($count > 0) {
            $output->writeln("Database must be empty. {$count} tables found.");

            return self::SUCCESS;
        }

        $output->writeln('Starting to initialise database...');

        $this->db->beginTransaction();

        try {
            $this->processSqlCommands();
        } catch (Throwable $e) {
            $this->db->rollBack();

            throw $e;
        }

        $this->db->commit();

        $output->writeln('Successfully initialised database.');

        return self::SUCCESS;
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function getTableCount(): int
    {
        $qb = $this->db->createQueryBuilder();

        return (int) $qb->select('count(*)')
            ->from('information_schema.tables')
            ->where('table_type = :tableType')
            ->setParameter('tableType', 'base table')
            ->executeQuery()
            ->fetchOne();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    private function processSqlCommands(): void
    {
        foreach ($this->schemaDeployCommands as $sql) {
            $this->db->executeStatement($sql);
        }

        foreach ($this->migrationSeedCommands as $sql) {
            $this->db->executeStatement($sql);
        }

        foreach (self::INSERT_COMMANDS as $sql) {
            $this->db->executeStatement($sql);
        }
    }
}
