<?php

declare(strict_types=1);

namespace app\Console\Build;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

#[AsCommand('build:add-default-data')]
final class AddDefaultDataCommand extends Command
{
    public const EN_GB_LANGAUGE_ID = 7;
    private const DATA = [
        'MEDCLA' => [
            1 => 'Narcotics',
            2 => 'Depressants',
            3 => 'Stimulants',
            4 => 'Hallucinogens',
            5 => 'Anabolic steroids',
        ],
        'MEDBRA' => [
            1 => 'Ibuprin',
            2 => 'Lipitor',
            3 => 'Nexium',
            4 => 'Actos',
        ],
        'MEDMAN' => [
            1 => 'Ibuprofen',
            2 => 'Atorvastatin',
            3 => 'Esomeprazole',
            4 => 'Pioglitazone',
        ],
        'DRUG' => [
            1 => 'Ibuprofen',
            2 => 'Atorvastatin',
            3 => 'Esomeprazole',
            4 => 'Pioglitazone',
        ],
        'MEDTYP' => [
            1 => 'Analgesic',
            2 => 'Psychedelic',
            3 => 'Anaesthetic',
            4 => 'Antacid',
            5 => 'Antidepressant',
            6 => 'Eugeroic',
            7 => 'Antibiotic',
            8 => 'Emollient',
            9 => 'Vaccine',
        ],
    ];

    /** @readonly */
    private Connection $db;
    private int $skipped = 0;

    public function __construct(Connection $db)
    {
        $this->db = $db;

        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->db->beginTransaction();

        try {
            $this->insertData($output);
        } catch (Throwable $e) {
            $this->db->rollBack();

            throw $e;
        }

        $this->db->commit();

        if ($this->skipped === count(self::DATA)) {
            $output->writeln('Completed. No data imported.');
        } else {
            $output->writeln('Successfully imported data.');
        }

        return self::SUCCESS;
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function insertData(OutputInterface $output): void
    {
        $language = self::EN_GB_LANGAUGE_ID;

        foreach (self::DATA as $codeType => $values) {
            if ($this->getCodeTypeCount($codeType) > 0) {
                ++$this->skipped;

                $output->writeln("No codes imported into code types table for type `{$codeType}`: data already existed in table.");

                continue;
            }

            foreach ($values as $code => $description) {
                $this->insertCodeType($codeType, $code, $description);

                if ($this->getCodeTypeDescriptionCount($codeType, $code, $language) === 0) {
                    $this->insertCodeTypeDescription($codeType, $code, $description, $language);
                }
            }
        }
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    private function getCodeTypeCount(string $codeType): int
    {
        $qb = $this->db->createQueryBuilder();

        return (int) $qb->select('count(*)')
            ->from('code_types')
            ->where('cod_type = :codeType')
            ->setParameter('codeType', $codeType)
            ->executeQuery()
            ->fetchOne();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    private function insertCodeType(string $codeType, int $code, string $description): void
    {
        $qb = $this->db->createQueryBuilder();

        $qb->insert('code_types')
            ->values([
                'cod_code' => ':codCode',
                'cod_descr' => ':codDescr',
                'cod_type' => ':codType',
            ])
            ->setParameters([
                'codCode' => (string) $code,
                'codDescr' => $description,
                'codType' => $codeType,
            ])
            ->executeStatement();
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    private function insertCodeTypeDescription(
        string $codeType,
        int $code,
        string $description,
        int $language
    ): void {
        $qb = $this->db->createQueryBuilder();

        $qb->insert('code_types_descr')
            ->values([
                'cod_code' => ':codCode',
                'cod_descr' => ':codDescr',
                'cod_type' => ':codType',
                'language' => ':language',
            ])
            ->setParameters([
                'codCode' => (string) $code,
                'codDescr' => $description,
                'codType' => $codeType,
                'language' => $language,
            ])
            ->executeStatement();
    }

    private function getCodeTypeDescriptionCount(string $codeType, int $code, int $language): int
    {
        $qb = $this->db->createQueryBuilder();

        return (int) $qb->select('count(*)')
            ->from('code_types_descr')
            ->where('cod_type = :codType')
            ->andWhere('cod_code = :codCode')
            ->andWhere('language = :language')
            ->setParameters([
                'codType' => $codeType,
                'codCode' => (string) $code,
                'language' => $language,
            ])
            ->executeQuery()
            ->fetchOne();
    }
}
