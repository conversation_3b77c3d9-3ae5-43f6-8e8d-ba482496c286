<?php

namespace app\Console\Docs;

use OpenApi\Generator;
use OpenApi\Loggers\ConsoleLogger;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Filesystem\Filesystem;

#[AsCommand('docs:api:generate')]
final class GenerateApiDocs extends Command
{
    private const string OUTPUT = './api/docs/openapi.json';

    protected function configure(): void
    {
        $this->setDescription('Automatically generate the OpenAPI yaml from annotations')
            ->addOption('output', null, InputOption::VALUE_OPTIONAL, 'relative path of file to write the OpenApi to');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $api = Generator::scan(
            [CONSOLE_DIR . 'app/models/', CONSOLE_DIR . '/api/'],
            ['logger' => new ConsoleLogger($output->isDebug())],
        );

        $filesystem = new Filesystem();
        $filesystem->dumpFile($input->getOption('output') ?? self::OUTPUT, $api->toJson());

        return self::SUCCESS;
    }
}
