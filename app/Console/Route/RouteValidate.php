<?php

declare(strict_types=1);

namespace app\Console\Route;

use ReflectionClass;
use ReflectionException;
use src\framework\model\Route;
use src\framework\model\RouteInterface;
use src\framework\session\UserSession;
use src\Router;
use src\system\container\facade\Container;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use function sprintf;

#[AsCommand('routes:validate')]
final class RouteValidate extends Command
{
    public function configure()
    {
        $this->setDescription('Validate routes')
            ->setHelp('Validates all none legacy routes are correctly accessible');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $hasIssue = false;
        foreach ($this->getFilterRoutes() as $name => $route) {
            try {
                $reflection = new ReflectionClass($route->getController());
                /** @noinspection PhpExpressionResultUnusedInspection */
                $reflection->getMethod($name);
            } catch (ReflectionException $e) {
                $hasIssue = true;
                $output->writeln(
                    sprintf(
                        '<error>Cannot access method `%s` in class `%s`</error>',
                        $route->getName(),
                        $route->getController(),
                    ),
                );
            }
        }

        if (!$hasIssue) {
            $output->writeln('<info>No Issues found in routes</info>');
        }

        return !$hasIssue ? self::SUCCESS : self::FAILURE;
    }

    private function getAnonymousClass(): Router
    {
        return new class (Container::get(UserSession::class)) extends Router {
            public function getRoutes(): array
            {
                return $this->routes;
            }
        };
    }

    private function getFilterRoutes(): array
    {
        $routeAccess = $this->getAnonymousClass();

        return array_filter($routeAccess->getRoutes(), static function (RouteInterface $route): bool {
            return $route instanceof Route;
        });
    }
}
