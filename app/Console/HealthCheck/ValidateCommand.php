<?php

declare(strict_types=1);

namespace app\Console\HealthCheck;

use app\models\framework\config\DatixConfig;
use Doctrine\DBAL\DriverManager;
use Exception;
use GuzzleHttp\Client as GuzzleHttpClient;
use MongoDB\Client;
use PDO;
use src\system\container\facade\Container;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function sprintf;

#[AsCommand('health-check:validate')]
final class ValidateCommand extends Command
{
    /** @readonly */
    private OutputInterface $output;

    /** @readonly */
    private InputInterface $input;

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->input = $input;
        $this->output = $output;

        $this->output->writeln("\nTesting Container connections\n");

        $this->validateDBAL();
        $this->validatePDO();
        $this->validateMongo();
        $this->validateCarlton();

        $this->output->writeln("\n\n");

        return self::SUCCESS;
    }

    protected function configure(): void
    {
        $this->setDescription('Validates if container is being able to access Database and other containers')
            ->addOption('carlton-endpoint', null, InputOption::VALUE_OPTIONAL, 'Endpoint to validate Carlton connection', '/healthcheck');
    }

    private function DBCredentials(): array
    {
        $config = Container::get(DatixConfig::class);

        return [
            'dbname' => $config->getDbName(),
            'user' => $config->getDbUsername(),
            'password' => $config->getDbPassword(),
            'host' => $config->getDbServerName(),
            'driver' => 'pdo_sqlsrv',
        ];
    }

    private function validateDBAL(): void
    {
        $this->output->write('<info>Validating DBAL Connection</info> ... ');

        $dbCredentials = $this->DBCredentials();

        try {
            $conn = DriverManager::getConnection($dbCredentials);
            $conn->connect();

            $this->output->write('<info>OK</info>');
        } catch (Exception $e) {
            $this->output->write(
                sprintf(
                    "<error>Failed to connect to database: %s</error>\n<message>ServerName: %s / Database: %s / User: %s</message>",
                    $e->getMessage(),
                    $dbCredentials['host'],
                    $dbCredentials['dbname'],
                    $dbCredentials['user'],
                ),
            );
        } finally {
            if (isset($conn)) {
                $conn->close();
            }
        }

        $this->output->write("\n");
    }

    private function validatePDO(): void
    {
        $this->output->write('<info>Validating PDO Connection</info> ... ');

        $dbCredentials = $this->DBCredentials();

        try {
            $pdo = new PDO('sqlsrv:Database=' . $dbCredentials['dbname'] . ';Server=' . $dbCredentials['host'], $dbCredentials['user'], $dbCredentials['password']);
            $pdo->setAttribute(PDO::SQLSRV_ATTR_ENCODING, PDO::SQLSRV_ENCODING_UTF8);

            $this->output->write('<info>OK</info>');
        } catch (Exception $e) {
            $this->output->write(
                sprintf(
                    "<error>Failed to connect to database: %s</error>\n<message>ServerName: %s / Database: %s / User: %s</message>",
                    $e->getMessage(),
                    $dbCredentials['host'],
                    $dbCredentials['dbname'],
                    $dbCredentials['user'],
                ),
            );
        } finally {
            if (isset($pdo)) {
                $pdo = null;
            }
        }

        $this->output->write("\n");
    }

    private function validateMongo(): void
    {
        $this->output->write('<info>Validating Mongo Connection</info> ... ');

        $mongoConnectionUrl = getenv('MONGO_URI');

        if (empty($mongoConnectionUrl)) {
            $this->output->write('<comment>MONGO_URI - Not configured</comment>');

            return;
        }

        try {
            $mongoClient = new Client($mongoConnectionUrl, [
                'connectTimeoutMS' => 500,
                'socketTimeoutMS' => 5000,
                'w' => 'majority',
                'wtimeout' => 500,
            ]);
            $mongoClient->listDatabases();

            $this->output->write('<info>OK</info>');
        } catch (Exception $e) {
            $this->output->write(
                sprintf(
                    '<error>Failed to connect to mongo: %s</error>',
                    $e->getMessage(),
                ),
            );
        }

        $this->output->write("\n");
    }

    private function validateCarlton(): void
    {
        $this->output->write('<info>Validating Carlton Connection</info> ... ');

        $url = getenv('CARLTON_BASE_API_URL');

        if (empty($url)) {
            $this->output->write('<comment>CARLTON_BASE_API_URL - Not configured</comment>');

            return;
        }

        try {
            $client = new GuzzleHttpClient([
                'base_uri' => $url,
                'headers' => [
                    'Accept' => 'application/json',
                ],
            ]);

            $endpoint = $this->input->getOption('carlton-endpoint');
            $response = $client->get($endpoint);

            $this->output->write(
                sprintf('<info>OK (%d) [%s]</info>', $response->getStatusCode(), $endpoint),
            );
        } catch (Exception $e) {
            $this->output->write(
                sprintf(
                    '<error>Failed to connect to carlton: %s</error>',
                    $e->getMessage(),
                ),
            );
        }

        $this->output->write("\n");
    }
}
