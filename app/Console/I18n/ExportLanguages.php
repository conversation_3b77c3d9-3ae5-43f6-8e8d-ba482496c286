<?php

namespace app\Console\I18n;

use app\models\language\entities\LanguageEntity;
use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Yaml\Yaml;

#[AsCommand('tools:i18n:export-languages')]
final class ExportLanguages extends Command
{
    private const IMPORT_PRIORITY = 0;

    /** @var Connection */
    private $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    public function configure()
    {
        $this->setDescription('Exports languages table to yaml format needed by `tools:i18n:update-translations` to resynchronise language file with the database');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $data = $this->connection->fetchAllAssociative('select code, [description], rtl from languages order by [code]');
        $dump = Yaml::dump([
            'entity' => LanguageEntity::class,
            'priority' => self::IMPORT_PRIORITY,
            'data' => array_map(static function (array $item): array {
                return [
                    'field' => [
                        'code' => $item['code'],
                        'description' => $item['description'],
                        'rtl' => (int) $item['rtl'] === 1,
                    ],
                    'ref' => strtolower($item['code']),
                ];
            }, $data),
        ]);
        $output->write($dump);

        return 0;
    }
}
