<?php

namespace app\Console\I18n;

use app\models\multiLanguage\entities\Placeholder;
use app\Traits\I18n\TranslationTrait;
use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Yaml\Yaml;

#[AsCommand('tools:i18n:export-placeholders')]
final class ExportPlaceholders extends Command
{
    use TranslationTrait;
    private const IMPORT_PRIORITY = 1;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Exports i18n_placeholder table by domain to yaml format needed by `tools:i18n:update-translations` to resynchronise placeholder files with the database')
            ->addArgument('domain', InputArgument::REQUIRED, 'domain of the placeholders');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $domain = $input->getArgument('domain');

        $data = $this->transformer($this->getPlaceholders($domain));

        $output->write(Yaml::dump($data));

        return 0;
    }

    private function getPlaceholders(string $domain): array
    {
        return $this->connection->fetchAllAssociative(
            'SELECT
                [key],
                [type],
                [domain]
            FROM i18n_placeholder
            WHERE [domain] LIKE :domain
            ORDER BY [key];',
            ['domain' => $domain],
        );
    }

    /**
     * transforms data into the structure need for placeholder yaml use in `tools:i18n:update-translations` command.
     */
    private function transformer(array $data): array
    {
        $collection = [];
        foreach ($data as $item) {
            $collection[] = [
                'field' => [
                    'domain' => $item['domain'],
                    'type' => $item['type'],
                    'key' => $item['key'],
                ],
                'ref' => $this->generatePlaceholderRef($item['type'], $item['domain'], $item['key']),
            ];
        }

        return [
            'entity' => Placeholder::class,
            'priority' => self::IMPORT_PRIORITY,
            'data' => $collection,
        ];
    }
}
