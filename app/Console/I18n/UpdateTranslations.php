<?php

namespace app\Console\I18n;

use app\framework\DoctrineEntityManagerFactory;
use app\Hydrator\LanguageHydrate;
use app\Hydrator\LanguageTranslationHydrator;
use app\Hydrator\PlaceholderHydrator;
use app\Hydrator\TranslationHydrator;
use app\models\language\entities\LanguageEntity;
use app\models\language\entities\LanguagesTranslation;
use app\models\multiLanguage\entities\Placeholder;
use app\models\multiLanguage\entities\Translation;
use ArrayObject;
use Doctrine\ORM\EntityManager;
use InvalidArgumentException;
use OutOfBoundsException;
use Psr\SimpleCache\CacheInterface;
use ReflectionClass;
use RuntimeException;
use SplFileInfo;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Yaml\Exception\ParseException;
use Symfony\Component\Yaml\Yaml;
use Throwable;

use function count;

use const ARRAY_FILTER_USE_BOTH;

/**
 * UpdateTranslations command reads fixture translations from /db/data, sorts this into hierarchical order and then
 * updates the database via Doctrine entities. keeping the database upto date with the fixture files.
 */
#[AsCommand('tools:i18n:update-translations')]
final class UpdateTranslations extends Command
{
    private const FILE_NAMES = ['*.yml', '*.yaml'];

    /** @readonly */
    private CacheInterface $cache;

    /** @readonly */
    private EntityManager $entityManager;

    /** @readonly */
    private array $mappings = [];

    public function __construct(CacheInterface $cache)
    {
        $this->cache = $cache;
        $this->entityManager = (new DoctrineEntityManagerFactory())->getInstance();
        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Update i18n translations from yaml files');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln('Loading fixture files');
        $fixtures = $this->load(CONSOLE_DIR . 'db/data/', $output);

        $output->writeln('Processing files');

        try {
            $this->entityManager->getConnection()->beginTransaction();
            foreach ($fixtures as $data) {
                $this->processData($data, $output);
            }
            $this->entityManager->getConnection()->commit();
            $this->cache->clear();
        } catch (Throwable $e) {
            $this->entityManager->getConnection()->rollBack();
            $output->writeln('<error>Error: ' . $e->getMessage() . '</error>');

            throw $e;
        }

        return 0;
    }

    /**
     * loader function.
     */
    private function loadFiles(string $directory): Finder
    {
        return (new Finder())->files()
            ->in($directory)
            ->name(self::FILE_NAMES)
            ->sortByName();
    }

    /**
     * Loader function.
     */
    private function yamlToArray(SplFileInfo $file): ArrayObject
    {
        try {
            $filename = $file->getPathname();
            $parse = Yaml::parseFile($filename, Yaml::PARSE_OBJECT);
            $parse['filename'] = $filename;

            return new ArrayObject($parse);
        } catch (ParseException $e) {
            throw new ParseException(sprintf("Unable to parse YAML %s\n%s", $file->getPathname(), $e->getMessage()));
        }
    }

    /**
     * loader function.
     */
    private function load(string $directory, OutputInterface $output): ArrayObject
    {
        $fixture = new ArrayObject();
        foreach ($this->loadFiles($directory) as $file) {
            $fixture->append($this->yamlToArray($file));
        }

        $output->writeln('Sorting files into priority order');
        $fixture->uasort(static function ($a, $b) {
            return $a['priority'] <=> $b['priority'];
        });

        return $fixture;
    }

    private function processData(ArrayObject $fixture, OutputInterface $output): void
    {
        $output->writeln('Importing: ' . $fixture['filename']);

        $this->validateFixtureStructure($fixture);

        $reflection = new ReflectionClass($fixture['entity']);
        foreach ($fixture['data'] as $row) {
            $qb = $this->entityManager->createQueryBuilder();

            $data = $row['field'];
            $ref = $row['ref'] ?? null;

            switch ($fixture['entity']) {
                case LanguageEntity::class:
                    $hydrator = new LanguageHydrate();
                    $entity = $qb->select(['l'])
                        ->from(LanguageEntity::class, 'l')
                        ->andWhere('l.code = :code')
                        ->setParameter('code', $data['code'])
                        ->getQuery()
                        ->getResult();

                    break;
                case LanguagesTranslation::class:
                    $hydrator = new LanguageTranslationHydrator();

                    $languageRef = substr($data['language'], 1);
                    $translationLanguageRef = substr($data['translationLanguage'], 1);

                    if (!isset($this->mappings[$languageRef])) {
                        throw new OutOfBoundsException(sprintf(
                            'language reference "%s" was not found in mappings',
                            $languageRef,
                        ));
                    }

                    if (!isset($this->mappings[$translationLanguageRef])) {
                        throw new OutOfBoundsException(sprintf(
                            'translationLanguage reference "%s" was not found in mappings',
                            $languageRef,
                        ));
                    }

                    $data['language'] = $this->mappings[$languageRef];
                    $data['translationLanguage'] = $this->mappings[$translationLanguageRef];

                    $entity = $qb->select(['lt'])
                        ->from(LanguagesTranslation::class, 'lt')
                        ->andWhere('lt.language = :language', 'lt.translationLanguage = :translationLanguage')
                        ->setParameters([
                            'language' => $this->mappings[$languageRef],
                            'translationLanguage' => $this->mappings[$translationLanguageRef],
                        ])
                        ->getQuery()
                        ->getResult();

                    break;
                case Placeholder::class:
                    $hydrator = new PlaceholderHydrator();
                    $entity = $qb->select(['p'])
                        ->from(Placeholder::class, 'p')
                        ->andWhere(
                            'p.key = :key',
                            'p.type = :type',
                            'p.domain = :domain',
                        )
                        ->setParameters([
                            'key' => $data['key'],
                            'type' => $data['type'],
                            'domain' => $data['domain'],
                        ])
                        ->getQuery()
                        ->getResult();

                    break;
                case Translation::class:
                    $hydrator = new TranslationHydrator();
                    $placeholderRef = substr($data['placeholder'], 1);
                    $languageRef = substr($data['language'], 1);

                    if (!isset($this->mappings[$placeholderRef])) {
                        // Get the placeholder for the ref from the YAML files.
                        $actualPlaceholderRef = $this->getActualPlaceholderByRef($placeholderRef);
                        if (isset($actualPlaceholderRef, $this->mappings[$actualPlaceholderRef])) {
                            // If it exists, assign to the mapping, and continue.
                            $placeholderRef = $actualPlaceholderRef;
                        } else {
                            // If it still doesn't exist, throw the error.
                            throw new OutOfBoundsException(
                                sprintf('placeholder reference "%s" was not found in mappings', $placeholderRef),
                            );
                        }
                    }

                    if (!isset($this->mappings[$languageRef])) {
                        throw new OutOfBoundsException(
                            sprintf('language reference "%s" was not find in mappings', $languageRef),
                        );
                    }

                    $data['placeholder'] = $this->mappings[$placeholderRef];
                    $data['language'] = $this->mappings[$languageRef];

                    $entity = $qb->select(['t'])
                        ->from(Translation::class, 't')
                        ->andWhere('t.placeholder = :placeholder', 't.language = :language')
                        ->setParameters([
                            'placeholder' => $this->mappings[$placeholderRef],
                            'language' => $this->mappings[$languageRef],
                        ])
                        ->getQuery()
                        ->getResult();

                    break;
            }

            $entity = $entity[0] ?? $reflection->newInstance();
            if ($ref) {
                if (isset($this->mappings[$ref])) {
                    throw new OutOfBoundsException(sprintf('reference is already mapped "%s"', $ref));
                }
                $this->mappings[$ref] = $entity;
            }

            $this->entityManager->persist($hydrator->hydrate($data, $entity));
        }
        $this->entityManager->flush();
    }

    private function validateFixtureStructure(ArrayObject $fixture): void
    {
        if (!isset($fixture['entity'])) {
            throw new InvalidArgumentException('Entity property not found.');
        }

        if (!class_exists($fixture['entity'])) {
            throw new RuntimeException(sprintf('The specified entity "%s" cannot be found.', $fixture['entity']));
        }

        if (!isset($fixture['data'])) {
            throw new InvalidArgumentException(
                sprintf('Data property not found when processing "%s".', $fixture['filename']),
            );
        }
    }

    private function getKeyByRef(string $ref): array
    {
        [$type, $domain, $key] = explode(':', $ref);

        return [
            'type' => $type,
            'domain' => $domain,
            'key' => $key,
        ];
    }

    private function isMatchingCombinedKey(array $key1, array $key2): bool
    {
        if (count($key1) === count($key2)) {
            foreach ($key1 as $name => $value) {
                if (strtolower($key2[$name]) !== strtolower($value)) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }

    private function getActualPlaceholderByRef(string $ref): string
    {
        // Split the $ref into type:domain:key as the yaml trnasformer uses.
        $combinedKey = $this->getKeyByRef($ref);
        // Check each placeholder YAML file
        $files = (new Finder())->files()
            ->in(CONSOLE_DIR . 'db/data/*')
            ->name('placeholders.yml')
            ->sortByName();

        foreach ($files as $file) {
            $content = $this->yamlToArray($file);
            $matchingTranslations = [];
            $matchingTranslations = array_filter($content['data'], function ($v, $k) use ($combinedKey) {
                return $this->isMatchingCombinedKey($v['field'], $combinedKey);
            }, ARRAY_FILTER_USE_BOTH);
            if (count($matchingTranslations) > 0) {
                $matchingTranslation = array_pop($matchingTranslations);

                return $matchingTranslation['ref'];
            }
        }

        return '';
    }
}
