<?php

namespace app\Console\I18n;

use app\models\multiLanguage\entities\Placeholder;
use app\Traits\I18n\TranslationTrait;
use InvalidArgumentException;
use League\Csv\Reader;
use RuntimeException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Yaml\Yaml;


#[AsCommand('tools:i18n:convert-placeholders')]
final class ConvertPlaceholdersToYml extends Command
{
    use TranslationTrait;
    private const IMPORT_PRIORITY = 1;

    protected function configure(): void
    {
        $this->setDescription('Converts csv placeholder file into yaml format which is used by `tools:i18n:update-translations`')
            ->addArgument('file', InputArgument::REQUIRED, 'relative location of csv file on disk');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $path = $input->getArgument('file');
        $this->validateFilePath($path);

        $structure = $this->transform($this->getPlaceholders($path));

        $output->writeln(Yaml::dump($structure));

        return 0;
    }

    private function validateFilePath(string $file): void
    {
        if (!file_exists($file)) {
            throw new InvalidArgumentException('Unable to find file at specified location.');
        }

        if (!is_readable($file)) {
            throw new RuntimeException('File is not readable.');
        }
    }

    private function getPlaceholders(string $filePath): array
    {
        $this->validateFilePath($filePath);
        $reader = Reader::createFromPath($filePath);
        $reader->setHeaderOffset(0);

        return array_values(iterator_to_array($reader->getRecords()));
    }

    private function transform(array $data): array
    {
        $collection = [];
        foreach ($data as $row) {
            $collection[] = [
                'field' => [
                    'domain' => $row['domain'],
                    'type' => $row['type'],
                    'key' => $row['key'],
                ],
                'ref' => $this->generatePlaceholderRef($row['key'], $row['type'], $row['domain']),
            ];
        }

        return [
            'entity' => Placeholder::class,
            'priority' => self::IMPORT_PRIORITY,
            'data' => $collection,
        ];
    }
}
