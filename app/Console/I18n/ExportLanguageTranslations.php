<?php

namespace app\Console\I18n;

use app\models\language\entities\LanguagesTranslation;
use app\Traits\I18n\TranslationTrait;
use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Yaml\Yaml;

#[AsCommand('tools:i18n:export-languages-translations')]
final class ExportLanguageTranslations extends Command
{
    use TranslationTrait;
    private const IMPORT_PRIORITY = 1;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    public function configure()
    {
        $this->setDescription('Exports languages_translation table to yaml format needed by `tools:i18n:update-translations` to resynchronise language file with the database');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $structure = $this->transform($this->getData());
        $output->write(Yaml::dump($structure));

        return 0;
    }

    private function getData(): array
    {
        return $this->connection->fetchAllAssociative('SELECT * FROM languages_translation ORDER BY language_id');
    }

    private function transform(array $data): array
    {
        $languages = array_flip($this->getLocales());
        $collection = array_map(static function ($row) use ($languages): array {
            return [
                'field' => [
                    'language' => '@' . $languages[(int) $row['language_id']],
                    'translationLanguage' => '@' . $languages[(int) $row['translation_language']],

                    'translation' => $row['translation'],
                ],
            ];
        }, $data);


        return [
            'entity' => LanguagesTranslation::class,
            'priority' => self::IMPORT_PRIORITY,
            'data' => $collection,
        ];
    }
}
