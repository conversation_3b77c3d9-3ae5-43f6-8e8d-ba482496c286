<?php

namespace app\Console\I18n;

use app\Traits\I18n\TranslationTrait;
use app\Transformer\I18n\YamlTransformer;
use Doctrine\DBAL\Connection;
use InvalidArgumentException;
use League\Csv\Reader;
use RuntimeException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function count;
use function in_array;

#[AsCommand('tools:i18n:convert-translations')]
final class ConvertTranslationsToYaml extends Command
{
    use TranslationTrait;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Converts csv translation file into yaml format which is used by `tools:i18n:update-translations`')
            ->addArgument('file', InputArgument::REQUIRED, 'relative location of csv file on disk')
            ->addArgument('locale', InputArgument::REQUIRED, 'locale of the translations')
            ->addOption('split', null, InputOption::VALUE_NONE, 'split into suitable module files and save them in suitable locations');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $path = $input->getArgument('file');
        $locale = strtolower($input->getArgument('locale'));

        $available = $this->getLocales();
        $this->validateLocale($locale, $available);
        $transformer = new YamlTransformer($locale);
        $translations = $this->getTranslations($path);
        if ($input->getOption('split')) {
            // Split the data up by module.
            $aggregates = $this->aggregate($translations);
            foreach ($aggregates as $module => $aggregate) {
                $count = count($aggregate);
                if ($count > 0) {
                    $yaml = $transformer->transform($aggregate);
                    // Write yaml file to appropriate directory.
                    $path = "db/data/{$module}/translations/{$locale}.yml";
                    file_put_contents($path, $yaml);
                    if (!$output->isQuiet()) {
                        echo "- Wrote {$count} translations to {$module} ({$path}).\n";
                    }
                }
            }
        } else {
            $output->write($transformer->transform($translations));
        }

        return 0;
    }

    private function validateFilePath(string $file): void
    {
        if (!file_exists($file)) {
            throw new InvalidArgumentException('Unable to find file at specified location.');
        }

        if (!is_readable($file)) {
            throw new RuntimeException('File is not readable.');
        }
    }

    private function getTranslations(string $filePath): array
    {
        $this->validateFilePath($filePath);
        $reader = Reader::createFromPath($filePath);
        $reader->setHeaderOffset(0);

        return array_values(iterator_to_array($reader->getRecords()));
    }

    private function aggregate(array $data): array
    {
        $paths = array_filter(glob('db/data/*'), 'is_dir');

        $modulePaths = [];
        $moduleNames = [];

        foreach ($paths as $path) {
            $modulePath = explode('/', $path)[2];
            $moduleName = strtolower($modulePath);
            $modulePaths[$moduleName] = $modulePath;
            $moduleNames[] = $moduleName;
        }

        $modulePaths['mortality_review'] = 'MortalityReview';
        $moduleNames[] = 'mortality_review';
        $modulePaths['complaints'] = 'Feedback';
        $moduleNames[] = 'complaints';

        $aggregations = [];
        foreach ($modulePaths as $module) {
            $aggregations[$module] = [];
        }

        foreach ($data as $translation) {
            $module = strtolower(explode('.', $translation['domain'])[0]);
            if (in_array($module, $moduleNames)) {
                // Put into module file
                $aggregations[$modulePaths[$module]][] = $translation;
            } else {
                // Put into misc
                $aggregations['Misc'][] = $translation;
            }
        }

        return $aggregations;
    }
}
