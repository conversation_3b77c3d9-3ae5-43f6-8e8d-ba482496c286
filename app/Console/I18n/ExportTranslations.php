<?php

namespace app\Console\I18n;

use app\Traits\I18n\TranslationTrait;
use app\Transformer\I18n\CsvTransformer;
use app\Transformer\I18n\YamlTransformer;
use app\Transformer\Transformer;
use Doctrine\DBAL\Connection;
use InvalidArgumentException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use function in_array;

#[AsCommand('tools:i18n:export-translations')]
final class ExportTranslations extends Command
{
    use TranslationTrait;
    private const SUPPORTED_FORMATS = ['yml', 'yaml', 'csv'];

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setDescription('Exports i18n_translation table into yaml or csv formats, this is needed for `tools:i18n:update-translations` to resynchronise translation files with the database')
            ->addArgument('locale', InputArgument::REQUIRED, 'Locale of translations to export')
            ->addArgument('format', InputArgument::OPTIONAL, 'Format to export to, default is (csv)', 'csv');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $locale = strtolower($input->getArgument('locale'));
        $available = $this->getLocales();
        $this->validateLocale($locale, $available);

        $format = $input->getArgument('format');
        $this->validateFormat($format);

        $data = $this->getTranslationData($locale, $available);
        $transformer = $this->getTransformer($format, $locale);
        $output->write($transformer->transform($data));

        return 0;
    }

    private function validateFormat(string $format): void
    {
        if (!in_array($format, self::SUPPORTED_FORMATS, true)) {
            throw new InvalidArgumentException(
                'unsupported format specified, supported formats are: ' . implode(', ', self::SUPPORTED_FORMATS),
            );
        }
    }

    private function getTranslationData(string $locale, array $available): array
    {
        return $this->connection->fetchAllAssociative(
            'SELECT p.[key], p.[type], p.domain, t.system_translation AS [translation]
            FROM i18n_placeholder AS p
            LEFT JOIN i18n_translation AS t on p.id = t.placeholder_id AND t.language_id = :language
            ORDER BY p.[key], p.[type]',
            ['language' => $available[$locale]],
        );
    }

    /**
     * @throws InvalidArgumentException
     */
    private function getTransformer(string $format, string $locale): Transformer
    {
        switch ($format) {
            case 'csv':
                return new CsvTransformer();
            case 'yml':
            case 'yaml':
                return new YamlTransformer($locale);
            default:
                throw new InvalidArgumentException('No transformer for specified format');
        }
    }
}
