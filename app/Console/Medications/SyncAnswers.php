<?php

declare(strict_types=1);

namespace app\Console\Medications;

use app\services\equipment\EquipmentAnswerSyncInterface;
use app\services\medications\MedicationAnswerSyncInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Throwable;

use function in_array;

final class SyncAnswers extends Command
{
    protected static $defaultName = 'tools:medications:sync-answers';

    /** @readonly */
    private MedicationAnswerSyncInterface $medicationAnswerSyncService;

    /** @readonly */
    private EquipmentAnswerSyncInterface $equipmentAnswerSyncService;

    public function __construct(
        MedicationAnswerSyncInterface $medicationAnswerSyncService,
        EquipmentAnswerSyncInterface $equipmentAnswerSyncService
    ) {
        $this->medicationAnswerSyncService = $medicationAnswerSyncService;
        $this->equipmentAnswerSyncService = $equipmentAnswerSyncService;
        parent::__construct();
    }

    public function configure()
    {
        $this->setDescription('Sync medication and equipment answers')
            ->setHelp('Fetches the answer (link) data for medications and equipment and persists locally within Capture')
            ->addArgument('client', InputArgument::REQUIRED, 'The client prefix for the answer DBs in mongo')
            ->addArgument('type', InputArgument::OPTIONAL, 'The data type being synced: "medication", "equipment", or "both" (default)');
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $client = $input->getArgument('client');
        $type = $input->getArgument('type');
        $syncMedicationAnswers = true;
        $syncEquipmentAnswers = true;
        $result = self::SUCCESS;

        if ($type) {
            if (!in_array($type, ['medication', 'equipment', 'both'], true)) {
                $output->writeln(
                    sprintf(
                        '<error>Invalid value "%s" supplied for type option. Valid values are: "medication", "equipment", "both"</error>',
                        $type,
                    ),
                );

                return self::FAILURE;
            }

            if ($type === 'medication') {
                $syncEquipmentAnswers = false;
            }

            if ($type === 'equipment') {
                $syncMedicationAnswers = false;
            }
        }

        if ($syncMedicationAnswers) {
            try {
                $this->medicationAnswerSyncService->syncAnswers($client, $output);
            } catch (Throwable $e) {
                $output->writeln(
                    sprintf(
                        '<error>Error syncing medication answers: %s</error>',
                        $e->getMessage(),
                    ),
                );
                $result = self::FAILURE;
            }
        }

        if ($syncEquipmentAnswers) {
            try {
                $this->equipmentAnswerSyncService->syncAnswers($client, $output);
            } catch (Throwable $e) {
                $output->writeln(
                    sprintf(
                        '<error>Error syncing equipment answers: %s</error>',
                        $e->getMessage(),
                    ),
                );
                $result = self::FAILURE;
            }
        }

        return $result;
    }
}
