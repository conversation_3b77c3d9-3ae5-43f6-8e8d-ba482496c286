<?php

namespace app\Console\User;

use Doctrine\DBAL\Connection;
use S<PERSON>fony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

use Throwable;

use function array_column;
use function fclose;

#[AsCommand('tools:user:migrate-identities')]
final class MigrateUserIdentities extends Command
{
    private const DUPLICATES = 3;
    private const DB_ERROR = 4;
    private const CONFLICTS = 5;

    /** @readonly */
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Migrates identities.')
            ->setHelp('Allows you to use a CSV to update user identities from one to another.')
            ->addArgument(
                'file-path',
                InputArgument::REQUIRED,
                'Specify the path to the file to be processed.
                    This should be a CSV file with old identifiers in the first column,
                    new identifiers in the second column, and no header row.',
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $filePath = $input->getArgument('file-path');

        if (!file_exists($filePath)) {
            $output->writeln('<comment>The specified file was not found.</comment>');

            return self::FAILURE;
        }

        $fileContents = $this->readFile($filePath);
        if (!$fileContents) {
            $output->writeln('<error>The specified file was empty or could not be read.</error>');

            return self::INVALID;
        }

        $from = array_column($fileContents, 'from');
        $to = array_column($fileContents, 'to');

        $duplicates = $this->hasDuplicates($from, $to);
        if ($duplicates) {
            $output->writeln('<error>The file contains duplicate identities.</error>');
            foreach ($duplicates as $duplicate) {
                $output->writeln($duplicate);
            }

            return self::DUPLICATES;
        }

        $conflicts = $this->hasConflicts($to);
        if ($conflicts) {
            $output->writeln('<error>Can not update identities, there are existing users who conflict with the identity data.</error>');
            foreach ($conflicts as $conflict) {
                $output->writeln($conflict);
            }

            return self::CONFLICTS;
        }

        $this->connection->beginTransaction();

        try {
            foreach ($fileContents as $row) {
                $this->connection->update(
                    'USERS_MAIN',
                    ['login' => $row['to']],
                    ['login' => $row['from']],
                );
            }
            $this->connection->commit();
        } catch (Throwable $e) {
            $output->writeln('<error>Failed writing to the database: ' . $e->getMessage() . '</error>');
            $this->connection->rollback();

            return self::DB_ERROR;
        }

        $output->writeln('<info>Identities migrated successfully.</info>');

        return self::SUCCESS;
    }

    /**
     * @return array array of associative arrays, each with two keys: 'from' (the source identity) and 'to' (the target)
     */
    private function readFile(string $filePath): array
    {
        $handle = fopen($filePath, 'rb');
        if ($handle === false) {
            return [];
        }

        $data = [];
        while ($line = fgetcsv($handle)) {
            $data[] = [
                'from' => $line[0],
                'to' => $line[1],
            ];
        }
        fclose($handle);

        if ($data[0]['from'] === 'from' && $data[0]['to'] === 'to') {
            unset($data[0]);
        }

        return $data;
    }

    /**
     * Looks through the supplied array for any duplicated identities in the 'from' or 'to' columns.
     * Also checks to make sure nothing in the 'from' column appears in the 'to' column.
     *
     * @param array<string> $from
     * @param array<string> $to
     *
     * @return array returns a list of duplicates or an empty error if there are none
     */
    private function hasDuplicates(array $from, array $to): array
    {
        $duplicates = array_intersect($from, $to);
        if ($duplicates) {
            // Value from 'to' appears somewhere in 'from' array. We don't like that.
            return $duplicates;
        }

        return [];
    }

    private function hasConflicts(array $to): array
    {
        $qb = $this->connection->createQueryBuilder();

        $qb->select('CONCAT(recordid, \', \', login) as conflict')
            ->from('users_main')
            ->where($qb->expr()->in('login', ':to'))
            ->setParameter('to', $to, Connection::PARAM_STR_ARRAY);

        return $qb->executeQuery()->fetchFirstColumn();
    }
}
