<?php

declare(strict_types=1);

namespace app\Console\User;

use app\framework\Environment;
use app\models\framework\config\DatixConfig;
use Doctrine\DBAL\Connection;
use RuntimeException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\QuestionHelper;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Throwable;

use function array_column;
use function count;

#[AsCommand('tools:user:copy-permissions')]
final class CopyUserPermissions extends Command
{
    /** @readonly */
    private Connection $connection;

    /** @readonly */
    private DatixConfig $config;

    public function __construct(Connection $connection, DatixConfig $config)
    {
        $this->connection = $connection;
        $this->config = $config;

        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $sourceUserId = (int) $input->getArgument('source');
        $targetUserId = (int) $input->getArgument('target');

        try {
            [$sourceUser, $targetUser] = $this->getUsers($sourceUserId, $targetUserId);
            if (!$this->warn($input, $output, $sourceUser, $targetUser)) {
                return 0;
            }

            $this->connection->beginTransaction();

            $this->setProfile($sourceUser, $targetUser);
            $this->setLocations($sourceUserId, $targetUserId);
            $this->setServices($sourceUserId, $targetUserId);
            $this->setSecurityGroups($sourceUserId, $targetUserId);
            $this->setUserParameters($sourceUser['login'], $targetUser['login']);

            $this->connection->commit();
        } catch (Throwable $e) {
            $this->connection->rollBack();
            $output->writeln("<error>Error: {$e->getMessage()}</error>");

            return 1;
        }

        return 0;
    }

    protected function configure()
    {
        $this->setDescription('Copy a user permissions')
            ->setHelp('Developer command for copy a user permissions and applies applying them to another user')
            ->addArgument('source', InputArgument::REQUIRED, 'User ID to copy permissions from')
            ->addArgument('target', InputArgument::REQUIRED, 'User ID to apply permissions to')
            ->addOption('yes', 'y', InputOption::VALUE_NONE, 'confirm copy permissions');
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    private function getUsers(int $sourceId, int $targetId): array
    {
        $qb = $this->connection->createQueryBuilder();
        $result = $qb->select(
            'recordid, CONCAT(use_forenames, \' \', use_surname) as name, sta_profile, [login], location_id, service_id',
        )
            ->from('users_main', 'u')
            ->where($qb->expr()->in('recordid', ':users'))
            ->setMaxResults(2)
            ->setParameter('users', [$sourceId, $targetId], Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();

        if (count($result) === 2) {
            return $result;
        }

        $foundId = (int) array_column($result, 'recordid')[0];
        if ($foundId === $sourceId) {
            throw new RuntimeException('Unable to find target user by ID');
        }

        if ($foundId === $targetId) {
            throw new RuntimeException('Unable to find source user by ID');
        }

        throw new RuntimeException('Unable to find Source and Target users by ID');
    }

    private function warn(InputInterface $input, OutputInterface $output, array $source, array $target): bool
    {
        $env = $this->config->getEnvironment();
        $confirm = $input->getOption('yes');
        if ($confirm) {
            return true;
        }

        $output->writeln("<info>Copying permissions from '{$source['name']}' to '{$target['name']}'</info>");

        /** @var QuestionHelper $helper */
        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion('Are you sure? [y/N]', false);
        if ($env !== Environment::DEVELOPMENT && !$confirm) {
            $question = new ConfirmationQuestion(
                '<error>Warning: this command should only be run on development environments, are you sure you want to do this? [y/N]</error>',
                false,
            );
        }

        return $helper->ask($input, $output, $question);
    }

    private function setProfile(array $source, array $target): void
    {
        $this->connection->executeQuery(
            'UPDATE users_main SET sta_profile=:profile WHERE recordid = :target',
            ['profile' => $source['sta_profile'], 'target' => (int) $target['recordid']],
        );
    }

    private function setLocations(int $source, int $target): void
    {
        $this->connection->executeQuery(
            'DELETE FROM users_location where users_id = :target',
            ['target' => $target],
        );

        $this->connection->executeQuery(
            <<<'SQL'
                INSERT INTO users_location (users_id, location_id)
                SELECT :target, location_id FROM users_location where users_id = :source
                SQL,
            ['target' => $target, 'source' => $source],
        );
    }

    private function setServices(int $source, int $target): void
    {
        $this->connection->executeQuery(
            'DELETE FROM users_service where users_id = :target',
            ['target' => $target],
        );

        $this->connection->executeQuery(
            <<<'SQL'
                INSERT INTO users_service (users_id, service_id)
                SELECT :target, service_id FROM users_service where users_id = :source
                SQL,
            ['target' => $target, 'source' => $source],
        );
    }

    private function setSecurityGroups(int $source, int $target): void
    {
        $this->connection->executeQuery(
            'DELETE FROM sec_staff_group WHERE use_id = :target',
            ['target' => $target],
        );

        $this->connection->executeQuery(
            <<<'SQL'
                INSERT INTO sec_staff_group (grp_id, use_id)
                SELECT grp_id, :target FROM sec_staff_group WHERE use_id = :source
                SQL,
            ['target' => $target, 'source' => $source],
        );
    }

    private function setUserParameters(string $source, string $target): void
    {
        $this->connection->executeQuery(
            'DELETE FROM user_parms WHERE login = :target',
            ['target' => $target],
        );
        $this->connection->executeQuery(
            <<<'SQL'
                INSERT INTO user_parms([login], parameter, parmvalue)
                SELECT :target, parameter, parmvalue FROM user_parms WHERE login = :source
                SQL,
            ['target' => $target, 'source' => $source],
        );
    }
}
