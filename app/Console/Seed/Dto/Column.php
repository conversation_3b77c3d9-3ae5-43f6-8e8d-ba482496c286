<?php

declare(strict_types=1);

namespace app\Console\Seed\Dto;

final class Column
{
    /** @readonly */
    private string $table;

    /** @readonly */
    private string $name;

    /** @readonly */
    private ?string $type;

    /** @readonly */
    private ?int $length;

    public function __construct(string $table, string $name, ?string $type = null, ?int $length = null)
    {
        $this->table = $table;
        $this->name = $name;
        $this->type = $type;
        $this->length = $length;
    }

    public function getTable(): string
    {
        return $this->table;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getLength(): ?int
    {
        return $this->length;
    }
}
