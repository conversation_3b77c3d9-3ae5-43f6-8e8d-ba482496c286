<?php

declare(strict_types=1);

namespace app\Console\Seed;

use app\Console\Seed\Dto\Column;
use Doctrine\DBAL\Connection;
use RuntimeException;
use src\system\database\FieldInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

use function array_merge;
use function sprintf;
use function in_array;

#[AsCommand('seed:validate:field-directory')]
final class FieldDirectoryValidatorCommand extends Command
{
    /** @readonly */
    private InputInterface $input;

    /** @readonly */
    private OutputInterface $output;

    /** @readonly */
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
        parent::__construct();
    }

    public function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->input = $input;
        $this->output = $output;

        $columnsObjects = $this->structureFieldSeedData($this->fetchSeedData());

        return $this->validate($columnsObjects) ? self::SUCCESS : self::FAILURE;
    }

    protected function configure(): void
    {
        $this->setDescription('Validates Field Directory seed data')
            ->setHelp('Developer command to validating field directory seed data against the schema')
            ->addOption('empty', 'e', InputOption::VALUE_NONE, 'exclude empty code fields')
            ->addOption('type', 't', InputOption::VALUE_NONE, 'exclude type mismatch')
            ->addOption('length', 'l', InputOption::VALUE_NONE, 'exclude length mismatch');
    }

    private function structureFieldSeedData(array $data): array
    {
        $structuredFieldSeedData = [];
        foreach ($data as $item) {
            $structuredFieldSeedData[] = [
                new Column(
                    $item['fdr_table'],
                    $item['fdr_name'],
                    $item['fdr_data_type'],
                    (int) $item['fdr_data_length'],
                ),
            ];

            $codeColumns = $this->getCodeTableColumns($item);
            if (!empty($codeColumns)) {
                $structuredFieldSeedData[] = $codeColumns;
            }
        }

        return array_merge(...$structuredFieldSeedData);
    }

    /**
     * @return Column[]
     */
    private function getCodeTableColumns(array $item): array
    {
        if (empty($item['fdr_code_table'])) {
            return [];
        }

        $excludeEmpty = $this->input->getOption('empty');
        if (empty($item['fdr_code_field']) || empty($item['fdr_code_descr'])) {
            if (!$excludeEmpty) {
                $this->output->writeln(
                    sprintf(
                        '<comment>`code_field` or `fdr_code_descr` is empty (%s, %s, %s)</comment>',
                        $item['fdr_name'],
                        $item['fdr_table'],
                        $item['fdr_code_table'],
                    ),
                );
            }

            return [];
        }

        $codeTable = 'code_types';
        if ($item['fdr_code_table'][0] !== '!') {
            $codeTable = $item['fdr_code_table'];
        }

        $columns = [
            new Column($codeTable, $item['fdr_code_field']),
            new Column($codeTable, $item['fdr_code_descr']),
        ];

        if (!empty($item['fdr_code_order'])) {
            $columns[] = new Column($codeTable, $item['fdr_code_order']);
        }

        return $columns;
    }

    private function fetchSeedData(): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->addSelect(
            'fdr_name, fdr_table, fdr_code_table, fdr_code_field, fdr_code_descr, fdr_code_parent, fdr_code_parent2, fdr_data_type, fdr_data_length, fdr_code_order',
        )
            ->from('field_directory')
            ->addOrderBy('fdr_table, fdr_code_table')
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * @param Column[] $columns
     */
    private function validate(array $columns): bool
    {
        $isValid = true;
        foreach ($columns as $column) {
            $schema = $this->getInfoFromSchema($column);
            if ($schema === false) {
                $this->output->writeln(
                    sprintf(
                        '<error>Invalid field directory, column `%s.%s` does not exist</error>',
                        $column->getTable(),
                        $column->getName(),
                    ),
                );
                $isValid = false;

                continue;
            }

            // skip over code field as we don't have enough information
            if ($column->getType() === null && $column->getLength() === null) {
                continue;
            }

            $types = $this->getTypeFromDataType($schema['DATA_TYPE']);
            $excludeType = $this->input->getOption('type');
            if (!$excludeType && !in_array($column->getType(), $types, true)) {
                $this->output->writeln(
                    sprintf(
                        '<info>datatype is incorrect for `%s.%s` it is %s but should be (%s)</info>',
                        $column->getTable(),
                        $column->getName(),
                        $column->getType(),
                        implode(', ', $types),
                    ),
                );
            }

            $excludeLength = $this->input->getOption('length');
            if (
                !$excludeLength
                && $schema['CHARACTER_MAXIMUM_LENGTH'] !== null
                && $column->getLength() !== (int) $schema['CHARACTER_MAXIMUM_LENGTH']
            ) {
                $this->output->writeln(
                    sprintf(
                        'Length is incorrect `%s.%s` is it %d but should be %d',
                        $column->getTable(),
                        $column->getName(),
                        $column->getLength(),
                        $schema['CHARACTER_MAXIMUM_LENGTH'],
                    ),
                );
            }
        }

        return $isValid;
    }

    private function getInfoFromSchema(Column $column)
    {
        $sql = 'SELECT DATA_TYPE, CHARACTER_MAXIMUM_LENGTH FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = :table AND COLUMN_NAME = :column';

        return $this->connection->executeQuery(
            $sql,
            ['table' => $column->getTable(), 'column' => $column->getName()],
            ['table' => null, 'column' => null],
        )->fetchAssociative();
    }

    private function getTypeFromDataType(string $datatype): array
    {
        switch ($datatype) {
            case 'varchar':
            case 'nvarchar':
            case 'ntext':
            case 'text':
            case 'nchar':
            case 'char':
                return [FieldInterface::STRING_CODE, FieldInterface::TEXT_CODE, FieldInterface::CODE_CODE];
            case 'int':
            case 'tinyint':
            case 'decimal':
                return [FieldInterface::MONEY_CODE, FieldInterface::NUMBER_CODE, FieldInterface::TREE_CODE];
            case 'datetime2':
            case 'datetime':
            case 'date':
                return [FieldInterface::DATE_CODE, FieldInterface::TIME_CODE];
            case 'bit':
                return [FieldInterface::YESNO_CODE];
            default:
                throw new RuntimeException('Unknown code');
        }
    }
}
