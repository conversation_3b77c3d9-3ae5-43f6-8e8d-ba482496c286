<?php

namespace app\models\codefield\hydrators;

use app\models\codefield\entities\codeEntities\ComTypeCodeDescrEntity;
use app\models\codefield\entities\codeEntities\ComTypeCodeEntity;
use app\models\codefield\entities\codeEntities\CsuSubjectCodeDescrEntity;
use app\models\codefield\entities\codeEntities\CsuSubjectCodeEntity;
use app\models\codefield\entities\codeEntities\CsuSubsubjectCodeDescrEntity;
use app\models\codefield\entities\codeEntities\CsuSubsubjectCodeEntity;
use app\models\codefield\entities\codeEntities\IncCatCodeDescrEntity;
use app\models\codefield\entities\codeEntities\IncCatCodeEntity;
use app\models\codefield\entities\codeEntities\IncClinDetailCodeDescrEntity;
use app\models\codefield\entities\codeEntities\IncClinDetailCodeEntity;
use app\models\codefield\entities\codeEntities\IncLevelHarmCodeDescrEntity;
use app\models\codefield\entities\codeEntities\IncLevelHarmCodeEntity;
use app\models\codefield\entities\codeEntities\IncSubcategoryCodeDescrEntity;
use app\models\codefield\entities\codeEntities\IncSubcategoryCodeEntity;
use app\models\codefield\entities\codeEntities\IncTypeCodeDescrEntity;
use app\models\codefield\entities\codeEntities\IncTypeCodeEntity;
use app\models\codefield\entities\CodeFieldEntity;
use InvalidArgumentException;
use src\complaints\model\FeedbackFields;
use src\incidents\model\IncidentsFields;

use function array_key_exists;

class CodeFieldHydrator
{
    private const PERMITTED_FIELDS = [
        IncidentsFields::INC_TYPE => [
            'code' => IncTypeCodeEntity::class,
            'descr' => IncTypeCodeDescrEntity::class,
        ],
        IncidentsFields::INC_CAT => [
            'code' => IncCatCodeEntity::class,
            'descr' => IncCatCodeDescrEntity::class,
        ],
        IncidentsFields::INC_SUBCATEGORY => [
            'code' => IncSubcategoryCodeEntity::class,
            'descr' => IncSubcategoryCodeDescrEntity::class,
        ],
        IncidentsFields::INC_CLIN_DETAIL => [
            'code' => IncClinDetailCodeEntity::class,
            'descr' => IncClinDetailCodeDescrEntity::class,
        ],
        IncidentsFields::INC_LEVEL_HARM => [
            'code' => IncLevelHarmCodeEntity::class,
            'descr' => IncLevelHarmCodeDescrEntity::class,
        ],
        FeedbackFields::COM_TYPE => [
            'code' => ComTypeCodeEntity::class,
            'descr' => ComTypeCodeDescrEntity::class,
        ],
        FeedbackFields::CSU_SUBJECT => [
            'code' => CsuSubjectCodeEntity::class,
            'descr' => CsuSubjectCodeDescrEntity::class,
        ],
        FeedbackFields::CSU_SUBSUBJECT => [
            'code' => CsuSubsubjectCodeEntity::class,
            'descr' => CsuSubsubjectCodeDescrEntity::class,
        ],
    ];

    /** @var CodeEntityHydrator */
    private $codeEntityHydrator;

    public function __construct(CodeEntityHydrator $codeEntityHydrator)
    {
        $this->codeEntityHydrator = $codeEntityHydrator;
    }

    public function hydrate(array $field): CodeFieldEntity
    {
        $hydratedData = [];

        $fieldName = $field['name'] ?? '';

        if (!array_key_exists($fieldName, self::PERMITTED_FIELDS)) {
            throw new InvalidArgumentException('Field is not permitted');
        }

        if (empty($field['codes'])) {
            throw new InvalidArgumentException('No codes provided');
        }

        foreach ($field['codes'] as $code) {
            $hydratedData[] = $this->codeEntityHydrator->hydrate($code, self::PERMITTED_FIELDS[$fieldName]);
        }

        return (new CodeFieldEntity())->setName($fieldName)->setCodes($hydratedData);
    }
}
