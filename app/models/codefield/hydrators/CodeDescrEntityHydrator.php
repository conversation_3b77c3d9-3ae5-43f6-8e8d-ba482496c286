<?php

namespace app\models\codefield\hydrators;

use app\models\codefield\interfaces\CodeDescrEntityInterface;
use InvalidArgumentException;

class CodeDescrEntityHydrator
{
    public function hydrate(array $data, string $class): CodeDescrEntityInterface
    {
        if (!class_exists($class) || !is_a($class, CodeDescrEntityInterface::class, true)) {
            throw new InvalidArgumentException('Invalid class supplied to hydrator: ' . $class);
        }

        if (!isset($data['code'], $data['language'], $data['description'])) {
            throw new InvalidArgumentException('Missing parameters from code');
        }

        /** @var CodeDescrEntityInterface $entity */
        $entity = new $class();

        return $entity
            ->setCode($data['code'])
            ->setLanguage($data['language'])
            ->setDescription($data['description']);
    }
}
