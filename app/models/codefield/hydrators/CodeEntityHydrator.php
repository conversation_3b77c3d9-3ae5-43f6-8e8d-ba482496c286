<?php

namespace app\models\codefield\hydrators;

use app\models\codefield\interfaces\CodeEntityInterface;
use InvalidArgumentException;

use function strlen;
use function in_array;

class CodeEntityHydrator
{
    /** @var array */
    private const ACCEPTED_PRIV_LEVELS = ['Y', 'X', 'N'];

    /** @var CodeDescrEntityHydrator */
    private $codeDescrEntityHydrator;

    public function __construct(CodeDescrEntityHydrator $codeDescrEntityHydrator)
    {
        $this->codeDescrEntityHydrator = $codeDescrEntityHydrator;
    }

    public function hydrate(array $code, array $classes): CodeEntityInterface
    {
        if (strlen($code['code']) > 6) {
            throw new InvalidArgumentException('Code too long');
        }

        if (!empty($code['level']) && !in_array($code['level'], self::ACCEPTED_PRIV_LEVELS)) {
            throw new InvalidArgumentException('Accepted code levels are: ' . implode(' ', self::ACCEPTED_PRIV_LEVELS));
        }

        $codeDescriptions = [];

        if (empty($code['description'])) {
            throw new InvalidArgumentException('At least one code description must be provided');
        }

        foreach ($code['description'] as $codeDescription) {
            if ($code['code'] !== $codeDescription['code']) {
                throw new InvalidArgumentException('Codes do not match');
            }

            $codeDescriptionEntity = $this->codeDescrEntityHydrator->hydrate($codeDescription, $classes['descr']);
            $codeDescriptions[] = $codeDescriptionEntity;
        }

        $class = $classes['code'] ?? '';

        if (!class_exists($class) || !is_a($class, CodeEntityInterface::class, true)) {
            throw new InvalidArgumentException('Invalid class supplied to hydrator: ' . $class);
        }

        /** @var CodeEntityInterface $entity */
        $entity = new $class();

        return $entity
            ->setCode(strtoupper($code['code']))
            ->setCodListorder($code['order'])
            ->setCodParent(strtoupper($code['parent']))
            ->setCodParent2(strtoupper($code['parent2']))
            ->setCodPrivLevel(strtoupper($code['level']))
            ->setCodWebColour(strtolower($code['colour']))
            ->setCodeDescriptions($codeDescriptions);
    }
}
