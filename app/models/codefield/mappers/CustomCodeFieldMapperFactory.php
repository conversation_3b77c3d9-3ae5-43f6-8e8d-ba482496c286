<?php

namespace app\models\codefield\mappers;

use app\framework\DBALConnectionFactory;
use app\services\audit\FullAuditFactory;
use src\system\language\LanguageSessionFactory;

/**
 * @codeCoverageIgnore
 */
class CustomCodeFieldMapperFactory
{
    public function create($codeName): CustomCodeFieldMapper
    {
        $db = (new DBALConnectionFactory())->getInstance();
        $fullAudit = (new FullAuditFactory())->create($db);
        $languageSession = LanguageSessionFactory::getInstance();

        return new CustomCodeFieldMapper($db, $fullAudit, $languageSession, $codeName);
    }
}
