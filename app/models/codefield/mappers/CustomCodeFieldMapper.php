<?php

namespace app\models\codefield\mappers;

use app\models\codefield\entities\CustomCodeField;
use app\models\generic\mappers\MapperAbstract;
use app\services\audit\AuditInterface;
use Doctrine\DBAL\Connection;
use src\system\language\LanguageSession;

/**
 * @codeCoverageIgnore
 */
class CustomCodeFieldMapper extends MapperAbstract
{
    /** @var string */
    private $codeName;

    /** @var LanguageSession */
    private $languageSession;

    public function __construct(Connection $db, AuditInterface $audit, LanguageSession $languageSession, $codeName)
    {
        parent::__construct($db, $audit);
        $this->codeName = $codeName;
        $this->languageSession = $languageSession;
    }

    public function getTable()
    {
        return 'code_' . $this->codeName;
    }

    public function getPrimaryKey()
    {
        return ['code', 'language'];
    }

    public function getDescription($id)
    {
        $language = $this->languageSession->getLanguage();
        $data = $this->get([$id, $language]);

        return $data['description'];
    }

    protected function map(CustomCodeField $codeType)
    {
        $codeTypeArray = $codeType->toArray();

        return [
            'code' => $codeTypeArray['code'],
            'description' => $codeTypeArray['description'],
        ];
    }
}
