<?php

namespace app\models\codefield\mappers;

use app\models\codefield\entities\CodeField;
use app\models\generic\mappers\MapperAbstract;

/**
 * @codeCoverageIgnore
 */
class CodeFieldMapper extends MapperAbstract
{
    public function getTable()
    {
        return 'code_types';
    }

    public function getPrimaryKey()
    {
        return ['cod_code', 'cod_type'];
    }

    public function codeExists($code, $type)
    {
        $code = $this->get([$code, $type]);

        return !empty($code);
    }

    protected function map(CodeField $codeType)
    {
        $codeTypeArray = $codeType->toArray();

        return [
            'cod_code' => $codeTypeArray['code'],
            'cod_type' => $codeTypeArray['type'],
            'cod_descr' => $codeTypeArray['description'],
            'cod_listorder' => $codeTypeArray['order'],
            'cod_web_colour' => $codeTypeArray['colour'],
        ];
    }
}
