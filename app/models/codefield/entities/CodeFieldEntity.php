<?php

namespace app\models\codefield\entities;

use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
class CodeFieldEntity
{
    /**
     * @var string
     *
     * @OA\Property(property="name", type="string", maxLength=128, nullable=false)
     */
    private $name;

    /**
     * @OA\Property(
     *     property="codes",
     *     type="array",
     *     @OA\Items(anyOf={
     *         @OA\Schema(ref="#/components/schemas/ComTypeCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/CsuSubjectCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/CsuSubsubjectCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/IncCatCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/IncClinDetailCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/IncLevelHarmCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/IncSubcategoryCodeEntity"),
     *         @OA\Schema(ref="#/components/schemas/IncTypeCodeEntity"),
     *     })
     * )
     */
    private $codes;

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @param string $name
     */
    public function setName($name): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return array
     */
    public function getCodes()
    {
        return $this->codes;
    }

    /**
     * @return CodeFieldEntity
     */
    public function setCodes(array $codes)
    {
        $this->codes = $codes;

        return $this;
    }
}
