<?php

namespace app\models\codefield\entities;

use app\models\generic\entities\EntityInterface;

/**
 * @codeCoverageIgnore
 */
class CustomCodeField implements EntityInterface
{
    /** @var string */
    private $code;

    /** @var string */
    private $description;

    public function __construct($code, $description)
    {
        $this->code = $code;
        $this->description = $description;
    }

    /**
     * @return string[]
     */
    public function getId(): array
    {
        return [$this->code];
    }

    public function toArray()
    {
        return [
            'code' => $this->code,
            'description' => $this->description,
        ];
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }
}
