<?php

namespace app\models\codefield\entities\codeEntities;

use app\models\codefield\interfaces\CodeEntityInterface;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema
 *
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "code_sub_subject")]
class CsuSubsubjectCodeEntity implements CodeEntityInterface
{
    /**
     * @OA\Property(property="code", type="string", maxLength=6, nullable=false)
     */
    #[Id]
    #[Column(type: "string")]
    private string $code;

    /**
     * @OA\Property(property="cod_parent", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private string $cod_parent;

    /**
     * @OA\Property(property="cod_parent2", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private string $cod_parent2;

    /**
     * @OA\Property(property="cod_priv_level", maxLength=1, type="string", nullable=true)
     */
    #[Column(type: "string")]
    private string $cod_priv_level;

    /**
     * @OA\Property(property="cod_listorder", type="integer", nullable=true, format="int32")
     */
    #[Column(type: "integer")]
    private int $cod_listorder;

    /**
     * @OA\Property(property="cod_web_colour", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private string $cod_web_colour;

    /**
     * @var Collection|CsuSubsubjectCodeDescrEntity[]
     *
     * @OA\Property(
     *     property="descriptions",
     *     @OA\Property(
     *         property="codes",
     *         type="array",
     *         @OA\Items(ref="#/components/schemas/CsuSubsubjectCodeDescrEntity"),
     *     )
     * )
     */
    #[OneToMany(
        mappedBy: "code",
        targetEntity: CsuSubsubjectCodeDescrEntity::class,
        cascade: ["persist"],
    )]
    private $codeDescriptions;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getCodParent(): string
    {
        return $this->cod_parent;
    }

    public function setCodParent(string $cod_parent): self
    {
        $this->cod_parent = $cod_parent;

        return $this;
    }

    public function getCodParent2(): string
    {
        return $this->cod_parent2;
    }

    public function setCodParent2(string $cod_parent2): self
    {
        $this->cod_parent2 = $cod_parent2;

        return $this;
    }

    public function getCodPrivLevel(): string
    {
        return $this->cod_priv_level;
    }

    public function setCodPrivLevel(string $cod_priv_level): self
    {
        $this->cod_priv_level = $cod_priv_level;

        return $this;
    }

    public function getCodListorder(): int
    {
        return $this->cod_listorder;
    }

    /**
     * @param int $cod_listorder
     */
    public function setCodListorder(?int $cod_listorder): self
    {
        $this->cod_listorder = $cod_listorder;

        return $this;
    }

    public function getCodWebColour(): string
    {
        return $this->cod_web_colour;
    }

    public function setCodWebColour(string $cod_web_colour): self
    {
        $this->cod_web_colour = $cod_web_colour;

        return $this;
    }

    /**
     * @return CsuSubSubjectCodeDescrEntity[]|Collection
     */
    public function getCodeDescriptions(): Collection
    {
        return $this->codeDescriptions;
    }

    /**
     * @param CsuSubSubjectCodeDescrEntity[]|Collection $codeDescriptions
     *
     * @return ComTypeCodeEntity
     */
    public function setCodeDescriptions($codeDescriptions): self
    {
        $this->codeDescriptions = $codeDescriptions;

        return $this;
    }

    public function getDescrTableName(): string
    {
        return CsuSubsubjectCodeDescrEntity::class;
    }
}
