<?php

namespace app\models\codefield\entities\codeEntities;

use app\models\codefield\interfaces\CodeDescrEntityInterface;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema
 *
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "code_ccs2_tier_zero_descr")]
class IncAffectingTierZeroCodeDescrEntity implements CodeDescrEntityInterface
{
    /**
     * @OA\Property(property="code", type="string", maxLength=6, nullable=false)
     */
    #[Id]
    #[Column(type: "string")]
    private string $code;

    /**
     * @OA\Property(property="language", type="integer", format="int32", nullable=false)
     */
    #[Id]
    #[Column(type: "integer")]
    private int $language;

    /**
     * @OA\Property(property="description", type="string", nullable=false)
     */
    #[Column(type: "string")]
    private string $description;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLanguage(): int
    {
        return $this->language;
    }

    public function setLanguage(int $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }
}
