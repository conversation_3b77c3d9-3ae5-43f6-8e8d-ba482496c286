<?php

namespace app\models\codefield\entities;

use app\models\generic\entities\EntityInterface;

/**
 * @codeCoverageIgnore
 */
class CodeField implements EntityInterface
{
    /** @var string */
    private $type;

    /** @var string */
    private $code;

    /** @var string */
    private $description;

    /** @var string */
    private $order;

    /** @var string */
    private $colour;

    public function __construct($type, $code, $description, $order, $colour)
    {
        $this->type = $type;
        $this->code = $code;
        $this->description = $description;
        $this->order = $order;
        $this->colour = $colour;
    }

    public function getId(): array
    {
        return [$this->code, $this->type];
    }

    public function toArray()
    {
        return [
            'code' => $this->code,
            'type' => $this->type,
            'description' => $this->description,
            'order' => $this->order,
            'colour' => $this->colour,
        ];
    }
}
