<?php

namespace app\models\codefield\interfaces;

interface CodeEntityInterface
{
    public function getCode();

    public function setCode(string $code);

    public function getCodParent();

    public function setCodParent(string $parent);

    public function getCodParent2();

    public function setCodParent2(string $parent2);

    public function getCodPrivLevel();

    public function setCodPrivLevel(string $privLevel);

    public function getCodListorder();

    public function setCodListorder(int $order);

    public function getCodWebColour();

    public function setCodWebColour(string $colour);

    public function getCodeDescriptions();

    public function setCodeDescriptions($description);
}
