<?php

declare(strict_types=1);

namespace app\models\codefield\traits;

use Doctrine\DBAL\Connection;

trait IncidentCodesTrait
{
    public function getCodes(): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.code, c.cod_parent')
            ->where('c.cod_priv_level = \'Y\' OR c.cod_priv_level IS NULL OR c.cod_priv_level = \'\'')
            ->getQuery()
            ->getResult();
    }

    public function getCodesByParents(array $parentCodes): array
    {
        return $this->createQueryBuilder('c')
            ->select('c.code, c.cod_parent')
            ->where('c.cod_parent IN (:parentCodes)')
            ->setParameter('parentCodes', $parentCodes, Connection::PARAM_STR_ARRAY)
            ->getQuery()
            ->getResult();
    }
}
