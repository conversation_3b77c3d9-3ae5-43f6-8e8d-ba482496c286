<?php

namespace app\models\codefield\services;

use app\models\generic\exceptions\RecordNotFoundException;
use app\models\generic\Tables;
use InvalidArgumentException;
use src\incidents\model\IncidentsFields;
use src\system\database\code\Code;
use src\system\database\code\CodeCollection;
use src\system\database\CodeFieldInterface;
use src\system\database\fielddef\FieldDefCollection;
use src\framework\controller\Request;

class CodeInfoRetriever
{
    /** @var CodeCollection[] Cache of codes keyed by full field name */
    protected $codeLookupCache = [];

    /** @var FieldDefCollection */
    private $fieldDefs;
    private Request $request;

    public function __construct(FieldDefCollection $fieldDefs, Request $request)
    {
        $this->fieldDefs = $fieldDefs;
        $this->request = $request;
    }

    /**
     * Builds a cache for the given field and stores it against the code info retriever for later use.
     */
    public function cache(string $fullFieldName): void
    {
        // Clear existing cache if there is one so get codes will always fallback to the full lookup
        unset($this->codeLookupCache[$fullFieldName]);
        $this->codeLookupCache[$fullFieldName] = $this->getCodes($fullFieldName);
    }

    /**
     * @param string $fullFieldName
     * @param string $code
     */
    public function retrieve($fullFieldName, $code): Code
    {
        if (!isset($code) || $code === '') {
            return new Code();
        }

        $codeCollection = $this->getCodes($fullFieldName);

        return $codeCollection[$code] ?? (new Code())->setCode($code)->setDescription([$code]);
    }

    public function retrieveStaffDescriptionFromInitials(string $code)
    {
        if (!isset($code) || $code === '') {
            return new Code();
        }

        $codeCollection = $this->getCodes(Tables::INCIDENTS_MAIN . '.' . IncidentsFields::HANDLER);

        return $codeCollection[$code] ?? (new Code())->setCode($code)->setDescription([$code]);
    }

    public function retrieveAll(string $fullFieldName): ?array
    {
        if ($fullFieldName === '') {
            return null;
        }

        if (!str_contains($fullFieldName, '.') && !str_starts_with($fullFieldName, 'UDF_')) {
            throw new InvalidArgumentException('Field name does not contain a table or it is improperly formatted');
        }

        $codeCollection = $this->getCodes($fullFieldName);

        if ($codeCollection instanceof CodeCollection) {
            return $codeCollection->toArray();
        }

        return $codeCollection;
    }

    /**
     * @param $value
     *
     * @return array|null
     *                    the type of the value is not specified in the method signature because it can be a string or an integer
     */
    public function getCodesByColumnValue(string $fullFieldName, string $column, $value): ?array
    {
        $codes = $this->getCodes($fullFieldName)->getData();

        $codesToReturn = [];

        foreach ($codes as $code) {
            if ($code[$column] === $value) {
                $codesToReturn[] = $code;
            }
        }

        return $codesToReturn;
    }

    /**
     * Retrieve a Code by an index ( which is the desired CODE that we are searching for ).
     *
     * @throws RecordNotFoundException
     */
    public function getCodesByIndex(string $fullFieldName, ?string $code): ?array
    {
        if (!$code) {
            return null;
        }

        $codes = $this->getCodes($fullFieldName)->getData();

        $filteredCodes = array_filter($codes, static fn ($cod) => $cod['code'] === $code);

        if (empty($filteredCodes)) {
            throw new RecordNotFoundException('sfg_local_auth_coded_field_deleted');
        }

        return array_values($filteredCodes)[0];
    }

    /**
     * Get codes for a given field(preferably from the cache).
     *
     * @return array|CodeCollection
     */
    protected function getCodes(string $fullFieldName)
    {
        if (isset($this->codeLookupCache[$fullFieldName])) {
            return $this->codeLookupCache[$fullFieldName];
        }

        $fieldDefObj = $this->fieldDefs[$fullFieldName];

        if (!$fieldDefObj instanceof CodeFieldInterface) {
            return [];
        }

        if ($this->request->getParameter('autocomplete') === null && $fieldDefObj->getCodeTable() === Tables::VW_STAFF_COMBOS) {
            $fieldDefObj->setCodeTable(Tables::STAFF);
        }

        return $fieldDefObj->getCodes();
    }
}
