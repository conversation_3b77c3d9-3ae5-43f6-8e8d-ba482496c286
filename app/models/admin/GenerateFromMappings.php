<?php

namespace app\models\admin;

use app\models\contact\ContactTypes;
use app\models\generic\valueObjects\Module;
use Exception;
use src\claims\models\ClaimsFields;
use src\complaints\model\FeedbackFields;
use src\incidents\model\IncidentsFields;
use src\mortality\model\MortalityFields;
use src\redress\models\RedressFields;
use src\safeguarding\models\SafeguardingFields;

final class GenerateFromMappings
{
    public const MAPPINGS = [
        Module::INCIDENTS => self::INCIDENTS,
        Module::CLAIMS => self::CLAIMS,
        Module::FEEDBACK => self::FEEDBACK,
        Module::MORTALITY_REVIEW => self::MORTALITY_REVIEW,
        Module::REDRESS => self::REDRESS,
        Module::SAFEGUARDING => self::SAFEGUARDING,
    ];
    public const ENABLED_CAUSAL_FACTORS_COPYING = [
        Module::INCIDENTS,
        Module::CLAIMS,
        Module::REDRESS,
    ];
    public const ENABLED_RESPONDENTS_COPYING = [
        Module::CLAIMS,
        Module::REDRESS,
        Module::SAFEGUARDING,
    ];
    public const CONTACT_MAPPINGS = [
        Module::INCIDENTS => self::INCIDENTS_CONTACTS,
        Module::CLAIMS => self::CLAIMS_CONTACTS,
        Module::FEEDBACK => self::FEEDBACK_CONTACTS,
        Module::MORTALITY_REVIEW => self::MORTALITY_REVIEW_CONTACTS,
        Module::REDRESS => self::REDRESS_CONTACTS,
        Module::SAFEGUARDING => self::SAFEGUARDING_CONTACTS,
    ];
    private const INCIDENTS_CONTACTS = [
        Module::MORTALITY_REVIEW => [
            ContactTypes::PERSON_AFFECTED => ContactTypes::OTHER_CONTACT,
            ContactTypes::EMPLOYEE => ContactTypes::OTHER_CONTACT,
            ContactTypes::WITNESS => ContactTypes::OTHER_CONTACT,
            ContactTypes::REPORTER => ContactTypes::OTHER_CONTACT,
            ContactTypes::POLICE_OFFICER => ContactTypes::OTHER_CONTACT,
            ContactTypes::ALLEGED_ASSAILANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CLAIMANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::INDIVIDUAL_RESPONDENT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::RESPONDENT => ContactTypes::OTHER_CONTACT,
        ],
    ];
    private const CLAIMS_CONTACTS = [
        Module::MORTALITY_REVIEW => [
            ContactTypes::PERSON_AFFECTED => ContactTypes::OTHER_CONTACT,
            ContactTypes::EMPLOYEE => ContactTypes::OTHER_CONTACT,
            ContactTypes::WITNESS => ContactTypes::OTHER_CONTACT,
            ContactTypes::REPORTER => ContactTypes::OTHER_CONTACT,
            ContactTypes::POLICE_OFFICER => ContactTypes::OTHER_CONTACT,
            ContactTypes::ALLEGED_ASSAILANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CLAIMANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::INDIVIDUAL_RESPONDENT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::RESPONDENT => ContactTypes::OTHER_CONTACT,
        ],
    ];
    private const FEEDBACK_CONTACTS = [
        Module::INCIDENTS => [
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
        ],
        Module::CLAIMS => [
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
        ],
        Module::MORTALITY_REVIEW => [
            ContactTypes::PERSON_AFFECTED => ContactTypes::OTHER_CONTACT,
            ContactTypes::EMPLOYEE => ContactTypes::OTHER_CONTACT,
            ContactTypes::WITNESS => ContactTypes::OTHER_CONTACT,
            ContactTypes::REPORTER => ContactTypes::OTHER_CONTACT,
            ContactTypes::POLICE_OFFICER => ContactTypes::OTHER_CONTACT,
            ContactTypes::ALLEGED_ASSAILANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CLAIMANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::INDIVIDUAL_RESPONDENT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::RESPONDENT => ContactTypes::OTHER_CONTACT,
        ],
        Module::REDRESS => [
            ContactTypes::CONSULTANT => ContactTypes::CLAIMANT,
        ],
        Module::SAFEGUARDING => [
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
        ],
    ];
    private const MORTALITY_REVIEW_CONTACTS = [
        Module::INCIDENTS => [
            ContactTypes::DECEASED => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::EMPLOYEE,
        ],
        Module::CLAIMS => [
            ContactTypes::DECEASED => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::EMPLOYEE,
        ],
        Module::FEEDBACK => [
            ContactTypes::DECEASED => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::EMPLOYEE,
        ],
        Module::REDRESS => [
            ContactTypes::DECEASED => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::EMPLOYEE,
        ],
        Module::SAFEGUARDING => [
            ContactTypes::DECEASED => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::EMPLOYEE,
        ],
    ];
    private const REDRESS_CONTACTS = [
        Module::MORTALITY_REVIEW => [
            ContactTypes::PERSON_AFFECTED => ContactTypes::OTHER_CONTACT,
            ContactTypes::EMPLOYEE => ContactTypes::OTHER_CONTACT,
            ContactTypes::WITNESS => ContactTypes::OTHER_CONTACT,
            ContactTypes::REPORTER => ContactTypes::OTHER_CONTACT,
            ContactTypes::POLICE_OFFICER => ContactTypes::OTHER_CONTACT,
            ContactTypes::ALLEGED_ASSAILANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CLAIMANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::INDIVIDUAL_RESPONDENT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::RESPONDENT => ContactTypes::OTHER_CONTACT,
        ],
    ];
    private const SAFEGUARDING_CONTACTS = [
        Module::MORTALITY_REVIEW => [
            ContactTypes::PERSON_AFFECTED => ContactTypes::OTHER_CONTACT,
            ContactTypes::EMPLOYEE => ContactTypes::OTHER_CONTACT,
            ContactTypes::WITNESS => ContactTypes::OTHER_CONTACT,
            ContactTypes::REPORTER => ContactTypes::OTHER_CONTACT,
            ContactTypes::POLICE_OFFICER => ContactTypes::OTHER_CONTACT,
            ContactTypes::ALLEGED_ASSAILANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CLAIMANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::INDIVIDUAL_RESPONDENT => ContactTypes::OTHER_CONTACT,
            ContactTypes::CONSULTANT => ContactTypes::OTHER_CONTACT,
            ContactTypes::RESPONDENT => ContactTypes::OTHER_CONTACT,
        ],
    ];
    private const INCIDENTS = [
        Module::CLAIMS => [
            'inc_name' => 'cla_name',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'inc_mgr' => 'cla_mgr',
            'inc_notes' => 'cla_synopsis',
            'rep_approved' => 'rep_approved',
            'inc_dincident' => 'cla_dincident',
            'inc_carestage' => 'cla_carestage',
            'inc_clin_detail' => 'cla_clin_detail',
            'inc_clintype' => 'cla_clintype',
            'inc_ourref' => 'cla_inc_ourref',
            'inc_time' => 'cla_inc_time',
            'incident_occurred_on_employer_premises' => 'incident_occurred_on_employer_premises',
            'safeguard_provided' => 'safeguard_provided',
            'safeguard_used' => 'safeguard_used',
        ],
        Module::FEEDBACK => [
            'inc_name' => 'com_name',
            'inc_ourref' => 'com_ourref',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'inc_mgr' => 'com_mgr',
            'inc_notes' => 'com_detail',
            'inc_root_causes' => 'com_root_causes',
            'rep_approved' => 'rep_approved',
            'inc_dincident' => 'com_dincident',
            'inc_head' => 'com_head',
            'inc_type' => 'com_inc_type',
        ],
        Module::MORTALITY_REVIEW => [
            IncidentsFields::REFERENCE => MortalityFields::REFERENCE,
            IncidentsFields::LOCATION_ID => MortalityFields::LOCATION_ID,
            IncidentsFields::SERVICE_ID => MortalityFields::SERVICE_ID,
            IncidentsFields::DESCRIPTION => MortalityFields::DESCRIPTION,
            IncidentsFields::INCIDENT_DATE => MortalityFields::INCIDENT_DATE,
        ],
        Module::REDRESS => [
            'inc_name' => 'patient_name',
            'inc_severity' => 'level_of_harm',
            'inc_dincident' => 'date_of_index_incident',
            'location_id' => 'location_id',
            'other_location' => 'secondary_location_id',
            'service_id' => 'principle_service_id',
            'other_service' => 'secondary_service_id',
            'inc_causal_factors_linked' => 'causal_factors_linked',
        ],
        Module::SAFEGUARDING => [
            IncidentsFields::LOCATION_ID => SafeguardingFields::LOCATION_ID,
            IncidentsFields::SERVICE_ID => SafeguardingFields::SERVICE_ID,
            IncidentsFields::HANDLER => SafeguardingFields::HANDLER,
            IncidentsFields::MANAGER => SafeguardingFields::MANAGER,
            IncidentsFields::DESCRIPTION => SafeguardingFields::DESCRIPTION,
            IncidentsFields::DATE_OPENED => SafeguardingFields::DATE_OPENED,
            IncidentsFields::INCIDENT_DATE => SafeguardingFields::DATE_REPORTED,
        ],
    ];
    private const CLAIMS = [
        Module::INCIDENTS => [
            'cla_name' => 'inc_name',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'cla_mgr' => 'inc_mgr',
            'cla_synopsis' => 'inc_notes',
            'rep_approved' => 'rep_approved',
            'cla_dincident' => 'inc_dincident',
            'cla_carestage' => 'inc_carestage',
            'cla_clin_detail' => 'inc_clin_detail',
            'cla_clintype' => 'inc_clintype',
            'cla_inc_ourref' => 'inc_ourref',
            'cla_inc_time' => 'inc_time',
            'incident_occurred_on_employer_premises' => 'incident_occurred_on_employer_premises',
            'safeguard_provided' => 'safeguard_provided',
            'safeguard_used' => 'safeguard_used',
        ],
        Module::FEEDBACK => [
            'cla_name' => 'com_name',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'cla_mgr' => 'com_mgr',
            'cla_synopsis' => 'com_detail',
            'rep_approved' => 'rep_approved',
            'cla_dincident' => 'com_dincident',
        ],
        Module::MORTALITY_REVIEW => [
            ClaimsFields::REFERENCE => MortalityFields::REFERENCE,
            ClaimsFields::LOCATION_ID => MortalityFields::LOCATION_ID,
            ClaimsFields::SERVICE_ID => MortalityFields::SERVICE_ID,
            ClaimsFields::DESCRIPTION => MortalityFields::DESCRIPTION,
        ],
        Module::REDRESS => [],
        Module::SAFEGUARDING => [
            ClaimsFields::LOCATION_ID => SafeguardingFields::LOCATION_ID,
            ClaimsFields::SERVICE_ID => SafeguardingFields::SERVICE_ID,
            ClaimsFields::HANDLER => SafeguardingFields::HANDLER,
            ClaimsFields::MANAGER => SafeguardingFields::MANAGER,
            ClaimsFields::DESCRIPTION => SafeguardingFields::DESCRIPTION,
        ],
    ];
    private const FEEDBACK = [
        Module::INCIDENTS => [
            'com_name' => 'inc_name',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'com_mgr' => 'inc_mgr',
            'com_detail' => 'inc_notes',
            'com_root_causes' => 'inc_root_causes',
            'rep_approved' => 'rep_approved',
            'com_dincident' => 'inc_dincident',
            'com_ourref' => 'inc_ourref',
        ],
        Module::CLAIMS => [
            'com_name' => 'cla_name',
            'location_id' => 'location_id',
            'service_id' => 'service_id',
            'com_mgr' => 'cla_mgr',
            'com_detail' => 'cla_synopsis',
            'com_root_causes' => 'cla_root_causes',
            'rep_approved' => 'rep_approved',
            'com_dincident' => 'cla_dincident',
        ],
        Module::MORTALITY_REVIEW => [
            FeedbackFields::REFERENCE => MortalityFields::REFERENCE,
            FeedbackFields::LOCATION_ID => MortalityFields::LOCATION_ID,
            FeedbackFields::SERVICE_ID => MortalityFields::SERVICE_ID,
            FeedbackFields::DESCRIPTION => MortalityFields::DESCRIPTION,
        ],
        Module::REDRESS => [
            'com_name' => 'patient_name',
            'com_dincident' => 'date_of_index_incident',
            'location_id' => 'location_id',
            'other_location' => 'secondary_location_id',
            'service_id' => 'principle_service_id',
            'other_service' => 'secondary_service_id',
        ],
        Module::SAFEGUARDING => [
            FeedbackFields::LOCATION_ID => SafeguardingFields::LOCATION_ID,
            FeedbackFields::SERVICE_ID => SafeguardingFields::SERVICE_ID,
            FeedbackFields::HANDLER => SafeguardingFields::HANDLER,
            FeedbackFields::MANAGER => SafeguardingFields::MANAGER,
            FeedbackFields::DESCRIPTION => SafeguardingFields::DESCRIPTION,
            FeedbackFields::DATE_FIRST_RECEIVED => SafeguardingFields::DATE_REPORTED,
            FeedbackFields::DATE_OPENED => SafeguardingFields::DATE_OPENED,
        ],
    ];
    private const MORTALITY_REVIEW = [
        Module::INCIDENTS => [
            MortalityFields::REFERENCE => IncidentsFields::REFERENCE,
            MortalityFields::LOCATION_ID => IncidentsFields::LOCATION_ID,
            MortalityFields::SERVICE_ID => IncidentsFields::SERVICE_ID,
            MortalityFields::DESCRIPTION => IncidentsFields::DESCRIPTION,
            MortalityFields::INCIDENT_DATE => IncidentsFields::INCIDENT_DATE,
        ],
        Module::CLAIMS => [
            MortalityFields::REFERENCE => ClaimsFields::REFERENCE,
            MortalityFields::LOCATION_ID => ClaimsFields::LOCATION_ID,
            MortalityFields::SERVICE_ID => ClaimsFields::SERVICE_ID,
            MortalityFields::DESCRIPTION => ClaimsFields::DESCRIPTION,
        ],
        Module::FEEDBACK => [
            MortalityFields::REFERENCE => FeedbackFields::REFERENCE,
            MortalityFields::LOCATION_ID => FeedbackFields::LOCATION_ID,
            MortalityFields::SERVICE_ID => FeedbackFields::SERVICE_ID,
            MortalityFields::DESCRIPTION => FeedbackFields::DESCRIPTION,
        ],
        Module::REDRESS => [],
        Module::SAFEGUARDING => [
            MortalityFields::LOCATION_ID => SafeguardingFields::LOCATION_ID,
            MortalityFields::SERVICE_ID => SafeguardingFields::SERVICE_ID,
            MortalityFields::DESCRIPTION => SafeguardingFields::DESCRIPTION,
        ],
    ];
    private const REDRESS = [
        Module::INCIDENTS => [],
        Module::CLAIMS => [
            'patient_name' => 'cla_name',
            'date_of_index_incident' => 'cla_dincident',
            'location_id' => 'location_id',
            'principle_service_id' => 'service_id',
        ],
        Module::FEEDBACK => [],
        Module::MORTALITY_REVIEW => [],
        Module::SAFEGUARDING => [
            RedressFields::LOCATION_ID => SafeguardingFields::LOCATION_ID,
            RedressFields::PRINCIPLE_CLINICAL_SPECIALTY => SafeguardingFields::SERVICE_ID,
            RedressFields::DESCRIPTION => SafeguardingFields::DESCRIPTION,
            RedressFields::DATE_RECORD_OPENED => SafeguardingFields::DATE_OPENED,
        ],
    ];
    private const SAFEGUARDING = [
        Module::INCIDENTS => [
            SafeguardingFields::LOCATION_ID => IncidentsFields::LOCATION_ID,
            SafeguardingFields::SERVICE_ID => IncidentsFields::SERVICE_ID,
            SafeguardingFields::HANDLER => IncidentsFields::HANDLER,
            SafeguardingFields::MANAGER => IncidentsFields::MANAGER,
            SafeguardingFields::DESCRIPTION => IncidentsFields::DESCRIPTION,
            SafeguardingFields::DATE_OPENED => IncidentsFields::DATE_OPENED,
            SafeguardingFields::DATE_REPORTED => IncidentsFields::INCIDENT_DATE,
        ],
        Module::CLAIMS => [
            SafeguardingFields::LOCATION_ID => ClaimsFields::LOCATION_ID,
            SafeguardingFields::SERVICE_ID => ClaimsFields::SERVICE_ID,
            SafeguardingFields::HANDLER => ClaimsFields::HANDLER,
            SafeguardingFields::MANAGER => ClaimsFields::MANAGER,
            SafeguardingFields::DESCRIPTION => ClaimsFields::DESCRIPTION,
        ],
        Module::FEEDBACK => [
            SafeguardingFields::LOCATION_ID => FeedbackFields::LOCATION_ID,
            SafeguardingFields::SERVICE_ID => FeedbackFields::SERVICE_ID,
            SafeguardingFields::HANDLER => FeedbackFields::HANDLER,
            SafeguardingFields::MANAGER => FeedbackFields::MANAGER,
            SafeguardingFields::DESCRIPTION => FeedbackFields::DESCRIPTION,
            SafeguardingFields::DATE_OPENED => FeedbackFields::DATE_OPENED,
            SafeguardingFields::DATE_REPORTED => FeedbackFields::DATE_FIRST_RECEIVED,
        ],
        Module::MORTALITY_REVIEW => [
            SafeguardingFields::LOCATION_ID => MortalityFields::LOCATION_ID,
            SafeguardingFields::SERVICE_ID => MortalityFields::SERVICE_ID,
            SafeguardingFields::DESCRIPTION => MortalityFields::DESCRIPTION,
        ],
        Module::REDRESS => [
            SafeguardingFields::LOCATION_ID => RedressFields::LOCATION_ID,
            SafeguardingFields::SERVICE_ID => RedressFields::PRINCIPLE_CLINICAL_SPECIALTY,
            SafeguardingFields::DESCRIPTION => RedressFields::DESCRIPTION,
            SafeguardingFields::DATE_OPENED => RedressFields::DATE_RECORD_OPENED,
        ],
    ];

    /**
     * @throws Exception
     */
    private function __construct()
    {
        // throw an exception if someone can get in here
        throw new Exception("Can't get an instance of GenerateFromMappings");
    }
}
