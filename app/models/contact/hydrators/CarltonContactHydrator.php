<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\AddressEntity;
use app\models\contact\entities\ContactEntity;
use app\models\contact\entities\EmailEntity;
use app\models\contact\entities\IdNumberEntity;
use app\models\contact\entities\PhoneNumberEntity;
use app\models\generic\valueObjects\RecordID;
use app\services\approvalStatus\ApprovalStatus;
use app\services\carlton\generic\ApprovalStatusAdapter;
use app\services\carlton\generic\DateAdapter;
use app\services\carlton\generic\MultiSelectFieldApiAdapter;
use app\services\carlton\generic\SingleSelectFieldApiAdapter;
use DateTime;
use DatixDBQuery;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use src\framework\registry\Registry;

use function array_filter;
use function array_key_exists;
use function is_array;
use function is_object;

/**
 * @todo: IQ-29285 only have one hydrator
 * Any changes made here may need to be reflected in app/services/prince/contact/hydrators/ContactHydrator.php
 */
class CarltonContactHydrator
{
    // Handy reference for field mapping that should hopefully map all carlton-capture field pairings/combos
    public const CARLTON_FIELD_TO_CAPTURE_MAP = [
        'id' => 'recordid',
        'title' => 'con_title',
        'surname' => 'con_surname',
        'middleName' => 'con_middle_name',
        'forename' => 'con_forenames',
        'disabilities' => 'con_disability',
        'gender' => 'con_gender',
        'ethnicity' => 'con_ethnicity',
        'type' => 'con_type',
        'subType' => 'con_sub_type',
        'language' => 'con_language',
        'loneWorker' => 'con_work_alone_assessed',
        'sexualOrientation' => 'con_sec_orientation',
        'religion' => 'con_religion',
        'dateOfBirth' => 'con_dob',
        'dateOfDeath' => 'con_dod',
        'status' => 'rep_approved',
        'oshaDateHired' => 'osha_date_hired',
        'city' => 'con_city',
        'state' => 'con_state',
        'county' => 'con_county',
        'socialSecurityNumber' => 'con_social_security_number',
        'taxId' => 'tax_id',
        'employeeStateHired' => 'employee_state_hired',
        'employmentTerminationDate' => 'employment_termination_date',
        'employmentStatusCode' => 'con_employment_status_code',
        'processLevel' => 'con_process_level',
        'jobCode' => 'con_job_code',
        'supervisorName' => 'con_supervisor_name',
        'department' => 'con_department',
        'locationCode' => 'con_location_code',
        'fte' => 'con_fte',
        'lawsonNumber' => 'con_lawson_number',
        'employeeIdAssignedByJurisdiction' => 'employee_id_assigned_by_jurisdiction',
        'hoursWorked' => 'con_hours_worked',
        'hourlyRate' => 'con_hourly_rate',
        'ncciCode' => 'con_ncci_code',
        'occupation' => 'con_occupation',
        'maritalStatus' => 'con_marital_status',
        'remoteId' => 'con_remote_id',
        'emplGrade' => 'con_empl_grade',
        'nationality' => 'nationality',
        'jobTitle' => 'job_title',
        'sourceOfRecord' => 'source_of_record',
        'apiSource' => 'api_source',
        // Linked entities:
        'addresses' => ['con_line1', 'con_line2', 'con_line3', 'con_city', 'con_county', 'con_state', 'con_country', 'con_postcode'],
        'numbers' => ['con_tel1', 'con_tel2'],
        'emails' => 'con_email',
        'idNumbers' => ['id_numbers', 'con_id_numbers'],
        'lastUpdatedDate' => 'lastUpdatedDate',
    ];
    private SingleSelectFieldApiAdapter $singleSelectFieldApiAdapter;
    private MultiSelectFieldApiAdapter $multiSelectFieldApiAdapter;
    private ApprovalStatusAdapter $approvalStatusAdapter;
    private DateAdapter $dateAdapter;
    private DatixDBQuery $db;
    private Registry $registry;
    private EmailHydrator $emailHydrator;
    private PhoneNumberHydrator $phoneNumberHydrator;
    private AddressHydrator $addressHydrator;
    private IdNumberHydrator $idNumberHydrator;
    private EntityManagerInterface $entityManager;
    private ?array $idNumberMap = null;

    public function __construct(
        SingleSelectFieldApiAdapter $singleSelectFieldApiAdapter,
        MultiSelectFieldApiAdapter $multiSelectFieldApiAdapter,
        ApprovalStatusAdapter $approvalStatusAdapter,
        DateAdapter $dateAdapter,
        DatixDBQuery $db,
        Registry $registry,
        EmailHydrator $emailHydrator,
        PhoneNumberHydrator $phoneNumberHydrator,
        AddressHydrator $addressHydrator,
        IdNumberHydrator $idNumberHydrator,
        EntityManagerInterface $entityManager
    ) {
        $this->singleSelectFieldApiAdapter = $singleSelectFieldApiAdapter;
        $this->multiSelectFieldApiAdapter = $multiSelectFieldApiAdapter;
        $this->approvalStatusAdapter = $approvalStatusAdapter;
        $this->dateAdapter = $dateAdapter;
        $this->db = $db;
        $this->registry = $registry;
        $this->emailHydrator = $emailHydrator;
        $this->phoneNumberHydrator = $phoneNumberHydrator;
        $this->addressHydrator = $addressHydrator;
        $this->idNumberHydrator = $idNumberHydrator;
        $this->entityManager = $entityManager;
    }

    /**
     * @throws Exception
     */
    public function hydrate(array $data, ?ContactEntity $contactEntity = null): ContactEntity
    {
        $contactEntity ??= new ContactEntity();

        if (isset($data['status']['id'])) {
            $repStatus = $this->approvalStatusAdapter->adaptFromRequest($data['status']['id']);
        } elseif ($contactEntity->getRepApproved() === null) {
            $repStatus = ApprovalStatus::UNAPPROVED;
        } else {
            $repStatus = $contactEntity->getRepApproved();
        }

        $disabilities = $contactEntity->getConDisability() ?? '';
        if (isset($data['disabilities'])) {
            $disabilities = $this->multiSelectFieldApiAdapter->adaptFromRequest($data['disabilities']);
        }

        $contactEntity
            ->setRecordid($data['id'] ?? $contactEntity->getRecordid())
            ->setConTitle($data['title'] ?? $contactEntity->getConTitle())
            ->setConSurname($data['surname'] ?? $contactEntity->getConSurname())
            ->setConMiddleName($data['middleName'] ?? $contactEntity->getConMiddleName())
            ->setConForenames($data['forename'] ?? $contactEntity->getConForenames())
            ->setConGender($this->extractFieldId($data['gender'] ?? $contactEntity->getConGender()))
            ->setConEthnicity($this->extractFieldId($data['ethnicity'] ?? $contactEntity->getConEthnicity()))
            ->setConType($this->extractFieldId($data['type'] ?? $contactEntity->getConType()))
            ->setConSubtype($this->extractFieldId($data['subType'] ?? $contactEntity->getConSubtype()))
            ->setConLanguage($this->extractFieldId($data['language'] ?? $contactEntity->getConLanguage()))
            ->setConDisability($disabilities)
            ->setConWorkAloneAssessed(($data['loneWorker'] ?? $contactEntity->getConWorkAloneAssessed()) == 1 ? 'Y' : 'N')
            ->setConSexOrientation($this->extractFieldId($data['sexualOrientation'] ?? $contactEntity->getConSexOrientation()))
            ->setConReligion($this->extractFieldId($data['religion'] ?? $contactEntity->getConReligion()))
            ->setConNotes($data['notes'] ?? $contactEntity->getConNotes())
            ->setConDob($this->dateAdapter->adaptFromRequest($data['dateOfBirth'] ?? $contactEntity->getConDob() ?? ['date' => '']))
            ->setConDod($this->dateAdapter->adaptFromRequest($data['dateOfDeath'] ?? $contactEntity->getConDod() ?? ['date' => '']))
            ->setRepApproved($repStatus)
            ->setLocationId($data['locations'][0]['location']['id'] ?? $contactEntity->getLocationId())
            ->setServiceId($data['services'][0]['service']['id'] ?? $contactEntity->getServiceId())
            ->setOshaDateHired($this->dateAdapter->adaptFromRequest($data['oshaDateHired'] ?? $contactEntity->getOshaDateHired() ?? ['date' => '']))
            ->setConCity($data['city'] ?? $contactEntity->getConCity())
            ->setConState($data['state'] ?? $contactEntity->getConState())
            ->setConCounty($data['county'] ?? $contactEntity->getConCounty())
            ->setConSocialSecurityNumber($data['socialSecurityNumber'] ?? $contactEntity->getConSocialSecurityNumber())
            ->setTaxId($data['taxId'] ?? $contactEntity->getTaxId())
            ->setEmployeeStateHired($data['employeeStateHired'] ?? $contactEntity->getEmployeeStateHired())
            ->setEmploymentTerminationDate($this->dateAdapter->adaptFromRequest($data['employmentTerminationDate'] ?? $contactEntity->getEmploymentTerminationDate() ?? ['date' => '']))
            ->setConEmploymentStatusCode($data['employmentStatusCode'] ?? $contactEntity->getConEmploymentStatusCode())
            ->setConProcessLevel($data['processLevel'] ?? $contactEntity->getConProcessLevel())
            ->setConJobCode($data['jobCode'] ?? $contactEntity->getConJobCode())
            ->setConSupervisorName($data['supervisorName'] ?? $contactEntity->getConSupervisorName())
            ->setConDepartment($data['department'] ?? $contactEntity->getConDepartment())
            ->setConLocationCode($data['locationCode'] ?? $contactEntity->getConLocationCode())
            ->setConFte($data['fte'] ?? $contactEntity->getConFte())
            ->setConLawsonNumber($data['lawsonNumber'] ?? $contactEntity->getConLawsonNumber())
            ->setEmployeeIdAssignedByJurisdiction($data['employeeIdAssignedByJurisdiction'] ?? $contactEntity->getEmployeeIdAssignedByJurisdiction())
            ->setConHoursWorked($data['hoursWorked'] ?? $contactEntity->getConHoursWorked())
            ->setConHourlyRate($data['hourlyRate'] ?? $contactEntity->getConHourlyRate())
            ->setConNcciCode($data['ncciCode'] ?? $contactEntity->getConNcciCode())
            ->setConOccupation($data['occupation'] ?? $contactEntity->getConOccupation())
            ->setConMaritalStatus($data['maritalStatus'] ?? $contactEntity->getConMaritalStatus())
            ->setConRemoteId($data['remoteId'] ?? $contactEntity->getConRemoteId())
            ->setConEmplGrade($data['emplGrade'] ?? $contactEntity->getConEmplGrade())
            ->setNationality($data['nationality'] ?? $contactEntity->getNationality())
            ->setJobTitle($data['jobTitle'] ?? $contactEntity->getJobTitle())
            ->setSourceOfRecord($data['sourceOfRecord'] ?? $contactEntity->getSourceOfRecord())
            ->setApiSource($data['apiSource'] ?? $contactEntity->getApiSource())
            ->setLastUpdatedDate($data['lastUpdatedDate'] ? new DateTime($data['lastUpdatedDate']) : null);

        $idNumbers = $data['idNumbers'] ?? [];

        foreach ($idNumbers as $idKey => $idField) {
            $type = $this->idNumbersMap()[$idField['type']];
            if (isset($type)) {
                $functionName = 'set' . $type;
                $contactEntity->{$functionName}($idField['number']);
            }
            // Id field is a Carlton specific datasource item reference
            // Remove unncessary data
            if (array_key_exists('id', $idField)) {
                unset($idNumbers[$idKey]['id']);
            }
        }
        $contactEntity->setConIdNumbers(['idNumbers' => $idNumbers]);

        $this->hydrateEmails($contactEntity, $data['emails'] ?? [])
            ->hydratePhoneNumbers($contactEntity, $data['numbers'] ?? [])
            ->hydrateAddresses($contactEntity, $data['addresses'] ?? [])
            ->hydrateIdNumbers($contactEntity, $data['idNumbers'] ?? []);

        return $contactEntity;
    }

    /**
     * @return array $data
     */
    public function extract(ContactEntity $contactEntity): array
    {
        $contactResponseArray = [
            'id' => $contactEntity->getRecordid() ? (new RecordID($contactEntity->getRecordid()))->getId() : null,
            'title' => $contactEntity->getConTitle(),
            'surname' => $contactEntity->getConSurname(),
            'middleName' => $contactEntity->getConMiddleName(),
            'forename' => $contactEntity->getConForenames(),
            'disabilities' => $this->multiSelectFieldApiAdapter->adaptForResponse($contactEntity->getConDisability()),
            'gender' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConGender()),
            'ethnicity' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConEthnicity()),
            'type' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConType()),
            'subType' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConSubtype()),
            'language' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConLanguage()),
            'loneWorker' => $this->extractLoneWorker($contactEntity->getConWorkAloneAssessed()),
            'sexualOrientation' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConSexOrientation()),
            'religion' => $this->singleSelectFieldApiAdapter->adaptForResponse($contactEntity->getConReligion()),
            'dateOfBirth' => $contactEntity->getConDob(),
            'dateOfDeath' => $contactEntity->getConDod(),
            'status' => $this->approvalStatusAdapter->adaptForResponse($contactEntity->getRepApproved()),
            'oshaDateHired' => $contactEntity->getOshaDateHired(),
            'city' => $contactEntity->getConCity(),
            'state' => $contactEntity->getConState(),
            'county' => $contactEntity->getConCounty(),
            'socialSecurityNumber' => $contactEntity->getConSocialSecurityNumber(),
            'taxId' => $contactEntity->getTaxId(),
            'employeeStateHired' => $contactEntity->getEmployeeStateHired(),
            'employmentTerminationDate' => $contactEntity->getEmploymentTerminationDate(),
            'employmentStatusCode' => $contactEntity->getConEmploymentStatusCode(),
            'processLevel' => $contactEntity->getConProcessLevel(),
            'jobCode' => $contactEntity->getConJobCode(),
            'supervisorName' => $contactEntity->getConSupervisorName(),
            'department' => $contactEntity->getConDepartment(),
            'locationCode' => $contactEntity->getConLocationCode(),
            'fte' => $contactEntity->getConFte(),
            'lawsonNumber' => $contactEntity->getConLawsonNumber(),
            'employeeIdAssignedByJurisdiction' => $contactEntity->getEmployeeIdAssignedByJurisdiction(),
            'hoursWorked' => $contactEntity->getConHoursWorked(),
            'hourlyRate' => $contactEntity->getConHourlyRate(),
            'ncciCode' => $contactEntity->getConNcciCode(),
            'occupation' => $contactEntity->getConOccupation(),
            'maritalStatus' => $contactEntity->getConMaritalStatus(),
            'remoteId' => $contactEntity->getConRemoteId(),
            'emplGrade' => $contactEntity->getConEmplGrade(),
            'nationality' => $contactEntity->getNationality(),
            'jobTitle' => $contactEntity->getJobTitle(),
            'sourceOfRecord' => $contactEntity->getSourceOfRecord(),
            'apiSource' => $contactEntity->getApiSource(),
            'notes' => $contactEntity->getConNotes(),
        ];

        if ($this->registry->getParm('MULTI_ID_NUMBER_SECTION', 'N', true)->isTrue()) {
            $contactResponseArray['idNumbers'] = array_map(
                fn (IdNumberEntity $idNumberEntity): array => $this->idNumberHydrator->extract($idNumberEntity),
                $contactEntity->getIdNumbers()->getValues(),
            );
        } else {
            $contactResponseArray['idNumbers'] = $this->extractIdNumberFields($contactEntity);
        }

        $contactResponseArray['emails'] = array_map(
            fn (EmailEntity $emailEntity): array => $this->emailHydrator->extract($emailEntity),
            $contactEntity->getEmails()->toArray(),
        );

        $contactResponseArray['numbers'] = array_filter(array_map(
            fn (PhoneNumberEntity $phoneNumberEntity): array => $this->phoneNumberHydrator->extract($phoneNumberEntity),
            $contactEntity->getPhoneNumbers()->toArray(),
        ));

        $contactResponseArray['addresses'] = array_map(
            fn (AddressEntity $addressEntity): array => $this->addressHydrator->extract($addressEntity),
            $contactEntity->getAddresses()->toArray(),
        );

        if ($contactEntity->getLocationId()) {
            $contactResponseArray['locations'] = [
                ['location' => $contactEntity->getLocationId()],
            ];
        }

        if ($contactEntity->getServiceId()) {
            $contactResponseArray['services'] = [
                ['service' => $contactEntity->getServiceId()],
            ];
        }

        return $this->setAllEmptyValuesToNull($contactResponseArray);
    }

    public function hydrateLinks(ContactEntity $contactEntity, $links)
    {
        $linkCollection = $contactEntity->getLinks();
        $linkHydrator = new ContactLinksHydrator();

        if (empty($links)) {
            return $this;
        }

        if (is_array($links) || is_object($links)) {
            foreach ($links as $link) {
                $linkEntity = $linkHydrator->hydrate($link, $contactEntity);
                $linkCollection->add($linkEntity);
            }
        }

        return $this;
    }

    protected function extractIdNumberFields($contactEntity)
    {
        $requiredMappings = array_filter($this->idNumbersMap(), function ($princeFunctionName) use ($contactEntity) {
            $functionName = 'get' . $princeFunctionName;

            return !empty($contactEntity->{$functionName}());
        });

        return array_map(function ($carltonFieldName, $princeFunctionName) use ($contactEntity) {
            $functionName = 'get' . $princeFunctionName;

            return [
                'number' => $contactEntity->{$functionName}(),
                'type' => $carltonFieldName,
            ];
        }, array_keys($requiredMappings), $requiredMappings);
    }

    protected function setAllEmptyValuesToNull($contactResponseArray)
    {
        array_walk_recursive($contactResponseArray, function (&$value) {
            if ($value === '') {
                $value = null;
            }
        });

        return $contactResponseArray;
    }

    private function hydrateEmails(ContactEntity $contactEntity, array $emails): self
    {
        // check if any of the emails are not on the contact, we will remove them ($emails from Carlton is our source of truth)
        $this->removeExcludedChildEntities(
            $contactEntity,
            $contactEntity->getEmails(),
            $emails,
            function (EmailEntity $entityOnContact, $data) {
                return $entityOnContact->getId() === $data['id'];
            },
        );

        foreach ($emails as $email) {
            $doctrineEntity = $this->getEntity($contactEntity->getEmails(), EmailEntity::class, $email['id'] ?? null);
            // Hydration of the entity still need to happen as we need to persist any updates to existing entities
            $emailEntity = $this->emailHydrator->hydrate($email, $contactEntity, $doctrineEntity);

            if ($doctrineEntity === null) {
                $contactEntity->addEmail($emailEntity);
            }
        }

        /** @var EmailEntity|null $email */
        $email = $contactEntity->getEmails()->first();
        $contactEntity->setConEmail($email ? $email->getEmail() : null);

        return $this;
    }

    private function removeExcludedChildEntities(ContactEntity $contactEntity, Collection $entitiesOnContact, array $carltonArray, callable $filter): void
    {
        // check if any of the child entities are not on the contact, we will remove them ($carltonArray from Carlton is our source of truth)
        foreach ($entitiesOnContact as $entityOnContact) {
            $found = false;
            foreach ($carltonArray as $carltonItem) {
                if (
                    $filter($entityOnContact, $carltonItem)
                ) {
                    $found = true;

                    break;
                }
            }
            if (!$found) {
                $contactEntity->remove($entityOnContact);
            }
        }
    }

    private function hydratePhoneNumbers(ContactEntity $contactEntity, array $phoneNumbers): self
    {
        // check if any of the phone numbers are not on the contact, we will remove them ($phoneNumbers from Carlton is our source of truth)
        $this->removeExcludedChildEntities(
            $contactEntity,
            $contactEntity->getPhoneNumbers(),
            $phoneNumbers,
            function (PhoneNumberEntity $entityOnContact, $data) {
                return $entityOnContact->getId() === $data['id'];
            },
        );

        foreach ($phoneNumbers as $number) {
            $doctrineEntity = $this->getEntity($contactEntity->getPhoneNumbers(), PhoneNumberEntity::class, $number['id'] ?? null);
            // Hydration of the entity still need to happen as we need to persist any updates to existing entities
            $phoneNumberEntity = $this->phoneNumberHydrator->hydrate($number, $contactEntity, $doctrineEntity);

            if ($doctrineEntity === null) {
                $contactEntity->addPhoneNumber($phoneNumberEntity);
            }
        }

        $phoneNumberEntities = array_values($contactEntity->getPhoneNumbers()->toArray());

        /** @var PhoneNumberEntity|null $tel1 */
        $tel1 = $phoneNumberEntities[0] ?? null;
        $contactEntity->setConTel1($tel1 ? $tel1->getPhoneNumber() : null);

        /** @var PhoneNumberEntity|null $tel2 */
        $tel2 = $phoneNumberEntities[1] ?? null;
        $contactEntity->setConTel2($tel2 ? $tel2->getPhoneNumber() : null);

        return $this;
    }

    private function hydrateAddresses(ContactEntity $contactEntity, array $addresses): self
    {
        // check if any of the addresses are not on the contact, we will remove them ($addresses from Carlton is our source of truth)
        $this->removeExcludedChildEntities(
            $contactEntity,
            $contactEntity->getAddresses(),
            $addresses,
            function (AddressEntity $entityOnContact, $data) {
                return $entityOnContact->getId() === $data['id'];
            },
        );

        foreach ($addresses as $address) {
            $doctrineEntity = $this->getEntity($contactEntity->getAddresses(), AddressEntity::class, $address['id'] ?? null);
            // Hydration of the entity still need to happen as we need to persist any updates to existing entities
            $addressEntity = $this->addressHydrator->hydrate($address, $contactEntity, $doctrineEntity);

            if ($doctrineEntity === null) {
                $contactEntity->addAddress($addressEntity);
            }
        }

        /** @var AddressEntity|null $address */
        $address = $contactEntity->getAddresses()->first();
        $contactEntity->setConLine1($address ? $address->getLine1() : null);
        $contactEntity->setConLine2($address ? $address->getLine2() : null);
        $contactEntity->setConLine3($address ? $address->getLine3() : null);
        $contactEntity->setConCity($address ? $address->getCity() : null);
        $contactEntity->setConCounty($address ? $address->getCounty() : null);
        $contactEntity->setConCountry($address ? $address->getCountry() : null);
        $contactEntity->setConPostcode($address ? $address->getPostcode() : null);

        return $this;
    }

    /**
     * @param AddressEntity[]|EmailEntity[]|PhoneNumberEntity[]|IdNumberEntity[]|Collection $collection
     *
     * @return AddressEntity|EmailEntity|PhoneNumberEntity|IdNumberEntity|null
     */
    private function getEntity(Collection $collection, string $entityClass, ?int $id)
    {
        if ($id === null) {
            return null;
        }

        foreach ($collection as $item) {
            if ($item->getId() === $id) {
                return $item;
            }
        }

        return $this->entityManager->getRepository($entityClass)->find($id);
    }

    private function hydrateIdNumbers(ContactEntity $contactEntity, array $idNumbers): self
    {
        // check if any of the id numbers are not on the contact, we will remove them ($idNumbers from Carlton is our source of truth)
        $this->removeExcludedChildEntities(
            $contactEntity,
            $contactEntity->getIdNumbers(),
            $idNumbers,
            function (IdNumberEntity $entityOnContact, $data) {
                return $entityOnContact->getId() === $data['id'];
            },
        );

        foreach ($idNumbers as $item) {
            $doctrineEntity = $this->getEntity($contactEntity->getIdNumbers(), IdNumberEntity::class, $item['id'] ?? null);
            // Hydration of the entity still need to happen as we need to persist any updates to existing entities
            $idNumber = $this->idNumberHydrator->hydrate($item, $contactEntity, $doctrineEntity);

            if ($doctrineEntity === null) {
                $contactEntity->addIdNumber($idNumber);

                // if Carlton knows this code but with a different Id, then remove the ones
                // from Capture with a different Id.  Carlton is the source or truth.
                // Otherwise, a new entity is added and the user see duplicate codes within Capture
                $idNumbersCollection = $contactEntity->getIdNumbers();
                foreach ($idNumbersCollection as $numberEntity) {
                    if (
                        $numberEntity->getId() !== $idNumber->getId()
                        && $numberEntity->getContact() === $idNumber->getContact()
                        && $numberEntity->getNumber() === $idNumber->getNumber()
                        && $numberEntity->getType() === $idNumber->getType()
                    ) {
                        $contactEntity->removeIdNumber($numberEntity);
                    }
                }
            }
        }

        return $this;
    }

    /**
     * @return bool|null
     */
    private function extractLoneWorker($value)
    {
        if ($value == 'Y') {
            return true;
        }

        if ($value == 'N') {
            return false;
        }

        return null;
    }

    private function idNumbersMap(): array
    {
        if ($this->idNumberMap !== null) {
            return $this->idNumberMap;
        }
        $this->idNumberMap = [];
        $idNumberTypes = $this->db::PDO_fetch_all(
            "SELECT code, cod_mapping FROM code_con_number_type WHERE cod_mapping IS NOT NULL AND cod_mapping != ''",
        );

        foreach ($idNumberTypes as $idNumberType) {
            $this->idNumberMap[$idNumberType['code']] = $idNumberType['cod_mapping'];
        }

        return $this->idNumberMap;
    }

    private function extractFieldId($value): ?int
    {
        if ($value === null) {
            return null;
        }

        return (int) ($value['id'] ?? $value);
    }
}
