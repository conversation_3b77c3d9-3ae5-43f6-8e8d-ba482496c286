<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\ContactEntity;
use app\models\contact\entities\IdNumberEntity;

class IdNumberHydrator
{
    public function hydrate(array $data, ContactEntity $contactEntity, ?IdNumberEntity $entity = null): IdNumberEntity
    {
        $entity ??= new IdNumberEntity();

        return $entity
            ->setId(!empty($data['id']) ? (int) $data['id'] : $entity->getId())
            ->setType($data['type'])
            ->setNumber($data['number'])
            ->setContact($contactEntity);
    }

    public function extract(IdNumberEntity $idNumberEntity): array
    {
        return array_filter([
            'id' => $idNumberEntity->getId(),
            'number' => $idNumberEntity->getNumber(),
            'type' => $idNumberEntity->getType(),
        ]);
    }
}
