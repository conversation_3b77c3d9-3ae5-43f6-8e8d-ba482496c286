<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\AddressEntity;
use app\models\contact\entities\ContactEntity;
use app\models\generic\valueObjects\Address;
use app\models\generic\valueObjects\PostCode;
use app\models\generic\valueObjects\RecordID;

class AddressHydrator
{
    public function hydrate(
        array $data,
        ContactEntity $contactEntity,
        ?AddressEntity $addressEntity = null
    ): AddressEntity {
        // TODO Update this logic once Carlton implemented single line address.
        $multiLineAddress = new Address(
            $data['line1'],
            $data['line2'],
            $data['line3'],
            $data['city'],
            $data['county'],
            $data['country'],
        );

        $addressEntity ??= new AddressEntity();

        return $addressEntity
            ->setId(isset($data['id']) ? (new RecordID($data['id']))->getId() : null)
            ->setLine1($multiLineAddress->getLine1())
            ->setLine2($multiLineAddress->getLine2())
            ->setLine3($multiLineAddress->getLine3())
            ->setCity($multiLineAddress->getCity())
            ->setCounty($multiLineAddress->getCounty())
            ->setCountry($multiLineAddress->getCountry())
            ->setAddress((string) $multiLineAddress)
            ->setPostcode((string) (new PostCode($data['postcode'])))
            ->setType($data['type'])
            ->setContact($contactEntity);
    }

    public function extract(AddressEntity $addressEntity): array
    {
        $addressCheck = !empty($addressEntity->getLine1())
            || !empty($addressEntity->getLine2())
            || !empty($addressEntity->getLine3())
            || !empty($addressEntity->getCity())
            || !empty($addressEntity->getCounty())
            || !empty($addressEntity->getCountry())
            || !empty($addressEntity->getPostcode());

        if (!$addressCheck) {
            return [];
        }

        // TODO Update this logic once Carlton implemented single line address.
        return [
            'id' => $addressEntity->getId() && $addressCheck ? (new RecordID($addressEntity->getId()))->getId() : null,
            'line1' => $addressEntity->getLine1(),
            'line2' => $addressEntity->getLine2(),
            'line3' => $addressEntity->getLine3(),
            'city' => $addressEntity->getCity(),
            'county' => $addressEntity->getCounty(),
            'country' => $addressEntity->getCountry(),
            'postcode' => $addressEntity->getPostcode(),
            'type' => $addressEntity->getType(),
        ];
    }
}
