<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\ContactEntity;
use app\models\contact\entities\EmailEntity;
use app\models\generic\valueObjects\EmailAddress;
use app\models\generic\valueObjects\RecordID;

use function array_filter;

class EmailHydrator
{
    public function hydrate(array $data, ContactEntity $contactEntity, ?EmailEntity $emailEntity = null): EmailEntity
    {
        $emailEntity ??= new EmailEntity();

        return $emailEntity
            ->setId(isset($data['id']) ? (new RecordID($data['id']))->getId() : null)
            ->setEmail((string) (new EmailAddress($data['email'])))
            ->setType($data['type'])
            ->setContact($contactEntity);
    }

    public function extract(EmailEntity $emailEntity): array
    {
        $requiredFields = !empty($emailEntity->getEmail());

        return array_filter([
            'id' => $emailEntity->getId() && $requiredFields ? (new RecordID($emailEntity->getId()))->getId() : null,
            'email' => $emailEntity->getEmail(),
            'type' => $requiredFields ? $emailEntity->getType() : null,
        ]);
    }
}
