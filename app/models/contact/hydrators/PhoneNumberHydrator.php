<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\ContactEntity;
use app\models\contact\entities\PhoneNumberEntity;
use app\models\generic\valueObjects\PhoneNumber;
use app\models\generic\valueObjects\RecordID;

use function array_filter;

class PhoneNumberHydrator
{
    public function hydrate(array $data, ContactEntity $contactEntity, ?PhoneNumberEntity $phoneNumberEntity = null): PhoneNumberEntity
    {
        $phoneNumberEntity ??= new PhoneNumberEntity();

        return $phoneNumberEntity
            ->setId(isset($data['id']) ? (new RecordID($data['id']))->getId() : null)
            ->setPhoneNumber((string) (new PhoneNumber($data['number'])))
            ->setType($data['type'])
            ->setContact($contactEntity);
    }

    public function extract(PhoneNumberEntity $phoneNumberEntity): array
    {
        $requiredFields = !empty($phoneNumberEntity->getPhoneNumber());

        return array_filter([
            'id' => $phoneNumberEntity->getId() && $requiredFields ? (new RecordID($phoneNumberEntity->getId()))->getId() : null,
            'number' => $phoneNumberEntity->getPhoneNumber(),
            'type' => $requiredFields ? $phoneNumberEntity->getType() : null,
        ]);
    }
}
