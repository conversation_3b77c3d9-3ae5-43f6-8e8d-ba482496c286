<?php

namespace app\models\contact\hydrators;

use app\framework\DoctrineEntityManagerFactory;
use app\services\carlton\generic\ApprovalStatusAdapter;
use app\services\carlton\generic\DateAdapter;
use app\services\carlton\generic\MultiSelectFieldApiAdapter;
use app\services\carlton\generic\SingleSelectFieldApiAdapter;
use DatixDBQuery;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class CarltonContactHydratorFactory
{
    public function create(): CarltonContactHydrator
    {
        return new CarltonContactHydrator(
            new SingleSelectFieldApiAdapter(),
            new MultiSelectFieldApiAdapter(),
            new ApprovalStatusAdapter(),
            new DateAdapter(),
            new DatixDBQuery(),
            Container::get(Registry::class),
            new EmailHydrator(),
            new PhoneNumberHydrator(),
            new AddressHydrator(),
            new IdNumberHydrator(),
            (new DoctrineEntityManagerFactory())->getInstance(),
        );
    }
}
