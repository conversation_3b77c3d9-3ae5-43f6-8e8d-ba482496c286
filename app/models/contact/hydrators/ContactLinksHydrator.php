<?php

namespace app\models\contact\hydrators;

use app\models\contact\entities\ContactEntity;
use app\models\contact\entities\ContactLinksEntity;

/** @codeCoverageIgnore */
class ContactLinksHydrator
{
    /**
     * @return ContactLinksEntity
     */
    public function hydrate(array $link, ContactEntity $contactEntity)
    {
        return (new ContactLinksEntity())
            ->setClaId($link['cla_id'])
            ->setComId($link['com_id'])
            ->setIncId($link['inc_id'])
            ->setMorId($link['mor_id'])
            ->setLinkType($link['link_type'])
            ->setLinkRole($link['link_role'])
            ->setLinkDeceased($link['link_deceased'])
            ->setContact($contactEntity);
    }

    /**
     * @return mixed
     */
    public function extract(ContactLinksEntity $contactLinksEntity)
    {
        $contactLinksResponseArray = [
            'con_id' => $contactLinksEntity->getConId(),
            'cla_id' => $contactLinksEntity->getClaId(),
            'com_id' => $contactLinksEntity->getComId(),
            'inc_id' => $contactLinksEntity->getIncId(),
            'mor_id' => $contactLinksEntity->getMorId(),
            'link_type' => $contactLinksEntity->getLinkType(),
            'link_role' => $contactLinksEntity->getLinkRole(),
            'link_deceased' => $contactLinksEntity->getLinkDeceased(),
        ];

        return $this->setAllEmptyValuesToNull($contactLinksResponseArray);
    }

    /**
     * @param $contactLinksResponseArray
     *
     * @return mixed
     */
    protected function setAllEmptyValuesToNull($contactLinksResponseArray)
    {
        array_walk_recursive($contactLinksResponseArray, static function (&$value) {
            if ($value === '') {
                $value = null;
            }
        });

        return $contactLinksResponseArray;
    }
}
