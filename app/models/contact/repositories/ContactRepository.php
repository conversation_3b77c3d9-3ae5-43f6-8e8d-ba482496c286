<?php

namespace app\models\contact\repositories;

use app\models\contact\entities\ContactEntity;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception\DeadlockException;
use Doctrine\DBAL\Exception\RetryableException;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Exception;
use src\logger\Facade\Log;

use function is_array;
use function count;

/**
 * @extends EntityRepository<ContactEntity>
 */
class ContactRepository extends EntityRepository
{
    /**
     * @return array|mixed
     *
     * @throws Exception
     */
    public function findExistingContacts(array $contactData)
    {
        $princeContact = [];

        if (empty($contactData['idNumbers'])) {
            throw new Exception('Patient cannot be identified');
        }

        $sql = <<<'SQL'
            SELECT
                c.*
            FROM contacts_main c
            INNER JOIN contact_id_numbers n ON c.recordid  = n.contact_id
            WHERE
                n.[type] = :type AND n.[number] = :number
            SQL;

        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addRootEntityFromClassMetadata(ContactEntity::class, 'c');

        foreach ($contactData['idNumbers'] as $key => $idNumber) {
            $princeContact = [];

            $query = $this->getEntityManager()->createNativeQuery($sql, $rsm);
            $query->setParameters(['type' => $idNumber['type'], 'number' => $idNumber['number']]);
            $princeContacts = $query->getResult();

            // If more than one contact is found, we cannot be certain which is correct
            if (is_array($princeContacts)) {
                if (count($princeContacts) === 1) {
                    $princeContact = $princeContacts[0];
                } elseif (count($princeContacts) > 1) {
                    $ids = array_map(function (ContactEntity $contact) {
                        return $contact->getRecordid();
                    }, $princeContacts);

                    return ['duplicatesFound' => $ids];
                }
            }

            // If contact exists, get recordid; else generate new recordid
            // Once found, exit loop as we assume that all ID numbers are unique
            if (!empty($princeContact)) {
                break;
            }
        }

        return $princeContact;
    }

    public function find($id, $lockMode = null, $lockVersion = null)
    {
        for ($attempt = 1; $attempt <= 3; ++$attempt) {
            try {
                return parent::find($id);
            } catch (RetryableException $e) {
                if ($attempt === 3) {
                    throw $e;
                }
                if ($e instanceof DeadlockException) {
                    Log::warning('Database encounter a deadlock finding contact by ID, Retrying: Retry ' . $attempt . ' of 2', ['exception' => $e]);
                } else {
                    Log::warning('Database encountered a Retryable error, retrying: Retry ' . $attempt . ' of 2', ['exception' => $e]);
                }
            }
        }

        return null;
    }

    /**
     * @throws EntityNotFoundException
     */
    public function findOneByRecordid(int $recordid): ContactEntity
    {
        $user = $this->find($recordid);
        if ($user === null) {
            throw EntityNotFoundException::fromClassNameAndIdentifier(ContactEntity::class, [(string) $recordid]);
        }

        return $user;
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function findContactsForSync(array $ids): array
    {
        $connection = $this->getEntityManager()->getConnection();
        $qb = $connection->createQueryBuilder();

        return $qb->select('recordid, lastUpdatedDate')
            ->from('contacts_main', 'c')
            ->where($qb->expr()->in('recordid', ':ids'))
            ->setParameter('ids', $ids, Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();
    }
}
