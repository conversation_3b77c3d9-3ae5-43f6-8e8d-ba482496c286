<?php

declare(strict_types=1);

namespace app\models\contact\repositories;

use app\models\contact\entities\ContactLinksEntity;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<ContactLinksEntity>
 */
class ContactLinksRepository extends EntityRepository
{
    /**
     * @throws EntityNotFoundException
     */
    public function findOneByLinkRecordid(int $linkRecordId): ContactLinksEntity
    {
        $entity = $this->find($linkRecordId);

        if ($entity === null) {
            throw EntityNotFoundException::fromClassNameAndIdentifier(ContactLinksEntity::class, [(string) $linkRecordId]);
        }

        return $entity;
    }
}
