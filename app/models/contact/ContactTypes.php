<?php

namespace app\models\contact;

use Exception;

// Make this final so no one can extend it
final class ContactTypes
{
    public const PERSON_AFFECTED = 'A';
    public const EMPLOYEE = 'E';
    public const OTHER_CONTACT = 'N';
    public const WITNESS = 'W';
    public const REPORTER = 'R';
    public const POLICE_OFFICER = 'P';
    public const ALLEGED_ASSAILANT = 'L';
    public const CLAIMANT = 'M';
    public const INDIVIDUAL_RESPONDENT = 'O';
    public const CONSULTANT = 'C';
    public const RESPONDENT = 'G';
    public const DECEASED = 'P';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        // throw an exception if someone can get in here
        throw new Exception("Can't get an instance of ContactTypes");
    }
}
