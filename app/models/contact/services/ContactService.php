<?php

namespace app\models\contact\services;

use app\framework\DoctrineEntityManagerFactory;
use app\models\contact\ContactTypes;
use app\models\contact\entities\ContactEntity;
use app\models\contact\hydrators\CarltonContactHydratorFactory;
use app\models\contact\hydrators\ContactLinksHydrator;
use app\models\generic\valueObjects\Module;
use app\services\carlton\generic\ApprovalStatusAdapter;
use DatixDBQuery;
use Exception;
use InvalidArgumentException;
use InvalidParameterException;
use MapperException;
use PDO;
use src\contacts\model\Contact;
use src\contacts\service\ContactPersister;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\users\model\UserModelFactory;

use function is_array;
use function in_array;

class ContactService
{
    // Constant mapping API field names to DB column names
    // Multidimensional API arrays are indicated by slash delimitation
    // e.g. gender/id refers to ['gender']['id']
    private const contactApiToDbNames =
        [
            'addresses/0/line1' => 'con_line1',
            'addresses/0/line2' => 'con_line2',
            'addresses/0/line3' => 'con_line3',
            'addresses/0/city' => 'con_city',
            'addresses/0/county' => 'con_county',
            'addresses/0/country' => 'con_country',
            'addresses/0/postcode' => 'con_postcode',
            'disabilities' => 'con_disability',
            'dateOfBirth' => 'con_dob',
            'dateOfDeath' => 'con_dod',
            'email' => 'con_email',
            'ethnicity/id' => 'con_ethnicity',
            'forename' => 'con_forenames',
            'gender/id' => 'con_gender',
            'language/id' => 'con_language',
            'middleName' => 'con_middle_name',
            'notes' => 'con_notes',
            'religion/id' => 'con_religion',
            'sexualOrientation/id' => 'con_sex_orientation',
            'socialSecurityNumber' => 'con_social_security_number',
            'state' => 'con_state',
            'subType/id' => 'con_subtype',
            'surname' => 'con_surname',
            'numbers/0/number' => 'con_tel1',
            'numbers/1/number' => 'con_tel2',
            'title' => 'con_title',
            'type/id' => 'con_type',
            'loneWorker' => 'con_work_alone_assessed',
            'locations/0/location/id' => 'location_id',
            'oshaDateHired' => 'osha_date_hired',
            'id' => 'recordid',
            'services/0/service/id' => 'service_id',
            'status/id' => 'rep_approved',
            'taxId' => 'tax_id',
            'employeeStateHired' => 'employee_state_hired',
            'employmentTerminationDate' => 'employment_termination_date',
            'employmentStatusCode' => 'con_employment_status_code',
            'processLevel' => 'con_process_level',
            'jobCode' => 'con_job_code',
            'supervisorName' => 'con_supervisor_name',
            'department' => 'con_department',
            'locationCode' => 'con_location_code',
            'fte' => 'con_fte',
            'lawsonNumber' => 'con_lawson_number',
            'employeeIdAssignedByJurisdiction' => 'employee_id_assigned_by_jurisdiction',
            'hoursWorker' => 'con_hours_worked',
            'hourlyRate' => 'con_hourly_rate',
            'ncciCode' => 'con_ncci_code',
            'occupation' => 'con_occupation',
            'maritalStatus' => 'con_marital_status',
            'emplGrade' => 'con_empl_grade',
            'nationality' => 'nationality',
        ];

    /**
     * @return array
     *
     * Function to map an array with API field names as keys into one with db column names as the keys
     */
    public function convertApiNamesToDatabaseNames(array $contact): array
    {
        $keysToDelete = [];
        // Iterate over an array of API keys with contact values
        foreach ($contact as $conKey => $conValue) {
            // Get all API keys which sit within this API key
            // e.g. Addresses contains multiple API keys for address fields
            $apiKeys = $this->filterByApiKey(self::contactApiToDbNames, $conKey);
            if (!empty($apiKeys)) {
                foreach ($apiKeys as $apiKey) {
                    // Set value here as $conValue will update as array is iteratec through
                    $contactValue = $conValue;
                    // If the returned element is an array, we need to walk the array until elements are found
                    // which will refer to a DB column name
                    if (is_array($conValue)) {
                        // Slash characters delimit array levels
                        $columnValues = explode('/', $apiKey);
                        // Remove first element as we have already navigated to this level
                        array_shift($columnValues);
                        // Iterate through array until final level is reached
                        foreach ($columnValues as $columnValue) {
                            $contactValue = $contactValue[$columnValue];
                        }
                    }
                    $contact[self::contactApiToDbNames[$apiKey]] = $contactValue;
                    $keysToDelete[] = $conKey;
                }
            }
        }

        // Remove elements where API field names are keys
        foreach (array_unique($keysToDelete) as $keyToDelete) {
            unset($contact[$keyToDelete]);
        }

        return $contact;
    }

    /**
     * @throws InvalidParameterException
     * @throws MapperException
     */
    public function getLinkPosition(string $module, array $con): ?string
    {
        $linkedEntityCreatorInitials = $this->getLinkedEntityAuthor($module, $con);
        $linkPosition = $con['link_position'];
        if (!$linkedEntityCreatorInitials) {
            return $linkPosition;
        }

        $userMapper = (new UserModelFactory())->getMapper();
        $user = $userMapper->findByInitials($linkedEntityCreatorInitials);

        if (!$user) {
            return $linkPosition;
        }

        $positionJson = json_decode($user->getPositions(), true);
        if (!empty($con['link_position']) && $positionJson) {
            $positionKey = array_search($con['link_position'], array_column($positionJson, 'id'), true);
            $linkPosition = !empty($positionJson[$positionKey]['name']) ? $positionJson[$positionKey]['name'] : $con['link_position'];
        }

        return $linkPosition;
    }

    public function removeRowFromForm(array $contactFormArray, string $fieldKey): array
    {
        return array_filter($contactFormArray, static function ($field) use ($fieldKey) {
            if (is_array($field) && $field['Name'] === $fieldKey) {
                return false;
            }

            return $field !== $fieldKey;
        });
    }

    /**
     * @param bool $reportedAnonymously
     * @param string $reporterRole
     * @param string $linkRole
     * @param string $linkType
     *
     * @return bool
     */
    public function shouldSkipAnonymisedReporter($reportedAnonymously, $reporterRole, $linkRole, $linkType)
    {
        $hasReporterLinkRole = $linkRole === $reporterRole;
        // Need to check request for the link_type as the Post will have been set to N
        $isReporterSection = $linkType === ContactTypes::REPORTER;

        return $reportedAnonymously && ($hasReporterLinkRole || $isReporterSection);
    }

    /**
     * takes array of contacts and processes the sync request between carlton and capture.
     *
     * @param Contact[] $data
     *
     * @return Contact[]
     *
     * @throws Exception
     */
    public function processFromData(array $data): array
    {
        $contacts = [];
        $persist = Container::get(ContactPersister::class);
        $repository = (new DoctrineEntityManagerFactory())->getInstance()->getRepository(ContactEntity::class);
        $hydrator = (new CarltonContactHydratorFactory())->create();
        foreach ($data as $item) {
            if (empty($item['id'])) {
                $id = $persist->addContact($hydrator->hydrate($item));
                $contact = $repository->findOneBy(['recordid' => $id]);
            } else {
                /** @var ContactEntity $contact */
                $contact = $repository->findOneBy(['recordid' => $item['id']]);
                // stops override of status if one isn't proved
                if (!isset($item['status'])) {
                    $item['status']['id'] = (new ApprovalStatusAdapter())->adaptForResponse($contact->getRepApproved());
                }

                // merges existing data with new values
                $updated = array_merge(
                    $hydrator->extract($contact),
                    $hydrator->extract($hydrator->hydrate($item)),
                );
                $updated['status'] = ['id' => $updated['status']];

                $persist->updateContact($hydrator->hydrate($updated));
            }

            $contacts[] = $contact;
        }

        return $contacts;
    }

    /**
     * @param ContactEntity[] $contacts
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function linkContactsToRecord(string $module, int $recordId, string $linkType, array $contacts): void
    {
        $field = null;
        switch ($module) {
            case Module::INCIDENTS:
                $field = 'inc_id';

                break;
            case Module::FEEDBACK:
                $field = 'com_id';

                break;
            case Module::CLAIMS:
                $field = 'cla_id';

                break;
            default:
                throw new InvalidArgumentException('Invalid module provided');
        }

        $linkHydrator = new ContactLinksHydrator();
        $em = (new DoctrineEntityManagerFactory())->getInstance();
        $role = Container::get(Registry::class)->getParm('REPORTER_ROLE', 'REP', true);

        foreach ($contacts as $contact) {
            $linkEntity = $linkHydrator->hydrate(
                [
                    $field => $recordId,
                    'link_type' => $linkType,
                    'link_role' => $role,
                ],
                $contact,
            );
            $em->persist($linkEntity);
        }
        $em->flush();
    }

    /**
     * @param $searchValue
     *
     * @return array
     *
     * Function to filter the list of API field names by a particular value
     * Returns all values where the top level of the array matches the searched value
     */
    protected function filterByApiKey(array $array, $searchValue)
    {
        $keys = [];
        foreach ($array as $key => $item) {
            $keyValues = explode('/', $key);
            if ($keyValues[0] === $searchValue) {
                $keys[] = $key;
            }
        }

        return array_unique($keys);
    }

    /**
     * @param string[] $linkedContactArray
     *
     * @throws InvalidParameterException
     */
    private function getLinkedEntityAuthor(string $module, array $linkedContactArray): ?string
    {
        if (!in_array($module, [Module::INCIDENTS, Module::FEEDBACK], true)) {
            throw new InvalidParameterException(
                sprintf(
                    'Module must either be %s or %s',
                    Module::INCIDENTS,
                    Module::FEEDBACK,
                ),
            );
        }

        $table = 'compl_main';
        $linkedEntityIdKey = 'com_id';

        if ($module === Module::INCIDENTS) {
            $table = 'incidents_main';
            $linkedEntityIdKey = 'inc_id';
        }
        $sql = sprintf('SELECT createdby FROM %s WHERE recordid = %s', $table, $linkedContactArray[$linkedEntityIdKey]);

        return DatixDBQuery::PDO_fetch($sql, [], PDO::FETCH_COLUMN);
    }
}
