<?php

namespace app\models\contact\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity]
#[Table(name: "holding_letters")]
class HoldingLetterEntity
{
    /**
     * @OA\Property(property="id", type="integer", format="int32")
     *
     * @var int
     */
    #[Id]
    #[GeneratedValue(strategy: "IDENTITY")]
    #[Column(type: "integer")]
    protected $id;

    /**
     * FK from link_compl table.
     *
     * @OA\Property(property="linkComplId", type="integer", format="int32")
     *
     * @var int
     */
    #[Column(name: "link_compl_id", type: "integer")]
    protected $linkComplId;

    /**
     * @OA\Property(property="due", type="string", format="date-time", nullable=true)
     *
     * @var string
     */
    #[Column(type: "string")]
    protected $due;

    /**
     * @OA\Property(property="done", type="string", format="date-time", nullable=true)
     *
     * @var string
     */
    #[Column(type: "string")]
    protected $done;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getLinkComplId(): int
    {
        return $this->linkComplId;
    }

    public function setLinkComplId(int $linkComplId): self
    {
        $this->linkComplId = $linkComplId;

        return $this;
    }

    public function getDue(): string
    {
        return $this->due;
    }

    public function setDue(string $due): self
    {
        $this->due = $due;

        return $this;
    }

    public function getDone(): string
    {
        return $this->done;
    }

    public function setDone(string $done): self
    {
        $this->done = $done;

        return $this;
    }
}
