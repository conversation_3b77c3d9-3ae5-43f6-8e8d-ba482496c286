<?php

namespace app\models\contact\entities;

use app\models\contact\repositories\ContactRepository;
use app\models\feedback\entities\FeedbackLinksEntity;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;
use Exception;
use JsonException;
use OpenApi\Annotations as OA;

use const JSON_THROW_ON_ERROR;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity(repositoryClass: ContactRepository::class)]
#[Table(name: "contacts_main")]
class ContactEntity
{
    /**
     * @var IdNumberEntity[]|Collection
     *
     * @OA\Property(
     *     property="idNumbers",
     *     type="array",
     *     @OA\Items(ref="#/components/schemas/IdNumberEntity")
     * )
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: IdNumberEntity::class,
        cascade: ["persist", "remove"],
        fetch: "EAGER",
        orphanRemoval: true
    )]
    protected Collection $id_numbers;

    /**
     * @OA\Property(type="string", nullable=true)
     */
    #[Column(name: "job_title", type: "string", nullable: true)]
    protected ?string $jobTitle = null;

    #[Column(type: "string")]
    protected ?string $source_of_record = null;

    #[Column(name: "api_source", type: "string", nullable: true)]
    protected ?string $apiSource = null;

    /**
     * @OA\Property(property="id", type="integer", format="int32")
     */
    #[Id]
    #[GeneratedValue(strategy: "NONE")]
    #[Column(type: "integer")]
    private ?int $recordid = null;

    /**
     * @OA\Property(property="title", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_title = null;

    /**
     * @OA\Property(property="forename", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 225, nullable: true)]
    private ?string $con_forenames = null;

    /**
     * @OA\Property(property="surname", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_surname = null;

    #[Column(type: "string", length: 10, nullable: true)]
    private ?string $con_postcode = null;

    /**
     * @OA\Property(property="numbers", type="array", @OA\Items(ref="#/components/schemas/PhoneNumberEntity"))
     */
    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_tel1 = null;

    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_tel2 = null;

    /**
     * @OA\Property(
     *     property="type",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_type = null;

    /**
     * @todo change type to datetime
     *
     * @OA\Property(property="dateOfBirth", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string", nullable: true)]
    private $con_dob;

    #[Column(type: "string", length: 64, nullable: true)]
    private ?string $con_number = null;

    /**
     * @OA\Property(
     *     property="gender",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_gender = null;

    /**
     * @OA\Property(
     *     property="status",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true, enum={"1", "2", "3"})
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $rep_approved = null;

    /**
     * @OA\Property(
     *     property="ethnicity",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_ethnicity = null;

    #[Column(type: "string", length: 128, nullable: true)]
    private ?string $con_email = null;

    /**
     * @OA\Property(
     *     property="subType",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_subtype = null;

    #[Column(type: "string", length: 20, nullable: true)]
    private ?string $con_nhsno = null;

    /**
     * @todo change type datetime
     *
     * @OA\Property(property="dateOfDeath", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string", nullable: true)]
    private $con_dod;

    /**
     * @OA\Property(
     *     property="language",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_language = null;

    /**
     * @OA\Property(property="disabilities", type="array", @OA\Items(type="string"))
     */
    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_disability = null;

    #[Column(type: "string", length: 128, nullable: true)]
    private ?string $con_police_number = null;

    /**
     * @OA\Property(property="loneWorker", type="boolean")
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_work_alone_assessed = null;

    /**
     * @OA\Property(
     *     property="sexualOrientation",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_sex_orientation = null;

    /**
     * @OA\Property(
     *     property="religion",
     *     type="object",
     *     @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     * )
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_religion = null;

    /**
     * @OA\Property(property="notes", type="string", nullable=true)
     * @TODO figure out if the name being a different case matters here or not between annotation and column
     */
    #[Column(name: "CON_NOTES", type: "text", nullable: true)]
    private ?string $con_notes = null;

    /**
     * @OA\Property(property="locations", type="array", @OA\Items(
     *     @OA\Property(
     *         property="service",
     *         type="object",
     *         @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     *     )
     * ))
     */
    #[Column(type: "integer", nullable: true)]
    private ?int $location_id = null;

    /**
     * @OA\Property(property="services", type="array", @OA\Items(
     *     @OA\Property(
     *         property="location",
     *         type="object",
     *         @OA\Property(property="id", type="string", maxLength=6, nullable=true)
     *     )
     * ))
     */
    #[Column(type: "integer", nullable: true)]
    private ?int $service_id = null;

    #[Column(type: "string", length: 225, nullable: true)]
    private ?string $con_staff_number = null;

    /**
     * @todo change type to datetime
     *
     * @OA\Property(property="oshaDateHired", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string", nullable: true)]
    private $osha_date_hired;

    /**
     * @OA\Property(property="city", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_city = null;

    /**
     * @OA\Property(property="state", type="string", maxLength=2, nullable=true)
     */
    #[Column(type: "string", length: 2, nullable: true)]
    private ?string $con_state = null;

    /**
     * @OA\Property(property="socialSecurityNumber", type="string", maxLength=11, nullable=true)
     */
    #[Column(type: "string", length: 11, nullable: true)]
    private ?string $con_social_security_number = null;

    /**
     * @OA\Property(property="middleName", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_middle_name = null;

    /**
     * @OA\Property(property="taxId", type="string", maxLength=9, nullable=true)
     */
    #[Column(type: "string", length: 9, nullable: true)]
    private ?string $tax_id = null;

    /**
     * @OA\Property(property="county", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_county = null;

    /**
     * @OA\Property(property="employeeStateHired", type="string", maxLength=2, nullable=true)
     */
    #[Column(type: "string", length: 2, nullable: true)]
    private ?string $employee_state_hired = null;

    /**
     * @todo change type to date
     *
     * @OA\Property(property="employmentTerminationDate", type="string", format="date", nullable=true)
     */
    #[Column(type: "string", nullable: true)]
    private $employment_termination_date;

    /**
     * @OA\Property(property="employmentStatusCode", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_employment_status_code = null;

    /**
     * @OA\Property(property="processLevel", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_process_level = null;

    /**
     * @OA\Property(property="jobCode", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_job_code = null;

    /**
     * @OA\Property(property="supervisorName", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_supervisor_name = null;

    /**
     * @OA\Property(property="department", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_department = null;

    /**
     * @OA\Property(property="locationCode", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_location_code = null;

    /**
     * @OA\Property(property="fte", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer", nullable: true)]
    private ?int $con_fte = null;

    /**
     * @OA\Property(property="lawsonNumber", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_lawson_number = null;

    /**
     * @OA\Property(property="address", type="array", @OA\Items(ref="#/components/schemas/AddressEntity"))
     */
    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_line1 = null;

    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_line2 = null;

    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_line3 = null;

    #[Column(type: "string", length: 254, nullable: true)]
    private ?string $con_country = null;

    /**
     * @OA\Property(property="employeeIdAssignedByJurisdiction", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer", nullable: true)]
    private ?int $employee_id_assigned_by_jurisdiction = null;

    /**
     * @OA\Property(property="hoursWorked", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer", nullable: true)]
    private ?int $con_hours_worked = null;

    /**
     * @OA\Property(property="hourlyRate", type="number", format="double", nullable=true)
     */
    #[Column(type: "decimal", precision: 17, scale: 6, nullable: true)]
    private ?float $con_hourly_rate = null;

    /**
     * @OA\Property(property="ncciCode", type="string", maxLength=255, nullable=true)
     */
    #[Column(type: "string", length: 255, nullable: true)]
    private ?string $con_ncci_code = null;

    /**
     * @OA\Property(property="occupation", type="string", maxLength=64, nullable=true)
     */
    #[Column(type: "string", length: 64, nullable: true)]
    private ?string $con_occupation = null;

    /**
     * @OA\Property(property="maritalStatus", type="string", maxLength=2, nullable=true)
     */
    #[Column(type: "string", length: 2, nullable: true)]
    private ?string $con_marital_status = null;

    /**
     * @OA\Property(property="remoteId", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 256, nullable: true)]
    private ?string $con_remote_id = null;

    /**
     * @OA\Property(property="emplGrade", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $con_empl_grade = null;

    /**
     * @OA\Property(
     *     property="con_id_numbers",
     *     type="array",
     *     nullable=true,
     *     @OA\Items(
     *         type="object",
     *         @OA\Property(property="id", type="integer", format="int32"),
     *         @OA\Property(property="type", type="string"),
     *     )
     * )
     *
     * Suppressing write only inspection because this is written to the DB so it goes to ETL, it's just never used by capture
     * @noinspection PhpPropertyOnlyWrittenInspection
     */
    #[Column(type: "string", nullable: true)]
    private ?string $con_id_numbers = null;

    /**
     * @OA\Property(property="nationality", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $nationality = null;

    /**
     * @var Collection<EmailEntity>
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: EmailEntity::class,
        cascade: ["persist", "remove"],
        fetch: "EAGER",
        orphanRemoval: true,
    )]
    private Collection $emails;

    /**
     * @var Collection<PhoneNumberEntity>
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: PhoneNumberEntity::class,
        cascade: ["persist", "remove"],
        fetch: "EAGER",
        orphanRemoval: true,
    )]
    private Collection $phone_numbers;

    /**
     * @var Collection<AddressEntity>
     *
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: AddressEntity::class,
        cascade: ["persist", "remove"],
        fetch: "EAGER",
        orphanRemoval: true
    )]
    private Collection $addresses;

    /**
     * @var Collection<ContactLinksEntity>
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: ContactLinksEntity::class
    )]
    private Collection $links;

    /**
     * @var Collection<FeedbackLinksEntity>
     */
    #[OneToMany(
        mappedBy: "contact",
        targetEntity: FeedbackLinksEntity::class
    )]
    private Collection $feedback_links;

    #[Column(type: "datetime", nullable: true)]
    private ?DateTimeInterface $lastUpdatedDate = null;

    public function __construct()
    {
        $this->emails = new ArrayCollection();
        $this->phone_numbers = new ArrayCollection();
        $this->addresses = new ArrayCollection();
        $this->links = new ArrayCollection();
        $this->feedback_links = new ArrayCollection();
        $this->id_numbers = new ArrayCollection();
    }

    /**
     * @return int
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param int $recordid
     */
    public function setRecordid($recordid): self
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return string
     */
    public function getConTitle()
    {
        return $this->con_title;
    }

    /**
     * @param string $con_title
     */
    public function setConTitle($con_title): self
    {
        $this->con_title = $con_title;

        return $this;
    }

    /**
     * @return string
     */
    public function getConForenames()
    {
        return $this->con_forenames;
    }

    /**
     * @param string $con_forenames
     */
    public function setConForenames($con_forenames): self
    {
        $this->con_forenames = $con_forenames;

        return $this;
    }

    /**
     * @return string
     */
    public function getConMiddleName()
    {
        return $this->con_middle_name;
    }

    /**
     * @param string $con_middle_name
     */
    public function setConMiddleName($con_middle_name): self
    {
        $this->con_middle_name = $con_middle_name;

        return $this;
    }

    /**
     * @return string
     */
    public function getConSurname()
    {
        return $this->con_surname;
    }

    /**
     * @param string $con_surname
     */
    public function setConSurname($con_surname): self
    {
        $this->con_surname = $con_surname;

        return $this;
    }

    /**
     * @return string
     */
    public function getConPostcode()
    {
        return $this->con_postcode;
    }

    /**
     * @param string $con_postcode
     */
    public function setConPostcode($con_postcode): self
    {
        $this->con_postcode = $con_postcode;

        return $this;
    }

    /**
     * @return string
     */
    public function getConTel1()
    {
        return $this->con_tel1;
    }

    /**
     * @param string $con_tel1
     *
     * @return ContactEntity
     */
    public function setConTel1($con_tel1)
    {
        $this->con_tel1 = $con_tel1;

        return $this;
    }

    /**
     * @return string
     */
    public function getConTel2()
    {
        return $this->con_tel2;
    }

    /**
     * @param string $con_tel2
     */
    public function setConTel2($con_tel2): self
    {
        $this->con_tel2 = $con_tel2;

        return $this;
    }

    /**
     * @return string
     */
    public function getConGender()
    {
        return $this->con_gender;
    }

    /**
     * @param string $con_gender
     *
     * @return ContactEntity
     */
    public function setConGender($con_gender)
    {
        $this->con_gender = $con_gender;

        return $this;
    }

    /**
     * @return string
     */
    public function getConNumber()
    {
        return $this->con_number;
    }

    /**
     * @param string $con_number
     *
     * @return ContactEntity
     */
    public function setConNumber($con_number)
    {
        $this->con_number = $con_number;

        return $this;
    }

    /**
     * @return string
     */
    public function getConStaffNumber()
    {
        return $this->con_staff_number;
    }

    /**
     * @param string $con_staff_number
     *
     * @return ContactEntity
     */
    public function setConStaffNumber($con_staff_number)
    {
        $this->con_staff_number = $con_staff_number;

        return $this;
    }

    /**
     * @return string
     */
    public function getConNhsno()
    {
        return $this->con_nhsno;
    }

    /**
     * @param string $con_nhsno
     *
     * @return ContactEntity
     */
    public function setConNhsno($con_nhsno)
    {
        $this->con_nhsno = $con_nhsno;

        return $this;
    }

    /**
     * @return string
     */
    public function getConSocialSecurityNumber()
    {
        return $this->con_social_security_number;
    }

    /**
     * @return ContactEntity
     */
    public function setConSocialSecurityNumber($con_social_security_number)
    {
        $this->con_social_security_number = $con_social_security_number;

        return $this;
    }

    /**
     * @return string
     */
    public function getConPoliceNumber()
    {
        return $this->con_police_number;
    }

    /**
     * @param string $con_police_number
     *
     * @return ContactEntity
     */
    public function setConPoliceNumber($con_police_number)
    {
        $this->con_police_number = $con_police_number;

        return $this;
    }

    /**
     * @return string
     */
    public function getRepApproved()
    {
        return $this->rep_approved;
    }

    /**
     * @param string $rep_approved
     *
     * @return ContactEntity
     */
    public function setRepApproved($rep_approved)
    {
        $this->rep_approved = $rep_approved;

        return $this;
    }

    /**
     * @return string
     */
    public function getConEthnicity()
    {
        return $this->con_ethnicity;
    }

    /**
     * @param string $con_ethnicity
     *
     * @return ContactEntity
     */
    public function setConEthnicity($con_ethnicity)
    {
        $this->con_ethnicity = $con_ethnicity;

        return $this;
    }

    /**
     * @return string
     */
    public function getConType()
    {
        return $this->con_type;
    }

    /**
     * @param string $con_type
     *
     * @return ContactEntity
     */
    public function setConType($con_type)
    {
        $this->con_type = $con_type;

        return $this;
    }

    /**
     * @return string
     */
    public function getConSubtype()
    {
        return $this->con_subtype;
    }

    /**
     * @param string $con_subtype
     *
     * @return ContactEntity
     */
    public function setConSubtype($con_subtype)
    {
        $this->con_subtype = $con_subtype;

        return $this;
    }

    /**
     * @return string
     */
    public function getConLanguage()
    {
        return $this->con_language;
    }

    /**
     * @param string $con_language
     *
     * @return ContactEntity
     */
    public function setConLanguage($con_language)
    {
        $this->con_language = $con_language;

        return $this;
    }

    /**
     * @return string
     */
    public function getConDisability()
    {
        return $this->con_disability;
    }

    /**
     * @param string $con_disability
     *
     * @return ContactEntity
     */
    public function setConDisability($con_disability)
    {
        $this->con_disability = $con_disability;

        return $this;
    }

    /**
     * @return string
     */
    public function getConWorkAloneAssessed()
    {
        return $this->con_work_alone_assessed;
    }

    /**
     * @param string $con_work_alone_assessed
     *
     * @return ContactEntity
     */
    public function setConWorkAloneAssessed($con_work_alone_assessed)
    {
        $this->con_work_alone_assessed = $con_work_alone_assessed;

        return $this;
    }

    /**
     * @return string
     */
    public function getConSexOrientation()
    {
        return $this->con_sex_orientation;
    }

    /**
     * @param string $con_sex_orientation
     *
     * @return ContactEntity
     */
    public function setConSexOrientation($con_sex_orientation)
    {
        $this->con_sex_orientation = $con_sex_orientation;

        return $this;
    }

    /**
     * @return string
     */
    public function getConReligion()
    {
        return $this->con_religion;
    }

    /**
     * @param string $con_religion
     *
     * @return ContactEntity
     */
    public function setConReligion($con_religion)
    {
        $this->con_religion = $con_religion;

        return $this;
    }

    /**
     * @return string
     */
    public function getConNotes()
    {
        return $this->con_notes;
    }

    /**
     * @param string $con_notes
     *
     * @return ContactEntity
     */
    public function setConNotes($con_notes)
    {
        $this->con_notes = $con_notes;

        return $this;
    }

    /**
     * @return string
     */
    public function getConEmail()
    {
        return $this->con_email;
    }

    /**
     * @param string $con_email
     *
     * @return ContactEntity
     */
    public function setConEmail($con_email)
    {
        $this->con_email = $con_email;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConDob()
    {
        return $this->con_dob;
    }

    /**
     * @param mixed $con_dob
     *
     * @return ContactEntity
     */
    public function setConDob($con_dob)
    {
        $this->con_dob = $con_dob;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConDod()
    {
        return $this->con_dod;
    }

    /**
     * @param mixed $con_dod
     *
     * @return ContactEntity
     */
    public function setConDod($con_dod)
    {
        $this->con_dod = $con_dod;

        return $this;
    }

    /**
     * @return Collection<EmailEntity>
     */
    public function getEmails(): Collection
    {
        return $this->emails;
    }

    /**
     * @return Collection<PhoneNumberEntity>
     */
    public function getPhoneNumbers(): Collection
    {
        return $this->phone_numbers;
    }

    /**
     * @return Collection<AddressEntity>
     */
    public function getAddresses(): Collection
    {
        return $this->addresses;
    }

    /**
     * @return Collection<ContactLinksEntity>
     */
    public function getLinks(): Collection
    {
        return $this->links;
    }

    public function addLink(ContactLinksEntity $link): self
    {
        $link->setContact($this);
        $this->links->add($link);

        return $this;
    }

    /**
     * @return int
     */
    public function getLocationId()
    {
        return $this->location_id;
    }

    /**
     * @param int $location_id
     *
     * @return ContactEntity
     */
    public function setLocationId($location_id)
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * @return int
     */
    public function getServiceId()
    {
        return $this->service_id;
    }

    /**
     * @param int $service_id
     *
     * @return ContactEntity
     */
    public function setServiceId($service_id)
    {
        $this->service_id = $service_id;

        return $this;
    }

    /**
     * @return string
     */
    public function getOshaDateHired()
    {
        return $this->osha_date_hired;
    }

    /**
     * @param string $oshaDateHired
     *
     * @return ContactEntity
     */
    public function setOshaDateHired($oshaDateHired)
    {
        $this->osha_date_hired = $oshaDateHired;

        return $this;
    }

    /**
     * @return string
     */
    public function getConCity()
    {
        return $this->con_city;
    }

    /**
     * @param string $con_city
     *
     * @return ContactEntity
     */
    public function setConCity($con_city)
    {
        $this->con_city = $con_city;

        return $this;
    }

    /**
     * @return string
     */
    public function getConState()
    {
        return $this->con_state;
    }

    /**
     * @param string $con_state
     *
     * @return ContactEntity
     */
    public function setConState($con_state)
    {
        $this->con_state = $con_state;

        return $this;
    }

    /**
     * @return string
     */
    public function getConCounty()
    {
        return $this->con_county;
    }

    /**
     * @param string $con_county
     *
     * @return ContactEntity
     */
    public function setConCounty($con_county)
    {
        $this->con_county = $con_county;

        return $this;
    }

    /**
     * @return string
     */
    public function getTaxId()
    {
        return $this->tax_id;
    }

    /**
     * @param string $tax_id
     *
     * @return ContactEntity
     */
    public function setTaxId($tax_id)
    {
        $this->tax_id = $tax_id;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmployeeStateHired()
    {
        return $this->employee_state_hired;
    }

    /**
     * @param string $employee_state_hired
     *
     * @return ContactEntity
     */
    public function setEmployeeStateHired($employee_state_hired)
    {
        $this->employee_state_hired = $employee_state_hired;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmploymentTerminationDate()
    {
        return $this->employment_termination_date;
    }

    /**
     * @param string $employment_termination_date
     *
     * @return ContactEntity
     */
    public function setEmploymentTerminationDate($employment_termination_date)
    {
        $this->employment_termination_date = $employment_termination_date;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConEmploymentStatusCode()
    {
        return $this->con_employment_status_code;
    }

    /**
     * @param mixed $con_employment_status_code
     *
     * @return ContactEntity
     */
    public function setConEmploymentStatusCode($con_employment_status_code)
    {
        $this->con_employment_status_code = $con_employment_status_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConProcessLevel()
    {
        return $this->con_process_level;
    }

    /**
     * @param mixed $con_process_level
     *
     * @return ContactEntity
     */
    public function setConProcessLevel($con_process_level)
    {
        $this->con_process_level = $con_process_level;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConJobCode()
    {
        return $this->con_job_code;
    }

    /**
     * @param mixed $con_job_code
     *
     * @return ContactEntity
     */
    public function setConJobCode($con_job_code)
    {
        $this->con_job_code = $con_job_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConSupervisorName()
    {
        return $this->con_supervisor_name;
    }

    /**
     * @param mixed $con_supervisor_name
     *
     * @return ContactEntity
     */
    public function setConSupervisorName($con_supervisor_name)
    {
        $this->con_supervisor_name = $con_supervisor_name;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConDepartment()
    {
        return $this->con_department;
    }

    /**
     * @param mixed $con_department
     *
     * @return ContactEntity
     */
    public function setConDepartment($con_department)
    {
        $this->con_department = $con_department;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConLocationCode()
    {
        return $this->con_location_code;
    }

    /**
     * @param mixed $con_location_code
     *
     * @return ContactEntity
     */
    public function setConLocationCode($con_location_code)
    {
        $this->con_location_code = $con_location_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConFte()
    {
        return $this->con_fte;
    }

    /**
     * @param mixed $con_fte
     *
     * @return ContactEntity
     */
    public function setConFte($con_fte)
    {
        $this->con_fte = $con_fte;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConLawsonNumber()
    {
        return $this->con_lawson_number;
    }

    /**
     * @param mixed $con_lawson_number
     *
     * @return ContactEntity
     */
    public function setConLawsonNumber($con_lawson_number)
    {
        $this->con_lawson_number = $con_lawson_number;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEmployeeIdAssignedByJurisdiction()
    {
        return $this->employee_id_assigned_by_jurisdiction;
    }

    /**
     * @param mixed $employee_id_assigned_by_jurisdiction
     */
    public function setEmployeeIdAssignedByJurisdiction($employee_id_assigned_by_jurisdiction)
    {
        $this->employee_id_assigned_by_jurisdiction = $employee_id_assigned_by_jurisdiction;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConLine1()
    {
        return $this->con_line1;
    }

    /**
     * @param mixed $con_line1
     *
     * @return ContactEntity
     */
    public function setConLine1($con_line1)
    {
        $this->con_line1 = $con_line1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConLine2()
    {
        return $this->con_line2;
    }

    /**
     * @param mixed $con_line2
     *
     * @return ContactEntity
     */
    public function setConLine2($con_line2)
    {
        $this->con_line2 = $con_line2;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConLine3()
    {
        return $this->con_line3;
    }

    /**
     * @param mixed $con_line3
     *
     * @return ContactEntity
     */
    public function setConLine3($con_line3)
    {
        $this->con_line3 = $con_line3;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConCountry()
    {
        return $this->con_country;
    }

    /**
     * @param mixed $con_country
     *
     * @return ContactEntity
     */
    public function setConCountry($con_country)
    {
        $this->con_country = $con_country;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConHoursWorked()
    {
        return $this->con_hours_worked;
    }

    /**
     * @param mixed $con_hours_worked
     */
    public function setConHoursWorked($con_hours_worked)
    {
        $this->con_hours_worked = $con_hours_worked;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConHourlyRate()
    {
        return $this->con_hourly_rate;
    }

    /**
     * @param mixed $con_hourly_rate
     */
    public function setConHourlyRate($con_hourly_rate)
    {
        $this->con_hourly_rate = $con_hourly_rate;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConNcciCode()
    {
        return $this->con_ncci_code;
    }

    /**
     * @param mixed $con_ncci_code
     */
    public function setConNcciCode($con_ncci_code)
    {
        $this->con_ncci_code = $con_ncci_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConOccupation()
    {
        return $this->con_occupation;
    }

    /**
     * @param mixed $con_occupation
     */
    public function setConOccupation($con_occupation)
    {
        $this->con_occupation = $con_occupation;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConMaritalStatus()
    {
        return $this->con_marital_status;
    }

    /**
     * @param mixed $con_marital_status
     */
    public function setConMaritalStatus($con_marital_status)
    {
        $this->con_marital_status = $con_marital_status;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConRemoteId()
    {
        return $this->con_remote_id;
    }

    /**
     * @param mixed $con_remote_id
     *
     * @return ContactEntity
     */
    public function setConRemoteId($con_remote_id)
    {
        $this->con_remote_id = $con_remote_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConEmplGrade()
    {
        return $this->con_empl_grade;
    }

    /**
     * @param mixed $con_empl_grade
     */
    public function setConEmplGrade($con_empl_grade)
    {
        $this->con_empl_grade = $con_empl_grade;

        return $this;
    }

    /**
     * @return Collection<FeedbackLinksEntity>
     */
    public function getFeedbackLinks()
    {
        return $this->feedback_links;
    }

    /**
     * @param Collection<FeedbackLinksEntity> $feedback_links
     */
    public function setFeedbackLinks($feedback_links)
    {
        $this->feedback_links = $feedback_links;

        return $this;
    }

    /**
     * @return mixed
     *
     * @deprecated use getIdNumbers() instead
     */
    public function getConIdNumbers(): array
    {
        return [
            'idNumbers' => array_map(static function (IdNumberEntity $item): array {
                return ['type' => $item->getType(), 'number' => $item->getNumber()];
            }, $this->getIdNumbers()->getValues()),
        ];
    }

    /**
     * @throws Exception
     *
     * @deprecated use id number collection instead
     */
    public function setConIdNumbers(array $con_id_numbers): self
    {
        try {
            $this->con_id_numbers = json_encode($con_id_numbers, JSON_THROW_ON_ERROR);
        } catch (JsonException $e) {
            throw new Exception('Invalid JSON for ID numbers', $e->getCode(), $e);
        }

        return $this;
    }

    /**
     * @return mixed
     */
    public function getNationality()
    {
        return $this->nationality;
    }

    /**
     * @param mixed $nationality
     *
     * @return $this
     */
    public function setNationality($nationality)
    {
        $this->nationality = $nationality;

        return $this;
    }

    public function getMrnNumber(): ?string
    {
        return $this->con_number;
    }

    public function setMrnNumber(string $value): self
    {
        $this->con_number = $value;

        return $this;
    }

    public function addEmail(EmailEntity $emailEntity): void
    {
        $this->emails->add($emailEntity);
        $emailEntity->setContact($this);
    }

    public function removeEmail(EmailEntity $entity): void
    {
        $this->emails->removeElement($entity);
    }

    /**
     * Removes a child entity from the associated collection.
     *
     * @param mixed $entity The child entity to remove
     */
    public function remove($entity): void
    {
        // We are switching based on "true" instead of a string representation of the class so that we can use instanceof to consider inheritance and implementations.
        switch (true) {
            case $entity instanceof EmailEntity:
                $this->removeEmail($entity);

                break;
            case $entity instanceof PhoneNumberEntity:
                $this->removePhoneNumber($entity);

                break;
            case $entity instanceof AddressEntity:
                $this->removeAddress($entity);

                break;
            case $entity instanceof IdNumberEntity:
                $this->removeIdNumber($entity);

                break;
        }
    }

    public function addPhoneNumber(PhoneNumberEntity $phoneNumberEntity): void
    {
        $this->phone_numbers->add($phoneNumberEntity);
        $phoneNumberEntity->setContact($this);
    }

    public function removePhoneNumber(PhoneNumberEntity $phoneNumberEntity): void
    {
        $this->phone_numbers->removeElement($phoneNumberEntity);
    }

    public function addAddress(AddressEntity $addressEntity): void
    {
        $this->addresses->add($addressEntity);
        $addressEntity->setContact($this);
    }

    public function removeAddress(AddressEntity $addressEntity): void
    {
        $this->addresses->removeElement($addressEntity);
    }

    public function addIdNumber(IdNumberEntity $entity): void
    {
        $entity->setContact($this);
        $this->id_numbers->add($entity);
    }

    public function removeIdNumber(IdNumberEntity $entity): void
    {
        $this->id_numbers->removeElement($entity);
    }

    public function setIdNumbers(?Collection $entities = null): void
    {
        $this->id_numbers->clear();

        if (empty($entities)) {
            return;
        }

        foreach ($entities as $entity) {
            $entity->setContact($this);
        }
        $this->id_numbers = $entities;
    }

    /**
     * @return Collection<IdNumberEntity>
     */
    public function getIdNumbers(): Collection
    {
        return $this->id_numbers;
    }

    public function getJobTitle(): ?string
    {
        return $this->jobTitle;
    }

    public function setJobTitle(?string $jobTitle): self
    {
        $this->jobTitle = $jobTitle;

        return $this;
    }

    public function getSourceOfRecord()
    {
        return $this->source_of_record;
    }

    public function setSourceOfRecord($source_of_record): self
    {
        $this->source_of_record = $source_of_record;

        return $this;
    }

    public function getApiSource(): ?string
    {
        return $this->apiSource;
    }

    public function setApiSource(?string $apiSource): self
    {
        $this->apiSource = $apiSource;

        return $this;
    }

    public function getLastUpdatedDate(): ?DateTimeInterface
    {
        return $this->lastUpdatedDate;
    }

    public function setLastUpdatedDate(?DateTimeInterface $lastUpdatedDate): self
    {
        $this->lastUpdatedDate = $lastUpdatedDate;

        return $this;
    }
}
