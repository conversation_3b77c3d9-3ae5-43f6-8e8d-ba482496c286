<?php

namespace app\models\contact\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

#[Entity]
#[Table(name: "contact_emails")]
class EmailEntity
{
    #[Id]
    #[GeneratedValue(strategy: "NONE")]
    #[Column(type: "integer")]
    protected $id;

    #[Column(type: "string")]
    protected $email;

    #[Column(type: "string")]
    protected $type;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        inversedBy: "emails",
    )]
    #[JoinColumn(
        name: "contact_id",
        referencedColumnName: "recordid",
    )]
    protected $contact;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     *
     * @return EmailEntity
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @param string $email
     *
     * @return EmailEntity
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     *
     * @return EmailEntity
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return string
     */
    public function getContact()
    {
        return $this->contact;
    }

    /**
     * @return EmailEntity
     */
    public function setContact($contact)
    {
        $this->contact = $contact;

        return $this;
    }
}
