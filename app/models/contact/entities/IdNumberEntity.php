<?php

declare(strict_types=1);

namespace app\models\contact\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema
 */
#[Entity]
#[Table(name: "contact_id_numbers")]
class IdNumberEntity
{
    #[Id]
    #[GeneratedValue(strategy: "NONE")]
    #[Column(type: "integer")]
    private ?int $id = null;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        inversedBy: "id_numbers"
    )]
    #[JoinColumn(
        name: "contact_id",
        referencedColumnName: "recordid"
    )]
    private ContactEntity $contact;

    /**
     * @OA\Property(property="number", type="string", maxLength=255)
     */
    #[Column(type: "string", length: 255)]
    private string $number;

    /**
     * @OA\Property(property="type", type="string", maxLength=6)
     */
    #[Column(type: "string", length: 6, nullable: true)]
    private ?string $type = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getContact(): ContactEntity
    {
        return $this->contact;
    }

    public function setContact(ContactEntity $contact): self
    {
        $this->contact = $contact;

        return $this;
    }

    public function getNumber(): string
    {
        return $this->number;
    }

    public function setNumber(string $number): self
    {
        $this->number = $number;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
