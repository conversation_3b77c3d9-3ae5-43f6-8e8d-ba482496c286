<?php

namespace app\models\contact\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity]
#[Table(name: "contact_addresses")]
class AddressEntity
{
    /**
     * @OA\Property(property="id", type="integer", format="int32")
     */
    #[Id]
    #[GeneratedValue(strategy: "NONE")]
    #[Column(type: "integer")]
    protected $id;

    /**
     * @OA\Property(property="line1", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $line1 = null;

    /**
     * @OA\Property(property="line2", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $line2 = null;

    /**
     * @OA\Property(property="line3", type="string", maxLength=128, nullable=true),
     */
    #[Column(type: "string")]
    protected ?string $line3 = null;

    /**
     * @OA\Property(property="city", type="string", maxLength=254, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $city = null;

    /**
     * @OA\Property(property="county", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $county = null;

    /**
     * @OA\Property(property="country", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $country = null;

    /**
     * @OA\Property(property="$address", type="string", maxLength=254, nullable=true)
     */
    #[Column(type: "string")]
    protected ?string $address = null;

    /**
     * @OA\Property(property="postcode", type="string", maxLength=32)
     */
    #[Column(type: "string")]
    protected ?string $postcode = null;

    /**
     * @OA\Property(property="type", type="string", maxLength=64)
     */
    #[Column(type: "string")]
    protected ?string $type = null;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        inversedBy: "addresses"
    )]
    #[JoinColumn(
        name: "contact_id",
        referencedColumnName: "recordid"
    )]
    protected ?ContactEntity $contact = null;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     *
     * @return AddressEntity
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getLine1()
    {
        return $this->line1;
    }

    /**
     * @param string $line1
     *
     * @return AddressEntity
     */
    public function setLine1($line1)
    {
        $this->line1 = $line1;

        return $this;
    }

    /**
     * @return string
     */
    public function getLine2()
    {
        return $this->line2;
    }

    /**
     * @param string $line2
     *
     * @return AddressEntity
     */
    public function setLine2($line2)
    {
        $this->line2 = $line2;

        return $this;
    }

    /**
     * @return string
     */
    public function getLine3()
    {
        return $this->line3;
    }

    /**
     * @param string $line3
     *
     * @return AddressEntity
     */
    public function setLine3($line3)
    {
        $this->line3 = $line3;

        return $this;
    }

    /**
     * @return string
     */
    public function getCounty()
    {
        return $this->county;
    }

    /**
     * @param string $county
     *
     * @return AddressEntity
     */
    public function setCounty($county)
    {
        $this->county = $county;

        return $this;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param string $country
     *
     * @return AddressEntity
     */
    public function setCountry($country)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * @param string $address
     *
     * @return AddressEntity
     */
    public function setAddress($address)
    {
        $this->address = $address;

        return $this;
    }

    /**
     * @return string
     */
    public function getPostcode()
    {
        return $this->postcode;
    }

    /**
     * @param string $postcode
     *
     * @return AddressEntity
     */
    public function setPostcode($postcode)
    {
        $this->postcode = $postcode;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     *
     * @return AddressEntity
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    public function getContact(): ContactEntity
    {
        return $this->contact;
    }

    public function setContact(ContactEntity $contact): self
    {
        $this->contact = $contact;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param mixed $city
     */
    public function setCity($city)
    {
        $this->city = $city;

        return $this;
    }
}
