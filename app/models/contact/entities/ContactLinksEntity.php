<?php

namespace app\models\contact\entities;

use app\models\contact\repositories\ContactLinksRepository;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity(repositoryClass: ContactLinksRepository::class)]
#[Table(name: "link_contacts")]
class ContactLinksEntity
{
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    private $link_recordid;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        cascade: ["merge", "persist"],
        inversedBy: "links"
    )]
    #[JoinColumn(
        name: "con_id",
        referencedColumnName: "recordid"
    )]
    private $contact;

    #[Column(type: "integer")]
    private $inc_id;

    /**
     * @OA\Property(property="cla_id", type="integer", nullable=true)
     */
    #[Column(type: "integer")]
    private $cla_id;

    /**
     * @OA\Property(property="com_id", type="integer", nullable=true)
     */
    #[Column(type: "integer")]
    private $com_id;

    /**
     * @OA\Property(property="mor_id", type="integer", nullable=true)
     */
    #[Column(type: "integer")]
    private $mor_id;

    #[Column(type: "integer")]
    private $red_id;

    #[Column(type: "integer")]
    private $sfg_id;

    /**
     * @OA\Property(property="link_type", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private $link_type;

    /**
     * @OA\Property(property="link_role", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private $link_role;

    /**
     * @OA\Property(property="link_deceased", type="string", nullable=true)
     */
    #[Column(type: "string")]
    private $link_deceased;

    /**
     * @return mixed
     */
    public function getIncId()
    {
        return $this->inc_id;
    }

    /**
     * @param mixed $inc_id
     *
     * @return ContactLinksEntity
     */
    public function setIncId($inc_id)
    {
        $this->inc_id = $inc_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getClaId()
    {
        return $this->cla_id;
    }

    /**
     * @param mixed $cla_id
     *
     * @return ContactLinksEntity
     */
    public function setClaId($cla_id)
    {
        $this->cla_id = $cla_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComId()
    {
        return $this->com_id;
    }

    /**
     * @param mixed $com_id
     *
     * @return ContactLinksEntity
     */
    public function setComId($com_id)
    {
        $this->com_id = $com_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMorId()
    {
        return $this->mor_id;
    }

    /**
     * @param mixed $mor_id
     *
     * @return ContactLinksEntity
     */
    public function setMorId($mor_id)
    {
        $this->mor_id = $mor_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLinkRecordid()
    {
        return $this->link_recordid;
    }

    /**
     * @param mixed $link_recordid
     *
     * @return ContactLinksEntity
     */
    public function setLinkRecordid($link_recordid)
    {
        $this->link_recordid = $link_recordid;

        return $this;
    }

    /**
     * @return ContactEntity
     */
    public function getContact()
    {
        return $this->contact;
    }

    /**
     * @param ContactEntity $contact
     *
     * @return ContactLinksEntity
     */
    public function setContact($contact)
    {
        $this->contact = $contact;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLinkType()
    {
        return $this->link_type;
    }

    /**
     * @param mixed $link_type
     */
    public function setLinkType($link_type)
    {
        $this->link_type = $link_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLinkRole()
    {
        return $this->link_role;
    }

    /**
     * @param mixed $link_role
     */
    public function setLinkRole($link_role)
    {
        $this->link_role = $link_role;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLinkDeceased()
    {
        return $this->link_deceased;
    }

    /**
     * @param mixed $link_deceased
     */
    public function setLinkDeceased($link_deceased)
    {
        $this->link_deceased = $link_deceased;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getRedId()
    {
        return $this->red_id;
    }

    /**
     * @param mixed $red_id
     *
     * @return ContactLinksEntity
     */
    public function setRedId($red_id)
    {
        $this->red_id = $red_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getSfgId()
    {
        return $this->sfg_id;
    }

    /**
     * @param mixed $sfg_id
     *
     * @return ContactLinksEntity
     */
    public function setSfgId($sfg_id)
    {
        $this->sfg_id = $sfg_id;

        return $this;
    }
}
