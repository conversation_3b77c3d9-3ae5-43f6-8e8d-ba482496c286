<?php

namespace app\models\contact\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity]
#[Table(name: "contact_phone_numbers")]
class PhoneNumberEntity
{
    /**
     * @OA\Property(property="id", type="integer", format="int32")
     */
    #[Id]
    #[GeneratedValue(strategy: "NONE")]
    #[Column(type: "integer")]
    protected $id;

    /**
     * @OA\Property(property="number", type="string", maxLength=64, nullable=true)
     */
    #[Column(type: "string")]
    protected $phone_number;

    /**
     * @OA\Property(property="type", type="string", maxLength=64, nullable=true)
     */
    #[Column(type: "string")]
    protected $type;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        inversedBy: "phone_numbers",
    )]
    #[JoinColumn(
        name: "contact_id",
        referencedColumnName: "recordid",
    )]
    protected $contact;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     *
     * @return PhoneNumberEntity
     */
    public function setId($id)
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getPhoneNumber()
    {
        return $this->phone_number;
    }

    /**
     * @param string $phone_number
     *
     * @return PhoneNumberEntity
     */
    public function setPhoneNumber($phone_number)
    {
        $this->phone_number = $phone_number;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     *
     * @return PhoneNumberEntity
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return ContactEntity
     */
    public function getContact()
    {
        return $this->contact;
    }

    /**
     * @param ContactEntity $contact
     *
     * @return PhoneNumberEntity
     */
    public function setContact($contact)
    {
        $this->contact = $contact;

        return $this;
    }
}
