<?php

namespace app\models\datetime;

use app\models\framework\session\ClientSession;
use Psr\SimpleCache\CacheInterface;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class DateTimeGeneratorFactory
{
    public function create(): DateTimeGenerator
    {

        return new DateTimeGenerator(
            Container::get(ClientSession::class),
            Container::get('http_client.carlton'),
            Container::get(CacheInterface::class),
        );
    }
}
