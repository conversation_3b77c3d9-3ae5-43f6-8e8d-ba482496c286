<?php

declare(strict_types=1);

namespace app\models\datetime;

use DateTime;
use DateTimeZone;
use Exception;
use InvalidArgumentException;
use DateInterval;

class DateTimeCalculator
{
    /**
     * @throws InvalidArgumentException on invalid start/end values
     */
    public static function getDifferenceInDays(string $start, string $end): int
    {
        $startDT = self::normaliseDate($start);
        $endDT = self::normaliseDate($end);

        $diff = $startDT->diff($endDT);

        return ($diff->invert ? -1 : 1) * $diff->days;
    }

    /**
     * @throws InvalidArgumentException on invalid start/end values
     */
    public static function getDifferenceInYears(string $start, string $end): int
    {
        $startDT = self::normaliseDate($start);
        $endDT = self::normaliseDate($end);

        $diff = $startDT->diff($endDT);

        return ($diff->invert ? -1 : 1) * $diff->y;
    }

    public static function calculateWorkingDaysDifference(
        string $startDate,
        string $endDate,
        array $publicHolidays,
        array $workingDays = [1, 2, 3, 4, 5]
    ): int {
        // Ensure if someone tries to use the old JS week starting on Sunday we add 7 to ensure it still behaves
        // like ISO8601 weekdays(1-7 with 7 being sunday)
        if (in_array(0, $workingDays)) {
            $workingDays[] = 7;
        }

        // If we include the time on the public holidays let's strip that first
        if (isset($publicHolidays[0]) && strlen($publicHolidays[0]) > 10) {
            foreach ($publicHolidays as &$holiday) {
                $holiday = substr($holiday, 0, 10);
            }
        }

        $workingDaysCount = 0;
        $iter = self::normaliseDate($startDate);
        $end = self::normaliseDate($endDate);
        $interval = new DateInterval('P1D');
        while ($iter < $end) {
            $iter->add($interval);
            if (
                in_array((int) $iter->format('N'), $workingDays, true)
                && !in_array($iter->format('Y-m-d'), $publicHolidays, true)
            ) {
                ++$workingDaysCount;
            }
        }

        return $workingDaysCount;
    }

    /**
     * Normalises the date to remove anything we don't care about in datetime calculations like timezone and time
     * which might mess with the calculations.
     *
     * @throws InvalidArgumentException on invalid date values
     */
    private static function normaliseDate(string $date): DateTime
    {
        try {
            return (new DateTime($date, new DateTimeZone('+0000')))->setTime(0, 0, 0, 0);
        } catch (Exception $exception) {
            throw new InvalidArgumentException('Date is not a valid date/time string', $exception->getCode(), $exception);
        }
    }
}
