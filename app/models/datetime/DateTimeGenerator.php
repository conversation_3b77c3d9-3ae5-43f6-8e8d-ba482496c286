<?php

namespace app\models\datetime;

use app\models\framework\session\ClientSession;
use app\models\framework\session\ClientSessionFactory;
use DateInterval;
use DateTime;
use DateTimeZone;
use Exception;
use GuzzleHttp\Client;
use Psr\SimpleCache\CacheInterface;

class DateTimeGenerator
{
    /** @var int Client offset from UTC in hours. */
    protected $offset;

    /** @var string System offset from UTC in hours. */
    protected $systemOffset;
    protected $httpClient;

    /** @var CacheInterface */
    protected $cache;

    public function __construct(
        ClientSession $clientSession,
        Client $httpClient,
        CacheInterface $cache
    ) {
        $this->httpClient = $httpClient;
        $this->cache = $cache;
        $this->offset = (int) $clientSession->getTimezoneOffset();
        $this->systemOffset = $this->getSystemUtcOffset();
    }

    /**
     * Builds a base DateTime object to allow for dependency injection for testing.
     *
     * @throws Exception
     */
    public function getDateTime(?string $dateTime = 'now', ?DateTimeZone $timezone = null): DateTime
    {
        return new DateTime($dateTime, $timezone);
    }

    /**
     * this funcation.
     *
     * @return DateTime offsets the current server date and time to the date and time at the client
     *
     * @throws Exception
     *
     * @internal param \DateTime $date
     */
    public function getCurrentDateTimeAtClient(): DateTime
    {
        $dateTime = $this->getDateTime(null, new DateTimeZone('+0000'));

        return $dateTime->sub(DateInterval::createFromDateString($this->offset . ' hours'));
    }

    /**
     * @throws Exception
     *
     * @internal param \DateTime $date
     *
     * @brief Returns a DateTime object with the current local date and the time passed as a parameter in the form hh:mm.
     */
    public function getCurrentDateWithTime(string $time): DateTime
    {
        return $this->setTimeOnDate($this->getCurrentDateTimeAtClient(), $time);
    }

    public function setTimeOnDate(DateTime $date, ?string $time): DateTime
    {
        if ($time) {
            [$hour, $min] = explode(':', $time);

            return $date->setTime($hour, $min);
        }

        return $date;
    }

    /**
     * @desc Modifies the datetime for display based on the System UTC Offset value.
     */
    public function getCurrentDateTimeAtSystem(DateTime $datetime): DateTime
    {
        if ($this->systemOffset === null || $this->systemOffset === '+0') {
            return $datetime;
        }

        return $datetime->modify($this->systemOffset . ' hours');
    }

    /**
     * @desc Returns the UTC offset value from the system configuration.
     * System Admin > System Configuration > System UTC Offset.
     *
     * @throws Exception
     */
    private function getSystemUtcOffset(): ?string
    {
        $cacheKey = 'system-configuration';
        if (!$this->cache->has($cacheKey)) {
            $response = $this->httpClient->get('/system-configuration');

            if ($response->getStatusCode() !== 200) {
                throw new Exception('Unable to retrieve system configuration data');
            }

            $this->cache->set($cacheKey, json_decode($response->getBody(), true));
        }

        $config = $this->cache->get($cacheKey);

        return empty($config['data']['utcOffset']) ? null : $config['data']['utcOffset'];
    }
}
