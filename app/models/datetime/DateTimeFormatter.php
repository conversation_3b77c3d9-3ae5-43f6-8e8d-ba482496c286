<?php

namespace app\models\datetime;

use DateTime;
use src\framework\registry\Registry;

class DateTimeFormatter
{
    /** @var DateTime The datetime object that we would like to format. */
    protected $dateTime;

    /** @var Registry The registry to use for the current config. */
    protected $registry;

    public function __construct(Registry $registry)
    {
        $this->registry = $registry;
    }

    /**
     * @param DateTime $dateTime the Date to be formatted
     *
     * @return string The formatted date acording to the date settings in the system
     */
    public function formatDate(DateTime $dateTime)
    {
        $format = $this->registry->getParm('FMT_DATE_WEB', 'GB')->is('US') ? 'm/d/Y' : 'd/m/Y';

        return $dateTime->format($format);
    }
}
