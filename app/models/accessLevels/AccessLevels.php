<?php

namespace app\models\accessLevels;

use Exception;

final class AccessLevels
{
    public const CODE_MOR1_INPUT_ONLY = 'CODE_MOR1_INPUT_ONLY';
    public const CODE_MOR2_READ_ONLY = 'CODE_MOR2_READ_ONLY';
    public const CODE_MOR2_STAGE1_REVIEW_MOR1_FORMS = 'CODE_MOR2_STAGE1_REVIEW_MOR1_FORMS';
    public const CODE_MOR2_STAGE2_REVIEW_MOR1_FORMS = 'CODE_MOR2_STAGE2_REVIEW_MOR1_FORMS';
    public const CODE_FINAL_APPROVAL_MOR2 = 'CODE_FINAL_APPROVAL_MOR2';
    public const CODE_FULL_ACCESS = 'CODE_FULL_ACCESS';
    public const CODE_RED1_INPUT_ONLY = 'RED1';
    public const CODE_RED2_READ_ONLY = 'RED2_READ_ONLY';
    public const CODE_FINAL_APPROVAL_RED2 = 'RED2';
    public const CODE_SFG2_FULL_ACCESS = 'SFG2';
    public const CODE_SFG2_FULL_ACCESS_NO_CLOSE = 'SFG2_NO_CLOSE';
    public const CODE_SFG2_READ_ONLY = 'SFG2_READ_ONLY';
    public const CODE_CON_INPUT_ONLY = 'CON_INPUT_ONLY';
    public const NONE = 'NONE';
    public const DESCRIPTION_MOR1_INPUT_ONLY = 'code_mor1_input_only';
    public const DESCRIPTION_MOR2_READ_ONLY = 'code_mor2_read_only';
    public const DESCRIPTION_MOR2_STAGE1_REVIEW_MOR1_FORMS = 'code_mor2_stage1_review_mor1_forms';
    public const DESCRIPTION_MOR2_STAGE2_REVIEW_MOR1_FORMS = 'code_mor2_stage2_review_mor1_forms';
    public const DESCRIPTION_FINAL_APPROVAL_MOR2 = 'code_final_approval_mor2';
    public const DESCRIPTION_FULL_ACCESS = 'code_full_access';
    public const DESCRIPTION_RED1_INPUT_ONLY = 'code_red1_input_only';
    public const DESCRIPTION_RED2_READ_ONLY = 'code_red2_read_only';
    public const DESCRIPTION_FINAL_APPROVAL_RED2 = 'code_final_approval_red2';
    public const DESCRIPTION_CODE_SFG2_FULL_ACCESS = 'code_sfg2_full_access';
    public const DESCRIPTION_CODE_SFG2_FULL_ACCESS_NO_CLOSE = 'code_sfg2_full_access_no_close';
    public const DESCRIPTION_CODE_SFG2_READ_ONLY = 'code_sfg2_read_only';
    public const EDIT_ACCESS = 'E';
    public const READ_ACCESS = 'R';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        // throw an exception if someone can get in here
        throw new Exception("Can't get an instance of AccessLevels");
    }
}
