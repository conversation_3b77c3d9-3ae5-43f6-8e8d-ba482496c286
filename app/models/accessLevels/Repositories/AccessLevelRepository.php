<?php

declare(strict_types=1);

namespace app\models\accessLevels\Repositories;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as BDALException;

/**
 * @codeCoverageIgnore
 */
class AccessLevelRepository
{
    private Connection $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    /**
     * @throws BDALException
     */
    public function getListOfUserGroups(int $start, int $end): array
    {
        $sql = <<<SQL
            SELECT
                c.recordid AS contactId,
                l.grp_id as groupId
            FROM users_main c
            JOIN sec_staff_group l
                ON
                l.use_id = c.recordid
            JOIN sec_groups g
                ON
                g.recordid = l.grp_id
            WHERE
                c.recordid BETWEEN {$start} AND {$end}
            UNION
            SELECT
                u.RECORDID,
                lp.lpg_group
            FROM link_profile_group lp
            INNER JOIN sec_groups g
                ON
                g.recordid = lp.lpg_group
            INNER JOIN users_main u WITH (INDEX(IX_users_main_use_email))
                ON
                u.sta_profile = lp.lpg_profile
            WHERE
                u.recordid BETWEEN {$start} AND {$end}
            SQL;

        return $this->db->fetchAllAssociative($sql);
    }
}
