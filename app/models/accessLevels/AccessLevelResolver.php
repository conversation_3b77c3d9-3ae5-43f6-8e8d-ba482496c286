<?php

namespace app\models\accessLevels;

use app\models\accessLevels\valueObjects\UserPermission;

class AccessLevelResolver
{
    protected UserGroups $userGroups;
    protected array $userAccessLvls;
    protected array $accessLevelDefs;

    /** @var array<UserPermission[]> */
    protected array $userPermissionsByGroup;
    protected UserParmsAccessLevels $userPermissions;

    /**
     * @param array<UserPermission[]> $userPermissionsByGroup
     */
    public function __construct(
        array $accessLevelDefs,
        UserGroups $userGroups,
        UserParmsAccessLevels $userPermissions,
        array $userPermissionsByGroup
    ) {
        $this->accessLevelDefs = $accessLevelDefs;
        $this->userAccessLvls = array_fill_keys($this->getAccessLevelKeys(), false);
        $this->userGroups = $userGroups;
        $this->userPermissions = $userPermissions;
        $this->userPermissionsByGroup = $userPermissionsByGroup;
    }

    /**
     * @param $con_id
     *
     * @return array
     */
    public function getUserAccessLevels($con_id)
    {
        /** @var UserPermission[] $permissions */
        if ($this->userGroups->userHasGroups($con_id)) {
            $permissions = $this->getUserPermissionsFromGroups($con_id);
        } else {
            $permissions = $this->userPermissions->getUserPermissionsFromUserParms($con_id);
        }

        $userAccessLevels = array_fill_keys($this->getAccessLevelKeys(), false);

        foreach ($permissions as $permission) {
            $name = $permission->getPermissionName();
            $value = $permission->getValue();

            $possibleValuesForPermission = $this->accessLevelDefs[$name] ?? [];

            if (
                empty($userAccessLevels[$name])
                || empty($possibleValuesForPermission[$userAccessLevels[$name]])
                || $possibleValuesForPermission[$value]['order'] > $possibleValuesForPermission[$userAccessLevels[$name]]['order']
            ) {
                $userAccessLevels[$name] = $value;
            }
        }

        return $userAccessLevels;
    }

    /**
     * @return array
     */
    protected function getAccessLevelKeys()
    {
        return array_keys($this->accessLevelDefs);
    }

    /**
     * @param $con_id
     *
     * @return UserPermission[]
     */
    protected function getUserPermissionsFromGroups($con_id)
    {
        $permissions = [];

        $userGroups = $this->userGroups->getUserGroups($con_id);
        foreach ($userGroups as $groupId) {
            $groupUserPermissions = $this->userPermissionsByGroup[$groupId] ?? [];
            if (!empty($groupUserPermissions)) {
                $permissions = array_merge($permissions, $groupUserPermissions);
            }
        }

        return $permissions;
    }
}
