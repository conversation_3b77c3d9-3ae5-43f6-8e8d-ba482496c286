<?php

declare(strict_types=1);

namespace app\models\accessLevels\factories;

use app\models\accessLevels\AccessLevelResolver;
use app\models\accessLevels\UserGroups;
use app\models\accessLevels\UserParmsAccessLevels;
use app\models\accessLevels\valueObjects\UserPermission;
use DatixDBQuery;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class AccessLevelResolverFactory
{
    public function create(): AccessLevelResolver
    {
        return new AccessLevelResolver(
            Container::get(Registry::class)->getAccessLvlDefs(),
            Container::get(UserGroups::class),
            Container::get(UserParmsAccessLevels::class),
            $this->getGroupUserParmsByGroup(),
        );
    }

    /**
     * @return array<UserPermission[]>
     */
    private function getGroupUserParmsByGroup(): array
    {
        $sql = 'SELECT grp_id, item_code AS parameter, perm_value FROM sec_group_permissions';

        $results = DatixDBQuery::PDO_fetch_all($sql);

        $groups = [];
        foreach ($results as $row) {
            $groups[$row['grp_id']][] = new UserPermission($row['parameter'], $row['perm_value']);
        }

        return $groups;
    }
}
