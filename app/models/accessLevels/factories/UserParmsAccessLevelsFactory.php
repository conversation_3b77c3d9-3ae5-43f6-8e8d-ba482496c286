<?php

namespace app\models\accessLevels\factories;

use app\models\accessLevels\UserParmsAccessLevels;
use app\models\accessLevels\valueObjects\UserPermission;
use DatixDBQuery;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @codeCoverageIgnore
 */
class UserParmsAccessLevelsFactory
{
    public function create(): UserParmsAccessLevels
    {
        return new UserParmsAccessLevels($this->getUserPermissionParms());
    }

    /**
     * @return array<UserPermission[]>
     */
    protected function getUserPermissionParms(): array
    {
        $accessLevelKeys = array_keys(Container::get(Registry::class)->getAccessLvlDefs());

        $validAccessLevelKeys = implode("', '", $accessLevelKeys);

        $sql = <<<SQL
            SELECT c.recordid as contactId, p.parameter AS parameter, p.parmvalue
            FROM user_parms p
            JOIN users_main c ON p.login = c.login
            WHERE p.parameter IN ('{$validAccessLevelKeys}');
            SQL;

        $permissions = DatixDBQuery::PDO_fetch_all($sql);

        $parms = [];
        foreach ($permissions as $permission) {
            $parms[$permission['contactId']][] = new UserPermission($permission['parameter'], $permission['parmvalue']);
        }

        return $parms;
    }
}
