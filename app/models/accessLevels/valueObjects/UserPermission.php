<?php

namespace app\models\accessLevels\valueObjects;

/**
 * @codeCoverageIgnore
 */
class UserPermission
{
    protected $permissionName;
    protected $value;

    public function __construct($permissionName, $value)
    {
        $this->permissionName = $permissionName;
        $this->value = $value;
    }

    public function getPermissionName()
    {
        return $this->permissionName;
    }

    public function getValue()
    {
        return $this->value;
    }
}
