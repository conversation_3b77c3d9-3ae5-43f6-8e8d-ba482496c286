<?php

namespace app\models\accessLevels;

use app\models\accessLevels\valueObjects\UserPermission;

class UserParmsAccessLevels
{
    /** @var array<UserPermission[]> */
    protected array $userParms;

    /**
     * @param array<UserPermission[]> $userPermissionParams
     */
    public function __construct(array $userPermissionParams)
    {
        $this->userParms = $userPermissionParams;
    }

    /**
     * @param $con_id
     *
     * @return UserPermission[]
     */
    public function getUserPermissionsFromUserParms($con_id)
    {
        return $this->userParms[$con_id] ?? [];
    }
}
