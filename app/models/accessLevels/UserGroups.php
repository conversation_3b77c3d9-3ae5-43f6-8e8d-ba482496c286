<?php

declare(strict_types=1);

namespace app\models\accessLevels;

use app\models\accessLevels\Repositories\AccessLevelRepository;
use Psr\SimpleCache\CacheInterface;

class UserGroups
{
    private const MAX_CACHE_SIZE = 100;
    private const CACHE_KEY = 'user_groups_list_';
    private AccessLevelRepository $repository;
    private CacheInterface $cache;
    /**
     * @var array<number, array<number, int[]>>
     *     Cache of the user group references, keyed first by batch, then user ID
     *     self::$userGroupsReferences[$cacheGroup][$userId] is an array of ints
     */
    private static array $userGroupsReferences = [];

    public function __construct(AccessLevelRepository $repository, CacheInterface $cache)
    {
        $this->repository = $repository;
        $this->cache = $cache;
    }

    /**
     * @return int[] list of group ids
     */
    public function getUserGroups(int $userId): array
    {
        $cacheGroup = (int) ($userId / self::MAX_CACHE_SIZE);
        $this->populateUserGroupCache($cacheGroup);

        return self::$userGroupsReferences[$cacheGroup][$userId] ?? [];
    }

    public function userHasGroups(int $userId): bool
    {
        return !empty($this->getUserGroups($userId));
    }

    private function populateUserGroupCache(int $cacheGroup): void
    {
        if (isset(self::$userGroupsReferences[$cacheGroup])) {
            return;
        }

        $perUserGroupList = $this->cache->get(self::CACHE_KEY . $cacheGroup);

        if ($perUserGroupList === null) {
            $perUserGroupList = [];
            $rawList = $this->repository->getListOfUserGroups(
                $cacheGroup * self::MAX_CACHE_SIZE,
                ($cacheGroup + 1) * self::MAX_CACHE_SIZE - 1
            );
            foreach ($rawList as $link) {
                $perUserGroupList[(int) $link['contactId']][] = (int) $link['groupId'];
            }
            $this->cache->set(self::CACHE_KEY . $cacheGroup, $perUserGroupList, 120);
        }

        self::$userGroupsReferences[$cacheGroup] = $perUserGroupList;
    }
}
