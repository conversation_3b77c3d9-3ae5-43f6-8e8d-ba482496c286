<?php

namespace app\models\document\repositories;

use app\models\document\entities\LinkedDocumentEntity;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<LinkedDocumentEntity>
 */
class LinkedDocumentEntityRepository extends EntityRepository
{
    public function findByModuleAndId(string $module, int $id): array
    {
        return $this->findBy([
            'main_record_module' => $module,
            'main_record_id' => $id,
        ]);
    }

    public function findByDocumentId(int $document_id): array
    {
        return $this->findBy([
            'document_id' => $document_id,
        ]);
    }

    public function retrieveReferralHistoryForSafeguardingRecord(int $docTemplateId, int $recordId, string $module): ?LinkedDocumentEntity
    {
        return $this->findOneBy([
            'document_template_id' => $docTemplateId,
            'main_record_id' => $recordId,
            'main_record_module' => $module,
        ], ['link_date' => 'DESC']);
    }

    public function findByModuleAndIdOrderByParam(string $module, int $id, array $orderBy): array
    {
        return $this->findBy([
            'main_record_module' => $module,
            'main_record_id' => $id,
        ], $orderBy);
    }
}
