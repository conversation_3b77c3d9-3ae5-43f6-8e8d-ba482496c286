<?php

declare(strict_types=1);

namespace app\models\document\repositories;

use app\models\document\entities\DocumentPendingEntity;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<DocumentPendingEntity>
 */
class DocumentPendingEntityRepository extends EntityRepository
{
    public function findByFilename(string $filename): ?DocumentPendingEntity
    {
        return $this->findOneBy([
            'filename' => $filename,
        ]);
    }

    public function setUploadStatusByUploadId(string $uploadId, string $status): void
    {
        $data = [
            'upload_status' => $status,
        ];
        $this->getEntityManager()->getConnection()->update(
            'documents_pending',
            $data,
            ['upload_id' => $uploadId],
        );
    }

    public function setMimeTypeByFilename(string $filename, string $mimeType): void
    {
        $data = [
            'mime_type' => $mimeType,
        ];
        $this->getEntityManager()->getConnection()->update(
            'documents_pending',
            $data,
            ['filename' => $filename],
        );
    }

    public function deleteByFilename(string $filename): void
    {
        $this->getEntityManager()->getConnection()->delete(
            'documents_pending',
            ['filename' => $filename],
        );
    }
}
