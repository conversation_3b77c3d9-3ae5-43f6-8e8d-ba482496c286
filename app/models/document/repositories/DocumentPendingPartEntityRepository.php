<?php

declare(strict_types=1);

namespace app\models\document\repositories;

use app\models\document\entities\DocumentPendingPartEntity;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<DocumentPendingPartEntity>
 */
class DocumentPendingPartEntityRepository extends EntityRepository
{
    public function findByUploadId(string $uploadId): ?array
    {
        $parts = $this->findBy(
            [
                'uploadId' => $uploadId,
            ],
            [
                'partNumber' => 'ASC',
            ],
        );
        $pendingParts = [];
        foreach ($parts as $part) {
            $pendingParts[$part->getPartNumber()] = [
                'PartNumber' => $part->getPartNumber(),
                'ETag' => $part->getEtag(),
            ];
        }

        return $pendingParts;
    }

    public function deleteByUploadId(string $uploadId): void
    {
        $dbh = $this->getEntityManager()->getConnection()->prepare('
            delete from
                documents_pending_parts
            where
                upload_id = :uploadId
        ');
        $dbh->executeQuery(['uploadId' => $uploadId]);
    }
}
