<?php

namespace app\models\document\repositories;

use app\models\document\entities\DocumentEntity;
use Doctrine\ORM\EntityRepository;
use src\wordmergetemplate\model\WordMergeTemplate;

/**
 * @extends EntityRepository<DocumentEntity>
 */
class DocumentEntityRepository extends EntityRepository
{
    /**
     * @return DocumentEntity
     */
    public function findByWordMergeTemplate(WordMergeTemplate $template): ?DocumentEntity
    {
        return $this->find($template->document_id);
    }
}
