<?php

namespace app\models\document\entities;

use app\models\document\repositories\DocumentEntityRepository;
use DateTime;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;
use SplFileInfo;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity(repositoryClass: DocumentEntityRepository::class)]
#[Table(name: "documents")]
class DocumentEntity
{
    /**
     * @OA\Property(property="id", type="integer")
     */
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    protected ?int $id = null;

    /**
     * @OA\Property(property="filename", type="string")
     */
    #[Column(type: "string")]
    protected ?string $filename = null;

    /**
     * @OA\Property(property="mime_type", type="string")
     */
    #[Column(type: "string")]
    protected ?string $mime_type = null;

    /**
     * @OA\Property(property="upload_date", type="string", nullable=true)
     */
    #[Column(type: "datetime")]
    protected ?DateTime $upload_date = null;

    /**
     * @OA\Property(property="user_id", type="integer", nullable=true)
     */
    #[Column(type: "integer")]
    protected ?int $user_id = null;

    /**
     * @OA\Property(property="original_filename", type="string")
     */
    #[Column(type: "string")]
    protected ?string $original_filename = null;

    /**
     * @OA\Property(property="is_missing", type="boolean", nullable=true)
     */
    #[Column(name: "is_missing", type: "boolean")]
    protected ?bool $isMissing = null;

    /**
     * @OA\Property(property="file_size", type="integer", format="int64", nullable=true)
     */
    #[Column(name: "file_size", type: "bigint")]
    protected ?string $filesize = null;

    /**
     * @OA\Property(property="reupload_date", type="string", nullable=true)
     */
    #[Column(name: "reupload_date", type: "datetime")]
    protected ?DateTime $reuploadDate = null;

    /**
     * @OA\Property(property="reuplaoded_user_id", type="integer")
     */
    #[Column(name: "reuploaded_user_id", type: "integer")]
    protected ?int $reuploadedUserId = null;

    public function __clone()
    {
        $this->id = null;
        $this->filename = null;
        $this->upload_date = null;
        $this->user_id = null;
        $this->isMissing = null;
        $this->reuploadDate = null;
        $this->reuploadedUserId = null;
        $this->filesize = null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return DocumentEntity
     */
    public function setId(int $id)
    {
        $this->id = $id;

        return $this;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    /**
     * @return DocumentEntity
     */
    public function setFilename(string $filename)
    {
        $this->filename = $filename;

        return $this;
    }

    public function getMimeType(): string
    {
        return $this->mime_type;
    }

    /**
     * @return DocumentEntity
     */
    public function setMimeType(string $mime_type)
    {
        $this->mime_type = $mime_type;

        return $this;
    }

    public function getUploadDate(): DateTime
    {
        return $this->upload_date;
    }

    /**
     * @return DocumentEntity
     */
    public function setUploadDate(DateTime $upload_date)
    {
        $this->upload_date = $upload_date;

        return $this;
    }

    /**
     * @return int
     */
    public function getUserId(): ?int
    {
        return $this->user_id;
    }

    /**
     * @param int $user_id
     *
     * @return DocumentEntity
     */
    public function setUserId(?int $user_id)
    {
        $this->user_id = $user_id;

        return $this;
    }

    public function getOriginalFilename(): string
    {
        return $this->original_filename;
    }

    /**
     * @return DocumentEntity
     */
    public function setOriginalFilename(string $original_filename)
    {
        $this->original_filename = $original_filename;

        return $this;
    }

    public function getExtension(): string
    {
        return (new SplFileInfo($this->original_filename))->getExtension();
    }

    public function getIsMissing(): ?bool
    {
        return $this->isMissing;
    }

    public function setIsMissing(?bool $isMissing): self
    {
        $this->isMissing = $isMissing;

        return $this;
    }

    public function getFilesize(): ?string
    {
        return $this->filesize;
    }

    public function setFilesize(?string $filesize): self
    {
        $this->filesize = $filesize;

        return $this;
    }

    public function getReuploadDate(): ?DateTime
    {
        return $this->reuploadDate;
    }

    public function setReuploadDate(DateTime $reupload_date): self
    {
        $this->reuploadDate = $reupload_date;

        return $this;
    }

    public function getReuploadedUserId(): ?int
    {
        return $this->reuploadedUserId;
    }

    public function setReuploadedUserId(?int $user_id): self
    {
        $this->reuploadedUserId = $user_id;

        return $this;
    }
}
