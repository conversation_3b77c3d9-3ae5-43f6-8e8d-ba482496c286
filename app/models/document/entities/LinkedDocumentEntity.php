<?php

namespace app\models\document\entities;

use app\models\document\repositories\LinkedDocumentEntityRepository;
use DateTime;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity(repositoryClass: LinkedDocumentEntityRepository::class)]
#[Table(name: "link_documents")]
class LinkedDocumentEntity
{
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    protected $id;

    #[Column(type: "string")]
    protected $main_record_module;

    #[Column(type: "integer")]
    protected $main_record_id;

    #[Column(type: "string")]
    protected $description;

    #[Column(type: "string")]
    protected $type;

    #[Column(type: "integer")]
    protected $document_id;

    /**
     * @var DateTime
     */
    #[Column(type: "datetime")]
    protected $link_date;

    #[ManyToOne(
        targetEntity: DocumentEntity::class,
        cascade: ["persist"],
    )]
    #[JoinColumn(
        name: "document_id",
        referencedColumnName: "id",
    )]
    protected ?DocumentEntity $document = null;

    #[Column(type: "integer")]
    protected $user_id;

    #[Column(type: "integer")]
    protected $document_template_id;

    public function __clone()
    {
        $this->id = null;
        $this->link_date = null;
        $this->user_id = null;
        $this->document = $this->document ? (clone $this->document) : null;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getMainRecordModule(): string
    {
        return $this->main_record_module;
    }

    public function setMainRecordModule(string $main_record_module): self
    {
        $this->main_record_module = $main_record_module;

        return $this;
    }

    public function getMainRecordId(): int
    {
        return $this->main_record_id;
    }

    public function setMainRecordId(int $main_record_id): self
    {
        $this->main_record_id = $main_record_id;

        return $this;
    }

    /**
     * @return string
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getDocumentId(): int
    {
        return $this->document_id;
    }

    public function setDocumentId(int $document_id): self
    {
        $this->document_id = $document_id;

        return $this;
    }

    public function getLinkDate(): DateTime
    {
        return $this->link_date;
    }

    public function setLinkDate(DateTime $link_date): self
    {
        $this->link_date = $link_date;

        return $this;
    }

    public function getDocument(): ?DocumentEntity
    {
        return $this->document;
    }

    public function setDocument(DocumentEntity $document): self
    {
        $this->document = $document;

        return $this;
    }

    /**
     * @return int
     */
    public function getUserId(): ?int
    {
        return $this->user_id;
    }

    public function setUserId(?int $user_id): self
    {
        $this->user_id = $user_id;

        return $this;
    }

    public function getDocumentTemplateId(): ?int
    {
        return $this->document_template_id;
    }

    public function setDocumentTemplateId(?int $document_template_id): self
    {
        $this->document_template_id = $document_template_id;

        return $this;
    }

    public function toArray(): array
    {
        $document_id = isset($this->document) ? $this->document->getId() : null;

        return [
            'id' => $this->id,
            'main_record_module' => $this->main_record_module,
            'main_record_id' => $this->main_record_id,
            'description' => $this->description,
            'type' => $this->type,
            'link_date' => $this->link_date->format('Y-m-d H:i:s'),
            'document_id' => $document_id,
            'user_id' => $this->user_id,
        ];
    }
}
