<?php

declare(strict_types=1);

namespace app\models\document\entities;

use app\models\document\repositories\DocumentPendingEntityRepository;
use DateTime;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity(repositoryClass: DocumentPendingEntityRepository::class)]
#[Table(name: "documents_pending")]
class DocumentPendingEntity
{
    public const UPLOAD_STATUS_COMPLETE = 'complete';
    public const UPLOAD_STATUS_PENDING = 'pending';

    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    protected int $id;

    #[Column(name: "upload_id", type: "string")]
    protected string $uploadId;

    #[Column(type: "string")]
    protected string $filename;

    #[Column(name: "original_filename", type: "string")]
    protected string $originalFilename;

    #[Column(name: "mime_type", type: "string")]
    protected ?string $mimeType;

    #[Column(name: "main_record_module", type: "string")]
    protected string $mainRecordModule;

    #[Column(name: "main_record_id", type: "integer")]
    protected ?int $mainRecordId;

    #[Column(name: "upload_status", type: "string")]
    protected string $uploadStatus;

    #[Column(name: "upload_started_date", type: "datetime")]
    protected ?DateTime $uploadStartedDate;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getUploadId(): string
    {
        return $this->uploadId;
    }

    public function setUploadId(string $uploadId): self
    {
        $this->uploadId = $uploadId;

        return $this;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getOriginalFilename(): string
    {
        return $this->originalFilename;
    }

    public function setOriginalFilename(string $originalFilename): self
    {
        $this->originalFilename = $originalFilename;

        return $this;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(string $mimeType): self
    {
        $this->mimeType = $mimeType;

        return $this;
    }

    public function getMainRecordModule(): string
    {
        return $this->mainRecordModule;
    }

    public function setMainRecordModule(string $mainRecordModule): self
    {
        $this->mainRecordModule = $mainRecordModule;

        return $this;
    }

    public function getMainRecordId(): ?int
    {
        return $this->mainRecordId;
    }

    public function setMainRecordId(int $mainRecordId): self
    {
        $this->mainRecordId = $mainRecordId;

        return $this;
    }

    public function getUploadStatus(): string
    {
        return $this->uploadStatus;
    }

    public function setUploadStatus(string $uploadStatus): self
    {
        $this->uploadStatus = $uploadStatus;

        return $this;
    }

    public function getUploadStartedDate(): ?DateTime
    {
        return $this->uploadStartedDate;
    }

    public function setUploadStartedDate(DateTime $uploadStartedDate): self
    {
        $this->uploadStartedDate = $uploadStartedDate;

        return $this;
    }
}
