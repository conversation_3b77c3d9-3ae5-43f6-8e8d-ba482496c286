<?php

declare(strict_types=1);

namespace app\models\document\entities;

use app\models\document\repositories\DocumentPendingPartEntityRepository;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity(repositoryClass: DocumentPendingPartEntityRepository::class)]
#[Table(name: "documents_pending_parts")]
class DocumentPendingPartEntity
{
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    protected int $id;

    #[Column(name: "upload_id", type: "string")]
    protected string $uploadId;

    #[Column(type: "string")]
    protected string $etag;

    #[Column(name: "part_number", type: "integer")]
    protected int $partNumber;

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getUploadId(): string
    {
        return $this->uploadId;
    }

    public function setUploadId(string $uploadId): self
    {
        $this->uploadId = $uploadId;

        return $this;
    }

    public function getEtag(): string
    {
        return $this->etag;
    }

    public function setEtag(string $etag): self
    {
        $this->etag = $etag;

        return $this;
    }

    public function getPartNumber(): int
    {
        return $this->partNumber;
    }

    public function setPartNumber(int $partNumber): self
    {
        $this->partNumber = $partNumber;

        return $this;
    }
}
