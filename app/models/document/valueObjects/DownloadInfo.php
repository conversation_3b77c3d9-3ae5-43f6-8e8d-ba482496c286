<?php

namespace app\models\document\valueObjects;

class DownloadInfo
{
    /** @var string */
    private $url;

    /** @var string */
    private $originalFilename;

    /** @var string */
    private $mimeType;
    private int $fileSize;

    public function __construct(string $url, int $fileSize, string $originalFilename, string $mimeType)
    {
        $this->url = $url;
        $this->fileSize = $fileSize;
        $this->originalFilename = $originalFilename;
        $this->mimeType = $mimeType;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getOriginalFilename(): string
    {
        return $this->originalFilename;
    }

    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    public function getFileSize(): int
    {
        return $this->fileSize;
    }
}
