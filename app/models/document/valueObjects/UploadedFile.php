<?php

declare(strict_types=1);

namespace app\models\document\valueObjects;

use InvalidArgumentException;
use Symfony\Component\Mime\MimeTypes;
use SplFileInfo;
use Exception;

use function in_array;

use const PATHINFO_EXTENSION;

class UploadedFile
{
    /** @var array */
    public const BANNED_MEDIA_TYPES = [
        'x-msdownload',
        'x-ms-installer',
        'x-elf',
        'x-sh',
        'x-perl',
        'x-python',
        'php',
        'x-php',
        'x-httpd-php',
        'x-httpd-php-source',
        'svg+xml',
    ];

    /** @readonly */
    private string $tmpFile;

    /** @readonly */
    private string $originalFilename;

    /** @readonly */
    private string $mimeType;

    private function __construct(string $tmpFile, string $originalFilename, string $mimeType)
    {
        $this->tmpFile = $tmpFile;
        $this->originalFilename = $originalFilename;
        $this->mimeType = $mimeType;
    }

    public function getTmpFile(): string
    {
        return $this->tmpFile;
    }

    public function getExtension(): string
    {
        return (new SplFileInfo($this->originalFilename))->getExtension();
    }

    public function getOriginalFilename(): string
    {
        return $this->originalFilename;
    }

    public function getMimeType(): string
    {
        return $this->mimeType;
    }

    public function toArray(): array
    {
        return [
            'mimeType' => $this->mimeType,
            'originalFileName' => $this->originalFilename,
        ];
    }

    public static function createFromS3FileUpload(string $filename, string $originalFilename, string $mimeType): self
    {
        if (empty(pathinfo($originalFilename, PATHINFO_EXTENSION))) {
            throw new Exception(_fdtk('missing_file_extension'));
        }

        $mediaType = explode('/', $mimeType)[1];

        if (in_array($mediaType, self::BANNED_MEDIA_TYPES, true)) {
            throw new Exception(_fdtk('banned_media_type'));
        }

        return new self($filename, $originalFilename, $mimeType);
    }

    /**
     * @param array $_file (item from $_FILES)
     */
    public static function createFromFileUpload(array $_file): self
    {
        return self::createFromTmpFile($_file['tmp_name'], $_file['name']);
    }

    public static function createFromBase64String(string $base64Body, string $mimeType): self
    {
        // create a temporary file with the content from the base64 string
        $tmpFilename = tempnam(sys_get_temp_dir(), 'api_file_upload');
        file_put_contents($tmpFilename, base64_decode($base64Body));

        $mime = MimeTypes::getDefault();

        $extension = $mime->getExtensions($mimeType)[0];

        if ($extension === null) {
            throw new InvalidArgumentException("Unable to determine file extension from mime type `{$mimeType}`.");
        }

        $pseudoRandom = (new RandomFileName($extension))->__toString();

        return new self($tmpFilename, $pseudoRandom, $mimeType);
    }

    /**
     * @throws Exception
     */
    public static function createFromTmpFile(string $tmpFile, string $originalFile): self
    {
        if (!file_exists($tmpFile)) {
            $errorMessage = sprintf(_fdtk('temporary_file_does_not_exist'), $tmpFile);

            throw new Exception($errorMessage);
        }

        if (empty(pathinfo($originalFile, PATHINFO_EXTENSION))) {
            throw new Exception(_fdtk('missing_file_extension'));
        }

        $mime = MimeTypes::getDefault();
        $mimeType = $mime->guessMimeType($tmpFile);

        $mediaType = explode('/', $mimeType)[1];

        if (in_array($mediaType, self::BANNED_MEDIA_TYPES, true)) {
            throw new Exception(_fdtk('banned_media_type'));
        }

        return new self($tmpFile, $originalFile, $mimeType);
    }
}
