<?php

namespace app\models\document\valueObjects;

use Ramsey\Uuid\Exception\RandomSourceException;
use <PERSON>\Uuid\Uuid;

class RandomFileName
{
    private string $filename;

    /**
     * @throws RandomSourceException if random_bytes() - called by RandomBytesGenerator - throws an exception/error
     */
    public function __construct(string $extension)
    {
        $this->filename = Uuid::uuid4()->toString() . '.' . $extension;
    }

    public function __toString()
    {
        return $this->filename;
    }
}
