<?php

namespace app\models\document\valueObjects;

class LinkedRecord
{
    /** @var string */
    private $module;

    /** @var int */
    private $id;

    public function __construct(string $module, int $id)
    {
        $this->module = $module;
        $this->id = $id;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function getId(): int
    {
        return $this->id;
    }
}
