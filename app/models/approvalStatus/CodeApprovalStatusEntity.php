<?php

declare(strict_types=1);

namespace app\models\approvalStatus;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "code_approval_status")]
class CodeApprovalStatusEntity
{
    #[Id]
    #[Column(type: "string")]
    private string $code;

    #[Column(type: "string")]
    private string $description;

    #[Column(type: "string")]
    private string $module;

    #[Column(name: "cod_priv_level", type: "string")]
    private string $privLevel;

    #[Column(name: "cod_listorder", type: "integer")]
    private int $listOrder;

    #[Column(name: "can_be_overdue", type: "boolean")]
    private bool $overdue;

    #[Column(name: "overdue_date_field", type: "string")]
    private string $overdueDateField;

    #[Column(type: "integer")]
    private int $workflow;

    #[Column(name: "cod_web_colour", type: "string")]
    private string $colour;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function setModule(string $module): self
    {
        $this->module = $module;

        return $this;
    }

    public function getPrivLevel(): string
    {
        return $this->privLevel;
    }

    public function setPrivLevel(string $privLevel): self
    {
        $this->privLevel = $privLevel;

        return $this;
    }

    public function getListOrder(): int
    {
        return $this->listOrder;
    }

    public function setListOrder(int $listOrder): self
    {
        $this->listOrder = $listOrder;

        return $this;
    }

    public function getOverdue(): bool
    {
        return $this->overdue;
    }

    public function setOverdue(bool $overdue): self
    {
        $this->overdue = $overdue;

        return $this;
    }

    public function getOverdueDateField(): string
    {
        return $this->overdueDateField;
    }

    public function setOverdueDateField(string $overdueDateField): self
    {
        $this->overdueDateField = $overdueDateField;

        return $this;
    }

    public function getWorkflow(): int
    {
        return $this->workflow;
    }

    public function setWorkflow(int $workflow): self
    {
        $this->workflow = $workflow;

        return $this;
    }

    public function getColour(): string
    {
        return $this->colour;
    }

    public function setColour(string $colour): self
    {
        $this->colour = $colour;

        return $this;
    }
}
