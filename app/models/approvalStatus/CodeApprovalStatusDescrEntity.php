<?php

declare(strict_types=1);

namespace app\models\approvalStatus;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "code_approval_status_descr")]
class CodeApprovalStatusDescrEntity
{
    #[Id]
    #[Column(type: "string")]
    private $code;

    #[Id]
    #[Column(type: "string")]
    private string $module;

    #[Id]
    #[Column(type: "integer")]
    private int $language;

    #[Column(type: "string")]
    private string $description;

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function setModule(string $module): self
    {
        $this->module = $module;

        return $this;
    }

    public function getLanguage(): int
    {
        return $this->language;
    }

    public function setLanguage(int $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }
}
