<?php

declare(strict_types=1);

namespace app\models\approvalStatus;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "approval_action")]
class ApprovalStatusActionEntity
{
    #[Id]
    #[Column(type: "integer")]
    private int $recordid;

    #[Column(name: "apac_from", type: "string")]
    private string $initialStatus;

    #[Column(name: "apac_to", type: "string")]
    private string $targetStatus;

    #[Column(name: "apac_action", type: "string")]
    private ?string $action;

    #[Column(type: "string")]
    private string $module;

    #[Column(name: "access_level", type: "string")]
    private string $accessLevel;

    #[Column(name: "apac_ignore_mandatory", type: "string")]
    private ?string $ignoreMandatory;

    #[Column(name: "apac_email_handler", type: "string")]
    private string $emailHandler;

    #[Column(name: "apac_email_reporter", type: "string")]
    private string $emailReporter;

    #[Column(name: "apac_workflow", type: "integer")]
    private string $workflow;

    #[Column(name: "apac_check_contacts", type: "string")]
    private string $checkContacts;

    public function getRecordid(): int
    {
        return $this->recordid;
    }

    public function setRecordid(int $recordid): self
    {
        $this->recordid = $recordid;

        return $this;
    }

    public function getInitialStatus(): string
    {
        return $this->initialStatus;
    }

    public function setInitialStatus(string $initialStatus): self
    {
        $this->initialStatus = $initialStatus;

        return $this;
    }

    public function getTargetStatus(): string
    {
        return $this->targetStatus;
    }

    public function setTargetStatus(string $targetStatus): self
    {
        $this->targetStatus = $targetStatus;

        return $this;
    }

    public function getAction(): ?string
    {
        return $this->action;
    }

    public function setAction(?string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getModule(): string
    {
        return $this->module;
    }

    public function setModule(string $module): self
    {
        $this->module = $module;

        return $this;
    }

    public function getAccessLevel(): string
    {
        return $this->accessLevel;
    }

    public function setAccessLevel(string $accessLevel): self
    {
        $this->accessLevel = $accessLevel;

        return $this;
    }

    public function getIgnoreMandatory(): ?string
    {
        return $this->ignoreMandatory;
    }

    public function setIgnoreMandatory(?string $ignoreMandatory): self
    {
        $this->ignoreMandatory = $ignoreMandatory;

        return $this;
    }

    public function getEmailHandler(): string
    {
        return $this->emailHandler;
    }

    public function setEmailHandler(string $emailHandler): self
    {
        $this->emailHandler = $emailHandler;

        return $this;
    }

    public function getEmailReporter(): string
    {
        return $this->emailReporter;
    }

    public function setEmailReporter(string $emailReporter): self
    {
        $this->emailReporter = $emailReporter;

        return $this;
    }

    public function getWorkflow(): string
    {
        return $this->workflow;
    }

    public function setWorkflow(string $workflow): self
    {
        $this->workflow = $workflow;

        return $this;
    }

    public function getCheckContacts(): string
    {
        return $this->checkContacts;
    }

    public function setCheckContacts(string $checkContacts): self
    {
        $this->checkContacts = $checkContacts;

        return $this;
    }
}
