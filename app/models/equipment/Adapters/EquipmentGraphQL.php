<?php

declare(strict_types=1);

namespace app\models\equipment\Adapters;

use app\models\equipment\entities\Equipment;
use GraphQL\Exception\QueryError;
use GraphQL\Mutation;
use GraphQL\Query;
use GraphQL\Variable;
use GuzzleHttp\Exception\TransferException;
use Ramsey\Uuid\Uuid;
use src\equipment\Exceptions\EquipmentException;
use src\equipment\Repositories\EquipmentInterface;
use src\medications\Clients\Client;

class EquipmentGraphQL implements EquipmentInterface
{
    /** @var Client */
    private $client;

    public function __construct()
    {
        $uri = getenv('MEDS_EQUIPMENT_URL') ?: self::DEFAULT_EQUIPMENT_URL;
        $this->client = new Client($uri);
    }

    public function attach(Equipment $equipment): void
    {
        $mutation = (new Mutation('SaveListOfEquipmentAnswers'))
            ->setVariables([new Variable('equipment', '[EquipmentAnswersInput]', true)])
            ->setArguments(['mutation' => '$equipment'])
            ->setSelectionSet(['id', 'recordId', 'recordType']);

        $equipmentAnswers = $equipment->toArray();

        // Generate a uuid when there's no equipment, to prevent the equipment answers service failing requests on validation
        if (empty($equipmentAnswers['equipmentId'])) {
            $equipmentAnswers['equipmentId'] = Uuid::uuid4()->toString();
        }

        $this->client->runQuery($mutation, true, ['equipment' => $equipmentAnswers]);
    }

    public function detach(Equipment $equipment): void
    {
        $mutation = (new Mutation('DeleteEquipmentAnswers'))
            ->setVariables([new Variable('answersId', 'String', true)])
            ->setArguments(['answersId' => '$answersId'])
            ->setSelectionSet(['result']);

        $result = $this->client->runQuery($mutation, true, ['answersId' => $equipment->getId()])
            ->getData();

        if (!isset($result['DeleteEquipmentAnswers']['result']) || $result['DeleteEquipmentAnswers']['result'] !== 'OK') {
            throw new EquipmentException("Cannot delete equipment [{$equipment->getId()}]");
        }
    }

    public function getAttachedRecords(string $uuid, string $recordType, string $locale): array
    {
        $query = (new Query('RequestListOfEquipmentAnswers'))
            ->setVariables([
                new Variable('id', 'String', true),
                new Variable('type', 'String', true),
                new Variable('locale', 'String', true),
            ])
            ->setArguments(['recordId' => '$id', 'recordType' => '$type', 'locale' => '$locale'])
            ->setSelectionSet([
                'id',
                'recordId',
                'recordType',
                (new Query('answers'))->setSelectionSet([
                    'id',
                    'questionId',
                    'answer',
                ]),
            ]);

        try {
            $results = $this->client->runQuery(
                $query,
                true,
                ['id' => $uuid, 'type' => $recordType, 'locale' => $locale],
            );

            return $results->getData()['RequestListOfEquipmentAnswers'] ?? [];
        } catch (QueryError|TransferException $exception) {
            throw new EquipmentException($exception->getMessage(), $exception->getCode(), $exception);
        }
    }
}
