<?php

declare(strict_types=1);

namespace app\models\equipment\hydrators;

use app\models\equipment\entities\Answer;

use function is_array;

class AnswerHydrator
{
    public function hydrate(array $data): Answer
    {
        $model = new Answer();
        $model->setId($data['id']);
        $model->setQuestionId($data['question_id'] ?? $data['questionId']);

        if (is_array($data['answer'])) {
            $answer = $data['answer'][0];
        } else {
            $answer = $data['answer'];
        }

        $model->setAnswer($answer);

        return $model;
    }

    public function extract(Answer $model): array
    {
        return [
            'id' => $model->getId(),
            'question_id' => $model->getQuestionId(),
            'answer' => $model->getAnswer(),
        ];
    }
}
