<?php

namespace app\models\equipment\hydrators;

use app\Hydrator\DateHydrator;
use app\models\equipment\entities\IncidentEquipmentLinkEntity;

/**
 * @codeCoverageIgnore
 */
class IncidentEquipmentLinkHydrator
{
    private DateHydrator $dateHydrator;

    public function __construct(DateHydrator $dateHydrator)
    {
        $this->dateHydrator = $dateHydrator;
    }

    /**
     * @return IncidentEquipmentLinkEntity[] $data
     */
    public function hydrateList(array $links): array
    {
        return array_map(function (array $link) {
            return $this->hydrateSingleLink($link);
        }, $links);
    }

    public function hydrateSingleLink(array $data, ?IncidentEquipmentLinkEntity $link = null): IncidentEquipmentLinkEntity
    {
        $link ??= new IncidentEquipmentLinkEntity();

        return $link
            ->setAnswerId($data['answer_id'])
            ->setIncidentId($data['incident_id'])
            ->setEquipmentReference($data['equipment_reference'])
            ->setProductType($data['product_type'])
            ->setExpiryDate($this->dateHydrator->hydrate($data['expiry_date']))
            ->setManufacturedDate($this->dateHydrator->hydrate($data['manufactured_date']))
            ->setSupplier($data['supplier'])
            ->setSupplyCategory($data['supply_category'])
            ->setSupplyClassification($data['supply_classification'])
            ->setCatalogueNumber($data['catalogue_number'])
            ->setBatchNumber($data['batch_number'])
            ->setSerialNumber($data['serial_number'])
            ->setQuantityUsed($data['quantity_used'])
            ->setCurrentLocation($data['current_location'])
            ->setDeviceType($data['device_type'])
            ->setOperator($data['operator'])
            ->setUsage($data['usage']);
    }

    /**
     * @param IncidentEquipmentLinkEntity[] $links
     *
     * @return array $data
     */
    public function extractList(array $links): array
    {
        return array_map(function (IncidentEquipmentLinkEntity $link) {
            return $this->extractSingleLink($link);
        }, $links);
    }

    /**
     * @return array
     */
    public function extractSingleLink(IncidentEquipmentLinkEntity $equipmentEntity)
    {
        return [
            'answer_id' => $equipmentEntity->getAnswerId(),
            'incident_id' => $equipmentEntity->getIncidentId(),
            'equipment_reference' => $equipmentEntity->getEquipmentReference(),
            'product_type' => $equipmentEntity->getProductType(),
            'expiryDate' => $this->dateHydrator->extract($equipmentEntity->getExpiryDate()),
            'manufactured_date' => $this->dateHydrator->extract($equipmentEntity->getManufacturedDate()),
            'supplier' => $equipmentEntity->getSupplier(),
            'supply_category' => $equipmentEntity->getSupplyCategory(),
            'supply_classification' => $equipmentEntity->getSupplyClassification(),
            'catalogue_number' => $equipmentEntity->getCatalogueNumber(),
            'batch_number' => $equipmentEntity->getBatchNumber(),
            'serial_number' => $equipmentEntity->getSerialNumber(),
            'quantity_used' => $equipmentEntity->getQuantityUsed(),
            'current_location' => $equipmentEntity->getCurrentLocation(),
            'device_type' => $equipmentEntity->getDeviceType(),
            'operator' => $equipmentEntity->getOperator(),
            'usage' => $equipmentEntity->getUsage(),
        ];
    }
}
