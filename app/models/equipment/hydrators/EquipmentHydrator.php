<?php

namespace app\models\equipment\hydrators;

use app\models\equipment\entities\EquipmentEntity;

/**
 * @codeCoverageIgnore
 */
class EquipmentHydrator
{
    public function hydrate(array $data): EquipmentEntity
    {
        $equipmentEntity = (new EquipmentEntity())
            ->setEquipmentReference($data['reference'])
            ->setBrand($data['brand'])
            ->setType($data['type'])
            ->setSubType($data['sub_type'])
            ->setGenericName($data['generic_name'])
            ->setManufacturer($data['manufacturer'])
            ->setModel($data['model'])
            ->setSupplier($data['supplier'])
            ->setSupplyCategory($data['supply_category'])
            ->setSupplyClassification($data['supply_classification'])
            ->setSourceId($data['source_id']);

        if (!empty($data['locale'])) {
            $equipmentEntity->setLocale($data['locale']);
        }

        return $equipmentEntity;
    }

    /**
     * @return array
     */
    public function extract(EquipmentEntity $equipmentEntity)
    {
        return [
            'reference' => $equipmentEntity->getEquipmentReference(),
            'locale' => $equipmentEntity->getLocale(),
            'brand' => $equipmentEntity->getBrand(),
            'type' => $equipmentEntity->getType(),
            'sub_type' => $equipmentEntity->getSubType(),
            'generic_name' => $equipmentEntity->getGenericName(),
            'manufacturer' => $equipmentEntity->getManufacturer(),
            'model' => $equipmentEntity->getModel(),
            'supplier' => $equipmentEntity->getSupplier(),
            'supply_category' => $equipmentEntity->getSupplyCategory(),
            'supply_classification' => $equipmentEntity->getSupplyClassification(),
            'source_id' => $equipmentEntity->getSourceid(),
        ];
    }
}
