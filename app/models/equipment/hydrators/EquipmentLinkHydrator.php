<?php

declare(strict_types=1);

namespace app\models\equipment\hydrators;

use app\Hydrator\DateHydrator;
use app\models\equipment\entities\EquipmentLinkEntity;
use src\equipment\models\EquipmentFields;

class EquipmentLinkHydrator
{
    private DateHydrator $dateHydrator;

    public function __construct(DateHydrator $dateHydrator)
    {
        $this->dateHydrator = $dateHydrator;
    }

    public function extract(EquipmentLinkEntity $entity): array
    {
        return [
            'id' => $entity->getAnswerId(),
            'record_id' => $entity->getEntityUuid(),
            'record_type' => $entity->getRecordType(),
            'equipment_id' => $entity->getEquipmentReference(),
            'answers' => array_filter([
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::DEVICE_TYPE),
                    'answer' => $entity->getDeviceType(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SUPPLIER),
                    'answer' => $entity->getSupplier(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::OPERATOR),
                    'answer' => $entity->getOperator(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::QUANTITY_USED),
                    'answer' => $entity->getQuantityUsed(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SERIAL_NUMBER),
                    'answer' => $entity->getSerialNumber(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::BATCH_NUMBER),
                    'answer' => $entity->getBatchNumber(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::EXPIRY_DATE),
                    'answer' => $this->dateHydrator->extract($entity->getExpiryDate()),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::MANUFACTURED_DATE),
                    'answer' => $this->dateHydrator->extract($entity->getManufacturedDate()),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::CATELOGUE_NUMBER),
                    'answer' => $entity->getCatalogueNumber(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::USAGE),
                    'answer' => $entity->getUsage(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::CURRENT_LOCATION),
                    'answer' => $entity->getCurrentLocation(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SUPPLY_CATEGORY),
                    'answer' => $entity->getSupplyCategory(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SUPPLY_CLASSIFICATION),
                    'answer' => $entity->getSupplyClassification(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl('equipment_product_type'),
                    'answer' => $entity->getProductType(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SEARCH_TERM),
                    'answer' => $entity->getSearchTerm(),
                ],
                [
                    'question_id' => EquipmentFields::convertForGraphQl(EquipmentFields::SEARCH_CATEGORY),
                    'answer' => $entity->getSearchCategory(),
                ],
            ], static fn (array $item): bool => isset($item['answer'])),
        ];
    }
}
