<?php

declare(strict_types=1);

namespace app\models\equipment\hydrators;

use app\models\equipment\entities\Answer;
use app\models\equipment\entities\Equipment;
use Ramsey\Uuid\Uuid;

class EquipmentModelHydrator
{
    public function hydrate(array $data): Equipment
    {
        $uuid = Uuid::uuid4();
        $answerId = $data['id'] ?? $uuid->toString();

        $model = new Equipment();
        $model->setId($answerId);
        $model->setRecordId($data['record_id'] ?? $data['recordId']);
        $model->setRecordType($data['record_type'] ?? $data['recordType']);
        $model->setEquipmentId($data['equipment_id'] ?? $data['equipmentId']);

        $hydrator = new AnswerHydrator();
        $answers = array_map(static function (array $answer) use ($hydrator, $answerId): Answer {
            $answer['id'] ??= $answerId;

            return $hydrator->hydrate($answer);
        }, $data['answers'] ?? []);
        $model->setAnswers($answers);

        return $model;
    }

    public function extract(Equipment $model): array
    {
        $hydrator = new AnswerHydrator();

        return [
            'id' => $model->getId(),
            'record_id' => $model->getRecordId(),
            'record_type' => $model->getRecordType(),
            'equipment_id' => $model->getEquipmentId(),
            'answers' => array_map(static function (Answer $answer) use ($hydrator): array {
                return $hydrator->extract($answer);
            }, $model->getAnswers()),
        ];
    }
}
