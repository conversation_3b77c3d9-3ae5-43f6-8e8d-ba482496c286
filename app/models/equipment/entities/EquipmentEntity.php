<?php

namespace app\models\equipment\entities;

use app\models\equipment\repositories\EquipmentRepository;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity(repositoryClass: EquipmentRepository::class)]
#[Table(name: "equipment")]
class EquipmentEntity
{
    public const DEFAULT_LOCALE = 'en_GB';

    /**
     * @var int
     *
     * @OA\Property(property="recordid", type="integer")
     */
    #[Id]
    #[GeneratedValue(strategy: "AUTO")]
    #[Column(type: "integer")]
    private $recordid;

    /**
     * @var string
     *
     * @OA\Property(property="reference", type="string")
     */
    #[Column(type: "string")]
    private $equipment_reference;

    /**
     * @var string
     *
     * @OA\Property(property="locale", type="string")
     */
    #[Column(type: "string")]
    private $locale = self::DEFAULT_LOCALE;

    /**
     * @var string
     *
     * @OA\Property(property="brand", type="string")
     */
    #[Column(type: "string")]
    private $brand;

    /**
     * @var string
     *
     * @OA\Property(property="type", type="string")
     */
    #[Column(type: "string")]
    private $type;

    /**
     * @var string
     *
     * @OA\Property(property="sub_type", type="string")
     */
    #[Column(type: "string")]
    private $sub_type;

    /**
     * @var string
     *
     * @OA\Property(property="generic_name", type="string")
     */
    #[Column(type: "string")]
    private $generic_name;

    /**
     * @var string
     *
     * @OA\Property(property="manufacturer", type="string")
     */
    #[Column(type: "string")]
    private $manufacturer;

    /**
     * @var string
     *
     * @OA\Property(property="model", type="string")
     */
    #[Column(type: "string")]
    private $model;

    /**
     * @var string
     *
     * @OA\Property(property="supplier", type="string")
     */
    #[Column(type: "string")]
    private $supplier;

    /**
     * @var string
     *
     * @OA\Property(property="supply_classification", type="string")
     */
    #[Column(type: "string")]
    private $supply_classification;

    /**
     * @var string
     *
     * @OA\Property(property="supply_category", type="string")
     */
    #[Column(type: "string")]
    private $supply_category;

    /**
     * @var string
     *
     * @OA\Property(property="source_id", type="string")
     */
    #[Column(type: "string")]
    private $source_id;

    #[Column(type: "boolean", nullable: false)]
    private $is_deleted = false;

    /**
     * @return string
     */
    public function getEquipmentReference()
    {
        return $this->equipment_reference;
    }

    public function setEquipmentReference(string $equipment_reference): self
    {
        $this->equipment_reference = $equipment_reference;

        return $this;
    }

    public function getLocale(): string
    {
        return $this->locale;
    }

    public function setLocale(string $locale): self
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @return string
     */
    public function getBrand()
    {
        return $this->brand;
    }

    /**
     * @param string $brand
     */
    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param string $type
     */
    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return string
     */
    public function getSubType()
    {
        return $this->sub_type;
    }

    /**
     * @param string $sub_type
     */
    public function setSubType(?string $sub_type): self
    {
        $this->sub_type = $sub_type;

        return $this;
    }

    /**
     * @return string
     */
    public function getGenericName()
    {
        return $this->generic_name;
    }

    /**
     * @param string $generic_name
     */
    public function setGenericName(?string $generic_name): self
    {
        $this->generic_name = $generic_name;

        return $this;
    }

    /**
     * @return string
     */
    public function getManufacturer()
    {
        return $this->manufacturer;
    }

    /**
     * @param string $manufacturer
     */
    public function setManufacturer(?string $manufacturer): self
    {
        $this->manufacturer = $manufacturer;

        return $this;
    }

    /**
     * @return string
     */
    public function getModel()
    {
        return $this->model;
    }

    /**
     * @param string $model
     *
     * @return self
     */
    public function setModel(?string $model)
    {
        $this->model = $model;

        return $this;
    }

    public function getSupplier(): string
    {
        return $this->supplier;
    }

    public function setSupplier(string $supplier): self
    {
        $this->supplier = $supplier;

        return $this;
    }

    public function getSupplyClassification(): string
    {
        return $this->supply_classification;
    }

    public function setSupplyClassification(string $supply_classification): self
    {
        $this->supply_classification = $supply_classification;

        return $this;
    }

    public function getSupplyCategory(): string
    {
        return $this->supply_category;
    }

    public function setSupplyCategory(string $supply_category): self
    {
        $this->supply_category = $supply_category;

        return $this;
    }

    public function getSourceId(): string
    {
        return $this->source_id;
    }

    public function setSourceId(string $source_id): self
    {
        $this->source_id = $source_id;

        return $this;
    }

    public function setIsDeleted(bool $is_deleted): self
    {
        $this->is_deleted = $is_deleted;

        return $this;
    }

    public function toArray(): array
    {
        $array = get_object_vars($this);
        unset($array['recordid']);

        return $array;
    }

    public static function getFields(): array
    {
        $fields = array_keys(get_class_vars(__CLASS__));

        return array_diff($fields, ['recordid']);
    }

    public function merge(self $equipmentEntity): void
    {
        $this->setBrand($equipmentEntity->getBrand())
            ->setType($equipmentEntity->getType())
            ->setSubType($equipmentEntity->getSubType())
            ->setGenericName($equipmentEntity->getGenericName())
            ->setManufacturer($equipmentEntity->getManufacturer())
            ->setModel($equipmentEntity->getModel())
            ->setSupplier($equipmentEntity->getSupplier())
            ->setSupplyClassification($equipmentEntity->getSupplyClassification())
            ->setSupplyCategory($equipmentEntity->getSupplyCategory())
            ->setSourceId($equipmentEntity->getSourceId());
    }
}
