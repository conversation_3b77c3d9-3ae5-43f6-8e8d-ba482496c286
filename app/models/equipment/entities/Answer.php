<?php

declare(strict_types=1);

namespace app\models\equipment\entities;

/**
 * Answer model is used to map calls to GraphQL.
 */
class Answer
{
    /**
     * UUID of the answer.
     *
     * @var string
     */
    private $id;

    /**
     * The question ID which the answer is linked to.
     *
     * @var string
     */
    private $questionId;

    /**
     * string value of the answer.
     *
     * @var string
     */
    private $answer;

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getQuestionId(): string
    {
        return $this->questionId;
    }

    public function setQuestionId(string $questionId): void
    {
        $this->questionId = $questionId;
    }

    public function getAnswer(): string
    {
        return $this->answer;
    }

    public function setAnswer(string $answer): void
    {
        $this->answer = $answer;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'questionId' => $this->getQuestionId(),
            'answer' => $this->getAnswer(),
        ];
    }
}
