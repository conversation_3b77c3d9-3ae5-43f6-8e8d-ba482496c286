<?php

declare(strict_types=1);

namespace app\models\equipment\entities;

use DateTimeInterface;

interface EquipmentLinkEntity
{
    public function getRecordId(): ?string;

    public function getRecordType(): string;

    public function getEquipmentReference(): string;

    public function setEquipmentReference(string $equipment_reference): self;

    public function getDeviceType(): ?string;

    public function setDeviceType(?string $device_type): self;

    public function getSupplier(): ?string;

    public function setSupplier(?string $supplier): self;

    public function getOperator(): ?string;

    public function setOperator(?string $operator): self;

    public function getQuantityUsed(): ?string;

    public function setQuantityUsed(?string $quantity_used): self;

    public function getSerialNumber(): ?string;

    public function setSerialNumber(?string $serial_number): self;

    public function getBatchNumber(): ?string;

    public function setBatchNumber(?string $batch_number): self;

    public function getExpiryDate(): ?DateTimeInterface;

    public function setExpiryDate(?DateTimeInterface $expiry_date): self;

    public function getManufacturedDate(): ?DateTimeInterface;

    public function setManufacturedDate(?DateTimeInterface $manufactured_date): self;

    public function getCatalogueNumber(): ?string;

    public function setCatalogueNumber(?string $catalogue_number): self;

    public function getUsage(): ?string;

    public function setUsage(?string $usage): self;

    public function getCurrentLocation(): ?string;

    public function setCurrentLocation(?string $current_location): self;

    public function getAnswerId(): string;

    public function setAnswerId(string $answer_id): self;

    public function getEntityUuid(): string;

    public function setEntityUuid(string $uuid): self;

    public function getProductType(): ?string;

    public function setProductType(?string $product_type): self;

    public function getSupplyCategory(): ?string;

    public function setSupplyCategory(?string $supply_category): self;

    public function getSupplyClassification(): ?string;

    public function setSupplyClassification(?string $supply_classification): self;

    public function getSearchTerm(): ?string;

    public function setSearchTerm(?string $search_term): self;

    public function getSearchCategory(): ?string;

    public function setSearchCategory(?string $search_category): self;
}
