<?php

declare(strict_types=1);

namespace app\models\equipment\entities;

use src\equipment\models\EquipmentFields;

/**
 * Equipment model is used to map calls to GraphQL.
 */
class Equipment
{
    /**
     * UUID of the equipment.
     *
     * @var string
     */
    private $id;

    /**
     * Record ID which will be linked.
     *
     * @var string
     */
    private $recordId;

    /**
     * record type which will be linked.
     *
     * @var string
     */
    private $recordType;

    /**
     * UUID of the equipment.
     *
     * @var string
     */
    private $equipmentId;

    /** @var Answer[] */
    private $answers = [];

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): void
    {
        $this->id = $id;
    }

    public function getRecordId(): string
    {
        return $this->recordId;
    }

    public function setRecordId(string $recordId): void
    {
        $this->recordId = $recordId;
    }

    public function getRecordType(): string
    {
        return $this->recordType;
    }

    public function setRecordType(string $recordType): void
    {
        $this->recordType = $recordType;
    }

    public function getEquipmentId(): string
    {
        return $this->equipmentId;
    }

    public function setEquipmentId(string $equipmentId): void
    {
        $this->equipmentId = $equipmentId;
    }

    /**
     * @return Answer[]
     */
    public function getAnswers(): array
    {
        return $this->answers;
    }

    /**
     * @param Answer[] $answers
     */
    public function setAnswers(array $answers): void
    {
        $this->answers = $answers;
    }

    public function getAnswer(string $questionId): ?string
    {
        $questionIdGQL = EquipmentFields::convertForGraphQl($questionId);

        foreach ($this->answers as $answer) {
            if ($answer->getQuestionId() === $questionId || $answer->getQuestionId() === $questionIdGQL) {
                return $answer->getAnswer();
            }
        }

        return null;
    }

    public function setAnswer(string $questionId, string $answerValue): void
    {
        $questionIdGQL = EquipmentFields::convertForGraphQl($questionId);

        foreach ($this->answers as $answer) {
            if ($answer->getQuestionId() === $questionId || $answer->getQuestionId() === $questionIdGQL) {
                $answer->setAnswer($answerValue);

                return;
            }
        }

        $answer = new Answer();
        $answer->setQuestionId($questionIdGQL);
        $answer->setAnswer($answerValue);
        $answer->setId($this->getId());
        $this->answers[] = $answer;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'recordId' => $this->getRecordId(),
            'recordType' => $this->getRecordType(),
            'equipmentId' => $this->getEquipmentId(),
            'answers' => array_map(static function (Answer $answer): array {
                return $answer->toArray();
            }, $this->getAnswers()),
        ];
    }
}
