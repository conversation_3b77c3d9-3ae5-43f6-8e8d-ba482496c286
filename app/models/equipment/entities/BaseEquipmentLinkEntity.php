<?php

declare(strict_types=1);

namespace app\models\equipment\entities;

use DateTimeInterface;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema
 */
abstract class BaseEquipmentLinkEntity implements EquipmentLinkEntity
{
    /**
     * @OA\Property
     */
    #[Id]
    #[GeneratedValue(strategy: "AUTO")]
    #[Column(type: "integer")]
    protected ?int $recordid = null;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $answer_id;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $equipment_reference;

    #[Column(type: "string")]
    protected ?string $product_type;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $device_type;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $supplier;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $supply_category;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $supply_classification;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $operator;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $quantity_used;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $serial_number;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $batch_number;

    /**
     * @OA\Property
     */
    #[Column(type: "datetime")]
    protected ?DateTimeInterface $expiry_date;

    /**
     * @OA\Property
     */
    #[Column(type: "datetime")]
    protected ?DateTimeInterface $manufactured_date;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $catalogue_number;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $usage;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $current_location;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $search_term;

    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    protected ?string $search_category;

    public function getRecordid(): ?string
    {
        return $this->recordid;
    }

    public function getEquipmentReference(): string
    {
        return $this->equipment_reference;
    }

    public function setEquipmentReference(string $equipment_reference): self
    {
        $this->equipment_reference = $equipment_reference;

        return $this;
    }

    public function getDeviceType(): ?string
    {
        return $this->device_type;
    }

    public function setDeviceType(?string $device_type): self
    {
        $this->device_type = $device_type;

        return $this;
    }

    public function getSupplier(): ?string
    {
        return $this->supplier;
    }

    public function setSupplier(?string $supplier): self
    {
        $this->supplier = $supplier;

        return $this;
    }

    public function getOperator(): ?string
    {
        return $this->operator;
    }

    public function setOperator(?string $operator): self
    {
        $this->operator = $operator;

        return $this;
    }

    public function getQuantityUsed(): ?string
    {
        return $this->quantity_used;
    }

    public function setQuantityUsed(?string $quantity_used): self
    {
        $this->quantity_used = $quantity_used;

        return $this;
    }

    public function getSerialNumber(): ?string
    {
        return $this->serial_number;
    }

    public function setSerialNumber(?string $serial_number): self
    {
        $this->serial_number = $serial_number;

        return $this;
    }

    public function getBatchNumber(): ?string
    {
        return $this->batch_number;
    }

    public function setBatchNumber(?string $batch_number): self
    {
        $this->batch_number = $batch_number;

        return $this;
    }

    public function getExpiryDate(): ?DateTimeInterface
    {
        return $this->expiry_date;
    }

    public function setExpiryDate(?DateTimeInterface $expiry_date): self
    {
        $this->expiry_date = $expiry_date;

        return $this;
    }

    public function getManufacturedDate(): ?DateTimeInterface
    {
        return $this->manufactured_date;
    }

    public function setManufacturedDate(?DateTimeInterface $manufactured_date): self
    {
        $this->manufactured_date = $manufactured_date;

        return $this;
    }

    public function getCatalogueNumber(): ?string
    {
        return $this->catalogue_number;
    }

    public function setCatalogueNumber(?string $catalogue_number): self
    {
        $this->catalogue_number = $catalogue_number;

        return $this;
    }

    public function getUsage(): ?string
    {
        return $this->usage;
    }

    public function setUsage(?string $usage): self
    {
        $this->usage = $usage;

        return $this;
    }

    public function getCurrentLocation(): ?string
    {
        return $this->current_location;
    }

    public function setCurrentLocation(?string $current_location): self
    {
        $this->current_location = $current_location;

        return $this;
    }

    public function getAnswerId(): string
    {
        return $this->answer_id;
    }

    public function setAnswerId(string $answer_id): self
    {
        $this->answer_id = $answer_id;

        return $this;
    }

    public function getProductType(): ?string
    {
        return $this->product_type;
    }

    public function setProductType(?string $product_type): self
    {
        $this->product_type = $product_type;

        return $this;
    }

    public function getSupplyCategory(): ?string
    {
        return $this->supply_category;
    }

    public function setSupplyCategory(?string $supply_category): self
    {
        $this->supply_category = $supply_category;

        return $this;
    }

    public function getSupplyClassification(): ?string
    {
        return $this->supply_classification;
    }

    public function setSupplyClassification(?string $supply_classification): self
    {
        $this->supply_classification = $supply_classification;

        return $this;
    }

    public function getSearchTerm(): ?string
    {
        return $this->search_term;
    }

    public function setSearchTerm(?string $search_term): self
    {
        $this->search_term = $search_term;

        return $this;
    }

    public function getSearchCategory(): ?string
    {
        return $this->search_category;
    }

    public function setSearchCategory(?string $search_category): self
    {
        $this->search_category = $search_category;

        return $this;
    }
}
