<?php

namespace app\models\equipment\entities;

use app\models\equipment\repositories\IncidentSyncedEquipmentLinkRepository;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity(repositoryClass: IncidentSyncedEquipmentLinkRepository::class)]
#[Table(name: "incident_equipment_link")]
class IncidentEquipmentLinkEntity extends BaseEquipmentLinkEntity
{
    /**
     * @OA\Property
     */
    #[Column(type: "string")]
    private string $incident_id;

    public function getRecordType(): string
    {
        return 'incident';
    }

    public function getIncidentId(): ?string
    {
        return $this->incident_id;
    }

    public function setIncidentId(string $incident_id): self
    {
        $this->incident_id = $incident_id;

        return $this;
    }

    public function getEntityUuid(): string
    {
        return $this->getIncidentId();
    }

    public function setEntityUuid(string $uuid): self
    {
        return $this->setIncidentId($uuid);
    }
}
