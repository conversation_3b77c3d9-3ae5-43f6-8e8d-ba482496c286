<?php

namespace app\models\equipment\repositories;

use app\models\equipment\entities\EquipmentLinkEntity;
use app\models\equipment\entities\IncidentEquipmentLinkEntity;
use Doctrine\ORM\EntityRepository;
use Ramsey\Uuid\Uuid;

/**
 * @extends EntityRepository<EquipmentLinkEntity>
 */
class IncidentSyncedEquipmentLinkRepository extends BaseEquipmentLinkRepository
{
    public function getByEntityUuid(string $uuid): array
    {
        return $this->findBy(['incident_id' => $uuid]);
    }

    public function getById(string $id): ?EquipmentLinkEntity
    {
        return $this->findOneBy(['answer_id' => $id]);
    }

    public function createNew(): EquipmentLinkEntity
    {
        $answerId = Uuid::uuid4()->toString();

        return (new IncidentEquipmentLinkEntity())
            ->setAnswerId($answerId);
    }

    /**
     * Delete medication answer (and link to incident) by id.
     */
    public function deleteAnswerById(string $id): void
    {
        $statement = $this->createQueryBuilder('e')
            ->delete()
            ->where('e.answer_id = :id')
            ->getQuery();

        $statement->execute(['id' => $id]);
    }
}
