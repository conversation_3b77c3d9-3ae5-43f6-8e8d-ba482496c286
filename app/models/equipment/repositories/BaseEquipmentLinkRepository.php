<?php

declare(strict_types=1);

namespace app\models\equipment\repositories;

use app\models\equipment\entities\BaseEquipmentLinkEntity;
use app\models\equipment\entities\EquipmentLinkEntity;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<BaseEquipmentLinkEntity>
 */
abstract class BaseEquipmentLinkRepository extends EntityRepository implements EquipmentLinkRepository
{
    public function persist(EquipmentLinkEntity $entity): void
    {
        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();
    }

    public function deleteEntity(EquipmentLinkEntity $entity): void
    {
        $em = $this->getEntityManager();
        $em->remove($entity);
        $em->flush();
    }
}
