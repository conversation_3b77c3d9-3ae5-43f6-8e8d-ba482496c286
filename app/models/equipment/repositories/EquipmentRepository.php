<?php

declare(strict_types=1);

namespace app\models\equipment\repositories;

use app\models\equipment\entities\EquipmentEntity;
use Doctrine\ORM\EntityRepository;

/**
 * @extends EntityRepository<EquipmentEntity>
 */
class EquipmentRepository extends EntityRepository
{
    public function findOneByReferenceAndLocale(string $reference, string $locale): ?EquipmentEntity
    {
        $equipment = $this->findOneBy([
            'equipment_reference' => $reference,
            'locale' => $locale,
        ]);

        if (!$equipment) {
            $equipment = $this->findOneBy([
                'equipment_reference' => $reference,
                'locale' => EquipmentEntity::DEFAULT_LOCALE,
            ]);
        }

        return $equipment;
    }

    public function persist(EquipmentEntity $entity): void
    {
        $em = $this->getEntityManager();
        $em->persist($entity);
        $em->flush();
    }
}
