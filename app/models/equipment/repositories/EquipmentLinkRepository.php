<?php

namespace app\models\equipment\repositories;

use app\models\equipment\entities\EquipmentLinkEntity;
use Doctrine\Persistence\ObjectRepository;

interface EquipmentLinkRepository extends ObjectRepository
{
    public function getByEntityUuid(string $uuid): array;

    public function getById(string $id): ?EquipmentLinkEntity;

    public function createNew(): EquipmentLinkEntity;

    public function persist(EquipmentLinkEntity $entity): void;

    public function deleteEntity(EquipmentLinkEntity $entity): void;
}
