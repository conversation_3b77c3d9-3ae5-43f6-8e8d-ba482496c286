<?php

namespace app\models\extraFields\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "udf_mod_link")]
class ExtraFieldModulesEntity
{
    #[Id]
    #[Column(type: "string")]
    private $uml_module;

    #[ManyToOne(
        targetEntity: ExtraFieldEntity::class,
        inversedBy: "modules",
        cascade: ["merge", "persist"],
    )]
    #[JoinColumn(
        name: "uml_id",
        referencedColumnName: "recordid"
    )]
    private $extraField;

    #[Id]
    #[Column(type: "integer")]
    private $uml_id;

    /**
     * @return mixed
     */
    public function getUmlModule()
    {
        return $this->uml_module;
    }

    /**
     * @param mixed $uml_module
     *
     * @return ExtraFieldModulesEntity
     */
    public function setUmlModule($uml_module)
    {
        $this->uml_module = $uml_module;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getExtraField()
    {
        return $this->extraField;
    }

    /**
     * @param mixed $extraField
     *
     * @return ExtraFieldModulesEntity
     */
    public function setExtraField($extraField)
    {
        $this->extraField = $extraField;

        return $this;
    }
}
