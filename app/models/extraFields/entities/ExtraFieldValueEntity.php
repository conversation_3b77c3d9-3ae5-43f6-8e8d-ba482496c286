<?php

namespace app\models\extraFields\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "udf_values")]
class ExtraFieldValueEntity
{
    #[Id]
    #[Column(type: "integer")]
    private $mod_id;

    #[Id]
    #[Column(type: "integer")]
    private $cas_id;

    #[Column(type: "integer")]
    private $group_id;

    #[Id]
    #[Column(type: "integer")]
    private $field_id;

    #[Column(type: "string")]
    private $udv_string;

    #[Column(type: "decimal", precision: 14, scale: 5)]
    private $udv_number;

    #[Column(type: "string")]
    private $udv_date;

    #[Column(type: "decimal", precision: 13, scale: 2)]
    private $udv_money;

    #[Column(type: "integer")]
    private $con_id;

    #[Column(type: "string")]
    private $udv_text;

    /**
     * @return mixed
     */
    public function getModId()
    {
        return $this->mod_id;
    }

    /**
     * @param mixed $mod_id
     *
     * @return ExtraFieldValueEntity
     */
    public function setModId($mod_id)
    {
        $this->mod_id = $mod_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCasId()
    {
        return $this->cas_id;
    }

    /**
     * @param mixed $cas_id
     *
     * @return ExtraFieldValueEntity
     */
    public function setCasId($cas_id)
    {
        $this->cas_id = $cas_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getGroupId()
    {
        return $this->group_id;
    }

    /**
     * @param mixed $group_id
     *
     * @return ExtraFieldValueEntity
     */
    public function setGroupId($group_id)
    {
        $this->group_id = $group_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFieldId()
    {
        return $this->field_id;
    }

    /**
     * @param mixed $field_id
     *
     * @return ExtraFieldValueEntity
     */
    public function setFieldId($field_id)
    {
        $this->field_id = $field_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUdvString()
    {
        return $this->udv_string;
    }

    /**
     * @param mixed $udv_string
     *
     * @return ExtraFieldValueEntity
     */
    public function setUdvString($udv_string)
    {
        $this->udv_string = $udv_string;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUdvNumber()
    {
        return $this->udv_number;
    }

    /**
     * @param mixed $udv_number
     *
     * @return ExtraFieldValueEntity
     */
    public function setUdvNumber($udv_number)
    {
        $this->udv_number = $udv_number;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUdvDate()
    {
        return $this->udv_date;
    }

    /**
     * @param mixed $udv_date
     *
     * @return ExtraFieldValueEntity
     */
    public function setUdvDate($udv_date)
    {
        $this->udv_date = $udv_date;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUdvMoney()
    {
        return $this->udv_money;
    }

    /**
     * @param mixed $udv_money
     *
     * @return ExtraFieldValueEntity
     */
    public function setUdvMoney($udv_money)
    {
        $this->udv_money = $udv_money;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getConId()
    {
        return $this->con_id;
    }

    /**
     * @param mixed $con_id
     *
     * @return ExtraFieldValueEntity
     */
    public function setConId($con_id)
    {
        $this->con_id = $con_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getUdvText()
    {
        return $this->udv_text;
    }

    /**
     * @param mixed $udv_text
     *
     * @return ExtraFieldValueEntity
     */
    public function setUdvText($udv_text)
    {
        $this->udv_text = $udv_text;

        return $this;
    }
}
