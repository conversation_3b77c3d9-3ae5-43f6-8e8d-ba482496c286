<?php

namespace app\models\extraFields\entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "udf_fields")]
class ExtraFieldEntity
{
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    private $recordid;

    #[Column(type: "string")]
    private $fld_name;

    #[Column(type: "string")]
    private $fld_type;

    #[Column(type: "integer")]
    private $mod_id;

    #[Column(type: "string")]
    private $fld_format;

    #[Column(type: "string")]
    private $fld_code_like;

    #[Column(type: "string")]
    private $fld_code_like_table;

    #[Column(type: "integer")]
    private $central_id;

    #[Column(type: "string")]
    private $central_source;

    #[Column(type: "integer")]
    private $fld_length;

    #[Column(type: "string")]
    private $central_locked;

    #[Column(type: "integer")]
    private $queryid;

    #[Column(type: "string")]
    private $fld_code_like_module;

    /**
     * @var Collection<ExtraFieldModulesEntity>
     */
    #[OneToMany(
        mappedBy: "extraField",
        targetEntity: ExtraFieldModulesEntity::class,
        cascade: ["persist", "remove"],
    )]
    private Collection $modules;

    public function __construct()
    {
        $this->modules = new ArrayCollection();
    }

    /**
     * @return mixed
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param mixed $recordid
     *
     * @return ExtraFieldEntity
     */
    public function setRecordid($recordid)
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldName()
    {
        return $this->fld_name;
    }

    /**
     * @param mixed $fld_name
     *
     * @return ExtraFieldEntity
     */
    public function setFldName($fld_name)
    {
        $this->fld_name = $fld_name;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldType()
    {
        return $this->fld_type;
    }

    /**
     * @param mixed $fld_type
     *
     * @return ExtraFieldEntity
     */
    public function setFldType($fld_type)
    {
        $this->fld_type = $fld_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getModId()
    {
        return $this->mod_id;
    }

    /**
     * @param mixed $mod_id
     *
     * @return ExtraFieldEntity
     */
    public function setModId($mod_id)
    {
        $this->mod_id = $mod_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldFormat()
    {
        return $this->fld_format;
    }

    /**
     * @param mixed $fld_format
     *
     * @return ExtraFieldEntity
     */
    public function setFldFormat($fld_format)
    {
        $this->fld_format = $fld_format;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldCodeLike()
    {
        return $this->fld_code_like;
    }

    /**
     * @param mixed $fld_code_like
     *
     * @return ExtraFieldEntity
     */
    public function setFldCodeLike($fld_code_like)
    {
        $this->fld_code_like = $fld_code_like;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldCodeLikeTable()
    {
        return $this->fld_code_like_table;
    }

    /**
     * @param mixed $fld_code_like_table
     *
     * @return ExtraFieldEntity
     */
    public function setFldCodeLikeTable($fld_code_like_table)
    {
        $this->fld_code_like_table = $fld_code_like_table;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCentralId()
    {
        return $this->central_id;
    }

    /**
     * @param mixed $central_id
     *
     * @return ExtraFieldEntity
     */
    public function setCentralId($central_id)
    {
        $this->central_id = $central_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCentralSource()
    {
        return $this->central_source;
    }

    /**
     * @param mixed $central_source
     *
     * @return ExtraFieldEntity
     */
    public function setCentralSource($central_source)
    {
        $this->central_source = $central_source;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldLength()
    {
        return $this->fld_length;
    }

    /**
     * @param mixed $fld_length
     *
     * @return ExtraFieldEntity
     */
    public function setFldLength($fld_length)
    {
        $this->fld_length = $fld_length;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCentralLocked()
    {
        return $this->central_locked;
    }

    /**
     * @param mixed $central_locked
     *
     * @return ExtraFieldEntity
     */
    public function setCentralLocked($central_locked)
    {
        $this->central_locked = $central_locked;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getQueryid()
    {
        return $this->queryid;
    }

    /**
     * @param mixed $queryid
     *
     * @return ExtraFieldEntity
     */
    public function setQueryid($queryid)
    {
        $this->queryid = $queryid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFldCodeLikeModule()
    {
        return $this->fld_code_like_module;
    }

    /**
     * @param mixed $fld_code_like_module
     *
     * @return ExtraFieldEntity
     */
    public function setFldCodeLikeModule($fld_code_like_module)
    {
        $this->fld_code_like_module = $fld_code_like_module;

        return $this;
    }

    /**
     * @return Collection<ExtraFieldModulesEntity>
     */
    public function getModules(): Collection
    {
        return $this->modules;
    }

    /**
     * @param Collection<ExtraFieldModulesEntity> $modules
     *
     * @return ExtraFieldEntity
     */
    public function setModules($modules)
    {
        $this->modules = $modules;

        return $this;
    }
}
