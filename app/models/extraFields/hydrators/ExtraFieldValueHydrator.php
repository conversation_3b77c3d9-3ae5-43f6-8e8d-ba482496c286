<?php

namespace app\models\extraFields\hydrators;

use app\models\extraFields\entities\ExtraFieldEntity;
use app\models\extraFields\entities\ExtraFieldValueEntity;
use app\models\generic\valueObjects\Module;
use app\services\carlton\generic\DateAdapter;
use Exception;
use src\system\database\FieldInterface;

use function in_array;

/** @codeCoverageIgnore  */
class ExtraFieldValueHydrator
{
    protected $extraField;
    protected $dateAdapter;
    protected $module;

    public function __construct(ExtraFieldEntity $extraField, DateAdapter $dateAdapter, $module)
    {
        $this->extraField = $extraField;
        $this->dateAdapter = $dateAdapter;
        $this->module = $module;
    }

    /**
     * @return ExtraFieldValueEntity
     *
     * @throws Exception
     */
    public function hydrate(array $data)
    {
        $availableModules = array_map(function ($e) {
            return $e->getUmlModule();
        }, $this->extraField->getModules()->toArray());

        $module = (new Module($this->module));
        $moduleCode = $module->getId();

        if (!empty($availableModules) && !in_array($this->module, $availableModules)) {
            throw new Exception('Extra field not available for ' . $module->getSingularName() . ' module');
        }

        $udfType = $this->extraField->getFldType();

        $extraFieldValueEntity = (new ExtraFieldValueEntity())
            ->setModId($moduleCode)
            ->setCasId($data['recordId'])
            ->setGroupId($data['group_id'] ?: 0)
            ->setFieldId($data['udfFieldId']);

        switch ($udfType) {
            case FieldInterface::STRING_CODE:
            case FieldInterface::CODE_CODE:
            case FieldInterface::MULTI_SELECT_CODE:
            case FieldInterface::YESNO_CODE:
                $extraFieldValueEntity->setUdvString($data['udfValue']);

                break;
            case FieldInterface::DATE_CODE:
                $extraFieldValueEntity->setUdvDate($this->dateAdapter->adaptFromRequest($data['udfValue'] ?: ['date' => '']));

                break;
            case FieldInterface::NUMBER_CODE:
                $extraFieldValueEntity->setUdvNumber($data['udfValue']);

                break;
            case FieldInterface::MONEY_CODE:
                $extraFieldValueEntity->setUdvMoney($data['udfValue']);

                break;
            case FieldInterface::TEXT_CODE:
                $extraFieldValueEntity->setUdvText($data['udfValue']);

                break;
            default:
                throw new Exception('Invalid data type for extra field');
        }

        return $extraFieldValueEntity;
    }
}
