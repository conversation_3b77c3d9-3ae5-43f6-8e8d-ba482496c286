<?php

namespace app\models\extraFields\hydrators;

use app\framework\DoctrineEntityManagerFactory;

use app\models\extraFields\entities\ExtraFieldEntity;
use app\services\carlton\generic\DateAdapter;
use Exception;

/**
 * @codeCoverageIgnore
 */
class ExtraFieldValueHydratorFactory
{
    /**
     * @throws Exception
     */
    public function create($fieldId, $module): ExtraFieldValueHydrator
    {
        $entityManager = (new DoctrineEntityManagerFactory())->getInstance();

        $extraFieldRepository = $entityManager->getRepository(ExtraFieldEntity::class);
        $extraField = $extraFieldRepository->find($fieldId);

        if (!empty($extraField)) {
            return new ExtraFieldValueHydrator($extraField, new DateAdapter(), $module);
        }

        throw new Exception('Extra field not found');
    }
}
