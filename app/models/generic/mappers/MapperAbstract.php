<?php

namespace app\models\generic\mappers;

use app\models\generic\entities\EntityInterface;
use app\models\generic\exceptions\RecordAlreadyExistsException;
use app\services\audit\AuditInterface;
use app\services\audit\FullAudit;
use Doctrine\DBAL\Connection;
use Exception;

use function count;

abstract class MapperAbstract
{
    /** @var Connection */
    protected $db;

    /** @var AuditInterface */
    protected $audit;

    public function __construct(Connection $db, AuditInterface $audit)
    {
        $this->db = $db;
        $this->audit = $audit;
    }

    public function insert(EntityInterface $entity)
    {
        if ($this->exists($entity)) {
            throw new RecordAlreadyExistsException('Record already exists');
        }

        $data = $this->map($entity);
        $fields = array_keys($data);
        $values = array_values($data);

        $sql = 'INSERT INTO ' . $this->getTable() . ' ([' . implode('],[', $fields) . ']) '
             . 'VALUES (' . trim(str_repeat('?,', count($data)), ',') . ')';

        $this->db->prepare($sql)->executeStatement($values);

        if ($data['inc_mob_anonymous'] == 'Yes') {
            $data['createdby'] = '';
        }

        $this->logAudit($entity, FullAudit::SOURCE_API_INSERT, $data);
    }

    public function update(EntityInterface $entity)
    {
        if (!$this->exists($entity)) {
            throw new Exception('Record not found');
        }

        $newData = $this->map($entity);

        // fetch existing record before update (for audit)
        $stmt = $this->db->prepare('SELECT * FROM ' . $this->getTable() . ' WHERE ' . $this->getWhereConditionForPrimaryKey());
        $oldData = $stmt->executeQuery($entity->getId())->fetchAssociative();

        // update
        $values = array_merge(
            array_values($this->map($entity)),
            $entity->getId(),
        );

        $sql = 'UPDATE ' . $this->getTable()
            . ' SET [' . implode('] = ?, [', array_keys($newData)) . '] = ? '
            . ' WHERE ' . $this->getWhereConditionForPrimaryKey();

        $this->db->prepare($sql)->executeStatement($values);

        $this->logAudit($entity, FullAudit::SOURCE_API_UPDATE, $newData, $oldData ?: []);
    }

    public function delete(EntityInterface $entity)
    {
        if (!$this->exists($entity)) {
            throw new Exception("Record doesn't exist");
        }

        $sql = 'DELETE FROM ' . $this->getTable() . ' WHERE ' . $this->getWhereConditionForPrimaryKey();

        $this->db->prepare($sql)->executeStatement($entity->getId());

        $this->logAudit($entity, FullAudit::SOURCE_API_DELETE, $this->map($entity));
    }

    /**
     * @return array
     */
    public function get(array $id)
    {
        $row = $this->db->prepare('SELECT * FROM ' . $this->getTable() . ' WHERE ' . $this->getWhereConditionForPrimaryKey())
            ->executeQuery($id)
            ->fetchAssociative();

        if ($row === false) {
            $row = [];
        }

        $result = [];
        foreach ($row as $field => $value) {
            // FIXME: we should fix the database column names instead of doing this
            $result[strtolower($field)] = $value;
        }

        return $result ?: [];
    }

    /**
     * @return string
     */
    abstract public function getTable();

    /**
     * @return array
     */
    abstract public function getPrimaryKey();

    protected function logAudit(EntityInterface $entity, $source, array $newData, array $oldData = [])
    {
        $id = implode(', ', $entity->getId());
        $this->audit->add($this->getTable(), $id, $source, $newData, $oldData);
    }

    /**
     * @return string
     */
    protected function getWhereConditionForPrimaryKey()
    {
        $conditions = [];

        foreach ($this->getPrimaryKey() as $index => $columnName) {
            $conditions[] = "[{$columnName}] = ?";
        }

        return implode(' AND ', $conditions);
    }

    /**
     * @param array $fields
     *
     * @return string
     */
    protected function getWhereConditionForFields($fields)
    {
        $conditions = [];

        foreach ($fields as $index => $columnName) {
            $conditions[] = "[{$columnName}] = ?";
        }

        return implode(' AND ', $conditions);
    }

    /**
     * @return bool
     *
     * @throws \Doctrine\DBAL\Exception
     */
    protected function exists(EntityInterface $entity)
    {
        $data = $this->db->prepare('SELECT * FROM ' . $this->getTable() . ' WHERE ' . $this->getWhereConditionForPrimaryKey())
            ->executeQuery($entity->getId())
            ->fetchAssociative();

        return !empty($data);
    }
}
