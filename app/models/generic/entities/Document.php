<?php

namespace app\models\generic\entities;

use app\models\generic\valueObjects\Base64Content;
use app\models\generic\valueObjects\ForeignKeyID;
use app\models\generic\valueObjects\RecordID;

class Document implements EntityInterface
{
    protected $id;
    protected $claimId;
    protected $feedbackId;
    protected $incidentId;
    protected $documentId;
    protected $body;
    protected $type;
    protected $extension;
    protected $initials;
    protected $description;
    private $documentTitleDescription;

    public function __construct(
        RecordID $id,
        ForeignKeyID $claimId,
        ForeignKeyID $feedbackId,
        ForeignKeyID $incidentId,
        ForeignKeyID $documentId,
        Base64Content $body,
        $type,
        $extension,
        $initials,
        $description,
        $documentTitleDescription
    ) {
        $this->id = $id;
        $this->claimId = $claimId;
        $this->feedbackId = $feedbackId;
        $this->incidentId = $incidentId;
        $this->documentId = $documentId;
        $this->body = $body;
        $this->type = $type;
        $this->extension = $extension;
        $this->initials = $initials;
        $this->description = $description;
        $this->documentTitleDescription = $documentTitleDescription;
    }

    public function getId(): array
    {
        return [$this->id];
    }

    public function toArray()
    {
        return [
            'id' => $this->id->getId(),
            'claimId' => (string) $this->claimId,
            'feedbackId' => (string) $this->feedbackId,
            'incidentId' => (string) $this->incidentId,
            'documentId' => (string) $this->documentId,
            'body' => (string) $this->body,
            'type' => (string) $this->type,
            'extension' => (string) $this->extension,
            'initials' => (string) $this->initials,
            'description' => (string) $this->description,
        ];
    }

    public function map()
    {
        return [
            'recordid' => $this->id->getId(),
            'cla_id' => $this->claimId->getId(),
            'com_id' => $this->feedbackId->getId(),
            'inc_id' => $this->incidentId->getId(),
            'doc_id' => $this->documentId->getId(),
            'body' => (string) $this->body,
            'doc_type' => (string) $this->type,
            'doc_extension' => (string) $this->extension,
            'doc_createdby' => (string) $this->initials,
            'doc_dcreated' => date('Y-m-d H:m:s.000'),
            'doc_notes' => $this->documentTitleDescription,
        ];
    }

    public function getBody()
    {
        return $this->body;
    }

    public function getDocumentId()
    {
        return $this->documentId;
    }

    public function getExtension()
    {
        return $this->extension;
    }

    public function getDescriptionFieldText()
    {
        return $this->description;
    }

    public function getTitleDescription()
    {
        return $this->documentTitleDescription;
    }
}
