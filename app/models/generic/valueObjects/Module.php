<?php

namespace app\models\generic\valueObjects;

use InvalidArgumentException;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

class Module
{
    public const CLAIMS = 'CLA';
    public const FEEDBACK = 'COM';
    public const INCIDENTS = 'INC';
    public const CONTACTS = 'CON';
    public const ACTIONS = 'ACT';
    public const ADMIN = 'ADM';
    public const EQUIPMENT = 'AST';
    public const DASHBOARDS = 'DAS';
    public const MEDICATIONS = 'MED';
    public const PAYMENTS = 'PAY';
    public const INSURANCE = 'POL';
    public const ORGANISATIONS = 'ORG';
    public const DELEGATIONS = 'DEL';
    public const MORTALITY_REVIEW = 'MOR';
    public const USERS = 'USE';
    public const PROMPTS = 'PRO';
    public const REPORTS = 'REP';
    public const TEMPLATES = 'TEM';
    public const REDRESS = 'RED';
    public const SAFEGUARDING = 'SFG';
    public const ASSETS = 'AST';
    public const POLICIES = 'POL';
    public const CASUALFACTOR = 'CAF';
    public const ICONWALL = 'ICO';
    public const SYSTEMADMIN = 'SYS';
    public const TAGS = 'TAG';
    public const DIF1 = 'DIF1';
    public const IDS = [
        self::INCIDENTS => 3,
        self::FEEDBACK => 2,
        self::CLAIMS => 1,
        self::PAYMENTS => 37,
        self::INSURANCE => 45,
        self::EQUIPMENT => 24,
        self::CONTACTS => 4,
        self::ACTIONS => 7,
        self::PROMPTS => null,
        self::MEDICATIONS => 31,
        self::DASHBOARDS => 30,
        self::MORTALITY_REVIEW => 48,
        self::ORGANISATIONS => 46,
        self::USERS => 49,
        self::ADMIN => 10,
        self::DELEGATIONS => 47,
        self::REPORTS => null,
        self::TEMPLATES => null,
        self::REDRESS => 50,
        self::SAFEGUARDING => 51,
        self::ICONWALL => 52,
        self::SYSTEMADMIN => null,
    ];
    public const DOMAINS = [
        self::INCIDENTS => 'INCIDENTS',
        self::FEEDBACK => 'FEEDBACK',
        self::CLAIMS => 'CLAIMS',
        self::CONTACTS => 'CONTACTS',
        self::MORTALITY_REVIEW => 'MORTALITY_REVIEW',
        self::REDRESS => 'REDRESS',
        self::SAFEGUARDING => 'SAFEGUARDING',
    ];
    protected $code;

    public function __construct(string $code)
    {
        $this->code = $code;

        if (!$this->getPluralName()) {
            throw new InvalidArgumentException("{$code} is not a valid module code");
        }
    }

    /**
     * Get descriptive name for module.
     *
     * @return string
     */
    public function __toString()
    {
        return (string) $this->getCode();
    }

    /**
     * Get descriptive name for module.
     *
     * @return string
     */
    public function getPluralName()
    {
        $names = [
            self::INCIDENTS => _fdtk('mod_incidents_title'),
            self::FEEDBACK => _fdtk('mod_feedback_title'),
            self::CLAIMS => _fdtk('mod_claims_title'),
            self::PAYMENTS => _fdtk('mod_payments_title'),
            self::INSURANCE => _fdtk('mod_policies_title'),
            self::EQUIPMENT => _fdtk('mod_assets_title'),
            self::CONTACTS => _fdtk('mod_contacts_title'),
            self::ACTIONS => _fdtk('mod_actions_title'),
            self::PROMPTS => _fdtk('mod_prompt_title'),
            self::MEDICATIONS => _fdtk('mod_medications_title'),
            self::DASHBOARDS => _fdtk('mod_dashboard_title'),
            self::MORTALITY_REVIEW => _fdtk('mod_mortality_title'),
            self::ORGANISATIONS => _fdtk('mod_organisations_title'),
            self::USERS => _fdtk('mod_users_title'),
            self::DELEGATIONS => _fdtk('delegation_rules'),
            self::REPORTS => _fdtk('reports'),
            self::TEMPLATES => _fdtk('mod_templates_title'),
            self::ADMIN => _fdtk('mod_capture_admin_title'),
            self::REDRESS => _fdtk('mod_redress_title'),
            self::SAFEGUARDING => _fdtk('mod_safeguarding_title'),
            self::ICONWALL => _fdtk('mod_icon_wall_title'),
        ];

        return $names[$this->code];
    }

    /**
     * Get descriptive name for module.
     *
     * @return string
     */
    public function getAddNewString()
    {
        $strings = [
            self::USERS => _fdtk('add_new_user'),
            self::ORGANISATIONS => _fdtk('add_new_organisation'),
            self::INCIDENTS => _fdtk('add_new_incident'),
            self::EQUIPMENT => _fdtk('add_new_asset'),
            self::FEEDBACK => _fdtk('add_new_feedback'),
            self::CLAIMS => _fdtk('add_new_claim'),
            self::MEDICATIONS => _fdtk('add_new_medication'),
            self::PAYMENTS => _fdtk('add_new_payment'),
            self::INSURANCE => _fdtk('add_new_policy'),
            self::CONTACTS => _fdtk('add_new_contact'),
            self::REDRESS => _fdtk('add_new_redress'),
            self::SAFEGUARDING => _fdtk('add_new_safeguarding'),
        ];

        if (isset($strings[$this->code])) {
            return $strings[$this->code];
        }

        $moduleDef = Container::get(ModuleDefs::class)->getModuleData($this->code);

        return _fdtk('add_a_new') . ' ' . $moduleDef['REC_NAME'];
    }

    /**
     * Get descriptive name for module.
     *
     * @return string
     */
    public function getSingularName()
    {
        $names = [
            self::INCIDENTS => _fdtk('INCName'),
            self::FEEDBACK => _fdtk('COMName'),
            self::CLAIMS => _fdtk('CLAName'),
            self::PAYMENTS => _fdtk('PAYName'),
            self::INSURANCE => _fdtk('POLName'),
            self::EQUIPMENT => _fdtk('ASTName'),
            self::CONTACTS => _fdtk('CONName'),
            self::ACTIONS => _fdtk('ACTName'),
            self::PROMPTS => _fdtk('PROName'),
            self::MEDICATIONS => _fdtk('MEDName'),
            self::DASHBOARDS => _fdtk('DASName'),
            self::MORTALITY_REVIEW => _fdtk('MORName'),
            self::ORGANISATIONS => _fdtk('ORGName'),
            self::USERS => _fdtk('USEName'),
            self::DELEGATIONS => _fdtk('DELName'),
            self::REPORTS => _fdtk('REPName'),
            self::TEMPLATES => _fdtk('TEMName'),
            self::ADMIN => _fdtk('ADMName'),
            self::REDRESS => _fdtk('REDName'),
            self::SAFEGUARDING => _fdtk('SFGName'),
        ];

        return $names[$this->code];
    }

    /**
     * Get module code.
     *
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * Get the id of the module.
     *
     * @return int
     */
    public function getId(): ?int
    {
        return self::IDS[$this->code] ?? null;
    }

    public function getPrinceModuleCarltonString(): string
    {
        $names = [
            self::INCIDENTS => 'Incidents',
            self::FEEDBACK => 'Feedback',
            self::CLAIMS => 'Claims',
            self::PAYMENTS => null,
            self::INSURANCE => null,
            self::EQUIPMENT => null,
            self::CONTACTS => null,
            self::ACTIONS => null,
            self::PROMPTS => null,
            self::MEDICATIONS => null,
            self::DASHBOARDS => 'Dashboards',
            self::MORTALITY_REVIEW => 'Mortalities',
            self::ORGANISATIONS => null,
            self::USERS => null,
            self::ADMIN => null,
            self::ICONWALL => null,
            self::DELEGATIONS => null,
            self::REPORTS => null,
            self::TEMPLATES => null,
            self::REDRESS => 'Redress',
            self::SAFEGUARDING => 'Safeguarding',
        ];

        return $names[$this->code];
    }
}
