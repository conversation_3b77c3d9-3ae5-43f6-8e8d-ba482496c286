<?php

namespace app\models\generic\valueObjects;

use Exception;

class RecordID
{
    protected $recordID;

    /**
     * @param int $recordID
     */
    public function __construct($recordID)
    {
        $this->validate($recordID);

        $this->recordID = (int) $recordID;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->recordID;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->recordID;
    }

    protected function validate($recordID)
    {
        if (!$recordID) {
            throw new Exception('Record ID has not been set', 406);
        }

        if (!is_numeric($recordID)) {
            throw new Exception('Record ID needs to be an Integer', 406);
        }

        if ($recordID <= 0) {
            throw new Exception('Record ID can not be a negative integer or equal to zero', 406);
        }
    }
}
