<?php

namespace app\models\generic\valueObjects;

use InvalidArgumentException;

use function is_scalar;

class ForeignKeyCode
{
    protected $code;

    /**
     * @param string $code
     *
     * @throws InvalidArgumentException
     */
    public function __construct($code)
    {
        if (($code !== null && !is_scalar($code)) || $code === '') {
            throw new InvalidArgumentException('Code needs to be an scalar or null', 406);
        }

        $this->code = $code;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->code === null ? '' : (string) $this->code;
    }

    /**
     * @return int|string|null
     */
    public function getCode()
    {
        return ctype_digit((string) $this->code) ? (int) $this->code : $this->code;
    }
}
