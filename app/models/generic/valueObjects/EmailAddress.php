<?php

namespace app\models\generic\valueObjects;

class EmailAddress implements EmailAddressInterface
{
    protected $email;

    /**
     * @param string $email
     */
    public function __construct($email)
    {
        $this->email = $email;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->email;
    }

    public function getEmail()
    {
        return $this->email;
    }
}
