<?php

namespace app\models\generic\valueObjects;

use Exception;

class PhoneNumber
{
    protected $number;

    /**
     * @param string $number
     */
    public function __construct($number)
    {
        $this->validate($number);

        $this->number = $number;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->number;
    }

    public function getNumber()
    {
        return $this->number;
    }

    protected function validate($number)
    {
        $this->validateLength($number, 64);
    }

    protected function validateLength($value, $length)
    {
        if (mb_strlen($value) > $length) {
            throw new Exception($value . ' length exceeds the maximum allowed length of ' . $length, 406);
        }
    }
}
