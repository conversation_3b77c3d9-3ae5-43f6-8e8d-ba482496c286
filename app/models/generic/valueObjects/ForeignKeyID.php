<?php

namespace app\models\generic\valueObjects;

use Exception;

class ForeignKeyID
{
    protected $id;

    /**
     * @param $id
     *
     * @throws Exception
     */
    public function __construct($id)
    {
        if ($id !== null && $id <= 0) {
            throw new Exception('ID can be null but not a negative integer or equal to zero', 406);
        }

        if (!is_numeric($id) && $id !== null) {
            throw new Exception('Record ID needs to be an Integer if not null', 406);
        }

        $this->id = $id === null ? null : (int) $id;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->id === null ? '' : (string) $this->id;
    }

    /**
     * @return int|string|null
     */
    public function getId()
    {
        return $this->id;
    }
}
