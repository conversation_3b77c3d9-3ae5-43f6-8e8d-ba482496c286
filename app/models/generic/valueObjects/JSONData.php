<?php

namespace app\models\generic\valueObjects;

use Exception;

class JSONData
{
    private $data;

    public function __construct($data)
    {
        $object = json_decode($data);

        if ($object === null) {
            throw new Exception('Invalid json data provided');
        }

        $this->data = $data;
    }

    public function __toString()
    {
        return $this->data === null ? '' : (string) $this->data;
    }

    /**
     * @return array
     */
    public function toArray()
    {
        return json_decode($this->data, true);
    }

    /**
     * @param $data
     *
     * @return $this
     *
     * @throws Exception
     */
    public function resetDataTo($data)
    {
        $this->__construct($data);

        return $this;
    }
}
