<?php

namespace app\models\generic\valueObjects;

use DateTime;
use Exception;
use InvalidArgumentException;

class DateTimeString
{
    /**
     * The date time string.
     *
     * @var string
     */
    private $datetime;

    public function __construct(?string $datetime)
    {
        $datetime = ($datetime === null || $datetime === '') ? null : str_replace('.000', '', $datetime);
        $this->validate($datetime);
        $this->datetime = $datetime;
    }

    public function __toString()
    {
        return $this->datetime === null ? '' : (string) $this->datetime;
    }

    public function validate($datetime)
    {
        if ($datetime !== null) {
            // check format
            if (!preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $datetime)) {
                throw new InvalidArgumentException("Datetime should be formatted yyyy-mm-dd hh:mm:ss or NULL {$datetime}!");
            }

            // catch parse errors (ie: "1977-08-45 10:10:10")
            try {
                $datetimeObject = new DateTime($datetime);
            } catch (Exception $e) {
                throw new InvalidArgumentException('Unparseable date. Should be formatted yyyy-mm-dd hh:mm:ss or NULL');
            }

            // validate date
            if ($datetimeObject->format('Y-m-d H:i:s') != $datetime) {
                throw new InvalidArgumentException("Datetime should contain a valid date {$datetime} " . $datetimeObject->format('Y-m-d'));
            }
        }
    }

    public function getValue()
    {
        return $this->datetime;
    }

    public function getTime()
    {
        return $this->datetime === null ? null : date('H:i', strtotime($this->datetime));
    }

    /**
     * @param $hours
     * @param $minutes
     * @param $seconds
     *
     * @throws Exception
     */
    public function setTime($hours = '', $minutes = '', $seconds = '')
    {
        $time = ($hours ?: '00') . ':' . ($minutes ?: '00') . ':' . ($seconds ?: '00');

        if (!preg_match("/^([01]?\d|2[0-3]):[0-5]?\d:[0-5]?\d$/", $time)) {
            throw new Exception('Invalid time provided');
        }

        $this->datetime = substr($this->datetime, 0, -8) . $time;
    }
}
