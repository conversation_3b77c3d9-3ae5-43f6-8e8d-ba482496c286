<?php

namespace app\models\generic\valueObjects;

use InvalidArgumentException;

/** @codeCoverageIgnore */
class PostCode
{
    public const MAX_LENGTH = 10;
    protected $postcode;

    /**
     * @param string $postcode
     *
     * @throws InvalidArgumentException
     */
    public function __construct($postcode)
    {
        if (mb_strlen($postcode ?? '') > $this::MAX_LENGTH) {
            throw new InvalidArgumentException($postcode . ' length exceeds the maximum allowed length of ' . $this::MAX_LENGTH, 406);
        }

        $this->postcode = $postcode;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->postcode;
    }
}
