<?php

namespace app\models\generic\valueObjects;

use InvalidArgumentException;

/** @codeCoverageIgnore */
class SingleLineAddress
{
    public const MAX_LENGTH = 254;
    protected $address;

    /**
     * @param string $address
     *
     * @throws InvalidArgumentException
     */
    public function __construct($address)
    {
        if (mb_strlen($address) > static::MAX_LENGTH) {
            throw new InvalidArgumentException($address . ' length exceeds the maximum allowed length of ' . static::MAX_LENGTH, 406);
        }

        $this->address = $address;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->address;
    }
}
