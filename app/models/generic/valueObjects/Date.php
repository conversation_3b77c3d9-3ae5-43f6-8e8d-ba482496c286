<?php

namespace app\models\generic\valueObjects;

use DateTime;
use Exception;
use InvalidArgumentException;

class Date
{
    private $date;

    public function __construct($date)
    {
        $date = $date === null ? null : str_replace(' 00:00:00.000', '', $date);

        if ($date !== null) {
            // check format
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
                throw new InvalidArgumentException("Date should be formatted yyyy-mm-dd or NULL {$date}!");
            }

            // catch parse errors (ie: "1977-08-45")
            try {
                $datetime = new DateTime($date);
            } catch (Exception $e) {
                throw new InvalidArgumentException('Unparseable date. Should be formatted yyyy-mm-dd or NULL');
            }

            // validate date
            if ($datetime->format('Y-m-d') != $date) {
                throw new InvalidArgumentException("Date should contain a valid date {$date} " . $datetime->format('Y-m-d'));
            }
        }

        $this->date = $date;
    }

    public function __toString()
    {
        return $this->date === null ? '' : (string) $this->date;
    }

    public function getDate()
    {
        return $this->date;
    }
}
