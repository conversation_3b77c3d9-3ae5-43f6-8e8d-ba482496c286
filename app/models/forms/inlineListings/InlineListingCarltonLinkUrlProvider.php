<?php

namespace app\models\forms\inlineListings;

use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use InvalidArgumentException;

use function array_key_exists;

class InlineListingCarltonLinkUrlProvider implements InlineListingLinkUrlProviderInterface
{
    /** @var DatixConfig */
    private $config;

    /** @var string */
    private $module;

    /** @var string */
    private $currentURL;

    /**
     * @param string $module Carlton module name
     * @param string $currentURL
     */
    public function __construct($module, DatixConfig $config, $currentURL)
    {
        $this->config = $config;
        $this->module = $module;
        $this->currentURL = $currentURL;
    }

    /**
     * @param int $mainRecordId
     * @param $mainModule
     */
    public function getUrlToCreateForm($mainRecordId, $mainModule, array $extraParams = []): string
    {
        $baseUrl = $this->config->getCarltonBaseUrl();

        $module = $this->getMappedCarltonModule($this->module);
        $recordName = $this->getMappedCarltonRecordName($this->module);

        $carltonAction = (!empty($extraParams['isActionPlan'])) ? 'action-plan' : "new-{$recordName}";

        $url = $baseUrl . "{$module}/third-party/{$carltonAction}?remoteId={$mainRecordId}&remoteModule={$mainModule}";

        if (!empty($extraParams)) {
            $url .= '&' . http_build_query($extraParams);
        }

        return $url;
    }

    /**
     * @param int $recordId
     * @param int $mainRecordId
     * @param $mainModule
     *
     * @return string url
     */
    public function getUrlToEditForm($recordId, $mainRecordId, $mainModule, array $extraParams = [])
    {
        $baseUrl = $this->config->getCarltonBaseUrl();

        $module = $this->getMappedCarltonModule($this->module);

        $url = $baseUrl . "{$module}/{$recordId}?remoteId={$mainRecordId}&remoteModule={$mainModule}";

        if (!empty($extraParams)) {
            $url .= '&' . http_build_query($extraParams);
        }

        return $url;
    }

    public function getContactUrlToEditFrom($recordId): string
    {
        $module = $this->getMappedCarltonModule(Module::CONTACTS);
        $baseUrl = $this->config->getCarltonBaseUrl();

        return $baseUrl . "{$module}/{$recordId}/edit";
    }

    /**
     * @param string $module 3 letter code prince module
     *
     * @return string carlton module
     */
    protected function getMappedCarltonModule($module): string
    {
        $carltonModuleMapping = [
            'ACT' => 'actions',
            'LEA' => 'safety-learnings',
            Module::CONTACTS => 'contacts',
        ];

        if (!array_key_exists($module, $carltonModuleMapping)) {
            throw new InvalidArgumentException("Invalid carlton module ({$module})");
        }

        return $carltonModuleMapping[$module];
    }

    /**
     * @param string $module 3 letter code prince module
     *
     * @return string carlton module
     */
    protected function getMappedCarltonRecordName($module)
    {
        $carltonModuleMapping = [
            'ACT' => 'action',
        ];

        if (!array_key_exists($module, $carltonModuleMapping)) {
            throw new InvalidArgumentException("Invalid carlton module ({$module})");
        }

        return $carltonModuleMapping[$module];
    }
}
