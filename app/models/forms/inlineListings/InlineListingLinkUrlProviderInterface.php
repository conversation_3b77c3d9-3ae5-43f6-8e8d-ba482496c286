<?php

namespace app\models\forms\inlineListings;

interface InlineListingLinkUrlProviderInterface
{
    /**
     * @param $mainRecordId
     * @param $mainModule 3 letter code
     *
     * @return string url
     */
    public function getUrlToCreateForm($mainRecordId, $mainModule, array $extraParams = []);

    /**
     * @param int $recordId
     * @param $mainRecordId
     * @param $mainModule 3 letter code
     *
     * @return string url
     */
    public function getUrlToEditForm($recordId, $mainRecordId, $mainModule, array $extraParams = []);
}
