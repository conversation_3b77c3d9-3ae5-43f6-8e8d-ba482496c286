<?php

namespace app\models\forms\inlineListings;

use app\models\framework\config\DatixConfigFactory;

use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

use function in_array;

/**
 * @codeCoverageIgnore
 */
class InlineListingLinkUrlProviderFactory
{
    /**
     * @param string $module 3 letter code
     *
     * @return InlineListingLinkUrlProviderInterface
     */
    public function createFromModule($module)
    {
        $modulesEditableOnPrince = [
            'INC', 'COM', 'CLA', 'MOR', 'PAY', 'POL', 'ORG', 'ADM', 'USE',
        ];

        if (in_array($module, $modulesEditableOnPrince)) {
            $moduleDefs = Container::get(ModuleDefs::class);

            return new InlineListingPrinceLinkUrlProvider($module, $moduleDefs);
        }

        $config = (new DatixConfigFactory())->getInstance();
        $currentUrl = rtrim(getenv('BASE_URL'), '/') . $_SERVER['REQUEST_URI'];

        return new InlineListingCarltonLinkUrlProvider($module, $config, $currentUrl);
    }
}
