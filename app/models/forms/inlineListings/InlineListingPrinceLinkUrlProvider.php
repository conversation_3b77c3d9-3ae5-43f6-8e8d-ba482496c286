<?php

namespace app\models\forms\inlineListings;

use src\system\moduledefs\ModuleDefs;

class InlineListingPrinceLinkUrlProvider implements InlineListingLinkUrlProviderInterface
{
    /** @var ModuleDefs */
    protected $moduleDefs;

    /** @var string */
    private $module;

    public function __construct($module, ModuleDefs $moduleDefs)
    {
        $this->moduleDefs = $moduleDefs;
        $this->module = $module;
    }

    /**
     * @param int $mainRecordId
     * @param $mainModule
     *
     * @return string
     */
    public function getUrlToCreateForm($mainRecordId, $mainModule, array $extraParams = [])
    {
        $action = $this->moduleDefs[$this->module]['ACTION'];

        $url = "?action={$action}&module={$mainModule}&act_cas_id={$mainRecordId}&frommainrecord=1";

        if (!empty($extraParams)) {
            $url .= '&' . http_build_query($extraParams);
        }

        return $url;
    }

    /**
     * @param int $recordId
     * @param int $mainRecordId
     * @param $mainModule
     *
     * @return string url
     */
    public function getUrlToEditForm($recordId, $mainRecordId, $mainModule, array $extraParams = [])
    {
        $action = $this->moduleDefs[$this->module]['ACTION'];
        $idFieldName = $this->moduleDefs[$this->module]['URL_RECORDID'] ?: 'recordid';

        $url = "?action={$action}&module={$mainModule}&{$idFieldName}={$recordId}&frommainrecord=1";

        if (!empty($extraParams)) {
            $url .= '&' . http_build_query($extraParams);
        }

        return $url;
    }
}
