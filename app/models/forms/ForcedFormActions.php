<?php

namespace app\models\forms;

use Forms_FormDesign;

use function defined;
use function constant;

final class ForcedFormActions
{
    private const INC_1 = [
        'anon_reporting' => [
            [
                'section' => 'contacts_type_R',
                'values' => ['N'],
            ],
        ],
    ];

    /**
     * @return array
     */
    public static function getSectionActionsForFormDesign(Forms_FormDesign $formDesign)
    {
        $constantName = $formDesign->Module . '_' . $formDesign->getLevel();

        if (defined($constantName)) {
            return constant('self::' . $constantName);
        }

        return [];
    }
}
