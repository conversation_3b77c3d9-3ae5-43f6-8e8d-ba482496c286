<?php

declare(strict_types=1);

namespace app\models\feedback\events;

use app\models\feedback\entities\FeedbackEntity;
use app\models\feedback\entities\FeedbackLinksEntity;
use app\models\feedback\entities\FeedbackSubjectEntity;
use app\services\globals\GlobalService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;

final class FeedbackEntityListener
{
    private GlobalService $globalService;

    public function __construct(GlobalService $globalService)
    {
        $this->globalService = $globalService;
    }

    public function prePersist(FeedbackEntity $feedback, LifecycleEventArgs $event): void
    {
        $this->updatePrimarySubject($feedback);
    }

    public function preUpdate(FeedbackEntity $feedback, PreUpdateEventArgs $event): void
    {
        $this->updatePrimarySubject($feedback);
    }

    public function postUpdate(FeedbackEntity $feedback, LifecycleEventArgs $event): void
    {
        $this->updateEmptyPrimaryFeedbackChainReceivedDates($feedback, $event->getEntityManager());
    }

    private function updatePrimarySubject(FeedbackEntity $feedback): void
    {
        $subjects = $feedback->getSubjects();

        if ($subjects->isEmpty()) {
            $primarySubject = new FeedbackSubjectEntity();
        } else {
            $primarySubject = $subjects->first();
        }

        $primarySubject->mapToFeedback($feedback);
    }

    private function updateEmptyPrimaryFeedbackChainReceivedDates(FeedbackEntity $feedback, EntityManagerInterface $em): void
    {
        $global = $this->globalService->getParm('COM_AUTO_DRECEIVED_LCOM_DRECEIVED', null, 'N');
        $feedbackDateReceived = $feedback->getComDreceived();

        if ($global->isFalse() || $feedbackDateReceived === null) {
            return;
        }

        /** @var FeedbackLinksEntity[] $feedbackChains */
        $feedbackChains = $feedback->getPrimaryContactFeedbackChains();
        foreach ($feedbackChains as $chain) {
            if ($chain->getLcomDreceived() === null) {
                $chain->setLcomDreceived($feedbackDateReceived);
                $em->persist($chain);
            }
        }

        $em->flush();
    }
}
