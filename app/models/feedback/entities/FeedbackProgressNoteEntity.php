<?php

declare(strict_types=1);

namespace app\models\feedback\entities;

use app\models\progressNote\entities\ProgressNoteEntity;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

#[Entity]
#[Table(name: "progress_notes")]
class FeedbackProgressNoteEntity extends ProgressNoteEntity
{
    #[ManyToOne(
        targetEntity: FeedbackEntity::class,
        inversedBy: "progressNotes",
        cascade: ["persist"]
    )]
    #[JoinColumn(
        name: "pno_link_id",
        referencedColumnName: "recordid"
    )]
    private FeedbackEntity $feedback;

    public function getFeedback(): FeedbackEntity
    {
        return $this->feedback;
    }

    public function setFeedback(FeedbackEntity $feedback): self
    {
        $this->feedback = $feedback;

        return $this;
    }
}
