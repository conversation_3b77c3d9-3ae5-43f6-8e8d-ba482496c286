<?php

namespace app\models\feedback\entities;

use app\models\email\EmailNotifiable;
use app\models\email\NewStaff;
use app\models\email\NewStaffNotifiable;
use app\models\email\ReporterProgressNotifiable;
use app\models\feedback\events\FeedbackEntityListener;
use app\models\feedback\hydrators\FeedbackHydratorFactory;
use app\models\generic\entities\HasUUIDEntity;
use app\models\generic\valueObjects\Module;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\Common\Collections\Expr\Comparison;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\EntityListeners;
use Doctrine\ORM\Mapping\HasLifecycleCallbacks;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\OrderBy;
use Doctrine\ORM\Mapping\Table;
use Exception;
use OpenApi\Annotations as OA;

use function array_key_exists;

/**
 * @codeCoverageIgnore
 *
 * @OA\Schema
 */
#[Entity]
#[Table(name: "compl_main")]
#[HasLifecycleCallbacks]
#[EntityListeners([FeedbackEntityListener::class])]
class FeedbackEntity implements HasUUIDEntity, EmailNotifiable, NewStaffNotifiable, ReporterProgressNotifiable
{
    use NewStaff;
    private const EMAIL_TEMPLATE_VARIABLE_MAP = [
        'dreceived' => 'com_dreceived',
        'detail' => 'com_detail',
        'mgr' => 'com_mgr',
        'id' => 'recordid',
        'head' => 'com_head',
        'method' => 'com_method',
        'subject1' => 'com_subject1',
        'subsubject1' => 'com_subsubject1',
        'type' => 'com_type',
        'name' => 'com_name',
        'dopened' => 'com_dopened',
        'ourref' => 'com_ourref',
        'grade' => 'com_grade',
    ];

    /**
     * @OA\Property(property="id", type="integer", format="int32")
     */
    #[Id]
    #[Column(type: "integer")]
    protected $recordid;

    /**
     * @OA\Property(property="name", type="string", maxLength=32, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_name;

    /**
     * @OA\Property(property="mgr", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_mgr;

    /**
     * @OA\Property(property="ourref", type="string", maxLength=32, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ourref;

    /**
     * @OA\Property(property="otherref", type="string", maxLength=32, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_otherref;

    /**
     * @OA\Property(property="pasno1", type="string", maxLength=20, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_pasno1;

    /**
     * @OA\Property(property="pasno2", type="string", maxLength=20, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_pasno2;

    /**
     * @OA\Property(property="pasno3", type="string", maxLength=20, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_pasno3;

    /**
     * @OA\Property(property="type", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_type;

    /**
     * @OA\Property(property="subtype", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_subtype;

    /**
     * @OA\Property(property="outcome", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_outcome;

    /**
     * @OA\Property(property="method", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_method;

    /**
     * @OA\Property(property="purchaser", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_purchaser;

    /**
     * @OA\Property(property="dincident", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dincident;

    /**
     * @OA\Property(property="dreceived", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dreceived;

    /**
     * @OA\Property(property="dclosed", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dclosed;

    /**
     * @OA\Property(property="dreopened", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dreopened;

    /**
     * @OA\Property(property="currentstage", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_curstage;

    /**
     * @OA\Property(property="head", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_head;

    /**
     * @OA\Property(property="drequest", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_drequest;

    /**
     * @OA\Property(property="ddueackreq", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ddueackreq;

    /**
     * @OA\Property(property="dackreq", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dackreq;

    /**
     * @OA\Property(property="dstatement", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dstatement;

    /**
     * @OA\Property(property="dduedecision", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dduedecision;

    /**
     * @OA\Property(property="ddecision", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ddecision;

    /**
     * @OA\Property(property="recir", type="string", maxLength=1, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_recir;

    /**
     * @OA\Property(property="dinform", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dinform;

    /**
     * @OA\Property(property="dduepappt", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dduepappt;

    /**
     * @OA\Property(property="dpappt", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dpappt;

    /**
     * @OA\Property(property="dduepdraft", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dduepdraft;

    /**
     * @OA\Property(property="dpdraft", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dpdraft;

    /**
     * @OA\Property(property="ddueppublish", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ddueppublish;

    /**
     * @OA\Property(property="dppublish", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dppublish;

    /**
     * @OA\Property(property="dduecereply", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dduecereply;

    /**
     * @OA\Property(property="dcereply", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dcereply;

    /**
     * @OA\Property(property="expenses", type="number", format="double", nullable=true)
     */
    #[Column(type: "decimal", precision: 13, scale: 2)]
    protected $com_expenses;

    /**
     * @OA\Property(property="koservarea", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_koservarea;

    /**
     * @OA\Property(property="kosubject", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_kosubject;

    /**
     * @OA\Property(property="koprof", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_koprof;

    /**
     * @OA\Property(property="subject1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_subject1;

    /**
     * @OA\Property(property="subsubject1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_subsubject1;

    /**
     * @OA\Property(property="location1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_location1;

    /**
     * @OA\Property(property="directorate1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_directorate1;

    /**
     * @OA\Property(property="stafftype1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_stafftype1;

    /**
     * @OA\Property(property="specialty1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_specialty1;

    /**
     * @OA\Property(property="seclevel", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $seclevel;

    /**
     * @OA\Property(property="secgroup", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $secgroup;

    /**
     * @OA\Property(property="dduelaychair", type="string", format="datetime", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dduelaychair;

    /**
     * @OA\Property(property="dlaychair", type="string", format="datetime", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dlaychair;

    /**
     * @OA\Property(property="ircode", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ircode;

    /**
     * @OA\Property(property="assessor", type="string", maxLength=1, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_assessor;

    /**
     * @OA\Property(property="koethnic_pat", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_koethnic_pat;

    /**
     * @OA\Property(property="koethnic_staff", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_koethnic_staff;

    /**
     * @OA\Property(property="consent", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_consent;

    /**
     * @OA\Property(property="outcome1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_outcome1;

    /**
     * @OA\Property(property="ko41_type", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ko41_type;

    /**
     * @OA\Property(property="investigator", type="string", maxLength=252, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_investigator;

    /**
     * @OA\Property(property="inv_dstart", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inv_dstart;

    /**
     * @OA\Property(property="inv_dcomp", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inv_dcomp;

    /**
     * @OA\Property(property="consequence", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_consequence;

    /**
     * @OA\Property(property="likelihood", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_likelihood;

    /**
     * @OA\Property(property="rating", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $com_rating;

    /**
     * @OA\Property(property="grade", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_grade;

    /**
     * @OA\Property(property="colour", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $com_colour;

    /**
     * @OA\Property(property="root_causes", type="integer", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_root_causes;

    /**
     * @OA\Property(property="inv_outcome", type="string", maxLength=248, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inv_outcome;

    /**
     * @OA\Property(property="recomm_code", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_recomm_code;

    /**
     * @OA\Property(property="action_code", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_action_code;

    /**
     * @OA\Property(property="lessons_code", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_lessons_code;

    /**
     * @OA\Property(property="unit_type", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_unit_type;

    /**
     * @OA\Property(property="inc_type", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inc_type;

    /**
     * @OA\Property(property="inc_category", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inc_category;

    /**
     * @OA\Property(property="inc_subcat", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inc_subcat;

    /**
     * @OA\Property(property="dopened", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dopened;

    /**
     * @OA\Property(property="ddueinform", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_ddueinform;

    /**
     * @OA\Property(property="weighting", type="number", format="double", nullable=true)
     */
    #[Column(type: "decimal", precision: 13, scale: 6)]
    protected $weighting;

    /**
     * @OA\Property(property="isd_unit", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_unit;

    /**
     * @OA\Property(property="isd_locactual", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_locactual;

    /**
     * @OA\Property(property="isd_consent", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_consent;

    /**
     * @OA\Property(property="isd_dconsent_req", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_dconsent_req;

    /**
     * @OA\Property(property="isd_dconsent_rec", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_dconsent_rec;

    /**
     * @OA\Property(property="isd_div_sent", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_div_sent;

    /**
     * @OA\Property(property="isd_ref_added", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_ref_added;

    /**
     * @OA\Property(property="isd_iaas_involved", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_iaas_involved;

    /**
     * @OA\Property(property="isd_cas_involved", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_cas_involved;

    /**
     * @OA\Property(property="isd_chi_no", type="string", maxLength=64, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_chi_no;

    /**
     * @OA\Property(property="isd_resp_sent_20", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_resp_sent_20;

    /**
     * @OA\Property(property="isd_resp_20_reason", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_resp_20_reason;

    /**
     * @OA\Property(property="isd_agree_40", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_agree_40;

    /**
     * @OA\Property(property="isd_agree_40_date", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_agree_40_date;

    /**
     * @OA\Property(property="isd_actions", type="string", maxLength=254, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_actions;

    /**
     * @OA\Property(property="isd_dexport", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_dexport;

    /**
     * @OA\Property(property="rep_approved", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $rep_approved;

    /**
     * @OA\Property(property="createdby", type="string", maxLength=20, nullable=true)
     */
    #[Column(type: "string")]
    protected $createdby;

    /**
     * @OA\Property(property="show_person", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $show_person;

    /**
     * @OA\Property(property="dcompleted1", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_dcompleted1;

    /**
     * @OA\Property(property="detail", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $COM_DETAIL;

    /**
     * @OA\Property(property="summary", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $COM_SUMMARY;

    /**
     * @OA\Property(property="reason", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $COM_REASON;

    /**
     * @OA\Property(property="report", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $COM_REPORT;

    /**
     * @OA\Property(property="irsynopsis", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $COM_IRSYNOPSIS;

    /**
     * @OA\Property(property="recommend", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_recommend;

    /**
     * @OA\Property(property="inv_action", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inv_action;

    /**
     * @OA\Property(property="inv_lessons", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_inv_lessons;

    /**
     * @OA\Property(property="isd_resp_40_reason", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_resp_40_reason;

    /**
     * @OA\Property(property="isd_plan", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_isd_plan;

    /**
     * @OA\Property(property="notes1", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_notes1;

    /**
     * @OA\Property(property="type_tier_one", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_type_tier_one;

    /**
     * @OA\Property(property="type_tier_two", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_type_tier_two;

    /**
     * @OA\Property(property="type_tier_three", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_type_tier_three;

    /**
     * @OA\Property(property="affecting_tier_zero", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_affecting_tier_zero;

    /**
     * @OA\Property(property="issues_linked", type="string", maxLength=1, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_issues_linked;

    /**
     * @OA\Property(property="subjects_linked", type="string", maxLength=1, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_subjects_linked;

    /**
     * @OA\Property(property="location_id", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $location_id;

    /**
     * @OA\Property(property="service_id", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $service_id;

    /**
     * @OA\Property(property="location_id1", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $location_id1;

    /**
     * @OA\Property(property="service_id1", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $service_id1;

    /**
     * @OA\Property(property="flag_for_investigation", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $flag_for_investigation;

    /**
     * @OA\Property(property="flag_for_rib", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $flag_for_rib;

    /**
     * @OA\Property(property="other_location", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $other_location;

    /**
     * @OA\Property(property="other_service", type="integer", format="int32", nullable=true)
     */
    #[Column(type: "integer")]
    protected $other_service;

    /**
     * @OA\Property(property="subtype1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_subtype1;

    /**
     * @OA\Property(property="type1", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_type1;

    /**
     * @OA\Property(property="key_learnings", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $key_learnings;

    /**
     * @OA\Property(property="learnings_to_share", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $learnings_to_share;

    /**
     * @OA\Property(property="learnings_title", type="string", maxLength=64, nullable=true)
     */
    #[Column(type: "string")]
    protected $learnings_title;

    /**
     * @OA\Property(property="requested_consultant", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $requested_consultant;

    /**
     * @OA\Property(property="priority_scale", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $priority_scale;

    /**
     * @OA\Property(property="is_record_sensitive", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $is_record_sensitive;

    /**
     * @OA\Property(property="com_lesson_learned_sub_category", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_lesson_learned_sub_category;

    /**
     * @OA\Property(property="com_hro_characteristics", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_hro_characteristics;

    /**
     * @OA\Property(property="com_specialty", type="string", maxLength=128, nullable=true)
     */
    #[Column(type: "string")]
    protected $com_specialty;

    /**
     * @OA\Property(property="mca_or_na", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $mca_or_na;

    /**
     * @OA\Property(property="source_of_record", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $source_of_record;

    /**
     * @OA\Property(property="patient_expectations", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_pat_expectations;

    /**
     * @OA\Property(property="patient_update_prefs", type="string", nullable=true)
     */
    #[Column(type: "string")]
    protected $com_pat_update_pref;

    #[Column(type: "string")]
    protected $referred_to_ombudsman;

    #[Column(type: "string")]
    protected $date_first_contact;

    #[Column(type: "string")]
    protected $date_evidence_due;

    #[Column(type: "string")]
    protected $date_evidence_submitted;

    #[Column(type: "string")]
    protected $ombudsman_reference;

    #[Column(type: "string")]
    protected $ombudsman_handler;

    #[Column(type: "string")]
    protected $ombudsman_current_stage;

    #[Column(type: "string")]
    protected $early_settlement_proposal;

    #[Column(type: "string")]
    protected $early_settlement_proposal_date_received;

    #[Column(type: "string")]
    protected $early_settlement_proposal_date_requested;

    #[Column(type: "string")]
    protected $early_settlement_proposal_date_submitted;

    #[Column(type: "string")]
    protected $date_investigation_began;

    #[Column(type: "string")]
    protected $date_response_due;

    #[Column(type: "string")]
    protected $date_doc_inv_sub;

    #[Column(type: "string")]
    protected $date_inv_sub;

    #[Column(type: "string")]
    protected $date_draft_received;

    #[Column(type: "string")]
    protected $date_draft_response_due;

    #[Column(type: "string")]
    protected $date_rec_recieved;

    #[Column(type: "string")]
    protected $date_action_plan;

    #[Column(type: "string")]
    protected $date_draft_report_sub;

    #[Column(type: "string")]
    protected $date_report_received;

    #[Column(type: "string")]
    protected $final_rep_type;

    #[Column(type: "string")]
    protected $ombudsman_outcome;

    #[Column(type: "string")]
    protected $ombudsman_learning;

    #[Column(type: "integer")]
    protected $com_grade_rating;

    #[Column(type: "string")]
    protected $com_outcome_grading;

    #[Column(type: "string")]
    protected $com_serious_incident;

    #[Column(type: "string")]
    protected $com_welsh_language;

    /**
     * @OA\Property(property="outbreak_impact", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $outbreak_impact;

    /**
     * @OA\Property(property="outbreak_type", type="string", maxLength=6, nullable=true)
     */
    #[Column(type: "string")]
    protected $outbreak_type;

    #[Column(type: "string")]
    protected ?string $com_redress_escalated = null;

    /**
     * @OA\Property(property="show_document", type="string", maxLength=1, nullable=true)
     */
    #[Column(type: "string")]
    protected $show_document;

    /**
     * @OA\Property(property="updateddate", type="string", format="date-time", nullable=true)
     */
    #[Column(type: "string")]
    protected $updateddate;

    /**
     * @OA\Property(property="updatedby", type="string", maxLength=20, nullable=true)
     */
    #[Column(type: "string")]
    protected $updatedby;

    /**
     * @var Collection<FeedbackLinksEntity>
     */
    #[OneToMany(
        targetEntity: FeedbackLinksEntity::class,
        mappedBy: "feedback",
        cascade: ["persist"],
    )]
    protected $feedbackChains;

    protected bool $closedDateHasChanged = false;

    #[Column(type: "string", name: "uuid", length: "255")] private ?string $uuid;

    /**
     * @var Collection<FeedbackSubjectEntity>
     */
    #[OneToMany(
        mappedBy: "feedback",
        targetEntity: FeedbackSubjectEntity::class,
        cascade: ["persist"],
        orphanRemoval: true
    )]
    #[JoinColumn(
        name: "recordid",
        referencedColumnName: "com_id"
    )]
    #[OrderBy([
        "listorder" => "ASC",
        "recordid" => "ASC"
    ])]
    private $subjects;

    /**
     * @var Collection<FeedbackProgressNoteEntity>
     */
    #[OneToMany(
        mappedBy: "feedback",
        targetEntity: FeedbackProgressNoteEntity::class,
        cascade: ["persist"],
        orphanRemoval: true
    )]
    #[JoinColumn(
        name: "recordid",
        referencedColumnName: "pno_link_id"
    )]
    private $progressNotes;

    public function __construct()
    {
        $this->subjects = new ArrayCollection();
        $this->progressNotes = new ArrayCollection();
        $this->feedbackChains = new ArrayCollection();
    }

    /**
     * @return mixed
     */
    public function getSourceOfRecord()
    {
        return $this->source_of_record;
    }

    /**
     * @param mixed $source_of_record
     *
     * @return $this
     */
    public function setSourceOfRecord($source_of_record): self
    {
        $this->source_of_record = $source_of_record;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param mixed $recordid
     *
     * @return $this
     */
    public function setRecordid($recordid): self
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComName()
    {
        return $this->com_name;
    }

    /**
     * @param mixed $com_name
     *
     * @return $this
     */
    public function setComName($com_name): self
    {
        $this->com_name = $com_name;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComMgr()
    {
        return $this->com_mgr;
    }

    /**
     * @param mixed $com_mgr
     *
     * @return $this
     */
    public function setComMgr($com_mgr): self
    {
        if ($com_mgr !== null && $com_mgr !== $this->com_mgr) {
            $this->newHandler = $com_mgr;
            $this->handlerChanged = true;
        }

        $this->com_mgr = $com_mgr;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComOurref()
    {
        return $this->com_ourref;
    }

    /**
     * @param mixed $com_ourref
     *
     * @return $this
     */
    public function setComOurref($com_ourref): self
    {
        $this->com_ourref = $com_ourref;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComOtherref()
    {
        return $this->com_otherref;
    }

    /**
     * @param mixed $com_otherref
     *
     * @return $this
     */
    public function setComOtherref($com_otherref): self
    {
        $this->com_otherref = $com_otherref;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComPasno1()
    {
        return $this->com_pasno1;
    }

    /**
     * @param mixed $com_pasno1
     *
     * @return $this
     */
    public function setComPasno1($com_pasno1): self
    {
        $this->com_pasno1 = $com_pasno1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComPasno2()
    {
        return $this->com_pasno2;
    }

    /**
     * @param mixed $com_pasno2
     *
     * @return $this
     */
    public function setComPasno2($com_pasno2): self
    {
        $this->com_pasno2 = $com_pasno2;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComPasno3()
    {
        return $this->com_pasno3;
    }

    /**
     * @param mixed $com_pasno3
     *
     * @return $this
     */
    public function setComPasno3($com_pasno3): self
    {
        $this->com_pasno3 = $com_pasno3;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComType()
    {
        return $this->com_type;
    }

    /**
     * @param mixed $com_type
     *
     * @return $this
     */
    public function setComType($com_type): self
    {
        $this->com_type = $com_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubtype()
    {
        return $this->com_subtype;
    }

    /**
     * @param mixed $com_subtype
     *
     * @return $this
     */
    public function setComSubtype($com_subtype): self
    {
        $this->com_subtype = $com_subtype;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComOutcome()
    {
        return $this->com_outcome;
    }

    /**
     * @param mixed $com_outcome
     *
     * @return $this
     */
    public function setComOutcome($com_outcome): self
    {
        $this->com_outcome = $com_outcome;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComMethod()
    {
        return $this->com_method;
    }

    /**
     * @param mixed $com_method
     *
     * @return $this
     */
    public function setComMethod($com_method): self
    {
        $this->com_method = $com_method;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComPurchaser()
    {
        return $this->com_purchaser;
    }

    /**
     * @param mixed $com_purchaser
     *
     * @return $this
     */
    public function setComPurchaser($com_purchaser): self
    {
        $this->com_purchaser = $com_purchaser;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDincident()
    {
        return $this->com_dincident;
    }

    /**
     * @param mixed $com_dincident
     *
     * @return $this
     */
    public function setComDincident($com_dincident): self
    {
        $this->com_dincident = $com_dincident;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDreceived()
    {
        return $this->com_dreceived;
    }

    /**
     * @param mixed $com_dreceived
     *
     * @return $this
     */
    public function setComDreceived($com_dreceived): self
    {
        $this->com_dreceived = $com_dreceived;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDclosed()
    {
        return $this->com_dclosed;
    }

    /**
     * @param mixed $com_dclosed
     *
     * @return $this
     */
    public function setComDclosed($com_dclosed): self
    {
        try {
            if (!empty($com_dclosed)) {
                if (empty($this->com_dclosed)) {
                    $this->closedDateHasChanged = true;
                } else {
                    $currentClosedDate = (new DateTime($this->com_dclosed))->format('Y-m-d');
                    $newClosedDate = (new DateTime($com_dclosed))->format('Y-m-d');

                    if ($currentClosedDate !== $newClosedDate) {
                        $this->closedDateHasChanged = true;
                    }
                }
            }
        } catch (Exception $e) {
            // It would be better if date properties were DateTimes (or even DateTimeImmutables!) already
            // and not strings but, well... Here we are.
        }

        $this->com_dclosed = $com_dclosed;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDreopened()
    {
        return $this->com_dreopened;
    }

    /**
     * @param mixed $com_dreopened
     *
     * @return $this
     */
    public function setComDreopened($com_dreopened): self
    {
        $this->com_dreopened = $com_dreopened;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComCurstage()
    {
        return $this->com_curstage;
    }

    /**
     * @param mixed $com_curstage
     *
     * @return $this
     */
    public function setComCurstage($com_curstage): self
    {
        $this->com_curstage = $com_curstage;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComHead()
    {
        return $this->com_head;
    }

    /**
     * @param mixed $com_head
     *
     * @return $this
     */
    public function setComHead($com_head): self
    {
        $this->com_head = $com_head;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDrequest()
    {
        return $this->com_drequest;
    }

    /**
     * @param mixed $com_drequest
     *
     * @return $this
     */
    public function setComDrequest($com_drequest): self
    {
        $this->com_drequest = $com_drequest;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDdueackreq()
    {
        return $this->com_ddueackreq;
    }

    /**
     * @param mixed $com_ddueackreq
     *
     * @return $this
     */
    public function setComDdueackreq($com_ddueackreq): self
    {
        $this->com_ddueackreq = $com_ddueackreq;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDackreq()
    {
        return $this->com_dackreq;
    }

    /**
     * @param mixed $com_dackreq
     *
     * @return $this
     */
    public function setComDackreq($com_dackreq): self
    {
        $this->com_dackreq = $com_dackreq;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDstatement()
    {
        return $this->com_dstatement;
    }

    /**
     * @param mixed $com_dstatement
     *
     * @return $this
     */
    public function setComDstatement($com_dstatement): self
    {
        $this->com_dstatement = $com_dstatement;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDduedecision()
    {
        return $this->com_dduedecision;
    }

    /**
     * @param mixed $com_dduedecision
     *
     * @return $this
     */
    public function setComDduedecision($com_dduedecision): self
    {
        $this->com_dduedecision = $com_dduedecision;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDdecision()
    {
        return $this->com_ddecision;
    }

    /**
     * @param mixed $com_ddecision
     *
     * @return $this
     */
    public function setComDdecision($com_ddecision): self
    {
        $this->com_ddecision = $com_ddecision;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComRecir()
    {
        return $this->com_recir;
    }

    /**
     * @param mixed $com_recir
     *
     * @return $this
     */
    public function setComRecir($com_recir): self
    {
        $this->com_recir = $com_recir;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDinform()
    {
        return $this->com_dinform;
    }

    /**
     * @param mixed $com_dinform
     *
     * @return $this
     */
    public function setComDinform($com_dinform): self
    {
        $this->com_dinform = $com_dinform;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDduepappt()
    {
        return $this->com_dduepappt;
    }

    /**
     * @param mixed $com_dduepappt
     *
     * @return $this
     */
    public function setComDduepappt($com_dduepappt): self
    {
        $this->com_dduepappt = $com_dduepappt;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDpappt()
    {
        return $this->com_dpappt;
    }

    /**
     * @param mixed $com_dpappt
     *
     * @return $this
     */
    public function setComDpappt($com_dpappt): self
    {
        $this->com_dpappt = $com_dpappt;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDduepdraft()
    {
        return $this->com_dduepdraft;
    }

    /**
     * @param mixed $com_dduepdraft
     *
     * @return $this
     */
    public function setComDduepdraft($com_dduepdraft): self
    {
        $this->com_dduepdraft = $com_dduepdraft;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDpdraft()
    {
        return $this->com_dpdraft;
    }

    /**
     * @param mixed $com_dpdraft
     *
     * @return $this
     */
    public function setComDpdraft($com_dpdraft): self
    {
        $this->com_dpdraft = $com_dpdraft;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDdueppublish()
    {
        return $this->com_ddueppublish;
    }

    /**
     * @param mixed $com_ddueppublish
     *
     * @return $this
     */
    public function setComDdueppublish($com_ddueppublish): self
    {
        $this->com_ddueppublish = $com_ddueppublish;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDppublish()
    {
        return $this->com_dppublish;
    }

    /**
     * @param mixed $com_dppublish
     *
     * @return $this
     */
    public function setComDppublish($com_dppublish): self
    {
        $this->com_dppublish = $com_dppublish;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDduecereply()
    {
        return $this->com_dduecereply;
    }

    /**
     * @param mixed $com_dduecereply
     *
     * @return $this
     */
    public function setComDduecereply($com_dduecereply): self
    {
        $this->com_dduecereply = $com_dduecereply;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDcereply()
    {
        return $this->com_dcereply;
    }

    /**
     * @param mixed $com_dcereply
     *
     * @return $this
     */
    public function setComDcereply($com_dcereply): self
    {
        $this->com_dcereply = $com_dcereply;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComExpenses()
    {
        return $this->com_expenses;
    }

    /**
     * @param mixed $com_expenses
     *
     * @return $this
     */
    public function setComExpenses($com_expenses): self
    {
        $this->com_expenses = $com_expenses;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKoservarea()
    {
        return $this->com_koservarea;
    }

    /**
     * @param mixed $com_koservarea
     *
     * @return $this
     */
    public function setComKoservarea($com_koservarea): self
    {
        $this->com_koservarea = $com_koservarea;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKosubject()
    {
        return $this->com_kosubject;
    }

    /**
     * @param mixed $com_kosubject
     *
     * @return $this
     */
    public function setComKosubject($com_kosubject): self
    {
        $this->com_kosubject = $com_kosubject;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKoprof()
    {
        return $this->com_koprof;
    }

    /**
     * @param mixed $com_koprof
     *
     * @return $this
     */
    public function setComKoprof($com_koprof): self
    {
        $this->com_koprof = $com_koprof;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubject1()
    {
        return $this->com_subject1;
    }

    /**
     * @param mixed $com_subject1
     *
     * @return $this
     */
    public function setComSubject1($com_subject1): self
    {
        $this->com_subject1 = $com_subject1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubsubject1()
    {
        return $this->com_subsubject1;
    }

    /**
     * @param mixed $com_subsubject1
     *
     * @return $this
     */
    public function setComSubsubject1($com_subsubject1): self
    {
        $this->com_subsubject1 = $com_subsubject1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComLocation1()
    {
        return $this->com_location1;
    }

    /**
     * @param mixed $com_location1
     *
     * @return $this
     */
    public function setComLocation1($com_location1): self
    {
        $this->com_location1 = $com_location1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDirectorate1()
    {
        return $this->com_directorate1;
    }

    /**
     * @param mixed $com_directorate1
     *
     * @return $this
     */
    public function setComDirectorate1($com_directorate1): self
    {
        $this->com_directorate1 = $com_directorate1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComStafftype1()
    {
        return $this->com_stafftype1;
    }

    /**
     * @param mixed $com_stafftype1
     *
     * @return $this
     */
    public function setComStafftype1($com_stafftype1): self
    {
        $this->com_stafftype1 = $com_stafftype1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSpecialty1()
    {
        return $this->com_specialty1;
    }

    /**
     * @param mixed $com_specialty1
     *
     * @return $this
     */
    public function setComSpecialty1($com_specialty1): self
    {
        $this->com_specialty1 = $com_specialty1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getSeclevel()
    {
        return $this->seclevel;
    }

    /**
     * @param mixed $seclevel
     *
     * @return $this
     */
    public function setSeclevel($seclevel): self
    {
        $this->seclevel = $seclevel;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getSecgroup()
    {
        return $this->secgroup;
    }

    /**
     * @param mixed $secgroup
     *
     * @return $this
     */
    public function setSecgroup($secgroup): self
    {
        $this->secgroup = $secgroup;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDduelaychair()
    {
        return $this->com_dduelaychair;
    }

    /**
     * @param mixed $com_dduelaychair
     *
     * @return $this
     */
    public function setComDduelaychair($com_dduelaychair): self
    {
        $this->com_dduelaychair = $com_dduelaychair;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDlaychair()
    {
        return $this->com_dlaychair;
    }

    /**
     * @param mixed $com_dlaychair
     *
     * @return $this
     */
    public function setComDlaychair($com_dlaychair): self
    {
        $this->com_dlaychair = $com_dlaychair;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIrcode()
    {
        return $this->com_ircode;
    }

    /**
     * @param mixed $com_ircode
     *
     * @return $this
     */
    public function setComIrcode($com_ircode): self
    {
        $this->com_ircode = $com_ircode;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComAssessor()
    {
        return $this->com_assessor;
    }

    /**
     * @param mixed $com_assessor
     *
     * @return $this
     */
    public function setComAssessor($com_assessor): self
    {
        $this->com_assessor = $com_assessor;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKoethnicPat()
    {
        return $this->com_koethnic_pat;
    }

    /**
     * @param mixed $com_koethnic_pat
     *
     * @return $this
     */
    public function setComKoethnicPat($com_koethnic_pat): self
    {
        $this->com_koethnic_pat = $com_koethnic_pat;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKoethnicStaff()
    {
        return $this->com_koethnic_staff;
    }

    /**
     * @param mixed $com_koethnic_staff
     *
     * @return $this
     */
    public function setComKoethnicStaff($com_koethnic_staff): self
    {
        $this->com_koethnic_staff = $com_koethnic_staff;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComConsent()
    {
        return $this->com_consent;
    }

    /**
     * @param mixed $com_consent
     *
     * @return $this
     */
    public function setComConsent($com_consent): self
    {
        $this->com_consent = $com_consent;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComOutcome1()
    {
        return $this->com_outcome1;
    }

    /**
     * @param mixed $com_outcome1
     *
     * @return $this
     */
    public function setComOutcome1($com_outcome1): self
    {
        $this->com_outcome1 = $com_outcome1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComKo41Type()
    {
        return $this->com_ko41_type;
    }

    /**
     * @param mixed $com_ko41_type
     *
     * @return $this
     */
    public function setComKo41Type($com_ko41_type): self
    {
        $this->com_ko41_type = $com_ko41_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvestigator()
    {
        return $this->com_investigator;
    }

    /**
     * @param mixed $com_investigator
     *
     * @return $this
     */
    public function setComInvestigator($com_investigator): self
    {
        $currentInvestigators = isset($this->com_investigator) ? explode(' ', $this->com_investigator) : [];
        $newInvestigators = isset($com_investigator) ? explode(' ', $com_investigator) : [];
        $this->newInvestigators = array_diff($newInvestigators, $currentInvestigators);

        if (!empty($this->newInvestigators)) {
            $this->investigatorsChanged = true;
        }

        $this->com_investigator = $com_investigator;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvDstart()
    {
        return $this->com_inv_dstart;
    }

    /**
     * @param mixed $com_inv_dstart
     *
     * @return $this
     */
    public function setComInvDstart($com_inv_dstart): self
    {
        $this->com_inv_dstart = $com_inv_dstart;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvDcomp()
    {
        return $this->com_inv_dcomp;
    }

    /**
     * @param mixed $com_inv_dcomp
     *
     * @return $this
     */
    public function setComInvDcomp($com_inv_dcomp): self
    {
        $this->com_inv_dcomp = $com_inv_dcomp;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComConsequence()
    {
        return $this->com_consequence;
    }

    /**
     * @param mixed $com_consequence
     *
     * @return $this
     */
    public function setComConsequence($com_consequence): self
    {
        $this->com_consequence = $com_consequence;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComLikelihood()
    {
        return $this->com_likelihood;
    }

    /**
     * @param mixed $com_likelihood
     *
     * @return $this
     */
    public function setComLikelihood($com_likelihood): self
    {
        $this->com_likelihood = $com_likelihood;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComRating()
    {
        return $this->com_rating;
    }

    /**
     * @param mixed $com_rating
     *
     * @return $this
     */
    public function setComRating($com_rating): self
    {
        $this->com_rating = $com_rating;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComGrade()
    {
        return $this->com_grade;
    }

    /**
     * @param mixed $com_grade
     *
     * @return $this
     */
    public function setComGrade($com_grade): self
    {
        $this->com_grade = $com_grade;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComColour()
    {
        return $this->com_colour;
    }

    /**
     * @param mixed $com_colour
     *
     * @return $this
     */
    public function setComColour($com_colour): self
    {
        $this->com_colour = $com_colour;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComRootCauses()
    {
        return $this->com_root_causes;
    }

    /**
     * @param mixed $com_root_causes
     *
     * @return $this
     */
    public function setComRootCauses($com_root_causes): self
    {
        $this->com_root_causes = $com_root_causes;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvOutcome()
    {
        return $this->com_inv_outcome;
    }

    /**
     * @param mixed $com_inv_outcome
     *
     * @return $this
     */
    public function setComInvOutcome($com_inv_outcome): self
    {
        $this->com_inv_outcome = $com_inv_outcome;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComRecommCode()
    {
        return $this->com_recomm_code;
    }

    /**
     * @param mixed $com_recomm_code
     *
     * @return $this
     */
    public function setComRecommCode($com_recomm_code): self
    {
        $this->com_recomm_code = $com_recomm_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComActionCode()
    {
        return $this->com_action_code;
    }

    /**
     * @param mixed $com_action_code
     *
     * @return $this
     */
    public function setComActionCode($com_action_code): self
    {
        $this->com_action_code = $com_action_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComLessonsCode()
    {
        return $this->com_lessons_code;
    }

    /**
     * @param mixed $com_lessons_code
     *
     * @return $this
     */
    public function setComLessonsCode($com_lessons_code): self
    {
        $this->com_lessons_code = $com_lessons_code;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComUnitType()
    {
        return $this->com_unit_type;
    }

    /**
     * @param mixed $com_unit_type
     *
     * @return $this
     */
    public function setComUnitType($com_unit_type): self
    {
        $this->com_unit_type = $com_unit_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIncType()
    {
        return $this->com_inc_type;
    }

    /**
     * @param mixed $com_inc_type
     *
     * @return $this
     */
    public function setComIncType($com_inc_type): self
    {
        $this->com_inc_type = $com_inc_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIncCategory()
    {
        return $this->com_inc_category;
    }

    /**
     * @param mixed $com_inc_category
     *
     * @return $this
     */
    public function setComIncCategory($com_inc_category): self
    {
        $this->com_inc_category = $com_inc_category;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIncSubcat()
    {
        return $this->com_inc_subcat;
    }

    /**
     * @param mixed $com_inc_subcat
     *
     * @return $this
     */
    public function setComIncSubcat($com_inc_subcat): self
    {
        $this->com_inc_subcat = $com_inc_subcat;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDopened()
    {
        return $this->com_dopened;
    }

    /**
     * @param mixed $com_dopened
     *
     * @return $this
     */
    public function setComDopened($com_dopened): self
    {
        $this->com_dopened = $com_dopened;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDdueinform()
    {
        return $this->com_ddueinform;
    }

    /**
     * @param mixed $com_ddueinform
     *
     * @return $this
     */
    public function setComDdueinform($com_ddueinform): self
    {
        $this->com_ddueinform = $com_ddueinform;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getWeighting()
    {
        return $this->weighting;
    }

    /**
     * @param mixed $weighting
     *
     * @return $this
     */
    public function setWeighting($weighting): self
    {
        $this->weighting = $weighting;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdUnit()
    {
        return $this->com_isd_unit;
    }

    /**
     * @param mixed $com_isd_unit
     *
     * @return $this
     */
    public function setComIsdUnit($com_isd_unit): self
    {
        $this->com_isd_unit = $com_isd_unit;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdLocactual()
    {
        return $this->com_isd_locactual;
    }

    /**
     * @param mixed $com_isd_locactual
     *
     * @return $this
     */
    public function setComIsdLocactual($com_isd_locactual): self
    {
        $this->com_isd_locactual = $com_isd_locactual;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdConsent()
    {
        return $this->com_isd_consent;
    }

    /**
     * @param mixed $com_isd_consent
     *
     * @return $this
     */
    public function setComIsdConsent($com_isd_consent): self
    {
        $this->com_isd_consent = $com_isd_consent;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdDconsentReq()
    {
        return $this->com_isd_dconsent_req;
    }

    /**
     * @param mixed $com_isd_dconsent_req
     *
     * @return $this
     */
    public function setComIsdDconsentReq($com_isd_dconsent_req): self
    {
        $this->com_isd_dconsent_req = $com_isd_dconsent_req;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdDconsentRec()
    {
        return $this->com_isd_dconsent_rec;
    }

    /**
     * @param mixed $com_isd_dconsent_rec
     *
     * @return $this
     */
    public function setComIsdDconsentRec($com_isd_dconsent_rec): self
    {
        $this->com_isd_dconsent_rec = $com_isd_dconsent_rec;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdDivSent()
    {
        return $this->com_isd_div_sent;
    }

    /**
     * @param mixed $com_isd_div_sent
     *
     * @return $this
     */
    public function setComIsdDivSent($com_isd_div_sent): self
    {
        $this->com_isd_div_sent = $com_isd_div_sent;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdRefAdded()
    {
        return $this->com_isd_ref_added;
    }

    /**
     * @param mixed $com_isd_ref_added
     *
     * @return $this
     */
    public function setComIsdRefAdded($com_isd_ref_added): self
    {
        $this->com_isd_ref_added = $com_isd_ref_added;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdIaasInvolved()
    {
        return $this->com_isd_iaas_involved;
    }

    /**
     * @param mixed $com_isd_iaas_involved
     *
     * @return $this
     */
    public function setComIsdIaasInvolved($com_isd_iaas_involved): self
    {
        $this->com_isd_iaas_involved = $com_isd_iaas_involved;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdCasInvolved()
    {
        return $this->com_isd_cas_involved;
    }

    /**
     * @param mixed $com_isd_cas_involved
     *
     * @return $this
     */
    public function setComIsdCasInvolved($com_isd_cas_involved): self
    {
        $this->com_isd_cas_involved = $com_isd_cas_involved;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdChiNo()
    {
        return $this->com_isd_chi_no;
    }

    /**
     * @param mixed $com_isd_chi_no
     *
     * @return $this
     */
    public function setComIsdChiNo($com_isd_chi_no): self
    {
        $this->com_isd_chi_no = $com_isd_chi_no;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdRespSent20()
    {
        return $this->com_isd_resp_sent_20;
    }

    /**
     * @param mixed $com_isd_resp_sent_20
     *
     * @return $this
     */
    public function setComIsdRespSent20($com_isd_resp_sent_20): self
    {
        $this->com_isd_resp_sent_20 = $com_isd_resp_sent_20;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdResp20Reason()
    {
        return $this->com_isd_resp_20_reason;
    }

    /**
     * @param mixed $com_isd_resp_20_reason
     *
     * @return $this
     */
    public function setComIsdResp20Reason($com_isd_resp_20_reason): self
    {
        $this->com_isd_resp_20_reason = $com_isd_resp_20_reason;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdAgree40()
    {
        return $this->com_isd_agree_40;
    }

    /**
     * @param mixed $com_isd_agree_40
     *
     * @return $this
     */
    public function setComIsdAgree40($com_isd_agree_40): self
    {
        $this->com_isd_agree_40 = $com_isd_agree_40;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdAgree40Date()
    {
        return $this->com_isd_agree_40_date;
    }

    /**
     * @param mixed $com_isd_agree_40_date
     *
     * @return $this
     */
    public function setComIsdAgree40Date($com_isd_agree_40_date): self
    {
        $this->com_isd_agree_40_date = $com_isd_agree_40_date;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdActions()
    {
        return $this->com_isd_actions;
    }

    /**
     * @param mixed $com_isd_actions
     *
     * @return $this
     */
    public function setComIsdActions($com_isd_actions): self
    {
        $this->com_isd_actions = $com_isd_actions;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdDexport()
    {
        return $this->com_isd_dexport;
    }

    /**
     * @param mixed $com_isd_dexport
     *
     * @return $this
     */
    public function setComIsdDexport($com_isd_dexport): self
    {
        $this->com_isd_dexport = $com_isd_dexport;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getRepApproved()
    {
        return $this->rep_approved;
    }

    /**
     * @param mixed $rep_approved
     *
     * @return $this
     */
    public function setRepApproved($rep_approved): self
    {
        $this->rep_approved = $rep_approved;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCreatedby()
    {
        return $this->createdby;
    }

    /**
     * @param mixed $createdby
     *
     * @return $this
     */
    public function setCreatedby($createdby): self
    {
        $this->createdby = $createdby;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getShowPerson()
    {
        return $this->show_person;
    }

    /**
     * @param mixed $show_person
     *
     * @return $this
     */
    public function setShowPerson($show_person): self
    {
        $this->show_person = $show_person;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComDcompleted1()
    {
        return $this->com_dcompleted1;
    }

    /**
     * @param mixed $com_dcompleted1
     *
     * @return $this
     */
    public function setComDcompleted1($com_dcompleted1): self
    {
        $this->com_dcompleted1 = $com_dcompleted1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCOMDETAIL()
    {
        return $this->COM_DETAIL;
    }

    /**
     * @param mixed $COM_DETAIL
     *
     * @return $this
     */
    public function setCOMDETAIL($COM_DETAIL): self
    {
        $this->COM_DETAIL = $COM_DETAIL;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCOMSUMMARY()
    {
        return $this->COM_SUMMARY;
    }

    /**
     * @param mixed $COM_SUMMARY
     *
     * @return $this
     */
    public function setCOMSUMMARY($COM_SUMMARY): self
    {
        $this->COM_SUMMARY = $COM_SUMMARY;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCOMREASON()
    {
        return $this->COM_REASON;
    }

    /**
     * @param mixed $COM_REASON
     *
     * @return $this
     */
    public function setComReason($COM_REASON): self
    {
        $this->COM_REASON = $COM_REASON;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComReport()
    {
        return $this->COM_REPORT;
    }

    /**
     * @param mixed $COM_REPORT
     *
     * @return $this
     */
    public function setComReport($COM_REPORT): self
    {
        $this->COM_REPORT = $COM_REPORT;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCOMIRSYNOPSIS()
    {
        return $this->COM_IRSYNOPSIS;
    }

    /**
     * @param mixed $COM_IRSYNOPSIS
     *
     * @return $this
     */
    public function setComIrsynopsis($COM_IRSYNOPSIS): self
    {
        $this->COM_IRSYNOPSIS = $COM_IRSYNOPSIS;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComRecommend()
    {
        return $this->com_recommend;
    }

    /**
     * @param mixed $com_recommend
     *
     * @return $this
     */
    public function setComRecommend($com_recommend): self
    {
        $this->com_recommend = $com_recommend;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvAction()
    {
        return $this->com_inv_action;
    }

    /**
     * @param mixed $com_inv_action
     *
     * @return $this
     */
    public function setComInvAction($com_inv_action): self
    {
        $this->com_inv_action = $com_inv_action;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComInvLessons()
    {
        return $this->com_inv_lessons;
    }

    /**
     * @param mixed $com_inv_lessons
     *
     * @return $this
     */
    public function setComInvLessons($com_inv_lessons): self
    {
        $this->com_inv_lessons = $com_inv_lessons;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdResp40Reason()
    {
        return $this->com_isd_resp_40_reason;
    }

    /**
     * @param mixed $com_isd_resp_40_reason
     *
     * @return $this
     */
    public function setComIsdResp40Reason($com_isd_resp_40_reason): self
    {
        $this->com_isd_resp_40_reason = $com_isd_resp_40_reason;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIsdPlan()
    {
        return $this->com_isd_plan;
    }

    /**
     * @param mixed $com_isd_plan
     *
     * @return $this
     */
    public function setComIsdPlan($com_isd_plan): self
    {
        $this->com_isd_plan = $com_isd_plan;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComNotes1()
    {
        return $this->com_notes1;
    }

    /**
     * @param mixed $com_notes1
     *
     * @return $this
     */
    public function setComNotes1($com_notes1): self
    {
        $this->com_notes1 = $com_notes1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComTypeTierOne()
    {
        return $this->com_type_tier_one;
    }

    /**
     * @param mixed $com_type_tier_one
     *
     * @return $this
     */
    public function setComTypeTierOne($com_type_tier_one): self
    {
        $this->com_type_tier_one = $com_type_tier_one;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComTypeTierTwo()
    {
        return $this->com_type_tier_two;
    }

    /**
     * @param mixed $com_type_tier_two
     *
     * @return $this
     */
    public function setComTypeTierTwo($com_type_tier_two): self
    {
        $this->com_type_tier_two = $com_type_tier_two;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComTypeTierThree()
    {
        return $this->com_type_tier_three;
    }

    /**
     * @param mixed $com_type_tier_three
     *
     * @return $this
     */
    public function setComTypeTierThree($com_type_tier_three): self
    {
        $this->com_type_tier_three = $com_type_tier_three;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComAffectingTierZero()
    {
        return $this->com_affecting_tier_zero;
    }

    /**
     * @param mixed $com_affecting_tier_zero
     *
     * @return $this
     */
    public function setComAffectingTierZero($com_affecting_tier_zero): self
    {
        $this->com_affecting_tier_zero = $com_affecting_tier_zero;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComIssuesLinked()
    {
        return $this->com_issues_linked;
    }

    /**
     * @param mixed $com_issues_linked
     *
     * @return $this
     */
    public function setComIssuesLinked($com_issues_linked): self
    {
        $this->com_issues_linked = $com_issues_linked;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubjectsLinked()
    {
        return $this->com_subjects_linked;
    }

    /**
     * @param mixed $com_subjects_linked
     *
     * @return $this
     */
    public function setComSubjectsLinked($com_subjects_linked): self
    {
        $this->com_subjects_linked = $com_subjects_linked;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLocationId()
    {
        return $this->location_id;
    }

    /**
     * @param mixed $location_id
     *
     * @return $this
     */
    public function setLocationId($location_id): self
    {
        $this->location_id = $location_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getServiceId()
    {
        return $this->service_id;
    }

    /**
     * @param mixed $service_id
     *
     * @return $this
     */
    public function setServiceId($service_id): self
    {
        $this->service_id = $service_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLocationId1()
    {
        return $this->location_id1;
    }

    /**
     * @param mixed $location_id1
     *
     * @return $this
     */
    public function setLocationId1($location_id1): self
    {
        $this->location_id1 = $location_id1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getServiceId1()
    {
        return $this->service_id1;
    }

    /**
     * @param mixed $service_id1
     *
     * @return $this
     */
    public function setServiceId1($service_id1): self
    {
        $this->service_id1 = $service_id1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFlagForInvestigation()
    {
        return $this->flag_for_investigation;
    }

    /**
     * @param mixed $flag_for_investigation
     *
     * @return $this
     */
    public function setFlagForInvestigation($flag_for_investigation): self
    {
        $this->flag_for_investigation = $flag_for_investigation;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFlagForRib()
    {
        return $this->flag_for_rib;
    }

    /**
     * @param mixed $flag_for_rib
     *
     * @return $this
     */
    public function setFlagForRib($flag_for_rib): self
    {
        $this->flag_for_rib = $flag_for_rib;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOtherLocation()
    {
        return $this->other_location;
    }

    /**
     * @param mixed $other_location
     *
     * @return $this
     */
    public function setOtherLocation($other_location): self
    {
        $this->other_location = $other_location;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOtherService()
    {
        return $this->other_service;
    }

    /**
     * @param mixed $other_service
     *
     * @return $this
     */
    public function setOtherService($other_service): self
    {
        $this->other_service = $other_service;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubtype1()
    {
        return $this->com_subtype1;
    }

    /**
     * @param mixed $com_subtype1
     *
     * @return $this
     */
    public function setComSubtype1($com_subtype1): self
    {
        $this->com_subtype1 = $com_subtype1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComType1()
    {
        return $this->com_type1;
    }

    /**
     * @param mixed $com_type1
     *
     * @return $this
     */
    public function setComType1($com_type1): self
    {
        $this->com_type1 = $com_type1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getKeyLearnings()
    {
        return $this->key_learnings;
    }

    /**
     * @param mixed $key_learnings
     *
     * @return $this
     */
    public function setKeyLearnings($key_learnings): self
    {
        $this->key_learnings = $key_learnings;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLearningsToShare()
    {
        return $this->learnings_to_share;
    }

    /**
     * @param mixed $learnings_to_share
     *
     * @return $this
     */
    public function setLearningsToShare($learnings_to_share): self
    {
        $this->learnings_to_share = $learnings_to_share;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLearningsTitle()
    {
        return $this->learnings_title;
    }

    /**
     * @param mixed $learnings_title
     *
     * @return $this
     */
    public function setLearningsTitle($learnings_title): self
    {
        $this->learnings_title = $learnings_title;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getRequestedConsultant()
    {
        return $this->requested_consultant;
    }

    /**
     * @param mixed $requested_consultant
     *
     * @return $this
     */
    public function setRequestedConsultant($requested_consultant): self
    {
        $this->requested_consultant = $requested_consultant;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getPriorityScale()
    {
        return $this->priority_scale;
    }

    /**
     * @param mixed $priority_scale
     *
     * @return $this
     */
    public function setPriorityScale($priority_scale): self
    {
        $this->priority_scale = $priority_scale;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getisRecordSensitive()
    {
        return $this->is_record_sensitive;
    }

    /**
     * @param mixed $is_record_sensitive
     *
     * @return $this
     */
    public function setIsRecordSensitive($is_record_sensitive): self
    {
        $this->is_record_sensitive = $is_record_sensitive;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComLessonLearnedSubCategory()
    {
        return $this->com_lesson_learned_sub_category;
    }

    /**
     * @param mixed $com_lesson_learned_sub_category
     *
     * @return $this
     */
    public function setComLessonLearnedSubCategory($com_lesson_learned_sub_category): self
    {
        $this->com_lesson_learned_sub_category = $com_lesson_learned_sub_category;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComHroCharacteristics()
    {
        return $this->com_hro_characteristics;
    }

    /**
     * @param mixed $com_hro_characteristics
     *
     * @return $this
     */
    public function setComHroCharacteristics($com_hro_characteristics): self
    {
        $this->com_hro_characteristics = $com_hro_characteristics;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSpecialty()
    {
        return $this->com_specialty;
    }

    /**
     * @param mixed $com_specialty
     *
     * @return $this
     */
    public function setComSpecialty($com_specialty): self
    {
        $this->com_specialty = $com_specialty;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getMcaOrNa()
    {
        return $this->mca_or_na;
    }

    /**
     * @param mixed $mca_or_na
     *
     * @return $this
     */
    public function setMcaOrNa($mca_or_na): self
    {
        $this->mca_or_na = $mca_or_na;

        return $this;
    }

    public function getComPatExpectations(): ?string
    {
        return $this->com_pat_expectations;
    }

    /**
     * @return $this
     */
    public function setComPatExpectations(?string $com_pat_expectations): self
    {
        $this->com_pat_expectations = $com_pat_expectations;

        return $this;
    }

    public function getComPatUpdatePref(): ?string
    {
        return $this->com_pat_update_pref;
    }

    /**
     * @return $this
     */
    public function setComPatUpdatePref(?string $com_pat_update_pref): self
    {
        $this->com_pat_update_pref = $com_pat_update_pref;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComGradeRating()
    {
        return $this->com_grade_rating;
    }

    /**
     * @param mixed $com_grade_rating
     *
     * @return $this
     */
    public function setComGradeRating($com_grade_rating): self
    {
        $this->com_grade_rating = $com_grade_rating;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getReferredToOmbudsman()
    {
        return $this->referred_to_ombudsman;
    }

    /**
     * @param mixed $referred_to_ombudsman
     *
     * @return $this
     */
    public function setReferredToOmbudsman($referred_to_ombudsman)
    {
        $this->referred_to_ombudsman = $referred_to_ombudsman;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateFirstContact()
    {
        return $this->date_first_contact;
    }

    /**
     * @param mixed $date_first_contact
     *
     * @return $this
     */
    public function setDateFirstContact($date_first_contact)
    {
        $this->date_first_contact = $date_first_contact;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateEvidenceDue()
    {
        return $this->date_evidence_due;
    }

    /**
     * @param mixed $date_evidence_due
     *
     * @return $this
     */
    public function setDateEvidenceDue($date_evidence_due)
    {
        $this->date_evidence_due = $date_evidence_due;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateEvidenceSubmitted()
    {
        return $this->date_evidence_submitted;
    }

    /**
     * @param mixed $date_evidence_submitted
     *
     * @return $this
     */
    public function setDateEvidenceSubmitted($date_evidence_submitted)
    {
        $this->date_evidence_submitted = $date_evidence_submitted;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOmbudsmanReference()
    {
        return $this->ombudsman_reference;
    }

    /**
     * @param mixed $ombudsman_reference
     *
     * @return $this
     */
    public function setOmbudsmanReference($ombudsman_reference)
    {
        $this->ombudsman_reference = $ombudsman_reference;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOmbudsmanHandler()
    {
        return $this->ombudsman_handler;
    }

    /**
     * @param mixed $ombudsman_handler
     *
     * @return $this
     */
    public function setOmbudsmanHandler($ombudsman_handler)
    {
        $this->ombudsman_handler = $ombudsman_handler;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOmbudsmanCurrentStage()
    {
        return $this->ombudsman_current_stage;
    }

    /**
     * @param mixed $ombudsman_current_stage
     *
     * @return $this
     */
    public function setOmbudsmanCurrentStage($ombudsman_current_stage)
    {
        $this->ombudsman_current_stage = $ombudsman_current_stage;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEarlySettlementProposal()
    {
        return $this->early_settlement_proposal;
    }

    /**
     * @param mixed $early_settlement_proposal
     *
     * @return $this
     */
    public function setEarlySettlementProposal($early_settlement_proposal)
    {
        $this->early_settlement_proposal = $early_settlement_proposal;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEarlySettlementProposalDateReceived()
    {
        return $this->early_settlement_proposal_date_received;
    }

    /**
     * @param mixed $early_settlement_proposal_date_received
     *
     * @return $this
     */
    public function setEarlySettlementProposalDateReceived($early_settlement_proposal_date_received)
    {
        $this->early_settlement_proposal_date_received = $early_settlement_proposal_date_received;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEarlySettlementProposalDateRequested()
    {
        return $this->early_settlement_proposal_date_requested;
    }

    /**
     * @param mixed $early_settlement_proposal_date_requested
     *
     * @return $this
     */
    public function setEarlySettlementProposalDateRequested($early_settlement_proposal_date_requested)
    {
        $this->early_settlement_proposal_date_requested = $early_settlement_proposal_date_requested;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getEarlySettlementProposalDateSubmitted()
    {
        return $this->early_settlement_proposal_date_submitted;
    }

    /**
     * @param mixed $early_settlement_proposal_date_submitted
     *
     * @return $this
     */
    public function setEarlySettlementProposalDateSubmitted($early_settlement_proposal_date_submitted)
    {
        $this->early_settlement_proposal_date_submitted = $early_settlement_proposal_date_submitted;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateInvestigationBegan()
    {
        return $this->date_investigation_began;
    }

    /**
     * @param mixed $date_investigation_began
     *
     * @return $this
     */
    public function setDateInvestigationBegan($date_investigation_began)
    {
        $this->date_investigation_began = $date_investigation_began;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateResponseDue()
    {
        return $this->date_response_due;
    }

    /**
     * @param mixed $date_response_due
     *
     * @return $this
     */
    public function setDateResponseDue($date_response_due)
    {
        $this->date_response_due = $date_response_due;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateDocInvSub()
    {
        return $this->date_doc_inv_sub;
    }

    /**
     * @param mixed $date_doc_inv_sub
     *
     * @return $this
     */
    public function setDateDocInvSub($date_doc_inv_sub)
    {
        $this->date_doc_inv_sub = $date_doc_inv_sub;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateInvSub()
    {
        return $this->date_inv_sub;
    }

    /**
     * @param mixed $date_inv_sub
     *
     * @return $this
     */
    public function setDateInvSub($date_inv_sub)
    {
        $this->date_inv_sub = $date_inv_sub;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateDraftReceived()
    {
        return $this->date_draft_received;
    }

    /**
     * @param mixed $date_draft_received
     *
     * @return $this
     */
    public function setDateDraftReceived($date_draft_received)
    {
        $this->date_draft_received = $date_draft_received;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateDraftResponseDue()
    {
        return $this->date_draft_response_due;
    }

    /**
     * @param mixed $date_draft_response_due
     *
     * @return $this
     */
    public function setDateDraftResponseDue($date_draft_response_due)
    {
        $this->date_draft_response_due = $date_draft_response_due;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateRecRecieved()
    {
        return $this->date_rec_recieved;
    }

    /**
     * @param mixed $date_rec_recieved
     *
     * @return $this
     */
    public function setDateRecRecieved($date_rec_recieved)
    {
        $this->date_rec_recieved = $date_rec_recieved;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateActionPlan()
    {
        return $this->date_action_plan;
    }

    /**
     * @param mixed $date_action_plan
     *
     * @return $this
     */
    public function setDateActionPlan($date_action_plan)
    {
        $this->date_action_plan = $date_action_plan;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateDraftReportSub()
    {
        return $this->date_draft_report_sub;
    }

    /**
     * @param mixed $date_draft_report_sub
     *
     * @return $this
     */
    public function setDateDraftReportSub($date_draft_report_sub)
    {
        $this->date_draft_report_sub = $date_draft_report_sub;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getDateReportReceived()
    {
        return $this->date_report_received;
    }

    /**
     * @param mixed $date_report_received
     *
     * @return $this
     */
    public function setDateReportReceived($date_report_received)
    {
        $this->date_report_received = $date_report_received;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFinalRepType()
    {
        return $this->final_rep_type;
    }

    /**
     * @param mixed $final_rep_type
     *
     * @return $this
     */
    public function setFinalRepType($final_rep_type)
    {
        $this->final_rep_type = $final_rep_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOmbudsmanOutcome()
    {
        return $this->ombudsman_outcome;
    }

    /**
     * @param mixed $ombudsman_outcome
     *
     * @return $this
     */
    public function setOmbudsmanOutcome($ombudsman_outcome)
    {
        $this->ombudsman_outcome = $ombudsman_outcome;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getOmbudsmanLearning()
    {
        return $this->ombudsman_learning;
    }

    /**
     * @param mixed $ombudsman_learning
     *
     * @return $this
     */
    public function setOmbudsmanLearning($ombudsman_learning)
    {
        $this->ombudsman_learning = $ombudsman_learning;

        return $this;
    }

    public function getComOutcomeGrading(): ?string
    {
        return $this->com_outcome_grading;
    }

    public function setComOutcomeGrading(?string $com_outcome_grading): self
    {
        $this->com_outcome_grading = $com_outcome_grading;

        return $this;
    }

    public function getComSeriousIncident(): ?string
    {
        return $this->com_serious_incident;
    }

    /**
     * @return $this
     */
    public function setComSeriousIncident(?string $com_serious_incident): self
    {
        $this->com_serious_incident = $com_serious_incident;

        return $this;
    }

    public function getComWelshLanguage(): ?string
    {
        return $this->com_welsh_language;
    }

    /**
     * @return $this
     */
    public function setComWelshLanguage(?string $com_welsh_language): self
    {
        $this->com_welsh_language = $com_welsh_language;

        return $this;
    }

    public function getOutbreakImpact(): ?string
    {
        return $this->outbreak_impact;
    }

    /**
     * @return $this
     */
    public function setOutbreakImpact(?string $outbreak_impact): self
    {
        $this->outbreak_impact = $outbreak_impact;

        return $this;
    }

    public function getOutbreakType(): ?string
    {
        return $this->outbreak_type;
    }

    /**
     * @return $this
     */
    public function setOutbreakType(?string $outbreak_type): self
    {
        $this->outbreak_type = $outbreak_type;

        return $this;
    }

    public function getComRedressEscalated(): ?string
    {
        return $this->com_redress_escalated;
    }

    /**
     * @return $this
     */
    public function setComRedressEscalated(?string $com_redress_escalated): self
    {
        $this->com_redress_escalated = $com_redress_escalated;

        return $this;
    }

    public function setUUID(string $uuid): self
    {
        $this->uuid = $uuid;

        return $this;
    }

    public function getUUID(): ?string
    {
        return $this->uuid;
    }

    public function getShowDocument(): ?string
    {
        return $this->show_document;
    }

    public function setShowDocument(?string $show_document): self
    {
        $this->show_document = $show_document;

        return $this;
    }

    public function getUpdateddate(): ?string
    {
        return $this->updateddate;
    }

    public function setUpdateddate(?string $updateddate): self
    {
        $this->updateddate = $updateddate;

        return $this;
    }

    public function getUpdatedby(): ?string
    {
        return $this->updatedby;
    }

    public function setUpdatedby(?string $updatedby): self
    {
        $this->updatedby = $updatedby;

        return $this;
    }

    /**
     * @return Collection<FeedbackSubjectEntity>
     */
    public function getSubjects(): Collection
    {
        return $this->subjects;
    }

    /**
     * @param Collection<FeedbackSubjectEntity> $subjects
     */
    public function setSubjects(Collection $subjects): self
    {
        $this->subjects = $subjects;

        return $this;
    }

    /**
     * @return Collection<FeedbackProgressNoteEntity>
     */
    public function getProgressNotes(): Collection
    {
        return $this->progressNotes;
    }

    /**
     * @param Collection<FeedbackProgressNoteEntity> $progressNotes
     */
    public function setProgressNotes(Collection $progressNotes): self
    {
        $this->progressNotes = $progressNotes;

        return $this;
    }

    /**
     * @return Collection<FeedbackLinksEntity>
     */
    public function getFeedbackChains(): Collection
    {
        return $this->feedbackChains;
    }

    /**
     * @param Collection<FeedbackLinksEntity> $feedbackChains
     */
    public function setFeedbackChains(Collection $feedbackChains): self
    {
        $this->feedbackChains = $feedbackChains;

        return $this;
    }

    /**
     * @return Collection<FeedbackLinksEntity>
     */
    public function getPrimaryContactFeedbackChains(): Collection
    {
        $expr = new Comparison('lcom_primary', '=', 'Y');
        $criteria = new Criteria();
        $criteria->where($expr);

        return $this->feedbackChains->matching($criteria);
    }

    public function getModule(): string
    {
        return Module::FEEDBACK;
    }

    public function getEmailTemplateData(): array
    {
        $data = (new FeedbackHydratorFactory())->create()->extract($this);

        foreach (self::EMAIL_TEMPLATE_VARIABLE_MAP as $source => $destination) {
            if (array_key_exists($source, $data)) {
                $data[$destination] = $data[$source];
                unset($data[$source]);
            }
        }

        return $data;
    }

    public function shouldEmailProgressToReporters(): bool
    {
        return $this->closedDateHasChanged;
    }
}
