<?php

namespace app\models\feedback\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "compl_subjects")]
class FeedbackSubjectEntity
{
    #[Column(type: "string")]
    protected ?string $com_issue_pathway = null;

    #[Column(type: "string")]
    protected ?string $com_issue_type = null;

    #[Id]
    #[Column(type: "integer")]
    private $recordid;

    #[ManyToOne(
        targetEntity: FeedbackEntity::class,
        cascade: ["merge", "persist"],
        inversedBy: "subjects"
    )]
    #[JoinColumn(
        name: "com_id",
        referencedColumnName: "recordid"
    )]
    private $feedback;

    #[Column(type: "integer")]
    private $listorder;

    #[Column(type: "string")]
    private $com_subject;

    #[Column(type: "string")]
    private $com_subsubject;

    #[Column(type: "string")]
    private $com_stafftype;

    #[Column(type: "string")]
    private $com_outcome;

    #[Column(type: "string")]
    private $com_ch8_poc;

    #[Column(type: "string")]
    private $com_ch8_subject;

    #[Column(type: "string")]
    private $csu_dcompleted;

    #[Column(type: "string")]
    private $csu_notes;

    #[Column(type: "string")]
    private $com_service_area;

    #[Column(type: "integer")]
    private $csu_location_id;

    #[Column(type: "integer")]
    private $csu_service_id;

    #[Column(type: "string")]
    private $com_subtype;

    #[Column(type: "string")]
    private $com_type;

    #[Column(type: "string")]
    private $csu_level_of_harm;

    /**
     * @return mixed
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param mixed $recordid
     *
     * @return FeedbackSubjectEntity
     */
    public function setRecordid($recordid)
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getFeedback()
    {
        return $this->feedback;
    }

    /**
     * @param mixed $feedback
     *
     * @return FeedbackSubjectEntity
     */
    public function setFeedback($feedback)
    {
        $this->feedback = $feedback;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getListorder()
    {
        return $this->listorder;
    }

    /**
     * @param mixed $listorder
     *
     * @return FeedbackSubjectEntity
     */
    public function setListorder($listorder)
    {
        $this->listorder = $listorder;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubject()
    {
        return $this->com_subject;
    }

    /**
     * @param mixed $com_subject
     *
     * @return FeedbackSubjectEntity
     */
    public function setComSubject($com_subject)
    {
        $this->com_subject = $com_subject;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubsubject()
    {
        return $this->com_subsubject;
    }

    /**
     * @param mixed $com_subsubject
     *
     * @return FeedbackSubjectEntity
     */
    public function setComSubsubject($com_subsubject)
    {
        $this->com_subsubject = $com_subsubject;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComStafftype()
    {
        return $this->com_stafftype;
    }

    /**
     * @param mixed $com_stafftype
     *
     * @return FeedbackSubjectEntity
     */
    public function setComStafftype($com_stafftype)
    {
        $this->com_stafftype = $com_stafftype;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComOutcome()
    {
        return $this->com_outcome;
    }

    /**
     * @param mixed $com_outcome
     *
     * @return FeedbackSubjectEntity
     */
    public function setComOutcome($com_outcome)
    {
        $this->com_outcome = $com_outcome;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComCh8Poc()
    {
        return $this->com_ch8_poc;
    }

    /**
     * @param mixed $com_ch8_poc
     *
     * @return FeedbackSubjectEntity
     */
    public function setComCh8Poc($com_ch8_poc)
    {
        $this->com_ch8_poc = $com_ch8_poc;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComCh8Subject()
    {
        return $this->com_ch8_subject;
    }

    /**
     * @param mixed $com_ch8_subject
     *
     * @return FeedbackSubjectEntity
     */
    public function setComCh8Subject($com_ch8_subject)
    {
        $this->com_ch8_subject = $com_ch8_subject;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCsuDcompleted()
    {
        return $this->csu_dcompleted;
    }

    /**
     * @param mixed $csu_dcompleted
     *
     * @return FeedbackSubjectEntity
     */
    public function setCsuDcompleted($csu_dcompleted)
    {
        $this->csu_dcompleted = $csu_dcompleted;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCsuNotes()
    {
        return $this->csu_notes;
    }

    /**
     * @param mixed $csu_notes
     *
     * @return FeedbackSubjectEntity
     */
    public function setCsuNotes($csu_notes)
    {
        $this->csu_notes = $csu_notes;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComServiceArea()
    {
        return $this->com_service_area;
    }

    /**
     * @param mixed $com_service_area
     *
     * @return FeedbackSubjectEntity
     */
    public function setComServiceArea($com_service_area)
    {
        $this->com_service_area = $com_service_area;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCsuLocationId()
    {
        return $this->csu_location_id;
    }

    /**
     * @param mixed $csu_location_id
     *
     * @return FeedbackSubjectEntity
     */
    public function setCsuLocationId($csu_location_id)
    {
        $this->csu_location_id = $csu_location_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCsuServiceId()
    {
        return $this->csu_service_id;
    }

    /**
     * @param mixed $csu_service_id
     *
     * @return FeedbackSubjectEntity
     */
    public function setCsuServiceId($csu_service_id)
    {
        $this->csu_service_id = $csu_service_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComSubtype()
    {
        return $this->com_subtype;
    }

    /**
     * @param mixed $com_subtype
     *
     * @return FeedbackSubjectEntity
     */
    public function setComSubtype($com_subtype)
    {
        $this->com_subtype = $com_subtype;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getComType()
    {
        return $this->com_type;
    }

    /**
     * @param mixed $com_type
     *
     * @return FeedbackSubjectEntity
     */
    public function setComType($com_type)
    {
        $this->com_type = $com_type;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCsuLevelOfHarm()
    {
        return $this->csu_level_of_harm;
    }

    /**
     * @param mixed $csu_level_of_harm
     *
     * @return FeedbackSubjectEntity
     */
    public function setCsuLevelOfHarm($csu_level_of_harm)
    {
        $this->csu_level_of_harm = $csu_level_of_harm;

        return $this;
    }

    public function getComIssuePathway(): ?string
    {
        return $this->com_issue_pathway;
    }

    public function setComIssuePathway(?string $com_issue_pathway): self
    {
        $this->com_issue_pathway = $com_issue_pathway;

        return $this;
    }

    public function getComIssueType(): ?string
    {
        return $this->com_issue_type;
    }

    public function setComIssueType(?string $com_issue_type): self
    {
        $this->com_issue_type = $com_issue_type;

        return $this;
    }

    public function mapToFeedback(FeedbackEntity $feedback): void
    {
        $feedback
            ->setComSubject1($this->getComSubject())
            ->setComSubsubject1($this->getComSubsubject())
            ->setComStafftype1($this->getComStafftype())
            ->setLocationId1($this->getCsuLocationId())
            ->setServiceId1($this->getCsuServiceId())
            ->setComOutcome1($this->getComOutcome())
            ->setComNotes1($this->getCsuNotes())
            ->setComDcompleted1($this->getCsuDcompleted())
            ->setComType1($this->getComType())
            ->setComSubtype1($this->getComSubtype());
    }
}
