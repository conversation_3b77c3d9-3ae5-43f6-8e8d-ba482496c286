<?php

namespace app\models\feedback\entities;

use app\models\contact\entities\ContactEntity;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "link_compl")]
class FeedbackLinksEntity
{
    #[Id]
    #[Column(type: "integer")]
    private $recordid;

    #[ManyToOne(
        targetEntity: ContactEntity::class,
        inversedBy: "feedback_links",
        cascade: ["merge", "persist"]
    )]
    #[JoinColumn(
        name: "con_id",
        referencedColumnName: "recordid"
    )]
    private $contact;

    #[ManyToOne(
        targetEntity: FeedbackEntity::class,
        inversedBy: "feedbackChains"
    )]
    #[Join<PERSON>olumn(
        name: "com_id",
        referencedColumnName: "recordid"
    )]
    private $feedback;

    #[Column(type: "string")]
    private $lcom_dreceived;

    #[Column(type: "string")]
    private $lcom_dack;

    #[Column(type: "string")]
    private $lcom_dactioned;

    #[Column(type: "string")]
    private $lcom_dhold1;

    #[Column(type: "string")]
    private $lcom_dresponse;

    #[Column(type: "string")]
    private $lcom_dholding;

    #[Column(type: "string")]
    private $lcom_dreplied;

    #[Column(type: "string")]
    private $lcom_dreopened;

    #[Column(type: "string")]
    private $lcom_ddueack;

    #[Column(type: "string")]
    private $lcom_dduehold1;

    #[Column(type: "string")]
    private $lcom_ddueact;

    #[Column(type: "string")]
    private $lcom_ddueresp;

    #[Column(type: "string")]
    private $lcom_dduehold;

    #[Column(type: "string")]
    private $lcom_dduerepl;

    #[Column(type: "string")]
    private $lcom_nextdue;

    #[Column(type: "string")]
    private $lcom_nexttype;

    #[Column(type: "string")]
    private $lcom_iscomplpat;

    #[Column(type: "string")]
    private $lcom_current;

    #[Column(type: "string")]
    private $lcom_primary;

    #[Column(type: "string")]
    private $lcom_details;

    #[Column(type: "string")]
    private $lcom_reopen;

    #[Column(type: "string")]
    private $lcom_reason_for_reopen;

    /**
     * @return mixed
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param mixed $recordid
     *
     * @return FeedbackLinksEntity
     */
    public function setRecordid($recordid)
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getContact()
    {
        return $this->contact;
    }

    /**
     * @param mixed $contact
     *
     * @return FeedbackLinksEntity
     */
    public function setContact($contact)
    {
        $this->contact = $contact;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDreceived()
    {
        return $this->lcom_dreceived;
    }

    /**
     * @param mixed $lcom_dreceived
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDreceived($lcom_dreceived)
    {
        $this->lcom_dreceived = $lcom_dreceived;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDack()
    {
        return $this->lcom_dack;
    }

    /**
     * @param mixed $lcom_dack
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDack($lcom_dack)
    {
        $this->lcom_dack = $lcom_dack;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDactioned()
    {
        return $this->lcom_dactioned;
    }

    /**
     * @param mixed $lcom_dactioned
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDactioned($lcom_dactioned)
    {
        $this->lcom_dactioned = $lcom_dactioned;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDresponse()
    {
        return $this->lcom_dresponse;
    }

    /**
     * @param mixed $lcom_dresponse
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDresponse($lcom_dresponse)
    {
        $this->lcom_dresponse = $lcom_dresponse;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDholding()
    {
        return $this->lcom_dholding;
    }

    /**
     * @param mixed $lcom_dholding
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDholding($lcom_dholding)
    {
        $this->lcom_dholding = $lcom_dholding;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDreplied()
    {
        return $this->lcom_dreplied;
    }

    /**
     * @param mixed $lcom_dreplied
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDreplied($lcom_dreplied)
    {
        $this->lcom_dreplied = $lcom_dreplied;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDreopened()
    {
        return $this->lcom_dreopened;
    }

    /**
     * @param mixed $lcom_dreopened
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDreopened($lcom_dreopened)
    {
        $this->lcom_dreopened = $lcom_dreopened;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDdueack()
    {
        return $this->lcom_ddueack;
    }

    /**
     * @param mixed $lcom_ddueack
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDdueack($lcom_ddueack)
    {
        $this->lcom_ddueack = $lcom_ddueack;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDdueact()
    {
        return $this->lcom_ddueact;
    }

    /**
     * @param mixed $lcom_ddueact
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDdueact($lcom_ddueact)
    {
        $this->lcom_ddueact = $lcom_ddueact;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDdueresp()
    {
        return $this->lcom_ddueresp;
    }

    /**
     * @param mixed $lcom_ddueresp
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDdueresp($lcom_ddueresp)
    {
        $this->lcom_ddueresp = $lcom_ddueresp;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDduehold()
    {
        return $this->lcom_dduehold;
    }

    /**
     * @param mixed $lcom_dduehold
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDduehold($lcom_dduehold)
    {
        $this->lcom_dduehold = $lcom_dduehold;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDduerepl()
    {
        return $this->lcom_dduerepl;
    }

    /**
     * @param mixed $lcom_dduerepl
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDduerepl($lcom_dduerepl)
    {
        $this->lcom_dduerepl = $lcom_dduerepl;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomNextdue()
    {
        return $this->lcom_nextdue;
    }

    /**
     * @param mixed $lcom_nextdue
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomNextdue($lcom_nextdue)
    {
        $this->lcom_nextdue = $lcom_nextdue;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomNexttype()
    {
        return $this->lcom_nexttype;
    }

    /**
     * @param mixed $lcom_nexttype
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomNexttype($lcom_nexttype)
    {
        $this->lcom_nexttype = $lcom_nexttype;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomIscomplpat()
    {
        return $this->lcom_iscomplpat;
    }

    /**
     * @param mixed $lcom_iscomplpat
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomIscomplpat($lcom_iscomplpat)
    {
        $this->lcom_iscomplpat = $lcom_iscomplpat;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomCurrent()
    {
        return $this->lcom_current;
    }

    /**
     * @param mixed $lcom_current
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomCurrent($lcom_current)
    {
        $this->lcom_current = $lcom_current;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomPrimary()
    {
        return $this->lcom_primary;
    }

    /**
     * @param mixed $lcom_primary
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomPrimary($lcom_primary)
    {
        $this->lcom_primary = $lcom_primary;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDetails()
    {
        return $this->lcom_details;
    }

    /**
     * @param mixed $lcom_details
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDetails($lcom_details)
    {
        $this->lcom_details = $lcom_details;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomReopen()
    {
        return $this->lcom_reopen;
    }

    /**
     * @param mixed $lcom_reopen
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomReopen($lcom_reopen)
    {
        $this->lcom_reopen = $lcom_reopen;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomReasonForReopen()
    {
        return $this->lcom_reason_for_reopen;
    }

    /**
     * @param mixed $lcom_reason_for_reopen
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomReasonForReopen($lcom_reason_for_reopen)
    {
        $this->lcom_reason_for_reopen = $lcom_reason_for_reopen;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDhold1()
    {
        return $this->lcom_dhold1;
    }

    /**
     * @param mixed $lcom_dhold1
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDhold1($lcom_dhold1)
    {
        $this->lcom_dhold1 = $lcom_dhold1;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getLcomDduehold1()
    {
        return $this->lcom_dduehold1;
    }

    /**
     * @param mixed $lcom_dduehold1
     *
     * @return FeedbackLinksEntity
     */
    public function setLcomDduehold1($lcom_dduehold1)
    {
        $this->lcom_dduehold1 = $lcom_dduehold1;

        return $this;
    }

    public function getFeedback(): FeedbackEntity
    {
        return $this->feedback;
    }

    public function setFeedback(FeedbackEntity $feedback): self
    {
        $this->feedback = $feedback;

        return $this;
    }
}
