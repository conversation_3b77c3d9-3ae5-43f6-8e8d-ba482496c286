<?php

namespace app\models\feedback\hydrators;

use app\services\carlton\generic\ApprovalStatusAdapter;
use app\services\carlton\generic\DateAdapter;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class FeedbackHydratorFactory
{
    public function create(): FeedbackHydrator
    {
        return new FeedbackHydrator(
            new ApprovalStatusAdapter(),
            new DateAdapter(),
        );
    }
}
