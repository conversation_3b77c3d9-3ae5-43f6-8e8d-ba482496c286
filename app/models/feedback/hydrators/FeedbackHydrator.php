<?php

namespace app\models\feedback\hydrators;

use app\models\feedback\entities\FeedbackEntity;
use app\services\carlton\generic\ApprovalStatusAdapter;
use app\services\carlton\generic\DateAdapter;
use src\complaints\model\FeedbackFields;

/** @codeCoverageIgnore  */
class FeedbackHydrator
{
    protected $approvalStatusAdapter;
    protected $dateAdapter;

    public function __construct(ApprovalStatusAdapter $approvalStatusAdapter, DateAdapter $dateAdapter)
    {
        $this->approvalStatusAdapter = $approvalStatusAdapter;
        $this->dateAdapter = $dateAdapter;
    }

    public function hydrate(array $data)
    {
        $data['createdby'] = 'Created via API';

        $feedbackEntity = (new FeedbackEntity())
            ->setRecordid($data['id'])
            ->setComName($data['name'])
            ->setComMgr($data['mgr'])
            ->setComOurref($data['ourref'])
            ->setComOtherref($data['otherref'])
            ->setComPasno1($data['pasno1'])
            ->setComPasno2($data['pasno2'])
            ->setComPasno3($data['pasno3'])
            ->setComType($data['type'])
            ->setComSubtype($data['subtype'])
            ->setComOutcome($data['outcome'])
            ->setComMethod($data['method'])
            ->setComPurchaser($data['purchaser'])
            ->setComDincident($this->dateAdapter->adaptFromRequest($data['dincident'] ?: ['date' => '']))
            ->setComDreceived($this->dateAdapter->adaptFromRequest($data['dreceived'] ?: ['date' => '']))
            ->setComDclosed($this->dateAdapter->adaptFromRequest($data['dclosed'] ?: ['date' => '']))
            ->setComDreopened($this->dateAdapter->adaptFromRequest($data['dreopened'] ?: ['date' => '']))
            ->setComCurstage($data['currentstage'])
            ->setComHead($data['head'])
            ->setComDrequest($this->dateAdapter->adaptFromRequest($data['drequest'] ?: ['date' => '']))
            ->setComDdueackreq($this->dateAdapter->adaptFromRequest($data['ddueackreq'] ?: ['date' => '']))
            ->setComDackreq($this->dateAdapter->adaptFromRequest($data['dackreq'] ?: ['date' => '']))
            ->setComDstatement($this->dateAdapter->adaptFromRequest($data['dstatement'] ?: ['date' => '']))
            ->setComDduedecision($this->dateAdapter->adaptFromRequest($data['dduedecision'] ?: ['date' => '']))
            ->setComDdecision($this->dateAdapter->adaptFromRequest($data['ddecision'] ?: ['date' => '']))
            ->setComRecir($data['recir'])
            ->setComDinform($data['dinform'])
            ->setComDduepappt($this->dateAdapter->adaptFromRequest($data['dduepappt'] ?: ['date' => '']))
            ->setComDpdraft($this->dateAdapter->adaptFromRequest($data['dpdraft'] ?: ['date' => '']))
            ->setComDdueppublish($this->dateAdapter->adaptFromRequest($data['ddueppublish'] ?: ['date' => '']))
            ->setComDppublish($this->dateAdapter->adaptFromRequest($data['dppublish'] ?: ['date' => '']))
            ->setComDduecereply($this->dateAdapter->adaptFromRequest($data['dduecereply'] ?: ['date' => '']))
            ->setComDcereply($this->dateAdapter->adaptFromRequest($data['dcereply'] ?: ['date' => '']))
            ->setComExpenses($data['expenses'])
            ->setComKoservarea($data['koservarea'])
            ->setComKosubject($data['kosubject'])
            ->setComKoprof($data['koprof'])
            ->setComSubject1($data['subject1'])
            ->setComSubsubject1($data['subsubject1'])
            ->setComLocation1($data['location1'])
            ->setComDirectorate1($data['directorate1'])
            ->setComStafftype1($data['stafftype1'])
            ->setComSpecialty1($data['specialty1'])
            ->setSeclevel($data['seclevel'])
            ->setSecgroup($data['secgroup'])
            ->setComDduelayChair($this->dateAdapter->adaptFromRequest($data['dduelaychair'] ?: ['date' => '']))
            ->setComDlaychair($this->dateAdapter->adaptFromRequest($data['dlaychair'] ?: ['date' => '']))
            ->setComIrcode($data['ircode'])
            ->setComAssessor($data['assessor'])
            ->setComKoethnicPat($data['koethnic_pat'])
            ->setComKoethnicStaff($data['koethnic_staff'])
            ->setComConsent($data['consent'])
            ->setComOutcome1($data['outcome1'])
            ->setComKo41Type($data['ko41_type'])
            ->setComInvestigator($data['investigator'])
            ->setComInvDstart($data['inv_dstart'])
            ->setComInvDcomp($data['inv_dcomp'])
            ->setComConsequence($data['consequence'])
            ->setComLikelihood($data['likelihood'])
            ->setComRating($data['rating'])
            ->setComGrade($data['grade'])
            ->setComColour($data['colour'])
            ->setComRootCauses($data['root_causes'])
            ->setComInvOutcome($data['inv_outcome'])
            ->setComRecommCode($data['recomm_code'])
            ->setComActionCode($data['action_code'])
            ->setComLessonsCode($data['lessons_code'])
            ->setComUnitType($data['unit_type'])
            ->setComIncType($data['inc_type'])
            ->setComIncCategory($data['inc_category'])
            ->setComIncSubcat($data['inc_subcat'])
            ->setComDopened($this->dateAdapter->adaptFromRequest($data['dopened'] ?: ['date' => '']))
            ->setComDdueinform($this->dateAdapter->adaptFromRequest($data['ddueinform'] ?: ['date' => '']))
            ->setWeighting($data['weighting'])
            ->setComIsdUnit($data['isd_unit'])
            ->setComIsdLocactual($data['isd_locactual'])
            ->setComIsdConsent($data['isd_consent'])
            ->setComIsdDconsentReq($data['isd_dconsent_req'])
            ->setComIsdDconsentRec($data['isd_dconsent_rec'])
            ->setComIsdDivSent($data['isd_div_sent'])
            ->setComIsdRefAdded($data['isd_ref_added'])
            ->setComIsdIaasInvolved($data['isd_iaas_involved'])
            ->setComIsdCasInvolved($data['isd_cas_involved'])
            ->setComIsdChiNo($data['isd_chi_no'])
            ->setComIsdRespSent20($data['isd_resp_sent_20'])
            ->setComIsdResp20Reason($data['isd_resp_20_reason'])
            ->setComIsdAgree40($data['isd_agree_40'])
            ->setComIsdAgree40Date($data['isd_agree_40_date'])
            ->setComIsdActions($data['isd_actions'])
            ->setComIsdDexport($data['isd_dexport'])
            ->setRepApproved($data['rep_approved'])
            ->setCreatedby($data['createdby'])
            ->setShowPerson($data['show_person'])
            ->setComDcompleted1($this->dateAdapter->adaptFromRequest($data['dcompleted1'] ?: ['date' => '']))
            ->setComDetail($data['detail'])
            ->setComSummary($data['summary'])
            ->setComReason($data['reason'])
            ->setComReport($data['report'])
            ->setComIrsynopsis($data['irsynopsis'])
            ->setComRecommend($data['recommend'])
            ->setComInvAction($data['inv_action'])
            ->setComInvLessons($data['inv_lessons'])
            ->setComIsdResp40Reason($data['isd_resp_40_reason'])
            ->setComIsdPlan($data['isd_plan'])
            ->setComNotes1($data['notes1'])
            ->setComTypeTierOne($data['type_tier_one'])
            ->setComTypeTierTwo($data['type_tier_two'])
            ->setComTypeTierThree($data['type_tier_three'])
            ->setComAffectingTierZero($data['affecting_tier_zero'])
            ->setComIssuesLinked($data['issues_linked'])
            ->setComSubjectsLinked($data['subjects_linked'])
            ->setLocationId($data['location_id'])
            ->setServiceId($data['service_id'])
            ->setLocationId1($data['location_id1'])
            ->setServiceId1($data['service_id1'])
            ->setFlagForInvestigation($data['flag_for_investigation'])
            ->setFlagForRib($data['flag_for_rib'])
            ->setOtherLocation($data['other_location'])
            ->setOtherService($data['other_service'])
            ->setComSubtype1($data['subtype1'])
            ->setComType1($data['type1'])
            ->setKeyLearnings($data['key_learnings'])
            ->setLearningsToShare($data['learnings_to_share'])
            ->setLearningsTitle($data['learnings_title'])
            ->setRequestedConsultant($data['requested_consultant'])
            ->setPriorityScale($data['priority_scale'])
            ->setIsRecordSensitive($data['is_record_sensitive'])
            ->setComLessonLearnedSubCategory($data['com_lesson_learned_sub_category'])
            ->setComHroCharacteristics($data['com_hro_characteristics'])
            ->setComSpecialty($data['com_specialty'])
            ->setMcaOrNa($data['mca_or_na'])
            ->setSourceOfRecord($data['source_of_record'])
            ->setComPatExpectations($data['patient_expectations'] ?? null)
            ->setComPatUpdatePref($data['patient_update_prefs'] ?? null)
            ->setComGradeRating($data['grade_rating'] ?? null)
            ->setReferredToOmbudsman($data['referred_to_ombudsman'])
            ->setDateFirstContact($data['date_first_contact'])
            ->setDateEvidenceDue($data['date_evidence_due'])
            ->setDateEvidenceSubmitted($data['date_evidence_submitted'])
            ->setOmbudsmanReference($data['ombudsman_reference'])
            ->setOmbudsmanHandler($data['ombudsman_handler'])
            ->setOmbudsmanCurrentStage($data['ombudsman_current_stage'])
            ->setEarlySettlementProposal($data['early_settlement_proposal'])
            ->setEarlySettlementProposalDateReceived($data[FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED])
            ->setEarlySettlementProposalDateRequested($data[FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED])
            ->setEarlySettlementProposalDateSubmitted($data[FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED])
            ->setDateInvestigationBegan($data['date_investigation_began'])
            ->setDateResponseDue($data['date_response_due'])
            ->setDateDocInvSub($data['date_doc_inv_sub'])
            ->setDateInvSub($data['date_inv_sub'])
            ->setDateDraftReceived($data['date_draft_received'])
            ->setDateDraftResponseDue($data['date_draft_response_due'])
            ->setDateRecRecieved($data['date_rec_recieved'])
            ->setDateActionPlan($data['date_action_plan'])
            ->setDateDraftReportSub($data['date_draft_report_sub'])
            ->setDateReportReceived($data['date_report_received'])
            ->setFinalRepType($data['final_rep_type'])
            ->setOmbudsmanOutcome($data['ombudsman_outcome'])
            ->setOmbudsmanLearning($data['ombudsman_learning'])
            ->setComOutcomeGrading($data['com_outcome_grading'])
            ->setComSeriousIncident($data['com_serious_incident'])
            ->setComWelshLanguage($data['com_welsh_language'])
            ->setOutbreakImpact($data['outbreak_impact'])
            ->setOutbreakType($data['outbreak_type'])
            ->setComRedressEscalated($data['com_redress_escalated']);

        if (isset($data['uuid'])) {
            $feedbackEntity->setUUID((string) $data['uuid']);
        }

        return $feedbackEntity;
    }

    public function extract(FeedbackEntity $feedbackEntity)
    {
        $feedbackResponseArray = [
            'id' => $feedbackEntity->getRecordid(),
            'name' => $feedbackEntity->getComName(),
            'mgr' => $feedbackEntity->getComMgr(),
            'ourref' => $feedbackEntity->getComOurref(),
            'otherref' => $feedbackEntity->getComOtherref(),
            'pasno1' => $feedbackEntity->getComPasno1(),
            'pasno2' => $feedbackEntity->getComPasno2(),
            'pasno3' => $feedbackEntity->getComPasno3(),
            'type' => $feedbackEntity->getComType(),
            'subtype' => $feedbackEntity->getComSubtype(),
            'outcome' => $feedbackEntity->getComOutcome(),
            'method' => $feedbackEntity->getComMethod(),
            'purchaser' => $feedbackEntity->getComPurchaser(),
            'dincident' => $feedbackEntity->getComDincident(),
            'dreceived' => $feedbackEntity->getComDreceived(),
            'dclosed' => $feedbackEntity->getComDclosed(),
            'dreoponed' => $feedbackEntity->getComDreopened(),
            'currentstage' => $feedbackEntity->getComCurstage(),
            'head' => $feedbackEntity->getComHead(),
            'drequest' => $feedbackEntity->getComDrequest(),
            'ddueackreq' => $feedbackEntity->getComDdueackreq(),
            'dackreq' => $feedbackEntity->getComDackreq(),
            'dstatement' => $feedbackEntity->getComDstatement(),
            'dduedecision' => $feedbackEntity->getComDduedecision(),
            'ddecision' => $feedbackEntity->getComDdecision(),
            'recir' => $feedbackEntity->getComRecir(),
            'dinform' => $feedbackEntity->getComDinform(),
            'dduepappt' => $feedbackEntity->getComDduepappt(),
            'dpdraft' => $feedbackEntity->getComDpdraft(),
            'ddueppublish' => $feedbackEntity->getComDdueppublish(),
            'dppublish' => $feedbackEntity->getComDppublish(),
            'dduecereply' => $feedbackEntity->getComDduecereply(),
            'dcereply' => $feedbackEntity->getComDcereply(),
            'expenses' => $feedbackEntity->getComExpenses(),
            'koservarea' => $feedbackEntity->getComKoservarea(),
            'kosubject' => $feedbackEntity->getComKosubject(),
            'koprof' => $feedbackEntity->getComKoprof(),
            'subject1' => $feedbackEntity->getComSubject1(),
            'subsubject1' => $feedbackEntity->getComSubsubject1(),
            'location1' => $feedbackEntity->getComLocation1(),
            'directorate1' => $feedbackEntity->getComDirectorate1(),
            'stafftype1' => $feedbackEntity->getComStafftype1(),
            'specialty1' => $feedbackEntity->getComSpecialty1(),
            'seclevel' => $feedbackEntity->getSeclevel(),
            'secgroup' => $feedbackEntity->getSecgroup(),
            'dduelaychair' => $feedbackEntity->getComDduelaychair(),
            'dlaychair' => $feedbackEntity->getComDlaychair(),
            'ircode' => $feedbackEntity->getComIrcode(),
            'assessor' => $feedbackEntity->getComAssessor(),
            'koethnic_pat' => $feedbackEntity->getComKoethnicPat(),
            'koethnic_staff' => $feedbackEntity->getComKoethnicStaff(),
            'consent' => $feedbackEntity->getComConsent(),
            'outcome1' => $feedbackEntity->getComOutcome1(),
            'ko41_type' => $feedbackEntity->getComKo41Type(),
            'investigator' => $feedbackEntity->getComInvestigator(),
            'inv_dstart' => $feedbackEntity->getComInvDstart(),
            'inv_dcomp' => $feedbackEntity->getComInvDcomp(),
            'consequence' => $feedbackEntity->getComConsequence(),
            'likelihood' => $feedbackEntity->getComLikelihood(),
            'rating' => $feedbackEntity->getComRating(),
            'grade' => $feedbackEntity->getComGrade(),
            'colour' => $feedbackEntity->getComColour(),
            'root_causes' => $feedbackEntity->getComRootCauses(),
            'inv_outcome' => $feedbackEntity->getComInvOutcome(),
            'recomm_code' => $feedbackEntity->getComRecommCode(),
            'action_code' => $feedbackEntity->getComActionCode(),
            'lessons_code' => $feedbackEntity->getComLessonsCode(),
            'unit_type' => $feedbackEntity->getComUnitType(),
            'inc_type' => $feedbackEntity->getComIncType(),
            'inc_category' => $feedbackEntity->getComIncCategory(),
            'inc_subcat' => $feedbackEntity->getComIncSubcat(),
            'dopened' => $feedbackEntity->getComDopened(),
            'ddueinform' => $feedbackEntity->getComDdueinform(),
            'weighting' => $feedbackEntity->getWeighting(),
            'isd_unit' => $feedbackEntity->getComIsdUnit(),
            'isd_locactual' => $feedbackEntity->getComIsdLocactual(),
            'isd_consent' => $feedbackEntity->getComIsdConsent(),
            'isd_dconsent_req' => $feedbackEntity->getComIsdDconsentReq(),
            'isd_dconsent_rec' => $feedbackEntity->getComIsdDconsentRec(),
            'isd_div_sent' => $feedbackEntity->getComIsdDivSent(),
            'isd_ref_added' => $feedbackEntity->getComIsdRefAdded(),
            'isd_iaas_involved' => $feedbackEntity->getComIsdIaasInvolved(),
            'isd_cas_involved' => $feedbackEntity->getComIsdCasInvolved(),
            'isd_chi_no' => $feedbackEntity->getComIsdChiNo(),
            'isd_resp_sent_20' => $feedbackEntity->getComIsdRespSent20(),
            'isd_resp_20_reason' => $feedbackEntity->getComIsdResp20Reason(),
            'isd_agree_40' => $feedbackEntity->getComIsdAgree40(),
            'isd_agree_40_date' => $feedbackEntity->getComIsdAgree40Date(),
            'isd_actions' => $feedbackEntity->getComIsdActions(),
            'isd_dexport' => $feedbackEntity->getComIsdDexport(),
            'rep_approved' => $feedbackEntity->getRepApproved(),
            'createdby' => $feedbackEntity->getCreatedby(),
            'show_person' => $feedbackEntity->getShowPerson(),
            'dcompleted1' => $feedbackEntity->getComDcompleted1(),
            'detail' => $feedbackEntity->getCOMDETAIL(),
            'summary' => $feedbackEntity->getCOMSUMMARY(),
            'reason' => $feedbackEntity->getCOMREASON(),
            'report' => $feedbackEntity->getCOMREPORT(),
            'irsynopsis' => $feedbackEntity->getCOMIRSYNOPSIS(),
            'recommend' => $feedbackEntity->getComRecommend(),
            'inv_action' => $feedbackEntity->getComInvAction(),
            'inv_leassons' => $feedbackEntity->getComInvLessons(),
            'isd_resp_40_reason' => $feedbackEntity->getComIsdResp40Reason(),
            'isd_plan' => $feedbackEntity->getComIsdPlan(),
            'notes1' => $feedbackEntity->getComNotes1(),
            'type_tier_one' => $feedbackEntity->getComTypeTierOne(),
            'type_tier_two' => $feedbackEntity->getComTypeTierTwo(),
            'type_tier_three' => $feedbackEntity->getComTypeTierThree(),
            'affecting_tier_zero' => $feedbackEntity->getComAffectingTierZero(),
            'issues_linked' => $feedbackEntity->getComIssuesLinked(),
            'subjects_linked' => $feedbackEntity->getComSubjectsLinked(),
            'location_id' => $feedbackEntity->getLocationId(),
            'service_id' => $feedbackEntity->getServiceId(),
            'location_id1' => $feedbackEntity->getLocationId1(),
            'service_id1' => $feedbackEntity->getServiceId1(),
            'flag_for_investigation' => $feedbackEntity->getFlagForInvestigation(),
            'flag_for_rib' => $feedbackEntity->getFlagForRib(),
            'other_location' => $feedbackEntity->getOtherLocation(),
            'other_service' => $feedbackEntity->getOtherService(),
            'subtype1' => $feedbackEntity->getComSubtype1(),
            'type1' => $feedbackEntity->getComType1(),
            'key_learnings' => $feedbackEntity->getKeyLearnings(),
            'learnings_to_share' => $feedbackEntity->getLearningsToShare(),
            'learnings_title' => $feedbackEntity->getLearningsTitle(),
            'requested_consultant' => $feedbackEntity->getRequestedConsultant(),
            'priority_scale' => $feedbackEntity->getPriorityScale(),
            'is_record_sensitive' => $feedbackEntity->getisRecordSensitive(),
            'com_lesson_learned_sub_category' => $feedbackEntity->getComLessonLearnedSubCategory(),
            'com_hro_characteristics' => $feedbackEntity->getComHroCharacteristics(),
            'com_specialty' => $feedbackEntity->getComSpecialty(),
            'mca_or_na' => $feedbackEntity->getMcaOrNa(),
            'source_of_record' => $feedbackEntity->getSourceOfRecord(),
            'patient_expectations' => $feedbackEntity->getComPatExpectations(),
            'patient_update_prefs' => $feedbackEntity->getComPatUpdatePref(),
            'grade_rating' => $feedbackEntity->getComGradeRating(),
            'referred_to_ombudsman' => $feedbackEntity->getReferredToOmbudsman(),
            'date_first_contact' => $feedbackEntity->getDateFirstContact(),
            'date_evidence_due' => $feedbackEntity->getDateEvidenceDue(),
            'date_evidence_submitted' => $feedbackEntity->getDateEvidenceSubmitted(),
            'ombudsman_reference' => $feedbackEntity->getOmbudsmanReference(),
            'ombudsman_handler' => $feedbackEntity->getOmbudsmanHandler(),
            'ombudsman_current_stage' => $feedbackEntity->getOmbudsmanCurrentStage(),
            'early_settlement_proposal' => $feedbackEntity->getEarlySettlementProposal(),
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED => $feedbackEntity->getEarlySettlementProposalDateReceived(),
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED => $feedbackEntity->getEarlySettlementProposalDateRequested(),
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED => $feedbackEntity->getEarlySettlementProposalDateSubmitted(),
            'date_investigation_began' => $feedbackEntity->getDateInvestigationBegan(),
            'date_response_due' => $feedbackEntity->getDateResponseDue(),
            'date_doc_inv_sub' => $feedbackEntity->getDateDocInvSub(),
            'date_inv_sub' => $feedbackEntity->getDateInvSub(),
            'date_draft_received' => $feedbackEntity->getDateDraftReceived(),
            'date_draft_response_due' => $feedbackEntity->getDateDraftResponseDue(),
            'date_rec_recieved' => $feedbackEntity->getDateRecRecieved(),
            'date_action_plan' => $feedbackEntity->getDateActionPlan(),
            'date_draft_report_sub' => $feedbackEntity->getDateDraftReportSub(),
            'date_report_received' => $feedbackEntity->getDateReportReceived(),
            'final_rep_type' => $feedbackEntity->getFinalRepType(),
            'ombudsman_outcome' => $feedbackEntity->getOmbudsmanOutcome(),
            'ombudsman_learning' => $feedbackEntity->getOmbudsmanLearning(),
            'com_outcome_grading' => $feedbackEntity->getComOutcomeGrading(),
            'com_serious_incident' => $feedbackEntity->getComSeriousIncident(),
            'com_welsh_language' => $feedbackEntity->getComWelshLanguage(),
            'outbreak_impact' => $feedbackEntity->getOutbreakImpact(),
            'outbreak_type' => $feedbackEntity->getOutbreakType(),
            'com_redress_escalated' => $feedbackEntity->getComRedressEscalated(),
        ];

        return $this->setAllEmptyValuesToNull($feedbackResponseArray);
    }

    protected function setAllEmptyValuesToNull($contactResponseArray)
    {
        array_walk_recursive($contactResponseArray, function (&$value) {
            if ($value === '') {
                $value = null;
            }
        });

        return $contactResponseArray;
    }
}
