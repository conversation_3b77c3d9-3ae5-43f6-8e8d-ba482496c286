<?php

namespace app\models\feedback\hydrators;

use app\models\feedback\entities\FeedbackLinksEntity;
use app\services\carlton\generic\DateAdapter;
use src\framework\registry\Registry;

/** @codeCoverageIgnore  */
class FeedbackLinksHydrator
{
    protected $dateAdapter;
    protected $registry;

    public function __construct(DateAdapter $dateAdapter, Registry $registry)
    {
        $this->dateAdapter = $dateAdapter;
        $this->registry = $registry;
    }

    public function hydrate(array $contactData, array $data, $contact)
    {
        // Populate complainant chain
        $contactData['lcom_dreceived'] = date('Y-m-d H:i:s.000');
        require_once __DIR__ . '/../../../../Source/generic_modules/COM/ModuleFunctions.php';
        $calculatedDates = CalculateComplaintDates([
            'date_received' => ($this->registry->getParm('FMT_DATE_WEB')->is('US') ? date('m/d/Y') : date('d/m/Y')),
            'complaint_recordid' => $data['feedbackId'],
        ]);
        $data = array_merge($data, $calculatedDates);

        $feedbackLinkEntity = (new FeedbackLinksEntity())
            ->setFeedback($data['feedbackEntity'])
            ->setRecordid($data['id'])
            ->setLcomDreceived($this->dateAdapter->adaptFromRequest($contactData['lcom_dreceived'] ?: ['date' => '']))
            ->setLcomDack($this->dateAdapter->adaptFromRequest($contactData['lcom_dack'] ?: ['date' => '']))
            ->setLcomDactioned($this->dateAdapter->adaptFromRequest($contactData['lcom_dactioned'] ?: ['date' => '']))
            ->setLcomDhold1($this->dateAdapter->adaptFromRequest($contactData['lcom_dhold1'] ?: ['date' => '']))
            ->setLcomDresponse($this->dateAdapter->adaptFromRequest($contactData['lcom_dresponse'] ?: ['date' => '']))
            ->setLcomDholding($this->dateAdapter->adaptFromRequest($contactData['lcom_dholding'] ?: ['date' => '']))
            ->setLcomDreplied($this->dateAdapter->adaptFromRequest($contactData['lcom_dreplied'] ?: ['date' => '']))
            ->setLcomDreopened($this->dateAdapter->adaptFromRequest($contactData['lcom_dreopened'] ?: ['date' => '']))
            ->setLcomIscomplpat($contactData['lcom_iscomplpat'])
            ->setLcomCurrent($contactData['lcom_current'])
            ->setLcomPrimary($contactData['lcom_primary'])
            ->setLcomDetails($contactData['lcom_details'])
            ->setLcomDdueack($this->dateAdapter->adaptFromRequest($data['lcom_ddueack'] ?: ['date' => '']))
            ->setLcomDdueact($this->dateAdapter->adaptFromRequest($data['lcom_ddueact'] ?: ['date' => '']))
            ->setLcomDduehold1($this->dateAdapter->adaptFromRequest($data['lcom_dduehold1'] ?: ['date' => '']))
            ->setLcomDdueresp($this->dateAdapter->adaptFromRequest($data['lcom_ddueresp'] ?: ['date' => '']))
            ->setLcomDduehold($this->dateAdapter->adaptFromRequest($data['lcom_dduehold'] ?: ['date' => '']))
            ->setLcomDduerepl($this->dateAdapter->adaptFromRequest($data['lcom_dduerepl'] ?: ['date' => '']))
            ->setLcomReopen($contactData['lcom_reopen'])
            ->setLcomReasonForReopen($contactData['lcom_reason_for_reopen'])
            ->setContact($contact);

        return $feedbackLinkEntity;
    }
}
