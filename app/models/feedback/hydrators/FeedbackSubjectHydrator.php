<?php

namespace app\models\feedback\hydrators;

use app\models\feedback\entities\FeedbackEntity;
use app\models\feedback\entities\FeedbackSubjectEntity;
use app\services\carlton\generic\DateAdapter;
use src\complaints\model\FeedbackFields;

/** @codeCoverageIgnore  */
class FeedbackSubjectHydrator
{
    protected $dateAdapter;

    public function __construct(DateAdapter $dateAdapter)
    {
        $this->dateAdapter = $dateAdapter;
    }

    /**
     * @return FeedbackSubjectEntity
     */
    public function hydrate(array $subject, FeedbackEntity $feedbackEntity)
    {
        return (new FeedbackSubjectEntity())
            ->setRecordid($subject['id'])
            ->setListorder($subject['listorder'])
            ->setComSubject($subject['subject'])
            ->setComSubsubject($subject['subsubject'])
            ->setComStafftype($subject['stafftype'])
            ->setComOutcome($subject['outcome'])
            ->setComCh8Poc($subject['ch8Poc'])
            ->setComCh8Subject($subject['ch8Subject'])
            ->setCsuDcompleted($this->dateAdapter->adaptFromRequest($subject['dcompleted'] ?: ['date' => '']))
            ->setCsuNotes($subject['notes'])
            ->setComServiceArea($subject['servicearea'])
            ->setCsuLocationId($subject['location'])
            ->setCsuServiceId($subject['service'])
            ->setComSubtype($subject['subtype'])
            ->setComType($subject['type'])
            ->setCsuLevelOfHarm($subject['csu_level_of_harm'])
            ->setComIssuePathway($subject[FeedbackFields::COM_ISSUE_PATHWAY])
            ->setComIssueType($subject[FeedbackFields::COM_ISSUE_PATHWAY])
            ->setFeedback($feedbackEntity);
    }

    /**
     * @return mixed
     */
    public function extract(FeedbackSubjectEntity $feedbackSubjectEntity)
    {
        $feedbackSubjectResponseArray = [
            'listorder' => $feedbackSubjectEntity->getListorder(),
            'subject' => $feedbackSubjectEntity->getComSubject(),
            'subsubject' => $feedbackSubjectEntity->getComSubsubject(),
            'stafftype' => $feedbackSubjectEntity->getComStafftype(),
            'outcome' => $feedbackSubjectEntity->getComOutcome(),
            'ch8Poc' => $feedbackSubjectEntity->getComCh8Poc(),
            'ch8Subject' => $feedbackSubjectEntity->getComCh8Subject(),
            'dcompleted' => $feedbackSubjectEntity->getCsuDcompleted(),
            'notes' => $feedbackSubjectEntity->getCsuNotes(),
            'servicearea' => $feedbackSubjectEntity->getComServiceArea(),
            'location' => $feedbackSubjectEntity->getCsuLocationId(),
            'service' => $feedbackSubjectEntity->getCsuServiceId(),
            'subtype' => $feedbackSubjectEntity->getComSubtype(),
            'type' => $feedbackSubjectEntity->getComType(),
            'csu_level_of_harm' => $feedbackSubjectEntity->getCsuLevelOfHarm(),
            FeedbackFields::COM_ISSUE_PATHWAY => $feedbackSubjectEntity->getComIssuePathway(),
            FeedbackFields::COM_ISSUE_TYPE => $feedbackSubjectEntity->getComIssueType(),
        ];

        return $this->setAllEmptyValuesToNull($feedbackSubjectResponseArray);
    }

    /**
     * @param $contactLinksResponseArray
     *
     * @return mixed
     */
    protected function setAllEmptyValuesToNull($contactLinksResponseArray)
    {
        array_walk_recursive($contactLinksResponseArray, function (&$value) {
            if ($value === '') {
                $value = null;
            }
        });

        return $contactLinksResponseArray;
    }
}
