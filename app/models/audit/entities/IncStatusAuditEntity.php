<?php

namespace app\models\audit\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "inc_status_audit")]
class IncStatusAuditEntity
{
    #[Id]
    #[GeneratedValue(strategy: "IDENTITY")]
    #[Column(type: "integer")]
    private ?int $id = null;

    #[Column(type: "integer")]
    private ?int $recordId = null;

    #[Column(type: "string")]
    private ?string $login = null;

    #[Column(type: "string")]
    private ?string $date = null;

    #[Column(type: "string")]
    private ?string $status = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getRecordId(): ?int
    {
        return $this->recordId;
    }

    public function setRecordId(?int $recordId): self
    {
        $this->recordId = $recordId;

        return $this;
    }

    public function getDate(): ?string
    {
        return $this->date;
    }

    public function setDate(?string $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getLogin(): ?string
    {
        return $this->login;
    }

    public function setLogin(?string $login): self
    {
        $this->login = $login;

        return $this;
    }
}
