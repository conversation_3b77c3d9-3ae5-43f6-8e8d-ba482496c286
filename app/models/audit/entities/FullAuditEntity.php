<?php

namespace app\models\audit\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "full_audit")]
class FullAuditEntity
{
    #[Id]
    #[GeneratedValue(strategy: "IDENTITY")]
    #[Column(type: "integer")]
    private ?int $id = null;

    #[Column(type: "string")]
    private ?string $aud_module = null;

    #[Column(type: "integer")]
    private ?int $aud_record = null;

    #[Column(type: "string")]
    private ?string $aud_login = null;

    #[Column(type: "string")]
    private ?string $aud_date = null;

    #[Column(type: "string")]
    private ?string $aud_action = null;

    #[Column(type: "string")]
    private ?string $aud_detail = null;

    #[Column(type: "integer")]
    private ?int $aud_delegate_id = null;

    #[Column(type: "string")]
    private ?string $link_section = null;

    #[Column(type: "integer")]
    private ?int $link_id = null;

    public function getAudModule(): ?string
    {
        return $this->aud_module;
    }

    public function setAudModule(?string $aud_module): self
    {
        $this->aud_module = $aud_module;

        return $this;
    }

    public function getAudRecord(): ?int
    {
        return $this->aud_record;
    }

    public function setAudRecord(?int $aud_record): self
    {
        $this->aud_record = $aud_record;

        return $this;
    }

    public function getAudLogin(): ?string
    {
        return $this->aud_login;
    }

    public function setAudLogin(?string $aud_login): self
    {
        $this->aud_login = $aud_login;

        return $this;
    }

    public function getAudDate(): ?string
    {
        return $this->aud_date;
    }

    public function setAudDate(?string $aud_date): self
    {
        $this->aud_date = $aud_date;

        return $this;
    }

    public function getAudAction(): ?string
    {
        return $this->aud_action;
    }

    public function setAudAction(?string $aud_action): self
    {
        $this->aud_action = $aud_action;

        return $this;
    }

    public function getAudDetail(): ?string
    {
        return $this->aud_detail;
    }

    public function setAudDetail(?string $aud_detail): self
    {
        $this->aud_detail = $aud_detail;

        return $this;
    }

    public function getAudDelegateId(): ?int
    {
        return $this->aud_delegate_id;
    }

    public function setAudDelegateId(?int $aud_delegate_id): self
    {
        $this->aud_delegate_id = $aud_delegate_id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getLinkSection(): ?string
    {
        return $this->link_section;
    }

    public function setLinkSection(?string $link_section): self
    {
        $this->link_section = $link_section;

        return $this;
    }

    public function getLinkId(): ?int
    {
        return $this->link_id;
    }

    public function setLinkId(?int $link_id): self
    {
        $this->link_id = $link_id;

        return $this;
    }
}
