<?php

namespace app\models\audit\hydrators;

use app\models\audit\entities\IncStatusAuditEntity;

class IncStatusAuditHydrator
{
    public function hydrate(array $data): IncStatusAuditEntity
    {
        return (new IncStatusAuditEntity())
            ->setId($data['id'] ?? null)
            ->setRecordId($data['recordId'] ?? null)
            ->setLogin($data['login'] ?? null)
            ->setDate($data['date'] ?? null)
            ->setStatus($data['status'] ?? null);
    }

    public function extract(IncStatusAuditEntity $incStatusAuditEntity): array
    {
        return [
            'id' => $incStatusAuditEntity->getId(),
            'recordId' => $incStatusAuditEntity->getRecordId(),
            'login' => $incStatusAuditEntity->getLogin(),
            'date' => $incStatusAuditEntity->getDate(),
            'status' => $incStatusAuditEntity->getStatus(),
        ];
    }
}
