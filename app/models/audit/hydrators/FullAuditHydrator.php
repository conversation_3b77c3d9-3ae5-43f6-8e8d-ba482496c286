<?php

namespace app\models\audit\hydrators;

use app\models\audit\entities\FullAuditEntity;

class FullAuditHydrator
{
    public function hydrate(array $data): FullAuditEntity
    {
        return (new FullAuditEntity())
            ->setId($data['id'] ?? null)
            ->setAudModule($data['aud_module'] ?? null)
            ->setAudRecord($data['aud_record'] ?? null)
            ->setAudLogin($data['aud_login'] ?? null)
            ->setAudDate($data['aud_date'] ?? null)
            ->setAudAction($data['aud_action'] ?? null)
            ->setAudDetail($data['aud_detail'] ?? null)
            ->setAudDelegateId($data['aud_delegate_id'] ?? null)
            ->setLinkSection($data['link_section'] ?? null)
            ->setLinkId($data['link_id'] ?? null);
    }

    public function extract(FullAuditEntity $fullAuditEntity): array
    {
        return [
            'id' => $fullAuditEntity->getId(),
            'aud_module' => $fullAuditEntity->getAudModule(),
            'aud_record' => $fullAuditEntity->getAudRecord(),
            'aud_login' => $fullAuditEntity->getAudLogin(),
            'aud_date' => $fullAuditEntity->getAudDate(),
            'aud_action' => $fullAuditEntity->getAudAction(),
            'aud_detail' => $fullAuditEntity->getAudDetail(),
            'aud_delegate_id' => $fullAuditEntity->getAudDelegateId(),
            'link_section' => $fullAuditEntity->getLinkSection(),
            'link_id' => $fullAuditEntity->getLinkId(),
        ];
    }
}
