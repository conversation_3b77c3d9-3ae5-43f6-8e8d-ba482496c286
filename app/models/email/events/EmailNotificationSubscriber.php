<?php

declare(strict_types=1);

namespace app\models\email\events;

use app\models\email\EmailNotifiable;
use app\models\email\NewHandlerNotifiable;
use app\models\email\NewInvestigatorNotifiable;
use app\models\email\ReporterProgressNotifiable;
use app\services\email\ReporterUpdateNotificationServiceInterface;
use app\services\email\StaffChangeNotificationServiceInterface;
use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;

final class EmailNotificationSubscriber implements EventSubscriber
{
    private StaffChangeNotificationServiceInterface $staffChangeNotificationService;
    private ReporterUpdateNotificationServiceInterface $reporterUpdateNotificationService;

    public function __construct(
        StaffChangeNotificationServiceInterface $staffChangeNotificationService,
        ReporterUpdateNotificationServiceInterface $reporterUpdateNotificationService
    ) {
        $this->staffChangeNotificationService = $staffChangeNotificationService;
        $this->reporterUpdateNotificationService = $reporterUpdateNotificationService;
    }

    public function getSubscribedEvents(): array
    {
        return [
            Events::postUpdate,
        ];
    }

    public function postUpdate(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof EmailNotifiable) {
            return;
        }

        $data = $entity->getEmailTemplateData();
        $module = $entity->getModule();

        if ($entity instanceof NewHandlerNotifiable && $entity->handlerHasChanged()) {
            $this->staffChangeNotificationService->emailNewHandler(
                $entity->getNewHandler(),
                $data,
                $module,
            );
        }

        if ($entity instanceof NewInvestigatorNotifiable && $entity->investigatorsHaveChanged()) {
            $this->staffChangeNotificationService->emailNewInvestigators(
                $entity->getNewInvestigators(),
                $data,
                $module,
            );
        }

        if ($entity instanceof ReporterProgressNotifiable && $entity->shouldEmailProgressToReporters()) {
            $this->reporterUpdateNotificationService->emailUpdateToReporters(
                $data,
                $module,
            );
        }
    }
}
