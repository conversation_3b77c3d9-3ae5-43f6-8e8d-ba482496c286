<?php

namespace app\models\actions\entities;

use app\models\location\entities\LocationEntity;
use app\models\service\entities\ServiceEntity;
use app\models\user\entities\UserEntity;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "ca_actions")]
class ActionEntity
{
    /**
     * @OA\Property(property="id", type="integer", format="int32")
     */
    #[Id]
    #[Column(type: "integer")]
    private $recordid;

    /**
     * @OA\Property(
     *     property="meta",
     *     type="object",
     *     @OA\Property(property="recordModule", type="string"),
     *     @OA\Property(property="recordId", type="integer", format="int32"),
     * )
     */
    #[Column(type: "string")]
    private $act_module;

    #[Column(type: "integer")]
    private $act_cas_id;

    /**
     * @OA\Property(property="dueDate", type="string", format="date-time")
     */
    #[Column(type: "string")]
    private $act_ddue;

    /**
     * @OA\Property(property="title", type="string")
     */
    #[Column(type: "string")]
    private $act_descr;

    /**
     * @OA\Property(property="startDate", type="string", format="date-time")
     */
    #[Column(type: "string")]
    private $act_dstart;

    /**
     * @OA\Property(
     *     property="priority",
     *     type="object",
     *     @OA\Property(property="id", type="string"),
     * )
     */
    #[Column(type: "string")]
    private $act_priority;

    /**
     * @OA\Property(
     *     property="actionType",
     *     type="object",
     *     @OA\Property(property="id", type="string")
     * )
     */
    #[Column(type: "string")]
    private $type;

    /**
     * @OA\Property(property="status", type="object", @OA\Property(property="label", type="string"))
     */
    #[Column(type: "string")]
    private $status;

    /**
     * @OA\Property(property="description", type="string")
     */
    #[Column(type: "string")]
    private $act_synopsis;

    /**
     * @OA\Property(property="act_from_inits", type="string")
     */
    #[Column(type: "string")]
    private $act_from_inits;

    /**
     * @OA\Property(property="act_to_inits", type="string")
     */
    #[Column(type: "string")]
    private $act_to_inits;

    /**
     * @var Collection|ActionUserEntity[]
     *
     * @OA\Property(property="actionUsers", type="array", @OA\Items(ref="#/components/schemas/UserEntity"))
     */
    #[OneToMany(
        mappedBy: "action",
        targetEntity: ActionUserEntity::class,
        cascade: ["persist", "remove"],
        orphanRemoval: true
    )]
    private $actionUsers;

    /**
     * @OA\Property(property="location", type="object", @OA\Property(property="id", type="integer", format="int32"))
     */
    #[ManyToOne(targetEntity: LocationEntity::class)]
    private ?LocationEntity $location = null;

    /**
     * @var ServiceEntity|null
     *
     * @OA\Property(property="service", type="object", @OA\Property(property="id", type="integer", format="int32"))
     */
    #[ManyToOne(targetEntity: ServiceEntity::class)]
    private ?ServiceEntity $service = null;

    #[JoinColumn(
        name: "completed_by",
        referencedColumnName: "recordid",
        nullable: true
    )]
    #[ManyToOne(targetEntity: UserEntity::class)]
    private ?UserEntity $completed_by = null;

    #[Column(type: "string")]
    private $completed_date;

    /**
     * @return mixed
     */
    public function getRecordid()
    {
        return $this->recordid;
    }

    /**
     * @param mixed $recordid
     *
     * @return ActionEntity
     */
    public function setRecordid($recordid)
    {
        $this->recordid = $recordid;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActModule()
    {
        return $this->act_module;
    }

    /**
     * @param mixed $act_module
     *
     * @return ActionEntity
     */
    public function setActModule($act_module)
    {
        $this->act_module = $act_module;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActCasId()
    {
        return $this->act_cas_id;
    }

    /**
     * @param mixed $act_cas_id
     *
     * @return ActionEntity
     */
    public function setActCasId($act_cas_id)
    {
        $this->act_cas_id = $act_cas_id;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActDdue()
    {
        return $this->act_ddue;
    }

    /**
     * @param mixed $act_ddue
     *
     * @return ActionEntity
     */
    public function setActDdue($act_ddue)
    {
        $this->act_ddue = $act_ddue;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActDescr()
    {
        return $this->act_descr;
    }

    /**
     * @param mixed $act_descr
     *
     * @return ActionEntity
     */
    public function setActDescr($act_descr)
    {
        $this->act_descr = $act_descr;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActDstart()
    {
        return $this->act_dstart;
    }

    /**
     * @param mixed $act_dstart
     *
     * @return ActionEntity
     */
    public function setActDstart($act_dstart)
    {
        $this->act_dstart = $act_dstart;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActPriority()
    {
        return $this->act_priority;
    }

    /**
     * @param mixed $act_priority
     *
     * @return ActionEntity
     */
    public function setActPriority($act_priority)
    {
        $this->act_priority = $act_priority;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param mixed $status
     *
     * @return ActionEntity
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getActSynopsis()
    {
        return $this->act_synopsis;
    }

    /**
     * @param mixed $act_synopsis
     *
     * @return ActionEntity
     */
    public function setActSynopsis($act_synopsis)
    {
        $this->act_synopsis = $act_synopsis;

        return $this;
    }

    /**
     * @return Collection|ActionUserEntity[]
     */
    public function getActionUsers()
    {
        return $this->actionUsers;
    }

    /**
     * @param ActionUserEntity[] $actionUsers
     */
    public function setActionUsers(array $actionUsers)
    {
        $this->actionUsers = $actionUsers;

        return $this;
    }

    public function getActFromInits(): ?string
    {
        return $this->act_from_inits;
    }

    public function setActFromInits(?string $act_from_inits): self
    {
        $this->act_from_inits = $act_from_inits;

        return $this;
    }

    public function getActToInits(): ?string
    {
        return $this->act_to_inits;
    }

    public function setActToInits(?string $act_to_inits): self
    {
        $this->act_to_inits = $act_to_inits;

        return $this;
    }

    public function getLocation(): ?LocationEntity
    {
        return $this->location;
    }

    public function setLocation(?LocationEntity $location): self
    {
        $this->location = $location;

        return $this;
    }

    public function getService(): ?ServiceEntity
    {
        return $this->service;
    }

    public function setService(?ServiceEntity $service): self
    {
        $this->service = $service;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     *
     * @return ActionEntity
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection|ActionUserEntity
     */
    public function getCompletedBy(): ?UserEntity
    {
        return $this->completed_by;
    }

    public function setCompletedBy(?UserEntity $user): self
    {
        $this->completed_by = $user;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getCompletedDate()
    {
        return $this->completed_date;
    }

    /**
     * @param mixed $completed_date
     */
    public function setCompletedDate($completed_date): self
    {
        $this->completed_date = $completed_date;

        return $this;
    }
}
