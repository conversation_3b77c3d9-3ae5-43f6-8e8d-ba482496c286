<?php

namespace app\models\actions\entities;

use app\models\generic\entities\EntityInterface;
use app\models\generic\valueObjects\DateTimeString;
use app\models\generic\valueObjects\Module;
use app\models\generic\valueObjects\RecordID;

class Action implements EntityInterface
{
    /** @var RecordID */
    private $id;

    /** @var Module */
    private $module;

    /** @var RecordID */
    private $linkedRecord;

    /** @var */
    private $title;

    /** @var */
    private $summary;

    /** @var */
    private $assignedTo;

    /** @var */
    private $assignedBy;

    /** @var DateTimeString */
    private $startDate;

    /** @var DateTimeString */
    private $dueDate;

    /** @var */
    private $priority;

    /** @var */
    private $status;
    private $type;

    public function __construct(
        RecordID $id,
        Module $module,
        RecordID $linkedRecord,
        $title,
        $summary,
        $assignedTo,
        $assignedBy,
        DateTimeString $startDate,
        DateTimeString $dueDate,
        $priority,
        $status
    ) {
        $this->id = $id;
        $this->module = $module;
        $this->linkedRecord = $linkedRecord;
        $this->title = $title;
        $this->summary = $summary;
        $this->assignedTo = $assignedTo;
        $this->assignedBy = $assignedBy;
        $this->startDate = $startDate;
        $this->dueDate = $dueDate;
        $this->priority = $priority;
        $this->status = $status;
    }

    /**
     * @return RecordID[]
     */
    public function getId(): array
    {
        return [$this->id];
    }

    public function getModule()
    {
        return $this->module;
    }

    public function toArray()
    {
        return [
            'id' => $this->id->getId(),
            'module' => $this->module->getCode(),
            'linkedRecord' => $this->linkedRecord->getId(),
            'title' => $this->title,
            'summary' => $this->summary,
            'assignedTo' => $this->assignedTo,
            'assignedBy' => $this->assignedBy,
            'startDate' => $this->startDate->getValue(),
            'dueDate' => $this->dueDate->getValue(),
            'priority' => $this->priority,
            'type' => $this->type,
            'status' => $this->status,
        ];
    }
}
