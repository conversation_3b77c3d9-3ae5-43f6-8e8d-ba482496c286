<?php

namespace app\models\actions\entities;

use app\models\user\entities\UserEntity;
use Doctrine\ORM\EntityNotFoundException;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\GeneratedValue;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "ca_actions_user")]
class ActionUserEntity
{
    #[Id]
    #[GeneratedValue]
    #[Column(type: "integer")]
    protected ?int $id = null;

    /**
     * @var ActionEntity
     *
     */
    #[ManyToOne(targetEntity: ActionEntity::class)]
    #[JoinColumn(
        name: "action_id",
        referencedColumnName: "recordid",
        nullable: false
    )]
    protected ?ActionEntity $action = null;

    #[ManyToOne(targetEntity: UserEntity::class)]
    #[JoinColumn(
        name: "assigned_by_id",
        referencedColumnName: "recordid",
        nullable: true
    )]
    protected ?UserEntity $assignedBy;

    #[ManyToOne(targetEntity: UserEntity::class)]
    #[JoinColumn(
        name: "assigned_to_id",
        referencedColumnName: "recordid",
        nullable: false
    )]
    protected ?UserEntity $assignedTo = null;

    public function getId(): int
    {
        return $this->id;
    }

    public function getAction(): ActionEntity
    {
        return $this->action;
    }

    public function setAction(ActionEntity $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getAssignedBy(): ?UserEntity
    {
        return $this->assignedBy;
    }

    public function setAssignedBy(?UserEntity $assignedBy): self
    {
        $this->assignedBy = $assignedBy;

        return $this;
    }

    public function getAssignedTo(): UserEntity
    {
        return $this->assignedTo;
    }

    public function setAssignedTo(UserEntity $assignedTo): self
    {
        $this->assignedTo = $assignedTo;

        return $this;
    }

    public function getAssignedByUsersName(): string
    {
        $assignedBy = $this->getAssignedBy();

        try {
            return $assignedBy->getUseForenames() . ' ' . $assignedBy->getUseSurname();
        } catch (EntityNotFoundException $exception) {
            return _fdtk('act_by_not_found') . $this->getId();
        }
    }

    public function getAssignedToUsersName(): string
    {
        $assignedTo = $this->getAssignedTo();

        try {
            return $assignedTo->getUseForenames() . ' ' . $assignedTo->getUseSurname();
        } catch (EntityNotFoundException $exception) {
            return _fdtk('act_to_not_found') . $this->getId();
        }
    }
}
