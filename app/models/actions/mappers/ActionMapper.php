<?php

namespace app\models\actions\mappers;

use app\models\actions\entities\Action;
use app\models\generic\mappers\MapperAbstract;

/**
 * @codeCoverageIgnore
 */
class ActionMapper extends MapperAbstract
{
    /**
     * @return array
     *
     * @codeCoverageIgnore
     */
    public function map(Action $action)
    {
        $actionArray = $action->toArray();

        return [
            'recordid' => $actionArray['id'],
            'act_module' => $actionArray['module'],
            'act_cas_id' => $actionArray['linkedRecord'],
            'act_descr' => $actionArray['title'],
            'act_synopsis' => $actionArray['summary'],
            'act_dstart' => $actionArray['startDate'],
            'act_ddue' => $actionArray['dueDate'],
            'act_priority' => $actionArray['priority'],
            'type' => $actionArray['type'],
            'status' => $actionArray['status'],
        ];
    }

    /**
     * @return string
     *
     * @codeCoverageIgnore
     */
    public function getTable()
    {
        return 'ca_actions';
    }

    /**
     * @return array
     *
     * @codeCoverageIgnore
     */
    public function getPrimaryKey()
    {
        return ['recordid'];
    }
}
