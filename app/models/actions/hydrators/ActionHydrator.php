<?php

namespace app\models\actions\hydrators;

use app\models\actions\entities\ActionEntity;
use app\models\actions\entities\ActionUserEntity;
use app\models\location\entities\LocationEntity;
use app\models\service\entities\ServiceEntity;
use app\models\user\entities\UserEntity;
use app\services\action\ActionUserAdaptor;
use app\services\carlton\generic\DateAdapter;
use Doctrine\ORM\EntityManager;
use Exception;

class ActionHydrator
{
    /** @var DateAdapter */
    protected $dateAdapter;

    /** @var ActionUserAdaptor */
    private $actionUserAdaptor;

    /** @var EntityManager */
    private $em;

    public function __construct(DateAdapter $dateAdapter, ActionUserAdaptor $actionUserAdaptor, EntityManager $em)
    {
        $this->dateAdapter = $dateAdapter;
        $this->actionUserAdaptor = $actionUserAdaptor;
        $this->em = $em;
    }

    /**
     * @return ActionEntity
     *
     * @throws Exception
     */
    public function hydrate(array $data)
    {
        $action = (new ActionEntity())
            ->setRecordid($data['id'])
            ->setActModule($data['meta']['recordModule'])
            ->setActCasId($data['meta']['recordId'])
            ->setActDdue($this->dateAdapter->adaptFromRequest($data['dueDate']))
            ->setActDescr($data['title'])
            ->setActDstart($this->dateAdapter->adaptFromRequest($data['startDate']))
            ->setActPriority($data['priority']['id'])
            ->setStatus($data['status']['label'])
            ->setActSynopsis($data['description'])
            ->setType($data['actionType']['id']);

        $actionUsers = $this->actionUserAdaptor->adaptUsers($data['actionUsers'], $action);
        $action->setActionUsers($actionUsers);

        // Store action users in the Old School format to facilitate searching
        $assignedByInitials = [];
        $assignedToInitials = [];

        /** @var ActionUserEntity $actionUser */
        foreach ($actionUsers as $actionUser) {
            $assignedBy = $actionUser->getAssignedBy();
            if ($assignedBy) {
                $assignedByInitials[] = $assignedBy->getInitials();
            }
            $assignedToInitials[] = $actionUser->getAssignedTo()->getInitials();
        }

        $data['act_from_inits'] = implode(' ', array_unique($assignedByInitials));
        $data['act_to_inits'] = implode(' ', array_unique($assignedToInitials));

        $action->setActFromInits($data['act_from_inits'])
            ->setActToInits($data['act_to_inits']);

        if (isset($data['location']['id'])) {
            $location = $this->em->getRepository(LocationEntity::class)->find($data['location']['id']);
            $action->setLocation($location);
        }

        if (isset($data['service']['id'])) {
            $service = $this->em->getRepository(ServiceEntity::class)->find($data['service']['id']);
            $action->setService($service);
        }

        if ($data['status']['label'] === 'ACTIONS.FORM.FIELDS.STATUS.OPTIONS.COMPLETED') {
            $action->setCompletedDate($data['completed']);
            if (isset($data['completedBy']['identity']['id'])) {
                $completedBy = $this->em->getRepository(UserEntity::class)->find($data['completedBy']['identity']['id']);
                $action->setCompletedBy($completedBy);
            }
        }

        return $action;
    }

    /**
     * @return array $data
     */
    public function extract(ActionEntity $actionEntity)
    {
        $action = [
            'id' => $actionEntity->getRecordid(),
            'dueDate' => $actionEntity->getActDdue(),
            'title' => $actionEntity->getActSynopsis(),
            'startDate' => $actionEntity->getActDstart(),
            'priority' => [
                'id' => (int) $actionEntity->getActPriority(),
            ],
            'type' => [
                'id' => (int) $actionEntity->getType(),
            ],
            'status' => $actionEntity->getStatus(),
            'description' => $actionEntity->getActDescr(),
            'meta' => [
                'recordModule' => $actionEntity->getActModule(),
                'recordId' => $actionEntity->getActCasId(),
            ],
        ];

        $location = $actionEntity->getLocation();
        if ($location) {
            $action['location'] = ['id' => $location->getId()];
        }

        $service = $actionEntity->getService();
        if ($service) {
            $action['service'] = ['id' => $service->getId()];
        }

        return $action;
    }
}
