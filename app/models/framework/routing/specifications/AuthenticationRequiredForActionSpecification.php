<?php

declare(strict_types=1);

namespace app\models\framework\routing\specifications;

use src\inactivitytimeout\controllers\LoggedOutDueToInactivityController;

use function in_array;

class AuthenticationRequiredForActionSpecification
{
    private const PUBLIC_ACTIONS = [
        '',
        'httprequest',
        'savedocumentmultipartbegin',
        'savedocumentmultipartchunk',
        'savedocumentmultipartend',
        'saveincident',
        'saverisk',
        'saverecord',
        'incident',
        'risk',
        'record',
        'fieldhelp',
        'clearhotspotqueue',
        'processhotspotsqueue',
        'sessionlost',
        'changelanguage',
        'treefieldfilter',
        'classificationfilter',
        'gettagsbyid',
        'treefieldsuggestions',
        'classificationfieldfilter',
        LoggedOutDueToInactivityController::ACTION_NOTICE,
        'getlocalauthorityfile',
        'showlocalauthorityform',
    ];

    public function isSatisfiedBy(string $action): bool
    {
        return !in_array(strtolower($action), self::PUBLIC_ACTIONS, true);
    }
}
