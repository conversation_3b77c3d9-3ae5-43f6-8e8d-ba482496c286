<?php

namespace app\models\framework\modules;

class PrinceModule implements ModuleInterface
{
    protected $title;
    protected $code;
    protected $menuPosition;
    protected $moduleType;
    protected $enabled;
    protected $saleUrl;
    protected $moduleUrl;
    protected bool $openInNewTab;

    /**
     * @param $title
     * @param $code
     * @param $moduleType
     * @param $menuPosition
     * @param $enabled
     * @param $saleUrl
     *
     * @internal param $salesUrl
     */
    public function __construct($title, $code, $moduleType, $menuPosition, $enabled, $saleUrl, $openInNewTab = false)
    {
        $this->title = $title;
        $this->code = $code;
        $this->moduleType = $moduleType;
        $this->menuPosition = $menuPosition;
        $this->enabled = $enabled;
        $this->saleUrl = $saleUrl;
        $this->openInNewTab = $openInNewTab;
    }

    /**
     * @return string
     */
    public function getUri()
    {
        if ($this->code === 'ICO') {
            return $this->enabled ? getenv('IQ_BASE_URL') . '/iconwall/' : $this->saleUrl;
        }

        return $this->enabled ? '?action=home&module=' . $this->code : $this->saleUrl;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    public function getLabel()
    {
        // Function required for interface. Currently not called
        return $this->title;
    }

    /**
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @return int
     */
    public function getMenuPosition()
    {
        return $this->menuPosition;
    }

    /**
     * @return string
     */
    public function getModuleType()
    {
        return $this->moduleType;
    }

    public function isEnabled(): bool
    {
        return $this->enabled == 1;
    }

    public function shouldOpenInNewTab(): bool
    {
        return $this->openInNewTab;
    }
}
