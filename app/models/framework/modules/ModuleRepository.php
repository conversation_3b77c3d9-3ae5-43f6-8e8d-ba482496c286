<?php

namespace app\models\framework\modules;

use app\models\framework\config\DatixConfig;
use Doctrine\DBAL\Connection;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use src\admin\services\JwtService;
use src\framework\session\Session;
use src\framework\session\UserSession;
use src\system\language\LanguageSession;
use JsonException;

use function array_filter;
use function array_merge;
use function in_array;

use const JSON_THROW_ON_ERROR;

class ModuleRepository
{
    public const USER_MODULES_KEY = 'user';
    private const DEFAULT_SALE_URL = 'http://www.datix.co.uk/iq/';
    private const MODULE_HOTSPOTS = 'hotspots';

    /** @var ModuleInterface[]|null */
    protected ?array $modules = null;

    /** @readonly  */
    protected DatixConfig $datixConfig;

    /** @readonly */
    protected Client $carltonClient;

    /** @readonly  */
    protected LanguageSession $languageSession;

    /** @readonly */
    protected Session $session;
    protected array $carltonNavigationMenu = [];

    /** @readonly */
    private UserSession $userSession;

    /** @readonly */
    private Connection $connection;

    /** @readonly */
    private JwtService $jwtService;

    public function __construct(
        DatixConfig $datixConfig,
        Client $carltonClient,
        LanguageSession $languageSession,
        Session $session,
        Connection $connection,
        UserSession $userSession,
        JwtService $jwtService
    ) {
        $this->datixConfig = $datixConfig;
        $this->carltonClient = $carltonClient;
        $this->languageSession = $languageSession;
        $this->session = $session;
        $this->connection = $connection;
        $this->userSession = $userSession;
        $this->jwtService = $jwtService;
    }

    /**
     * @throws GuzzleException
     * @throws JsonException
     */
    public function initModules()
    {
        $modulePermissions = null;
        if (isset($_COOKIE['datix_jwtPerms'])) {
            $modulePermissions = $this->jwtService->decode($_COOKIE['datix_jwtPerms']);
        }

        $this->modules = [];
        foreach ($this->getModuleData() as $module) {
            if ($module->key === 'reporting' && $module->enabled) { // TODO Filthy hardcoded implementation required until Carlton APIs refactored on 13/1/20
                $this->modules[] = new CarltonModule(
                    _fdtk('mod_reporting_title'),
                    'RPT',
                    'coredata',
                    getenv('REPORTING_URL'),
                    9,
                    1,
                    true,
                );
            } elseif ($module->key === 'mySettings') {
                $userId = $this->userSession->getCurrentUser()->recordid;
                $url = str_replace('{recordId}', $userId, $module->url);
                $this->modules[] = new CarltonModule(
                    _fdtk('mod_my_settings_title'),
                    null,
                    self::USER_MODULES_KEY,
                    $url,
                    3,
                    1,
                );
            } else {
                $title = $this->languageSession->getStaticString($module->key);
                $moduleCode = $module->meta->moduleCode ?? null;
                $moduleType = $module->meta->moduleNavigationType ?? null;
                $moduleNavigationPosition = $module->meta->moduleNavigationPosition ?? null;
                $enabled = $module->access;

                if ($this->isPrinceModule($moduleType)) {
                    $saleUrl = $this->generateSaleUrl($module->meta->saleUrl ?? null);

                    $this->modules[] = new PrinceModule(
                        $title,
                        $moduleCode,
                        $moduleType,
                        $moduleNavigationPosition,
                        $enabled,
                        $saleUrl,
                    );
                } else {
                    if ($moduleCode === 'CPL' && in_array(getenv('POLICIES_AND_GUIDELINES_URL'), [false, ''], true)) {
                        continue;
                    }

                    $moduleUrl = $this->getCarltonUrl($module->meta->moduleNavigationUrl ?? '', $moduleCode);
                    if ($module->key === self::MODULE_HOTSPOTS) {
                        if (!getenv('HOTSPOTS_URL') || ($modulePermissions && $modulePermissions["hotspots"]->is_admin === false)) {
                            continue;
                        }
                        $moduleUrl = getenv('IQ_BASE_URL') . '/' . getenv('HOTSPOTS_URL');
                    }
                    $this->modules[] = new CarltonModule(
                        $title,
                        $moduleCode,
                        $moduleType,
                        $moduleUrl,
                        $moduleNavigationPosition,
                        $enabled,
                    );
                }
            }
        }

        $this->modules = array_merge(
            $this->modules,
            $this->getRemainingPrinceModules(),
            $this->getRemainingCarltonModules(),
        );
    }

    /**
     * @return ModuleInterface[]
     */
    public function getModules(): array
    {
        if ($this->modules === null) {
            $this->initModules();
        }

        return $this->modules;
    }

    /**
     * @return string[]
     */
    public function getEnabledCaptureModuleCodes(): array
    {
        return array_map(
            static fn (ModuleInterface $module): string => $module->getCode(),
            $this->getCaptureModules(),
        );
    }

    /**
     * @return ModuleInterface[]
     */
    public function getCaptureModules(): array
    {
        return $this->getModuleByType(['capture']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getEvaluateModules(): array
    {
        return $this->getModuleByType(['evaluate']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getImplementModules(): array
    {
        return $this->getModuleByType(['implement']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getStrategyModules(): array
    {
        return $this->getModuleByType(['strategy']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getAssessModules(): array
    {
        return $this->getModuleByType(['assess']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getCoreDataModules(): array
    {
        $this->getCarltonAdditionalModules();

        return $this->getModuleByType(['coredata', 'generic']);
    }

    /**
     * @return ModuleInterface[]
     */
    public function getUserModules(): array
    {
        return $this->getModuleByType([self::USER_MODULES_KEY]);
    }

    /**
     * @return PrinceModule[]
     */
    public function getPrinceModules(): array
    {
        return array_filter(
            $this->getModules(),
            static fn (ModuleInterface $module) => $module instanceof PrinceModule,
        );
    }

    /**
     * @return CarltonModule[]
     */
    public function getCarltonModules(): array
    {
        return array_filter(
            $this->getModules(),
            static fn (ModuleInterface $module) => $module instanceof CarltonModule,
        );
    }

    /**
     * @param string $code
     *
     * @throws Exception
     */
    public function getModuleByCode($code): ModuleInterface
    {
        foreach ($this->getModules() as $module) {
            if ($module->getCode() === $code) {
                return $module;
            }
        }

        throw new Exception('Invalid module code');
    }

    /**
     * @param string $moduleCode
     */
    public function princeModuleExists($moduleCode): bool
    {
        return in_array($moduleCode, $this->getPrinceModuleCodes(), true);
    }

    public function carltonModuleExists(string $moduleCode): bool
    {
        foreach ($this->getCarltonModules() as $carltonModule) {
            if ($moduleCode === $carltonModule->getCode()) {
                return true;
            }
        }

        return false;
    }

    public function moduleExists(string $moduleCode): bool
    {
        foreach ($this->getModules() as $module) {
            if ($module->getCode() === $moduleCode) {
                return true;
            }
        }

        return false;
    }

    public function setCarltonNavigationMenu(array $carltonMenu): void
    {
        $this->carltonNavigationMenu = $carltonMenu;
    }

    /**
     * @psalm-type MOD_KEY array{mod_module: string, mod_id: string}
     *
     * @return array<MOD_KEY>
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getModuleIdsByCode(): array
    {
        return $this->connection->executeQuery('SELECT mod_module, mod_id FROM modules')->fetchAllKeyValue();
    }

    /**
     * Sorts module array by menu position.
     */
    protected function sortByMenuPosition(ModuleInterface $a, ModuleInterface $b): int
    {
        return $a->getMenuPosition() - $b->getMenuPosition();
    }

    /**
     * @return string[]
     */
    protected function getPrinceModuleCodes(): array
    {
        return array_map(
            static fn (ModuleInterface $moduleObject) => $moduleObject->getCode(),
            $this->getPrinceModules(),
        );
    }

    /**
     * @return ModuleInterface[]
     */
    private function getModuleByType(array $type): array
    {
        $modules = array_filter($this->getModules(), static function (ModuleInterface $module) use ($type) {
            return in_array($module->getModuleType(), $type, true) && $module->isEnabled();
        });

        usort($modules, [$this, 'sortByMenuPosition']);

        return $modules;
    }

    /**
     * @return PrinceModule[]
     */
    private function getRemainingPrinceModules(): array
    {
        $saleUrl = $this->datixConfig->getCarltonBaseUrl() . 'redirect?url=' . urlencode(self::DEFAULT_SALE_URL);

        return [
            new PrinceModule(_fdtk('mod_organisations_title'), 'ORG', 'coredata', 6, 1, $saleUrl),
            new PrinceModule(_fdtk('mod_insurance_title'), 'POL', 'coredata', 7, 1, $saleUrl),
            new PrinceModule(_fdtk('mod_payments_title'), 'PAY', 'coredata', 8, 1, $saleUrl),
            new PrinceModule(_fdtk('mod_capture_admin_title'), 'ADM', 'none', 0, 1, $saleUrl),
        ];
    }

    /**
     * @return CarltonModule[]
     */
    private function getRemainingCarltonModules(): array
    {
        $baseUri = $this->datixConfig->getCarltonBaseUrl();

        return [
            new CarltonModule(_fdtk('mod_actions_title'), 'ACT', 'coredata', $baseUri . 'actions', 0, 1),
            new CarltonModule(_fdtk('mod_distlists_title'), 'DIS', 'coredata', $baseUri . 'distribution-lists', 1, 1),
            new CarltonModule(_fdtk('mod_contacts_title'), 'CON', 'coredata', $baseUri . 'contacts', 4, 1),
            new CarltonModule(_fdtk('mod_surveys_title'), 'SUR', 'coredata', $baseUri . 'surveys/templates', 5, 1),
            new CarltonModule(_fdtk('system_admin_title'), 'ADM', 'coredata', $baseUri . 'admin', 10, 1),
            new CarltonModule(_fdtk('mod_dashboard_title'), 'DAS', 'none', $baseUri . 'dashboard', 112, 1),
            new CarltonModule(_fdtk('mod_devices_title'), 'DEV', 'none', $baseUri . 'devices', 202, 1),
            new CarltonModule(_fdtk('mod_locations_title'), 'LOC', 'none', $baseUri . 'locations', 206, 1),
            new CarltonModule(_fdtk('mod_services_title'), 'SER', 'none', $baseUri . 'services', 207, 1),
            new CarltonModule(_fdtk('mod_users_title'), 'USE', 'none', $baseUri . 'users', 210, 1),
        ];
    }

    private function isPrinceModule(?string $moduleType): bool
    {
        return in_array($moduleType, ['capture', 'coredata']);
    }

    private function generateSaleUrl(?string $saleUrl): string
    {
        $baseUri = $this->datixConfig->getCarltonBaseUrl() . 'redirect?url=';

        return isset($saleUrl)
            ? $baseUri . urlencode($saleUrl)
            : $baseUri . urlencode(self::DEFAULT_SALE_URL);
    }

    /**
     * API calls to Carlton are expensive, cache the results in to the session.
     *
     * @return mixed
     *
     * @throws JsonException
     * @throws GuzzleException
     */
    private function getModuleData(): array
    {
        if (!$this->session->has('cache', 'modules')) {
            $userId = $this->userSession->getCurrentUser()->recordid;
            $currentUrl = $this->datixConfig->getScriptUrl() . substr("{$_SERVER['REQUEST_URI']}", 1);
            $response = $this->carltonClient->get(
                '/modules/navigation',
                [
                    RequestOptions::QUERY => [
                        'userId' => $userId,
                        'currentUrl' => urlencode($currentUrl),
                    ],
                ],
            );
            $modules = json_decode($response->getBody(), false, 512, JSON_THROW_ON_ERROR);

            $this->session->set('cache', 'modules', $modules);
        }

        return $this->session->get('cache', 'modules');
    }

    private function getCarltonUrl(string $moduleUrl, ?string $moduleCode): string
    {
        if ($moduleCode === 'CPL') {
            return getenv('POLICIES_AND_GUIDELINES_URL');
        }

        return $this->datixConfig->getCarltonBaseUrl() . $moduleUrl;
    }

    private function getSubscriptionsLink($moduleCode): string
    {
        $carltonMenu = $this->getCarltonNavigationMenu();
        $filteredList = array_filter($carltonMenu['data']['cdm'], static function ($k) use ($moduleCode) {
            return ($k['key'] ?? null) === $moduleCode;
        });
        $menu = reset($filteredList);

        if (!empty($menu)) {
            return $menu['url'];
        }

        $medicationsPath = rtrim(getenv('MEDICATIONS_PATH'), '/');
        if ($moduleCode == 'equipment') {
            if ($this->datixConfig->getEquipmentsV2Enabled()) {
                return rtrim(getenv('IQ_BASE_URL'), '/') . '/' . $medicationsPath . '/#/equipmentgateway';
            }

            return $this->datixConfig->getCarltonBaseUrl() . 'equipment';
        }

        if ($medicationsPath) {
            return rtrim(getenv('IQ_BASE_URL'), '/') . '/' . $medicationsPath . '/#/medsgateway';
        }

        return $this->datixConfig->getCarltonBaseUrl() . 'medications';
    }

    private function getCarltonNavigationMenu(): array
    {
        return $this->carltonNavigationMenu;
    }

    private function getCarltonAdditionalModules(): void
    {
        $this->modules = array_merge($this->modules, [
            new CarltonModule(_fdtk('mod_medications_title'), 'MED', 'coredata', $this->getSubscriptionsLink('medications'), 2, 1),
            new CarltonModule(_fdtk('mod_equipment_title'), 'EQU', 'coredata', $this->getSubscriptionsLink('equipments'), 3, 1),
        ]);
    }
}
