<?php

namespace app\models\framework\modules;

use Exception;
use src\users\model\User;

use function in_array;

class UserAccessibleModules
{
    /** @var User */
    protected $user;

    /** @var ModuleRepository[] */
    private $moduleRepository;

    public function __construct(UserInterface $user, ModuleRepository $moduleRepository)
    {
        $this->user = $user;
        $this->moduleRepository = $moduleRepository;
    }

    /**
     * @return ModuleInterface[]
     */
    public function getModules()
    {
        $filter = function (PrinceModule $module) {
            return $this->user->canSeeModule($module->getCode());
        };

        $accessiblePrinceModules = array_filter($this->moduleRepository->getPrinceModules(), $filter);

        // TODO confirm that only accessible carlton modules are passed through
        $modules = array_merge($accessiblePrinceModules, $this->moduleRepository->getCarltonModules());

        usort($modules, static function (ModuleInterface $a, ModuleInterface $b): int {
            return $a->getMenuPosition() <=> $b->getMenuPosition();
        });

        return $modules;
    }

    /**
     * @param string $dropdownName
     *
     * @return ModuleInterface[]
     *
     * @throws Exception
     */
    public function getModulesForDropdown($dropdownName)
    {
        if (!in_array($dropdownName, ['capture', 'evaluate', 'strategy', 'implement', 'assess', 'none', 'coredata'])) {
            throw new Exception("Invalid module type {$dropdownName}");
        }

        $dropdownModules = [];
        foreach ($this->getModules() as $module) {
            if ($module->getModuleType() === $dropdownName) {
                $dropdownModules[] = $module;
            }
        }

        return $dropdownModules;
    }
}
