<?php

namespace app\models\framework\modules;

/**
 * @codeCoverageIgnore
 */
class CarltonModule implements ModuleInterface
{
    protected $title;
    protected $code;
    protected $uri;
    protected $menuPosition;
    protected $moduleType;
    protected $enabled;
    protected bool $openInNewTab;

    public function __construct($title, $code, $moduleType, $uri, $menuPosition, $enabled, $openInNewTab = false)
    {
        $this->title = $title;
        $this->code = $code;
        $this->uri = $uri;
        $this->menuPosition = $menuPosition;
        $this->moduleType = $moduleType;
        $this->enabled = $enabled;
        $this->openInNewTab = $openInNewTab;
    }

    /**
     * @return string
     */
    public function getUri()
    {
        return $this->uri;
    }

    /**
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    public function getLabel()
    {
        // Function required for interface. Currently not called
        return $this->title;
    }

    /**
     * @return string
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @return int
     */
    public function getMenuPosition()
    {
        return $this->menuPosition;
    }

    /**
     * @return string
     */
    public function getModuleType()
    {
        return $this->moduleType;
    }

    public function isEnabled(): bool
    {
        return $this->enabled == 1;
    }

    public function shouldOpenInNewTab(): bool
    {
        return $this->openInNewTab;
    }
}
