<?php

namespace app\models\framework\modules;

use Exception;

use function in_array;

class CurrentModule
{
    /** @var string */
    protected $module;

    /** @var string */
    protected $action;

    /** @var ModuleRepository */
    protected $moduleRepository;

    /** @var string[] */
    protected $actionsToUseModuleTitle = [
        'home',
        'record',
        'savedqueries',
        'incidentssearch',
        'reportdesigner',
        'listmyreports',
        'addnewincident,',
        'list',
        'search',
        'incident',
        'batchdelete',
        'batchupdate',
        'copyrecord',
        'addnew',
        'generaterecord',
        'editlinkedrecord',
        'editdocument',
        'linkcontactgeneral',
        'newcontact',
        'newdif1',
        'dashboard',
        'addnewincident',
        'displaypolicylinkform',
        'listmatchingpolicies',
        'generateLinkedDataModal',
        'savequery',
        'executequery',
        'managequeries',
    ];

    /**
     * @param $module
     * @param $action
     */
    public function __construct($module, $action, ModuleRepository $moduleRepository)
    {
        $this->module = $module;
        $this->action = $action;
        $this->moduleRepository = $moduleRepository;
    }

    /**
     * @return string
     *
     * @throws Exception
     */
    public function getLabel()
    {
        $useModuleTitle = (empty($this->action) || in_array($this->action, $this->actionsToUseModuleTitle));

        if ($useModuleTitle && $this->moduleRepository->moduleExists((string) $this->module)) {
            return $this->moduleRepository->getModuleByCode($this->module)->getTitle();
        }

        return $this->moduleRepository->getModuleByCode('ADM')->getTitle();
    }

    /**
     * @return string
     */
    public function getModule()
    {
        return $this->module;
    }
}
