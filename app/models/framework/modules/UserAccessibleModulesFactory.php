<?php

namespace app\models\framework\modules;

use src\framework\session\UserSession;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class UserAccessibleModulesFactory
{
    public function create(): UserAccessibleModules
    {
        $user = Container::get(UserSession::class)->getCurrentUser() ?? new NullUser();
        $modules = Container::get(ModuleRepository::class);

        return new UserAccessibleModules($user, $modules);
    }
}
