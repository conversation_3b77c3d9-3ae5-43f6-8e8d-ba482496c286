<?php

namespace app\models\framework\modules;

use Exception;
use src\framework\controller\Request;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class CurrentModuleFactory
{
    /**
     * @throws Exception
     */
    public function create(): CurrentModule
    {
        $request = new Request();
        $action = $request->getParameter('action');

        if ($action === 'generateLinkedDataModal') {
            $moduleCode = $request->getParameter('linkModule');
        }

        if (empty($moduleCode)) {
            // FIXME $_GET['module'] is being manually added to
            $moduleCode = $request->getParameter('module') ?? $_GET['module'] ?? 'ADM';
        }

        // TODO remove this exception when we remove the concept of DIF1 as a module
        if ($moduleCode == 'DIF1') {
            $moduleCode = 'INC';
        }

        return new CurrentModule($moduleCode, $action, Container::get(ModuleRepository::class));
    }
}
