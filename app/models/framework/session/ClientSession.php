<?php

namespace app\models\framework\session;

use src\framework\registry\Registry;

/**
 * This class deals with the session that holds values related to the web client.
 */
/**
 * @codeCoverageIgnore
 */
class ClientSession
{
    /**
     * The registry instance to use.
     *
     * @var Registry
     */
    protected $registry;

    /** @var array Reference to PHP session */
    protected $session;

    /**
     * @codeCoverageIgnore
     *
     * @param array $session reference to $_SESSION
     */
    public function __construct(Registry $registry, &$session)
    {
        $this->registry = $registry;
        $this->session = &$session;
    }

    public function setTimezoneOffset($timezoneOffset)
    {
        if ($this->registry->getParm('DETECT_TIMEZONES', 'N')->isTrue()) {
            $this->session['Timezone'] = $timezoneOffset;
        } else {
            $this->session['Timezone'] = 0;
        }
    }

    public function getTimezoneOffset()
    {
        if ($this->registry->getParm('DETECT_TIMEZONES', 'N')->isTrue()) {
            return $this->session['Timezone'];
        }

        return 0;
    }

    public function setFlashAvailability($flashAvailable)
    {
        $this->session['FlashAvailable'] = $flashAvailable;
    }

    public function getFlashAvailability()
    {
        return $this->session['FlashAvailable'];
    }
}
