<?php

namespace app\models\framework\states;

use Exception;

final class ActiveStatus
{
    public const ENABLED = 0;
    public const DEACTIVATED = 1;
    public const DISABLED = 2;
    public const STATUSES = [
        self::ENABLED,
        self::DEACTIVATED,
        self::DISABLED,
    ];

    /**
     * @throws Exception
     */
    private function __construct()
    {
        // throw an exception if someone can get in here
        throw new Exception("Can't get an instance of ActiveStatus");
    }
}
