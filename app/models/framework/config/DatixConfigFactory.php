<?php

declare(strict_types=1);

namespace app\models\framework\config;

use app\framework\Environment;
use Monolog\Logger;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class DatixConfigFactory
{
    private static ?DatixConfig $instance = null;

    /**
     * @deprecated use DI or container instead
     *
     * @see Container::get()
     *
     * @throws InvalidConfigurationException
     */
    public function getInstance(): DatixConfig
    {
        if (self::$instance === null) {
            self::$instance = new DatixConfig($this->getConfig());
        }

        return self::$instance;
    }

    /**
     * @psalm-import-type CONFIG_ARRAY from DatixConfig
     *
     * @psalm-return CONFIG_ARRAY
     */
    private function getConfig(): array
    {
        $environment = getenv('MODE') ?: Environment::PRODUCTION;
        $developmentMode = $environment === Environment::DEVELOPMENT;

        return [
            'environment' => $environment,
            'developmentMode' => $developmentMode,
            'dtxdebug' => $developmentMode,
            'ServerName' => getenv('SQLSERVER_HOST'),
            'UserName' => getenv('SQLSERVER_USER'),
            'Password' => getenv('SQLSERVER_PASSWORD'),
            'Database' => getenv('SQLSERVER_DATABASE'),
            'ClientFolder' => DatixConfig::CLIENT_FOLDER,
            'logLevel' => (int) (getenv('LOG_LEVEL') ?: Logger::NOTICE),
            'MinifierDisabled' => $developmentMode,
            'baseCarltonUrl' => getenv('CARLTON_BASE_URL'),
            'baseCarltonApiUrl' => getenv('CARLTON_BASE_API_URL'),
            'carltonApiUserName' => getenv('CARLTON_JWT_USERNAME'),
            'carltonApiPassword' => getenv('CARLTON_JWT_PASSWORD'),
            'tokenApiUsername' => getenv('JWT_USERNAME'),
            'tokenApiPassword' => getenv('JWT_PASSWORD'),
            'jwtSecret' => getenv('JWT_SECRET'),
            'jwtAlgorithm' => getenv('JWT_ALGORITHM'),
            'reportGeneratorApiUrl' => getenv('REPORTGEN_API_URL'),
            'notificationsCentreApiUrl' => getenv('NOTIFICATIONS_CENTRE_URL'),
            'corsAllowedOrigins' => array_filter(array_map('trim', explode(',', getenv('CORS_ALLOWED_ORIGINS') ?: ''))),
            'disableHttps' => getenv('DISABLE_HTTP') == 1,
            'exposeServerPorts' => getenv('EXPOSE_SERVER_PORTS') == 1,
            'storageEngine' => getenv('STORAGE_ENGINE'),
            'scripturl' => getenv('BASE_URL'),
            'transcriptionSvcUrl' => getenv('TRANSCRIPTION_SVC_URL'),
            'mongoURI' => getenv('MONGO_URI'),
            'mongoEmailDB' => getenv('MONGO_EMAIL_DB'),
            'mongoEmailCollection' => getenv('MONGO_EMAIL_COLLECTION'),
            'mongoDomainWhitelistCollection' => getenv('MONGO_DOMAIN_WHITELIST_COLLECTION'),
            'transcriptionTenant' => getenv('TRANSCRIPTION_TENANT'),
            'serviceName' => getenv('SERVICE_NAME'),
            's3Bucket' => getenv('S3_BUCKET'),
            's3Region' => getenv('S3_REGION'),
            's3AccessKey' => getenv('S3_ACCESS_KEY'),
            's3Secret' => getenv('S3_SECRET'),
            's3UploadsFolder' => getenv('S3_UPLOADS_FOLDER'),
            'missingFilesFeatureEnabled' => getenv('MISSING_FILES_FEATURE_ENABLED') === '1',
            'externalContactsSecret' => getenv('EXTERNAL_CONTACTS_SECRET'),
            'externalContactsServiceEnabled' => getenv('EXTERNAL_CONTACTS_SERVICE_ENABLED') === '1',
            'externalContactsServiceUrl' => getenv('EXTERNAL_CONTACTS_SERVICE_URL'),
            'displayMissingI18nPlaceholderKeys' => getenv('DISPLAY_MISSING_I18N_PLACEHOLDER_KEYS'),
            'medicationsV2Enabled' => getenv('MEDICATIONSV2_ENABLED'),
            'medicationsBaseUrl' => getenv('MEDICATIONS_BASEURL'),
            'medicationsLocationFilteringEnabled' => getenv('MEDICATIONLOCATIONFILTERING_ENABLED'),
            'centralAdminTarget' => getenv('CENTRALADMIN_TARGET'),
            'vanessaLawEnabled' => getenv('VANESSALAW_ENABLED'),
            'exposeConfig' => getenv('EXPOSE_CONFIG'),
            'psimsEnabled' => getenv('PSIMS_ENABLED'),
            'multiselect2Enabled' => getenv('MULTISELECT2_ENABLED'),
            'loginLocalAuthUrl' => getenv('LOGIN_LOCAL_AUTH_URL'),
            'loginUrl' => getenv('LOGIN_URL'),
            'logoutUrl' => getenv('LOGOUT_URL'),
            'authMethod' => getenv('AUTH_METHOD'),
            'usePhpMedsForm' => getenv('PHP_MEDS_FORM'),
            'useLocalAnswersPersistence' => getenv('LOCAL_ANSWERS'),
            'enableTranscription' => getenv('ENABLE_TRANSCRIPTION'),
            'usePhpEquipForm' => getenv('PHP_EQUIP_FORM'),
            'showLastChildFirst' => getenv('SHOW_LAST_CHILD_FIRST'),
            'useAdditionalEmails' => getenv('USE_ADDITIONAL_EMAILS'),
            'isMinioEnabled' => getenv('MINIO_ENABLED') === '1',
            'minioUrl' => getenv('MINIO_ENDPOINT'),
            'batchUpdateDeleteEnabled' => getenv('BATCH_UPDATE_DELETE_ENABLED'),
            'isBJPEnabled' => getenv('ENABLE_BJP'),
            'bjpBaseURL' => getenv('BJP_API_BASE_URL'),
            'feedbackMeds' => getenv('FEEDBACK_MEDS') === '1',
            'queryLoggingEnabled' => getenv('QUERY_LOGGING_ENABLED') === '1',
            'pendoEnabled' => getenv('PENDO_ENABLED'),
            'pendoApiKey' => getenv('PENDO_API_KEY'),
            'doctrineMetadataCacheEnabled' => getenv('DOCTRINE_METADATA_CACHE_ENABLED') === '1',
            'doctrineQueryCacheEnabled' => getenv('DOCTRINE_QUERY_CACHE_ENABLED') === '1',
            'auditBcContactEnabled' => getenv('AUDIT_BC_CONTACT_ENABLED') === '1',
            'sfgLocalAuthorityEnabled' => getenv('SFG_LOCAL_AUTHORITY') === '1',
        ];
    }
}
