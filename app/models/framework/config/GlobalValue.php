<?php

namespace app\models\framework\config;

use function array_values;
use function explode;

/**
 * This class serves as a Value Object for Datix global values.
 */
class GlobalValue
{
    /** @var scalar */
    private $value;

    public function __construct($value)
    {
        $this->value = $value;
    }

    /**
     * This method allows us to keep using the instance variable as a string.
     *
     * @return string $value
     */
    public function __toString(): string
    {
        return (string) $this->value;
    }

    /**
     * Alias to __toString().
     *
     * @return mixed
     */
    public function toScalar()
    {
        return $this->value;
    }

    /**
     * Check if the value is a "Y".
     */
    public function toBool(): bool
    {
        return strtolower($this->value ?? '') == 'y';
    }

    public function toInt(): int
    {
        return (int) $this->value;
    }

    /**
     * Alias to toBool(), helps with legibility.
     */
    public function isTrue(): bool
    {
        return $this->toBool();
    }

    /**
     * Opposite of toBool(), helps with legibility.
     */
    public function isFalse(): bool
    {
        return !$this->toBool();
    }

    /**
     * Checks if value is empty.
     */
    public function isEmpty(): bool
    {
        return empty($this->value);
    }

    /**
     * Opposite of isEmpty() for legibility.
     */
    public function isNotEmpty(): bool
    {
        return !empty($this->value);
    }

    /**
     * Compares the value with a given string, for readability.
     *
     * @param string $string
     */
    public function is($string): bool
    {
        return strtolower($this->value ?? '') === strtolower($string ?? '');
    }

    /**
     * Compares the value with a given string, for readability.
     *
     * @param string $string
     */
    public function isNot($string): bool
    {
        return strtolower($this->value) !== strtolower($string);
    }

    public function toArray(string $separator = ' '): array
    {
        return array_values(array_filter(explode($separator, $this->value ?? '') ?: []));
    }
}
