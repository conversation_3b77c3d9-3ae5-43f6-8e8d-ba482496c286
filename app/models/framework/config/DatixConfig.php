<?php

namespace app\models\framework\config;

use Firebase\JWT\Key;
use Monolog\Logger;

/**
 * @psalm-type CONFIG_ARRAY = array{
 *     environment: string,
 *     developmentMode: bool,
 *     dtxdebug: bool,
 *     ServerName: string|false,
 *     UserName: string|false,
 *     Password: string|false,
 *     Database: string|false,
 *     ClientFolder: string,
 *     logLevel: int,
 *     MinifierDisabled: bool,
 *     baseCarltonUrl: string|false,
 *     baseCarltonApiUrl: string|false,
 *     carltonApiUserName: string|false,
 *     carltonApiPassword: string|false,
 *     tokenApiUsername: string|false,
 *     tokenApiPassword: string|false,
 *     jwtSecret: string|false,
 *     jwtAlgorithm: string|false,
 *     reportGeneratorApiUrl: string|false,
 *     notificationsCentreApiUrl: string|false,
 *     corsAllowedOrigins: string[],
 *     disableHttps: bool,
 *     exposeServerPorts: bool,
 *     storageEngine: string|false,
 *     scripturl: string|false,
 *     transcriptionSvcUrl: string|false,
 *     mongoURI: string|false,
 *     mongoEmailDB: string|false,
 *     mongoEmailCollection: string|false,
 *     transcriptionTenant: string|false,
 *     serviceName: string|false,
 *     s3Bucket: string|false,
 *     s3Region: string|false,
 *     s3AccessKey: string|false,
 *     s3Secret: string|false,
 *     s3UploadsFolder: string|false,
 *     missingFilesFeatureEnabled: bool,
 *     externalContactsSecret: string|false,
 *     externalContactsServiceEnabled: bool,
 *     externalContactsServiceUrl: string|false,
 *     displayMissingI18nPlaceholderKeys: string|false,
 *     medicationsV2Enabled: string|false,
 *     medicationsBaseUrl: string|false,
 *     medicationsLocationFilteringEnabled: string|false,
 *     centralAdminTarget: string|false,
 *     vanessaLawEnabled: string|false,
 *     exposeConfig: string|false,
 *     psimsEnabled: string|false,
 *     multiselect2Enabled: string|false,
 *     loginlocalAuthUrl: string|false,
 *     loginUrl: string|false,
 *     authMethod: string|false,
 *     usePhpMedsFomr: string|false,
 *     useLocalAAnswersPersistence: string|false,
 *     showLastChildFirst: string|false,
 *     useAdditionalEmails: string|false,
 *     isMiniEnabled: bool,
 *     minioUrl: string|false,
 *     batchUpdateDeleteEnabled: string|false,
 *     isBJPEnabled: string|false,
 *     bjpBaseURL: string|false,
 *     feedbackMeds: bool,
 *     queryLoggingEnabled: bool,
 *     pendoEnabled: string|false,
 *     pendoApiKey: string|false,
 *     doctrineMetadataCacheEnabled: bool,
 *     doctrineQueryCacheEnabled: bool,
 *     auditBcContactEnabled: bool,
 * },
 */
class DatixConfig
{
    public const MANDATORY_ENTRIES = [
        'UserName',
        'Password',
        'Database',
        'ServerName',
        'baseCarltonUrl',
        'baseCarltonApiUrl',
        'carltonApiUserName',
        'carltonApiPassword',
        'tokenApiUsername',
        'tokenApiPassword',
        'jwtSecret',
        'jwtAlgorithm',
        'mongoURI',
        'mongoEmailDB',
        'mongoEmailCollection',
    ];
    public const CLIENT_FOLDER = 'client';

    /**
     * @psalm-var CONFIG_ARRAY
     *
     * @readonly
     */
    private array $config;

    /**
     * @psalm-param CONFIG_ARRAY $config
     *
     * @throws InvalidConfigurationException
     */
    public function __construct(array $config)
    {
        $this->config = $config;

        foreach (self::MANDATORY_ENTRIES as $entry) {
            if (empty($this->config[$entry])) {
                throw new InvalidConfigurationException("Missing mandatory config item `{$entry}`..");
            }
        }
    }

    /**
     * @return string
     */
    public function getDbUsername()
    {
        return $this->config['UserName'];
    }

    /**
     * @return string
     */
    public function getDbPassword()
    {
        return $this->config['Password'];
    }

    /**
     * @return string
     */
    public function getDbName()
    {
        return $this->config['Database'];
    }

    /**
     * @return string
     */
    public function getDbServerName()
    {
        return $this->config['ServerName'];
    }

    public function getClientFolder(): string
    {
        return self::CLIENT_FOLDER;
    }

    /**
     * @return bool
     */
    public function isDebugModeOn()
    {
        return $this->config['dtxdebug'] == true;
    }

    /**
     * @return bool
     */
    public function isMinifierOn()
    {
        return $this->config['MinifierDisabled'] != true;
    }

    public function isHttpsDisabled()
    {
        return $this->config['disableHttps'] == true;
    }

    /**
     * @return bool
     */
    public function isDevModeOn()
    {
        return ($this->config['devNoCache'] ?? false) == true;
    }

    /**
     * Returns the path of the top right hand logo.
     *
     * @return string
     */
    public function getTopRightLogo()
    {
        return $this->config['logo_topright'] ?? null;
    }

    public function getMaxLogFiles()
    {
        return $this->config['maxLogFiles'];
    }

    public function getCentrifySingleSignOnServiceUrl()
    {
        return $this->config['centrifySignOnUrl'];
    }

    public function getCentrifySingleLogoutServiceUrl()
    {
        return $this->config['centrifyLogoutUrl'];
    }

    public function getCentrifyBaseUrl()
    {
        return rtrim($this->config['centrifyBaseUrl'], '/');
    }

    public function getCentrifyX509cert()
    {
        return $this->config['centrifyCert'];
    }

    public function getCarltonBaseUrl()
    {
        return $this->config['baseCarltonUrl'];
    }

    public function getCarltonApiBaseUrl()
    {
        return $this->config['baseCarltonApiUrl'];
    }

    public function getCarltonApiUserName()
    {
        return $this->config['carltonApiUserName'];
    }

    public function getCarltonApiPassword()
    {
        return $this->config['carltonApiPassword'];
    }

    public function getEnvironment()
    {
        return $this->config['environment'];
    }

    public function getTokenApiUsername()
    {
        return $this->config['tokenApiUsername'];
    }

    public function getTokenApiPassword()
    {
        return $this->config['tokenApiPassword'];
    }

    public function getReportGeneratorApiUrl(): string
    {
        return $this->config['reportGeneratorApiUrl'] ?? '';
    }

    public function getNotificationsCentreApiUrl(): string
    {
        return $this->config['notificationsCentreApiUrl'] ?? '';
    }

    public function getCorsAllowedOrigins(): array
    {
        return $this->config['corsAllowedOrigins'];
    }

    public function getStorageEngine()
    {
        return $this->config['storageEngine'];
    }

    public function getJWTSecret(): Key
    {
        return new Key(
            $this->config['jwtSecret'],
            $this->getJWTAlgorithm(),
        );
    }

    public function getJWTAlgorithm(): string
    {
        return $this->config['jwtAlgorithm'];
    }

    public function getTranscriptionSvcUrl(): string
    {
        return $this->config['transcriptionSvcUrl'];
    }

    public function getMongoURI(): string
    {
        return $this->config['mongoURI'];
    }

    public function getMongoEmailDB(): string
    {
        return $this->config['mongoEmailDB'];
    }

    public function getMongoEmailCollection(): string
    {
        return $this->config['mongoEmailCollection'];
    }

    public function getMongoDomainWhitelistCollection(): string
    {
        return $this->config['mongoDomainWhitelistCollection'];
    }

    public function getServiceName(): string
    {
        return $this->config['serviceName'];
    }

    public function getS3Bucket(): string
    {
        return $this->config['s3Bucket'];
    }

    public function getS3Region(): string
    {
        return $this->config['s3Region'];
    }

    public function getS3AccessKey(): string
    {
        return $this->config['s3AccessKey'];
    }

    public function getS3Secret(): string
    {
        return $this->config['s3Secret'];
    }

    public function getS3UploadsFolder(): string
    {
        return $this->config['s3UploadsFolder'];
    }

    public function getTranscriptionTenant(): string
    {
        return $this->config['transcriptionTenant'];
    }

    public function getExternalContactsUrl(): string
    {
        return $this->config['externalContactsUrl'];
    }

    public function getExternalContactsSecret(): string
    {
        return $this->config['externalContactsSecret'] ?? '';
    }

    public function isExternalContactsServiceEnabled(): bool
    {
        return $this->config['externalContactsServiceEnabled'];
    }

    public function getExternalsContactsServiceUrl(): string
    {
        return $this->config['externalContactsServiceUrl'] ?? '';
    }

    public function getScriptUrl(): string
    {
        return $this->config['scripturl'] ?? '';
    }

    public function getMedicationsBaseUrl(): string
    {
        return $this->config['medicationsBaseUrl'] ?? '';
    }

    public function getMedicationLocationFilteringEnabled(): bool
    {
        return (bool) $this->config['medicationsLocationFilteringEnabled'];
    }

    public function getEquipmentsV2Enabled(): bool
    {
        // will replace it with equipment v2
        return (bool) $this->config['medicationsV2Enabled'];
    }

    public function getCentralAdminTarget(): bool
    {
        return (bool) $this->config['centralAdminTarget'];
    }

    public function getVanessaLawEnabled(): bool
    {
        return (bool) $this->config['vanessaLawEnabled'];
    }

    public function getExposeConfig(): bool
    {
        return (bool) $this->config['exposeConfig'];
    }

    public function getPsimsEnabled(): bool
    {
        return (bool) $this->config['psimsEnabled'];
    }

    public function getMultiSelect2Enabled(): bool
    {
        return (bool) $this->config['multiselect2Enabled'];
    }

    public function getBatchUpdateDeleteEnabled(): bool
    {
        return (bool) $this->config['batchUpdateDeleteEnabled'];
    }

    public function queryLoggingEnabled(): bool
    {
        return (bool) $this->config['queryLoggingEnabled'];
    }

    /*
     * AUTH_METHOD is a replication of the METHOD environment variable from the auth service
     *
     *  A possible value of this env var is '', which indicates the user should be sent
     *  directly to the local auth service to login.
     *
     *  getLoginUrl checks if the value of AUTH_METHOD is '', and sets the login endpoint to
     *  the local login URL
     */
    public function getLoginUrl(): string
    {
        if ($this->config['authMethod'] === '') {
            return $this->config['loginLocalAuthUrl'];
        }

        return $this->config['loginUrl'];
    }

    public function usePhpMedsForm(): bool
    {
        return (bool) $this->config['usePhpMedsForm'];
    }

    public function useLocalAnswersPersistence(): bool
    {
        return (bool) $this->config['useLocalAnswersPersistence'];
    }

    public function isTranscriptionEnabled(): bool
    {
        return (bool) $this->config['enableTranscription'];
    }

    public function usePhpEquipForm(): bool
    {
        return (bool) $this->config['usePhpEquipForm'];
    }

    public function showLastChildFirst(): bool
    {
        return (bool) $this->config['showLastChildFirst'];
    }

    public function useAdditionalEmails(): bool
    {
        return (bool) $this->config['useAdditionalEmails'];
    }

    public function getCarltonPublicUrl(): string
    {
        return str_replace('/#/', '/public/#/', $this->getCarltonBaseUrl());
    }

    public function isMinioEnabled(): bool
    {
        return $this->config['isMinioEnabled'];
    }

    public function getMinioUrl(): string
    {
        return $this->config['minioUrl'] ?? '';
    }

    public function getIsBjpEnabled(): bool
    {
        return ($this->config['isBJPEnabled'] ?? false) && !empty($this->getBjpBaseURL());
    }

    public function getBjpBaseURL(): string
    {
        return $this->config['bjpBaseURL'] ?? '';
    }

    public function isFeedbackMedicationsEnabled(): bool
    {
        return ($this->config['feedbackMeds'] ?? false) && $this->usePhpMedsForm();
    }

    public function getLogoutUrl(): string
    {
        return $this->config['logoutUrl'] ?? '';
    }

    public function getLogLevel(): int
    {
        return $this->config['logLevel'] ?: Logger::ERROR;
    }

    public function isMissingFilesFeatureEnabled(): bool
    {
        return $this->config['missingFilesFeatureEnabled'] ?? false;
    }

    public function getPendoEnabled(): bool
    {
        return (bool) $this->config['pendoEnabled'];
    }

    public function getPendoApiKey(): string
    {
        return $this->config['pendoApiKey'] ?: '';
    }

    public function isDoctrineMetadataCacheEnabled(): bool
    {
        return (bool) $this->config['doctrineMetadataCacheEnabled'] ?? false;
    }

    public function isDoctrineQueryCacheEnabled(): bool
    {
        return (bool) $this->config['doctrineQueryCacheEnabled'] ?? false;
    }

    public function isContactAuditingEnabled(): bool
    {
        return $this->config['auditBcContactEnabled'];
    }

    public function isSfgLocalAuthorityEnabled(): bool
    {
        return $this->config['sfgLocalAuthorityEnabled'] ?? false;
    }
}
