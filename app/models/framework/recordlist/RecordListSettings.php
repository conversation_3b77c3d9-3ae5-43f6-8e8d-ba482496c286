<?php

namespace app\models\framework\recordlist;

use src\framework\controller\Request;

class RecordListSettings
{
    /** @var string[] */
    protected $session;

    /** @var string */
    protected $defaultOrderBy;

    /** @var string */
    protected $defaultOrder;

    /**
     * @param array $session reference
     * @param string $defaultOrderBy
     * @param string $defaultOrder
     */
    public function __construct(&$session, $defaultOrderBy = '', $defaultOrder = '')
    {
        $this->session = &$session;
        $this->defaultOrderBy = $defaultOrderBy;
        $this->defaultOrder = $defaultOrder;
    }

    /**
     * Update settings with data from request.
     */
    public function updateFromRequest(Request $request)
    {
        $data = $request->getParameters();

        $this->session['OVERDUE'] = $data['overdue'] ?? null;

        if ($data['orderby'] ?? false) {
            $this->session['ORDERBY'] = $data['orderby'];
        }

        if ($data['order'] ?? false) {
            $this->session['ORDER'] = $data['order'];
        }
    }

    /**
     * @return string
     */
    public function getOrder()
    {
        $order = strtoupper($this->session['ORDER'] ?? '');
        if ($order !== 'DESC' && $order !== 'ASC') {
            $order = $this->defaultOrder;
        }

        return $order;
    }

    /**
     * @return string
     */
    public function getOrderBy()
    {
        return $this->session['ORDERBY'] ?? $this->defaultOrderBy ?? null;
    }

    /**
     * @return string
     */
    public function getOverdue()
    {
        return (string) $this->session['OVERDUE'];
    }
}
