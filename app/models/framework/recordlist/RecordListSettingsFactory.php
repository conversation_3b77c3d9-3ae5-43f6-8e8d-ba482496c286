<?php

namespace app\models\framework\recordlist;

use app\models\generic\valueObjects\Module;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

/**
 * @codeCoverageIgnore
 */
class RecordListSettingsFactory
{
    public function create(Module $module): RecordListSettings
    {
        $moduleDefs = Container::get(ModuleDefs::class)->getModuleData($module->getCode());

        $defaultOrder = $moduleDefs['DEFAULT_ORDER_DIRECTION'] ?? null;
        $defaultOrderBy = $moduleDefs['DEFAULT_ORDER'] ?? null;

        return new RecordListSettings($_SESSION['LIST'][$module->getCode()], $defaultOrderBy, $defaultOrder);
    }
}
