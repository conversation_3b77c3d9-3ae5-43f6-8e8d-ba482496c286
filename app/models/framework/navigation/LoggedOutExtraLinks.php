<?php

namespace app\models\framework\navigation;

class LoggedOutExtraLinks
{
    private string $loginLink;

    public function __construct(string $loginUrl)
    {
        $this->loginLink = $loginUrl;
    }

    /**
     * @return Hyperlink[]
     */
    public function getLinks(): array
    {
        return [
            'login' => $this->getLoginLink(),
        ];
    }

    protected function getLoginLink(): Hyperlink
    {
        return new Hyperlink(_fdtk('login'), $this->loginLink);
    }
}
