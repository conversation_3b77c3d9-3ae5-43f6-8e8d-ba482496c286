<?php

namespace app\models\framework\navigation;

use InvalidArgumentException;

use function is_scalar;

class Hyperlink
{
    protected $label;
    protected $url;

    public function __construct($label, $url)
    {
        if (!is_scalar($label)) {
            throw new InvalidArgumentException('label is not a string');
        }

        if (!is_scalar($url)) {
            throw new InvalidArgumentException('url is not a string');
        }

        $this->label = $label;
        $this->url = $url;
    }

    /**
     * @return string
     */
    public function getUrl()
    {
        return $this->url;
    }

    /**
     * @return string
     */
    public function getLabel()
    {
        return $this->label;
    }
}
