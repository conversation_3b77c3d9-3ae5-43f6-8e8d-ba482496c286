<?php

namespace app\models\framework\navigation;

use app\models\framework\config\DatixConfigFactory;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class LoggedOutExtraLinksFactory
{
    public function create(): LoggedOutExtraLinks
    {
        $config = (new DatixConfigFactory())->getInstance();

        return new LoggedOutExtraLinks(
            $config->getLoginUrl(),
        );
    }
}
