<?php

namespace app\models\framework\flashMessages;

/**
 * @codeCoverageIgnore
 */
class InfoMessage implements MessageInterface
{
    protected $message;

    public function __construct($message)
    {
        $this->message = $message;
    }

    public function getSortPosition()
    {
        return 2;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function getHTML()
    {
        return '<div class="info_div">' . $this->message . '</div>';
    }

    public function setMessage(string $message)
    {
        $this->message = $message;
    }
}
