<?php

namespace app\models\framework\flashMessages;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class FlashMessageContainerFactory
{
    public function create(): FlashMessageContainer
    {
        if (empty($_SESSION['messages'])) {
            $_SESSION['messages'] = [];
        }

        return new FlashMessageContainer($_SESSION['messages']);
    }
}
