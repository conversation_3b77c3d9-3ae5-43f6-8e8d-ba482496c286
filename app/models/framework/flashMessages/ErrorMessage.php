<?php

namespace app\models\framework\flashMessages;

/**
 * @codeCoverageIgnore
 */
class ErrorMessage implements MessageInterface
{
    protected $message;

    public function __construct($message)
    {
        $this->message = $message;
    }

    public function getSortPosition()
    {
        return 1;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function getHTML()
    {
        return '<div class="error_div">' . $this->message . '</div>';
    }
}
