<?php

namespace app\models\framework\flashMessages;

/**
 * @codeCoverageIgnore
 */
class PopUpMessage implements MessageInterface
{
    protected $message;

    public function __construct($message)
    {
        $this->message = $message;
    }

    public function getSortPosition()
    {
        return 3;
    }

    public function getMessage()
    {
        return $this->message;
    }

    public function getHTML()
    {
        return '<script> jQuery(document).ready(function() { alert("' . $this->message . '"); }); </script>';
    }
}
