<?php

namespace app\models\framework\flashMessages;

class FlashMessageContainer
{
    /** @psalm-var list<MessageInterface> */
    protected array $messages;

    /**
     * @param list<MessageInterface> $messages
     */
    public function __construct(array &$messages)
    {
        $this->messages = &$messages;
    }

    /**
     * Add message to the list.
     */
    public function addMessage(MessageInterface $message)
    {
        foreach ($this->messages as $existingMessage) {
            if ($message->getMessage() === $existingMessage->getMessage()) {
                return;
            }
        }

        $this->messages[] = $message;
    }

    /**
     * Returns a sorted list of all messages, and clears the list.
     *
     * @return string
     */
    public function getMessages()
    {
        // sort the list
        $this->sortMessagesByPriority();

        $msgTexts = array_map(function (MessageInterface $error) {
            return $error->getHTML();
        }, $this->messages);

        // truncate the list
        $this->clear();

        $messages = implode("\n", $msgTexts);

        if ($messages) {
            $messages = '<div id="header-error-buffer">' . $messages . '</div>';
        }

        return $messages;
    }

    /**
     * Are there any error messages in the list?
     *
     * @return bool
     */
    public function containsErrorMessages()
    {
        foreach ($this->messages as $message) {
            if ($message instanceof ErrorMessage) {
                return true;
            }
        }

        return false;
    }

    /**
     * @psalm-return list<MessageInterface>
     */
    public function getRawMessages(): array
    {
        return $this->messages;
    }

    /**
     * Returns a list of just the error messages.
     *
     * @return string HTML
     */
    public function getErrorMessages()
    {
        $errorList = array_filter($this->messages, function ($item) {
            return $item instanceof ErrorMessage;
        });

        $htmlErrorList = array_map(function (ErrorMessage $item) {
            return $item->getHTML();
        }, $errorList);

        return implode("\n", $htmlErrorList);
    }

    /**
     * Sorts the messages by priority.
     */
    protected function sortMessagesByPriority()
    {
        usort($this->messages, function (MessageInterface $a, MessageInterface $b) {
            if ($a->getSortPosition() == $b->getSortPosition()) {
                return 0;
            }

            return ($a->getSortPosition() < $b->getSortPosition()) ? -1 : 1;
        });
    }

    /**
     * Clears the list.
     */
    protected function clear()
    {
        $this->messages = [];
    }
}
