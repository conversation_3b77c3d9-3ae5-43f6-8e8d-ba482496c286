<?php

namespace app\models\draftLocation\hydrators;

use app\models\draftLocation\entities\DraftLocation;
use app\models\draftLocation\entities\DraftLocationCaptureOnlyData;
use app\models\draftLocation\entities\DraftLocationDescr;
use app\models\draftLocation\entities\DraftLocationIdNumbers;
use app\models\framework\states\ActiveStatus;
use app\models\language\mappers\LanguageMapper;
use app\models\location\entities\LocationTagEntity;
use app\models\location\services\TreeFieldService;
use app\models\service\entities\ServiceEntity;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManager;
use InvalidArgumentException;
use OpenApi\Annotations as OA;
use src\system\language\LanguageSession;

use function in_array;

class DraftLocationHydrator
{
    private LanguageSession $languageSession;
    private LanguageMapper $languageMapper;
    private TreeFieldService $treeFieldService;
    private EntityManager $entityManager;
    private int $autoIncrement;

    public function __construct(LanguageSession $languageSession, LanguageMapper $languageMapper, TreeFieldService $treeFieldService, EntityManager $entityManager)
    {
        $this->languageSession = $languageSession;
        $this->languageMapper = $languageMapper;
        $this->treeFieldService = $treeFieldService;
        $this->entityManager = $entityManager;
    }

    /**
     * @OA\Schema(
     *     schema="DraftLocationHydratorInput",
     *     @OA\Property(
     *         property="node",
     *         type="object",
     *         @OA\Property(property="id", type="integer"),
     *     ),
     *     @OA\Property(
     *         property="title",
     *         type="object",
     *         @OA\AdditionalProperties(type="string"),
     *     ),
     *     @OA\Property(property="left", type="integer", nullable=true),
     *     @OA\Property(property="right", type="integer", nullable=true),
     *     @OA\Property(property="level", type="integer", nullable=true),
     *     @OA\Property(property="language", type="integer", nullable=true),
     *     @OA\Property(property="root", type="integer", nullable=true),
     *     @OA\Property(property="parent_id", type="integer", nullable=true),
     *     @OA\Property(property="status", type="integer", nullable=true, enum={0, 1, 2}),
     *     @OA\Property(
     *         property="path",
     *         type="object",
     *         @OA\AdditionalProperties(
     *             type="object",
     *             @OA\Property(
     *                 property="title",
     *                 type="object",
     *                 @OA\AdditionalProperties(type="string"),
     *             ),
     *         ),
     *     ),
     *     @OA\Property(
     *         property="tags",
     *         type="array",
     *         @OA\Items(type="object", @OA\Property(property="id", type="integer")),
     *     ),
     *     @OA\Property(
     *         property="services",
     *         type="array",
     *         @OA\Items(type="object", @OA\Property(property="id", type="integer")),
     *     ),
     *     @OA\Property(
     *         property="idNumberTypes",
     *         type="array",
     *         @OA\Items(type="object", @OA\Property(property="id", type="integer")),
     *     ),
     *     @OA\Property(
     *         property="idNumber",
     *         type="array",
     *         @OA\Items(type="object", @OA\Property(property="id", type="integer")),
     *     ),
     * )
     */
    public function hydrate(array $data, array $existingCaptureOnlyData, ?DraftLocation $draftLocation = null): DraftLocation
    {
        if ($draftLocation === null) {
            $draftLocation = new DraftLocation();
        }

        $draftLocation = $this->applyDirectData($draftLocation, $data);
        $draftLocation = $this->applyLinkedData($draftLocation, $data);

        return $this->applyCaptureOnlyData($draftLocation, $existingCaptureOnlyData);
    }

    public function getAndIncrementAutoIncrement(): int
    {
        return $this->autoIncrement++;
    }

    public function setAutoIncrement(int $autoIncrement): void
    {
        $this->autoIncrement = $autoIncrement;
    }

    private function getTitleFromHydrationData(array $data): string
    {
        $defaultLanguage = $this->languageSession->getLanguage();
        $languageCode = $this->languageMapper->get([$defaultLanguage])['code'] ?? '';
        $titleArray = $data['title'] ?? [];

        return $this->treeFieldService->generateDefaultTitle($titleArray, $languageCode) ?? '';
    }

    private function applyCaptureOnlyData(DraftLocation $draftLocation, array $existingCaptureOnlyData): DraftLocation
    {
        $draftLocationId = $draftLocation->getNodeId();
        if (!isset($existingCaptureOnlyData[$draftLocationId])) {
            return $draftLocation;
        }

        /** @var DraftLocationCaptureOnlyData $existingCaptureOnlyDatum */
        $existingCaptureOnlyDatum = $existingCaptureOnlyData[$draftLocationId];

        $draftLocation
            ->setCodNpsaRp02($existingCaptureOnlyDatum->getCodNpsaRp02())
            ->setCodNpsaPd20($existingCaptureOnlyDatum->getCodNpsaPd20())
            ->setCodNpsaIn03($existingCaptureOnlyDatum->getCodNpsaIn03())
            ->setCodKo41Ods($existingCaptureOnlyDatum->getCodKo41Ods())
            ->setCodNcdsLocation($existingCaptureOnlyDatum->getCodNcdsLocation())
            ->setCodHero($existingCaptureOnlyDatum->getCodHero());

        return $draftLocation;
    }

    private function applyDirectData(DraftLocation $draftLocation, array $data): DraftLocation
    {
        $id = $this->getId($data);
        $title = $this->getTitleFromHydrationData($data);
        $left = $data['left'] ? (int) $data['left'] : 0;
        $right = $data['right'] ? (int) $data['right'] : null;
        $level = isset($data['level']) ? (int) $data['level'] : null;
        $language = $data['language'] ? (int) $data['language'] : 7; // TODO is language required?
        $root = $data['root'] ? (int) $data['root'] : null;
        $parent = isset($data['parent_id']) ? (int) $data['parent_id'] : null;
        $status = $this->getStatusFromRequest($data);
        $path = $this->getPathFromRequest($data);

        $draftLocation
            ->setId($id)
            ->setTitle($title)
            ->setLeft($left)
            ->setRight($right)
            ->setLevel($level)
            ->setLanguage($language)
            ->setRoot($root)
            ->setParentId($parent)
            ->setStatus($status)
            ->setPath($path);

        $draftLocation->setNodeId(!empty($data['node']['id']) ? $data['node']['id'] : null);

        return $draftLocation;
    }

    private function getStatusFromRequest(array $data): int
    {
        $status = isset($data['status']) ? (int) $data['status'] : 0;

        if (in_array($status, ActiveStatus::STATUSES, true)) {
            return $status;
        }

        return 0;
    }

    private function applyLinkedData(DraftLocation $draftLocation, array $data): DraftLocation
    {
        $draftLocation->setTags($this->getTagsFromRequest($data));
        $draftLocation->setServices($this->getServicesFromRequest($data));
        $draftLocation->setIdNumberTypes($this->getIdNumbersFromRequest($draftLocation, $data));
        $draftLocation->setTranslations($this->getTranslationsFromRequest($draftLocation, $data));

        return $draftLocation;
    }

    private function getPathFromRequest(array $data): string
    {
        $path = $data['path'];
        foreach ($path as $key => $node) {
            $titles = $node['title'];
            foreach ($titles as $locale => $titleString) {
                $languageId = $this->languageMapper->getLanguageFromLocale($locale)['id'];
                unset($path[$key]['title'][$locale]);
                $path[$key]['title'][$languageId] = $titleString;
            }
        }

        return json_encode($path);
    }

    private function getTagsFromRequest(array $data): ArrayCollection
    {
        $tags = $data['tags'] ?? [];
        if (empty($tags)) {
            return new ArrayCollection();
        }

        $tagRepo = $this->entityManager->getRepository(LocationTagEntity::class);

        $existingTags = array_map(static function (array $tag) use ($tagRepo) {
            $tagId = $tag['id'] ?? null;
            if (!$tagId) {
                throw new InvalidArgumentException('Tag ID missing');
            }
            /** @var LocationTagEntity $tagEntity */
            $tagEntity = $tagRepo->find($tagId);
            if (!$tagEntity) {
                throw new InvalidArgumentException('Tag cannot be found in DB');
            }

            return $tagEntity;
        }, $tags);

        return new ArrayCollection($existingTags);
    }

    private function getServicesFromRequest(array $data): ArrayCollection
    {
        $services = $data['services'] ?? [];
        if (empty($services)) {
            return new ArrayCollection();
        }

        $serviceRepo = $this->entityManager->getRepository(ServiceEntity::class);

        $existingServices = array_map(static function (array $service) use ($serviceRepo) {
            $serviceId = $service['id'] ?? null;
            if (!$serviceId) {
                throw new InvalidArgumentException('Service ID missing');
            }
            /** @var ServiceEntity $serviceEntity */
            $serviceEntity = $serviceRepo->find($serviceId);
            if (!$serviceEntity) {
                throw new InvalidArgumentException('Service cannot be found in DB');
            }

            return $serviceEntity;
        }, $services);

        return new ArrayCollection($existingServices);
    }

    private function getIdNumbersFromRequest(DraftLocation $draftLocation, array $data): ArrayCollection
    {
        $idNumberTypes = $data['idNumberTypes'] ?? [];
        if (empty($idNumberTypes)) {
            return new ArrayCollection();
        }

        $idNumberTypeEntities = array_map(static function (array $numberType) use ($draftLocation) {
            $idNumberType = isset($numberType['id']) ? (int) $numberType['id'] : null;
            if ($idNumberType === null) {
                throw new InvalidArgumentException('Number type ID missing');
            }

            return (new DraftLocationIdNumbers())
                ->setIdNumberType($idNumberType)
                ->setLocation($draftLocation);
        }, $idNumberTypes);

        return new ArrayCollection($idNumberTypeEntities);
    }

    private function getTranslationsFromRequest(DraftLocation $draftLocation, array $data): ArrayCollection
    {
        $translations = $data['title'] ?? [];
        if (empty($translations)) {
            throw new InvalidArgumentException('Translations missing');
        }

        $languageMapper = $this->languageMapper;

        $translationEntities = array_map(static function (string $language, ?string $value) use ($draftLocation, $languageMapper) {
            $languageArray = $languageMapper->getLanguageFromLocale($language);
            $languageId = $languageArray['id'] ?? null;
            if ($languageId === null) {
                throw new InvalidArgumentException('Language could not be found');
            }

            return (new DraftLocationDescr())
                ->setLanguage($languageId)
                ->setPath($draftLocation->getPath())
                ->setTitle($value)
                ->setId($draftLocation->getId())
                ->setLocationEntity($draftLocation);
        }, array_keys($translations), $translations);

        return new ArrayCollection($translationEntities);
    }

    private function getId(array $data): int
    {
        if ($data['node']) {
            return $data['node']['id'];
        }

        return $this->getAndIncrementAutoIncrement();
    }
}
