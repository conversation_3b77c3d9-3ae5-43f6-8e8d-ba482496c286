<?php

namespace app\models\draftLocation\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_locations_id_numbers")]
class DraftLocationIdNumbers
{
    #[Id]
    #[ManyToOne(
        targetEntity: DraftLocation::class,
        inversedBy: "idNumbers"
    )]
    #[JoinColumn(
        name: "location_id",
        referencedColumnName: "id"
    )]
    protected ?DraftLocation $location;

    #[Id]
    #[Column(type: "integer")]
    private ?int $id_number_type;

    public function getIdNumberType(): ?int
    {
        return $this->id_number_type;
    }

    public function setIdNumberType(?int $id_number_type): self
    {
        $this->id_number_type = $id_number_type;

        return $this;
    }

    public function getLocation(): ?DraftLocation
    {
        return $this->location;
    }

    public function setLocation(?DraftLocation $location): self
    {
        $this->location = $location;

        return $this;
    }
}
