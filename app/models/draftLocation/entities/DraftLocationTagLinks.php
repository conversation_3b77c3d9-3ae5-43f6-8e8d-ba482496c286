<?php

namespace app\models\draftLocation\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_location_tag_links")]
class DraftLocationTagLinks
{
    #[Id]
    #[Column(name: "[location_id]", type: "integer")]
    private ?int $locationId;

    #[Id]
    #[Column(name: "[location_tag_id]", type: "integer")]
    private ?int $locationTagId;

    #[Id]
    #[Column(name: "[language]", type: "integer")]
    private ?int $language;

    public function getLocationId(): ?int
    {
        return $this->locationId;
    }

    public function setLocationId(?int $locationId): self
    {
        $this->locationId = $locationId;

        return $this;
    }

    public function getLocationTagId(): ?int
    {
        return $this->locationTagId;
    }

    public function setLocationTagId(?int $locationTagId): self
    {
        $this->locationTagId = $locationTagId;

        return $this;
    }

    public function getLanguage(): ?int
    {
        return $this->language;
    }

    public function setLanguage(?int $language): self
    {
        $this->language = $language;

        return $this;
    }
}
