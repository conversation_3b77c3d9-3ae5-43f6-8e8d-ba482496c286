<?php

namespace app\models\draftLocation\entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_location_tag")]
class DraftLocationTag
{
    #[Id]
    #[Column(type: "integer")]
    private ?int $id;

    #[Column(type: "datetime")]
    private ?DateTime $created;

    #[Column(type: "string")]
    private ?string $title;

    #[Column(type: "integer")]
    private ?int $priority;

    /**
     * @var Collection<DraftLocationTagDescr>
     */
    #[OneToMany(
        mappedBy: "DraftLocationTag",
        targetEntity: DraftLocationTagDescr::class,
        cascade: ["persist"]
    )]
    private Collection $translations;

    public function __construct()
    {
        $this->translations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCreated(): ?DateTime
    {
        return $this->created;
    }

    public function setCreated(?DateTime $created): self
    {
        $this->created = $created;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getPriority(): ?int
    {
        return $this->priority;
    }

    public function setPriority(?int $priority): self
    {
        $this->priority = $priority;

        return $this;
    }

    /**
     * @return Collection<DraftLocationTagDescr>
     */
    public function getTranslations(): Collection
    {
        return $this->translations;
    }

    /**
     * @param Collection<DraftLocationTagDescr> $translations
     */
    public function setTranslations(Collection $translations): self
    {
        $this->translations = $translations;

        return $this;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'created' => [
                'date' => $this->created->format('Y-m-d H:i:s'),
            ],
            'title' => $this->title,
            'priority' => $this->priority,
        ];
    }
}
