<?php

namespace app\models\draftLocation\entities;

use app\models\location\entities\LocationTagEntity;
use app\models\service\entities\ServiceEntity;
use app\models\treeFields\TreeFieldEntityInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\JoinTable;
use Doctrine\ORM\Mapping\ManyToMany;
use Doctrine\ORM\Mapping\OneToMany;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_location")]
class DraftLocation implements TreeFieldEntityInterface
{
    #[Id]
    #[Column(type: "string")]
    private ?int $id;

    #[Column(name: "[parent_id]", type: "integer")]
    private ?int $parentId;

    #[Column(name: "[title]", type: "string")]
    private ?string $title;

    #[Column(name: "[left]", type: "integer")]
    private ?int $left;

    #[Column(name: "[right]", type: "integer")]
    private ?int $right;

    #[Column(name: "[root]", type: "integer")]
    private ?int $root;

    #[Column(name: "[level]", type: "integer")]
    private ?int $level;

    #[Column(name: "[path]", type: "string")]
    private ?string $path;

    #[Column(name: "[language]", type: "string")]
    private ?int $language;

    #[Column(name: "[cod_npsa_in03]", type: "string")]
    private ?string $cod_npsa_in03;

    #[Column(name: "[cod_npsa_rp02]", type: "string")]
    private ?string $cod_npsa_rp02;

    #[Column(name: "[cod_npsa_pd20]", type: "string")]
    private ?string $cod_npsa_pd20;

    #[Column(name: "[cod_ko41_ods]", type: "string")]
    private ?string $cod_ko41_ods;

    #[Column(name: "[cod_ncds_location]", type: "string")]
    private ?string $cod_ncds_location;

    #[Column(name: "[cod_hero]", type: "string")]
    private ?string $cod_hero;

    /**
     * @var Collection<LocationTagEntity>
     */
    #[ManyToMany(
        targetEntity: LocationTagEntity::class,
        cascade: ["merge", "detach"],
    )]
    #[JoinTable(
        name: "draft_location_tag_links",
        joinColumns: [
            new JoinColumn(
                name: "location_id",
                referencedColumnName: "id"
            ),
        ],
        inverseJoinColumns: [
            new JoinColumn(
                name: "location_tag_id",
                referencedColumnName: "id"
            )
        ]
    )]
    private Collection $tags;

    /**
     * @var Collection<DraftLocationDescr>
     *
     */
    #[OneToMany(
        mappedBy: "DraftLocation",
        targetEntity: DraftLocationDescr::class,
        cascade: ["persist", "remove"],
    )]
    private Collection $translations;

    /**
     * @var Collection<ServiceEntity>
     */
    #[ManyToMany(
        targetEntity: ServiceEntity::class,
        cascade: ["merge", "detach"]
    )]
    #[JoinTable(
        name: "draft_services_locations",
        joinColumns: [
            new JoinColumn(
                name: "locationentity_id",
                referencedColumnName: "id"
            ),
        ],
        inverseJoinColumns: [
            new JoinColumn(
                name: "serviceentity_id",
                referencedColumnName: "id"
            ),
        ]
    )]
    private Collection $services;

    /**
     * @var Collection<DraftLocationIdNumbers>
     */
    #[ManyToMany(
        targetEntity: DraftLocationIdNumbers::class,
        mappedBy: "DraftLocation",
        cascade: ["persist", "remove"],
    )]
    private Collection $idNumberTypes;

    #[Column(name: "[status]", type: "integer")]
    private ?int $status;
    private ?int $nodeId = null;

    public function __construct()
    {
        $this->tags = new ArrayCollection();
        $this->translations = new ArrayCollection();
        $this->services = new ArrayCollection();
        $this->idNumberTypes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function setParentId(?int $parentId): self
    {
        $this->parentId = $parentId;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getLeft(): ?int
    {
        return $this->left;
    }

    public function setLeft(?int $left): self
    {
        $this->left = $left;

        return $this;
    }

    public function getRight(): ?int
    {
        return $this->right;
    }

    public function setRight(?int $right): self
    {
        $this->right = $right;

        return $this;
    }

    public function getRoot(): ?int
    {
        return $this->root;
    }

    public function setRoot(?int $root): self
    {
        $this->root = $root;

        return $this;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(?int $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?string $path): self
    {
        $this->path = $path;

        return $this;
    }

    public function getLanguage(): ?int
    {
        return $this->language;
    }

    public function setLanguage(?int $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getCodNpsaIn03(): ?string
    {
        return $this->cod_npsa_in03;
    }

    public function setCodNpsaIn03(?string $cod_npsa_in03): self
    {
        $this->cod_npsa_in03 = $cod_npsa_in03;

        return $this;
    }

    public function getCodNpsaRp02(): ?string
    {
        return $this->cod_npsa_rp02;
    }

    public function setCodNpsaRp02(?string $cod_npsa_rp02): self
    {
        $this->cod_npsa_rp02 = $cod_npsa_rp02;

        return $this;
    }

    public function getCodNpsaPd20(): ?string
    {
        return $this->cod_npsa_pd20;
    }

    public function setCodNpsaPd20(?string $cod_npsa_pd20): self
    {
        $this->cod_npsa_pd20 = $cod_npsa_pd20;

        return $this;
    }

    public function getCodKo41Ods(): ?string
    {
        return $this->cod_ko41_ods;
    }

    public function setCodKo41Ods(?string $cod_ko41_ods): self
    {
        $this->cod_ko41_ods = $cod_ko41_ods;

        return $this;
    }

    /**
     * @return Collection<LocationTagEntity>
     */
    public function getTags(): Collection
    {
        return $this->tags;
    }

    /**
     * @param Collection<LocationTagEntity> $tags
     */
    public function setTags(Collection $tags): self
    {
        $this->tags = $tags;

        return $this;
    }

    /**
     * @return Collection<DraftLocationDescr>
     */
    public function getTranslations(): Collection
    {
        return $this->translations;
    }

    /**
     * @param Collection<DraftLocationDescr> $translations
     */
    public function setTranslations(Collection $translations): self
    {
        $this->translations = $translations;

        return $this;
    }

    /**
     * @return Collection<ServiceEntity>
     */
    public function getServices(): Collection
    {
        return $this->services;
    }

    /**
     * @param Collection<ServiceEntity> $services
     */
    public function setServices(Collection $services): self
    {
        $this->services = $services;

        return $this;
    }

    /**
     * @return Collection<DraftLocationIdNumbers>
     */
    public function getIdNumberTypes(): Collection
    {
        return $this->idNumberTypes;
    }

    /**
     * @param Collection<DraftLocationIdNumbers> $idNumberTypes
     */
    public function setIdNumberTypes(Collection $idNumberTypes): self
    {
        $this->idNumberTypes = $idNumberTypes;

        return $this;
    }

    public function getStatus(): ?int
    {
        return $this->status;
    }

    public function setStatus(?int $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getCodNcdsLocation(): ?string
    {
        return $this->cod_ncds_location;
    }

    public function setCodNcdsLocation(?string $cod_ncds_location): self
    {
        $this->cod_ncds_location = $cod_ncds_location;

        return $this;
    }

    public function getCodHero(): ?string
    {
        return $this->cod_hero;
    }

    public function setCodHero(?string $cod_hero): self
    {
        $this->cod_hero = $cod_hero;

        return $this;
    }

    public function getNodeId(): ?int
    {
        return $this->nodeId;
    }

    public function setNodeId(?int $nodeId): void
    {
        $this->nodeId = $nodeId;
    }
}
