<?php

namespace app\models\draftLocation\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_location_descr")]
class DraftLocationDescr
{
    #[Id]
    #[Column(type: "string")]
    private ?int $id;

    #[Column(type: "string")]
    private ?string $title;

    #[Id]
    #[Column(type: "integer")]
    private ?int $language;

    #[Column(name: "[path]", type: "string")]
    private ?string $path;

    #[ManyToOne(
        targetEntity: DraftLocation::class,
        inversedBy: "translations",
    )]
    #[JoinColumn(
        name: "id",
        referencedColumnName: "id",
    )]
    private DraftLocation $LocationEntity;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getLanguage(): ?int
    {
        return $this->language;
    }

    public function setLanguage(?int $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?string $path): self
    {
        $this->path = $path;

        return $this;
    }

    public function getLocationEntity(): DraftLocation
    {
        return $this->LocationEntity;
    }

    public function setLocationEntity(DraftLocation $LocationEntity): self
    {
        $this->LocationEntity = $LocationEntity;

        return $this;
    }

    public function toArray(): array
    {
        return [
            $this->language => [
                'id' => $this->id,
                'title' => $this->title,
            ],
        ];
    }
}
