<?php

namespace app\models\draftLocation\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_services_locations")]
class DraftServicesLocations
{
    #[Id]
    #[Column(name: "[serviceentity_id]", type: "integer")]
    private ?int $serviceentity_id;

    #[Id]
    #[Column(name: "[locationentity_id]", type: "integer")]
    private ?int $locationentity_id;

    public function getServiceentityId(): ?int
    {
        return $this->serviceentity_id;
    }

    public function setServiceentityId(?int $serviceentity_id): self
    {
        $this->serviceentity_id = $serviceentity_id;

        return $this;
    }

    public function getLocationentityId(): ?int
    {
        return $this->locationentity_id;
    }

    public function setLocationentityId(?int $locationentity_id): self
    {
        $this->locationentity_id = $locationentity_id;

        return $this;
    }
}
