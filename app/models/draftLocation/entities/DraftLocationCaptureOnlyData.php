<?php

namespace app\models\draftLocation\entities;

use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "location")]
class DraftLocationCaptureOnlyData
{
    #[Id]
    #[Column(type: "string")]
    private ?int $id;

    #[Column(name: "[cod_npsa_in03]", type: "string")]
    private ?string $cod_npsa_in03;

    #[Column(name: "[cod_npsa_rp02]", type: "string")]
    private ?string $cod_npsa_rp02;

    #[Column(name: "[cod_npsa_pd20]", type: "string")]
    private ?string $cod_npsa_pd20;

    #[Column(name: "[cod_ko41_ods]", type: "string")]
    private ?string $cod_ko41_ods;

    #[Column(name: "[cod_ncds_location]", type: "string")]
    private ?string $cod_ncds_location;

    #[Column(name: "[cod_hero]", type: "string")]
    private ?string $cod_hero;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getCodNpsaIn03(): ?string
    {
        return $this->cod_npsa_in03;
    }

    public function setCodNpsaIn03(?string $cod_npsa_in03): self
    {
        $this->cod_npsa_in03 = $cod_npsa_in03;

        return $this;
    }

    public function getCodNpsaRp02(): ?string
    {
        return $this->cod_npsa_rp02;
    }

    public function setCodNpsaRp02(?string $cod_npsa_rp02): self
    {
        $this->cod_npsa_rp02 = $cod_npsa_rp02;

        return $this;
    }

    public function getCodNpsaPd20(): ?string
    {
        return $this->cod_npsa_pd20;
    }

    public function setCodNpsaPd20(?string $cod_npsa_pd20): self
    {
        $this->cod_npsa_pd20 = $cod_npsa_pd20;

        return $this;
    }

    public function getCodKo41Ods(): ?string
    {
        return $this->cod_ko41_ods;
    }

    public function setCodKo41Ods(?string $cod_ko41_ods): self
    {
        $this->cod_ko41_ods = $cod_ko41_ods;

        return $this;
    }

    public function getCodNcdsLocation(): ?string
    {
        return $this->cod_ncds_location;
    }

    public function setCodNcdsLocation(?string $cod_ncds_location): self
    {
        $this->cod_ncds_location = $cod_ncds_location;

        return $this;
    }

    public function getCodHero(): ?string
    {
        return $this->cod_hero;
    }

    public function setCodHero(?string $cod_hero): self
    {
        $this->cod_hero = $cod_hero;

        return $this;
    }
}
