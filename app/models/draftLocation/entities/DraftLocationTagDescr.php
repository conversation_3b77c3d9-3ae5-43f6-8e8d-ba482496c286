<?php

namespace app\models\draftLocation\entities;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Mapping\Column;
use Doctrine\ORM\Mapping\Entity;
use Doctrine\ORM\Mapping\Id;
use Doctrine\ORM\Mapping\JoinColumn;
use Doctrine\ORM\Mapping\ManyToOne;
use Doctrine\ORM\Mapping\Table;

/**
 * @codeCoverageIgnore
 */
#[Entity]
#[Table(name: "draft_location_tag_descr")]
class DraftLocationTagDescr
{
    #[Id]
    #[Column(type: "string")]
    private ?int $id;

    #[Column(name: "title", type: "string")]
    private ?string $title;

    #[Id]
    #[Column(name: "language", type: "integer")]
    private ?int $language;

    #[ManyToOne(
        targetEntity: DraftLocationTag::class,
        cascade: ["persist"],
        inversedBy: "translations"
    )]
    #[JoinColumn(
        name: "id",
        referencedColumnName: "id"
    )]
    private ArrayCollection $LocationTagEntity;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function getLanguage(): ?int
    {
        return $this->language;
    }

    public function setLanguage(?int $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getLocationTagEntity(): ArrayCollection
    {
        return $this->LocationTagEntity;
    }

    public function setLocationTagEntity($LocationTagEntity): self
    {
        $this->LocationTagEntity = $LocationTagEntity;

        return $this;
    }

    public function toArray(): array
    {
        return [
            $this->language => [
                'id' => $this->id,
                'title' => $this->title,
            ],
        ];
    }
}
