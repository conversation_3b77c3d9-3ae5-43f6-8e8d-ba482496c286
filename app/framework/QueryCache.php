<?php

declare(strict_types=1);

namespace app\framework;

use Psr\SimpleCache\CacheInterface;
use src\framework\query\SqlWriter;
use InvalidParameterException;

use function in_array;

class QueryCache
{
    public const SHA_256_ALGO = 'sha256';
    public const CRC_32_C_ALGO = 'crc32c';
    private string $hashAlgo;

    /** @var CacheInterface */
    private $cacheEngine;

    /** @var SqlWriter */
    private $sqlWriter;
    private array $hashAlgoList;

    public function __construct(CacheInterface $cache, SqlWriter $sqlWriter)
    {
        $this->cacheEngine = $cache;
        $this->sqlWriter = $sqlWriter;
        $this->hashAlgo = self::SHA_256_ALGO;
        $this->hashAlgoList = [self::SHA_256_ALGO, self::CRC_32_C_ALGO];
    }

    /**
     * @throws InvalidParameterException
     */
    public function setHashAlgo(string $hashAlgo): void
    {
        if (!in_array($hashAlgo, $this->hashAlgoList, true)) {
            throw new InvalidParameterException(
                printf(
                    'At the moment you can only use %u or %s algorithms.',
                    self::SHA_256_ALGO,
                    self::CRC_32_C_ALGO,
                ),
            );
        }
        $this->hashAlgo = $hashAlgo;
    }

    /**
     * Determines whether or not the result set is present in the cache.
     */
    public function has(string $namespace, string $sql, array $parameters = []): bool
    {
        if (!$this->cacheEngine->has($namespace)) {
            return false;
        }

        $resultSets = $this->cacheEngine->get($namespace);
        $cacheKey = $this->getCacheKey($sql, $parameters);

        return isset($resultSets[$cacheKey]);
    }

    /**
     * Returns the cached result set for this query, or an empty array if none exists.
     * Cannot be used to determine the existence of the result set in the cache - use QueryCache::has() instead.
     */
    public function get(string $namespace, string $sql, array $parameters = []): array
    {
        $resultSets = $this->cacheEngine->get($namespace, []);
        $cacheKey = $this->getCacheKey($sql, $parameters);

        return $resultSets[$cacheKey] ?? [];
    }

    /**
     * Persists a query result set in the cache.
     *
     * @return bool Indicating success or failure
     */
    public function set(
        string $namespace,
        array $resultSet,
        string $sql,
        array $parameters = [],
        ?int $timeToLeaveSeconds = null
    ): bool {
        $cacheKey = $this->getCacheKey($sql, $parameters);
        $resultSets = $this->cacheEngine->get($namespace, []);
        $resultSets[$cacheKey] = $resultSet;

        return $this->cacheEngine->set($namespace, $resultSets, $timeToLeaveSeconds);
    }

    /**
     * Invalidates the cached result sets stored under this namespace, e.g. when the underlying data has changed.
     *
     * @return bool Indicating success or failure
     */
    public function clear(string $namespace): bool
    {
        return $this->cacheEngine->delete($namespace);
    }

    /**
     * Generates a cache key for a given query.
     */
    private function getCacheKey(string $sql, array $parameters = []): string
    {
        if (!empty($parameters)) {
            $sql = $this->sqlWriter->combineSqlAndParameters($sql, $parameters);
        }

        return hash($this->hashAlgo, $sql);
    }
}
