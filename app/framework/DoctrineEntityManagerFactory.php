<?php

declare(strict_types=1);

namespace app\framework;

use app\models\framework\config\DatixConfigFactory;
use Doctrine\Common\Cache\Psr6\DoctrineProvider;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\ORMSetup;
use Psr\SimpleCache\CacheInterface;
use src\system\container\facade\Container;
use Symfony\Component\Cache\Adapter\Psr16Adapter;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
final class DoctrineEntityManagerFactory
{
    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\ORM\ORMException
     *
     * @see Container::get()
     *
     * @deprecated use DI or Container instead
     */
    public function getInstance(): EntityManager
    {
        static $entityManager = null;

        if ($entityManager !== null) {
            return $entityManager;
        }

        $doctrineConfig = ORMSetup::createAttributeMetadataConfiguration(
            [__DIR__ . '/../models'],
            getenv('MODE') === 'DEV',
            __DIR__ . '/_doctrineProxies',
        );
        $connection = (new DBALConnectionFactory())->getInstance();

        $config = (new DatixConfigFactory())->getInstance();
        if ($config->isDoctrineMetadataCacheEnabled()) {
            $cacheQuery = DoctrineProvider::wrap(
                new Psr16Adapter(
                    Container::get(CacheInterface::class),
                    'doctrine.query.cache',
                ),
            );
            $doctrineConfig->setQueryCacheImpl($cacheQuery);
        }

        if ($config->isDoctrineMetadataCacheEnabled()) {
            $cacheQuery = new Psr16Adapter(
                Container::get(CacheInterface::class),
                'doctrine.metadata.cache',
            );
            $doctrineConfig->setMetadataCache($cacheQuery);
        }

        $entityManager = EntityManager::create($connection, $doctrineConfig);

        return $entityManager;
    }
}
