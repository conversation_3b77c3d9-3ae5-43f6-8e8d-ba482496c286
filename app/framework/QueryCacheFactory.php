<?php

namespace app\framework;

use src\framework\query\SqlWriter;
use src\system\container\facade\Container;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class QueryCacheFactory
{
    public function create(): QueryCache
    {
        return new QueryCache(
            new GlobalsCacheEngine(),
            Container::get(SqlWriter::class),
        );
    }
}
