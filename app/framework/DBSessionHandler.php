<?php

namespace app\framework;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use SessionHandlerInterface;
use SessionUpdateTimestampHandlerInterface;

use function ini_get;
use function is_resource;
use function stream_get_contents;
use function time;

class DBSessionHandler implements SessionHandlerInterface, SessionUpdateTimestampHandlerInterface
{
    private Connection $dbh;
    private bool $gcCalled = false;
    private bool $sessionExpired = false;
    private ?string $prefetchId = null;
    private ?string $prefetchData = null;
    private ?string $newSessionId = null;

    public function __construct(Connection $dbh)
    {
        $this->dbh = $dbh;
    }

    public function isSessionExpired(): bool
    {
        return $this->sessionExpired;
    }

    /**
     * {@inheritdoc}
     */
    public function close(): bool
    {
        if ($this->gcCalled) {
            $this->gcCalled = false;

            $sql = 'DELETE FROM php_session where sess_lifetime < :time';
            $stmt = $this->dbh->prepare($sql);
            $stmt->bindValue(':time', time(), ParameterType::INTEGER);
            $stmt->executeStatement();
        }

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function destroy($id): bool
    {
        return $this->newSessionId === $id || $this->doDestroy($id);
    }

    /**
     * {@inheritdoc}
     */
    public function gc($max_lifetime)
    {
        $this->gcCalled = true;

        return 0;
    }

    /**
     * {@inheritdoc}
     */
    public function open($path, $name): bool
    {
        $this->sessionExpired = false;

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function read($id)
    {
        if ($this->prefetchId !== null) {
            $prefetchId = $this->prefetchId;
            $prefetchData = $this->prefetchData;
            unset($this->prefetchId, $this->prefetchData);

            if ($prefetchId === $id || $prefetchData === '') {
                $this->newSessionId = $prefetchData === '' ? $id : null;

                return $prefetchData;
            }
        }

        $data = $this->doRead($id);
        $this->newSessionId = $data === '' ? $id : null;

        return $data;
    }

    /**
     * {@inheritdoc}
     */
    public function write($id, $data): bool
    {
        if ($data === '') {
            return $this->destroy($id);
        }
        $this->newSessionId = null;

        return $this->doWrite($id, $data);
    }

    /**
     * {@inheritdoc}
     */
    public function validateId($key): bool
    {
        $this->prefetchData = $this->read($key);
        $this->prefetchId = $key;

        return $this->prefetchData !== '';
    }

    /**
     * {@inheritdoc}
     */
    public function updateTimestamp($key, $val)
    {
        $expiry = time() + (int) ini_get('session.gc_maxlifetime');

        $sql = 'UPDATE php_session SET sess_lifetime = :expiry, sess_time = :time WHERE sess_id = :id';

        $stmt = $this->dbh->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':expiry', $expiry, ParameterType::INTEGER);
        $stmt->bindValue(':time', time(), ParameterType::INTEGER);
        $stmt->executeStatement();

        return true;
    }

    private function doRead(string $id)
    {
        $selectSql = 'SELECT sess_data, sess_lifetime FROM php_session WHERE sess_id = :id';

        $selectStmt = $this->dbh->prepare($selectSql);
        $selectStmt->bindParam('id', $id);

        $sessionRows = $selectStmt->executeQuery()->fetchAssociative();
        if ($sessionRows) {
            $expiry = (int) $sessionRows['sess_lifetime'];
            if ($expiry < time()) {
                $this->sessionExpired = true;

                return '';
            }

            return is_resource($sessionRows['sess_data']) ?
                stream_get_contents($sessionRows['sess_data']) : $sessionRows['sess_data'];
        }

        return '';
    }

    private function doDestroy(string $id): bool
    {
        $sql = 'DELETE FROM php_session WHERE sess_id = :id';

        $stmt = $this->dbh->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->executeStatement();

        return true;
    }

    private function doWrite(string $id, string $data): bool
    {
        $sql = <<<'SQL'
            BEGIN TRANSACTION;
            UPDATE dbo.php_session WITH (UPDLOCK, SERIALIZABLE) SET sess_data = :update_data, sess_lifetime = :update_expiry, sess_time = :update_time WHERE sess_id = :match_id;

            IF @@ROWCOUNT = 0
            BEGIN
               INSERT dbo.php_session(sess_id, sess_data, sess_lifetime, sess_time) VALUES (:insert_id, :insert_data, :insert_expiry, :insert_time);
            END
            COMMIT TRANSACTION;
            SQL;

        $maxlifetime = (int) ini_get('session.gc_maxlifetime');

        $stmt = $this->dbh->prepare($sql);
        $stmt->bindParam(':match_id', $id);
        $stmt->bindParam(':insert_id', $id);
        $stmt->bindParam(':insert_data', $data, ParameterType::LARGE_OBJECT);
        $stmt->bindValue(':insert_expiry', time() + $maxlifetime, ParameterType::INTEGER);
        $stmt->bindValue(':insert_time', time(), ParameterType::INTEGER);
        $stmt->bindParam(':update_data', $data, ParameterType::LARGE_OBJECT);
        $stmt->bindValue(':update_expiry', time() + $maxlifetime, ParameterType::INTEGER);
        $stmt->bindValue(':update_time', time(), ParameterType::INTEGER);
        $stmt->executeStatement();

        return true;
    }
}
