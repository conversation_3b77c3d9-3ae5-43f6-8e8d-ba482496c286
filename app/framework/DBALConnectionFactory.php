<?php

namespace app\framework;

use app\models\framework\config\DatixConfigFactory;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception as DBALException;
use Doctrine\DBAL\DriverManager;
use Doctrine\DBAL\Types\Type;
use app\models\types\DateTime;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class DBALConnectionFactory
{
    /**
     * @throws DBALException
     */
    public function getInstance(): Connection
    {
        static $connection = null;

        if ($connection !== null) {
            return $connection;
        }

        $config = (new DatixConfigFactory())->getInstance();

        $credentials = [
            'dbname' => $config->getDbName(),
            'user' => $config->getDbUsername(),
            'password' => $config->getDbPassword(),
            'host' => $config->getDbServerName(),
            'driver' => 'pdo_sqlsrv',
        ];

        $connection = DriverManager::getConnection($credentials);

        // Register custom types not supported by Doctrine
        Type::overrideType('datetime', DateTime::class);
        $connection->getDatabasePlatform()->registerDoctrineTypeMapping('datetime', 'datetime');

        return $connection;
    }
}
