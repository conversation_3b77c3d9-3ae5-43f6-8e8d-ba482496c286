<?php

namespace app\framework;

use ArrayIterator;
use Psr\SimpleCache\CacheInterface;
use Symfony\Component\Cache\Exception\InvalidArgumentException;
use Traversable;

use function is_array;

/**
 * Allows caching of values per-request by storing them in the $GLOBALS array.
 * TTL is not supported.
 */
class GlobalsCacheEngine implements CacheInterface
{
    public function get($key, $default = null)
    {
        $this->validateKey($key);

        return $GLOBALS[self::class][$key] ?? $default;
    }

    public function set($key, $value, $ttl = null)
    {
        $this->validateKey($key);
        $GLOBALS[self::class][$key] = $value;

        return true;
    }

    public function delete($key)
    {
        $this->validateKey($key);
        unset($GLOBALS[self::class][$key]);

        return true;
    }

    public function clear()
    {
        unset($GLOBALS[self::class]);

        return true;
    }

    public function getMultiple($keys, $default = null)
    {
        $this->validateKeys($keys);
        $values = [];

        foreach ($keys as $key) {
            $values[$key] = $this->get($key, $default);
        }

        return $values;
    }

    public function setMultiple($values, $ttl = null)
    {
        $this->validateKeys(array_keys(iterator_to_array(new ArrayIterator($values))));

        foreach ($values as $key => $value) {
            $this->set($key, $value, $ttl);
        }

        return true;
    }

    public function deleteMultiple($keys)
    {
        $this->validateKeys($keys);

        foreach ($keys as $key) {
            $this->delete($key);
        }

        return true;
    }

    public function has($key)
    {
        return isset($GLOBALS[self::class][$key]);
    }

    private function validateKey($key): void
    {
        if ($key === '') {
            throw new InvalidArgumentException('A cache key must be a non-empty string');
        }
    }

    private function validateKeys($keys): void
    {
        if (!is_array($keys) && !($keys instanceof Traversable)) {
            throw new InvalidArgumentException('A cache key set must be either an array or a Traversable');
        }

        foreach ($keys as $key) {
            $this->validateKey($key);
        }
    }
}
