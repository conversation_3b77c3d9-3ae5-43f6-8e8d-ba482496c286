<?php

declare(strict_types=1);

namespace app\framework;

use Doctrine\DBAL\Connection;
use Psr\SimpleCache\CacheInterface;

use function in_array;

use const JSON_THROW_ON_ERROR;

class QueryResultsCache
{
    private const QUERY_HASHES_KEY = 'query_hashes';
    private CacheInterface $cache;
    private Connection $connection;

    public function __construct(
        CacheInterface $cache,
        Connection $connection
    ) {
        $this->cache = $cache;
        $this->connection = $connection;
    }

    public function getResults(
        string $sql,
        array $parameters,
        int $lifetime = 3600
    ): array {
        ksort($parameters);
        $cacheKey = $this->getCacheKey($sql, $parameters);
        $results = $this->cache->get($cacheKey);

        if (
            $results !== null
            && $sql === $results['query']
            && json_encode($parameters) === json_encode($results['parameters'])
        ) {
            return $results['results'];
        }

        $results = $this->runQuery($sql, $parameters);

        $this->cache->set(
            $cacheKey,
            [
                'query' => $sql,
                'parameters' => $parameters,
                'results' => $results,
            ],
            $lifetime,
        );

        $this->addQueryHashToCache($cacheKey);

        return $results;
    }

    public function clearCacheForTable(string $table): void
    {
        $allHashes = $this->cache->get(self::QUERY_HASHES_KEY);
        if ($allHashes === null) {
            return;
        }

        foreach ($allHashes as &$cacheKey) {
            $cacheEntry = $this->cache->get($cacheKey);
            if ($cacheEntry === null) {
                $cacheKey = null;

                continue;
            }

            if (str_contains($cacheEntry['query'], $table)) {
                $this->cache->delete($cacheKey);
                $cacheKey = null;
            }
        }

        $this->cache->set(self::QUERY_HASHES_KEY, array_filter($allHashes));
    }

    private function getCacheKey(
        string $sql,
        array $parameters
    ): string {
        $cacheKey = $sql . json_encode($parameters, JSON_THROW_ON_ERROR);

        return hash('sha256', $cacheKey);
    }

    private function runQuery(
        string $sql,
        array $parameters
    ): array {
        return $this->connection->executeQuery($sql, $parameters)->fetchAllAssociative();
    }

    private function addQueryHashToCache(string $cacheKey): void
    {
        $allHashes = $this->cache->get(self::QUERY_HASHES_KEY, []);

        if (!in_array($cacheKey, $allHashes)) {
            $allHashes[] = $cacheKey;
            $this->cache->set(self::QUERY_HASHES_KEY, $allHashes, 604800);
        }
    }
}
