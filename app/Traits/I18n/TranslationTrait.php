<?php

namespace app\Traits\I18n;

use Doctrine\DBAL\Connection;
use InvalidArgumentException;

use function in_array;

trait TranslationTrait
{
    /** @var Connection */
    protected $connection;

    protected function escapeKey(string $key): string
    {
        return str_replace(
            ['(', ')', '%', '\\', '"', '\''],
            '',
            str_replace(['*', '+', ' ', '.', '/'], '_', $key),
        );
    }

    protected function generatePlaceholderRef(string $key, string $type, string $domain): string
    {
        return strtolower(implode(':', [$type, $domain, $this->escapeKey($key)]));
    }

    protected function getLocales(): array
    {
        $locales = [];
        $available = $this->connection->fetchAllAssociative('select id, LOWER(code) as locale from languages');
        foreach ($available as $locale) {
            $locales[$locale['locale']] = (int) $locale['id'];
        }

        return $locales;
    }

    private function validateLocale(string $locale, array $available): void
    {
        $list = array_keys($available);
        if (!in_array($locale, $list, true)) {
            throw new InvalidArgumentException(
                'Invalid locale specified, supported locale are: ' . implode(', ', $list),
            );
        }
    }
}
