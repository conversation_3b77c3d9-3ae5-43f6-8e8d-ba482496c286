<?php

declare(strict_types=1);

namespace app\Traits\Migration;

trait DataDuplicationTrait
{
    /**
     * Delete duplicate records from a table based on a match of the provided columns.
     *
     * @param string[] $columns
     * @param bool $preferOldest a flag to determine which record to keep, by default will always keep the newest entered into the DB (highest ID)
     */
    protected function deleteDuplicates(string $tableName, array $columns, bool $preferOldest = false): void
    {
        $operand = $preferOldest ? '>' : '<';
        $sql = "DELETE c1
            FROM
                {$tableName} c1
            WHERE EXISTS (
                SELECT 1
                FROM {$tableName} c2
                WHERE c1.id != c2.id -- Ensure it's a different record
                AND c1.id {$operand} c2.id -- Keep the newest or oldest records, based on {$preferOldest}
            ";

        foreach ($columns as $column) {
            $sql = $sql . " AND ((c1.{$column} = c2.{$column}) OR (c1.{$column} IS NULL AND c2.{$column} IS NULL))";
        }
        $sql = $sql . ');';

        $this->execute($sql);
    }

    /**
     * Execute the provided SQL query.
     */
    abstract protected function execute(string $sql);
}
