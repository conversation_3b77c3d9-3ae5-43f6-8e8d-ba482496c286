<?php

namespace app\controllers\admin;

use app\models\framework\flashMessages\ErrorMessage;
use app\models\framework\flashMessages\FlashMessageContainerFactory;
use app\models\framework\flashMessages\InfoMessage;
use src\admin\model\services\BandBatchUpdater;
use src\framework\controller\TemplateController;

/**
 * @codeCoverageIgnore
 */
class BatchUpdateBandFieldController extends TemplateController
{
    /**
     * batch update link_age_band value
     * set link_age_band to a coded value from the code_age_band table.
     *
     * @retrun never
     */
    public function batchUpdateAgeBand(): void
    {
        $flashMessageContainer = (new FlashMessageContainerFactory())->create();
        $results = (new BandBatchUpdater())->updateAgeBand();

        if ($results) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('age_band_batch_update_success')));
        } else {
            $flashMessageContainer->addMessage(new ErrorMessage("'Age band' was not batch updated successfully"));
        }

        $this->redirect('index.php?action=home&module=ADM');
    }

    /**
     * batch update inc_time_band value
     * set inc_time_band to a coded value from the code_time_band table.
     *
     * @return never
     */
    public function batchUpdateTimeBand(): void
    {
        $flashMessageContainer = (new FlashMessageContainerFactory())->create();
        $results = (new BandBatchUpdater())->updateTimeBand();

        if ($results) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('time_band_batch_update_success')));
        } else {
            $flashMessageContainer->addMessage(new ErrorMessage("'Time band' was not batch updated successfully"));
        }

        $this->redirect('index.php?action=home&module=ADM');
    }
}
