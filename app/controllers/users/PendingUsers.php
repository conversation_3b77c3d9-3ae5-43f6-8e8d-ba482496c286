<?php

declare(strict_types=1);

namespace app\controllers\users;

use app\services\PendingUsers\PendingUsersServiceInterface;
use src\framework\controller\Controller;

use const JSON_THROW_ON_ERROR;

class PendingUsers extends Controller
{
    /** @readonly */
    private PendingUsersServiceInterface $service;

    public function __construct(PendingUsersServiceInterface $service)
    {
        $this->service = $service;
    }

    public function pendingusers(): void
    {
        $this->response->setBody(json_encode([
            'count' => $this->service->getPendingUsers(),
        ], JSON_THROW_ON_ERROR));
    }
}
