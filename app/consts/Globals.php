<?php

declare(strict_types=1);

namespace app\consts;

use Exception;
use RuntimeException;

final class Globals
{
    public const INC_BYPASS_MANDATORY_FIELDS = 'INC_BYPASS_MANDATORY_FIELDS';
    public const INC_BYPASS_MANDATORY_FIELDS_VAL_Y = 'Y';
    public const INC_BYPASS_MANDATORY_FIELDS_VAL_N = 'N';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        throw new RuntimeException("Can't get an instance of FieldDefKeys");
    }
}
