<?php

namespace app\Hydrator;

use app\models\language\entities\LanguageEntity;

class LanguageHydrate implements Hydrator
{
    /**
     * @param LanguageEntity $entity
     */
    public function hydrate(array $data, $entity): LanguageEntity
    {
        $entity->setDescription($data['description']);
        $entity->setCode($data['code']);
        $entity->setRtl($data['rtl']);

        return $entity;
    }

    /**
     * @param LanguageEntity $entity
     */
    public function extract($entity): array
    {
        return [
            'id' => $entity->getId(),
            'description' => $entity->getDescription(),
            'code' => $entity->getCode(),
            'rtl' => $entity->getRtl(),
        ];
    }
}
