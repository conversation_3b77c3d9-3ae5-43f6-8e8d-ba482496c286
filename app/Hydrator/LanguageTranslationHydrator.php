<?php

namespace app\Hydrator;

use app\models\language\entities\LanguagesTranslation;

class LanguageTranslationHydrator implements Hydrator
{
    /**
     * @param $entity LanguagesTranslation
     *
     * @return mixed
     */
    public function hydrate(array $data, $entity): LanguagesTranslation
    {
        $entity->setLanguage($data['language']);
        $entity->setTranslationLanguage($data['translationLanguage']);
        $entity->setTranslation($data['translation']);

        return $entity;
    }

    /**
     * @param LanguagesTranslation $entity
     */
    public function extract($entity): array
    {
        $languageHydrator = new LanguageHydrate();

        return [
            'language' => $languageHydrator->extract($entity->getLanguage()),
            'translationLanguage' => $languageHydrator->extract($entity->getTranslationLanguage()),
            'translation' => $entity->getTranslation(),
        ];
    }
}
