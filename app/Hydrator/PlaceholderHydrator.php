<?php

namespace app\Hydrator;

use app\models\multiLanguage\entities\Placeholder;
use app\models\multiLanguage\entities\Translation;

use function is_array;

class PlaceholderHydrator implements Hydrator
{
    /**
     * @param Placeholder $entity
     */
    public function hydrate(array $data, $entity): Placeholder
    {
        $translationHydrator = new TranslationHydrator();

        $entity->setKey($data['key']);
        $entity->setType($data['type']);
        $entity->setDomain($data['domain']);
        $entity->addTranslations(array_map(static function ($item) use ($translationHydrator): array {
            if (is_array($item)) {
                return $translationHydrator->hydrate($item, new Translation());
            }

            return $item;
        }, $data['translations'] ?? []));

        return $entity;
    }

    /**
     * @param Placeholder $entity
     */
    public function extract($entity): array
    {
        $translationHydrator = new TranslationHydrator();

        return [
            'id' => $entity->getId(),
            'key' => $entity->getKey(),
            'type' => $entity->getType(),
            'domain' => $entity->getDomain(),
            'translations' => array_map(static function (Translation $item) use ($translationHydrator) {
                return $translationHydrator->extract($item);
            }, $entity->getTranslations()->toArray()),
        ];
    }
}
