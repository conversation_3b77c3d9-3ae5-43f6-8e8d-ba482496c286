<?php

declare(strict_types=1);

namespace app\Hydrator;

use DateTime;
use DateTimeInterface;
use src\logger\Facade\Log;
use Throwable;

final class DateHydrator implements DateHydratorInterface
{
    private string $format;

    public function __construct(string $dateRegion)
    {
        $this->format = $dateRegion === 'US' ? 'm/d/Y' : 'd/m/Y';
    }

    public function hydrate(?string $date): ?DateTime
    {
        if (empty($date)) {
            return null;
        }

        $local = '/^\d{2}\/\d{2}\/\d{4}$/';
        $iso8601 = '/^\d{4}-\d{1,2}-\d{1,2}$/';
        $dateTime = null;

        if (preg_match($local, $date)) {
            $dateTime = DateTime::createFromFormat($this->format, $date);
        }

        if (preg_match($iso8601, $date)) {
            try {
                $dateTime = new DateTime($date);
            } catch (Throwable $e) {
                Log::error(
                    "Error hydrating date value {$date}",
                    ['exception' => $e],
                );
            }
        }

        if ($dateTime === false) {
            $dateTime = null;
        }

        if ($dateTime !== null) {
            $dateTime->setTime(0, 0);
        }

        return $dateTime;
    }

    public function extract(?DateTimeInterface $dateTime): ?string
    {
        if ($dateTime === null) {
            return null;
        }

        return $dateTime->format('Y-m-d');
    }
}
