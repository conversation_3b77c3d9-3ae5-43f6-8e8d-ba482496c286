<?php

namespace app\Hydrator;

use app\models\language\entities\LanguageEntity;
use app\models\multiLanguage\entities\Placeholder;
use app\models\multiLanguage\entities\Translation;

use function is_array;

class TranslationHydrator implements Hydrator
{
    /**
     * @param Translation $entity
     *
     * @return Translation
     */
    public function hydrate(array $data, $entity)
    {
        $placeholder = $data['placeholder'];
        if (is_array($data['placeholder'])) {
            $placeholder = (new PlaceholderHydrator())->hydrate($placeholder, new Placeholder());
        }

        $language = $data['language'];
        if (is_array($language)) {
            $language = (new LanguageHydrate())->hydrate($data['language'], new LanguageEntity());
        }

        $entity->setPlaceholder($placeholder);
        $entity->setLanguage($language);
        $entity->setSystemTranslation($data['system_translation']);
        $entity->setUserTranslation($entity->getUserTranslation() ?? $data['user_translation'] ?? null);

        return $entity;
    }

    /**
     * @param Translation $entity
     */
    public function extract($entity): array
    {
        return [
            'placeholder' => (new PlaceholderHydrator())->extract($entity->getPlaceholder()),
            'language' => (new LanguageHydrate())->extract($entity->getLanguage()),
            'system_transaction' => $entity->getSystemTranslation(),
            'user_transaction' => $entity->getUserTranslation(),
        ];
    }
}
