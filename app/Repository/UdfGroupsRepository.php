<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class UdfGroupsRepository extends BaseRepository
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::UDF_GROUPS, 'recordid', $connection);
    }

    public function getByRecordId($recordid): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('udf.grp_descr, udf.mod_id, mod.mod_module as mod_name')
            ->from(Tables::UDF_GROUPS, 'udf')
            ->innerJoin('udf', 'modules', 'mod', 'mod.mod_id = udf.mod_id')
            ->andWhere('udf.recordid = :recordid')
            ->setParameter('recordid', $recordid);

        return $query->executeQuery()->fetchAssociative();
    }
}
