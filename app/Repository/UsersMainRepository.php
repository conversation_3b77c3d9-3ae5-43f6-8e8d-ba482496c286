<?php

declare(strict_types=1);

namespace app\Repository;

use Doctrine\DBAL\Connection;

class UsersMainRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getProfileByUserId(int $userId): int
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->addSelect('um.sta_profile')
            ->from('users_main', 'um')
            ->where('recordid = :userId')
            ->setParameter('userId', $userId);

        return (int) $query->executeQuery()->fetchOne();
    }
}
