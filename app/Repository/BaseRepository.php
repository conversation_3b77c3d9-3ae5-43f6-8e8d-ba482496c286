<?php

declare(strict_types=1);

namespace app\Repository;

use Doctrine\DBAL\Connection;

abstract class BaseRepository
{
    protected Connection $connection;
    protected string $table;
    protected string $primaryKey;

    public function __construct(string $table, string $primaryKey, Connection $connection)
    {
        $this->connection = $connection;
        $this->table = $table;
        $this->primaryKey = $primaryKey;
    }

    public function recordExists($recordId): bool
    {
        $qb = $this->connection->createQueryBuilder();
        $record = $qb->select($this->primaryKey)
            ->from($this->table)
            ->andWhere($this->primaryKey . ' = :recordId')
            ->setParameter('recordId', $recordId)
            ->executeQuery()
            ->fetchOne();

        return $record !== false;
    }
}
