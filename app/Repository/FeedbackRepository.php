<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;

class FeedbackRepository extends BaseRepository
{
    use HasFlaggedForInvestigation;

    public function insertIntoLinkCompl($recordId, $conId, $comId, string $lcomCurrent): void
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->insert(Tables::INCIDENT_CONTACT_LINK)
            ->values([
                'recordid' => ':recordId',
                'con_id' => ':conId',
                'com_id' => ':comId',
                'lcom_current' => ':lcomCurrent',
            ])
            ->setParameters([
                'recordId' => $recordId,
                'conId' => $conId,
                'comId' => $comId,
                'lcomCurrent' => $lcomCurrent,
            ])
            ->executeStatement();
    }

    public function getLinkedFeedbackSubjects($feedbackId): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->select('cs.*')
            ->from(Tables::FEEDBACK_SUBJECTS, 'cs')
            ->where('cs.COM_ID = :feedbackId')
            ->setParameter('feedbackId', $feedbackId)
            ->executeQuery()
            ->fetchAllAssociative();
    }

    public function setFeedbackSubjectToEmpty($feedbackId): void
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->update($this->table)
            ->set('com_subject1', "''")
            ->set('com_subsubject1', "''")
            ->where('recordid = :recordId')
            ->setParameter('recordId', $feedbackId)
            ->executeQuery();
    }
}
