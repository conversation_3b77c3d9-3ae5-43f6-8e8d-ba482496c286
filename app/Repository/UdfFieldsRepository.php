<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class UdfFieldsRepository extends BaseRepository
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::UDF_FIELDS, 'recordid', $connection);
    }

    public function getAllByGroupId($groupId): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('udf.recordid AS fld_id, udf.fld_name, udf.fld_type')
            ->from(Tables::UDF_FIELDS, 'udf')
            ->innerJoin('udf', 'udf_links', 'link', 'udf.recordid = link.field_id')
            ->andWhere('link.group_id = :groupId')
            ->setParameter('groupId', $groupId)
            ->orderBy('link.listorder');

        return $query->executeQuery()->fetchAllAssociative();
    }

    public function getExtraFieldListByModuleAndLanguage($module, $language)
    {
        $modId = ModuleCodeToID($module == Module::ADMIN ? Module::CONTACTS : $module);

        $sql = <<<'RAWSQL'
            SELECT
                CAST(udf_fields.recordid as varchar) + '_' + CAST(UDF_LINKS.group_id as varchar) as udf_id,
                COALESCE(udf_fields_descr.description, fld_name) + ' (' + udf_groups.grp_descr + ')' as fld_name
            FROM UDF_FIELDS
            LEFT JOIN UDF_MOD_LINK ON uml_id = udf_fields.recordid
            LEFT JOIN UDF_LINKS on UDF_LINKS.field_id = udf_fields.recordid
            LEFT JOIN udf_fields_descr ON udf_fields.recordid = udf_fields_descr.fieldId AND udf_fields_descr.language = :language
            JOIN UDF_GROUPS on UDF_groups.recordid = UDF_LINKS.group_id
            WHERE (udf_groups.mod_id = :mod_id) AND (uml_module = :module OR uml_module IS NULL)
            UNION
            SELECT
                CAST(udf_fields.recordid as varchar) + '_0' as udf_id,
                COALESCE(udf_fields_descr.description, fld_name)
            FROM UDF_FIELDS
            LEFT JOIN UDF_MOD_LINK ON uml_id = udf_fields.recordid
            LEFT JOIN udf_fields_descr ON udf_fields.recordid = udf_fields_descr.fieldId AND udf_fields_descr.language = :language2
            WHERE (uml_module = :module2 OR uml_module IS NULL)
            ORDER BY fld_name
            RAWSQL;

        return $this->connection->executeQuery($sql, [
            'module' => $module,
            'module2' => $module,
            'mod_id' => $modId,
            'language' => $language,
            'language2' => $language,
        ])->fetchAllKeyValue();
    }

    public function insertModuleLink($recordId, $moduleId)
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->insert(Tables::UDF_MODULE_LINK)
            ->values([
                'uml_id' => ':umlId',
                'uml_module' => ':umlModule',
            ])
            ->setParameters([
                ':umlId' => $recordId,
                ':umlModule' => $moduleId,
            ])
            ->executeStatement();
    }
}
