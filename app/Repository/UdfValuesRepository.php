<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class UdfValuesRepository extends BaseRepository
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::UDF_VALUES, 'udf_value_id', $connection);
    }

    public function getExtraFieldValues($recordid, $module): array
    {
        $moduleId = GetModIDFromShortName($module);

        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('field_id, group_id, udv_string, udv_number, udv_date, udv_money, udv_text')
            ->from(Tables::UDF_VALUES)
            ->andWhere('mod_id = :mod_id')
            ->andWhere('cas_id = :cas_id')
            ->setParameter('mod_id', $moduleId)
            ->setParameter('cas_id', $recordid);

        $extraFieldValues = [];
        foreach ($query->executeQuery()->fetchAllAssociative() as $resultSet) {
            $extraFieldValues[$recordid][$moduleId][$resultSet['field_id']][$resultSet['group_id']] = [
                'udv_string' => $resultSet['udv_string'],
                'udv_number' => $resultSet['udv_number'],
                'udv_date' => $resultSet['udv_date'],
                'udv_money' => $resultSet['udv_money'],
                'udv_text' => $resultSet['udv_text'],
            ];
        }

        return $extraFieldValues;
    }

    /**
     * @param positive-int $recordId
     * @param positive-int $fieldId
     * @param positive-int $modId
     *
     * @return false|array{
     *             udv_string?:string,
     *             udv_number?:string,
     *             udv_date?:string,
     *             udv_money?:string,
     *             udv_text?:string
     *         }
     */
    public function getValue(int $recordId, int $fieldId, int $modId)
    {
        // RecordID is empty for new records, we'll never be able to find results so just return early
        if (empty($recordId)) {
            return false;
        }

        $qb = $this->connection->createQueryBuilder();

        $qb->select('udv_string, udv_number, udv_date, udv_money, udv_text')
            ->from($this->table)
            ->andWhere('cas_id = :cas_id')
            ->andWhere('field_id = :field_id')
            ->andWhere('mod_id = :mod_id')
            ->setParameter('cas_id', $recordId)
            ->setParameter('field_id', $fieldId)
            ->setParameter('mod_id', $modId);

        return $qb->executeQuery()->fetchAssociative();
    }
}
