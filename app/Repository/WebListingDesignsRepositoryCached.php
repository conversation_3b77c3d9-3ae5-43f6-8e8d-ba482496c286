<?php

declare(strict_types=1);

namespace app\Repository;

use Psr\SimpleCache\CacheInterface;

/**
 * @codeCoverageIgnore
 */
class WebListingDesignsRepositoryCached implements WebListingDesignsRepositoryInterface
{
    private CacheInterface $cache;
    private WebListingDesignsRepositoryInterface $repository;

    public function __construct(CacheInterface $cache, WebListingDesignsRepositoryInterface $repository)
    {
        $this->cache = $cache;
        $this->repository = $repository;
    }

    public function getListOfListingDesigns($module): array
    {
        $value = $this->cache->get("listing_design.list.{$module}");

        if ($value !== null) {
            return $value;
        }

        $value = $this->repository->getListOfListingDesigns($module);

        $this->cache->set("listing_design.list.{$module}", $value);

        return $value;
    }
}
