<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\valueObjects\Module;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;

use function is_array;

class DashboardRepository
{
    public const TYPE_PROFILE = 'PROFIL';
    public const TYPE_GROUP = 'GROUP';
    public const TYPE_USER = 'USER';
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /**
     * @retunr int[]
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getDashboardsByOwner(int $userId): array
    {
        $query = $this->getDashboardOwnerQuery($userId);

        return array_map('intval', $query->executeQuery()->fetchFirstColumn());
    }

    public function getCurrentDashboard(int $userId): int
    {
        $query = $this->getDashboardOwnerQuery($userId)
            ->setFirstResult(0);

        return (int) $query->executeQuery()->fetchOne();
    }

    /**
     * @return int[]
     *
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function getDashboardsByUsersProfile(int $profileId): array
    {
        $query = $this->getDashboardsByLinkType($profileId, self::TYPE_PROFILE);

        return array_map('intval', $query->executeQuery()->fetchFirstColumn());
    }

    /**
     * @param int[] $groupIds
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getDashboardsBySecurityGroups(array $groupIds): array
    {
        $result = $this->getDashboardsByLinkType($groupIds, self::TYPE_GROUP)
            ->executeQuery()
            ->fetchFirstColumn();

        return array_map('intval', $result);
    }

    /**
     * @return int[]
     *
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function getDashboardsByUserId(int $userId): array
    {
        $result = $this->getDashboardsByLinkType($userId, self::TYPE_USER)
            ->executeQuery()
            ->fetchFirstColumn();

        return array_map('intval', $result);
    }

    public function getPreviewDashboard(int $id): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->addSelect('rd.recordid')
            ->from('reports_dashboards', 'rd')
            ->andWhere('rd.recordid = :recordid')
            ->setParameter('recordid', $id)
            ->orderBy('rd.recordid');

        return array_map('intval', $query->executeQuery()->fetchFirstColumn());
    }

    public function getWidgetsByDashboard(int $id): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->addSelect('rdl.recordid, rdl.dlink_dash_id, rdl.dlink_title, rdl.dlink_package_id, rdl.dlink_row_limit, rdl.dlink_col_limit, rdl.dlink_collapse_headings, rdl.dlink_column, wr.module')
            ->from('reports_dash_links', 'rdl')
            ->leftJoin('rdl', 'web_packaged_reports', 'wpr', 'wpr.recordid = rdl.dlink_package_id')
            ->leftJoin('wpr', 'web_reports', 'wr', 'wr.recordid = wpr.web_report_id')
            ->andWhere('rdl.dlink_dash_id = :recordid')
            ->setParameter('recordid', $id)
            ->orderBy('rdl.dlink_order');

        return $query->executeQuery()->fetchAllAssociative();
    }

    /**
     * @return int[]
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getOwnersDashboardById(int $id): array
    {
        $results = $this->connection->executeQuery(
            'SELECT owner_id FROM reports_dashboards_owners WHERE dash_id = :id',
            ['id' => $id],
        )->fetchFirstColumn();

        return array_map('intval', $results);
    }

    public function getInfoById(int $id): ?array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('rd.recordid, rd.dash_name, rd.dash_graphs_per_row, rd.import_id, rdl.dash_link_type, rdl.dash_link_id')
            ->from('reports_dashboards', 'rd')
            ->leftJoin('rd', 'reports_dashboards_links', 'rdl', 'rd.recordid = rdl.dash_id')
            ->where('rd.recordid = :id')
            ->setParameter('id', $id);

        return $query->executeQuery()->fetchAllAssociative() ?: null;
    }

    public function getInfoByUserId(int $id): ?array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('rd.recordid, rd.dash_name, rd.dash_graphs_per_row, rd.import_id, rdl.dash_link_type, rdl.dash_link_id')
            ->from('reports_dashboards', 'rd')
            ->innerJoin('rd', 'reports_dashboards_owners', 'rdo', 'rd.recordid = rdo.dash_id')
            ->leftJoin('rd', 'reports_dashboards_links', 'rdl', 'rd.recordid = rdl.dash_id')
            ->where('rdo.owner_id = :id')
            ->setParameter('id', $id);

        return $query->executeQuery()->fetchAllAssociative() ?: null;
    }

    /**
     * @return array{id: int, name: string}
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getProfileList(): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->select('recordid id, pfl_name name')
            ->from('profiles')
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * @return array{id: int, name: string}
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getGroupList(): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->select('recordid id, grp_code name')
            ->from('sec_groups')
            ->executeQuery()
            ->fetchAllAssociative();
    }

    /**
     * @return array<array{recordid: string, initials: string, use_jobtitle: string, use_forenames: string}>
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getUserList(): array
    {
        $sql = GetContactListSQLByAccessLevel([
            'module' => Module::DASHBOARDS,
            'levels' => ['DAS_FULL', 'DAS_READ_ONLY'],
        ]);

        return $this->connection->executeQuery($sql)->fetchAllAssociative();
    }

    private function getDashboardOwnerQuery($userId): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->addSelect('rd.recordid')
            ->from('reports_dashboards', 'rd')
            ->innerJoin('rd', 'reports_dashboards_owners', 'rdo', 'rd.recordid = rdo.dash_id')
            ->andWhere('rdo.owner_id = :userId')
            ->setParameter('userId', $userId)
            ->orderBy('rd.recordid');
    }

    /**
     * @param int|int[] $ids
     */
    private function getDashboardsByLinkType($ids, string $type): QueryBuilder
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->addSelect('rd.recordid')
            ->from('reports_dashboards', 'rd')
            ->innerJoin('rd', 'reports_dashboards_links', 'rdl', 'rd.recordid = rdl.dash_id')
            ->andWhere('rdl.dash_link_type = :type')
            ->setParameter('type', $type);

        if (is_array($ids)) {
            $link = $qb->expr()->in('rdl.dash_link_id', ':links');
            $query->setParameter('links', $ids, Connection::PARAM_INT_ARRAY);
        } else {
            $link = 'rdl.dash_link_id = :links';
            $query->setParameter('links', $ids);
        }
        $query->andWhere($link);

        return $query;
    }
}
