<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class WebListingDesignsRepository extends BaseRepository implements WebListingDesignsRepositoryInterface
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::WEB_LISTING_DESIGNS, 'recordid', $connection);
    }

    public function getListOfListingDesigns($module): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('recordid, LST_TITLE')
            ->from(Tables::WEB_LISTING_DESIGNS)
            ->andWhere('lst_module = :module')
            ->setParameter('module', $module);

        return ['Datix Listing Design Template'] + $query->executeQuery()->fetchAllKeyValue();
    }
}
