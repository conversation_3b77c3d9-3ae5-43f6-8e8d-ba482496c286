<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class CodeTagsRepository extends BaseRepository
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::CODE_TAGS, 'recordid', $connection);
    }

    public function getByGroupId($groupId): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('recordid, name')
            ->from(Tables::CODE_TAGS, 'f')
            ->andWhere('group_id = :group_id')
            ->setParameter('group_id', $groupId);

        return $query->executeQuery()->fetchAllKeyValue();
    }
}
