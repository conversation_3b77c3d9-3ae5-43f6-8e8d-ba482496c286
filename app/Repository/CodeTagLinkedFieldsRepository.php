<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\Tables;
use Doctrine\DBAL\Connection;

/**
 * @codeCoverageIgnore
 */
class CodeTagLinkedFieldsRepository extends BaseRepository
{
    public function __construct(Connection $connection)
    {
        parent::__construct(Tables::CODE_TAG_LINKED_FIELDS, '', $connection);
    }

    public function getByTableAndField($field, $table): array
    {
        $qb = $this->connection->createQueryBuilder();

        $query = $qb->select('[group], name')
            ->from(Tables::CODE_TAG_LINKED_FIELDS, 'f')
            ->innerJoin('f', 'code_tag_groups', 'g', 'g.recordid = f.[group]')
            ->andWhere('field = :field')
            ->andWhere('[table] = :table')
            ->setParameter('field', $field)
            ->setParameter('table', $table);

        return $query->executeQuery()->fetchAllKeyValue();
    }
}
