<?php

declare(strict_types=1);

namespace app\Repository;

use app\models\generic\valueObjects\Module;
use Exception;

class RepositoryFactory
{
    private ClaimsRepository $claimsRepository;
    private FeedbackRepository $feedbackRepository;
    private IncidentsRepository $incidentsRepository;
    private MortalityRepository $mortalityRepository;

    public function __construct(
        ClaimsRepository $claimsRepository,
        FeedbackRepository $feedbackRepository,
        IncidentsRepository $incidentsRepository,
        MortalityRepository $mortalityRepository
    ) {
        $this->claimsRepository = $claimsRepository;
        $this->feedbackRepository = $feedbackRepository;
        $this->incidentsRepository = $incidentsRepository;
        $this->mortalityRepository = $mortalityRepository;
    }

    public function getModuleRepository(string $moduleCode): BaseRepository
    {
        switch ($moduleCode) {
            case Module::INCIDENTS:
                return $this->incidentsRepository;
            case Module::MORTALITY_REVIEW:
                return $this->mortalityRepository;
            case Module::FEEDBACK:
                return $this->feedbackRepository;
            case Module::CLAIMS:
                return $this->claimsRepository;
            default:
                throw new Exception('Invalid module provided');
        }
    }
}
