<?php

namespace app\Repository;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Exception;
use Doctrine\DBAL\Exception as DBException;

class IncidentReferenceRepository
{
    private Connection $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    /**
     * @throws DBException
     */
    public function insertRecord(int $recordId): void
    {
        $sql = 'INSERT INTO incidents_reference (incident_id) VALUES (:recordId)';
        $parameters = ['recordId' => $recordId];
        $this->db->executeStatement($sql, $parameters);
    }

    /**
     * @throws Exception
     * @throws DBException
     */
    public function getReference(int $recordId): ?int
    {
        $sql = 'SELECT id FROM incidents_reference WHERE incident_id = :recordId';
        $q = $this->db->prepare($sql);
        $referenceId = $q->executeQuery(['recordId' => $recordId])->fetchOne();

        return ($referenceId === false) ? null : (int) $referenceId;
    }
}
