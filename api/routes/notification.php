<?php

use api\controllers\NotificationController;

$app->get('/notifications/{module}/roles/{language}', NotificationController::class . ':getRoles');
$app->get('/notifications/{module}/status/{language}', NotificationController::class . ':getStatus');
$app->get('/incidents/notifications/overdue', NotificationController::class . ':getOverdueIncidentsRecords');
$app->get('/feedback/notifications/overdue', NotificationController::class . ':getOverdueFeedbackRecords');
$app->get('/mortality/notifications/overdue', NotificationController::class . ':getOverdueMortalityRecords');
$app->get('/notifications/fields/labels/{language}', NotificationController::class . ':getFieldLabelsFromFieldName');
$app->post('/incidents/notifications/relabel-roles', NotificationController::class . ':relabelIncidentRoleDescriptions');
