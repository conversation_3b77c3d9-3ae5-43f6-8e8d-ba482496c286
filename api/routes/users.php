<?php

declare(strict_types=1);

use api\controllers\Users\CreateUsersController;
use api\controllers\Users\LogoutFormUrlController;
use api\controllers\Users\UpdateUsersController;
use api\controllers\UsersController;

$app->post('/users/user', CreateUsersController::class . ':create');
$app->put('/users/user[/{id}]', UpdateUsersController::class . ':update');
$app->get('/users/logout-form-url/{id}', LogoutFormUrlController::class . ':getUrl');
$app->post('/users/has-access', UsersController::class . ':hasAccess');
$app->get('/users/get-user-param-value-by-parameter', UsersController::class . ':getUserParamValueByParameter');
