<?php

declare(strict_types=1);

use api\controllers\Feedback\CreateFeedbackController;
use api\controllers\Feedback\FeedbackByHandlerController;
use api\controllers\Feedback\FeedbackController;
use Slim\App;

/** @global App $app */
$app->get('/feedback/learning-link-information/{id}', FeedbackController::class . ':fetchLearningLink');
$app->post('/feedback/feedback', CreateFeedbackController::class . ':create');

/**
 * Return a list of feedback where the current user is marked as the handler, manager or investigator
 * to be used by the to-do list.
 */
$app->get('/feedback/by-handler', FeedbackByHandlerController::class . ':fetchByHandler');
