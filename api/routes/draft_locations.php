<?php

/** @var App $app */

use api\controllers\DraftLocationsController;
use Slim\App;

$app->post('/draft-locations/draft-location', DraftLocationsController::class . ':add');
$app->get('/draft-locations/counts', DraftLocationsController::class . ':getCounts');
$app->post('/draft-locations/discard-drafts', DraftLocationsController::class . ':discardDrafts');
$app->post('/draft-locations/promote-drafts', DraftLocationsController::class . ':promoteDraftLocations');
