<?php

use api\controllers\Incidents\Mobile\IncidentsMobileV1Controller;
use api\controllers\IncidentsController;
use api\helpers\UsersHelper;
use app\models\framework\config\DatixConfigFactory;
use app\models\generic\RecordSources;
use app\models\generic\valueObjects\JSONData;
use app\models\mobile\adaptors\MobileIncidentAggregateAdaptorFactory;
use app\models\mobile\MobileIncidentRepository;
use app\models\mobile\SawtakIncidentRepositoryFactory;
use app\services\records\RecordSourceServiceFactory;
use app\services\transcription\TranscriberFactory;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\framework\registry\Registry;
use src\generic\services\ExportServiceFactory;
use src\incidents\model\IncidentModelFactory;
use src\logger\Facade\Log;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;

/** @var Slim\App $app */
$app->post('/incidents/mobile/sawtak/v1', function (Request $request, Response $response) {
    try {
        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data['source'])) {
            $data['source'] = (new RecordSourceServiceFactory())->create()
                ->getCodeForSource(RecordSources::MOBILE_APP);
        }

        $adaptor = (new MobileIncidentAggregateAdaptorFactory())->create();

        $aggregate = $adaptor->adaptFromRequestv2($data, $request);
        $repository = (new SawtakIncidentRepositoryFactory())->create();
        $incidentAggregate = $repository->insert($aggregate);
        $incidentId = $aggregate->getIncident()->getId();
        Log::info('Datix Anywhere: incident saved successfully.', [
            'incidentId' => $incidentId[0],
        ]);

        $language = $data['language'];

        $savedAggregate = $repository->get($incidentId);
        $savedAggregateArray = $savedAggregate->toArray();
        $document = $incidentAggregate->getDocument();

        $isTranscriberEnabled = (new DatixConfigFactory())->getInstance()->isTranscriptionEnabled();

        if ($document !== null && !$isTranscriberEnabled) {
            $savedAggregate->getIncident()->setDescription(null);
            $repository->update($savedAggregate);
            $savedAggregateArray['description']['text'] = null;
        } elseif ($document !== null && $isTranscriberEnabled) {
            Log::info('Datix Anywhere: audio file found, preparing to transcribe', [
                'filename' => $incidentAggregate->getDocument()->getFileName(),
            ]);

            $transcriberFactory = new TranscriberFactory();

            try {
                $transcription = $transcriberFactory->create()->transcribe($incidentId[0], $language, $incidentAggregate->getDocument()->getFileName());
            } catch (Throwable $e) {
                Log::error('Error found during audio transcribing', [
                    'exception' => $e,
                ]);

                $transcription = 'An error was encountered when transcribing data. The audio recording has been attached to this record.';
            }
            $caption = $transcription['caption'];

            Log::info('Datix Anywhere: Transcription successful. Description of length ' . strlen($caption) . ' saved.');

            $savedAggregate->getIncident()->setDescription($caption);
            $repository->update($savedAggregate);
            $savedAggregateArray['description']['text'] = $caption;
        }
    } catch (Throwable $e) {
        throw new Exception('Internal server error', 500, $e);
    }

    Log::info('Datix Anywhere: Save complete, returning 201.');

    $response->getBody()->write(json_encode($savedAggregateArray));

    return $response->withStatus(201)
        ->withHeader('Content-Type', 'application/json');
});

$app->post('/incidents/mobile/v2', function (Request $request, Response $response) {
    try {
        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data['source'])) {
            $data['source'] = (new RecordSourceServiceFactory())->create()
                ->getCodeForSource(RecordSources::MOBILE_APP);
        }

        Log::info('Datix Anywhere request received.', [
            'data' => $data,
        ]);

        $adaptor = (new MobileIncidentAggregateAdaptorFactory())->create();

        $aggregate = $adaptor->adaptFromRequestv2($data, $request);
        $repository = Container::get(MobileIncidentRepository::class);

        $incidentAggregate = $repository->insert($aggregate);
        $incidentId = $aggregate->getIncident()->getId();

        Log::info('Datix Anywhere: incident saved successfully.', [
            'incidentId' => $incidentId[0],
        ]);

        $language = $data['language'];

        $savedAggregate = $repository->get($incidentId);
        $savedAggregateArray = $savedAggregate->toArray();
        $document = $incidentAggregate->getDocument();

        $isTranscriberEnabled = (new DatixConfigFactory())->getInstance()->isTranscriptionEnabled();

        if ($document !== null && !$isTranscriberEnabled) {
            $savedAggregate->getIncident()->setDescription(null);
            $repository->update($savedAggregate);
            $savedAggregateArray['description']['text'] = null;
        } elseif ($document !== null && $isTranscriberEnabled) {
            Log::info('Datix Anywhere: audio file found, preparing to transcribe.', [
                'filename' => $incidentAggregate->getDocument()->getFileName(),
            ]);

            $transcriberFactory = new TranscriberFactory();

            try {
                $transcription = $transcriberFactory->create()->transcribe($incidentId[0], $language, $incidentAggregate->getDocument()->getFileName());
            } catch (Throwable $e) {
                Log::error('Error during transcribing', [
                    'exception' => $e,
                ]);

                $transcription = 'An error was encountered when transcribing data. The audio recording has been attached to this record.';
            }
            $caption = $transcription['caption'];

            Log::info('Datix Anywhere: Transcription successful. Description of length ' . strlen($caption) . ' saved.');

            $savedAggregate->getIncident()->setDescription($caption);
            $repository->update($savedAggregate);
            $savedAggregateArray['description']['text'] = $caption;
        }
    } catch (Exception $e) {
        throw new Exception('Internal server error', 500, $e);
    }

    Log::info('Datix Anywhere: Save complete, returning 201.');

    $response->getBody()->write(json_encode($savedAggregateArray));

    return $response->withStatus(201)
        ->withHeader('Content-Type', 'application/json');
});

$app->post('/incidents/mobile', IncidentsMobileV1Controller::class . ':create');

$app->put('/incidents/postpsimssubmission/{id}', IncidentsController::class . ':postPsimsSubmission');

$app->get('/incidents', function (Request $request, Response $response) {
    $daysPrevious = $request->getQueryParams()['days_previous'];
    $login = $request->getQueryParams()['login'];

    if (!$daysPrevious || !is_numeric($daysPrevious) || $daysPrevious <= 0) {
        $response->getBody()->write(json_encode([
            'Error' => 'daysPrevious parameter must be a positive integer.',
        ]));

        return $response;
    }

    if (!$login) {
        $response->getBody()->write(json_encode(['Error' => 'No login context provided.']));

        return $response;
    }

    $exportService = (new ExportServiceFactory())->create('INC');

    $daysPrevious = max(0, (int) $daysPrevious);

    $ids = $exportService->getIdsByDaysPrevious('incidents_main', $daysPrevious);

    $jsonForExport = $exportService->constructJsonForExport($ids, $login);

    $response->getBody()->write($jsonForExport);

    return $response->withStatus(201)
        ->withHeader('Content-Type', 'application/json');
});

$app->get('/incidents/learning-link-information/{id}', function (Request $request, Response $response) {
    $incidentId = (int) $request->getAttribute('id');

    if (empty($incidentId)) {
        throw new Exception('Invalid id', 500);
    }

    $sql = 'SELECT inc_dincident AS eventDate, inc_notes AS eventDescription FROM incidents_main WHERE recordid = :recordid';
    $result = DatixDBQuery::PDO_fetch($sql, ['recordid' => $incidentId]);

    if (empty($result)) {
        $response->getBody()->write(json_encode([]));

        return $response->withStatus(404)
            ->withHeader('Content-Type', 'application/json');
    }

    $response->getBody()->write(json_encode($result));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json');
});

/**
 * Return a list of incidents where the current user is marked as the handler to be used by the to-do list.
 */
$app->get('/incidents/by-handler', function (Request $request, Response $response) {
    $results = [];

    $token = $request->getAttribute('token');
    $user = UsersHelper::getUserFromJWT($token);

    if ($user === null) {
        return UsersHelper::getEmptyJWTResponse($response);
    }

    $incidentMapper = (new IncidentModelFactory())->getMapper();
    $incidentData = $incidentMapper->findOpenByHandlerManagerAndInvestigator($user->initials);

    $languageSession = LanguageSessionFactory::getInstance(true);

    // Translate statuses into human readable form to be used on To-Do list
    $statusMap = [
        'AWAREV' => (string) $languageSession->getStaticString('todo_status_INC_AWAREV'),
        'INREV' => (string) $languageSession->getStaticString('todo_status_INC_INREV'),
        'AWAFA' => (string) $languageSession->getStaticString('todo_status_INC_AWAFA'),
        'INFA' => (string) $languageSession->getStaticString('todo_status_INC_INFA'),
        'FA' => (string) $languageSession->getStaticString('todo_status_INC_FA'),
    ];

    // Need to calculate a due date for each incident: will be the date that the incident should be complete
    // by according to the system overdue date settings.
    $registry = Container::get(Registry::class);
    $overdueDays = (int) $registry->getParm('DIF_OVERDUE_DAYS', 0, true)->toScalar();

    foreach ($incidentData as $key => $incident) {
        $results[$key]['id'] = $incident['recordid'];
        $results[$key]['title'] = $incident['inc_name'];

        if ($incident['recordid']) {
            $results[$key]['url'] = getenv('BASE_URL') . 'index.php?action=incident&recordid=' . $incident['recordid'];
        }

        if ($incident['rep_approved']) {
            $results[$key]['details'] = $statusMap[$incident['rep_approved']];
        }

        unset($results[$key]['rep_approved']);

        if ($overdueDays > 0 && $incident['inc_dreported']) {
            $results[$key]['due_date'] = (new DateTime($incident['inc_dreported']))->add(new DateInterval('P' . $overdueDays . 'D'))->format('Y-m-d');
        }

        unset($results[$key]['inc_dreported']);

        $roles = [];

        if ($incident['inc_mgr'] == $user->initials) {
            $roles[] = $languageSession->getFieldString('INCIDENTS', 'inc_mgr');
        }

        if ($incident['inc_head'] == $user->initials) {
            $roles[] = $languageSession->getFieldString('INCIDENTS', 'inc_head');
        }

        if ($incident['inc_investigator'] == $user->initials) {
            $roles[] = $languageSession->getFieldString('INCIDENTS', 'inc_investigator');
        }

        $results[$key]['role'] = implode(', ', $roles);
    }

    $response->getBody()->write(json_encode($results));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json')
        ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
});

$app->post('/incidents/incident', IncidentsController::class . ':create');
$app->post('/incidents/incident/{incidentId}', IncidentsController::class . ':update');
$app->get('/incidents/search', IncidentsController::class . ':getIncidentRecords');

/**
 * Deprecating these end points as they do not match api structure.
 * Deprecation message added 22/09/2020
 * Remove these after a sufficcient amount of time.
 *
 * @deprecated
 */
$app->post('/incidents', IncidentsController::class . ':create');
/** @deprecated */
$app->post('/incidents/{incidentId}', IncidentsController::class . ':update');
