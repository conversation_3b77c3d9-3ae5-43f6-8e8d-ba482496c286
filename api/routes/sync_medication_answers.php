<?php

use api\exceptions\RequiredFieldMissing;
use app\framework\DoctrineEntityManagerFactory;
use app\models\generic\valueObjects\JSONData;
use app\models\medication\entities\IncidentMedicationLink;
use app\models\medication\hydrators\IncidentMedicationLinkHydrator;
use app\models\medication\repositories\IncidentMedicationLinkRepository;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;
use src\system\container\facade\Container;

/**
 * Add and also Update/Rewrite. Deletes all the present links and replaces with new ones.
 */
$app->post('/sync/medication/answers', function (Request $request, Response $response) {
    $incMedicationHydrator = Container::get(IncidentMedicationLinkHydrator::class);
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $data = (new JSONData($request->getBody()))->toArray();

    // validation
    $requiredFields = ['answer_id', 'incident_id'];
    foreach ((array) $data as $index => $record) {
        foreach ($requiredFields as $field) {
            if (empty($record[$field])) {
                throw new RequiredFieldMissing("{$index} . {$field}");
            }
        }
    }

    // cast whatever link are to array. Any non-array data would cast to empty array, so it is safe to iterate over
    $requestedLinks = $incMedicationHydrator->hydrateList($data);
    /** @var IncidentMedicationLinkRepository $repository */
    $repository = $entityManager->getRepository(IncidentMedicationLink::class);

    // add new ones
    foreach ($requestedLinks as $key => $link) {
        // check if it already exists
        /** @var IncidentMedicationLink $existingAnswer */
        $existingAnswer = $repository->findOneBy([
            'answer_id' => $link->getAnswerId(),
        ]);

        if ($existingAnswer) {
            $link = $incMedicationHydrator->hydrateSingleLink($data[$key], $existingAnswer);
        }

        $entityManager->persist($link);
    }

    $entityManager->flush();

    $response->getBody()->write(json_encode($incMedicationHydrator->extractList($requestedLinks)));

    return $response->withStatus(201)
        ->withHeader('Content-Type', 'application/json');
});

/**
 * Delete. Deletes all medications by the aggregation ID.
 */
$app->delete('/sync/medication/answers/{id}', function (Request $request, Response $response) {
    $id = $request->getAttribute('id');
    if (!$id) {
        throw new RequiredFieldMissing('id');
    }
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();

    /** @var IncidentMedicationLinkRepository $repository */
    $repository = $entityManager->getRepository(IncidentMedicationLink::class);
    $repository->deleteAnswerById($id);

    // we do not care how many records are deleted, upsert-like
    return $response->withStatus(204)
        ->withHeader('Content-Type', 'application/json');
});
