<?php

use api\exceptions\RequiredFieldMissing;
use app\framework\DoctrineEntityManagerFactory;
use app\models\equipment\entities\EquipmentEntity;
use app\models\equipment\hydrators\EquipmentHydrator;
use app\models\generic\valueObjects\JSONData;
use Doctrine\ORM\EntityManager;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Handle update for global equipment.
 */
$app->put('/sync/equipment/global/{reference}/{locale}', function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    $locale = $request->getAttribute('locale');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }

    if (!$locale) {
        throw new RequiredFieldMissing('locale');
    }

    $data = (new JSONData($request->getBody()))->toArray();
    $equipmentHydrator = new EquipmentHydrator();
    $requestEquipmentEntity = $equipmentHydrator->hydrate(
        array_merge(
            $data,
            [
                'reference' => $reference,
                'locale' => $locale,
            ],
        ),
    );

    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(EquipmentEntity::class);

    // check if equipment already exists
    /** @var EquipmentEntity $existingEquipment */
    $existingEquipment = $repository->findOneBy([
        'equipment_reference' => $requestEquipmentEntity->getEquipmentReference(),
        'locale' => $requestEquipmentEntity->getLocale(),
    ]);

    if (!$existingEquipment) {
        // we do not care if regional update tries to update something
        // the tenant does not have
        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }

    $entityManager->beginTransaction();
    // we do not want to update existing entry as it may have some fields with data, and we want them to be reset
    $entityManager->remove($existingEquipment);
    $entityManager->flush();
    $entityManager->persist($requestEquipmentEntity);
    $entityManager->flush();

    $entityManager->commit();

    $response->getBody()->write(json_encode($equipmentHydrator->extract($requestEquipmentEntity)));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json');
});

/**
 * Handle upsert for local equipment.
 */
$app->put('/sync/equipment/local/{reference}/{locale}', function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    $locale = $request->getAttribute('locale');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }

    if (!$locale) {
        throw new RequiredFieldMissing('locale');
    }

    $data = (new JSONData($request->getBody()))->toArray();
    $equipmentHydrator = new EquipmentHydrator();
    $requestEquipmentEntity = $equipmentHydrator->hydrate(
        array_merge(
            $data,
            [
                'reference' => $reference,
                'locale' => $locale,
            ],
        ),
    );

    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(EquipmentEntity::class);

    // check if equipment already exists
    /** @var EquipmentEntity $existingEquipment */
    $existingEquipment = $repository->findOneBy([
        'equipment_reference' => $requestEquipmentEntity->getEquipmentReference(),
        'locale' => $requestEquipmentEntity->getLocale(),
    ]);

    if (!$existingEquipment) {
        // create a new one
        $entityManager->persist($requestEquipmentEntity);
        $entityManager->flush();

        $response->getBody()->write(json_encode($equipmentHydrator->extract($requestEquipmentEntity)));

        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }

    // update equipment
    $entityManager->beginTransaction();
    // we do not want to update existing entry as it may have some fields with data, and we want them to be reset
    $entityManager->remove($existingEquipment);
    $entityManager->flush();
    $entityManager->persist($requestEquipmentEntity);
    $entityManager->flush();

    $entityManager->commit();

    $response->getBody()->write(json_encode($equipmentHydrator->extract($requestEquipmentEntity)));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json');
});

$syncEquipmentDeleteHandler = function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }

    /** @var EntityManager $entityManager */
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(EquipmentEntity::class);

    $existingPiecesOfEquipment = $repository->findBy([
        'equipment_reference' => $reference,
    ]);

    if (empty($existingPiecesOfEquipment)) {
        // Upsert-like behaviour - tolerate deletion of non-existent records
        return $response->withStatus(204)
            ->withHeader('Content-Type', 'application/json');
    }

    array_map(function (EquipmentEntity $equip) use ($entityManager) {
        $equip->setIsDeleted(true);
        $entityManager->persist($equip);
    }, $existingPiecesOfEquipment);

    $entityManager->flush();

    return $response->withStatus(204)
        ->withHeader('Content-Type', 'application/json');
};

/**
 * Handle global delete.
 */
$app->delete('/sync/equipment/global/{reference}', $syncEquipmentDeleteHandler);

/**
 * Handle local delete.
 */
$app->delete('/sync/equipment/local/{reference}', $syncEquipmentDeleteHandler);
