<?php

declare(strict_types=1);

use api\controllers\Payments\CreatePaymentController;
use api\controllers\Payments\GetPaymentController;
use api\controllers\Payments\ListPaymentsController;
use api\controllers\Payments\UpdatePaymentController;

$app->get('/payments', ListPaymentsController::class . ':getPayments');

$app->get('/payments/payment/{id}', GetPaymentController::class . ':getPayment');

$app->post('/payments/payment', CreatePaymentController::class . ':createPayment');

$app->patch('/payments/payment/{id}', UpdatePaymentController::class . ':updatePayment');
