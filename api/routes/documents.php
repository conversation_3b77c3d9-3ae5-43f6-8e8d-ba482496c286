<?php

declare(strict_types=1);

use api\controllers\Documents\DocumentTemplatesController;
use api\controllers\Documents\ImportDocumentsController;
use Slim\App;

/** @var App $app */
$app->get('/documents/doctemplates', DocumentTemplatesController::class . ':getTemplates');

$app->get('/documents/export/doctemplate/{id}', DocumentTemplatesController::class . ':getExportedTemplates');

$app->post('/documents/import', ImportDocumentsController::class . ':createDocumentTemplate');
$app->put('/documents/import/{id}', ImportDocumentsController::class . ':updateDocumentTemplate');
$app->delete('/documents/import/{id}', ImportDocumentsController::class . ':deleteDocumentTemplate');
