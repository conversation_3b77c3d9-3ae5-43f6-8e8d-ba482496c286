<?php

use api\exceptions\RequiredFieldMissing;
use app\framework\DoctrineEntityManagerFactory;
use app\models\equipment\entities\IncidentEquipmentLinkEntity;
use app\models\equipment\hydrators\IncidentEquipmentLinkHydrator;
use app\models\equipment\repositories\IncidentSyncedEquipmentLinkRepository;
use app\models\generic\valueObjects\JSONData;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;
use src\system\container\facade\Container;

/**
 * Persist answers for synced equipment.
 */
$app->put('/sync/equipment/answers', function (Request $request, Response $response) {
    $incEquipmentHydrator = Container::get(IncidentEquipmentLinkHydrator::class);
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $data = (new JSONData($request->getBody()))->toArray();

    // validation
    $requiredFields = ['answer_id', 'incident_id', 'equipment_reference'];
    foreach ($data as $index => $record) {
        foreach ($requiredFields as $field) {
            if (empty($record[$field])) {
                throw new RequiredFieldMissing("{$index} . {$field}");
            }
        }
    }

    $requestedLinks = $incEquipmentHydrator->hydrateList($data);
    /** @var IncidentSyncedEquipmentLinkRepository $repository */
    $repository = $entityManager->getRepository(IncidentEquipmentLinkEntity::class);
    $logger = $this->get('logger');


    // add new ones
    foreach ($requestedLinks as $key => $link) {
        // check if it already exists
        $existingAnswer = $repository->findOneBy([
            'answer_id' => $link->getAnswerId(),
        ]);

        if ($existingAnswer) {
            $link = $incEquipmentHydrator->hydrateSingleLink($data[$key], $existingAnswer);
        }

        $entityManager->persist($link);
    }

    $entityManager->flush();

    $response->getBody()->write(json_encode($incEquipmentHydrator->extractList($requestedLinks)));

    return $response->withStatus(201)
        ->withHeader('Content-Type', 'application/json');
});

/**
 * Delete. Deletes all equipment answers by the aggregation ID.
 */
$app->delete('/sync/equipment/answers/{id}', function (Request $request, Response $response) {
    $id = $request->getAttribute('id');
    if (!$id) {
        throw new RequiredFieldMissing('id');
    }
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();

    /** @var IncidentSyncedEquipmentLinkRepository $repository */
    $repository = $entityManager->getRepository(IncidentEquipmentLinkEntity::class);
    $repository->deleteAnswerById($id);

    // upsert-like behaviour, tolerate deletion of non-existent records
    return $response->withStatus(204)
        ->withHeader('Content-Type', 'application/json');
});
