<?php

use api\controllers\ServicesController;
use app\framework\DoctrineEntityManagerFactory;
use app\models\generic\valueObjects\JSONData;
use app\models\service\entities\ServiceEntity;
use app\models\service\entities\ServiceTagEntity;
use app\models\service\hydrators\ServiceHydrator;
use app\models\service\services\ServiceServiceFactory;
use app\models\service\services\ServiceTagServiceFactory;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

$app->post('/services/service', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();

    if (!$data['node'] || !$data['tree']) {
        throw new Exception('Node or Tree data missing.');
    }

    $serviceHydrator = new ServiceHydrator();
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();

    $meta = $entityManager->getClassMetadata(ServiceEntity::class);
    $connection = $entityManager->getConnection();
    $connection->beginTransaction();

    $editedRecord = $data['node'];

    try {
        $expressionBuilder = $connection->getExpressionBuilder();
        $princeFields = $connection->createQueryBuilder()
            ->select('id', ...$serviceHydrator->getPrinceOnlyFields())
            ->from($meta->getTableName())
            ->where(
                $expressionBuilder->or(
                    ...array_map(static function (string $field) use ($expressionBuilder): string {
                        return $expressionBuilder->isNotNull($field);
                    }, $serviceHydrator->getPrinceOnlyFields()),
                ),
            )
            ->executeQuery()
            ->fetchAllAssociativeIndexed();

        $connection->executeStatement('DELETE FROM ' . $meta->getTableName());
        $connection->executeStatement('DELETE FROM service_tag_links WHERE service_id = ' . (int) $data['node']['id']);
        $connection->executeStatement('DELETE FROM code_service');
        $connection->executeStatement('DELETE FROM service_descr');
        $connection->executeStatement('DELETE FROM services_locations WHERE serviceentity_id = ' . (int) $data['node']['id']);

        foreach ($data['tree'] as $service) {
            if ($service['id'] == $editedRecord['id']) {
                $service = $editedRecord; // contains tags info
            }


            if (array_key_exists($service['id'], $princeFields)) {
                $service = array_merge($service, $princeFields[$service['id']]);
            }

            $serviceEntity = $serviceHydrator->hydrate($service);
            $entityManager->persist($serviceEntity);

            if (isset($service['id'])) {
                $codeData = array_merge(['id' => $service['id']], $princeFields[$service['id']] ?? []);
                if ($codeData === null) {
                    $msg = error_get_last();

                    throw new \Exception($msg['message']);
                }


                $connection->insert('code_service', $codeData);
            }
        }
        $entityManager->flush();
        $connection->commit();
    } catch (\Exception $e) {
        $connection->rollback();

        throw $e;
    }

    $repository = $entityManager->getRepository(ServiceEntity::class);

    /** @var ServiceEntity $savedService */
    $savedService = $repository->findBy(['id' => $data['node']['id']])[0];

    $response->getBody()->write(json_encode($serviceHydrator->extract($savedService)));

    return $response
        ->withStatus(Http::CREATED)
        ->withHeader('Content-Type', 'application/json');
});

$app->delete('/services/service/{id}', function (Request $request, Response $response, array $args) {
    $serviceId = $args['id'];
    $serviceService = (new ServiceServiceFactory())->create();
    $serviceService->delete($serviceId);

    return $response->withStatus(Http::NO_CONTENT);
});

$app->put('/services/service/import', ServicesController::class . ':importFromCmt');

$app->put('/services/service[/{id}]', ServicesController::class . ':importFromPostOrPut');

// Tag management ============================================

$app->get('/services/tag', function (Request $request, Response $response) {
    $service = (new ServiceTagServiceFactory())->create();

    $list = array_map(
        function (ServiceTagEntity $tag) {
            return $tag->toArray();
        },
        $service->getAll(),
    );

    $response->getBody()->write(json_encode($list));

    return $response->withStatus(Http::OK)
        ->withHeader('Content-Type', 'application/json');
});

$app->post('/services/tag', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();
    $serviceTagService = (new ServiceTagServiceFactory())->create();

    $serviceTagService->createNew($data);
    $createdService = $serviceTagService->get($data['id'])->toArray();

    $response->getBody()->write(json_encode($createdService));

    return $response->withStatus(Http::CREATED)
        ->withHeader('Content-Type', 'application/json');
});

$app->put('/services/tag/{id}', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();
    $serviceTagService = (new ServiceTagServiceFactory())->create();

    $serviceTagService->update($data);
    $createdService = $serviceTagService->get($data['id'])->toArray();

    $response->getBody()->write(json_encode($createdService));

    return $response->withStatus(Http::OK)
        ->withHeader('Content-Type', 'application/json');
});

$app->delete('/services/tag/{id}', function (Request $request, Response $response) {
    $id = (int) $request->getAttribute('id');

    $serviceTagService = (new ServiceTagServiceFactory())->create();
    $serviceTagService->delete($id);

    return $response->withStatus(Http::NO_CONTENT);
});
