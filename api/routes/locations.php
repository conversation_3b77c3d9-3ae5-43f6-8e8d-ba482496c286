<?php

use api\controllers\LocationsController;
use app\framework\DoctrineEntityManagerFactory;
use app\models\generic\valueObjects\JSONData;
use app\models\location\entities\LocationEntity;
use app\models\location\entities\LocationTagEntity;
use app\models\location\hydrators\LocationHydrator;
use app\models\location\services\LocationServiceFactory;
use app\models\location\services\LocationTagServiceFactory;
use app\models\service\entities\ServiceEntity;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

$app->post('/locations/location', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();

    if (!$data['node'] || !$data['tree']) {
        throw new Exception('Node or Tree data missing.');
    }

    $locationHydrator = new LocationHydrator();
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();

    $meta = $entityManager->getClassMetadata(LocationEntity::class);
    $connection = $entityManager->getConnection();
    $connection->beginTransaction();

    $editedRecord = $data['node'];

    try {
        $expressionBuilder = $connection->getExpressionBuilder();
        $princeFields = $connection->createQueryBuilder()
            ->select('id', ...$locationHydrator->getPrinceOnlyFields())
            ->from($meta->getTableName())
            ->where(
                $expressionBuilder->or(
                    ...array_map(static function (string $field) use ($expressionBuilder): string {
                        return $expressionBuilder->isNotNull($field);
                    }, $locationHydrator->getPrinceOnlyFields()),
                ),
            )
            ->executeQuery()
            ->fetchAllAssociativeIndexed();

        $connection->executeStatement('DELETE FROM ' . $meta->getTableName());
        $connection->executeStatement('DELETE FROM location_tag_links WHERE location_id = ' . (int) $data['node']['id']);

        // Remove any existing etl entries in these tables that would be duplicated/redundant since we're wiping and reinserting
        $connection->executeStatement(<<<'SQL'
                DELETE FROM etl
                WHERE
                    TableName = 'code_location'
                    AND (
                        Operation <> 'delete'
                        OR
                        JSON_VALUE(JsonData, '$[0].id') IN (
                            SELECT id FROM code_location
                        )
                    )
            SQL);
        $connection->executeStatement('DELETE FROM code_location');
        $connection->executeStatement(<<<'SQL'
                DELETE etl
                FROM etl
                LEFT JOIN location_descr
                ON
                    location_descr.id = JSON_VALUE(JsonData, '$[0].id')
                    AND
                    location_descr.[language] = JSON_VALUE(JsonData, '$[0].language')
                WHERE
                    TableName = 'location_descr'
                    AND (
                        Operation <> 'delete'
                        OR
                        location_descr.id IS NOT NULL
                    );
            SQL);
        $connection->executeStatement('DELETE FROM location_descr');

        $connection->executeStatement('DELETE FROM locations_id_numbers WHERE location_id = ' . (int) $data['node']['id']);
        $connection->executeStatement('DELETE FROM services_locations WHERE locationentity_id = ' . (int) $data['node']['id']);

        foreach ($data['tree'] as $location) {
            if ($location['id'] == $editedRecord['id']) {
                $location = $editedRecord; // contains tags info
            }

            if (array_key_exists($location['id'], $princeFields)) {
                $location = array_merge($location, $princeFields[$location['id']]);
            }

            $locationEntity = $locationHydrator->hydrate($location);
            $entityManager->persist($locationEntity);

            if (isset($location['id'])) {
                $codeData = array_merge(['id' => $location['id']], $princeFields[$location['id']] ?? []);
                unset($codeData['cod_ncds_location']);
                $connection->insert('code_location', $codeData);
            }
        }
        $entityManager->flush();
        $connection->commit();
    } catch (\Exception $e) {
        $connection->rollback();

        throw $e;
    }

    $repository = $entityManager->getRepository(LocationEntity::class);
    /** @var ServiceEntity $savedLocation */
    $savedLocation = $repository->findBy(['id' => $data['node']['id']])[0];

    $response->getBody()->write(json_encode($locationHydrator->extract($savedLocation)));

    return $response->withStatus(Http::CREATED)
        ->withHeader('Content-Type', 'application/json');
});

$app->delete('/locations/location/{id}', function (Request $request, Response $response, array $args) {
    $locationId = $args['id'];
    $locationService = (new LocationServiceFactory())->create();
    $locationService->delete($locationId);

    return $response->withStatus(Http::NO_CONTENT);
});

$app->put('/locations/location/import', LocationsController::class . ':importFromCmt');

$app->put('/locations/location[/{id}]', LocationsController::class . ':importFromPostOrPut');


// Tag management ============================================

$app->get('/locations/tag', function (Request $request, Response $response) {
    $service = (new LocationTagServiceFactory())->create();

    $list = array_map(
        function (LocationTagEntity $tag) {
            return $tag->toArray();
        },
        $service->getAll(),
    );

    $response->getBody()->write(json_encode($list));

    return $response->withStatus(Http::OK)
        ->withHeader('Content-Type', 'application/json');
});

$app->post('/locations/tag', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();
    $locationTagService = (new LocationTagServiceFactory())->create();

    $locationTagService->createNew($data);
    $createdLocation = $locationTagService->get($data['id'])->toArray();

    $response->getBody()->write(json_encode($createdLocation));

    return $response->withStatus(Http::CREATED)
        ->withHeader('Content-Type', 'application/json');
});

$app->put('/locations/tag/{id}', function (Request $request, Response $response) {
    $data = (new JSONData($request->getBody()))->toArray();
    $locationTagService = (new LocationTagServiceFactory())->create();

    $locationTagService->update($data);
    $createdLocation = $locationTagService->get($data['id'])->toArray();

    $response->getBody()->write(json_encode($createdLocation));

    return $response->withStatus(Http::OK)
        ->withHeader('Content-Type', 'application/json');
});

$app->delete('/locations/tag/{id}', function (Request $request, Response $response) {
    $id = (int) $request->getAttribute('id');

    $locationTagService = (new LocationTagServiceFactory())->create();
    $locationTagService->delete($id);

    return $response->withStatus(Http::NO_CONTENT);
});
