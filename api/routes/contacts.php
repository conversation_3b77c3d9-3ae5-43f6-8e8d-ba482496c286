<?php

declare(strict_types=1);

use api\controllers\Contacts\ContactsController;
use api\controllers\Contacts\ContactsMergeController;
use api\ServiceProviders\MiddlewareServiceProvider;
use Slim\App;
use Slim\Routing\RouteCollectorProxy;

/** @global App $app */
$app->group('/contacts', function (RouteCollectorProxy $router) {
    $router->post('/merge', ContactsMergeController::class . ':merge');
    $router->post('/match', ContactsController::class . ':match');
    $router->post('/batch', ContactsController::class . ':batch')
        ->add(MiddlewareServiceProvider::VALIDATOR_CONTACTS_BATCH);
    $router->map(['GET', 'POST'], '/links/{contactId}', ContactsController::class . ':links');

    $router->group('/contact', function (RouteCollectorProxy $router) {
        $router->post('', ContactsController::class . ':replaceOrCreate');
        $router->delete('/{contactId}', ContactsController::class . ':delete');
        $router->map(['patch', 'put'], '/{id}', ContactsController::class . ':update');
        $router->get('/{contactId}/user', ContactsController::class . ':getUserByContactId');
    });

    $router->post('/verify-timestamps', ContactsController::class . ':verifyTimestamps')
        ->add('validator.carlton_contacts_sync');
});
