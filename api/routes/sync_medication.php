<?php

use api\exceptions\RequiredFieldMissing;
use app\framework\DoctrineEntityManagerFactory;
use app\models\generic\valueObjects\JSONData;
use app\models\medication\entities\MedicationEntity;
use app\models\medication\hydrators\MedicationHydrator;
use Doctrine\ORM\EntityManager;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Message\ResponseInterface as Response;

/**
 * Handle global update.
 */
$app->put('/sync/medication/global/{reference}/{locale}', function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    $locale = $request->getAttribute('locale');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }

    if (!$locale) {
        throw new RequiredFieldMissing('locale');
    }

    $data = (new JSONData($request->getBody()))->toArray();
    $medicationHydrator = new MedicationHydrator();
    $requestMedicationEntity = $medicationHydrator->hydrate(
        array_merge(
            $data,
            [
                'reference' => $reference,
                'locale' => $locale,
            ],
        ),
    );

    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(MedicationEntity::class);

    // check if medication already exists
    /** @var MedicationEntity $existingMedication */
    $existingMedication = $repository->findOneBy([
        'med_reference' => $requestMedicationEntity->getMedReference(),
        'med_locale' => $requestMedicationEntity->getMedLocale(),
    ]);

    if (!$existingMedication) {
        // we do not care if regional update tries to update something
        // the tenant does not have
        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }

    // update medication
    $entityManager->beginTransaction();
    // we do not want to update existing entry as it may have some fields with data, and we want them to be reset
    $entityManager->remove($existingMedication);
    $entityManager->flush();
    $entityManager->persist($requestMedicationEntity);
    $entityManager->flush();

    $entityManager->commit();

    $response->getBody()->write(json_encode($medicationHydrator->extract($requestMedicationEntity)));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json');
});

/**
 * Handle local upsert.
 */
$app->put('/sync/medication/local/{reference}/{locale}', function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    $locale = $request->getAttribute('locale');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }

    if (!$locale) {
        throw new RequiredFieldMissing('locale');
    }

    $data = (new JSONData($request->getBody()))->toArray();
    $medicationHydrator = new MedicationHydrator();
    $requestMedicationEntity = $medicationHydrator->hydrate(
        array_merge(
            $data,
            [
                'reference' => $reference,
                'locale' => $locale,
            ],
        ),
    );

    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(MedicationEntity::class);

    // check if medication already exists
    /** @var MedicationEntity $existingMedication */
    $existingMedication = $repository->findOneBy([
        'med_reference' => $requestMedicationEntity->getMedReference(),
        'med_locale' => $requestMedicationEntity->getMedLocale(),
    ]);

    if (!$existingMedication) {
        $entityManager->persist($requestMedicationEntity);
        $entityManager->flush();

        $response->getBody()->write(json_encode($medicationHydrator->extract($requestMedicationEntity)));

        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }

    // update medication
    $entityManager->beginTransaction();
    // we do not want to update existing entry as it may have some fields with data, and we want them to be reset
    $entityManager->remove($existingMedication);
    $entityManager->flush();
    $entityManager->persist($requestMedicationEntity);
    $entityManager->flush();

    $entityManager->commit();

    $response->getBody()->write(json_encode($medicationHydrator->extract($requestMedicationEntity)));

    return $response->withStatus(200)
        ->withHeader('Content-Type', 'application/json');
});

$syncMedicationDeleteHandler = function (Request $request, Response $response) {
    $reference = $request->getAttribute('reference');
    if (!$reference) {
        throw new RequiredFieldMissing('reference');
    }


    /** @var EntityManager $entityManager */
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(MedicationEntity::class);

    $existingMedications = $repository->findBy([
        'med_reference' => $reference,
    ]);

    if (empty($existingMedications)) {
        // Upsert like behaviour where we tolerate deletion of non-existant record
        return $response->withStatus(204)
            ->withHeader('Content-Type', 'application/json');
    }

    array_map(function (MedicationEntity $med) use ($entityManager) {
        $med->setIsDeleted(true);
        $entityManager->persist($med);
    }, $existingMedications);

    $entityManager->flush();

    return $response->withStatus(204)
        ->withHeader('Content-Type', 'application/json');
};

/**
 * Handle global delete.
 */
$app->delete('/sync/medication/global/{reference}', $syncMedicationDeleteHandler);


/**
 * Handle local delete.
 */
$app->delete('/sync/medication/local/{reference}', $syncMedicationDeleteHandler);
