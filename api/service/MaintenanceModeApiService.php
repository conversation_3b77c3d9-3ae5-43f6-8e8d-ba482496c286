<?php

declare(strict_types=1);

namespace api\service;

use Psr\Http\Message\RequestInterface as Request;
use src\admin\services\JwtService;
use src\admin\services\MaintenanceModeService;

class MaintenanceModeApiService
{
    /** A whitelist of regex matches to allow certain request URIs through. */
    private const URI_WHITELIST = [
        'healthcheck',
        'healthz',
        'logout',
        'token',
        'users/get-user-param-value-by-parameter',
        'users/user',
        'users/user/[\d]+',
        'users/logout-form-url/[\d]+',
        'languages/setAvailable',
        'contacts/links/[\d]+',
        'contacts/contact',
        'contacts/contact/[\d]+/user',
        'contacts/contact/[\d]+',
        'saved-queries/saved-queries/(INC|COM|CLA|MOR|RED|SFG)',
        'saved-queries/details',
        'languages/setUserLanguage',
        'system_configuration/setSystemConfigurationSync',
    ];
    private JwtService $jwtService;
    private MaintenanceModeService $maintenanceModeService;

    public function __construct(
        JwtService $jwtService,
        MaintenanceModeService $maintenanceModeService
    ) {
        $this->jwtService = $jwtService;
        $this->maintenanceModeService = $maintenanceModeService;
    }

    public function shouldBlock(Request $request): bool
    {
        if ($this->matchesWhitelist($request)) {
            return false;
        }

        if (!$this->maintenanceModeService->isMaintenanceModeEnabled()) {
            return false;
        }

        if ($this->maintenanceModeService->hasMaintenanceModePermission()) {
            return false;
        }

        $token = $this->getTokenFromHeader($request);

        return !$this->jwtService->isAdmin($token);
    }

    private function matchesWhitelist(Request $request): bool
    {
        $path = $request->getUri()->getPath();

        foreach (self::URI_WHITELIST as $pattern) {
            if (preg_match("#^/?(api/)?{$pattern}$#", $path) === 1) {
                return true;
            }
        }

        return false;
    }

    private function getTokenFromHeader(Request $request): string
    {
        $header = $request->getHeader('Authorization')[0];

        return str_replace('Bearer ', '', $header);
    }
}
