<?php

declare(strict_types=1);

namespace api\service;

use app\models\email\events\EmailNotificationSubscriber;
use app\models\feedback\events\FeedbackEntityListener;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use src\logger\Facade\Log;

final class EntityEventService
{
    private const ENTITY_LISTENERS = [
        FeedbackEntityListener::class,
    ];
    private const EVENT_SUBSCRIBERS = [
        EmailNotificationSubscriber::class,
    ];
    private ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function registerEntityEvents(): void
    {
        try {
            /** @var EntityManagerInterface $entityManager */
            $entityManager = $this->container->get(EntityManagerInterface::class);

            $entityListenerResolver = $entityManager->getConfiguration()->getEntityListenerResolver();
            foreach (self::ENTITY_LISTENERS as $listener) {
                $entityListenerResolver->register(
                    $this->container->get($listener),
                );
            }

            $eventManager = $entityManager->getEventManager();
            foreach (self::EVENT_SUBSCRIBERS as $subscriber) {
                $eventManager->addEventSubscriber(
                    $this->container->get($subscriber),
                );
            }
        } catch (ContainerExceptionInterface $e) {
            Log::error('Error registering entity event', [
                'exception' => $e,
            ]);
        }
    }
}
