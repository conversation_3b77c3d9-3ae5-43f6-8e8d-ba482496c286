<?php

declare(strict_types=1);

namespace api\service\Contacts;

class ContactsBatchResult
{
    private int $new = 0;
    private int $updated = 0;

    public function setNew(int $new): void
    {
        $this->new = $new;
    }

    public function setUpdated(int $updated): void
    {
        $this->updated = $updated;
    }

    public function getNew(): int
    {
        return $this->new;
    }

    public function getUpdated(): int
    {
        return $this->updated;
    }
}
