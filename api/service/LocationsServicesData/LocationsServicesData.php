<?php

declare(strict_types=1);

namespace api\service\LocationsServicesData;

class LocationsServicesData
{
    private array $node;
    private array $tree;
    private bool $isPublishing;

    public function __construct(array $node, array $tree, bool $isPublishing)
    {
        $this->node = $node;
        $this->tree = $tree;
        $this->isPublishing = $isPublishing;
    }

    public function isPublishing(): bool
    {
        return $this->isPublishing;
    }

    public function getNode(): array
    {
        return $this->node;
    }

    public function getTree(): array
    {
        return $this->tree;
    }
}
