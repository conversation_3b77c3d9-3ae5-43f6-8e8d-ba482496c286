<?php

declare(strict_types=1);

namespace api\service\LocationsServicesData;

use app\models\generic\valueObjects\JSONData;
use Exception;
use Psr\Http\Message\ServerRequestInterface as Request;

/**
 * Factory used to create a LocationsServicesData Object.
 */
class LocationsServicesDataFactory
{
    /**
     * Create a LocationsServicesData object from the given Request.
     *
     * @param Request $request - HTTP request JSON
     */
    public function createFromRequest(Request $request): LocationsServicesData
    {
        $formattedData = $this->extractBodyFromRequest($request);

        return $this->createFromJson($formattedData);
    }

    /**
     * Create a LocationsServicesData object from a given array.
     *
     * @param array $formattedData array created from JSON
     */
    private function createFromJson(array $formattedData): LocationsServicesData
    {
        if (!$this->isValid($formattedData)) {
            throw new Exception('Node or Tree data missing.');
        }

        $node = $formattedData['node'];
        $tree = $formattedData['tree'];
        $isPublishing = $this->checkIsPublishing($formattedData);

        return new LocationsServicesData($node, $tree, $isPublishing);
    }

    /**
     * Ensure a given array has the relevent parts required to create a LocationsServicesData
     * object.
     *
     * @param array $formattedData array created from JSON
     *
     * @return bool True when valid, false when invalid
     */
    private function isValid(array $formattedData): bool
    {
        if (!isset($formattedData['node']) || !isset($formattedData['tree'])) {
            return false;
        }

        return true;
    }

    /**
     * Format a given Request object into an array.
     *
     * @param Request $request - HTTP request JSON
     */
    private function extractBodyFromRequest(Request $request): array
    {
        return (new JSONData($request->getBody()))->toArray();
    }

    /**
     * When publishing a draft Location/Service a flag must be set in order to
     * process the Request data correctly.
     *
     * @param array $formattedData array created from JSON
     *
     * @return bool true when we are publishing, false when not publishing (From Import/Export or non-draft Locations/services)
     */
    private function checkIsPublishing(array $formattedData): bool
    {
        if (isset($formattedData['publishServices'])) {
            return (bool) $formattedData['publishServices'];
        }

        if (isset($formattedData['publishLocations'])) {
            return (bool) $formattedData['publishLocations'];
        }

        return false;
    }
}
