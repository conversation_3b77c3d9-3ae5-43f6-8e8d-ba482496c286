<?php

declare(strict_types=1);

namespace api\service;

use src\system\language\LanguageSession;

class LanguageSessionService
{
    private LanguageSession $languageSession;

    public function __construct(LanguageSession $languageSession)
    {
        $this->languageSession = $languageSession;
    }

    public function refresh(): void
    {
        $this->languageSession->refresh();
    }

    public function getLocale(): string
    {
        return $this->languageSession->getLocale();
    }

    public function setLanguage(string $locale): void
    {
        setLanguage($locale);
    }
}
