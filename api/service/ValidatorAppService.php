<?php

declare(strict_types=1);

namespace api\service;

use api\interfaces\validator\ValidatorInterface;
use Closure;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\ContainerInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Http\Message\RequestInterface as Request;
use Psr\Http\Message\ServerRequestInterface;
use Slim\Routing\RouteContext;

class ValidatorAppService implements ValidatorInterface
{
    /** @var string */
    public const CONTROLLER_PLACEHOLDER = [
        'controllers',
        'Controller',
    ];

    /** @var string */
    public const VALIDATOR_PLACEHOLDER = [
        'validators',
        'Validator',
    ];
    private ContainerInterface $container;
    private ValidatorInterface $validator;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    /**
     * @throws ContainerExceptionInterface
     */
    public function validate(ServerRequestInterface $request): bool
    {
        $route = $request->getAttribute(RouteContext::ROUTE);

        if ($route === null) {
            // Context provided doesn't exist, no validation required, let the App handle a 404 page not found.
            // so carry on and consider validation ok as there is nothing to validate.
            return true;
        }

        $callableRoute = $route->getCallable();

        if ($callableRoute instanceof Closure) {
            /**
             * If we are here it means we have a matched route that does not specify a controller, it's not currently
             * possible to use this middleware to load a validator on a route without a controller, the idea going
             * forward is that we should have controllers for routes, so if you want to use validation then you
             * will need to update the route to use a controller.
             */
            return true;
        }

        $validatorClassMethod = $this->transformCallable($callableRoute);

        return $this->processControllerSpecificValidator($validatorClassMethod, $request);
    }

    /**
     * @return string[]
     */
    public function getMessages(): array
    {
        return $this->validator->getMessages();
    }

    private function transformCallable(string $callable): string
    {
        return str_replace(self::CONTROLLER_PLACEHOLDER, self::VALIDATOR_PLACEHOLDER, $callable);
    }

    private function parseCallable(string $toResolve): array
    {
        $parts = explode('::', $toResolve);
        if (count($parts) === 1) {
            return [$toResolve, '__invoke'];
        }

        return $parts;
    }

    private function validatorMethodExist(ValidatorInterface $validator, string $method): bool
    {
        return method_exists($validator, $method);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function processControllerSpecificValidator(
        string $validatorClassMethod,
        Request $request
    ): bool {
        [$class, $method] = $this->parseCallable($validatorClassMethod);

        if (!$this->container->has($class)) {
            return true;
        }

        $validator = $this->container->get($class);

        $this->validator = $validator;

        if (!$this->validatorMethodExist($validator, $method)) {
            if (!$this->validatorMethodExist($validator, '__invoke')) {
                // No validation declared just carry on, no validation to run.
                return true;
            }
            $method = '__invoke';
        }

        if (!$validator->{$method}($request)) {
            return false;
        }

        return $validator->validate($request);
    }
}
