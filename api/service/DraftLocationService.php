<?php

declare(strict_types=1);

namespace api\service;

use app\models\draftLocation\entities\DraftLocation;
use app\models\draftLocation\entities\DraftLocationCaptureOnlyData;
use app\models\draftLocation\entities\DraftLocationDescr;
use app\models\draftLocation\entities\DraftLocationIdNumbers;
use app\models\draftLocation\entities\DraftLocationTagLinks;
use app\models\draftLocation\entities\DraftServicesLocations;
use app\models\draftLocation\hydrators\DraftLocationHydrator;
use app\models\location\entities\LocationDescrEntity;
use app\models\location\entities\LocationEntity;
use app\models\location\entities\LocationIdNumbersLinkEntity;
use app\models\location\entities\LocationsServicesLink;
use app\models\location\hydrators\LocationHydrator;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Exception;
use Throwable;

use function count;

class DraftLocationService
{
    private const TABLES_TO_PROMOTE = [
        LocationEntity::class,
        LocationsServicesLink::class,
        LocationIdNumbersLinkEntity::class,
        LocationDescrEntity::class,
    ];
    private const BATCH_SIZE = 1000;
    private DraftLocationHydrator $draftLocationHydrator;
    private EntityManager $entityManager;
    private Connection $connection;
    private LocationHydrator $locationHydrator;

    public function __construct(DraftLocationHydrator $draftLocationHydrator, EntityManager $entityManager, LocationHydrator $locationHydrator)
    {
        $this->draftLocationHydrator = $draftLocationHydrator;
        $this->entityManager = $entityManager;
        $this->locationHydrator = $locationHydrator;
        $this->connection = $this->entityManager->getConnection();
    }

    public function allowNewDraftToCommence(): bool
    {
        $sql = <<<'SQL'
            (SELECT 'draft_location' as non_empty_tables FROM draft_location ) UNION
            (SELECT 'draft_location_tag_links' as draft_location_tag_links FROM draft_location_tag_links ) UNION
            (SELECT 'draft_services_locations' as non_empty_tables FROM draft_services_locations ) UNION
            (SELECT 'draft_locations_id_numbers' as non_empty_tables FROM draft_locations_id_numbers ) UNION
            (SELECT 'draft_location_descr' as non_empty_tables FROM draft_location_descr )
            SQL;
        $tablesWithValues = $this->connection->fetchFirstColumn($sql) ?? [];

        return count($tablesWithValues) === 0;
    }

    public function formatDataIntoEntities(array $treeData, int $autoIncrement): array
    {
        $this->draftLocationHydrator->setAutoIncrement($autoIncrement);
        $entities = [];

        $draftLocationRepository = $this->entityManager->getRepository(DraftLocation::class);
        $existingCaptureOnlyData = $this->getExistingCaptureOnlyLocationData($treeData);
        foreach ($treeData as $node) {
            $existingDraftLocation = isset($node['id']) ? $draftLocationRepository->find($node['id']) : null;
            $entities[] = $this->draftLocationHydrator->hydrate($node, $existingCaptureOnlyData, $existingDraftLocation);
        }

        return $entities;
    }

    /**
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getCounts(): array
    {
        $draftRepositories = $this->getDraftRepositories();

        return [
            'count-locations' => $draftRepositories['location']->count([]),
            'count-titles' => $draftRepositories['translation']->count([]),
            'count-tags' => $draftRepositories['tag']->count([]),
            'count-services' => $draftRepositories['services']->count([]),
            'count-id-numbers' => $draftRepositories['idNumbers']->count([]),
        ];
    }

    public function allowPromotionToCommence(): bool
    {
        $sql = <<<'SQL'
            (SELECT 'draft_location' as non_empty_tables FROM draft_location ) UNION
            (SELECT 'draft_location_descr' as non_empty_tables FROM draft_location_descr )
            SQL;
        $tablesWithValues = $this->connection->fetchFirstColumn($sql) ?? [];

        return count($tablesWithValues) === 2;
    }

    public function deleteLocationData(Connection $connection): void
    {
        $queryBuilder = $connection->createQueryBuilder();
        foreach (self::TABLES_TO_PROMOTE as $table) {
            $tableName = $this->entityManager->getClassMetadata($table)->getTableName();
            $queryBuilder->delete($tableName)
                ->executeStatement();
        }

        // Delete the location tag links data separately.
        $queryBuilder->delete('location_tag_links')
            ->executeStatement();
    }

    public function discardDrafts(): void
    {
        $draftClasses = $this->getDraftClasses();

        foreach ($draftClasses as $class) {
            $queryBuilder = $this->entityManager->createQueryBuilder();
            $queryBuilder->delete()
                ->from($class, 'l')
                ->getQuery()
                ->execute();
        }
    }

    public function promoteDraftsToMain(Connection $connection): void
    {
        $this->promoteBatchedLocations($connection);
    }

    public function getLocationCount(): int
    {
        $qb = $this->connection->createQueryBuilder();
        $locationCount = $qb->select('count(id)')
            ->from('draft_location')
            ->executeQuery()
            ->fetchOne();

        return (int) $locationCount;
    }

    public function promoteBatchedLocations(Connection $connection): void
    {
        $totalLocations = $this->getLocationCount();
        $lowerBatch = 0;

        while ($lowerBatch <= $totalLocations) {
            $qb = $this->connection->createQueryBuilder();

            try {
                $batchedLocations = $qb->select('*')
                    ->from('draft_location')
                    ->setFirstResult($lowerBatch)
                    ->setMaxResults(self::BATCH_SIZE)
                    ->orderBy('id')
                    ->executeQuery()
                    ->fetchAllAssociative();
            } catch (Throwable $e) {
                throw new Exception('Could not retrieve draft locations data.', 0, $e);
            }

            try {
                $locationIds = [];

                foreach ($batchedLocations as $draftLocation) {
                    $connection->insert('location', [
                        'id' => $draftLocation['id'] ?? null,
                        'parent_id' => $draftLocation['parent_id'] ?? null,
                        'title' => $draftLocation['title'] ?? null,
                        '[left]' => $draftLocation['left'] ?? null,
                        '[level]' => $draftLocation['level'] ?? null,
                        '[right]' => $draftLocation['right'] ?? null,
                        'path' => $draftLocation['path'] ?? null,
                        'root' => $draftLocation['root'] ?? null,
                        'cod_npsa_in03' => $draftLocation['cod_npsa_in03'] ?? null,
                        'cod_npsa_rp02' => $draftLocation['cod_npsa_rp02'] ?? null,
                        'cod_npsa_pd20' => $draftLocation['cod_npsa_pd20'] ?? null,
                        'cod_ko41_ods' => $draftLocation['cod_ko41_ods'] ?? null,
                        'cod_ncds_location' => $draftLocation['cod_ncds_location'] ?? null,
                        'cod_hero' => $draftLocation['cod_hero'] ?? null,
                        'status' => $draftLocation['status'] ?? null,
                    ]);
                    $locationIds[] = $draftLocation['id'];
                }
                $this->promoteLocationIds($connection, $locationIds);
                $this->promoteServicesLocation($connection, $locationIds);
                $this->promoteLocationDescr($connection, $locationIds);
                $this->promoteLocationTagLinks($connection, $locationIds);
            } catch (Throwable $e) {
                throw new Exception('Failed to promote draft locations.', 0, $e);
            }
            $lowerBatch += self::BATCH_SIZE;
        }
    }

    public function promoteServicesLocation(Connection $connection, array $locationIds): void
    {
        $queryBuilder = $connection->createQueryBuilder();

        $draftLocationServiceLocations = $queryBuilder->select('*')
            ->from('draft_services_locations')
            ->add('where', $queryBuilder->expr()->in('locationentity_id', ':locationIds'))
            ->setParameter('locationIds', $locationIds, Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();

        foreach ($draftLocationServiceLocations as $draftLocationServiceLocation) {
            try {
                $connection->insert('services_locations', [
                    'serviceentity_id' => $draftLocationServiceLocation['serviceentity_id'] ?? null,
                    'locationentity_id' => $draftLocationServiceLocation['locationentity_id'] ?? null,
                ]);
            } catch (Throwable $e) {
                throw new Exception('Promotion of draft service locations failed.', 0, $e);
            }
        }
    }

    public function promoteLocationIds(Connection $connection, array $locationIds): void
    {
        $queryBuilder = $connection->createQueryBuilder();

        $draftLocationIds = $queryBuilder->select('*')
            ->from('draft_locations_id_numbers')
            ->add('where', $queryBuilder->expr()->in('location_id', ':locationIds'))
            ->setParameter('locationIds', $locationIds, Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();

        foreach ($draftLocationIds as $draftLocationId) {
            try {
                $connection->insert('locations_id_numbers', [
                    'id_number_type' => $draftLocationId['id_number_type'] ?? null,
                    'location_id' => $draftLocationId['location_id'] ?? null,
                ]);
            } catch (Throwable $e) {
                throw new Exception('Promotion of draft location id numbers failed.', 0, $e);
            }
        }
    }

    public function promoteLocationDescr(Connection $connection, array $locationIds): void
    {
        $queryBuilder = $connection->createQueryBuilder();

        $draftLocationDescrs = $queryBuilder->select('*')
            ->from('draft_location_descr')
            ->add('where', $queryBuilder->expr()->in('id', ':locationIds'))
            ->setParameter('locationIds', $locationIds, Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();

        foreach ($draftLocationDescrs as $draftLocationDescr) {
            try {
                $connection->insert('location_descr', [
                    'id' => $draftLocationDescr['id'] ?? null,
                    'language' => $draftLocationDescr['language'] ?? null,
                    'title' => $draftLocationDescr['title'] ?? null,
                    'path' => $draftLocationDescr['path'] ?? null,
                ]);
            } catch (Throwable $e) {
                throw new Exception('Promotion of draft location desc failed.', 0, $e);
            }
        }
    }

    public function promoteLocationTagLinks(Connection $connection, array $locationIds): void
    {
        $queryBuilder = $connection->createQueryBuilder();

        $locationTagLinks = $queryBuilder->select('*')
            ->from('draft_location_tag_links')
            ->add('where', $queryBuilder->expr()->in('location_id', ':locationIds'))
            ->setParameter('locationIds', $locationIds, Connection::PARAM_INT_ARRAY)
            ->executeQuery()
            ->fetchAllAssociative();

        foreach ($locationTagLinks as $locationTagLink) {
            try {
                $this->connection->insert('location_tag_links', [
                    'location_id' => $locationTagLink['location_id'] ?? null,
                    'location_tag_id' => $locationTagLink['location_tag_id'] ?? null,
                    'language' => $locationTagLink['language'] ?? null,
                ]);
            } catch (Throwable $e) {
                throw new Exception('Promotion of draft location desc failed.', 0, $e);
            }
        }
    }

    public function getAutoIncrement(): int
    {
        return $this->draftLocationHydrator->getAndIncrementAutoIncrement();
    }

    private function getExistingCaptureOnlyLocationData(array $data): array
    {
        $nodeIdList = array_column(array_column($data, 'node'), 'id');

        $keyedData = [];
        $captureOnlyDataRepository = $this->entityManager->getRepository(DraftLocationCaptureOnlyData::class);
        $captureOnlyData = $captureOnlyDataRepository->findBy(['id' => $nodeIdList]);

        /** @var DraftLocationCaptureOnlyData $captureOnlyDatum */
        foreach ($captureOnlyData as $captureOnlyDatum) {
            $keyedData[$captureOnlyDatum->getId()] = $captureOnlyDatum;
        }

        return $keyedData;
    }

    private function getDraftRepositories(): array
    {
        $draftClasses = $this->getDraftClasses();
        $entityManager = $this->entityManager;

        return array_map(function (string $class) use ($entityManager) {
            return $entityManager->getRepository($class);
        }, $draftClasses);
    }

    private function getDraftClasses(): array
    {
        return [
            'location' => DraftLocation::class,
            'translation' => DraftLocationDescr::class,
            'tag' => DraftLocationTagLinks::class,
            'services' => DraftServicesLocations::class,
            'idNumbers' => DraftLocationIdNumbers::class,
        ];
    }
}
