<?php

namespace api\service;

use app\models\generic\valueObjects\Module;
use app\models\securityGroup\entities\SecurityGroupEntity;
use app\models\securityGroup\Repository\SecurityGroupRepository;
use app\models\securityGroup\services\SecurityGroupEntityService;
use app\services\dashboard\DashboardService;
use app\services\securityGroups\SecurityGroupService;
use Doctrine\DBAL\Connection;
use InvalidArgumentException;
use src\framework\registry\Registry;
use src\profiles\model\profile\Profile;
use src\profiles\model\profile\ProfileMapper;
use src\system\moduledefs\ModuleDefs;
use src\users\model\User;
use src\users\model\UserMapper;

class AccessCheckService
{
    private UserMapper $userMapper;
    private ProfileMapper $profileMapper;
    private ModuleDefs $moduleDefs;
    private SecurityGroupEntityService $securityGroupEntityService;
    private SecurityGroupService $securityGroupService;
    private Registry $registry;
    private Connection $connection;
    private SecurityGroupRepository $securityGroupRepository;
    private DashboardService $dashboardService;

    public function __construct(
        UserMapper $userMapper,
        ProfileMapper $profileMapper,
        ModuleDefs $moduleDefs,
        SecurityGroupEntityService $securityGroupEntityService,
        SecurityGroupService $securityGroupService,
        Registry $registry,
        Connection $connection,
        SecurityGroupRepository $securityGroupRepository,
        DashboardService $dashboardService
    ) {
        $this->userMapper = $userMapper;
        $this->profileMapper = $profileMapper;
        $this->moduleDefs = $moduleDefs;
        $this->securityGroupEntityService = $securityGroupEntityService;
        $this->securityGroupService = $securityGroupService;
        $this->registry = $registry;
        $this->connection = $connection;
        $this->securityGroupRepository = $securityGroupRepository;
        $this->dashboardService = $dashboardService;
    }

    public function check(int $userId, string $module, int $recordId): array
    {
        $user = $this->getUser($userId);
        $groups = $this->getGroups($user);
        $groupsThatCanAccess = $this->filterGroupsThatCanAccessRecord($groups, $module, $recordId, $user);

        return $this->formatGroupsForResponse($groupsThatCanAccess, $module, $recordId, $user);
    }

    private function getGroupsFromProfile(User $user): array
    {
        $profileId = (int) $user->getStaProfile();

        if (empty($profileId)) {
            return [];
        }

        /** @var ?Profile $profile */
        $profile = $this->profileMapper->find($profileId);
        if ($profile === null) {
            return [];
        }

        $groupsFromProfile = $this->securityGroupRepository->getSecurityGroupFromProfile($profile->getRecordId());

        foreach ($groupsFromProfile as $group) {
            $group->setFromProfile(true);
        }

        return $groupsFromProfile;
    }

    /**
     * @return SecurityGroupEntity[]
     */
    private function getGroups(User $user): array
    {
        $groupsFromProfile = $this->getGroupsFromProfile($user);
        $groupsFromUser = $this->getGroupsFromUser($user);

        return $groupsFromProfile + $groupsFromUser;
    }

    private function getGroupsFromUser(User $user): array
    {
        return $this->securityGroupRepository->getSecurityGroupFromUser($user->recordid);
    }

    private function filterGroupsThatCanAccessRecord(array $groups, string $module, int $recordid, User $user): array
    {
        $groupsThatCanAccessRecord = [];

        foreach ($groups as $group) {
            if ($this->canGroupAccessRecord($group, $module, $recordid, $user)) {
                $groupsThatCanAccessRecord[] = $group;
            }
        }

        return $groupsThatCanAccessRecord;
    }

    private function canGroupAccessRecord(SecurityGroupEntity $group, string $module, int $recordId, User $user): bool
    {
        $table = $this->moduleDefs[$module]->getDbReadObj();
        $permission = $group->getDecodedPermission()[$module]['where'];

        $moduleUsesWhereClause = $this->moduleDefs[$module]->usesModuleWhereClause();

        if ($moduleUsesWhereClause && empty($permission)) {
            return false;
        }

        $translatedPermission = TranslateWhereCom($permission, $user->getInitials(), $module);
        $translatedPermission = !empty($translatedPermission) ? ' AND ' . $translatedPermission : '';
        $sql = <<<SQL
            SELECT recordid
            FROM {$table}
            WHERE recordid = :recordid {$translatedPermission}
            SQL;

        $returnValue = $this->connection->fetchOne($sql, ['recordid' => $recordId]);

        return $returnValue !== false;
    }

    private function formatGroupsForResponse(array $groups, string $module, int $recordId, User $user): array
    {
        $groupsWhichAccess = [];
        $groupsWhichEmail = [];
        $groupsWhichDeny = [];
        $groupsFromProfile = [];
        $groupsFromUser = [];
        $topLevelWhere = $this->securityGroupService->makeToplevelSecurityWhereClause($module, $user->getRecordid(), false, $recordId);
        $permGlobal = $this->moduleDefs[$module]['PERM_GLOBAL'] ?? '';

        /** @var SecurityGroupEntity $group */
        foreach ($groups as $group) {
            if ($group->doesProvideAccess()) {
                $groupsWhichAccess[] = $group->getRecordid();
            }

            if ($group->doesEmail()) {
                $groupsWhichEmail[] = $group->getRecordid();
            }

            if ($group->doesDeny()) {
                $groupsWhichDeny[] = $group->getRecordid();
            }

            if ($group->isFromProfile()) {
                $groupsFromProfile[] = $this->securityGroupEntityService->getForResponse($group, $module, $permGlobal);

                continue;
            }
            $groupsFromUser[] = $this->securityGroupEntityService->getForResponse($group, $module, $permGlobal);
        }

        return [
            'useAdvancedAccessLevels' => $this->registry->getParm('USE_ADVANCED_ACCESS_LEVELS', 'N')->toBool(),
            'userCanSeeRecord' => $this->canUserSeeRecord($groupsWhichDeny, $groupsWhichAccess, $module, $recordId, $user->getRecordid()),
            'userWouldBeEmailedForRecord' => empty($groupsWhichDeny) && !empty($groupsWhichEmail),
            'groupsThatWouldProvideAccess' => $groupsWhichAccess,
            'groupsThatWouldEmailUserForThisRecord' => $groupsWhichEmail,
            'groupsThatDenyAccess' => $groupsWhichDeny,
            'groupsInProfile' => $groupsFromProfile,
            'groupsAttachedToUser' => $groupsFromUser,
            'topLevelWhere' => $topLevelWhere,
        ];
    }

    private function getUser(int $userId): User
    {
        /** @var ?User $user */
        $user = $this->userMapper->find($userId, true);
        if ($user === null) {
            throw new InvalidArgumentException('User not found');
        }

        return $user;
    }

    private function canUserSeeRecord(array $groupsWhichDeny, array $groupsWhichAccess, string $module, int $recordId, int $userRecordId): bool
    {
        if ($module === Module::DASHBOARDS) {
            return $this->dashboardService->canCurrentUserViewDashboard($recordId, $userRecordId, $groupsWhichDeny);
        }

        return empty($groupsWhichDeny) && !empty($groupsWhichAccess);
    }
}
