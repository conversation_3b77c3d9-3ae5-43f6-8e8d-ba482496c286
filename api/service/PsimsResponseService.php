<?php

declare(strict_types=1);

namespace api\service;

use app\models\incidents\entities\PsimsResponseEntity;
use app\models\incidents\hydrators\PsimsResponseHydrator;
use app\models\incidents\IncidentRepository;
use app\models\incidents\repositories\PsimsResponseRepository;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use src\system\container\facade\Container;

class PsimsResponseService
{
    private IncidentRepository $incidentRepository;
    private PsimsResponseRepository $psimsResponseRepository;

    public function __construct(IncidentRepository $incidentRepository, PsimsResponseRepository $psimsResponseRepository)
    {
        $this->incidentRepository = $incidentRepository;
        $this->psimsResponseRepository = $psimsResponseRepository;
    }

    public function checkIncidentExists(int $recordId): bool
    {
        return $this->incidentRepository->exists($recordId);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function upsertResponse(array $recordIds, array $data): void
    {
        $this->psimsResponseRepository->deleteByIds($recordIds);
        $psimsResponseEntity = Container::get(PsimsResponseEntity::class);

        $psimsResponseRecord = PsimsResponseHydrator::hydrate($psimsResponseEntity, $data);
        $this->psimsResponseRepository->insert($psimsResponseRecord);
    }
}
