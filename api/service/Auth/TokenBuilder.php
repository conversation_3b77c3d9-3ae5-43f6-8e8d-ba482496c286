<?php

declare(strict_types=1);

namespace api\service\Auth;

use app\models\framework\config\DatixConfig;
use DateInterval;
use DateTimeImmutable;
use Firebase\JWT\JWT;

class TokenBuilder
{
    private DatixConfig $config;

    public function __construct(DatixConfig $config)
    {
        $this->config = $config;
    }

    public function build(string $subject): string
    {
        $now = new DateTimeImmutable();
        $future = $now->add(new DateInterval('PT1H'));

        $payload = [
            'iat' => $now->getTimestamp(),
            'exp' => $future->getTimestamp(),
            'sub' => $subject,
        ];

        $jwtKey = $this->config->getJWTSecret();

        return JWT::encode($payload, $jwtKey->getKeyMaterial(), $jwtKey->getAlgorithm());
    }
}
