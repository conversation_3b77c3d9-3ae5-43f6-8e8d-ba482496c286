<?php

declare(strict_types=1);

namespace api\service\User;

use app\models\generic\InitialsGenerator;
use app\models\globals\UserParmEntity;
use app\models\user\entities\UserEntity;
use app\models\user\hydrators\UserHydrator;
use app\models\user\repositories\UserRepository;
use Doctrine\ORM\EntityManagerInterface;

class UpdateUsersService
{
    /** @readonly  */
    private InitialsGenerator $initialsGenerator;

    /** @readonly  */
    private UserRepository $repository;

    /** @readonly  */
    private UserHydrator $hydrator;

    /** @readonly  */
    private EntityManagerInterface $entityManager;

    public function __construct(
        EntityManagerInterface $entityManager,
        InitialsGenerator $initialsGenerator,
        UserRepository $repository,
        UserHydrator $hydrator
    ) {
        $this->entityManager = $entityManager;
        $this->initialsGenerator = $initialsGenerator;
        $this->repository = $repository;
        $this->hydrator = $hydrator;
    }

    public function update(array $data): UserEntity
    {
        $user = $this->repository->find($data['id']);

        if ($user === null) {
            $user = $this->hydrator->hydrate($data);
        } else {
            $this->updateLoginReferences($user, $data);
            $user = $this->hydrator->hydrate($data, $user->getInitials(), $user);
        }

        $this->populateInitials($user);
        $this->cleanseLocations($user, $data);
        $this->cleanseServices($user, $data);

        $this->entityManager->persist($user);
        $this->entityManager->flush();

        return $user;
    }

    public function populateInitials(UserEntity $user): void
    {
        if (!empty($user->getInitials())) {
            return;
        }

        $initials = $this->initialsGenerator->generate($user->getRecordid());
        $user->setInitials($initials);
    }

    public function cleanseLocations(UserEntity $user, array $data): void
    {
        $userLocations = $user->getLocations();

        if (empty($data['location'])) {
            $userLocations->clear();

            return;
        }

        $dataLocations = [];
        foreach ($data['location'] as $dataLocation) {
            $dataLocations[$dataLocation['id']] = $dataLocation['id'];
        }

        foreach ($userLocations as $userLocation) {
            if (!isset($dataLocations[(int) $userLocation->getId()])) {
                $userLocations->removeElement($userLocation);
            }
        }
    }

    public function cleanseServices(UserEntity $user, array $data): void
    {
        $userServices = $user->getServices();

        if (empty($data['service'])) {
            $userServices->clear();

            return;
        }

        $dataServices = [];
        foreach ($data['service'] as $dataService) {
            $dataServices[$dataService['id']] = $dataService['id'];
        }

        foreach ($userServices as $userService) {
            if (!isset($dataServices[(int) $userService->getId()])) {
                $userServices->removeElement($userService);
            }
        }
    }

    public function updateLoginReferences(UserEntity $user, array $data): void
    {
        if ($data['identity'] === $user->getLogin()) {
            return;
        }

        $userParmsRepo = $this->entityManager->getRepository(UserParmEntity::class);
        $userParms = $userParmsRepo->findBy([
            'login' => $user->getLogin(),
        ]);
        foreach ($userParms as $userParm) {
            $userParm->setLogin($data['identity']);
            $this->entityManager->persist($userParm);
        }
    }
}
