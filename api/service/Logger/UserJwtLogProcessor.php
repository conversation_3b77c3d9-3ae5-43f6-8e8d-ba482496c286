<?php

declare(strict_types=1);

namespace api\service\Logger;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;
use Psr\Http\Message\ServerRequestInterface;
use src\admin\services\JwtService;
use Throwable;

final class UserJwtLogProcessor implements ProcessorInterface
{
    private ServerRequestInterface $request;
    private JwtService $jwtService;

    public function __construct(ServerRequestInterface $request, JwtService $jwtService)
    {
        $this->request = $request;
        $this->jwtService = $jwtService;
    }

    public function __invoke(LogRecord $record): LogRecord
    {
        $userId = $this->getUserId();

        if ($userId !== null) {
            $record->extra['userId'] = $userId;
        }

        return $record;
    }

    private function getUserId(): ?int
    {
        $header = $this->getTokenFromHeader($this->request);

        if ($header === null) {
            return null;
        }

        try {
            $token = $this->jwtService->decode($header);

            return $token['userId'] ?? null;
        } catch (Throwable $exception) {
            return null;
        }
    }

    private function getTokenFromHeader(ServerRequestInterface $request): ?string
    {
        $header = $request->getHeader('Authorization')[0] ?? null;

        if ($header === null) {
            return null;
        }

        return str_replace('Bearer ', '', $header);
    }
}
