<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\controllers\Auth\TokenController;
use api\controllers\Documents\DocumentTemplatesController;
use api\controllers\Documents\ImportDocumentsController;
use api\controllers\Feedback\CreateFeedbackController;
use api\controllers\Incidents\Mobile\IncidentsMobileV1Controller;
use api\controllers\Payments\CreatePaymentController;
use api\controllers\Users\LogoutFormUrlController;
use api\controllers\Payments\ListPaymentsController;
use api\controllers\Users\UpdateUsersController;
use api\service\Auth\TokenBuilder;
use api\service\User\UpdateUsersService;
use api\V2\Controllers\GetController;
use api\V2\Controllers\Incidents\GetIncidentsController;
use api\V2\Controllers\PatchController;
use api\V2\Hydrators\Feedback\FeedbackHydratorManager;
use api\V2\Services\RequestService;
use api\V2\Services\ResponseService;
use api\V2\Services\UserAccessServiceInterface;
use app\models\contact\entities\ContactEntity;
use app\models\contact\hydrators\CarltonContactHydrator;
use app\models\contact\hydrators\ContactLinksHydrator;
use app\models\contact\repositories\ContactRepository;
use app\models\feedback\entities\FeedbackEntity;
use app\models\feedback\hydrators\FeedbackHydrator;
use app\models\feedback\hydrators\FeedbackLinksHydrator;
use app\models\feedback\hydrators\FeedbackSubjectHydrator;
use app\models\framework\config\DatixConfig;
use app\models\mobile\adaptors\MobileIncidentAggregateAdaptor;
use app\models\mobile\MobileIncidentRepository;
use app\models\user\entities\UserEntity;
use app\models\user\hydrators\UserHydrator;
use app\services\carlton\user\UserService;
use app\services\document\storagehandlers\DocumentApiUploadService;
use app\services\document\storagehandlers\FileStorageHandlerFactory;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\profiles\ProfileService;
use app\services\records\RecordSourceService;
use app\services\transcription\Transcriber;
use app\services\udf\UdfService;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use League\Container\ServiceProvider\AbstractServiceProvider;
use src\complaints\model\FeedbackMapper;
use src\contacts\service\ContactPersister;
use src\generic\services\SaveMainRecordService;
use src\payments\services\PaymentExportService;
use src\udf\service\UserDefinedField;
use src\wordmergetemplate\model\WordMergeTemplateModelFactory;

use function in_array;

final class ControllerServiceProvider extends AbstractServiceProvider
{
    public function provides(string $id): bool
    {
        return in_array($id, [
            CreateFeedbackController::class,
            CreatePaymentController::class,
            DocumentTemplatesController::class,
            'FeedbackGetController',
            'IncidentsGetController',
            'FeedbackPatchController',
            ImportDocumentsController::class,
            IncidentsMobileV1Controller::class,
            ListPaymentsController::class,
            LogoutFormUrlController::class,
            TokenController::class,
            UpdateUsersController::class,
        ], true);
    }

    public function register(): void
    {
        $this->getContainer()
            ->add(TokenController::class)
            ->addArgument(TokenBuilder::class);

        $this->getContainer()
            ->add(UpdateUsersController::class)
            ->addArgument(UserHydrator::class)
            ->addArgument(ProfileService::class)
            ->addArgument(UpdateUsersService::class);

        $this->getContainer()
            ->add(CreateFeedbackController::class)
            ->addArgument(RecordIdGeneratorFactory::class)
            ->addArgument(EntityManagerInterface::class)
            ->setConcrete(function (RecordIdGeneratorFactory $idGeneratorFactory, EntityManagerInterface $entityManager): CreateFeedbackController {
                $container = $this->getContainer();

                /** @var ContactRepository $contactRepository */
                $contactRepository = $entityManager->getRepository(ContactEntity::class);

                return new CreateFeedbackController(
                    $container->get(RecordSourceService::class),
                    $entityManager,
                    $idGeneratorFactory->create('compl_main'),
                    $idGeneratorFactory->create('link_compl'),
                    $idGeneratorFactory->create('compl_subjects'),
                    $container->get(FeedbackHydrator::class),
                    $container->get(UdfService::class),
                    $container->get(DocumentApiUploadService::class),
                    $container->get(FeedbackSubjectHydrator::class),
                    $container->get(CarltonContactHydrator::class),
                    $container->get(ContactPersister::class),
                    $container->get(ContactLinksHydrator::class),
                    $container->get(SaveMainRecordService::class),
                    $contactRepository,
                    $container->get(FeedbackLinksHydrator::class),
                    $entityManager->getRepository(FeedbackEntity::class),
                );
            });

        $this->getContainer()
            ->add(CreatePaymentController::class)
            ->addArgument(RecordIdGeneratorFactory::class)
            ->addArgument(UserDefinedField::class)
            ->addArgument(Connection::class)
            ->setConcrete(static function (
                RecordIdGeneratorFactory $idGeneratorFactory,
                UserDefinedField $userDefinedFieldService,
                Connection $connection
            ): CreatePaymentController {
                return new CreatePaymentController(
                    $idGeneratorFactory->create('payments'),
                    $userDefinedFieldService,
                    $connection,
                );
            });

        $this->getContainer()
            ->add(ImportDocumentsController::class)
            ->addArgument(Connection::class)
            ->addArgument(FileStorageHandlerFactory::class)
            ->setConcrete(static function (Connection $db, FileStorageHandlerFactory $factory): ImportDocumentsController {
                return new ImportDocumentsController($db, $factory->create());
            });

        $this->getContainer()
            ->add(DocumentTemplatesController::class)
            ->addArgument(WordMergeTemplateModelFactory::class)
            ->addArgument(Connection::class)
            ->addArgument(FileStorageHandlerFactory::class)
            ->setConcrete(static function (
                WordMergeTemplateModelFactory $modelFactory,
                Connection $db,
                FileStorageHandlerFactory $storageFactory
            ): DocumentTemplatesController {
                return new DocumentTemplatesController($modelFactory, $db, $storageFactory->create());
            });

        $this->getContainer()
            ->add(IncidentsMobileV1Controller::class)
            ->addArgument(MobileIncidentAggregateAdaptor::class)
            ->addArgument(MobileIncidentRepository::class)
            ->addArgument(Transcriber::class)
            ->addArgument(DatixConfig::class)
            ->setConcrete(static function (
                MobileIncidentAggregateAdaptor $incidentAdaptor,
                MobileIncidentRepository $repository,
                Transcriber $transcriber,
                DatixConfig $config
            ): IncidentsMobileV1Controller {
                return new IncidentsMobileV1Controller(
                    $incidentAdaptor,
                    $repository,
                    $transcriber,
                    $config->isTranscriptionEnabled(),
                );
            });

        $this->getContainer()
            ->add('FeedbackGetController', GetController::class)
            ->addArgument(RequestService::class)
            ->addArgument(ResponseService::class)
            ->addArgument('FeedbackGetResponseDataService');

        $this->getContainer()
            ->add('IncidentsGetController', GetIncidentsController::class)
            ->addArgument(RequestService::class)
            ->addArgument(ResponseService::class)
            ->addArgument('IncidentsGetResponseDataService');

        $this->getContainer()
            ->add('FeedbackPatchController', PatchController::class)
            ->addArgument(ResponseService::class)
            ->addArgument(EntityManagerInterface::class)
            ->addArgument(FeedbackHydratorManager::class)
            ->addArgument('FeedbackGetResponseDataService')
            ->addArgument(UserAccessServiceInterface::class)
            ->addArgument(FeedbackMapper::class);

        $this->getContainer()
            ->add(ListPaymentsController::class)
            ->addArgument(PaymentExportService::class);

        $this->getContainer()
            ->add(LogoutFormUrlController::class)
            ->addArgument(EntityManagerInterface::class)
            ->addArgument(UserService::class)
            ->setConcrete(static function (
                EntityManagerInterface $entityManager,
                UserService $userService
            ): LogoutFormUrlController {
                return new LogoutFormUrlController(
                    $entityManager,
                    $entityManager->getRepository(UserEntity::class),
                    $userService,
                );
            });
    }
}
