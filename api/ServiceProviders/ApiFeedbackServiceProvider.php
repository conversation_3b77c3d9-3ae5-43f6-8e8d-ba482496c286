<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\V2\Formatters\FieldFormatterFactory;
use api\V2\Hydrators\Feedback\FeedbackHydrator;
use api\V2\Hydrators\Feedback\FeedbackHydratorManager;
use api\V2\Services\DataFormatterService;
use api\V2\Services\Feedback\FeedbackEntityCacheService;
use api\V2\Services\FieldDefsService;
use api\V2\Services\FieldMapService;
use api\V2\Services\GetResponseDataService;
use api\V2\Services\ModuleService;
use api\V2\Services\ReadOnlyFieldService;
use api\V2\Services\RequestService;
use app\models\feedback\entities\FeedbackEntity;
use app\services\document\storagehandlers\DocumentApiUploadService;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\udf\UdfService;
use Doctrine\ORM\EntityManagerInterface;
use League\Container\ServiceProvider\AbstractServiceProvider;
use src\complaints\model\FeedbackModelFactory;

use function in_array;

class ApiFeedbackServiceProvider extends AbstractServiceProvider
{
    public function provides(string $id): bool
    {
        return in_array($id, [
            'FeedbackFieldMapService',
            'FeedbackDataFormatterService',
            'FeedbackGetResponseDataService',
            FeedbackHydratorManager::class,
        ], true);
    }

    public function register(): void
    {
        $this->getContainer()
            ->add('FeedbackFieldMapService', FieldMapService::class)
            ->setConcrete(static function (): FieldMapService {
                $fieldMap = require FieldMapService::MAP_LOCATIONS['feedback'];

                return new FieldMapService($fieldMap);
            });

        $this->getContainer()
            ->add('FeedbackDataFormatterService', DataFormatterService::class)
            ->addArgument(FieldDefsService::class)
            ->addArgument(FieldFormatterFactory::class)
            ->addArgument('FeedbackFieldMapService');

        $this->getContainer()
            ->add('FeedbackGetResponseDataService', GetResponseDataService::class)
            ->addArgument(FeedbackModelFactory::class)
            ->addArgument('FeedbackDataFormatterService')
            ->addArgument('FeedbackFieldMapService');

        $this->getContainer()
            ->add(FeedbackHydratorManager::class)
            ->addArgument(ReadOnlyFieldService::class)
            ->addArgument(FieldDefsService::class)
            ->addArgument(RequestService::class)
            ->addArgument(FeedbackEntity::class)
            ->addArgument(EntityManagerInterface::class)
            ->addArgument(ModuleService::class)
            ->addArgument(RecordIdGeneratorFactory::class)
            ->addArgument(FeedbackHydrator::class)
            ->addArgument(UdfService::class)
            ->addArgument(DocumentApiUploadService::class)
            ->addArgument('FeedbackFieldMapService')
            ->addArgument('FeedbackDataFormatterService')
            ->addArgument(FeedbackEntityCacheService::class);
    }
}
