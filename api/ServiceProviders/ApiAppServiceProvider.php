<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\service\Logger\UserJwtLogProcessor;
use api\service\PsimsResponseService;
use api\V2\Services\Feedback\FeedbackEntityCacheService;
use api\V2\Services\RequestService;
use api\V2\Services\UserAccessService;
use api\V2\Services\UserAccessServiceInterface;
use app\models\framework\config\DatixConfig;
use app\models\incidents\entities\IncidentProgressNoteEntity;
use app\models\incidents\entities\PsimsResponseEntity;
use app\models\incidents\entities\PsimsResponseWarningEntity;
use app\models\incidents\IncidentRepositoryFactory;
use app\models\incidents\repositories\ProgressNotesRepository;
use app\models\incidents\repositories\PsimsResponseRepository;
use app\models\mobile\adaptors\MobileIncidentAdaptor;
use app\models\mobile\MobileIncidentRepository;
use app\models\mobile\MobileIncidentRepositoryFactory;
use app\models\user\entities\UserEntity;
use app\services\email\ReporterUpdateNotificationService;
use app\services\email\StaffChangeNotificationService;
use app\services\idGenerator\RecordIdGeneratorFactory;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;
use Doctrine\ORM\EntityManagerInterface;
use League\Container\Argument\Literal\StringArgument;
use League\Container\ServiceProvider\AbstractServiceProvider;
use League\Container\ServiceProvider\BootableServiceProviderInterface;
use Psr\Log\LoggerInterface;
use src\admin\services\JwtService;
use src\contacts\model\ContactModelFactory;
use src\email\EmailSenderFactory;
use src\framework\registry\Registry;
use src\logger\LoggerFactory;
use src\users\model\UserMapper;
use src\users\specifications\EmailableUserSpecification;

use function in_array;
use function dirname;

final class ApiAppServiceProvider extends AbstractServiceProvider implements BootableServiceProviderInterface
{
    public function boot(): void
    {
        $this->getContainer()
            ->addShared(UserJwtLogProcessor::class)
            ->addArgument('request')
            ->addArgument(JwtService::class)
            ->addTag('logger.processors');
    }

    public function provides(string $id): bool
    {
        return in_array($id, [
            'appRoot',
            'db',
            LoggerInterface::class,
            MobileIncidentAdaptor::class,
            MobileIncidentRepository::class,
            StaffChangeNotificationService::class,
            ReporterUpdateNotificationService::class,
            UserAccessServiceInterface::class,
            FeedbackEntityCacheService::class,
            ProgressNotesRepository::class,
            PsimsResponseRepository::class,
            PsimsResponseService::class,
            PsimsResponseWarningEntity::class,
        ], true);
    }

    public function register(): void
    {
        $this->getContainer()->addShared('appRoot', new StringArgument(dirname(__DIR__) . '/../../'));

        $this->getContainer()->addShared('db')
            ->addArgument(DatixConfig::class)
            ->setConcrete(function (DatixConfig $config): Connection {
                return DriverManager::getConnection([
                    'dbname' => $config->getDbName(),
                    'user' => $config->getDbUsername(),
                    'password' => $config->getDbPassword(),
                    'host' => $config->getDbServerName(),
                    'driver' => 'pdo_sqlsrv',
                ]);
            });

        $this->getContainer()
            ->add(LoggerInterface::class)
            ->addArgument(LoggerFactory::class)
            ->setConcrete(static function (LoggerFactory $factory): LoggerInterface {
                return $factory->create('api_log');
            });

        $this->getContainer()
            ->addShared(MobileIncidentAdaptor::class)
            ->addArgument(RecordIdGeneratorFactory::class)
            ->addArgument(Registry::class)
            ->addArgument(EntityManagerInterface::class)
            ->setConcrete(static function (
                RecordIdGeneratorFactory $factory,
                Registry $registry,
                EntityManagerInterface $entityManager
            ): MobileIncidentAdaptor {
                return new MobileIncidentAdaptor(
                    $factory->create('incidents_main'),
                    $registry,
                    $entityManager->getRepository(UserEntity::class),
                );
            });

        $this->getContainer()
            ->addShared(MobileIncidentRepository::class)
            ->addArgument(MobileIncidentRepositoryFactory::class)
            ->setConcrete(static function (MobileIncidentRepositoryFactory $factory): MobileIncidentRepository {
                return $factory->create();
            });

        $this->getContainer()->addShared(StaffChangeNotificationService::class)
            ->addArgument(UserMapper::class)
            ->addArgument(LoggerInterface::class)
            ->setConcrete(static function (UserMapper $userMapper, LoggerInterface $logger): StaffChangeNotificationService {
                return new StaffChangeNotificationService(
                    $userMapper,
                    new EmailableUserSpecification(),
                    $logger,
                    new EmailSenderFactory(),
                );
            });

        $this->getContainer()->addShared(ReporterUpdateNotificationService::class)
            ->addArgument(LoggerInterface::class)
            ->setConcrete(static function (LoggerInterface $logger): ReporterUpdateNotificationService {
                return new ReporterUpdateNotificationService(
                    new EmailSenderFactory(),
                    (new ContactModelFactory())->getMapper(),
                    $logger,
                );
            });

        $this->getContainer()
            ->addShared(UserAccessServiceInterface::class, UserAccessService::class)
            ->addArgument(RequestService::class);

        $this->getContainer()->addShared(FeedbackEntityCacheService::class);

        $this->getContainer()->addShared(PsimsResponseWarningEntity::class);

        $this->getContainer()
            ->addShared(ProgressNotesRepository::class)
            ->addArgument(EntityManagerInterface::class)
            ->setConcrete(static function (EntityManagerInterface $em): ProgressNotesRepository {
                return new ProgressNotesRepository($em->getRepository(IncidentProgressNoteEntity::class));
            });

        $this->getContainer()
            ->addShared(PsimsResponseRepository::class)
            ->addArgument(EntityManagerInterface::class)
            ->setConcrete(static function (EntityManagerInterface $em): PsimsResponseRepository {
                return $em->getRepository(PsimsResponseEntity::class);
            });

        $this->getContainer()
            ->addShared(PsimsResponseService::class)
            ->addArgument(EntityManagerInterface::class)
            ->setConcrete(static function (EntityManagerInterface $em): PsimsResponseService {
                return new PsimsResponseService(
                    (new IncidentRepositoryFactory())->create(),
                    $em->getRepository(PsimsResponseEntity::class),
                );
            });
    }
}
