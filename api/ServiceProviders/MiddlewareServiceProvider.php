<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\helpers\UsersHelper;
use api\middleware\LanguageSessionMiddleware;
use api\middleware\ValidatorMiddleware;
use api\service\LanguageSessionService;
use api\service\ValidatorAppService;
use api\V2\Services\ResponseService;
use api\V2\Validators\Contacts\CarltonContactsSyncValidator;
use api\V2\Validators\Contacts\ContactsBatchValidator;
use api\V2\Validators\Etl\EtlTableNameValidator;
use api\V2\Validators\Etl\EtlTableSyncValidator;
use app\models\framework\config\DatixConfig;
use League\Container\ServiceProvider\AbstractServiceProvider;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use src\framework\session\UserSessionFactory;
use Teapot\StatusCode\RFC\RFC7235;
use Tuupola\Middleware\HttpBasicAuthentication;
use Tuupola\Middleware\JwtAuthentication;
use Tuupola\Middleware\JwtAuthentication\RequestPathRule;

use function in_array;

use const JSON_UNESCAPED_SLASHES;
use const JSON_PRETTY_PRINT;

final class MiddlewareServiceProvider extends AbstractServiceProvider
{
    public const VALIDATOR_CONTACTS_BATCH = 'validator.contacts_batch';
    private const VALIDATOR_ETL_SYNC = 'validator.etl_sync';
    private const VALIDATOR_ETL_TABLE_NAMES = 'validator.etl_sync_table_names';
    private const VALIDATOR_CARLTON_CONTACTS_SYNC = 'validator.carlton_contacts_sync';

    public function provides(string $id): bool
    {
        return in_array($id, [
            HttpBasicAuthentication::class,
            JwtAuthentication::class,
            ValidatorMiddleware::class,
            self::VALIDATOR_ETL_SYNC,
            self::VALIDATOR_ETL_TABLE_NAMES,
            self::VALIDATOR_CARLTON_CONTACTS_SYNC,
            self::VALIDATOR_CONTACTS_BATCH,
        ], true);
    }

    public function register(): void
    {
        $container = $this->getContainer();

        $datixConfig = $container->get(DatixConfig::class);

        $container->addShared(JwtAuthentication::class)
            ->addArgument(
                [
                    'secret' => $datixConfig->getJWTSecret()->getKeyMaterial(),
                    'algorithm' => $datixConfig->getJWTSecret()->getAlgorithm(),
                    'secure' => false,
                    'rules' => [
                        new RequestPathRule([
                            'path' => '/',
                            'ignore' => [
                                '/api/token',
                                '/api/incidents/mobile',
                                '/api/healthcheck',
                                '/api/healthz',
                            ],
                        ]),
                    ],
                    'callback' => function (ServerRequestInterface $request, ResponseInterface $response, array $arguments): bool {
                        // Global values fallback to the defaults if not found in the session, so we need to
                        // ensure they're set in the session here to prevent weird behaviour
                        $username = '';
                        $user = UsersHelper::getUserFromJWT($arguments['decoded']);
                        if ($user) {
                            $username = $user->getLogin();
                            (new UserSessionFactory())->create()->initSession($user, $user->getRecordid());
                        }
                        GetParms($username);

                        // Sometimes we need to reference the token when sending requests to other services (such as the Notification Centre)
                        // and currently when we do so we reference the cookie value directly. Because we don't require a cookie for API requests
                        // (nor should we..), the easiest thing to do for now is to just manually set this value here.
                        $_COOKIE['datix_jwtToken'] = $arguments['token'];

                        return true;
                    },
                    'error' => function (ResponseInterface $response, array $arguments) use ($container) {
                        if (str_contains($_SERVER['REQUEST_URI'], '/v2/')) {
                            return $container->get(ResponseService::class)
                                ->formatErrorResponse($response, RFC7235::UNAUTHORIZED, 'Unauthorised request');
                        }

                        $data['status'] = 'error';
                        $data['message'] = 'Authentication failed';

                        $response->getBody()->write(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));

                        return $response->withHeader('Content-Type', 'application/json');
                    },
                ],
            );

        $container->addShared(HttpBasicAuthentication::class)
            ->addArgument(
                [
                    'path' => '/token',
                    'secure' => false,
                    'users' => [
                        $datixConfig->getTokenApiUsername() => $datixConfig->getTokenApiPassword(),
                    ],
                    'error' => function (ServerRequestInterface $request, ResponseInterface $response) {
                        $data['status'] = 'error';
                        $data['message'] = 'Authentication failed';

                        $response->getBody()->write(json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));

                        return $response->withHeader('Content-Type', 'application/json');
                    },
                ],
            );

        $container->addShared(ValidatorMiddleware::class)
            ->addArgument(ValidatorAppService::class)
            ->addArgument(ResponseService::class);

        $container->addShared(LanguageSessionMiddleware::class)
            ->addArgument(LanguageSessionService::class);

        // TODO:: for the next person working on ETL_SYNC please move the following 4 service providers to
        // ValidatorsServiceProvider, this file is meant for middleware only.
        $container->addShared(self::VALIDATOR_ETL_SYNC)
            ->setConcrete(ValidatorMiddleware::class)
            ->addArgument(EtlTableSyncValidator::class)
            ->addArgument(ResponseService::class);

        $container->addShared(self::VALIDATOR_ETL_TABLE_NAMES)
            ->setConcrete(ValidatorMiddleware::class)
            ->addArgument(EtlTableNameValidator::class)
            ->addArgument(ResponseService::class);

        $container->addShared(self::VALIDATOR_CARLTON_CONTACTS_SYNC)
            ->setConcrete(ValidatorMiddleware::class)
            ->addArgument(CarltonContactsSyncValidator::class)
            ->addArgument(ResponseService::class);

        $container->addShared(self::VALIDATOR_CONTACTS_BATCH)
            ->setConcrete(ValidatorMiddleware::class)
            ->addArgument(ContactsBatchValidator::class)
            ->addArgument(ResponseService::class);
    }
}
