<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\service\Logger\UserJwtLogProcessor;
use api\V2\Formatters\FieldFormatterFactory;
use api\V2\Services\DataFormatterService;
use api\V2\Services\FieldDefsService;
use api\V2\Services\FieldMapService;
use api\V2\Services\GetResponseDataService;
use League\Container\ServiceProvider\AbstractServiceProvider;
use League\Container\ServiceProvider\BootableServiceProviderInterface;
use src\admin\services\JwtService;
use src\incidents\model\IncidentModelFactory;

final class ApiIncidentsServiceProvider extends AbstractServiceProvider implements BootableServiceProviderInterface
{
    public function boot(): void
    {
        $this->getContainer()
            ->addShared(UserJwtLogProcessor::class)
            ->addArgument('request')
            ->addArgument(JwtService::class)
            ->addArgument('logger.processors');
    }

    public function provides(string $id): bool
    {
        return in_array($id, [
            'IncidentsFieldMapService',
            'IncidentsDataFormatterService',
            'IncidentsGetResponseDataService',
        ]);
    }

    public function register(): void
    {
        $this->getContainer()
            ->add('IncidentsFieldMapService', FieldMapService::class)
            ->setConcrete(static function (): FieldMapService {
                $fieldMap = require FieldMapService::MAP_LOCATIONS['incident'];

                return new FieldMapService($fieldMap);
            });

        $this->getContainer()
            ->add('IncidentsDataFormatterService', DataFormatterService::class)
            ->addArgument(FieldDefsService::class)// Remove FieldDefsService its slow
            ->addArgument(FieldFormatterFactory::class)
            ->addArgument('IncidentsFieldMapService');

        $this->getContainer()
            ->add('IncidentsGetResponseDataService', GetResponseDataService::class)
            ->addArgument(IncidentModelFactory::class)
            ->addArgument('IncidentsDataFormatterService')
            ->addArgument('IncidentsFieldMapService');
    }
}
