<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\controllers\AccessCheckController;
use api\controllers\ActionsController;
use api\controllers\ActionTriggersController;
use api\controllers\Claims\ClaimsController;
use api\controllers\ClearFlaggedForInvestigationAction;
use api\controllers\Codes\CodesController;
use api\controllers\Contacts\ContactsMergeController;
use api\controllers\DraftLocationsController;
use api\controllers\HealthcheckController;
use api\controllers\LocationsController;
use api\controllers\MaintenanceModeController;
use api\controllers\NotificationController;
use api\controllers\ServicesController;
use api\controllers\UsersController;
use api\middleware\MaintenanceModeMiddleware;
use api\service\AccessCheckService;
use api\service\DraftLocationService;
use api\service\LocationsServicesData\LocationsServicesDataFactory;
use api\service\MaintenanceModeApiService;
use api\V2\Services\Locations\LocationsService;
use api\V2\Services\Services\ServicesService;
use api\validators\ClearFlaggedForInvestigationValidator;
use api\validators\Contacts\ContactMergeValidator;
use api\validators\MaintenanceMode\MaintenanceModeValidator;
use api\validators\Users\UserCanAccessValidator;
use app\framework\DBALConnectionFactory;
use app\framework\DoctrineEntityManagerFactory;
use app\models\actions\entities\ActionEntity;
use app\models\actions\hydrators\ActionHydrator;
use app\models\codefield\hydrators\CodeFieldHydratorFactory;
use app\models\draftLocation\hydrators\DraftLocationHydrator;
use app\models\framework\config\DatixConfig;
use app\models\incidents\services\RelabelIncidentRolesService;
use app\models\language\mappers\LanguageMapperFactory;
use app\models\location\hydrators\LocationHydrator;
use app\models\location\services\TreeFieldService;
use app\models\modules\ModuleEntity;
use app\models\securityGroup\Repository\SecurityGroupRepository;
use app\models\securityGroup\services\SecurityGroupEntityService;
use app\models\service\hydrators\ServiceHydrator;
use app\Repository\RepositoryFactory;
use app\services\action\ActionUserAdaptor;
use app\services\action\ActionUserAdaptorFactory;
use app\services\audit\FullAudit;
use app\services\carlton\api\CarltonAPIServiceFactory;
use app\services\carlton\generic\DateAdapter;
use app\services\codes\CodesService;
use app\services\dashboard\DashboardService;
use app\services\globals\GlobalService;
use app\services\overdue\OverdueServiceFactory;
use app\services\securityGroups\SecurityGroupService;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use GuzzleHttp\Client;
use League\Container\ServiceProvider\AbstractServiceProvider;
use Psr\Log\LoggerInterface;
use Rakit\Validation\Validator;
use src\actiontriggers\model\ActionTriggerModelFactory;
use src\admin\services\factories\JwtServiceFactory;
use src\admin\services\JwtPermissions;
use src\admin\services\MaintenanceModeService;
use src\claims\models\ClaimModelFactory;
use src\contacts\repository\ContactMergeRepository;
use src\contacts\service\ContactIdNumbersService;
use src\contacts\service\ContactMergeService;
use src\email\models\NotificationFactory;
use src\framework\registry\Registry;
use src\framework\session\Session;
use src\generic\services\ExportServiceFactory;
use src\incidents\services\AnonymousReportingService;
use src\incidents\services\AnonymousReportingServiceFactory;
use src\logger\DatixLogger;
use src\profiles\model\profile\ProfileMapper;
use src\system\language\LanguageSession;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;
use src\users\model\UserMapper;
use src\users\model\UserModelFactory;

use function in_array;

final class DependenciesServiceProvider extends AbstractServiceProvider
{
    public function provides(string $id): bool
    {
        return in_array($id, [
            EntityManager::class,
            DoctrineEntityManagerFactory::class,
            ActionHydrator::class,
            ActionUserAdaptor::class,
            ActionUserAdaptorFactory::class,
            ActionsController::class,
            LocationsController::class,
            ServicesController::class,
            DraftLocationHydrator::class,
            DraftLocationService::class,
            DraftLocationsController::class,
            NotificationController::class,
            ContactsMergeController::class,
            ContactMergeService::class,
            ContactIdNumbersService::class,
            MaintenanceModeController::class,
            MaintenanceModeService::class,
            GlobalService::class,
            ContactMergeRepository::class,
            MaintenanceModeMiddleware::class,
            MaintenanceModeApiService::class,
            Connection::class,
            LoggerInterface::class,
            ActionTriggersController::class,
            HealthcheckController::class,
            CodesController::class,
            ClaimsController::class,
            UsersController::class,
            ClearFlaggedForInvestigationAction::class,
            AccessCheckService::class,
            AccessCheckController::class,
            AnonymousReportingService::class,
        ], true);
    }

    public function register(): void
    {
        $container = $this->getContainer();

        $container->addShared(EntityManager::class, function (): EntityManager {
            $factory = $this->getContainer()->get(DoctrineEntityManagerFactory::class);

            return $factory->getInstance();
        });

        $container->addShared(DoctrineEntityManagerFactory::class, static function (): DoctrineEntityManagerFactory {
            return new DoctrineEntityManagerFactory();
        });

        $container->addShared(ActionHydrator::class, function (): ActionHydrator {
            return new ActionHydrator(
                new DateAdapter(),
                $this->getContainer()->get(ActionUserAdaptor::class),
                $this->getContainer()->get(EntityManager::class),
            );
        });

        $container->addShared(ActionUserAdaptor::class, function (): ActionUserAdaptor {
            $factory = $this->getContainer()->get(ActionUserAdaptorFactory::class);

            return $factory->create();
        });

        $container->addShared(ActionUserAdaptorFactory::class, static function (): ActionUserAdaptorFactory {
            return new ActionUserAdaptorFactory();
        });

        $container->addShared(ActionsController::class, function (): ActionsController {
            $entityManager = $this->getContainer()->get(EntityManager::class);

            return new ActionsController(
                $entityManager,
                $this->getContainer()->get(ActionHydrator::class),
                $entityManager->getRepository(ActionEntity::class),
            );
        });

        $container->addShared(LocationsController::class, function (): LocationsController {
            return new LocationsController(
                new LocationHydrator(),
                new LocationsService(
                    $this->getContainer()->get(EntityManager::class),
                    new TreeFieldService(),
                    new LocationHydrator(),
                ),
                new LocationsServicesDataFactory(),
            );
        });

        $container->addShared(ServicesController::class, function (): ServicesController {
            return new ServicesController(
                new ServiceHydrator(),
                new ServicesService(
                    $this->getContainer()->get(EntityManager::class),
                    new TreeFieldService(),
                    new ServiceHydrator(),
                ),
                new LocationsServicesDataFactory(),
            );
        });

        $container->addShared(DraftLocationHydrator::class, function (): DraftLocationHydrator {
            return new DraftLocationHydrator(
                LanguageSessionFactory::getInstance(),
                (new LanguageMapperFactory())->create(),
                new TreeFieldService(),
                $this->getContainer()->get(EntityManager::class),
            );
        });

        $container->addShared(DraftLocationService::class, function (): DraftLocationService {
            /** @var EntityManager $entityManager */
            $entityManager = $this->getContainer()->get(EntityManager::class);

            return new DraftLocationService(
                $this->getContainer()->get(DraftLocationHydrator::class),
                $entityManager,
                new LocationHydrator(),
            );
        });

        $container->addShared(DraftLocationsController::class, function (): DraftLocationsController {
            return new DraftLocationsController(
                $this->getContainer()->get(EntityManager::class),
                $this->getContainer()->get(DraftLocationService::class),
                $this->getContainer()->get(LoggerInterface::class),
            );
        });

        $container->addShared(NotificationController::class, function (): NotificationController {
            return new NotificationController(
                $this->getContainer()->get(DatixConfig::class),
                (new OverdueServiceFactory())->create(),
                (new NotificationFactory())->create(),
                $this->getContainer()->get(RelabelIncidentRolesService::class),
            );
        });

        $container->addShared(ContactsMergeController::class, function (): ContactsMergeController {
            return new ContactsMergeController(
                new ContactMergeValidator(),
                $this->getContainer()->get(ContactMergeService::class),
                $this->getContainer()->get(LanguageSession::class),
            );
        });

        $container->addShared(ContactMergeService::class, function (): ContactMergeService {
            return new ContactMergeService(
                $this->getContainer()->get(Connection::class),
                $this->getContainer()->get(ContactMergeRepository::class),
                $this->getContainer()->get(LoggerInterface::class),
            );
        });

        $container->addShared(MaintenanceModeController::class, function (): MaintenanceModeController {
            return new MaintenanceModeController(
                $this->getContainer()->get(MaintenanceModeService::class),
                new MaintenanceModeValidator(),
            );
        });

        $container->addShared(MaintenanceModeService::class, function (): MaintenanceModeService {
            $permissions = [];
            $request = $this->getContainer()['request'];
            $jwtService = JwtServiceFactory::create();
            if ($request->hasHeader('X-DatixPerms')) {
                $permissions = $jwtService->decode($request->getHeader('X-DatixPerms')[0]);
            }

            return new MaintenanceModeService(
                $this->getContainer()->get(GlobalService::class),
                new JwtPermissions($permissions),
            );
        });

        $container->addShared(GlobalService::class, function (): GlobalService {
            return new GlobalService($this->getContainer()->get(EntityManager::class));
        });

        $container->addShared(ContactMergeRepository::class, function (): ContactMergeRepository {
            return new ContactMergeRepository(
                $this->getContainer()->get(Connection::class),
                $this->getContainer()->get(ContactIdNumbersService::class),
            );
        });

        $container->addShared(MaintenanceModeMiddleware::class, function (): MaintenanceModeMiddleware {
            return new MaintenanceModeMiddleware($this->getContainer()->get(MaintenanceModeApiService::class));
        });

        $container->addShared(MaintenanceModeApiService::class, function (): MaintenanceModeApiService {
            return new MaintenanceModeApiService(
                JwtServiceFactory::create(),
                $this->getContainer()->get(MaintenanceModeService::class),
            );
        });

        $container->addShared(Connection::class, static function (): Connection {
            return (new DBALConnectionFactory())->getInstance();
        });

        $container->addShared(ActionTriggersController::class, static function (): ActionTriggersController {
            $factory = new ActionTriggerModelFactory();

            return new ActionTriggersController($factory->getMapper(), $factory->getCollection());
        });

        $container->addShared(HealthcheckController::class)
            ->addArgument('http_client.carlton')
            ->addArgument(DatixLogger::class)
            ->addArgument(Session::class)
            ->addArgument(EntityManager::class)
            ->setConcrete(
                static function (
                    Client $client,
                    DatixLogger $logger,
                    Session $session,
                    EntityManager $em
                ): HealthcheckController {
                    return new HealthcheckController(
                        $em->getRepository(ModuleEntity::class),
                        CarltonAPIServiceFactory::createNavigationService($client, $logger, $session),
                    );
                },
            );

        $container->addShared(CodesController::class, function (): CodesController {
            return new CodesController(
                (new CodeFieldHydratorFactory())->create(),
                $this->getContainer()->get(CodesService::class),
            );
        });

        $container->addShared(ClaimsController::class, function (): ClaimsController {
            $factory = new ExportServiceFactory();

            return new ClaimsController(
                $factory->create('CLA'),
                $this->getContainer()->get(LoggerInterface::class),
                new Validator(),
                $this->getContainer()->get(Connection::class),
                (new ClaimModelFactory())->getMapper(),
            );
        });

        $container->addShared(UsersController::class, static function (): UsersController {
            return new UsersController(
                (new UserModelFactory())->getMapper(),
                new UserCanAccessValidator(),
            );
        });

        $container->addShared(AccessCheckService::class)
            ->addArgument(UserMapper::class)
            ->addArgument(ProfileMapper::class)
            ->addArgument(ModuleDefs::class)
            ->addArgument(SecurityGroupEntityService::class)
            ->addArgument(SecurityGroupService::class)
            ->addArgument(Registry::class)
            ->addArgument(Connection::class)
            ->addArgument(SecurityGroupRepository::class)
            ->addArgument(DashboardService::class);

        $container->addShared(AccessCheckController::class)
            ->addArgument(AccessCheckService::class);

        $container->addShared(ClearFlaggedForInvestigationAction::class)
            ->addArgument(ClearFlaggedForInvestigationValidator::class)
            ->addArgument(FullAudit::class)
            ->addArgument(RepositoryFactory::class);

        $container->addShared(AnonymousReportingService::class, static function (): AnonymousReportingService {
            return (new AnonymousReportingServiceFactory())->create();
        });
    }
}
