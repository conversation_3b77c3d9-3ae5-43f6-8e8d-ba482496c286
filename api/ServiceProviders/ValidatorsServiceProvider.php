<?php

declare(strict_types=1);

namespace api\ServiceProviders;

use api\V2\Hydrators\Feedback\FeedbackHydratorManager;
use api\V2\Services\EntityService;
use api\V2\Services\FieldDefsService;
use api\V2\Services\RequestService;
use api\V2\Validators\Feedback\FeedbackGetValidator;
use api\V2\Validators\Feedback\FeedbackPatchValidator;
use api\V2\Validators\Incidents\IncidentsGetValidator;
use api\V2\Validators\System\FieldValidatorFactory;
use Laminas\InputFilter\InputFilter;
use League\Container\ServiceProvider\AbstractServiceProvider;
use src\complaints\model\FeedbackMapper;

use function in_array;

final class ValidatorsServiceProvider extends AbstractServiceProvider
{
    public function provides(string $id): bool
    {
        return in_array($id, [
            'FeedbackGetValidator',
            'FeedbackPatchValidator',
            'IncidentsGetValidator',
        ], true);
    }

    public function register(): void
    {
        $container = $this->getContainer();

        // API V2 Validators can't use FQNs as they are resolved from aliased controllers.
        $container->add('FeedbackGetValidator', FeedbackGetValidator::class)
            ->addArgument(InputFilter::class);

        $container->add('FeedbackPatchValidator', FeedbackPatchValidator::class)
            ->addArgument(InputFilter::class)
            ->addArgument(FeedbackHydratorManager::class)
            ->addArgument(FieldValidatorFactory::class)
            ->addArgument(EntityService::class)
            ->addArgument(RequestService::class)
            ->addArgument('FeedbackFieldMapService') // Aliased Class, hence the reason for just using a string.
            ->addArgument(FeedbackMapper::class)
            ->addArgument(FieldDefsService::class);

        // API V2 Validators can't use FQNs as they are resolved from aliased controllers.
        $container->add('IncidentsGetValidator', IncidentsGetValidator::class)
            ->addArgument(InputFilter::class);
    }
}
