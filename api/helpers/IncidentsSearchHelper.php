<?php

declare(strict_types=1);

namespace api\helpers;

use src\users\model\User;

class IncidentsSearchHelper
{
    /** @var string[]|null */
    private ?array $recordIds = null;
    private ?string $reference = null;
    private User $user;

    /**
     * @return string[]|null
     */
    public function getRecordIds(): ?array
    {
        return $this->recordIds;
    }

    /**
     * @param string[]|null $recordIds
     */
    public function setRecordIds(?array $recordIds): void
    {
        $this->recordIds = $recordIds;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    public function setReference(?string $reference): void
    {
        $this->reference = $reference;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): void
    {
        $this->user = $user;
    }
}
