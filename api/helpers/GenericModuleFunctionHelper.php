<?php

namespace api\helpers;

use app\models\notepad\hydrators\NotepadHydratorFactory;
use app\models\progressNote\hydrators\ProgressNoteHydratorFactory;
use app\services\idGenerator\RecordIdGeneratorFactory;
use Psr\Log\LoggerInterface;
use src\logger\DatixLogger;
use src\system\container\facade\Container;

/**
 * Class GenericModuleFunctionHelper.
 */
class GenericModuleFunctionHelper
{
    /** @var string */
    protected $module;

    /** @var string */
    protected $recordid;

    /** @var DatixLogger */
    protected $logger;
    protected RecordIdGeneratorFactory $idGeneratorFactory;

    public function __construct(string $module, int $recordid)
    {
        $this->module = $module;
        $this->recordid = $recordid;
        $this->logger = Container::get(LoggerInterface::class);
        $this->idGeneratorFactory = Container::get(RecordIdGeneratorFactory::class);
    }

    public function hydrateProgressNotes(array $progressNotesData): array
    {
        $progressNoteEntities = [];
        $progressNoteHydrator = (new ProgressNoteHydratorFactory())->create();
        foreach ($progressNotesData as $progressNoteData) {
            $progressNoteData['link_id'] = $this->recordid;
            $progressNoteData['link_module'] = $this->module;
            $progressNoteEntities[] = $progressNoteHydrator->hydrate($progressNoteData);
        }

        return $progressNoteEntities;
    }

    public function hydrateNotepad(array $notepadData): array
    {
        $notepadEntities = [];
        $notepadHydrator = (new NotepadHydratorFactory())->create();
        $notepadRecordidGenerator = $this->idGeneratorFactory->create('notepad');
        foreach ($notepadData as $notepadDatum) {
            $notepadRecordid = $notepadRecordidGenerator->generateRecordId();
            $notepadDatum['recordid'] = $notepadRecordid;
            $idColumn = strtolower($this->module) . '_id';
            $notepadDatum[$idColumn] = $this->recordid;
            $notepadEntities[] = $notepadHydrator->hydrate($notepadDatum);
        }

        return $notepadEntities;
    }
}
