<?php

declare(strict_types=1);

namespace api\helpers;

use Psr\Http\Message\ResponseInterface;
use src\users\model\User;
use src\users\model\UserModelFactory;

class UsersHelper
{
    /** @var int[] */
    private $recordIds;

    /** @var string */
    private $module;

    /** @var User */
    private $user;

    /**
     * @return int[]
     */
    public function getRecordIds(): array
    {
        return $this->recordIds;
    }

    /**
     * @param int[] $recordIds
     */
    public function setRecordIds(array $recordIds): void
    {
        $this->recordIds = $recordIds;
    }

    /**
     * @return string
     */
    public function getModule(): ?string
    {
        return $this->module;
    }

    public function setModule(string $module): void
    {
        $this->module = $module;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): void
    {
        $this->user = $user;
    }

    public static function getEmptyJWTResponse(ResponseInterface $response)
    {
        $response->getBody()->write(json_encode([
            'error' => 'Authentication failure: Decoded JWT is empty.',
        ]));

        return $response->withStatus(401)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * Instance method for static call, unit tests can't work without this.
     */
    public function getUserFromToken(object $token): ?User
    {
        return self::getUserFromJWT($token);
    }

    public static function getUserFromJWT($token): ?User
    {
        if (!$token) {
            return null;
        }

        $userId = $token['userId'];

        if ($userId) {
            $userFactory = new UserModelFactory();
            $user = $userFactory->getMapper()->findById($userId);

            if ($user && $user->initials) {
                $_SESSION['CurrentUser'] = $user;
                $_SESSION['logged_in'] = true;

                return $user;
            }
        }

        return null;
    }
}
