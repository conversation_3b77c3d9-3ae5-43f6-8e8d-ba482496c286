<?php

declare(strict_types=1);

namespace api\validators\Contacts;

use api\validators\RequestValidatorInterface;
use Psr\Http\Message\ServerRequestInterface as Request;

use function is_array;
use function count;

use const SORT_NUMERIC;

/**
 * @deprecated use ValidatorInterface for new Validators
 */
class ContactMergeValidator implements RequestValidatorInterface
{
    public function validate(Request $request): bool
    {
        $body = $request->getParsedBody();

        if ($body === null) {
            return false;
        }

        if (!isset($body['contacts'])) {
            return false;
        }

        if (!is_array($body['contacts'])) {
            return false;
        }

        foreach ($body['contacts'] as $contactId => $contacts) {
            if (!$this->validContactId($contactId)) {
                return false;
            }

            if (!is_array($contacts)) {
                return false;
            }

            foreach ($contacts as $duplicateContactId) {
                if (!$this->validContactId($duplicateContactId)) {
                    return false;
                }
            }
        }

        if ($this->requestContainsDuplicateContactIds($body)) {
            return false;
        }

        return true;
    }

    /**
     * @param mixed|int|string $contactId
     */
    private function validContactId($contactId): bool
    {
        return is_numeric($contactId) && $contactId > 0;
    }

    private function requestContainsDuplicateContactIds(array $body): bool
    {
        $leadIds = array_keys($body['contacts']);

        $contactIdsInBody = array_merge($leadIds, ...$body['contacts']);
        $uniqueContactIds = array_unique($contactIdsInBody, SORT_NUMERIC);

        return count($contactIdsInBody) !== count($uniqueContactIds);
    }
}
