<?php

namespace api\validators;

use Rakit\Validation\Validator;
use Source\classes\Exceptions\InvalidRequestParameterException;

/**
 * @deprecated use ValidatorInterface for new Validators
 */
class ClearFlaggedForInvestigationValidator
{
    private const VALIDATION_RULES = [
        'recordId' => 'numeric|min:1|required',
        'moduleCode' => 'required|in:INC,MOR,COM,CLA',
    ];
    private const VALIDATION_MESSAGES = [
        'numeric' => ':attribute is not a numeric value',
        'min' => ':attribute has a min value of :min',
        'string' => ':attribute is not a string value',
    ];
    private Validator $validator;

    public function __construct(Validator $validator)
    {
        $this->validator = $validator;
    }

    public function validateData(array $data): void
    {
        $this->validator->setMessages(self::VALIDATION_MESSAGES);

        $validation = $this->validator->validate($data, self::VALIDATION_RULES);

        if ($validation->fails()) {
            throw new InvalidRequestParameterException('Validation error occurred.', $validation->errors()->toArray());
        }
    }
}
