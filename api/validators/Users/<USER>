<?php

namespace api\validators\Users;

use api\validators\RequestValidatorInterface;
use app\models\generic\valueObjects\Module;
use Psr\Http\Message\ServerRequestInterface as Request;
use Throwable;

use function is_array;

/**
 * @deprecated use ValidatorInterface for new Validators
 */
class UserCanAccessValidator implements RequestValidatorInterface
{
    public function validate(Request $request): bool
    {
        $body = $request->getParsedBody();

        if (empty($body)) {
            return false;
        }

        foreach ($body as $moduleKey => $data) {
            if (!$this->isValidModule($moduleKey)) {
                return false;
            }

            if (!is_array($data) || empty($data)) {
                return false;
            }
        }

        return true;
    }

    private function isValidModule($moduleKey): bool
    {
        try {
            new Module($moduleKey);
        } catch (Throwable $exception) {
            return false;
        }

        return true;
    }
}
