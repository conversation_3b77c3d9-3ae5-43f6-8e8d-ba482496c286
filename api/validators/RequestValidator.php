<?php

declare(strict_types=1);

namespace api\validators;

use api\interfaces\validator\ValidatorInterface;
use <PERSON><PERSON>\InputFilter\Input;
use <PERSON>inas\InputFilter\InputFilter;
use <PERSON><PERSON>\Validator;
use Psr\Http\Message\ServerRequestInterface as Request;

abstract class RequestValidator implements ValidatorInterface
{
    protected InputFilter $filter;

    public function __construct(InputFilter $filter)
    {
        $this->filter = $filter;
    }

    public function validate(Request $request): bool
    {
        $this->filter->setData($request->getParams());

        return $this->filter->isValid();
    }

    /**
     * @return string[]
     */
    public function getMessages(): array
    {
        return $this->filter->getMessages();
    }

    protected function applyConfig(array $configuration): void
    {
        foreach ($configuration as $paramName => $paramConfig) {
            $this->filter->add($paramConfig, $paramName);
        }
    }

    protected function addExceptedUdfParamValidation(InputFilter $inputFilter, array $inputs): void
    {
        $validator = new Validator\Digits();

        foreach ($inputs as $value) {
            if (!$validator->isValid($value)) {
                $input = new Input($value);
                $input->setErrorMessage('Udf fields must be numerical');
                $input->getValidatorChain()->attach($validator);
                $inputFilter->add($input);
            }
        }
    }

    protected function addExceptedParamValidation(InputFilter $inputFilter, array $inputs, array $allowedList): void
    {
        $validator = new Validator\InArray(['haystack' => array_keys($allowedList)]);

        foreach ($inputs as $parameter => $value) {
            if ($this->isParameterUdf($parameter)) {
                continue;
            }

            if (!$validator->isValid($parameter)) {
                $input = new Input($parameter);
                $input->setErrorMessage('Invalid parameters sent in the request');
                $input->getValidatorChain()->attach($validator);
                $inputFilter->add($input);
            }
        }
    }

    protected function resetWhenValid(array $inputNamesToReset): bool
    {
        if ($this->filter->isValid()) {
            // Only allow resetting the filter validation chain after validated successfully.
            // this will allow error messages to be retained and returned when isValid is false.
            foreach ($inputNamesToReset as $inputName) {
                $this->filter->remove($inputName);
            }
            $this->filter->setData([]);

            return true;
        }

        return false;
    }

    private function isParameterUdf(string $parameter): bool
    {
        $udfParts = explode('UDF_', $parameter);
        $udfId = $udfParts[1] ?? null;

        if ($udfId === null) {
            return false;
        }

        return true;
    }
}
