<?php

declare(strict_types=1);

namespace api\validators\MaintenanceMode;

use api\validators\RequestValidatorInterface;
use Psr\Http\Message\ServerRequestInterface as Request;
use RuntimeException;

use function is_bool;

/**
 * @deprecated use ValidatorInterface for new Validators
 */
class MaintenanceModeValidator implements RequestValidatorInterface
{
    public function validate(Request $request): bool
    {
        try {
            $body = $request->getParsedBody();
        } catch (RuntimeException $e) {
            return false;
        }

        if ($body === null) {
            return false;
        }

        if (!isset($body['isMaintenanceModeEnabled'])) {
            return false;
        }

        if (!is_bool($body['isMaintenanceModeEnabled'])) {
            return false;
        }

        return true;
    }
}
