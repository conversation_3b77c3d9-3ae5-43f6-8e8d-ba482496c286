<?php

declare(strict_types=1);

namespace api\controllers\SystemConfiguration;

use app\services\systemConfiguration\SystemConfigurationService;
use Doctrine\DBAL\Exception;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Source\classes\Exceptions\SystemConfigurationException;
use src\system\configuration\DTO\SystemConfigurationDTO;
use src\system\configuration\hydrator\SystemConfigurationHydrator;
use Teapot\StatusCode\Http;

class SystemConfigurationController
{
    private SystemConfigurationService $systemConfigurationService;
    private SystemConfigurationHydrator $systemConfigurationHydrator;

    public function __construct(
        SystemConfigurationService $systemConfigurationService,
        SystemConfigurationHydrator $systemConfigurationHydrator
    ) {
        $this->systemConfigurationService = $systemConfigurationService;
        $this->systemConfigurationHydrator = $systemConfigurationHydrator;
    }

    /**
     * @throws SystemConfigurationException
     * @throws Exception
     *
     * @used-by system_configuration/setSystemConfigurationSync
     */
    public function setSystemConfigurationSync(Request $request, Response $response): Response
    {
        $data = $request->getParsedBody();
        $data = $this->systemConfigurationService->convertLanguages($data);

        $configuration = $this->systemConfigurationHydrator->hydrate($data);

        $this->systemConfigurationService->setSystemConfigurationSyncAndCache(
            SystemConfigurationDTO::FILE_UPLOAD_LIMIT_MAX,
            (string) $configuration->getMaxFileUploadLimit(),
        );
        $this->systemConfigurationService->setSystemConfigurationSyncAndCache(
            SystemConfigurationDTO::FOOTER_ENABLE,
            (string) (int) $configuration->getFooterEnabled(),
        );
        $this->systemConfigurationService->setSystemConfigurationSyncAndCache(
            SystemConfigurationDTO::FOOTER_TEXT,
            $configuration->getFooterText(),
        );
        $this->systemConfigurationService->setSystemConfigurationSyncAndCache(
            SystemConfigurationDTO::AVAILABLE_LANGUAGES,
            implode(' ', $configuration->getAvailableLanguages()),
        );
        $this->systemConfigurationService->setSystemConfigurationSyncAndCache(
            SystemConfigurationDTO::SYSTEM_LANGUAGE,
            (string) $configuration->getSystemLanguage(),
        );

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
