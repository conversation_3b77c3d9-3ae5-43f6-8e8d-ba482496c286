<?php

namespace api\controllers;

use api\enums\RelabelDirection;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use app\models\incidents\services\RelabelIncidentRolesService;
use app\models\language\mappers\LanguageMapperFactory;
use app\services\overdue\OverdueService;
use Doctrine\DBAL\Exception as DBALException;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use RuntimeException;
use src\email\controllers\OverdueEmailControllerFactory;
use src\email\models\Notification;
use src\email\service\NotificationServiceFactory;
use src\framework\query\exceptions\InvalidFieldException;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\system\language\LanguageServiceFactory;
use src\system\language\LanguageSession;
use src\system\language\LanguageSessionFactory;
use Teapot\StatusCode\Http;
use Throwable;

use function count;

/**
 * @OA\Schema(
 *     schema="Notification",
 *     type="object",
 *     @OA\Property(property="moduleKey", type="string"),
 *     @OA\Property(property="templateTypeKey", type="string"),
 *     @OA\Property(property="locationId", type="integer", nullable=true),
 *     @OA\Property(
 *         property="variables",
 *         type="array",
 *         @OA\Items(
 *             @OA\AdditionalProperties(
 *                 type="string",
 *                 oneOf={
 *                     @OA\Schema(@OA\AdditionalProperties(type="string")),
 *                     @OA\Schema(type="string"),
 *                 },
 *             ),
 *             @OA\Property(property="link", type="string"),
 *         ),
 *     ),
 *     @OA\Property(
 *         property="attachmentUrls",
 *         type="array",
 *         @OA\Items(
 *             @OA\Property(property="url", type="string"),
 *             @OA\Property(property="filename", type="string"),
 *         ),
 *     ),
 *     @OA\Property(
 *         property="recipients",
 *         type="array",
 *         @OA\Items(@OA\AdditionalProperties(type="string")),
 *     ),
 *     @OA\Property(
 *         property="records",
 *         type="array",
 *         @OA\Items(
 *             @OA\AdditionalProperties(
 *                 type="string",
 *                 oneOf={
 *                     @OA\Schema(type="string"),
 *                     @OA\Schema(@OA\AdditionalProperties(type="string")),
 *                 },
 *             ),
 *             @OA\Property(property="link", type="string"),
 *         ),
 *     ),
 * ),
 * @OA\Schema(
 *     schema="OverdueNotification",
 *     type="object",
 *     allOf={
 *         @OA\Schema(ref="#/components/schemas/Notification"),
 *         @OA\Schema(
 *             @OA\Property(
 *                 property="records",
 *                 type="array",
 *                 @OA\Items(
 *                     @OA\AdditionalProperties(
 *                         type="string",
 *                         oneOf={
 *                             @OA\Schema(type="string"),
 *                             @OA\Schema(@OA\AdditionalProperties(type="string")),
 *                         },
 *                     ),
 *                     @OA\Property(property="link", type="string"),
 *                     @OA\Property(property="recordLink", type="string"),
 *                 ),
 *             ),
 *         ),
 *     },
 * ),
 */
class NotificationController
{
    /** @var OverdueService */
    private $overdueService;

    /** @var Notification */
    private $notification;

    public function __construct(
        DatixConfig $datixConfig,
        OverdueService $overdueService,
        Notification $notification,
        private RelabelIncidentRolesService $relabelIncidentRolesService
    ) {
        global $scripturl;
        $this->notification = $notification;
        $this->overdueService = $overdueService;

        $scripturl = $datixConfig->getScriptUrl();
    }

    /**
     * @OA\Get(
     *     path="/notifications/{module}/status",
     *     @OA\PathParameter(name="module", @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Overdue statuses returned",
     *         ref="#/components/responses/OverdueNotificationResponse",
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     * @OA\Get(
     *     path="/notifications/{module}/status/{language}",
     *     @OA\PathParameter(name="module", @OA\Schema(type="string")),
     *     @OA\PathParameter(name="language", @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Overdue statuses returned",
     *         ref="#/components/responses/OverdueNotificationResponse",
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws DBALException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getStatus(Request $request, Response $response, array $args): Response
    {
        $module = $args['module'] ?? Module::INCIDENTS;
        $languageCode = $args['language'] ?? LanguageSession::DEFAULT_LOCALE;
        $languageMapper = (new LanguageMapperFactory())->create();
        $languageId = $languageMapper->getLanguageFromLocale($languageCode);
        $language = (int) $languageId['id'] ?: LanguageSession::ENGLISH_UK;
        $status = (new OverdueEmailControllerFactory())->create()->getOverdueStatuses($module, $language);
        $notificationsJson = $this->formatResponse($status);

        $response->getBody()->write($notificationsJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Get(
     *     path="/notifications/{module}/roles",
     *     @OA\PathParameter(name="module", @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Module role returned",
     *         ref="#/components/responses/NotificationModuleRolesResponse",
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     * @OA\Get(
     *     path="/notifications/{module}/roles/{language}",
     *     @OA\PathParameter(name="module", @OA\Schema(type="string")),
     *     @OA\PathParameter(name="language", @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Module role returned",
     *         ref="#/components/responses/NotificationModuleRolesResponse",
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws DBALException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getRoles(Request $request, Response $response, array $args): Response
    {
        $module = $args['module'] ?? Module::INCIDENTS;
        $languageCode = $args['language'] ?? LanguageSession::DEFAULT_LOCALE;
        $languageMapper = (new LanguageMapperFactory())->create();
        $languageId = $languageMapper->getLanguageFromLocale($languageCode);
        $this->setSystemLanguage($languageId['id'] ?: LanguageSession::ENGLISH_UK);

        $roles = (new OverdueEmailControllerFactory())->create()->getModuleRole($module);
        $notificationsJson = $this->formatResponse($roles);

        $response->getBody()->write($notificationsJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Get(
     *     path="/incidents/notifications/overdue",
     *     @OA\Parameter(in="query", name="status", required=false, @OA\Schema(type="array", @OA\Items(type="string"))),
     *     @OA\Parameter(in="query", name="role", required=false, @OA\Schema(type="array", @OA\Items(type="string", enum={"MGR", "HDLR", "INV", "REV"}))),
     *     @OA\Response(
     *         response="200",
     *         description="Overdue incidents returned",
     *         @OA\MediaType(
     *             mediaType="application/ld+json",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="_embedded",
     *                     type="object",
     *                     @OA\Property(
     *                         property="notifications",
     *                         type="array",
     *                         @OA\Items(ref="#/components/schemas/OverdueNotification"),
     *                     ),
     *                 ),
     *                 @OA\Property(property="page", type="integer"),
     *                 @OA\Property(property="page_count", type="integer"),
     *                 @OA\Property(property="total_items", type="integer"),
     *                 @OA\Property(property="page_size", type="integer"),
     *             ),
     *         ),
     *     ),
     * ),
     * @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws DBALException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getOverdueIncidentsRecords(Request $request, Response $response): Response
    {
        /** @var string[] $overdueStatuses */
        $overdueStatuses = $request->getQueryParams()['status'] ?? array_keys($this->getOverdueStatus(Module::INCIDENTS));

        /** @var string[] $roles */
        $roles = $request->getQueryParams()['role'] ?? [Notification::USER_MANAGER];

        $notifications['notifications'] = (new OverdueEmailControllerFactory())->create()
            ->sendOverDueEmails(Module::INCIDENTS, $overdueStatuses, $roles);

        $notificationsJson = $this->formatResponse($notifications);

        $response->getBody()->write($notificationsJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Get(
     *     path="/feedback/notifications/overdue",
     *     @OA\Parameter(in="query", name="status", required=false, @OA\Schema(type="array", @OA\Items(type="string"))),
     *     @OA\Parameter(in="query", name="role", required=false, @OA\Schema(type="array", @OA\Items(type="string", enum={"MGR", "HDLR", "INV", "REV"}))),
     *     @OA\Response(
     *         response="200",
     *         description="Overdue feedback records returned",
     *         @OA\MediaType(
     *             mediaType="application/ld+json",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="_embedded",
     *                     type="object",
     *                     @OA\Property(
     *                         property="notifications",
     *                         type="array",
     *                         @OA\Items(ref="#/components/schemas/OverdueNotification"),
     *                     ),
     *                 ),
     *                 @OA\Property(property="page", type="integer"),
     *                 @OA\Property(property="page_count", type="integer"),
     *                 @OA\Property(property="total_items", type="integer"),
     *                 @OA\Property(property="page_size", type="integer"),
     *             ),
     *         ),
     *     ),
     * )
     *
     * @throws DBALException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getOverdueFeedbackRecords(Request $request, Response $response): Response
    {
        /** @var string[] $overdueStatuses */
        $overdueStatuses = $request->getQueryParams()['status'] ?? array_keys($this->getOverdueStatus(Module::FEEDBACK));

        /** @var string[] $roles */
        $roles = $request->getQueryParams()['role'] ?? [Notification::USER_MANAGER];

        $notifications['notifications'] = (new OverdueEmailControllerFactory())->create()
            ->sendOverDueEmails(Module::FEEDBACK, $overdueStatuses, $roles);

        $notificationsJson = $this->formatResponse($notifications);

        $response->getBody()->write($notificationsJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Get(
     *     path="/mortality/notifications/overdue",
     *     @OA\Parameter(in="query", name="status", required=false, @OA\Schema(type="array", @OA\Items(type="string"))),
     *     @OA\Parameter(in="query", name="role", required=false, @OA\Schema(type="array", @OA\Items(type="string", enum={"MGR", "HDLR", "INV", "REV"}))),
     *     @OA\Response(
     *         response="200",
     *         description="Overdue feedback records returned",
     *         @OA\MediaType(
     *             mediaType="application/ld+json",
     *             @OA\Schema(
     *                 @OA\Property(
     *                     property="_embedded",
     *                     type="object",
     *                     @OA\Property(
     *                         property="notifications",
     *                         type="array",
     *                         @OA\Items(ref="#/components/schemas/OverdueNotification"),
     *                     ),
     *                 ),
     *                 @OA\Property(property="page", type="integer"),
     *                 @OA\Property(property="page_count", type="integer"),
     *                 @OA\Property(property="total_items", type="integer"),
     *                 @OA\Property(property="page_size", type="integer"),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws DBALException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getOverdueMortalityRecords(Request $request, Response $response): Response
    {
        $notifications = [];

        /** @var string[] $overdueStatuses */
        $overdueStatuses = $request->getQueryParams()['status'] ?? array_keys($this->getOverdueStatus(Module::MORTALITY_REVIEW));

        /** @var string[] $roles */
        $roles = $request->getQueryParams()['role'] ?? [Notification::USER_HANDLER];

        $notifications['notifications'] = (new OverdueEmailControllerFactory())->create()
            ->sendOverDueEmails(Module::MORTALITY_REVIEW, $overdueStatuses, $roles);

        $notificationsJson = $this->formatResponse($notifications);

        $response->getBody()->write($notificationsJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Get(
     *     path="/notifications/fields/labels/{language}",
     *     @OA\PathParameter(name="language", required=true, @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Field labels retruned",
     *         @OA\MediaType(
     *             mediaType="application/ld+json",
     *             @OA\Schema(@OA\AdditionalProperties(type="string")),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @param array<string,string> $args
     *
     * @throws DBALException
     * @throws InvalidFieldException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getFieldLabelsFromFieldName(Request $request, Response $response, array $args): Response
    {
        $languageCode = $args['language'] ?? LanguageSession::DEFAULT_LOCALE;
        $languageMapper = (new LanguageMapperFactory())->create();
        $languageId = $languageMapper->getLanguageFromLocale($languageCode);

        $this->setSystemLanguage($languageId['id'] ?: LanguageSession::ENGLISH_UK);

        $notificationService = (new NotificationServiceFactory())->create();
        $languageService = (new LanguageServiceFactory())->create();
        $fieldLabels = [];

        $variables = $this->notification->getAllVariableKeys();
        foreach ($variables as $module => $fields) {
            $domain = $languageService->getModuleDomain($module);
            $fieldLabels =
                array_reduce(
                    array_keys($fields),
                    static function ($fieldLabels, $fieldKey) use (
                        $notificationService,
                        $variables,
                        $module,
                        $domain
                    ) {
                        $label = $notificationService->getLabelFromField($fieldKey, $module, $domain);
                        $fieldLabels[Notification::MODULE_KEYS[$module] . '.' . $variables[$module][$fieldKey]] = $label;

                        return $fieldLabels;
                    },
                    $fieldLabels,
                ) ?? [];

            $fieldLabels[Notification::MODULE_KEYS[$module] . '.link'] = _fdtk('link');
            $fieldLabels[Notification::MODULE_KEYS[$module] . '.fullName'] = _fdtk('fullname');
            $fieldLabels[Notification::MODULE_KEYS[$module] . '.contactForenames'] = _fdtk('con_forenames');
            $fieldLabels[Notification::MODULE_KEYS[$module] . '.contactSurname'] = _fdtk('con_surname');
            $fieldLabels[Notification::MODULE_KEYS[$module] . '.summary'] = _fdtk('table_' . strtolower($module) . '_links_summary');
        }

        $response->getBody()->write(json_encode($fieldLabels));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/ld+json');
    }

    /**
     * @OA\Post(
     *     path="/notifications/roles/relabel",
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"direction"},
     *             @OA\Property(property="direction", type="string", enum={"incidents_to_events", "events_to_incidents"})
     *         )
     *     ),
     *     @OA\Response(response="200", description="Roles relabeled"),
     *     @OA\Response(response="400", description="Invalid direction"),
     * )
     */
    public function relabelIncidentRoleDescriptions(Request $request, Response $response): Response
    {
        $body = $request->getParsedBody();
        $direction = RelabelDirection::tryFrom($body['direction']);

        if ($direction === null) {
            return $response->withStatus(Http::BAD_REQUEST);
        }

        try {
            $this->relabelIncidentRolesService->relabelIncidentRoleDescriptions($direction);
        } catch (DBALException|Throwable $e) {
            Log::error($e->getMessage(), ['exception' => $e]);

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR);
        }

        $response->getBody()->write(json_encode(['status' => 'success']));

        return $response->withHeader('Content-Type', 'application/json');
    }

    private function getOverdueStatus(string $module): array
    {
        return $this->overdueService->getOverdueStatuses($module, true);
    }

    private function formatResponse(array $data): string
    {
        $json = (string) json_encode(
            [
                '_embedded' => $data,
                'page' => 1,
                'page_count' => 1,
                'total_items' => count($data),
                'page_size' => count($data),
            ],
        );

        if (json_last_error()) {
            throw new RuntimeException(json_last_error_msg());
        }

        return $json;
    }

    /**
     * recreates singletons to make sure any services we use after this function use the correct system language.
     */
    private function setSystemLanguage(int $languageId): void
    {
        $_SESSION['language'][LanguageSession::USER]['id'] = $languageId ?: LanguageSession::ENGLISH_UK;
        LanguageSessionFactory::getInstance(true);
        Registry::getInstance(true);
    }
}
