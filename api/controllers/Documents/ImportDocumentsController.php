<?php

declare(strict_types=1);

namespace api\controllers\Documents;

use app\models\document\valueObjects\UploadedFile;
use app\models\generic\valueObjects\JSONData;
use app\services\document\storagehandlers\FileStorageInterface;
use Doctrine\DBAL\Connection;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\logger\Facade\Log;
use Teapot\StatusCode\Http;
use Throwable;

use function in_array;
use function array_key_exists;

class ImportDocumentsController
{
    private Connection $db;
    private FileStorageInterface $storage;

    public function __construct(Connection $db, FileStorageInterface $storage)
    {
        $this->db = $db;
        $this->storage = $storage;
    }

    /**
     * @OA\Put(
     *     path="/documents/import/{id}",
     *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="file", type="string"),
     *             @OA\Property(
     *                 property="doc",
     *                 type="object",
     *                 @OA\Property(property="mime_type", type="string"),
     *                 @OA\Property(property="filename", type="string"),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Document Template updated successfully",
     *         @OA\JsonContent(type="object", @OA\Property(property="success", type="string")),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal server error",
     *         @OA\JsonContent(type="object", @OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Document template not found",
     *         @OA\JsonContent(type="object", @OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Failed validation",
     *         @OA\JsonContent(type="object", @OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function updateDocumentTemplate(Request $request, Response $response): Response
    {
        $importId = (int) $request->getAttribute('id');
        $data = (new JSONData($request->getBody()))->toArray();
        $templateQuery = 'SELECT * FROM templates_main WHERE import_id = :importId';
        $documentTemplate = $this->db->fetchAssociative($templateQuery, [':importId' => $importId]);

        if (!$documentTemplate) {
            $response->getBody()->write(json_encode([
                'error' => 'Invalid template id, Failed to update Document Template',
            ]));

            return $response->withStatus(Http::NOT_FOUND)
                ->withHeader('Content-Type', 'application/json');
        }

        if (isset($data['file'], $data['doc'])) {
            $uploadedFile = UploadedFile::createFromBase64String($data['file'], $data['doc']['mime_type']);
            if (in_array($uploadedFile->getMimeType(), UploadedFile::BANNED_MEDIA_TYPES, true)) {
                $response->getBody()->write(json_encode(['error' => _fdtk('banned_media_type')]));

                return $response->withStatus(Http::BAD_REQUEST)
                    ->withHeader('Content-Type', 'application/json');
            }

            $fileStatus = $this->storage->putObject($data['doc']['filename'], base64_decode($data['file']));
            if (!$fileStatus) {
                $response->getBody()->write(json_encode(['error' => 'File upload Failed']));

                return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                    ->withHeader('Content-Type', 'application/json');
            }
        }

        $documentId = $documentTemplate['document_id'];
        if (!$this->updateTemplate($importId, $documentId, $data)) {
            $response->getBody()->write(json_encode([
                'error' => 'Failed to update Document Template',
            ]));

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode([
            'success' => 'Document Template updated successfully',
        ]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Delete(
     *     path="/documents/import/{id}",
     *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response="404",
     *         description="Document template not found",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal server error",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(response="204", description="Document template deleted"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function deleteDocumentTemplate(Request $request, Response $response): Response
    {
        $importId = (int) $request->getAttribute('id');

        $templateQuery = 'SELECT * FROM templates_main WHERE import_id = :importId';
        $documentTemplate = $this->db->fetchAssociative($templateQuery, [':importId' => $importId]);
        if (!$documentTemplate) {
            $response->getBody()->write(json_encode([
                'error' => 'Invalid template id, Failed to delete Document Template',
            ]));

            return $response->withStatus(Http::NOT_FOUND)
                ->withHeader('Content-Type', 'application/json');
        }

        $recordId = $documentTemplate['recordid'];

        $documentQuery = 'SELECT * FROM documents WHERE id = :document_id';
        $document = $this->db->fetchAssociative($documentQuery, [':document_id' => $documentTemplate['document_id']]);
        if ($document) {
            $documentFile = file_get_contents($this->storage->getDownloadURL($document['filename']));
            $this->storage->delete($document['filename']);
        }

        $this->db->beginTransaction();

        try {
            $this->db->delete('documents', [
                'id' => $documentTemplate['document_id'],
            ]);

            $this->db->delete('sec_group_record_permissions', [
                'link_id' => $recordId,
                'module' => 'TEM',
                'tablename' => 'templates_main',
            ]);

            $this->db->delete('templates_main', [
                'recordid' => $recordId,
            ]);

            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollBack();
            if ($document) {
                $this->storage->putObject($document['filename'], $documentFile);
            }

            Log::critical('Error while deleting document template', [
                'importId' => $importId,
                'recordId' => $recordId,
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode([
                'error' => 'Failed to delete Document Template',
            ]));

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        return $response->withStatus(Http::NO_CONTENT);
    }

    /**
     * @OA\Post(
     *     path="/documents/import",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="file", type="string"),
     *             @OA\Property(
     *                 property="doc",
     *                 type="object",
     *                 @OA\Property(property="mime_type", type="string"),
     *                 @OA\Property(property="filename", type="string"),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="200", description="Document template created", @OA\JsonContent(type="string")),
     *     @OA\Response(response="500", description="Internal server error or banned media type", @OA\JsonContent(type="string")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function createDocumentTemplate(Request $request, Response $response): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();
            $error = false;
            $uploadedFile = UploadedFile::createFromBase64String($data['file'], $data['doc']['mime_type']);
            if (in_array($uploadedFile->getMimeType(), UploadedFile::BANNED_MEDIA_TYPES, true)) {
                $importStatus = _fdtk('banned_media_type');
                $responseCode = Http::INTERNAL_SERVER_ERROR;
                $error = true;
            }

            if (!$error) {
                $fileStatus = $this->storage->putObject($data['doc']['filename'], base64_decode($data['file']));
                if (!$fileStatus) {
                    $importStatus = 'File upload Failed';
                    $responseCode = Http::INTERNAL_SERVER_ERROR;
                    $error = true;
                }
            }

            if (!$error) {
                unset($data['doc']['id']);
                $this->db->insert('documents', $data['doc']);
                $documentId = $this->db->lastInsertId();
                $data['template']['document_id'] = $documentId;
                unset($data['template']['recordid']);
                $this->db->insert('templates_main', $data['template']);
                $documentTemplateId = $this->db->lastInsertId();
                if (array_key_exists('secGrp', $data) && $data['secGrp']) {
                    unset($data['secGrp']['recordid']);
                    $data['secGrp']['link_id'] = $documentTemplateId;
                    $this->db->insert('sec_group_record_permissions', $data['secGrp']);
                }
                $responseCode = Http::OK;
                $importStatus = 'Document Template Uploaded Successfully';
            }
        } catch (Throwable $e) {
            Log::critical('Error found during documents import', [
                'exception' => $e,
            ]);

            $importStatus = 'Failed to Import Document Template';
            $responseCode = Http::INTERNAL_SERVER_ERROR;
        }

        $response->getBody()->write(json_encode($importStatus));

        return $response->withStatus($responseCode)
            ->withHeader('Content-Type', 'application/json');
    }

    private function updateTemplate($importId, $documentId, $data)
    {
        $this->db->beginTransaction();

        try {
            if ($data['doc']) {
                unset($data['doc']['id']);
                $this->db->update('documents', $data['doc'], [
                    'id' => $documentId,
                ]);
            }

            if ($data['template']) {
                unset($data['template']['recordid']);
                $data['template']['document_id'] = $documentId;
                $this->db->update('templates_main', $data['template'], [
                    'import_id' => $importId,
                ]);
            }

            if ($data['secGrp']) {
                $secGroupId = $data['secGrp']['recordid'];
                unset($data['secGrp']['recordid']);
                $this->db->update('sec_group_record_permissions', $data['secGrp'], [
                    'recordid' => $secGroupId,
                ]);
            }

            $this->db->commit();
        } catch (Throwable $e) {
            $this->db->rollBack();

            Log::critical('Error while updating template', [
                'exception' => $e,
                'importId' => $importId,
                'documentId' => $documentId,
            ]);

            return false;
        }

        return true;
    }
}
