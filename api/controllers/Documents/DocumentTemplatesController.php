<?php

declare(strict_types=1);

namespace api\controllers\Documents;

use app\services\document\storagehandlers\FileStorageInterface;
use Doctrine\DBAL\Connection;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\wordmergetemplate\model\WordMergeTemplateModelFactory;
use Teapot\StatusCode\Http;

class DocumentTemplatesController
{
    private WordMergeTemplateModelFactory $factory;
    private Connection $db;
    private FileStorageInterface $storage;

    public function __construct(
        WordMergeTemplateModelFactory $factory,
        Connection $db,
        FileStorageInterface $storage
    ) {
        $this->factory = $factory;
        $this->db = $db;
        $this->storage = $storage;
    }

    /**
     * @OA\Get(
     *     path="/documents/doctemplates",
     *     @OA\Response(response="204", description="No document templates found"),
     *     @OA\Response(
     *         response="200",
     *         description="Document templates found",
     *         @OA\JsonContent(type="array", minItems=1, @OA\Items(type="integer", format="int32", minimum=1)),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getTemplates(Request $request, Response $response): Response
    {
        $query = $this->factory->getQueryFactory()->getQuery();
        $docTemplates = $this->factory->getCollection();
        $where = $this->factory->getQueryFactory()->getWhere();

        $query->where($where);
        $query->overrideSecurity();
        $docTemplates->setQuery($query);
        $documentTemplateIds = [];

        foreach ($docTemplates as $docTemplate) {
            $documentTemplateIds[] = $docTemplate->recordid;
        }

        $returnCode = !empty($documentTemplateIds) ? Http::OK : Http::NO_CONTENT;

        $response->getBody()->write(json_encode($documentTemplateIds));

        return $response->withStatus($returnCode)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Get(
     *     path="/documents/export/doctemplate/{id}",
     *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response="204",
     *         description="Document template or document not found",
     *         @OA\JsonContent(
     *             required={"template", "file"},
     *             @OA\Property(property="secGrp", type="object"),
     *             @OA\Property(property="template", type="object"),
     *             @OA\Property(property="file", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Document template and document found",
     *         @OA\JsonContent(
     *             required={"doc", "template", "file"},
     *             @OA\Property(property="doc", ref="#/components/schemas/DocumentEntity"),
     *             @OA\Property(property="secGrp", type="object"),
     *             @OA\Property(property="template", type="object"),
     *             @OA\Property(property="file", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getExportedTemplates(Request $request, Response $response): Response
    {
        $recordId = (int) $request->getAttribute('id');

        $documentData = [];
        $queryString = "
            SELECT *
            FROM templates_main
            WHERE recordid = '{$recordId}'";
        $documentTemplate = $this->db->fetchAssociative($queryString);

        $returnCode = !empty($documentTemplate) ? Http::OK : Http::NO_CONTENT;
        if ($documentTemplate) {
            $queryString1 = "
            SELECT *
            FROM documents
            WHERE id = '{$documentTemplate['document_id']}'";
            $document = $this->db->fetchAssociative($queryString1);
            $returnCode = Http::NO_CONTENT;
            if ($document) {
                $documentData['doc'] = $document;
                $returnCode = Http::OK;
            }
            $queryString2 = "
            SELECT *
            FROM sec_group_record_permissions
            WHERE link_id = '{$recordId}'
            AND module = 'TEM' AND tablename = 'templates_main'";
            $securityGroup = $this->db->fetchAssociative($queryString2);
            if (!empty($securityGroup)) {
                $documentData['secGrp'] = $securityGroup;
            }
            $documentData['template'] = $documentTemplate;

            $file = file_get_contents($this->storage->getDownloadURL($document['filename']));
            $documentData['file'] = base64_encode($file);
        }

        $response->getBody()->write(json_encode($documentData));

        return $response->withStatus($returnCode)
            ->withHeader('Content-Type', 'application/json');
    }
}
