<?php

declare(strict_types=1);

namespace api\controllers\Auth;

use api\service\Auth\TokenBuilder;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

use const JSON_THROW_ON_ERROR;
use const JSON_UNESCAPED_SLASHES;
use const JSON_PRETTY_PRINT;

class TokenController
{
    private TokenBuilder $builder;

    public function __construct(TokenBuilder $builder)
    {
        $this->builder = $builder;
    }

    /**
     * @OA\Post(
     *     path="/token",
     *     description="authenticate with api",
     *     security={{"basicAuth": {}}},
     *     @OA\Response(
     *         response="201",
     *         description="Gets bearer token",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="token", type="string"),
     *         ),
     *     )
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function token(Request $request, Response $response): Response
    {
        $data = [
            'status' => 'ok',
            'token' => $this->builder->build($request->getServerParams()['PHP_AUTH_USER']),
        ];

        $response->getBody()->write(json_encode($data, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT));

        return $response
            ->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }
}
