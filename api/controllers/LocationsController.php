<?php

namespace api\controllers;

use api\service\LocationsServicesData\LocationsServicesDataFactory;
use api\V2\Services\Locations\LocationsService;
use app\models\location\hydrators\LocationHydrator;
use OpenApi\Annotations as OA;

/**
 * @OA\Put(
 *     path="/locations/location/import",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/locations/location",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/locations/location/{id}",
 *     @OA\PathParameter(name="id", @OA\Schema(type="integer")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 */
class LocationsController extends AbstractAreaController
{
    public function __construct(
        LocationHydrator $locationHydrator,
        LocationsService $locationsService,
        LocationsServicesDataFactory $locationsServicesFactory
    ) {
        parent::__construct($locationsService, $locationHydrator, $locationsServicesFactory);
    }
}
