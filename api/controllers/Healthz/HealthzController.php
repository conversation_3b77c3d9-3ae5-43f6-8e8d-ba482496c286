<?php

declare(strict_types=1);

namespace api\controllers\Healthz;

use DateTime;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

class HealthzController
{
    /**
     * @OA\Get(
     *     path="/healthz",
     *     security={},
     *     @OA\Response(
     *         response="200",
     *         description="Healthcheck happy",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="timestamp", type="int"),
     *         ),
     *         @OA\Header(header="cache-control", @OA\Schema(type="string")),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getHealthzStatus(Request $request, Response $response): Response
    {
        $returnValues = [
            'status' => 'OK',
            'timestamp' => (new DateTime())->getTimestamp(),
        ];

        $response->getBody()->write(json_encode($returnValues));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json')
            ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
    }
}
