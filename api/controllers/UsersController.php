<?php

namespace api\controllers;

use api\exceptions\ApiValidationException;
use api\helpers\UsersHelper;
use api\validators\Users\UserCanAccessValidator;
use app\models\generic\valueObjects\JSONData;
use DatixDBQuery;
use Exception;
use OpenApi\Annotations as OA;
use PDO;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\logger\Facade\Log;
use src\users\model\UserMapper;
use Teapot\StatusCode\Http;
use Throwable;

class UsersController
{
    private UserMapper $userMapper;
    private UserCanAccessValidator $validator;

    public function __construct(UserMapper $userMapper, UserCanAccessValidator $validator)
    {
        $this->userMapper = $userMapper;
        $this->validator = $validator;
    }

    /**
     * @OA\Post(
     *     path="/users/has-access",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\AdditionalProperties(type="array", @OA\Items(type="integer")),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Validation error",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal Server error",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="User recor access map returned",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="records",
     *                 type="object",
     *                 @OA\AdditionalProperties(
     *                     type="object",
     *                     @OA\AdditionalProperties(
     *                         @OA\Property(property="title", type="string", nullable=true),
     *                         @OA\Property(property="dateOpened", type="string", nullable=true),
     *                         @OA\Property(property="status", type="string"),
     *                         @OA\Property(property="canRead", type="boolean"),
     *                         @OA\Property(property="recordsExist", type="boolean"),
     *                         @OA\Property(property="module", type="string"),
     *                     ),
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function hasAccess(Request $request, Response $response, array $args): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();
            $userRecordAccessMap = [];
            $user = $this->userMapper->findById($request->getAttribute('token')['userId']);

            if ($user === null) {
                throw new Exception('User from token does not exist');
            }

            if (!$this->validator->validate($request)) {
                throw new ApiValidationException('Provided json body was not in the correct format.');
            }

            foreach ($data as $moduleKey => $recordIds) {
                $filters = new UsersHelper();
                $filters->setRecordIds($recordIds);
                $filters->setModule($moduleKey);
                $filters->setUser($user);

                $userRecordAccessMap[$moduleKey] = $this->userMapper->canAccessRecords($filters);
            }
        } catch (ApiValidationException $e) {
            $response->getBody()->write(json_encode(['error' => $e->getMessage()]));

            return $response->withStatus(HTTP::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        } catch (Throwable $e) {
            Log::critical('Error found during hasAccess action', [
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode(['error' => 'Application failure: ' . $e->getMessage()]));

            return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode([
            'records' => $userRecordAccessMap,
        ]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Get(
     *     path="/users/get-user-param-value-by-parameter",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="userParamName", type="string"),
     *             @OA\Property(property="userIdentity", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="User Param returned",
     *         @OA\JsonContent(@OA\Property(property="isParamValueExist", type="string")),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getUserParamValueByParameter(Request $request, Response $response, array $args): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $sql = 'SELECT parmvalue FROM user_parms WHERE parameter = :userParamName AND login = :userIdentity';

        $isParamValueExist = DatixDBQuery::PDO_fetch($sql, [
            'userParamName' => $data['userParamName'],
            'userIdentity' => $data['userIdentity'],
        ], PDO::FETCH_COLUMN);

        $response->getBody()->write(json_encode([
            'isParamValueExist' => !$isParamValueExist ? 'N' : $isParamValueExist,
        ]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
