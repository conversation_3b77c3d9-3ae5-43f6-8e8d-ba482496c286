<?php

declare(strict_types=1);

namespace api\controllers\Claims;

use api\helpers\UsersHelper;
use app\models\generic\valueObjects\JSONData;
use Doctrine\DBAL\Connection;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Rakit\Validation\Validator;
use src\claims\models\ClaimMapper;
use src\claims\services\ClaimExportService;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use Teapot\StatusCode\Http;
use Throwable;

use function array_slice;
use function count;

use const JSON_THROW_ON_ERROR;

class ClaimsController
{
    private ClaimExportService $claimExportService;
    private LoggerInterface $logger;
    private Validator $validator;
    private Connection $connection;
    private ClaimMapper $claimMapper;

    public function __construct(
        ClaimExportService $claimExportService,
        LoggerInterface $logger,
        Validator $validator,
        Connection $connection,
        ClaimMapper $claimMapper
    ) {
        $this->claimExportService = $claimExportService;
        $this->logger = $logger;
        $this->validator = $validator;
        $this->connection = $connection;
        $this->claimMapper = $claimMapper;
    }

    /**
     * @OA\Get(
     *     path="/claims",
     *     @OA\Parameter(in="query", name="days_previous", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", name="cla_ourref", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="limit", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", name="start", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", name="login", required=true, @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Claims returned",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="results",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/ExportedClaim"),
     *             ),
     *             @OA\Property(property="limit", type="integer", nullable=true),
     *             @OA\Property(property="size", type="integer"),
     *             @OA\Property(property="start", type="integer"),
     *             @OA\Property(property="links", @OA\Property(property="self", type="string")),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Invalid request params(s)",
     *         @OA\JsonContent(@OA\Property(property="Error", type="string")),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getClaims(Request $request, Response $response): Response
    {
        $daysPrevious = $request->getQueryParams()['days_previous'] ?? null;
        $claimReference = $request->getQueryParams()['cla_ourref'] ?? null;
        $limit = $request->getQueryParams()['limit'] ?? null;
        $start = $request->getQueryParams()['start'] ?? 0;
        $login = $request->getQueryParams()['login'] ?? null;

        if (!($claimReference || $daysPrevious)) {
            $response->getBody()->write(json_encode([
                'Error' => 'You must provide a filter for your results. Acceptable filters: daysPrevious, cla_ourref.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        if ($daysPrevious && (!is_numeric($daysPrevious) || $daysPrevious <= 0)) {
            $response->getBody()->write(json_encode([
                'Error' => 'daysPrevious parameter must be a positive integer.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        if (!$login) {
            $response->getBody()->write(json_encode([
                'Error' => 'No login context provided.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $links = [];
        $queryParams = [
            'login' => $login,
        ];

        if ($daysPrevious) {
            $daysPrevious = max(0, (int) $daysPrevious);
            $queryParams['days_previous'] = $daysPrevious;
            $ids = $this->claimExportService->getIdsByDaysPrevious('claims_main', $daysPrevious);
        } elseif ($claimReference) {
            $queryParams['cla_ourref'] = $claimReference;
            $ids = $this->claimExportService->getIdsByClaimReference($claimReference);
        }

        $size = count($ids);

        $idsToSend = $ids;
        if (isset($limit)) {
            $queryParams['limit'] = $limit;
            $idsToSend = array_slice($ids, $start, $limit, true);
            $size = count($idsToSend);

            if (count($ids) > $start + $limit) {
                $nextStart = $start + $limit;
                $queryParams['start'] = $nextStart;
                $links['next'] = '/api/claims?' . http_build_query($queryParams);
            }

            if ($start > 0) {
                $previousStart = $start - $limit;
                if ($previousStart < 1) {
                    $previousStart = 0;
                }
                $queryParams['start'] = $previousStart;
                $links['prev'] = '/api/claims?' . http_build_query($queryParams);
            }
        }

        $queryParams['start'] = $start;
        $links['self'] = '/api/claims?' . http_build_query($queryParams);

        $parameters = [
            'limit' => $limit,
            'size' => $size,
            'start' => $start,
            'links' => $links,
        ];

        $jsonForExport = $this->claimExportService->constructJsonForExport($idsToSend, $login, $parameters);

        $response->getBody()->write($jsonForExport);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Get(
     *     path="/claims/claim/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Parameter(name="login", in="query", required=true, @OA\Schema(type="string", minLength=1)),
     *     @OA\Response(response="200", description="Claim returned", @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/ExportedClaim"))),
     *     @OA\Response(response="400", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getClaimById(Request $request, Response $response): Response
    {
        $id = (int) $request->getAttribute('id');

        if ($id <= 0) {
            $response->getBody()->write(json_encode([
                'Error' => 'id parameter must be a positive integer.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $login = $request->getQueryParams()['login'] ?? null;

        if (!$login) {
            $response->getBody()->write(json_encode([
                'Error' => 'No login context provided.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $jsonForExport = $this->claimExportService->constructJsonForExport([$id], $login);

        $response->getBody()->write($jsonForExport);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Patch(
     *     path="/claims/claim/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32", minimum=1)),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="claimEditAgencyClaimNumber", type="integer", format="int32", minimum=1),
     *             @OA\Property(property="claimEdiStatus", type="string"),
     *             @OA\Property(property="claimFROIMaintenenceTypeCode", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="200", description="Claim updated", @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/ExportedClaim"))),
     *     @OA\Response(response="204", description="No request body provided"),
     *     @OA\Response(
     *         response="400",
     *         description="Invalid request",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(@OA\Property(property="Error", type="string")),
     *                 @OA\Schema(@OA\Property(property="error", type="string")),
     *                 @OA\Schema(@OA\AdditionalProperties(@OA\AdditionalProperties(type="string"))),
     *             },
     *         ),
     *     ),
     *     @OA\Response(response="404", description="Claim not found", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="500", description="Internal server error", ref="#/components/responses/ErrorResponse"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function updateClaimById(Request $request, Response $response): Response
    {
        $id = (int) $request->getAttribute('id');

        if ($id <= 0) {
            $response->getBody()->write(json_encode([
                'Error' => 'id parameter must be a positive integer.',
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data)) {
            return $response->withStatus(Http::NO_CONTENT);
        }

        $allowedFields = [
            'claimEdiAgencyClaimNumber' => [
                'dbName' => 'edi_agency_claim_number',
                'validation' => 'numeric|min:1',
            ],
            'claimEdiStatus' => [
                'dbName' => 'edi_claim_status',
                'validation' => '',
            ],
            'claimFROIMaintenenceTypeCode' => [
                'dbName' => 'edi_froi_maintenance_type',
                'validation' => '',
            ],
        ];

        // Data validation
        $this->validator->setMessages([
            'numeric' => ':attribute is not a numeric value',
            'min' => ':attribute has a min value of :min',
            'date' => ':attribute value should be in :format format',
        ]);

        $validationRules = [];

        foreach ($allowedFields as $field => $fieldData) {
            $validationRules[$field] = $fieldData['validation'];
        }

        $validation = $this->validator->validate($data, $validationRules);

        if ($validation->fails()) {
            $response->getBody()->write(json_encode($validation->errors()->toArray(), JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $statement = $this->connection->prepare('SELECT recordid FROM claims_main WHERE recordid = :recordid');
        $claim = $statement->executeQuery(['recordid' => $id])->fetchAllAssociative()[0] ?? null;
        if (empty($claim)) {
            throw new Exception('Claim not found.', Http::NOT_FOUND);
        }

        $fieldSql = '';
        $parameters = [];

        foreach ($data as $field => $value) {
            if (isset($allowedFields[$field])) {
                $fieldSql .= "{$allowedFields[$field]['dbName']} = :{$field},";
                $parameters[$field] = $value;
            }
        }

        if (empty($fieldSql)) {
            $response->getBody()->write(json_encode(['error' => 'No valid fields in request'], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $fieldSql = substr($fieldSql, 0, -1);

        $parameters['recordid'] = $id;

        $this->connection->beginTransaction();
        $statement = $this->connection->prepare("UPDATE claims_main SET {$fieldSql} WHERE recordid = :recordid");

        try {
            $statement->executeStatement($parameters);
        } catch (Throwable $exception) {
            $this->connection->rollBack();
            $this->logger->error('Error white executing database query: updating claims_main.', [
                'exception' => $exception,
            ]);
            $response->getBody()->write(json_encode(['error' => 'Internal server error'], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $this->connection->commit();

        return $response->withStatus(Http::OK);
    }

    /**
     * @OA\Get(
     *     path="/claims/by-handler",
     *     @OA\Response(
     *         response="200",
     *         description="Claims returned",
     *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/ExportedClaim")),
     *         @OA\Header(header="Cache-Control", @OA\Schema(type="string")),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getClaimByHandler(Request $request, Response $response): Response
    {
        $results = [];

        $token = $request->getAttribute('token');
        $user = UsersHelper::getUserFromJWT($token);

        if ($user === null) {
            return UsersHelper::getEmptyJWTResponse($response);
        }

        $claimCollection = $this->claimMapper->findOpenByHandlerManagerOrInvestigator($user->initials);

        $languageSession = LanguageSessionFactory::getInstance(true);

        foreach ($claimCollection as $key => $claim) {
            $results[$key]['id'] = $claim->recordid;
            $results[$key]['title'] = $claim->cla_name;

            if ($claim->recordid) {
                $results[$key]['url'] = getenv('BASE_URL') . "index.php?action=record&module=CLA&recordid={$claim->recordid}";
            }

            $results[$key]['details'] = null;
            $registry = Container::get(Registry::class);
            $stageField = $registry->getFieldDefs()['claims_main.cla_curstage'];
            $codes = $stageField->getCodes();

            if ($codes[$claim->cla_curstage]) {
                $results[$key]['details'] = $codes[$claim->cla_curstage]->getDescription();
            }

            $results[$key]['due_date'] = null;

            $roles = [];

            if ($claim->cla_mgr === $user->initials) {
                $roles[] = $languageSession->getFieldString('CLAIMS', 'cla_mgr');
            }

            if ($claim->cla_head === $user->initials) {
                $roles[] = $languageSession->getFieldString('CLAIMS', 'cla_head');
            }

            if ($claim->cla_investigator === $user->initials) {
                $roles[] = $languageSession->getFieldString('CLAIMS', 'cla_investigator');
            }

            $results[$key]['role'] = implode(', ', $roles);
        }

        $response->getBody()->write(json_encode($results));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json')
            ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
    }
}
