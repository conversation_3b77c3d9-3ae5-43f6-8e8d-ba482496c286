<?php

declare(strict_types=1);

namespace api\controllers;

use app\models\modules\ModuleEntity;
use app\services\carlton\api\CarltonAPIService;
use DateTime;
use Doctrine\ORM\EntityRepository;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\framework\json\Json;
use Teapot\StatusCode\Http;

use function in_array;

/**
 * @OA\Schema(schema="HealthCheckEnum", type="string", enum={"OK", "FAILED"}))
 */
class HealthcheckController
{
    private const STATUS_OK = 'OK';
    private const STATUS_FAILED = 'FAILED';
    private EntityRepository $moduleRepository;
    private CarltonAPIService $navigationService;

    public function __construct(EntityRepository $moduleRepository, CarltonAPIService $navigationService)
    {
        $this->moduleRepository = $moduleRepository;
        $this->navigationService = $navigationService;
    }

    /**
     * @OA\Get(
     *     path="/healthcheck",
     *     security={},
     *     @OA\Response(
     *         response="200",
     *         description="Healthcheck happy",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", ref="#/components/schemas/HealthCheckEnum"),
     *             @OA\Property(property="timestamp", type="integer"),
     *             @OA\Property(
     *                 property="health",
     *                 type="object",
     *                 @OA\Property(property="carlton", ref="#/components/schemas/HealthCheckEnum"),
     *                 @OA\Property(property="mssql", ref="#/components/schemas/HealthCheckEnum"),
     *                 @OA\Property(
     *                     property="princeModules",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                 ),
     *             ),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function index(Request $request, Response $response): Response
    {
        /** @var ModuleEntity[] $moduleEntities */
        $moduleEntities = $this->moduleRepository->findAll();

        $princeModuleCodes = array_map(static fn (ModuleEntity $module): string => $module->getModModule(), $moduleEntities);

        $mssqlHealth = empty($princeModuleCodes) ? self::STATUS_FAILED : self::STATUS_OK;

        $this->navigationService->connect();

        $carltonHealth = $this->navigationService->getStatusCode() === Http::OK ? self::STATUS_OK : self::STATUS_FAILED;

        $returnCode = in_array(self::STATUS_FAILED, [$mssqlHealth, $carltonHealth], true) ? Http::INTERNAL_SERVER_ERROR : Http::OK;

        $returnValues = [
            'status' => $returnCode === Http::OK ? self::STATUS_OK : self::STATUS_FAILED,
            'timestamp' => (new DateTime())->getTimestamp(),
            'health' => [
                'carlton' => $carltonHealth,
                'mssql' => $mssqlHealth,
                'princeModules' => $princeModuleCodes,
            ],
        ];

        $response->getBody()->write(Json::encode($returnValues));

        return $response->withStatus($returnCode);
    }
}
