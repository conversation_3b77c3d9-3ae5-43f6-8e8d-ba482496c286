<?php

namespace api\controllers;

use api\helpers\IncidentsSearchHelper;
use api\service\PsimsResponseService;
use app\Exceptions\Api\AuthorisationError;
use app\models\contact\ContactTypes;
use app\models\contact\services\ContactService;
use app\models\framework\config\GlobalValue;
use app\models\generic\valueObjects\JSONData;
use app\models\generic\valueObjects\Module;
use app\models\incidents\adaptors\IncidentAggregateAdaptorFactory;
use app\models\incidents\entities\Incident;
use app\models\incidents\IncidentAggregate;
use app\models\incidents\IncidentRepositoryFactory;
use app\models\incidents\services\ProgressNoteService;
use app\services\document\storagehandlers\DocumentApiUploadServiceFactory;
use app\services\globals\GlobalService;
use app\services\incidents\IncidentUpdateEmailService;
use app\services\udf\UdfServiceFactory;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use RuntimeException;
use src\equipment\Services\EquipmentServiceFactory;
use src\incidents\model\IncidentModelFactory;
use src\logger\Facade\Log;
use src\medications\Services\MedicationsServiceFactory;
use src\system\container\facade\Container as ContainerFacade;
use src\system\specifications\ShouldSendUpdateEmailsSpecification;
use src\users\model\UserModelFactory;
use Teapot\StatusCode\Http;
use Teapot\StatusCode\RFC\RFC7231;
use Throwable;

use Exception;
use JsonException;

use function array_merge;

use const JSON_THROW_ON_ERROR;

class IncidentsController
{
    private GlobalService $globalService;

    public function __construct(GlobalService $globalService)
    {
        $this->globalService = $globalService;
    }

    /**
     * @OA\Post(
     *     path="/incidents/incident",
     *     description="Submit an incident",
     *     @OA\RequestBody(@OA\MediaType(mediaType="application/json", @OA\Schema(ref="#/components/schemas/Incident"))),
     *     @OA\Response(response="200", description="successful", @OA\JsonContent(example="{'success': 'Inserted Successfully', 'data': {'id': 30}}")),
     *     @OA\Response(response="401", description="Unauthorized", @OA\JsonContent(example="{'error': 'Authentication failure: reason'}")),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent(example="{'error': 'Application failure: reason'}")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     * @OA\Post(
     *     path="/incidents",
     *     deprecated=true,
     *     description="Submit an incident",
     *     @OA\RequestBody(@OA\MediaType(mediaType="application/json", @OA\Schema(ref="#/components/schemas/Incident"))),
     *     @OA\Response(response="200", description="successful", @OA\JsonContent(example="{'success': 'Inserted Successfully', 'data': {'id': 30}}")),
     *     @OA\Response(response="401", description="Unauthorized", @OA\JsonContent(example="{'error': 'Authentication failure: reason'}")),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent(example="{'error': 'Application failure: reason'}")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function create(Request $request, Response $response, array $args): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();

            $adaptor = (new IncidentAggregateAdaptorFactory())->create();
            $aggregate = $adaptor->adaptFromRequest($data, $request);

            $repository = (new IncidentRepositoryFactory())->create();

            if (!empty($data['recordId']) && $repository->exists($data['recordId'])) {
                $response->getBody()->write(json_encode([
                    'error' => 'To update a previous record please use the update endpoint',
                ]));

                return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                    ->withHeader('Content-Type', 'application/json');
            }

            $repository->insert($aggregate);

            $uuid = $aggregate->getIncident()->getUUID();
            $recordId = $data['recordId'] ?? $aggregate->getIncident()->getId()[0]->getId();
            $extraFieldsData = $data['extraFields'] ?? [];
            $documentData = $data['documents'] ?? [];

            (new UdfServiceFactory())->create()->upsert($extraFieldsData, Module::INCIDENTS, $recordId);
            (new DocumentApiUploadServiceFactory())->create()->upload(Module::INCIDENTS, $recordId, $documentData);

            if (isset($data['medications'])) {
                $medicationService = MedicationsServiceFactory::create(Module::INCIDENTS);
                $medicationService->process($uuid, $data['medications']);
            }

            if (isset($data['equipment'])) {
                $equipmentService = EquipmentServiceFactory::create(Module::INCIDENTS);
                $equipmentService->process($uuid, $data['equipment']);
            }

            if (!empty($data['progressNotes'])) {
                ContainerFacade::get(ProgressNoteService::class)->upsert($data['progressNotes'], $recordId);
            }

            $contactService = new ContactService();
            $contacts = $contactService->processFromData($data['contacts'] ?? []);
            $contactService->linkContactsToRecord(
                Module::INCIDENTS,
                $aggregate->getIncident()->getId()[0]->getId(),
                ContactTypes::PERSON_AFFECTED,
                $contacts,
            );
        } catch (AuthorisationError $e) {
            $response->getBody()->write(json_encode([
                'error' => 'Authentication failure: ' . $e->getMessage(),
            ]));

            return $response->withStatus(HTTP::UNAUTHORIZED)
                ->withHeader('Content-Type', 'application/json');
        } catch (Throwable $e) {
            Log::critical('Error found while creating an incident', [
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode([
                'error' => 'Application failure: ' . $e->getMessage(),
            ]));

            return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $successMessage = 'Inserted Successfully';

        if ($request->getUri()->getPath() === 'incidents/') {
            $successMessage .= " *** Important Note: This endpoint will soon be deprecated in favour of 'incidents/incident'. The only change required is to the path. Please update your usage as soon as possible ***";
        }

        $response->getBody()->write(json_encode([
            'success' => $successMessage,
            'data' => [
                'id' => $recordId,
            ],
        ]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/incidents/{incidentId}",
     *     deprecated=true,
     *     description="Update an existing incident, not this api is not resetful and expects the whole body to be subbmited",
     *     @OA\PathParameter(name="incidentId", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(@OA\MediaType(mediaType="application/json", @OA\Schema(ref="#/components/schemas/Incident"))),
     *     @OA\Response(response="200", description="successful", @OA\JsonContent(example="{'success': 'Updated Successfully'}"), @OA\Property(property="success", type="string")),
     *     @OA\Response(response="404", description="incident not found", @OA\MediaType(mediaType="application/json")),
     *     @OA\Response(response="500", description="internal server error", @OA\MediaType(mediaType="application/json")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     * @OA\Post(
     *     path="/incidents/incident/{incidentId}",
     *     description="Update an existing incident, not this api is not resetful and expects the whole body to be subbmited",
     *     @OA\PathParameter(name="incidentId", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(@OA\MediaType(mediaType="application/json", @OA\Schema(ref="#/components/schemas/Incident"))),
     *     @OA\Response(response="200", description="successful", @OA\JsonContent(example="{'success': 'Updated Successfully'}"), @OA\Property(property="success", type="string")),
     *     @OA\Response(response="404", description="incident not found", @OA\MediaType(mediaType="application/json")),
     *     @OA\Response(response="500", description="internal server error", @OA\MediaType(mediaType="application/json")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function update(Request $request, Response $response, array $args): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();

            $recordId = $request->getAttribute('incidentId');

            $data['recordId'] = $recordId;
            $repository = (new IncidentRepositoryFactory())->create();

            if (!$repository->exists($recordId)) {
                $response->getBody()->write(json_encode([
                    'error' => 'Incident not found.',
                ]));

                return $response->withStatus(Http::NOT_FOUND)
                    ->withHeader('Content-Type', 'application/json');
            }

            $currentData = $repository->get([$recordId])->toArray();
            $data['incidents']['inc_name'] ??= $currentData['incidentName'];
            $data['incidents']['rep_approved'] ??= $currentData['approvalStatus'];
            $data['incidents']['inc_ourref'] ??= $currentData['incOurref'];
            $data['descriptionText'] ??= $currentData['description']['text'];
            unset($currentData['incidentName'], $currentData['approvalStatus'], $currentData['incOurref'], $currentData['description']);

            $data = array_merge($currentData, $data);
            $adaptor = (new IncidentAggregateAdaptorFactory())->create();
            $aggregate = $adaptor->adaptFromRequest($data, $request);

            /** @var IncidentAggregate $incidentAggregate */
            $incidentAggregate = $repository->update($aggregate);
            $repository->update($aggregate);

            $uuid = $aggregate->getIncident()->getUUID();
            $extraFieldsData = $data['extraFields'] ?? [];
            $documentData = $data['documents'] ?? [];

            (new UdfServiceFactory())->create()->upsert($extraFieldsData, Module::INCIDENTS, $recordId);
            (new DocumentApiUploadServiceFactory())->create()->upload(Module::INCIDENTS, $recordId, $documentData);

            if (isset($data['medications'])) {
                $medicationService = MedicationsServiceFactory::create(Module::INCIDENTS);
                $medicationService->process($uuid, $data['medications']);
            }

            if (isset($data['equipment'])) {
                $equipmentService = EquipmentServiceFactory::create(Module::INCIDENTS);
                $equipmentService->process($uuid, $data['equipment']);
            }

            if (!empty($data['progressNotes'])) {
                ContainerFacade::get(ProgressNoteService::class)->upsert($data['progressNotes'], $recordId);
            }

            $contactService = new ContactService();
            $contacts = $contactService->processFromData($data['contacts'] ?? []);
            $contactService->linkContactsToRecord(
                Module::INCIDENTS,
                $aggregate->getIncident()->getId()[0]->getId(),
                ContactTypes::PERSON_AFFECTED,
                $contacts,
            );
        } catch (AuthorisationError $e) {
            $response->getBody()->write(json_encode([
                'error' => 'Authentication failure: ' . $e->getMessage(),
            ]));

            return $response->withStatus(HTTP::UNAUTHORIZED)
                ->withHeader('Content-Type', 'application/json');
        } catch (Throwable $e) {
            Log::critical('Error found while updating an incident', [
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode([
                'error' => 'Application failure: ' . $e->getMessage(),
            ]));

            return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $this->sendUpdateIncidentNotification(
            $aggregate->getIncident(),
            $incidentAggregate->getIncident(),
        );

        $successMessage = 'Updated Successfully';
        $pathString = '/incidents\/([0-9]+)/';

        if (preg_match($pathString, $request->getUri()->getPath())) {
            $successMessage .= " *** Important Note: This endpoint will soon be deprecated in favour of 'incidents/incident/{incidentId}'. The only change required is to the path. Please update your usage as soon as possible ***";
        }

        $response->getBody()->write(json_encode(['success' => $successMessage]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * Returns all or searched Incident records for a user with read permission.
     *
     * @OA\Get(
     *     path="/incidents/search",
     *     @OA\Parameter(in="query", name="recordIds", @OA\Schema(type="array", @OA\Items(type="integer"))),
     *     @OA\Parameter(in="query", name="reference", @OA\Schema(type="string")),
     *     @OA\Response(response="500", description="Application error", @OA\Property(property="error", type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Incident records returned",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="incidents",
     *                 type="array",
     *                 maxItems=100,
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="integer"),
     *                     @OA\Property(property="title", type="string"),
     *                     @OA\Property(property="date", type="string"),
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getIncidentRecords(Request $request, Response $response, array $args): Response
    {
        try {
            $filters = new IncidentsSearchHelper();

            if (isset($request->getQueryParams()['recordIds'])) {
                $filters->setRecordIds($request->getQueryParams()['recordIds']);
            }

            if (isset($request->getQueryParams()['reference'])) {
                $filters->setReference($request->getQueryParams()['reference']);
            }

            $userId = $request->getAttribute('token')['userId'];
            $userMapper = (new UserModelFactory())->getMapper();
            $user = $userMapper->findById($userId);

            $filters->setUser($user);

            $incidentMapper = (new IncidentModelFactory())->getMapper();
            $incidentData = $incidentMapper->search($filters);
        } catch (Throwable $e) {
            Log::critical('Error found while getting incident records', [
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode(['error' => 'Application failure: ' . $e->getMessage()]));

            return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode(['incidents' => $incidentData]));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @throws JsonException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function postPsimsSubmission(Request $request, Response $response, array $args): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();
            $recordId = (int) $request->getAttribute('id');
            $psimsResponseService = ContainerFacade::get(PsimsResponseService::class);

            if (!$psimsResponseService->checkIncidentExists($recordId)) {
                $response->getBody()->write(json_encode([
                    'error' => 'Incident not found whilst attempting to submit PSIMS response',
                ], JSON_THROW_ON_ERROR));
                Log::error('Post Psims Submission Failed because the Incident ID: ' . $recordId . ' does not exist!');

                return $response->withStatus(RFC7231::NOT_FOUND)
                    ->withHeader('Content-Type', 'application/json');
            }

            try {
                $psimsResponseService->upsertResponse([$recordId], $data);
            } catch (Exception $exception) {
                throw new RuntimeException('Post PSIMS Submission Failed with error: ' . $exception->getMessage());
            }
        } catch (Throwable $e) {
            Log::critical('Error found when updating incident_psims_response', [
                'exception' => $e,
            ]);

            $response->getBody()->write(json_encode([
                'error' => 'Application failure: ' . $e->getMessage(),
            ], JSON_THROW_ON_ERROR));

            return $response->withStatus(RFC7231::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $successMessage = 'PSIMS response data for incident ' . $recordId . ' updated successfully';

        $response->getBody()->write(json_encode(['success' => $successMessage], JSON_THROW_ON_ERROR));

        return $response->withStatus(RFC7231::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    private function sendUpdateIncidentNotification(Incident $oldIncident, Incident $incident): void
    {
        if (!$this->shouldSendEmail($incident)) {
            return;
        }

        $difPerms = $this->getParm('DIF_PERMS', $incident, 'NONE');

        $alreadyEmailedContacts = IncidentUpdateEmailService::getAlreadyEmailedContacts(
            $incident->getId()[0]->getId(),
            $difPerms,
            2,
            $this->getParm('RECORD_UPDATE_EMAIL_DATETIME', $incident),
        );

        $incidentData = $incident->toArray();
        $incidentData['recordid'] = $incidentData['id'];

        SendEmails([
            'module' => Module::INCIDENTS,
            'data' => $incidentData,
            'noApproval' => true,
            'from' => $oldIncident->approvalStatus,
            'to' => $incident->approvalStatus,
            'perms' => $difPerms ?: 'NONE',
            'level' => 2,
            'notList' => $alreadyEmailedContacts,
            'emailType' => 'UPDATE',
        ]);
    }

    private function shouldSendEmail(Incident $incident): bool
    {
        $recordUpdateEmailSpecification = new ShouldSendUpdateEmailsSpecification();

        if (!$recordUpdateEmailSpecification->isSatisfiedBy(
            Module::INCIDENTS,
            $this->getParm('RECORD_UPDATE_EMAIL_INC', $incident, 'N')->toBool(),
            $this->getParm('RECORD_UPDATE_EMAIL_DATETIME', $incident)->toScalar(),
        )) {
            return false;
        }

        if ($incident->approvalSatus === 'STCL') {
            return false;
        }

        if ($this->getParm('NEW_RECORD_LVL1_INC', $incident, 'N')->isFalse()) {
            return false;
        }

        return true;
    }

    private function getParm(string $parm, Incident $incident, ?string $defaultValue = null): GlobalValue
    {
        return $this->globalService->getParm($parm, $incident->toArray()['incRepId'] ?? null, $defaultValue);
    }
}
