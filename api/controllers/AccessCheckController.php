<?php

declare(strict_types=1);

namespace api\controllers;

use api\service\AccessCheckService;
use MissingParameterException;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

class AccessCheckController
{
    private AccessCheckService $accessCheckService;

    public function __construct(AccessCheckService $accessCheckService)
    {
        $this->accessCheckService = $accessCheckService;
    }

    /**
     * @OA\Get(
     *     path="/access-check/{userid}/{module}/{recordid}",
     *     description="Checks user access to a record",
     *     @OA\Parameter(name="userid", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Parameter(name="module", in="path", required=true, @OA\Schema(type="string")),
     *     @OA\Parameter(name="recordid", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="useAdvancedAccessLevels", type="boolean"),
     *             @OA\Property(property="userCanSeeRecord", type="boolean"),
     *             @OA\Property(property="userWouldBeEmailedForRecord", type="boolean"),
     *             @OA\Property(property="groupsThatWouldProvideAccess", type="array", @OA\Items(type="integer", format="int32")),
     *             @OA\Property(property="groupsThatWouldEmailUserForThisRecord", type="array", @OA\Items(type="integer", format="int32")),
     *             @OA\Property(property="groupsThatDenyAccess", type="array", @OA\Items(type="integer", format="int32")),
     *             @OA\Property(property="groupsInProfile", type="array", @OA\Items),
     *             @OA\Property(property="groupsAttachedToUser", type="array", @OA\Items),
     *             @OA\Property(property="topLevelWhere", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal Server Error or paramter is missing",
     *         @OA\JsonContent,
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws MissingParameterException
     *
     * @noinspection PhpUnused - Controller action
     */
    public function checkAccess(Request $request, Response $response, array $args): Response
    {
        if (!isset($args['userid'])) {
            throw new MissingParameterException('User Id is missing');
        }

        if (!isset($args['module'])) {
            throw new MissingParameterException('Module is missing');
        }

        if (!isset($args['recordid'])) {
            throw new MissingParameterException('Record Id is missing');
        }

        $userId = (int) $args['userid'];
        $module = $args['module'];
        $recordId = (int) $args['recordid'];

        $accessData = $this->accessCheckService->check($userId, $module, $recordId);

        $response->getBody()->write(json_encode($accessData));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
