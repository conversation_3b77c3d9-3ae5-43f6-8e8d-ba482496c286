<?php

namespace api\controllers;

use api\service\LocationsServicesData\LocationsServicesData;
use api\service\LocationsServicesData\LocationsServicesDataFactory;
use api\V2\Services\Locations\LocationsService;
use api\V2\Services\Services\ServicesService;
use app\models\location\entities\LocationEntity;
use app\models\location\hydrators\LocationHydrator;
use app\models\service\entities\ServiceEntity;
use app\models\service\hydrators\ServiceHydrator;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

/**
 * @OA\Schema(
 *     schema="TreeImportEntity",
 *     @OA\Property(property="node", type="object"),
 *     @OA\Property(property="tree", type="object"),
 * )
 */
abstract class AbstractAreaController
{
    /** @var ServiceHydrator|LocationHydrator */
    protected $hydrator;

    /** @var ServicesService|LocationsService */
    private $service;
    private LocationsServicesDataFactory $locationsServicesFactory;

    /**
     * @param ServicesService|LocationsService $service
     * @param ServiceHydrator|LocationHydrator $hydrator
     */
    public function __construct(
        $service,
        $hydrator,
        LocationsServicesDataFactory $locationsServicesFactory
    ) {
        $this->service = $service;
        $this->hydrator = $hydrator;
        $this->locationsServicesFactory = $locationsServicesFactory;
    }

    /**
     * @noinspection PhpUnused - Controller action
     */
    public function importFromCmt(Request $request, Response $response): Response
    {
        return $this->import($request, $response, true);
    }

    /**
     * @noinspection PhpUnused - Controller action
     */
    public function importFromPostOrPut(Request $request, Response $response): Response
    {
        return $this->import($request, $response, false);
    }

    /**
     * @param bool $fromImportExportService Is this request coming from the ImportExport Service
     *
     * @return LocationEntity|ServiceEntity
     */
    protected function importService(
        LocationsServicesData $locationsServicesData,
        bool $fromImportExportService = false
    ) {
        /** @var LocationEntity|ServiceEntity $savedEntity */
        return $this->service->import($locationsServicesData, $fromImportExportService);
    }

    private function import(
        Request $request,
        Response $response,
        bool $fromImportExportService
    ): Response {
        $savedEntity = $this->importService(
            $this->locationsServicesFactory->createFromRequest($request),
            $fromImportExportService,
        );

        $response->getBody()->write(json_encode($this->hydrator->extract($savedEntity)));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
