<?php

declare(strict_types=1);

namespace api\controllers;

use app\services\user\RecordLocksRepository;
use app\services\user\UserSessionRepository;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\logger\Facade\Log;
use Teapot\StatusCode\Http;
use Throwable;

use const JSON_THROW_ON_ERROR;

class LogoutController
{
    private RecordLocksRepository $recordLocksRepository;
    private UserSessionRepository $userSessionRepository;

    public function __construct(RecordLocksRepository $recordLocksRepository, UserSessionRepository $userSessionRepository)
    {
        $this->recordLocksRepository = $recordLocksRepository;
        $this->userSessionRepository = $userSessionRepository;
    }

    /**
     * @OA\Get(
     *     path="/logout",
     *     @OA\Response(
     *         response="500",
     *         description="Internal server error",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string"),
     *             @OA\Property(property="error", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Successfully logged out",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string"),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Microcontroller Action
     */
    public function __invoke(Request $request, Response $response): Response
    {
        $token = $request->getAttribute('token');
        $userId = $token['userId'];

        try {
            // Clear locks before session so that the current user session can be used for removing locks associated with a user session
            $this->recordLocksRepository->clearUserRecordLocksInDatabase($userId);

            // Clear all user sessions for the user ID passed in
            $this->userSessionRepository->clearUserSessionInDatabase($userId);
        } catch (Throwable $exception) {
            Log::critical('Error found during logout action', [
                'exception' => $exception,
            ]);

            $response->getBody()->write(json_encode([
                'status' => 'failure',
                'error' => $exception->getMessage(),
            ], JSON_THROW_ON_ERROR));

            return $response
                ->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode([
            'status' => 'success',
        ], JSON_THROW_ON_ERROR));

        return $response
            ->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
