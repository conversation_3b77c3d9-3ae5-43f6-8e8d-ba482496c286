<?php

declare(strict_types=1);

namespace api\controllers\Codes;

use app\models\codefield\hydrators\CodeFieldHydrator;
use app\models\generic\valueObjects\JSONData;
use app\services\codes\CodesService;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\logger\Facade\Log;
use Teapot\StatusCode\Http;
use Throwable;

class CodesController
{
    private CodeFieldHydrator $codeFieldHydrator;
    private CodesService $codesService;

    public function __construct(CodeFieldHydrator $codeFieldHydrator, CodesService $codesService)
    {
        $this->codeFieldHydrator = $codeFieldHydrator;
        $this->codesService = $codesService;
    }

    /**
     * @OA\Patch(
     *     path="/codes/code",
     *     description="Sync codes",
     *     @OA\RequestBody(
     *         description="body of the code fields",
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="fields",
     *                 type="array",
     *                 @OA\Items(@OA\Property(property="field", ref="#/components/schemas/CodeFieldEntity")),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response=204, description="Successful operation"),
     *     @OA\Response(
     *         response="400",
     *         description="Invalid input",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Update failed",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function updateCodes(Request $request, Response $response): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray()['fields'];

            $fieldEntities = [];
            foreach ($data as $fields) {
                foreach ($fields as $field) {
                    $fieldEntities[] = $this->codeFieldHydrator->hydrate($field);
                }
            }
        } catch (Exception $exception) {
            $response->getBody()->write(json_encode(['error' => 'Invalid input: ' . $exception->getMessage()]));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        try {
            foreach ($fieldEntities as $fieldEntity) {
                $this->codesService->update($fieldEntity);
            }
        } catch (Throwable $exception) {
            Log::critical('Error during update codes action', [
                'exception' => $exception,
            ]);

            $response->getBody()->write(json_encode(['error' => 'Error Saving Data : ' . $exception->getMessage()]));

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        return $response->withStatus(Http::NO_CONTENT);
    }
}
