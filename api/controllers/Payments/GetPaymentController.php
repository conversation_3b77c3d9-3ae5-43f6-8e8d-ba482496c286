<?php

declare(strict_types=1);

namespace api\controllers\Payments;

use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\payments\services\PaymentExportService;

final class GetPaymentController
{
    /** @readonly */
    private PaymentExportService $exportService;

    public function __construct(
        PaymentExportService $exportService
    ) {
        $this->exportService = $exportService;
    }

    /**
     * @OA\Get(
     *     path="/payments/payment/{id}",
     *     @OA\PathParameter(name="id", @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response="200",
     *         description="Payment returned",
     *         @OA\JsonContent(ref="#/components/schemas/ExportedPayment"),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getPayment(Request $request, Response $response): Response
    {
        $id = (int) $request->getAttribute('id');

        if ($id <= 0) {
            $response->getBody()->write(json_encode([
                'Error' => 'id parameter must be a positive integer.',
            ]));

            return $response;
        }

        $login = $request->getQueryParams()['login'];

        if (!$login) {
            $response->getBody()->write(json_encode([
                'Error' => 'No login context provided.',
            ]));

            return $response;
        }

        $jsonForExport = $this->exportService->constructJsonForExport([$id], $login);

        $response->getBody()->write($jsonForExport);

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
