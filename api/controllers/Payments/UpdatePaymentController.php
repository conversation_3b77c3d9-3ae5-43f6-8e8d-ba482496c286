<?php

declare(strict_types=1);

namespace api\controllers\Payments;

use app\models\generic\valueObjects\JSONData;
use app\models\generic\valueObjects\Module;
use Doctrine\DBAL\Connection;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Rakit\Validation\Validator;
use src\logger\Facade\Log;
use src\udf\service\UserDefinedField;
use Throwable;

final class UpdatePaymentController
{
    /** @readonly */
    private UserDefinedField $userDefinedFieldService;

    /** @readonly */
    private Connection $db;

    public function __construct(
        UserDefinedField $userDefinedFieldService,
        Connection $db
    ) {
        $this->userDefinedFieldService = $userDefinedFieldService;
        $this->db = $db;
    }

    /**
     * @OA\Patch(
     *     path="/payments/payment/{id}",
     *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="paymentDate", type="string"),
     *             @OA\Property(property="paymentAmount", type="integer"),
     *             @OA\Property(property="paymentPayee", type="integer"),
     *             @OA\Property(
     *                 property="additionalDataItems",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\AdditionalProperties(
     *                         oneOf={
     *                             @OA\Schema(type="string"),
     *                             @OA\Schema(type="integer"),
     *                         },
     *                     ),
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="204", description="Request body missing"),
     *     @OA\Response(
     *         response="400",
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\AdditionalProperties(type="object", @OA\AdditionalProperties(type="string")),
     *         ),
     *     ),
     *     @OA\Response(response="404", description="No payment found"),
     *     @OA\Response(response="500", ref="#/components/responses/ErrorResponse"),
     *     @OA\Response(
     *         response="200",
     *         description="Payment updated",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(@OA\Property(property="Error", type="string")),
     *                 @OA\Schema(type="array", maxItems=0, @OA\Items),
     *             },
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function updatePayment(Request $request, Response $response): Response
    {
        $id = (int) $request->getAttribute('id');

        if ($id <= 0) {
            $response->getBody()->write(json_encode([
                'Error' => 'id parameter must be a positive integer.',
            ]));

            return $response;
        }

        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data)) {
            $response->getBody()->write(json_encode([]));

            return $response->withStatus(204)
                ->withHeader('Content-Type', 'application/json');
        }

        $allowedFields = [
            'paymentDate' => [
                'dbName' => 'pay_date',
                'validation' => 'date:Y-m-d',
            ],
            'paymentAmount' => [
                'dbName' => 'pay_amount',
                'validation' => 'numeric',
            ],
            'paymentPayee' => [
                'dbName' => 'payee',
                'validation' => 'numeric',
            ],
        ];

        // Data validation
        $validator = new Validator();
        $validator->setMessages(
            [
                'numeric' => ':attribute is not a numeric value',
                'min' => ':attribute has a min value of :min',
                'date' => ':attribute value should be in :format format',
            ],
        );

        $validationRules = [];

        foreach ($allowedFields as $field => $fieldData) {
            $validationRules[$field] = $fieldData['validation'];
        }

        $validationRules['additionalDataItems'] = [
            function ($additionDataItems) {
                if (empty($additionDataItems)) {
                    return;
                }

                $payModuleCode = 'PAY';

                foreach ($additionDataItems as $item) {
                    foreach ($item as $fieldId => $fieldValue) {
                        if ($this->userDefinedFieldService->isFieldExists($payModuleCode, $fieldId) === false) {
                            return sprintf('Key %s in additionalDataItems does not exist in DB', $fieldId);
                        }

                        // This will validate value based on field type and length
                        $fieldValidation = $this->userDefinedFieldService->validateFieldValues($payModuleCode, $fieldId, $fieldValue);
                        if ($fieldValidation !== true) {
                            return sprintf('%s %s', $fieldId, $fieldValidation);
                        }
                    }
                }
            },
        ];

        $validation = $validator->validate($data, $validationRules);

        if ($validation->fails() === true) {
            $response->getBody()->write(json_encode($validation->errors()->toArray()));

            return $response->withStatus(400)
                ->withHeader('Content-Type', 'application/json');
        }

        $payment = $this->db->prepare('SELECT recordid FROM payments WHERE recordid = :recordid')
            ->executeQuery(['recordid' => $id])
            ->fetchAllAssociative()[0];


        if (empty($payment)) {
            $response->getBody()->write(json_encode([]));

            return $response->withStatus(404)
                ->withHeader('Content-Type', 'application/json');
        }

        $fieldSql = '';
        $parameters = [];

        foreach ($data as $field => $value) {
            if (isset($allowedFields[$field])) {
                $fieldSql .= $allowedFields[$field]['dbName'] . ' = :' . $field . ',';
                $parameters[$field] = $value;
            }
        }

        $parameters['recordid'] = $id;

        if (!empty($fieldSql)) {
            $fieldSql = substr($fieldSql, 0, -1);
        }


        $sql = "UPDATE payments SET {$fieldSql} WHERE recordid = :recordid";

        $query = $this->db->prepare($sql);

        $this->db->beginTransaction();

        try {
            $query->executeStatement($parameters);

            $payModuleCode = 'PAY';
            $payModuleId = (new Module('PAY'))->getId();

            foreach ($data['additionalDataItems'] as $item) {
                foreach ($item as $fieldId => $fieldValue) {
                    if (!isset($fieldValue)) {
                        $this->userDefinedFieldService->deleteField($payModuleId, $fieldId, $id);
                    } elseif ($this->userDefinedFieldService->hasValueAlready($payModuleId, $payModuleCode, $fieldId, $id)) {
                        $this->userDefinedFieldService->updateField($payModuleId, $payModuleCode, $fieldId, $fieldValue, $id);
                    } else {
                        $this->userDefinedFieldService->saveField($payModuleCode, $payModuleId, $fieldId, $fieldValue, $id);
                    }
                }
            }
        } catch (Throwable $ex) {
            $this->db->rollBack();

            Log::critical('Error during update payment action', [
                'exception' => $ex,
                'paymentId' => $id,
            ]);

            $response->getBody()->write(json_encode(['error' => $ex->getMessage()]));

            return $response->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }

        $this->db->commit();

        $response->getBody()->write(json_encode([]));

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
