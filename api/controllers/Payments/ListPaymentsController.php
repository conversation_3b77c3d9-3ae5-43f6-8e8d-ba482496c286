<?php

declare(strict_types=1);

namespace api\controllers\Payments;

use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\generic\services\GenericExportService;
use src\logger\Facade\Log;
use Throwable;

use function array_slice;
use function count;

final class ListPaymentsController
{
    /** @readonly */
    private GenericExportService $exportService;

    public function __construct(GenericExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * @OA\Get(
     *     path="/payments",
     *     @OA\Parameter(in="query", name="limit", @OA\Schema(type="integer", format="int32")),
     *     @OA\Parameter(in="query", name="start", @OA\Schema(type="integer", format="int32")),
     *     @OA\Parameter(in="query", name="days_previous", @OA\Schema(type="integer", format="int32", minimum=1)),
     *     @OA\Response(
     *         response="200",
     *         description="Payments provided",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(@OA\Property(property="error", type="string", description="Indicates a validation error")),
     *                 @OA\Schema(type="array", @OA\Items(ref="#/components/schemas/ExportedPayment")),
     *             },
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getPayments(Request $request, Response $response): Response
    {
        $daysPrevious = $request->getQueryParams()['days_previous'];
        if (!$daysPrevious || !is_numeric($daysPrevious) || $daysPrevious <= 0) {
            $response->getBody()->write(json_encode([
                'Error' => 'daysPrevious parameter must be a positive integer.',
            ]));

            return $response;
        }

        $daysPrevious = max(0, (int) $daysPrevious);
        $limit = (int) $request->getQueryParams()['limit'];
        $start = (int) $request->getQueryParams()['start'];

        $links = [];
        $ownLink = "/api/payments?days_previous={$daysPrevious}";
        $ownLink .= isset($limit) ? "&limit={$limit}" : '';
        $ownLink .= isset($start) ? "&start={$start}" : '';

        $links['self'] = $ownLink;

        $ids = $this->exportService->getIdsByDaysPrevious('vw_payments', $daysPrevious);
        $size = count($ids);

        $idsToSend = $ids;
        if (isset($limit)) {
            $idsToSend = array_slice($ids, $start, $limit, true);
            $size = count($idsToSend);

            if (count($ids) > $start + $limit) {
                $nextStart = $start + $limit;
                $links['next'] = "/api/payments?days_previous={$daysPrevious}&limit={$limit}&start={$nextStart}";
            }

            if ($start > 0) {
                $previousStart = $start - $limit;
                if ($previousStart < 1) {
                    $previousStart = 0;
                }
                $links['prev'] = "/api/payments?days_previous={$daysPrevious}&limit={$limit}&start={$previousStart}";
            }
        }

        $parameters = [
            'limit' => $limit,
            'size' => $size,
            'start' => $start,
            'links' => $links,
        ];

        try {
            $jsonForExport = $this->exportService->constructJsonForExport($idsToSend, null, $parameters);
        } catch (Throwable $ex) {
            Log::critical('error found during get payment action', [
                'exception' => $ex,
            ]);

            echo $ex->getMessage();

            exit;
        }

        $response->getBody()->write($jsonForExport);

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
