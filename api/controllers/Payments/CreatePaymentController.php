<?php

declare(strict_types=1);

namespace api\controllers\Payments;

use app\models\generic\valueObjects\JSONData;
use app\models\generic\valueObjects\Module;
use app\services\idGenerator\RecordIdGenerator;
use DatixDBQuery;
use Doctrine\DBAL\Connection;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Rakit\Validation\Validator;
use src\logger\Facade\Log;
use src\udf\service\UserDefinedField;
use Throwable;
use DateTime;

final class CreatePaymentController
{
    /** @readonly */
    private RecordIdGenerator $idGenerator;

    /** @readonly */
    private UserDefinedField $userDefinedFieldService;

    /** @readonly */
    private Connection $db;

    public function __construct(
        RecordIdGenerator $idGenerator,
        UserDefinedField $userDefinedFieldService,
        Connection $db
    ) {
        $this->idGenerator = $idGenerator;
        $this->userDefinedFieldService = $userDefinedFieldService;
        $this->db = $db;
    }

    /**
     * @OA\Post(
     *     path="/payments/payment",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             required={"payDate", "payType", "paySubtype", "payAmount", "payBy", "payVatRate"},
     *             @OA\Property(property="incidentId", type="integer", nullable=true),
     *             @OA\Property(property="claimId", type="integer", nullable=true),
     *             @OA\Property(property="comId", type="integer", nullable=true),
     *             @OA\Property(property="payDate", type="string"),
     *             @OA\Property(property="payType", type="integer"),
     *             @OA\Property(property="paySubtype", type="integer"),
     *             @OA\Property(property="payAmount", type="integer", minimum=1),
     *             @OA\Property(property="payVat", type="integer"),
     *             @OA\Property(property="payTotal", type="integer"),
     *             @OA\Property(property="payBy", type="string"),
     *             @OA\Property(property="rfiId", type="integer"),
     *             @OA\Property(property="updateId", type="integer"),
     *             @OA\Property(property="updatedDate", type="string"),
     *             @OA\Property(property="updatedBy", type="integer"),
     *             @OA\Property(property="createdBy", type="integer"),
     *             @OA\Property(property="payNotes", type="string"),
     *             @OA\Property(property="payVatRate", type="integer", minimum=1),
     *             @OA\Property(property="respId", type="integer"),
     *             @OA\Property(property="payRecipient", type="integer"),
     *             @OA\Property(property="payCalcTotal", type="integer"),
     *             @OA\Property(property="payPayee", type="integer", minimum=1),
     *             @OA\Property(property="additionalDataItems", type="array", @OA\Items),
     *         ),
     *     ),
     *     @OA\Response(response="400", description="Validation error", @OA\Property(property="error", type="string")),
     *     @OA\Response(response="500", description="Internal server error", @OA\Property(property="error", type="string")),
     *     @OA\Response(response="201", description="Payment created"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function createPayment(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        if (!empty($data['incidentId']) && !empty($data['claimId'])) {
            $response->getBody()->write(json_encode(['error' => 'you can not have claimId and incidentId together']));

            return $response->withStatus(400)
                ->withHeader('Content-Type', 'application/json');
        }

        // Data validation
        $validator = new Validator();
        $validator->setMessages(
            [
                'required' => ':attribute is required',
                'numeric' => ':attribute is not a numeric value',
                'min' => ':attribute has a min value of :min',
                'date' => ':attribute value should be in :format format',
            ],
        );

        // Validation will check the following:
        // - If there is Claim ID, then Incident ID is not required
        // - If there is Incident ID, then Claim ID is not required
        // - It will check if Claim ID or Incident ID is in the database otherwise it will return validation error
        $validation = $validator->validate(
            $data,
            [
                'claimId' => [
                    'required_without:incidentId',
                    function ($claimId) {
                        $db = DatixDBQuery::PDO_fetch(
                            'SELECT recordid from claims_main WHERE recordid = :claimId',
                            ['claimId' => $claimId],
                        );

                        if ($db === false) {
                            return ':attribute is not in the database';
                        }
                    },
                ],
                'comId' => '',
                'incidentId' => [
                    'required_without:claimId',
                    function ($incidentId) {
                        $db = DatixDBQuery::PDO_fetch(
                            'SELECT recordid from incidents_main WHERE recordid = :incidentId',
                            ['incidentId' => $incidentId],
                        );

                        if ($db === false) {
                            return ':attribute is not in the database';
                        }
                    },
                ],
                'payDate' => 'required|date:Y-m-d',
                'payType' => 'required|numeric',
                'paySubtype' => 'required|numeric',
                'payAmount' => 'required|numeric|min:1',
                'payVat' => '',
                'payTotal' => '',
                'payBy' => 'required|alpha',
                'rfiId' => '',
                'updateId' => '',
                'updatedDate' => '',
                'updatedBy' => '',
                'createdBy' => '',
                'payNotes' => '',
                'payVatRate' => 'required|numeric|min:1',
                'respId' => '',
                'payRecipient' => '',
                'payCalcVatAmount' => '',
                'payCalcTotal' => '',
                'payPayee' => 'numeric|min:1',
                'additionalDataItems' => [
                    function ($additionDataItems) {
                        if (empty($additionDataItems)) {
                            return;
                        }

                        $payModuleCode = 'PAY';

                        foreach ($additionDataItems as $item) {
                            foreach ($item as $fieldId => $fieldValue) {
                                if ($this->userDefinedFieldService->isFieldExists($payModuleCode, $fieldId) === false) {
                                    return sprintf('Key %s in additionalDataItems does not exist in DB', $fieldId);
                                }

                                // This will validate value based on field type and length
                                $fieldValidation = $this->userDefinedFieldService->validateFieldValues($payModuleCode, $fieldId, $fieldValue);
                                if ($fieldValidation !== true) {
                                    return sprintf('%s %s', $fieldId, $fieldValidation);
                                }
                            }
                        }
                    },
                ],
            ],
        );

        if ($validation->fails() === true) {
            $response->getBody()->write(json_encode($validation->errors()->toArray()));

            return $response->withStatus(400)
                ->withHeader('Content-Type', 'application/json');
        }


        $recordId = $this->idGenerator->generateRecordId();

        $this->db->beginTransaction();
        // Insert new record from API call
        $query = $this->db->prepare(
            'INSERT into payments
                        (recordid, cla_id, com_id, inc_id, pay_date, pay_type, pay_subtype, pay_amount, pay_vat, pay_total,pay_by, rfi_id, updateid, updateddate, updatedby, createdby, PAY_NOTES, pay_vat_rate, resp_id, pay_recipient, payee)
                       VALUES
                        (:newRecordId ,:claimId,:comId,:incId,:payDate,:payType,:paySubtype,:payAmount,:payVat, :payTotal, :payBy, :rfiId, :updateId, :updatedDate, :updatedBy, :createdBy, :payNotes, :payVatRate, :respId, :payRecipient, :payPayee);',
        );

        try {
            $vatAmount = ($data['payVatRate'] / 100 * $data['payAmount']);
            $totalPay = $data['payAmount'] + ($data['payVatRate'] / 100 * $data['payAmount']);
            $query->executeStatement(
                [
                    'newRecordId' => $recordId,
                    'claimId' => $data['claimId'],
                    'comId' => $data['comId'],
                    'incId' => $data['incidentId'],
                    'payDate' => $data['payDate'],
                    'payType' => $data['payType'],
                    'paySubtype' => $data['paySubtype'],
                    'payAmount' => $data['payAmount'],
                    'payVat' => $vatAmount,
                    'payVatRate' => $data['payVatRate'],
                    'payTotal' => $totalPay,
                    'payBy' => $data['payBy'],
                    'rfiId' => '',
                    'updateId' => '',
                    'updatedDate' => (new DateTime())->format('Y-m-d'),
                    'updatedBy' => '',
                    'createdBy' => '',
                    'payNotes' => $data['payNotes'],
                    'respId' => '',
                    'payRecipient' => $data['payRecipient'],
                    'payPayee' => $data['payPayee'],
                ],
            );

            $moduleCode = 'PAY';
            $payModuleId = (new Module('PAY'))->getId();

            foreach ($data['additionalDataItems'] as $item) {
                foreach ($item as $fieldId => $fieldValue) {
                    if ($this->userDefinedFieldService->saveField($moduleCode, $payModuleId, $fieldId, $fieldValue, $recordId) === false) {
                        $response->getBody()->write(sprintf('Key %s in additionalDataItems does not exist in DB', $fieldId));

                        return $response;
                    }
                }
            }
        } catch (Throwable $ex) {
            $this->db->rollBack();

            Log::critical('Error during create payment action', [
                'exception' => $ex,
            ]);

            $response->getBody()->write(json_encode(['error' => $ex->getMessage()]));

            return $response->withStatus(500)
                ->withHeader('Content-Type', 'application/json');
        }

        $this->db->commit();


        $response->getBody()->write(json_encode([]));

        // TODO:
        // decide response format
        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }
}
