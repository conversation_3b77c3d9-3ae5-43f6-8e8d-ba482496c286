<?php

declare(strict_types=1);

namespace api\controllers;

use OpenApi\Annotations as OA;

/**
 * @noinspection PhpUnused - having to do this because of change in package.
 *
 * @see https://zircote.github.io/swagger-php/guide/faq.html#warning-required-oa-info-not-found
 *
 * @OA\OpenApi(
 *     @OA\Server(
 *     url="https://localhost.datix/capture/api",
 *     description="RLDatix Capture API",
 *     ),
 *     @OA\Info(
 *     version="1.0.0",
 *     title="RLDatix Capture API",
 *     description="API documentation for the Prince project"
 *     ),
 *     @OA\Components(
 *          @OA\Response(
 *              response="BadRequestV2Response",
 *              description="Invalid request parameters",
 *              @OA\JsonContent(
 *                  @OA\Property(property="type", type="string"),
 *                  @OA\Property(property="title", type="string"),
 *                  @OA\Property(property="status", type="integer"),
 *                  @OA\Property(property="detail", type="string"),
 *                  @OA\Property(
 *                      property="validation_messages",
 *                      @OA\AdditionalProperties(type="array", @OA\Items(type="string")),
 *                  ),
 *             ),
 *          ),
 *          @OA\Response(
 *              response="BadRequest",
 *              description="Bad Request response",
 *              @OA\JsonContent(
 *                  @OA\Property(property="type", type="string"),
 *                  @OA\Property(property="title", type="string"),
 *                  @OA\Property(property="status", type="integer", format="int32"),
 *                  @OA\Property(property="detail", type="string"),
 *                  example="{type: 'http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html', title: 'Bad Request', status: 400, details: 'Invalid Request'}",
 *              ),
 *          ),
 *          @OA\Response(
 *              response="ErrorResponse",
 *              description="Internal server error with nonstandard error response format",
 *              @OA\JsonContent(@OA\Property(property="error", type="string")),
 *          ),
 *          @OA\Response(
 *              response="MaintenanceModeResponse",
 *              description="System in maintnenace mode",
 *              @OA\JsonContent(@OA\Property(property="message", type="string")),
 *              @OA\Header(header="retry-after", @OA\Schema(type="integer")),
 *          ),
 *          @OA\Response(
 *              response="OverdueNotificationResponse",
 *              description="Overdue statuses returned",
 *              @OA\MediaType(
 *                  mediaType="application/ld+json",
 *                  @OA\Schema(
 *                      @OA\Property(
 *                          property="_embedded",
 *                          type="object",
 *                          @OA\AdditionalProperties(type="string"),
 *                      ),
 *                      @OA\Property(property="page", type="integer"),
 *                      @OA\Property(property="page_count", type="integer"),
 *                      @OA\Property(property="total_items", type="integer"),
 *                      @OA\Property(property="page_size", type="integer"),
 *                  ),
 *              ),
 *          ),
 *          @OA\Response(
 *              response="NotificationModuleRolesResponse",
 *              description="Module role returned",
 *              @OA\MediaType(
 *                  mediaType="application/ld+json",
 *                  @OA\Schema(
 *                      @OA\Property(
 *                          property="_embedded",
 *                          type="object",
 *                          @OA\AdditionalProperties(type="string"),
 *                      ),
 *                      @OA\Property(property="page", type="integer"),
 *                      @OA\Property(property="page_count", type="integer"),
 *                      @OA\Property(property="total_items", type="integer"),
 *                      @OA\Property(property="page_size", type="integer"),
 *                  ),
 *              ),
 *          ),
 *          @OA\SecurityScheme(
 *              securityScheme="bearerAuth",
 *              type="http",
 *              scheme="bearer",
 *              bearerFormat="JWT",
 *          ),
 *          @OA\SecurityScheme(
 *              securityScheme="basicAuth",
 *              type="http",
 *              scheme="basic",
 *          ),
 *     ),
 *     security={{"bearerAuth": {}}},
 * )
 * @OA\Schema(
 *     schema="ExportCodedField",
 *     @OA\Property(property="Value", type="string"),
 *     @OA\Property(property="Description", type="string"),
 * )
 * @OA\Schema(
 *     schema="TreeEntityOutput",
 *     @OA\Property(property="id", type="integer"),
 *     @OA\Property(property="parent_id", type="integer"),
 *     @OA\Property(property="title", type="string"),
 *     @OA\Property(property="root", type="integer"),
 *     @OA\Property(property="level", type="integer"),
 *     @OA\Property(property="left", type="integer"),
 *     @OA\Property(property="right", type="integer"),
 *     @OA\Property(property="path", type="string"),
 *     @OA\Property(property="status", type="integer"),
 * )
 * @OA\Post(
 *     path="/incidents/mobile/sawtak/v1",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/MobileIncidentInput")),
 *     @OA\Response(
 *         response="201",
 *         description="Incident created",
 *         @OA\JsonContent(ref="#/components/schemas/MobileIncidentAggregate"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/incidents/mobile/v2",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/MobileIncidentInput")),
 *     @OA\Response(
 *         response="201",
 *         description="Incident created",
 *         @OA\JsonContent(ref="#/components/schemas/MobileIncidentAggregate"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Get(
 *     path="/incidents",
 *     @OA\Parameter(in="query", name="days_previous", @OA\Schema(type="integer")),
 *     @OA\Parameter(in="query", name="login", @OA\Schema(type="string")),
 *     @OA\Response(
 *         response="201",
 *         description="Incidents returned",
 *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/ExportedIncident")),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Get(
 *     path="/incidents/learning-link-information/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
 *     @OA\Response(response="404", description="Incident not found"),
 *     @OA\Response(
 *         response="200",
 *         description="Learning link information returned",
 *         @OA\JsonContent(
 *             @OA\Property(property="eventDate", type="string"),
 *             @OA\Property(property="eventDescription", type="string"),
 *         ),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Get(
 *     path="/incidents/by-handler",
 *     @OA\Response(
 *         response="200",
 *         description="Incidents returned",
 *         @OA\JsonContent(
 *             type="object",
 *             @OA\AdditionalProperties(
 *                 @OA\Property(property="id", type="integer"),
 *                 @OA\Property(property="title", type="string"),
 *                 @OA\Property(property="recordid", type="string"),
 *                 @OA\Property(property="details", type="string"),
 *                 @OA\Property(property="due_date", type="string"),
 *                 @OA\Property(property="role", type="string"),
 *             ),
 *         ),
 *         @OA\Header(header="cache-control", @OA\Schema(type="string")),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/sync/medication/answers",
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 required={"answer_id", "incident_id"},
 *                 ref="#/components/schemas/IncidentMedicationLink"
 *             )
 *         )
 *      ),
 *     @OA\Response(
 *         response="201",
 *         description="Medicaions answered created",
 *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/IncidentMedicationLink")),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/sync/medication/answers/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer", format="int32")),
 *     @OA\Response(response="204", description="Successufully deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/sync/medication/global/{reference}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\Response(response="204", description="Medications deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/sync/medication/local/{reference}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\Response(response="204", description="Medications deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/sync/equipment/answers",
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(
 *                 required={"answer_id", "incident_id", "equipment_reference"},
 *                 ref="#/components/schemas/IncidentEquipmentLinkEntity",
 *             ),
 *         ),
 *     ),
 *     @OA\Response(
 *         response="201",
 *         description="Equipment answers created",
 *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/IncidentEquipmentLinkEntity")),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/sync/equipment/global/{reference}/{locale}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\PathParameter(name="locale", required=true, @OA\Schema(type="string")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/EquipmentEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Successfully updated",
 *         @OA\JsonContent(ref="#/components/schemas/EquipmentEntity"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/sync/equipment/local/{reference}/{locale}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\PathParameter(name="locale", required=true, @OA\Schema(type="string")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/EquipmentEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Successfully updated",
 *         @OA\JsonContent(ref="#/components/schemas/EquipmentEntity"),
 *     ),
 *     @OA\Response(
 *         response="201",
 *         description="Successfully created",
 *         @OA\JsonContent(ref="#/components/schemas/EquipmentEntity"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/sync/equipment/global/{reference}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\Response(response="204", description="Deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/sync/equipment/local/{reference}",
 *     @OA\PathParameter(name="reference", required=true, @OA\Schema(type="string")),
 *     @OA\Response(response="204", description="Deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/services/service",
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="node",
 *                 type="object",
 *                 @OA\Property(property="id", type="integer"),
 *                 @OA\Property(property="parent_id", type="integer"),
 *                 @OA\Property(property="root", type="integer"),
 *                 @OA\Property(property="level", type="integer"),
 *                 @OA\Property(property="left", type="integer"),
 *                 @OA\Property(property="right", type="integer"),
 *                 @OA\Property(property="status", type="integer", enum={0, 1, 2}),
 *                 @OA\Property(
 *                     property="path",
 *                     type="array",
 *                     minItems=1,
 *                     @OA\Items(
 *                         @OA\Property(property="id", type="integer"),
 *                         @OA\Property(
 *                             property="title",
 *                             type="object",
 *                             @OA\AdditionalProperties(type="string"),
 *                         ),
 *                         @OA\Property(property="left", type="integer", minimum=0),
 *                         @OA\Property(property="right", type="integer", minimum=0),
 *                     ),
 *                 ),
 *                 @OA\Property(property="cod_npsa_pd04", type="string"),
 *                 @OA\Property(property="cod_npsa_pd05", type="string"),
 *                 @OA\Property(property="cod_ko41_serv_area", type="string"),
 *                 @OA\Property(
 *                     property="tags",
 *                     type="array",
 *                     @OA\Items(@OA\Property(property="id", type="integer")),
 *                 ),
 *                 @OA\Property(
 *                     property="locations",
 *                     type="array",
 *                     @OA\Items(@OA\Property(property="id", type="integer")),
 *                 ),
 *             ),
 *             @OA\Property(property="tree", type="object"),
 *         ),
 *     ),
 *     @OA\Response(
 *         response="201",
 *         description="Service created",
 *         @OA\JsonContent(ref="#/components/schemas/ServiceEntity"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/services/service/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(response="204", description="Service deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Get(
 *     path="/services/tag",
 *     @OA\Response(
 *         response="200",
 *         description="Service tags returned",
 *         @OA\JsonContent(
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/TreeTagEntity"),
 *         ),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/services/tag",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(response="201", description="Service tag created", @OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/services/tag/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(response="200", description="Service tag updated", @OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/services/tag/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(response="204", description="Service tag deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/locations/location",
 *     @OA\RequestBody(
 *         @OA\JsonContent(
 *             @OA\Property(property="node", type="object"),
 *             @OA\Property(property="tree", type="object"),
 *         ),
 *     ),
 *     @OA\Response(
 *         response="200",
 *         description="Location created",
 *         @OA\JsonContent(
 *             @OA\Property(property="id", type="integer"),
 *             @OA\Property(property="parent_id", type="integer"),
 *             @OA\Property(property="title", type="string"),
 *             @OA\Property(property="root", type="integer"),
 *             @OA\Property(property="level", type="integer"),
 *             @OA\Property(property="left", type="integer"),
 *             @OA\Property(property="right", type="integer"),
 *             @OA\Property(
 *                 property="path",
 *                 type="array",
 *                 minItems=1,
 *                 @OA\Items(
 *                     @OA\Property(property="id", type="integer"),
 *                     @OA\Property(
 *                         property="title",
 *                         type="object",
 *                         @OA\AdditionalProperties(type="string"),
 *                     ),
 *                     @OA\Property(property="left", type="integer", minimum=0),
 *                     @OA\Property(property="right", type="integer", minimum=0),
 *                 ),
 *             ),
 *             @OA\Property(property="status", type="integer", enum={0, 1, 2}),
 *         ),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/locations/location/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(response="204", description="Location deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * ),
 * @OA\Get(
 *     path="/locations/tag",
 *     @OA\Response(
 *         response="200",
 *         description="Location tags returned",
 *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/TreeTagEntity")),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Post(
 *     path="/locations/tag",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(
 *         response="201",
 *         description="Location tag created",
 *         @OA\JsonContent(ref="#/components/schemas/TreeTagEntity"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/locations/tag/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeTagEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Location tag updated",
 *         @OA\JsonContent(ref="#/components/schemas/TreeTagEntity"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Delete(
 *     path="/locations/tag/{id}",
 *     @OA\PathParameter(name="id", required=true, @OA\Schema(type="integer")),
 *     @OA\Response(response="204", description="Location tag deleted"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Get(
 *     path="/v2/feedback",
 *     @OA\Parameter(in="query", name="limit", @OA\Schema(type="integer", minimum=0, maximum=25)),
 *     @OA\Parameter(in="query", name="offset", @OA\Schema(type="integer", minimum=0)),
 *     @OA\Parameter(in="query", name="where[feedback.name]", @OA\Schema(type="string", maxLength=32)),
 *     @OA\Parameter(in="query", name="where[feedback.location]", @OA\Schema(type="integer")),
 *     @OA\Parameter(in="query", name="where[feedback.firstReceived]", @OA\Schema(type="string", maxLength=10)),
 *     @OA\Response(
 *         response="200",
 *         description="Feedback records returned",
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="data",
 *                 type="array",
 *                 @OA\Items(type="object"),
 *             ),
 *             @OA\Property(
 *                 property="metadata",
 *                 @OA\Property(
 *                     property="resultset",
 *                     @OA\Property(property="count", type="integer"),
 *                     @OA\Property(property="offset", type="integer"),
 *                     @OA\Property(property="limit", type="integer"),
 *                 ),
 *             ),
 *         ),
 *     ),
 *     @OA\Response(response="400", ref="#/components/responses/BadRequestV2Response"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Patch(
 *     path="/v2/feedback/{id}",
 *     @OA\PathParameter(name="id", @OA\Schema(type="integer", minimum=1, format="int32")),
 *     @OA\RequestBody(
 *         @OA\JsonContent(type="object"),
 *     ),
 *     @OA\Response(
 *         response="200",
 *         description="Feedback updated",
 *         @OA\JsonContent(type="object"),
 *     ),
 *     @OA\Response(response="403", description="Access forbidden", ref="#/components/responses/BadRequest"),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 */
interface ApiInterface
{
}
