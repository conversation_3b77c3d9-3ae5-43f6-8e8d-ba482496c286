<?php

namespace api\controllers;

use api\service\DraftLocationService;
use app\models\generic\valueObjects\JSONData;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use src\nrls\ApiException;
use Teapot\StatusCode\Http;
use Throwable;

class DraftLocationsController
{
    private EntityManager $entityManager;
    private DraftLocationService $draftLocationService;
    private Connection $connection;
    private LoggerInterface $logger;

    public function __construct(EntityManager $entityManager, DraftLocationService $draftLocationService, LoggerInterface $logger)
    {
        $this->entityManager = $entityManager;
        $this->draftLocationService = $draftLocationService;

        $this->connection = $this->entityManager->getConnection();
        $this->logger = $logger;
    }

    /**
     * @OA\Post(
     *     path="/draft-locations/draft-location",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="page", type="integer"),
     *             @OA\Property(
     *                 property="_embedded",
     *                 @OA\Property(
     *                     property="data",
     *                     type="array",
     *                     @OA\Items(type="object", ref="#/components/schemas/DraftLocationHydratorInput"),
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="201",
     *         description="Location draft created",
     *         @OA\JsonContent(@OA\Property(property="auto_increment", type="integer")),
     *     ),
     *     @OA\Response(response="400", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws ApiException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function add(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data)) {
            throw new ApiException('Empty request body', Http::BAD_REQUEST);
        }

        $pageNumber = $data['page'] ? (int) $data['page'] : null;

        if (!$pageNumber || $pageNumber < 0) {
            throw new ApiException('No page number included', Http::BAD_REQUEST);
        }

        $treeData = $data['_embedded']['data'] ?? [];

        if (empty($treeData)) {
            throw new ApiException('Tree data missing', Http::BAD_REQUEST);
        }

        $this->logger->notice('DRAFT_LOCATION - Attempting to add batch number ' . $pageNumber);

        if ($pageNumber === 1) {
            if (!$this->draftLocationService->allowNewDraftToCommence()) {
                throw new ApiException(
                    'Attempting to trigger a new publish sync with data in the tables',
                    Http::INTERNAL_SERVER_ERROR,
                );
            }
        }

        $this->logger->notice('DRAFT_LOCATION - Attempting format data into entities');

        $tree = $this->draftLocationService->formatDataIntoEntities($treeData, $data['auto_increment']);

        try {
            $this->connection->beginTransaction();

            foreach ($tree as $node) {
                $this->entityManager->persist($node);
            }

            $this->entityManager->flush();
            $this->entityManager->commit();
        } catch (Throwable $e) {
            $this->connection->rollBack();
            $this->logger->error($e->getMessage(), [
                'stack_trace' => $e->getTraceAsString(),
            ]);

            throw new ApiException('Error occurred when saving batch', Http::INTERNAL_SERVER_ERROR, $e);
        }

        $this->logger->notice('DRAFT_LOCATION - Success');

        $response->getBody()->write(json_encode(['auto_increment' => $this->draftLocationService->getAutoIncrement()]));

        return $response->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Get(
     *     path="/draft-locations/counts",
     *     @OA\Response(
     *         response="200",
     *         description="Counts returned",
     *         @OA\JsonContent(
     *             @OA\Property(property="count-locations", type="integer"),
     *             @OA\Property(property="count-titles", type="integer"),
     *             @OA\Property(property="count-tags", type="integer"),
     *             @OA\Property(property="count-services", type="integer"),
     *             @OA\Property(property="count-id-numbers", type="integer"),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getCounts(Request $request, Response $response): Response
    {
        try {
            $responseData = $this->draftLocationService->getCounts();
        } catch (Throwable $e) {
            throw new ApiException('Error occurred when getting counts', Http::INTERNAL_SERVER_ERROR, $e);
        }

        $response->getBody()->write(json_encode($responseData));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/draft-locations/discard-drafts",
     *     @OA\Response(response="200", description="Location drafts discarded"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function discardDrafts(Request $request, Response $response): Response
    {
        try {
            $this->draftLocationService->discardDrafts();

            return $response->withStatus(Http::OK)
                ->withHeader('Content-Type', 'application/json');
        } catch (Throwable $e) {
            throw new ApiException('Error occurred discarding drafts', Http::INTERNAL_SERVER_ERROR, $e);
        }
    }

    /**
     * @OA\Post(
     *     path="/draft-locations/promote-drafts",
     *     @OA\Response(response="200", description="Draft locations promoted"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function promoteDraftLocations(Request $request, Response $response): Response
    {
        $this->logger->notice('DRAFT_LOCATION - Beginning draft promotion');

        try {
            if (!$this->draftLocationService->allowPromotionToCommence()) {
                throw new ApiException(
                    'Attempting to trigger a new promotion with not enough draft data in the tables',
                    Http::INTERNAL_SERVER_ERROR,
                );
            }

            $this->connection->beginTransaction();

            try {
                $this->logger->notice('DRAFT_LOCATION - Draft tables cleared.');
                $this->draftLocationService->deleteLocationData($this->connection);
            } catch (Throwable $e) {
                throw new ApiException('DRAFT_LOCATION - Failed to delete draft data. ', Http::INTERNAL_SERVER_ERROR, $e);
            }

            try {
                $this->draftLocationService->promoteDraftsToMain($this->connection);
                $this->connection->commit();
            } catch (Throwable $e) {
                throw new ApiException('DRAFT_LOCATION - Failed to promote draft locations to main. ', Http::INTERNAL_SERVER_ERROR, $e);
            }
        } catch (Throwable $e) {
            $this->connection->rollBack();

            throw new ApiException('DRAFT_LOCATION - Error occurred during draft promotion.', Http::INTERNAL_SERVER_ERROR, $e);
        }

        $this->draftLocationService->discardDrafts();

        $this->logger->notice('DRAFT_LOCATION - Successful promotion of drafts.');

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
