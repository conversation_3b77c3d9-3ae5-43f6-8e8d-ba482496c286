<?php

namespace api\controllers;

use app\models\actions\entities\ActionEntity;
use app\models\actions\hydrators\ActionHydrator;
use app\models\generic\valueObjects\JSONData;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\logger\Facade\Log;
use Teapot\StatusCode\Http;
use Throwable;

class ActionsController
{
    /** @var EntityManager */
    private $entityManager;

    /** @var ActionHydrator */
    private $actionHydrator;

    /** @var EntityRepository */
    private $repository;

    public function __construct(
        EntityManager $entityManager,
        ActionHydrator $actionHydrator,
        EntityRepository $repository
    ) {
        $this->entityManager = $entityManager;
        $this->actionHydrator = $actionHydrator;
        $this->repository = $repository;
    }

    /**
     * @OA\Post(
     *     path="/actions/action",
     *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/ActionEntity")),
     *     @OA\Response(response="200", description="Successful operation", @OA\JsonContent(ref="#/components/schemas/ActionEntity")),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller action
     */
    public function create(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $actionEntity = $this->actionHydrator->hydrate($data);

        $this->entityManager->persist($actionEntity);
        $this->entityManager->flush();

        $repository = $this->entityManager->getRepository(ActionEntity::class);

        /** @var ActionEntity $action */
        $action = $repository->find($data['id']);

        $response->getBody()->write(json_encode($this->actionHydrator->extract($action)));

        return $response->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Put(
     *     path="/actions/action/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/ActionEntity")),
     *     @OA\Response(response="200", description="Successful operation", @OA\JsonContent(ref="#/components/schemas/ActionEntity")),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws Exception if the ID specified in $request does not match $carltonAction::getRecordid() after hydration
     * @throws \Doctrine\ORM\ORMException if $this->entityManager is closed
     *
     * @noinspection PhpUnused - Controller action
     */
    public function update(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $carltonAction = $this->actionHydrator->hydrate($data);

        if ($carltonAction->getRecordid() !== (int) $request->getAttribute('id')) {
            throw new Exception('ID specified does not match action object ID');
        }

        $princeAction = $this->repository->find($carltonAction->getRecordid());

        if ($princeAction) {
            $this->entityManager->remove($princeAction);
            $this->entityManager->flush();
        }
        $this->entityManager->persist($carltonAction);
        $this->entityManager->flush();

        /** @var ActionEntity $princeActionAfterUpdate */
        $princeActionAfterUpdate = $this->repository->find($carltonAction->getRecordid());

        $response->getBody()->write(json_encode($this->actionHydrator->extract($princeActionAfterUpdate)));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/actions/link-information",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             type="array", @OA\Items(type="integer", format="int32"),
     *             example="[1, 11, 111]",
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             example="{'11': {'module': 'INC', 'recordid': 22}}",
     *             @OA\Items(
     *                 @OA\Property(property="module", type="string"),
     *                 @OA\Property(property="recordid", type="integer", format="int32"),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller action
     */
    public function getLinkInformation(Request $request, Response $response): Response
    {
        $ids = (new JSONData($request->getBody()))->toArray();

        $data = [];
        foreach ($ids as $id) {
            /** @var ActionEntity $action */
            $action = $this->entityManager->find(ActionEntity::class, $id);
            if ($action === null) {
                $data[$id] = null;

                continue;
            }

            $data[$id] = [
                'module' => $action->getActModule(),
                'recordid' => $action->getActCasId(),
            ];
        }

        $response->getBody()->write(json_encode($data));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Delete(
     *     path="/actions/action/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(response="204", description="Successful operation"),
     *     @OA\Response(response="400", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="404", description="Not Found", @OA\JsonContent(@OA\Property(property="error", type="string"))),
     *     @OA\Response(response="500", description="Internal Server Error", @OA\JsonContent(@OA\Property(property="error", type="string"))),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller action
     */
    public function delete(Request $request, Response $response): Response
    {
        $actionId = $request->getAttribute('id');

        if (!$actionId || !is_numeric($actionId)) {
            $response->getBody()->write(json_encode([
                'error' => 'Wrong action ID format',
            ]));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $repository = $this->entityManager->getRepository(ActionEntity::class);

        /** @var ActionEntity $action */
        $action = $repository->find($actionId);

        if (!$action instanceof ActionEntity) {
            $response->getBody()->write(json_encode([
                'error' => 'No record found for action ID ' . $actionId,
            ]));

            return $response->withStatus(Http::NOT_FOUND)
                ->withHeader('Content-Type', 'application/json');
        }

        try {
            $this->entityManager->remove($action);
            $this->entityManager->flush();
        } catch (Throwable $e) {
            Log::critical('Error during delete Action controller action', [
                'exception' => $e,
                'actionId' => $actionId,
            ]);

            $response->getBody()->write(json_encode(['error' => 'Application failure: ' . $e->getMessage()]));

            return $response->withStatus(HTTP::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        return $response->withStatus(Http::NO_CONTENT);
    }
}
