<?php

declare(strict_types=1);

namespace api\controllers\Learnings;

use app\models\generic\valueObjects\JSONData;
use app\services\learnings\LearningsService;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

/**
 * @OA\Schema(
 *     schema="LearningEntity",
 *     type="object",
 *     @OA\Property(property="id", type="integer"),
 *     @OA\Property(
 *         property="moduleLinks",
 *         type="array",
 *         minItems=1,
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="module", type="string"),
 *             @OA\Property(property="id", type="integer"),
 *             @OA\Property(property="moduleId", type="integer"),
 *         ),
 *     ),
 * )
 */
class LearningsController
{
    private LearningsService $learningsService;

    public function __construct(LearningsService $learningsService)
    {
        $this->learningsService = $learningsService;
    }

    /**
     * @OA\Post(
     *     path="/learnings/learning",
     *     @OA\RequestBody(
     *         @OA\JsonContent(ref="#/components/schemas/LearningEntity"),
     *     ),
     *     @OA\Response(
     *         response="201",
     *         description="Learning created",
     *         @OA\JsonContent(ref="#/components/schemas/LearningEntity"),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function create(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $learningId = $data['id'];
        $carltonModuleCode = $data['moduleLinks'][0]['module'];
        $moduleLinkId = $data['moduleLinks'][0]['id'];
        $recordId = $data['moduleLinks'][0]['moduleId'];

        if (empty($learningId) || empty($carltonModuleCode) || empty($moduleLinkId) || empty($recordId)) {
            throw new Exception('Incomplete data provided');
        }

        if ($carltonModuleCode === 'Incidents') {
            $moduleCode = 'INC';
        } else {
            $moduleCode = $carltonModuleCode === 'Feedback' ? 'COM' : null;
        }

        if (empty($moduleCode)) {
            throw new Exception('Incorrect data provided');
        }

        $savedLearning = $this->learningsService->save(
            $moduleCode,
            $recordId,
            $learningId,
            $data['moduleLinks'][0]['eventDate'],
            $data['moduleLinks'][0]['eventDescription'],
            $data['moduleLinks'][0]['id'],
        );

        $response->getBody()->write(json_encode($savedLearning->getForPatch()));

        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }
}
