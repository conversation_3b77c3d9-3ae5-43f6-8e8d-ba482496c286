<?php

declare(strict_types=1);

namespace api\controllers\Incidents\Mobile;

use app\models\generic\valueObjects\JSONData;
use app\models\mobile\adaptors\MobileIncidentAggregateAdaptor;
use app\models\mobile\MobileIncidentRepository;
use app\services\transcription\Transcriber;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Throwable;

class IncidentsMobileV1Controller
{
    private MobileIncidentAggregateAdaptor $adaptor;
    private MobileIncidentRepository $repository;
    private Transcriber $transcriber;
    private bool $isTranscriberEnabled;

    public function __construct(
        MobileIncidentAggregateAdaptor $adaptor,
        MobileIncidentRepository $repository,
        Transcriber $transcriber,
        bool $isTranscriberEnabled
    ) {
        $this->adaptor = $adaptor;
        $this->repository = $repository;
        $this->transcriber = $transcriber;
        $this->isTranscriberEnabled = $isTranscriberEnabled;
    }

    /**
     * @OA\Post(
     *     path="/incidents/mobile",
     *     security={},
     *     @OA\RequestBody(
     *         @OA\JsonContent(type="object"),
     *     ),
     *     @OA\Response(
     *         response="201",
     *         description="Incident created",
     *         @OA\JsonContent(type="object"),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function create(Request $request, Response $response): Response
    {
        try {
            $data = (new JSONData($request->getBody()))->toArray();

            $aggregate = $this->adaptor->adaptFromRequest($data);

            $incidentAggregate = $this->repository->insert($aggregate);
            $incidentId = $aggregate->getIncident()->getId();
            $language = $data['language'];

            $savedAggregate = $this->repository->get($incidentId);
            $savedAggregateArray = $savedAggregate->toArray();
            $document = $incidentAggregate->getDocument();

            if ($document !== null && !$this->isTranscriberEnabled) {
                $savedAggregate->getIncident()->setDescription(null);
                $this->repository->update($savedAggregate);
                $savedAggregateArray['description']['text'] = null;
            } elseif ($document !== null && $this->isTranscriberEnabled) {
                $transcription = $this->transcriber->transcribe((string) $incidentId[0], $language, $incidentAggregate->getDocument()->getFileName());
                $caption = $transcription['caption'];
                $savedAggregate->getIncident()->setDescription($caption);
                $this->repository->update($savedAggregate);
                $savedAggregateArray['description']['text'] = $caption;
            }
        } catch (Throwable $e) {
            throw new Exception('Internal server error', 500, $e);
        }

        $response->getBody()->write(json_encode($savedAggregateArray));

        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }
}
