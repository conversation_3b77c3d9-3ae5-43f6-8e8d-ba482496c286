<?php

declare(strict_types=1);

namespace api\controllers\LocationServiceLinks;

use app\models\generic\valueObjects\JSONData;
use app\services\locationservicelinks\LocationServiceLinksService;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Throwable;

class LocationServiceLinksController
{
    private LocationServiceLinksService $locationServiceLinksService;

    public function __construct(LocationServiceLinksService $locationServiceLinksService)
    {
        $this->locationServiceLinksService = $locationServiceLinksService;
    }

    /**
     * @OA\Post(
     *     path="/location-service-links",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/LocationsServicesLink"),
     *         ),
     *     ),
     *     @OA\Response(response="201", description="Links created"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws Throwable
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function setLocationServiceLinks(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        if (empty($data)) {
            throw new Exception('No data provided');
        }

        $this->locationServiceLinksService->save($data);

        return $response->withStatus(201);
    }
}
