<?php

declare(strict_types=1);

namespace api\controllers\Languages;

use app\models\generic\valueObjects\JSONData;
use app\services\languages\LanguagesService;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\admin\services\JwtService;

class LanguagesController
{
    private LanguagesService $languagesService;
    private JwtService $jwtService;

    public function __construct(LanguagesService $languagesService, JwtService $jwtService)
    {
        $this->languagesService = $languagesService;
        $this->jwtService = $jwtService;
    }

    /**
     * @OA\Post(
     *     path="/languages/setUserLanguage",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="JWTToken", type="string"),
     *             @OA\Property(property="language", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="200", description="User language set"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function setUserLanguage(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $jwtToken = $this->jwtService->decode($data['JWTToken']);
        $jwtSession = $jwtToken['sessionId'];

        $this->languagesService->setUserLanguage($jwtSession, $data['language'], $data['userIdentity']);

        return $response->withStatus(200);
    }
}
