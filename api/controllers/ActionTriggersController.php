<?php

declare(strict_types=1);

namespace api\controllers;

use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\actiontriggers\model\ActionTrigger;
use src\actiontriggers\model\ActionTriggerCollection;
use src\actiontriggers\model\ActionTriggerMapper;
use src\framework\query\Query;
use Teapot\StatusCode\Http;

use function count;

class ActionTriggersController
{
    private ActionTriggerMapper $mapper;
    private ActionTriggerCollection $collection;

    public function __construct(ActionTriggerMapper $mapper, ActionTriggerCollection $collection)
    {
        $this->mapper = $mapper;
        $this->collection = $collection;
    }

    /**
     * @OA\Delete(
     *     path="/action-triggers",
     *     @OA\Parameter(in="query", name="title", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="description", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="module", @OA\Schema(type="string")),
     *     @OA\Parameter(in="query", name="query_id", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", name="action_plan_id", @OA\Schema(type="integer")),
     *     @OA\Parameter(in="query", name="recordid", @OA\Schema(type="integer")),
     *     @OA\Response(response="200", description="Successful operation"),
     *     @OA\Response(response="204", description="Nothing has been deleted"),
     *     @OA\Response(response="400", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="500", description="Internal Server Error", ref="#/components/responses/BadRequest"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller action
     */
    public function delete(Request $request, Response $response): Response
    {
        $filter = $request->getQueryParams();
        if (empty($filter)) {
            throw new Exception('Missing query parameters. Please provide filter conditions for deletion.', Http::BAD_REQUEST);
        }

        $invalidProperties = array_diff(array_keys($filter), ActionTrigger::getProperties());
        if (!empty($invalidProperties)) {
            $propertyText = count($invalidProperties) > 1 ? 'properties' : 'property';

            throw new Exception(
                sprintf(
                    'Invalid ActionTrigger %s: "%s". Valid properties: "%s".',
                    $propertyText,
                    implode('","', $invalidProperties),
                    implode('","', ActionTrigger::getProperties()),
                ),
                Http::BAD_REQUEST,
            );
        }

        $this->collection->setQuery((new Query())->where($filter));

        if ($this->collection->isEmpty()) {
            // Don't respond with a 404 if there are no matching triggers, since this doesn't indicate a problem necessarily
            // and we don't want to trigger downstream problems by returning a 4xx code
            return $response->withStatus(Http::OK);
        }

        $failedResults = [];
        $numberOfTriggers = $this->collection->count();

        foreach ($this->collection as $actionTrigger) {
            $success = $this->mapper->delete($actionTrigger);
            if (!$success) {
                $failedResults[] = $actionTrigger->recordid;
            }
        }

        if (!empty($failedResults)) {
            if (count($failedResults) === $numberOfTriggers) {
                $code = Http::INTERNAL_SERVER_ERROR;
                $message = 'Delete failed for all ActionTriggers, IDs ' . implode(', ', $failedResults);
            } else {
                $code = Http::OK;
                $message = 'Partial success. Delete failed for IDs ' . implode(', ', $failedResults);
            }

            throw new Exception($message, $code);
        }

        return $response->withStatus(Http::NO_CONTENT);
    }
}
