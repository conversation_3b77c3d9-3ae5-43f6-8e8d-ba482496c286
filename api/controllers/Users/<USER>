<?php

declare(strict_types=1);

namespace api\controllers\Users;

use api\service\User\UpdateUsersService;
use app\models\generic\valueObjects\JSONData;
use app\models\user\hydrators\UserHydrator;
use app\services\profiles\ProfileService;
use DateTime;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

use const JSON_THROW_ON_ERROR;

final class UpdateUsersController
{
    /** @readonly */
    private UserHydrator $userHydrator;

    /** @readonly */
    private ProfileService $profileService;
    private UpdateUsersService $service;

    public function __construct(
        UserHydrator $userHydrator,
        ProfileService $profileService,
        UpdateUsersService $service
    ) {
        $this->userHydrator = $userHydrator;
        $this->profileService = $profileService;
        $this->service = $service;
    }

    /**
     * Insert / update a user.
     *
     * @OA\Put(
     *     path="/users/user[/{id}]",
     *     description="Create / update a user",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(
     *         description="body of the user",
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UserEntity"),
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/UserEntity"),
     *     ),
     *     @OA\Response(response=400, ref="#/components/responses/BadRequest"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=500, description="Internal Server Error"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function update(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        if ((int) $data['id'] !== (int) $request->getAttribute('id')) {
            throw new Exception('ID specified does not match user object ID');
        }

        if (!empty($data['captureProfileId']) && !$this->profileService->doesProfileExist($data['captureProfileId'])) {
            throw new Exception('Profile does not exist');
        }

        if (!isset($data['lastSynced'])) {
            $data['lastSynced'] = (new DateTime())->format('Y-m-d H:i:s.000');
        }

        $user = $this->service->update($data);

        $response->getBody()->write(
            json_encode($this->userHydrator->extract($user), JSON_THROW_ON_ERROR),
        );

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
