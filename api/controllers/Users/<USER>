<?php

declare(strict_types=1);

namespace api\controllers\Users;

use app\models\user\entities\UserEntity;
use app\services\carlton\user\UserService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

final class LogoutFormUrlController
{
    /** @readonly  */
    private EntityManagerInterface $entityManager;

    /** @readonly */
    private EntityRepository $repository;

    /** @readonly */
    private UserService $userService;

    public function __construct(
        EntityManagerInterface $entityManager,
        EntityRepository $repository,
        UserService $userService
    ) {
        $this->entityManager = $entityManager;
        $this->repository = $repository;
        $this->userService = $userService;
    }

    /**
     * Returns the users default form on logout.
     *
     * @OA\Get(
     *     path="/users/logout-form-url/{id}",
     *     description="Retrieve user's form on logout",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="url", type="string"),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getUrl(Request $request, Response $response): Response
    {
        $id = (int) $request->getAttribute('id');
        /** @var UserEntity $princeUser */
        $currentUser = $this->repository->find($id);
        $logoutUrl = $this->userService->getDefaultFormOnLogout($currentUser);
        $this->entityManager->flush();

        $response->getBody()->write(json_encode(['url' => $logoutUrl]));

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
