<?php

declare(strict_types=1);

namespace api\controllers\Users;

use app\models\generic\valueObjects\JSONData;
use app\models\user\entities\UserEntity;
use app\models\user\hydrators\UserHydrator;
use app\services\profiles\ProfileService;
use app\services\user\UserCreator;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

final class CreateUsersController
{
    /** @readonly */
    private UserCreator $userCreator;

    /** @readonly */
    private ProfileService $profileService;

    /** @readonly */
    private UserHydrator $userHydrator;

    public function __construct(
        UserCreator $userCreator,
        ProfileService $profileService,
        UserHydrator $userHydrator
    ) {
        $this->userCreator = $userCreator;
        $this->profileService = $profileService;
        $this->userHydrator = $userHydrator;
    }

    /**
     * Insert a user.
     *
     * @OA\Post(
     *     path="/users/user",
     *     description="Create a User",
     *     @OA\RequestBody(
     *         description="body of the user",
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/UserEntity"),
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation",
     *         @OA\JsonContent(ref="#/components/schemas/UserEntity"),
     *     ),
     *     @OA\Response(response=400, ref="#/components/responses/BadRequest"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=500, description="Internal Server Error"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function create(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        if (!empty($data['captureProfileId']) && !$this->profileService->doesProfileExist($data['captureProfileId'])) {
            throw new Exception('Profile does not exist');
        }

        if (empty($data['captureProfileId'])) {
            $profile = $this->profileService->getDefaultProfileId();

            if ($profile) {
                $data['captureProfileId'] = $profile;
            }
        }

        /** @var UserEntity $user $user */
        $user = $this->userCreator->create($data);

        $response->getBody()->write(json_encode($this->userHydrator->extract($user)));

        return $response->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }
}
