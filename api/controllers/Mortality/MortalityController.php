<?php

declare(strict_types=1);

namespace api\controllers\Mortality;

use api\helpers\UsersHelper;
use app\models\generic\valueObjects\JSONData;
use app\services\mortality\MortalityService;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

use function in_array;

class MortalityController
{
    private MortalityService $mortalityService;

    public function __construct(MortalityService $mortalityService)
    {
        $this->mortalityService = $mortalityService;
    }

    /**
     * @OA\Post(
     *     path="/mortalities/mortality",
     *     description="Create Mortality Review",
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="mortality", ref="#/components/schemas/MortalityEntity"),
     *             @OA\Property(property="contacts", type="array", @OA\Items(ref="#/components/schemas/ContactEntity")),
     *         ),
     *     ),
     *     @OA\Response(response=400, ref="#/components/responses/BadRequest"),
     *     @OA\Response(response=500, description="Internal Server Error"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function save(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();
        $contactsData = $data['contacts'] ?? [];

        // Validate that a mortality record should be opened
        $contactsTypes = array_combine(array_keys($contactsData), array_column($contactsData, 'link_type'));
        if (!in_array('P', $contactsTypes)) {
            throw new Exception('No patient has been defined on the request');
        }

        foreach ($contactsTypes as $key => $contactsType) {
            if ($contactsType === 'P') {
                // Do not create mortality record for patients who are not deceased
                if ($contactsData[$key]['deceased'] !== 'Y' && !$contactsData[$key]['dateOfDeath']) {
                    throw new Exception('Patient is not marked as deceased');
                }
            }
        }

        $token = $request->getAttribute('token');
        $user = UsersHelper::getUserFromJWT($token);

        $responseData = $this->mortalityService->save($data, $user);

        $response->getBody()->write(json_encode($responseData));

        return $response->withStatus(HTTP::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * Return a list of mortality where the current user is marked as the handler to be used by the to-do list.
     *
     * @OA\Get(
     *     path="/mortality/by-handler",
     *     description="Returns a list of mortalities related to the logged in uses",
     *     @OA\Response(
     *         response=200,
     *         description="Successfull response",
     *         @OA\JsonContent(
     *             example="[{id: 1, title: 'Mortaility Review', url: 'https://localhost.datix/capture/index.php?action=record&module=MOR&recordid=1', details: 'Stage 1 review', due_date: '2019-02-10 15:00:00', role: 'Reviewer'}]"
     *         ),
     *     ),
     *     @OA\Response(response=400, ref="#/components/responses/BadRequest"),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized Access",
     *         @OA\JsonContent(example="{'error': 'Authentication failure: Decoded JWT is empty.'}"),
     *     ),
     *     @OA\Response(response=500, description="Internal Server Error"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getByHandler(Request $request, Response $response): Response
    {
        $token = $request->getAttribute('token');
        $user = UsersHelper::getUserFromJWT($token);
        if ($user === null) {
            return UsersHelper::getEmptyJWTResponse($response);
        }

        $results = $this->mortalityService->getByHandler($user->initials);

        $response->getBody()->write(json_encode($results));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json')
            ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
    }
}
