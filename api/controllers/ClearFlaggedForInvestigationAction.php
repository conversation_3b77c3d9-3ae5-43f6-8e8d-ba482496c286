<?php

declare(strict_types=1);

namespace api\controllers;

use api\validators\ClearFlaggedForInvestigationValidator;
use app\models\generic\valueObjects\JSONData;
use app\Repository\RepositoryFactory;
use app\services\audit\FullAudit;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Source\classes\Exceptions\InvalidRequestParameterException;
use Teapot\StatusCode\Http;

use const JSON_THROW_ON_ERROR;

class ClearFlaggedForInvestigationAction
{
    private const FLAG_FOR_INVESTIGATION_REMOVAL_REASON = 'Flag for investigation was removed due to investigation being deleted';
    private const FLAG_FOR_INVESTIGATION_FIELD_KEY = 'flag_for_investigation';
    private ClearFlaggedForInvestigationValidator $validator;
    private FullAudit $fullAudit;
    private RepositoryFactory $repositoryFactory;

    public function __construct(
        ClearFlaggedForInvestigationValidator $validator,
        FullAudit $fullAudit,
        RepositoryFactory $repositoryFactory
    ) {
        $this->validator = $validator;
        $this->fullAudit = $fullAudit;
        $this->repositoryFactory = $repositoryFactory;
    }

    /**
     * @OA\Post(
     *     path="/clear-flagged-for-investigation",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             required={"recordId", "moduleCode"},
     *             @OA\Property(property="recordId", type="integer", minimum=1),
     *             @OA\Property(property="moduleCode", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="400", description="Validation error"),
     *     @OA\Response(
     *         response="404",
     *         description="Entity not found",
     *         @OA\JsonContent(@OA\Property(property="error", type="string")),
     *     ),
     *     @OA\Response(response="200", description="Record cleared for investigation"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Microcontroller Action
     */
    public function __invoke(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        try {
            $this->validator->validateData($data);
        } catch (InvalidRequestParameterException $e) {
            $response->getBody()->write(json_encode($e->getErrors(), JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $moduleRepository = $this->repositoryFactory->getModuleRepository($data['moduleCode']);
        if (!$moduleRepository->recordExists($data['recordId'])) {
            $response->getBody()->write(json_encode(['error' => 'Entity not found'], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::NOT_FOUND)
                ->withHeader('Content-Type', 'application/json');
        }

        $moduleRepository->setFlagForInvestigationToNull($data['recordId']);
        $this->auditLogChangeFlagForInvestigation($data['moduleCode'], $data['recordId']);

        return $response;
    }

    private function auditLogChangeFlagForInvestigation(string $moduleCode, int $recordId): void
    {
        $this->fullAudit->add(
            $moduleCode,
            $recordId,
            FullAudit::SOURCE_API_UPDATE,
            [self::FLAG_FOR_INVESTIGATION_FIELD_KEY => self::FLAG_FOR_INVESTIGATION_REMOVAL_REASON],
            [],
        );
    }
}
