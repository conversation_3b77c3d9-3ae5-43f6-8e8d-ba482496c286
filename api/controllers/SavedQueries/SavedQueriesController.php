<?php

declare(strict_types=1);

namespace api\controllers\SavedQueries;

use app\models\generic\valueObjects\Module;
use app\models\generic\valueObjects\RecordID;
use app\services\savedQuery\RelatedEventsSavedQueriesProvider;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;

use function count;

class SavedQueriesController
{
    private RelatedEventsSavedQueriesProvider $provider;

    public function __construct(RelatedEventsSavedQueriesProvider $provider)
    {
        $this->provider = $provider;
    }

    /**
     * @OA\Get(
     *     path="/saved-queries/saved-queries/{module}",
     *     @OA\PathParameter(name="module", required=true, @OA\Schema(type="string")),
     *     @OA\Response(
     *         response="200",
     *         description="Saved queries provided",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *             ),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getByModule(Request $request, Response $response): Response
    {
        $module = new Module($request->getAttribute('module'));

        $response->getBody()->write(json_encode($this->provider->getQueries($module)));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Get(
     *     path="/saved-queries/details",
     *     @OA\Parameter(in="query", name="ids", description="A comma separated list of IDs", @OA\Schema(type="string")),
     *     @OA\Response(response="400", ref="#/components/responses/BadRequest"),
     *     @OA\Response(
     *         response="200",
     *         description="Saved query details returned",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 oneOf={
     *                     @OA\Schema(
     *                         description="Query details",
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="module", type="string"),
     *                         @OA\Property(property="title", type="string"),
     *                         @OA\Property(property="url", type="string"),
     *                         @OA\Property(property="count", type="integer"),
     *                         @OA\Property(property="success", type="boolean"),
     *                     ),
     *                     @OA\Schema(
     *                         description="Error when fetching query details",
     *                         type="object",
     *                         @OA\Property(property="id", type="integer"),
     *                         @OA\Property(property="success", type="boolean"),
     *                         @OA\Property(property="error", type="string"),
     *                     ),
     *                 },
     *             ),
     *         ),
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getDetails(Request $request, Response $response): Response
    {
        $ids = array_filter(explode(',', $request->getQueryParams()['ids'] ?? ''));

        if (count($ids) === 0) {
            throw new Exception('The ids parameter cannot be left empty', Http::BAD_REQUEST);
        }

        $idList = [];
        foreach ($ids as $id) {
            $idList[] = new RecordID($id);
        }

        $response->getBody()->write(json_encode($this->provider->getQueryDetails($idList)));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }
}
