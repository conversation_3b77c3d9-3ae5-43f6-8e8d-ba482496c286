<?php

declare(strict_types=1);

namespace api\controllers\Feedback;

use api\helpers\GenericModuleFunctionHelper;
use app\models\contact\hydrators\CarltonContactHydrator;
use app\models\contact\hydrators\ContactLinksHydrator;
use app\models\contact\repositories\ContactRepository;
use app\models\feedback\hydrators\FeedbackHydrator;
use app\models\feedback\hydrators\FeedbackLinksHydrator;
use app\models\feedback\hydrators\FeedbackSubjectHydrator;
use app\models\generic\RecordSources;
use app\models\generic\valueObjects\JSONData;
use app\models\generic\valueObjects\Module;
use app\services\document\storagehandlers\DocumentApiUploadService;
use app\services\idGenerator\RecordIdGenerator;
use app\services\records\RecordSourceService;
use app\services\udf\UdfService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Ramsey\Uuid\Uuid;
use src\contacts\service\ContactPersister;
use src\generic\services\SaveMainRecordService;

use function is_array;

class CreateFeedbackController
{
    private RecordSourceService $recordSourceService;
    private EntityManagerInterface $entityManager;
    private RecordIdGenerator $feedbackIdGenerator;
    private RecordIdGenerator $linkComplIdGenerator;
    private FeedbackHydrator $hydrator;
    private UdfService $udfService;
    private DocumentApiUploadService $documentUploadService;
    private FeedbackSubjectHydrator $subjectHydrator;
    private CarltonContactHydrator $carltonContactHydrator;
    private ContactPersister $contactPersister;
    private ContactLinksHydrator $contactLinksHydrator;
    private SaveMainRecordService $saveMainRecordService;
    private RecordIdGenerator $complSubjectsIdGenerator;
    private ContactRepository $contactRepository;
    private FeedbackLinksHydrator $linksHydrator;
    private EntityRepository $feedbackRepository;

    public function __construct(
        RecordSourceService $recordSourceService,
        EntityManagerInterface $entityManager,
        RecordIdGenerator $feedbackIdGenerator,
        RecordIdGenerator $linkComplIdGenerator,
        RecordIdGenerator $complSubjectsIdGenerator,
        FeedbackHydrator $hydrator,
        UdfService $udfService,
        DocumentApiUploadService $documentUploadService,
        FeedbackSubjectHydrator $subjectHydrator,
        CarltonContactHydrator $carltonContactHydrator,
        ContactPersister $contactPersister,
        ContactLinksHydrator $contactLinksHydrator,
        SaveMainRecordService $saveMainRecordService,
        ContactRepository $contactRepository,
        FeedbackLinksHydrator $linksHydrator,
        EntityRepository $feedbackRepository
    ) {
        $this->recordSourceService = $recordSourceService;
        $this->entityManager = $entityManager;
        $this->feedbackIdGenerator = $feedbackIdGenerator;
        $this->linkComplIdGenerator = $linkComplIdGenerator;
        $this->complSubjectsIdGenerator = $complSubjectsIdGenerator;
        $this->hydrator = $hydrator;
        $this->udfService = $udfService;
        $this->documentUploadService = $documentUploadService;
        $this->subjectHydrator = $subjectHydrator;
        $this->carltonContactHydrator = $carltonContactHydrator;
        $this->contactPersister = $contactPersister;
        $this->contactLinksHydrator = $contactLinksHydrator;
        $this->saveMainRecordService = $saveMainRecordService;
        $this->contactRepository = $contactRepository;
        $this->linksHydrator = $linksHydrator;
        $this->feedbackRepository = $feedbackRepository;
    }

    /**
     * @OA\Post(
     *     path="/feedback/feedback",
     *     description="Create new feedback",
     *     @OA\RequestBody(
     *         description="body of the feedback",
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/FeedbackEntity"),
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="eventDate", type="string"),
     *             @OA\Property(property="eventDescription", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function create(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $feedbackData = $data['feedback'] ?: [];
        $subjectsData = $data['subjects'] ?: [];
        $extraFieldsData = $data['extraFields'] ?: [];
        $contactsData = $data['contacts'] ?: [];
        $documentData = $data['documents'] ?: [];
        $progressNotesData = $data['progressNotes'] ?? [];
        $notepadData = $data['notepad'] ?? [];

        if (empty($feedbackData['source'])) {
            $feedbackData['source_of_record'] = $this->recordSourceService->getCodeForSource(RecordSources::THIRD_PARTY);
        } else {
            $feedbackData['source_of_record'] = $feedbackData['source'];
        }

        $this->entityManager->beginTransaction();

        try {
            $feedbackData['id'] = $this->feedbackIdGenerator->generateRecordId();

            if (!empty($subjectsData)) {
                $feedbackData['subjects_linked'] = 'Y';
            }

            if (empty($feedbackData['uuid'])) {
                $feedbackData['uuid'] = Uuid::uuid4()->toString();
            }

            $feedbackEntity = $this->hydrator->hydrate($feedbackData);

            $this->entityManager->persist($feedbackEntity);
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Exception $e) {
            $this->entityManager->getConnection()->rollBack();

            throw $e;
        }

        $this->udfService->upsert($extraFieldsData, Module::FEEDBACK, $feedbackData['id']);
        $this->documentUploadService->upload(Module::FEEDBACK, $feedbackData['id'], $documentData);

        // Hydrate linked subjects
        foreach ($subjectsData as $subjectData) {
            $feedbackSubjectRecordId = $this->complSubjectsIdGenerator->generateRecordId();

            $subjectData['id'] = $feedbackSubjectRecordId;

            $feedbackSubjectEntity = $this->subjectHydrator->hydrate($subjectData, $feedbackEntity);

            // Persist new feedback subject record
            $this->entityManager->persist($feedbackSubjectEntity);
        }

        $genericFunctionHydrator = new GenericModuleFunctionHelper(Module::FEEDBACK, $feedbackData['id']);

        // Hydrate progress notes
        $progressNoteEntities = $genericFunctionHydrator->hydrateProgressNotes($progressNotesData);
        foreach ($progressNoteEntities as $progressNoteEntity) {
            // Persist new progress note
            $this->entityManager->persist($progressNoteEntity);
        }

        // Hydrate notepad notes
        $notepadEntities = $genericFunctionHydrator->hydrateNotepad($notepadData);
        foreach ($notepadEntities as $notepadEntity) {
            // Persist new notepad note
            $this->entityManager->persist($notepadEntity);
        }

        $persistedContacts = [];
        $duplicateContacts = [];
        foreach ($contactsData as $contactData) {
            // Hydrate contact entity
            // Check if contact already exists in Prince
            $contact = $this->contactRepository->findExistingContacts($contactData);

            if (is_array($contact)) {
                if (isset($contact['duplicatesFound'])) {
                    $duplicatesFound = $contact['duplicatesFound'];
                    unset($contact['duplicatesFound']);
                }
            }

            // If contact exists, we cannot enforce any updates here as contacts ACL prevents modifying of contacts without a user id;
            // else persist new contact record
            if (empty($contact)) {
                $contactData['id'] = null;
                $contactEntity = $this->carltonContactHydrator->hydrate($contactData);

                $conId = $this->contactPersister->addContact($contactEntity);
                $PostedContact['recordid'] = $conId;
                if (!empty($duplicatesFound)) {
                    $duplicateContacts[$conId] = $duplicatesFound;
                }
                $contact = $this->contactRepository->find($PostedContact['recordid']);
            }

            // Set link data
            $contactLinkData = [
                'com_id' => $feedbackData['id'],
                'link_role' => $contactData['link_role'],
                'link_type' => $contactData['link_type'],
                'link_deceased' => $contactData['deceased'],
            ];

            // Persist link data
            $contactLinksEntity = $this->contactLinksHydrator->hydrate($contactLinkData, $contact);
            $this->entityManager->persist($contactLinksEntity);

            $contactData['lcom_current'] = 'Y';
            $contactData['lcom_primary'] = 'Y';

            // Persist link complainant data
            if ($contactData['link_type'] === 'C') {
                $feedbackLinkstRecordId = $this->linkComplIdGenerator->generateRecordId();
                $feedbackLinksEntity = $this->linksHydrator->hydrate($contactData, [
                    'id' => $feedbackLinkstRecordId,
                    'feedbackId' => $feedbackData['id'],
                    'feedbackEntity' => $feedbackEntity,
                ], $contact);
                $this->entityManager->persist($feedbackLinksEntity);
            }

            $persistedContacts[] = $this->carltonContactHydrator->extract($contact);
        }

        $this->entityManager->flush();

        $feedback = $this->feedbackRepository->find($feedbackData['id']);

        // Trigger email notifications
        $this->saveMainRecordService->sendEmailNotifications(
            $this->hydrator->extract($feedback),
            Module::FEEDBACK,
            $feedback->getRecordid(),
            1,
        );

        $responseData = [
            'feedback_id' => $feedback->getRecordid(),
            'contacts' => $persistedContacts,
        ];

        if (!empty($duplicateContacts)) {
            $responseString = 'Unable to match contact so a new one has been created.';
            foreach ($duplicateContacts as $createdId => $duplicateIds) {
                $responseData['error'][] = [
                    'createdContactId' => $createdId,
                    'duplicateContactIds' => $duplicateIds,
                    'message' => $responseString,
                ];
            }
        }

        $response->getBody()->write(json_encode($responseData));

        return $response->withStatus(201)
            ->withHeader('Content-Type', 'application/json');
    }
}
