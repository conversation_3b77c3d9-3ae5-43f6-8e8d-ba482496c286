<?php

declare(strict_types=1);

namespace api\controllers\Feedback;

use DatixDBQuery;
use InvalidArgumentException;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

class FeedbackController
{
    /**
     * @OA\Get(
     *     path="/feedback/learning-link-information/{id}",
     *     description="description",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="eventDate", type="string", format="date-time"),
     *             @OA\Property(property="eventDescription", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Feedback not found",
     *         @OA\JsonContent(example="[]"),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal Server Error or ID is missing",
     *         @OA\JsonContent,
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function fetchLearningLink(Request $request, Response $response): Response
    {
        $feedbackId = (int) $request->getAttribute('id');

        if (empty($feedbackId)) {
            throw new InvalidArgumentException('Invalid id', 500);
        }

        $sql = 'SELECT com_dincident AS eventDate, com_detail AS eventDescription FROM compl_main WHERE recordid = :recordid';
        $result = DatixDBQuery::PDO_fetch($sql, ['recordid' => $feedbackId]);

        if (empty($result)) {
            $response->getBody()->write(json_encode([]));

            return $response->withStatus(404)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode($result));

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
