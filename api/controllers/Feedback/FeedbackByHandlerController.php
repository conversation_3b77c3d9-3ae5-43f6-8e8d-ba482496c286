<?php

declare(strict_types=1);

namespace api\controllers\Feedback;

use api\helpers\UsersHelper;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\complaints\model\FeedbackMapper;
use src\system\language\LanguageSession;

class FeedbackByHandlerController
{
    private FeedbackMapper $feedbackMapper;
    private LanguageSession $languageSession;

    public function __construct(FeedbackMapper $feedbackMapper, LanguageSession $languageSession)
    {
        $this->feedbackMapper = $feedbackMapper;
        $this->languageSession = $languageSession;
    }

    /**
     * @OA\Get(
     *     path="/feedback/by-handler",
     *     @OA\Response(
     *         response="200",
     *         description="Feedback found",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\AdditionalProperties(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer"),
     *                 @OA\Property(property="title", type="string"),
     *                 @OA\Property(property="url", type="string"),
     *                 @OA\Property(property="role", type="string"),
     *             ),
     *         ),
     *         @OA\Header(header="cache-control", @OA\Schema(type="string")),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function fetchByHandler(Request $request, Response $response): Response
    {
        $results = [];

        $token = $request->getAttribute('token');
        $user = UsersHelper::getUserFromJWT($token);

        if ($user === null) {
            return UsersHelper::getEmptyJWTResponse($response);
        }

        $feedbackData = $this->feedbackMapper->findOpenByHandlerManagerAndInvestigator($user->initials);

        foreach ($feedbackData as $key => $feedback) {
            $results[$key]['id'] = $feedback['recordid'];
            $results[$key]['title'] = $feedback['com_name'];

            if ($feedback['recordid']) {
                $results[$key]['url'] = getenv('BASE_URL') . 'index.php?action=record&module=COM&recordid=' . $feedback['recordid'];
            }

            $this->feedbackMapper->setOverdueAndStatus($feedback, $results[$key]);

            $roles = [];

            if ($feedback['com_mgr'] == $user->initials) {
                $roles[] = $this->languageSession->getFieldString('FEEDBACK', 'com_mgr');
            }

            if ($feedback['com_head'] == $user->initials) {
                $roles[] = $this->languageSession->getFieldString('FEEDBACK', 'com_head');
            }

            if ($feedback['com_investigator'] == $user->initials) {
                $roles[] = $this->languageSession->getFieldString('FEEDBACK', 'com_investigator');
            }

            $results[$key]['role'] = implode(', ', $roles);
        }

        $response->getBody()->write(json_encode($results));

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json')
            ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
    }
}
