<?php

declare(strict_types=1);

namespace api\controllers\Safeguarding;

use api\helpers\UsersHelper;
use app\services\safeguarding\SafeguardingService;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\InvalidArgumentException;
use Teapot\StatusCode\Http;

class SafeguardingController
{
    private SafeguardingService $safeguardingService;

    public function __construct(SafeguardingService $safeguardingService)
    {
        $this->safeguardingService = $safeguardingService;
    }

    /**
     * @OA\Get(
     *     path="/safeguarding/by-handler",
     *     @OA\Response(
     *         response="200",
     *         description="Safeguardings returned",
     *         @OA\JsonContent,
     *         @OA\Header(header="cache-control", @OA\Schema(type="string")),
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="Authentication failed",
     *         @OA\Property(property="error", type="string"),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getByHandler(Request $request, Response $response): Response
    {
        $token = $request->getAttribute('token');
        $user = UsersHelper::getUserFromJWT($token);

        if ($user === null) {
            return UsersHelper::getEmptyJWTResponse($response);
        }

        $results = $this->safeguardingService->getByHandler($user->getInitials());

        $response->getBody()->write(json_encode($results));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json')
            ->withHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate');
    }

    /**
     * @OA\Get(
     *     path="/safeguarding/learning-link-information/{id}",
     *     description="description",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             @OA\Property(property="eventDate", type="string", format="date-time"),
     *             @OA\Property(property="eventDescription", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="404",
     *         description="Safeguarding not found",
     *         @OA\JsonContent(example="[]"),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Internal Server Error or ID is missing",
     *         @OA\JsonContent,
     *     ),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getByLearningLinkInformation(Request $request, Response $response): Response
    {
        $safeguardingId = (int) $request->getAttribute('id');
        if (empty($safeguardingId)) {
            throw new InvalidArgumentException('Invalid id', 500);
        }

        $result = $this->safeguardingService->getByLearningLinkInformation($safeguardingId);

        if (empty($result)) {
            $response->getBody()->write(json_encode([]));

            return $response->withStatus(404)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode($result));

        return $response->withStatus(200)
            ->withHeader('Content-Type', 'application/json');
    }
}
