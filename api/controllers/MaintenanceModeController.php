<?php

declare(strict_types=1);

namespace api\controllers;

use api\validators\RequestValidatorInterface;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\admin\services\MaintenanceModeService;
use Teapot\StatusCode\Http;
use Throwable;

class MaintenanceModeController
{
    private MaintenanceModeService $modeService;
    private RequestValidatorInterface $validator;

    public function __construct(MaintenanceModeService $modeService, RequestValidatorInterface $validator)
    {
        $this->modeService = $modeService;
        $this->validator = $validator;
    }

    /**
     * @OA\Post(
     *     path="/maintenance-mode",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="isMaintenanceModeEnabled", type="boolean"),
     *         ),
     *     ),
     *     @OA\Response(response="204", description="Maintenance mode configuration set"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function setMaintenanceModeEnabled(Request $request, Response $response): Response
    {
        if (!$this->validator->validate($request)) {
            throw new Exception('Invalid request body', Http::BAD_REQUEST);
        }

        try {
            $requestBody = $request->getParsedBody();
            $this->modeService->setMaintenanceModeEnabled($requestBody['isMaintenanceModeEnabled']);
        } catch (Throwable $e) {
            throw new Exception('Error occurred when saving value.', Http::INTERNAL_SERVER_ERROR, $e);
        }

        return $response->withStatus(Http::NO_CONTENT);
    }
}
