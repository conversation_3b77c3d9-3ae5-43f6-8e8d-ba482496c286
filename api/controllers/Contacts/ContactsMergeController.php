<?php

declare(strict_types=1);

namespace api\controllers\Contacts;

use api\validators\Contacts\ContactMergeValidator;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\contacts\Exceptions\LeadContactNotFoundException;
use src\contacts\service\ContactMergeService;
use src\framework\json\Json;
use src\logger\Facade\Log;
use src\system\language\LanguageSession;
use Teapot\StatusCode\Http;
use Throwable;

class ContactsMergeController
{
    private ContactMergeValidator $validator;
    private ContactMergeService $mergeService;
    private LanguageSession $language;

    public function __construct(
        ContactMergeValidator $validator,
        ContactMergeService $mergeService,
        LanguageSession $session
    ) {
        $this->validator = $validator;
        $this->mergeService = $mergeService;
        $this->language = $session;
    }

    /**
     * @OA\Post(
     *     path="/contacts/merge",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="contacts",
     *                 type="object",
     *                 @OA\AdditionalProperties(type="array", @OA\Items(type="integer")),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Validation failed",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="409",
     *         description="Lead contact not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string"),
     *         ),
     *     ),
     *     @OA\Response(response="204", description="Contact merging completed successfully"),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function merge(Request $request, Response $response): Response
    {
        if (!$this->validator->validate($request)) {
            $response->getBody()->write(Json::encode([
                'error' => $this->language->getStringByDomain('api_error_invalid_format', 'API.CONTACTS.MERGING'),
            ]));

            return $response->withStatus(Http::BAD_REQUEST)
                ->withHeader('Content-Type', 'application/json');
        }

        $contacts = $request->getParsedBody();

        try {
            $this->mergeService->merge($contacts['contacts']);
        } catch (LeadContactNotFoundException $e) {
            $response->getBody()->write(Json::encode([
                'error' => $this->language->getStringByDomain('api_error_lead_contact_not_found', 'API.CONTACTS.MERGING'),
            ]));

            return $response->withStatus(Http::CONFLICT)
                ->withHeader('Content-Type', 'application/json');
        } catch (Throwable $exception) {
            Log::critical('Error during contact merging', [
                'exception' => $exception,
            ]);

            $response->getBody()->write(Json::encode([
                'error' => $this->language->getStringByDomain('api_error_internal_error', 'API.CONTACTS.MERGING'),
            ]));

            return $response->withStatus(Http::INTERNAL_SERVER_ERROR)
                ->withHeader('Content-Type', 'application/json');
        }

        return $response->withStatus(Http::NO_CONTENT);
    }
}
