<?php

declare(strict_types=1);

namespace api\controllers\Contacts;

use app\models\contact\hydrators\CarltonContactHydrator;
use app\models\generic\valueObjects\JSONData;
use app\models\user\entities\UserEntity;
use app\models\user\hydrators\UserHydrator;
use Doctrine\DBAL\Exception\RetryableException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\ORM\ORMException;
use Exception;
use JsonException;
use OpenApi\Annotations as OA;
use Psr\Log\LoggerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\contacts\Exceptions\ContactNotFoundException;
use src\contacts\service\ContactService;
use Teapot\StatusCode\Http;
use Throwable;

use const JSON_THROW_ON_ERROR;

class ContactsController
{
    private CarltonContactHydrator $contactHydrator;
    private UserHydrator $userHydrator;
    private ContactService $service;
    private LoggerInterface $logger;

    public function __construct(
        CarltonContactHydrator $contactHydrator,
        UserHydrator $userHydrator,
        ContactService $service,
        LoggerInterface $logger
    ) {
        $this->contactHydrator = $contactHydrator;
        $this->userHydrator = $userHydrator;
        $this->service = $service;
        $this->logger = $logger;
    }

    /**
     * @OA\Put(
     *     path="/contacts/contact/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="200", description="Contact updated", @OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="500", description="Internal server error or contact not found"),
     * )
     * @OA\Patch(
     *     path="/contacts/contact/{id}",
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="200", description="Contact updated", @OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="500", description="Internal server error or contact not found"),
     * )
     *
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws \Doctrine\ORM\ORMException
     * @throws JsonException
     * @throws Exception
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function update(Request $request, Response $response): Response
    {
        $body = (new JSONData($request->getBody()))->toArray();
        $contactEntity = $this->service->update((int) $request->getAttribute('id'), $body);

        $response->getBody()->write(json_encode($this->contactHydrator->extract($contactEntity), JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/contacts/contact",
     *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="201", description="Contact replaced or created", @OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     * )
     *
     * @throws OptimisticLockException
     * @throws Throwable
     * @throws ORMException
     * @throws JsonException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function replaceOrCreate(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $contact = $this->service->createOrReplaceContact($data);

        $response->getBody()->write(json_encode($this->contactHydrator->extract($contact), JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/contact/match",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             required={"repalce", "repalceWith"},
     *             @OA\Property(property="replace", type="integer"),
     *             @OA\Property(property="replaceWith", type="integer"),
     *             @OA\Property(property="update", type="array", @OA\Items),
     *         ),
     *     ),
     *     @OA\Response(response="201", description="Contact replaced or created", @OA\JsonContent(ref="#/components/schemas/ContactEntity")),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws Throwable
     * @throws JsonException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function match(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $princeContactAfterUpdate = $this->service->replace(
            $data['replace'],
            $data['replaceWith'],
            $data['update'] ?? [],
        );

        $response->getBody()->write(json_encode($this->contactHydrator->extract($princeContactAfterUpdate), JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/contacts/links/{contactId}",
     *     @OA\Parameter(name="contactId", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="userId", type="integer"),
     *             @OA\Property(property="userIdentity", type="integer"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Links found",
     *         @OA\JsonContent(
     *             @OA\Property(property="incidents", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *             @OA\Property(property="feedback", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *             @OA\Property(property="claims", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *             @OA\Property(property="mortalities", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *             @OA\Property(property="redresses", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *             @OA\Property(property="safeguardings", type="array", @OA\Items(ref="#/components/schemas/ContactLinksEntity")),
     *         ),
     *     ),
     *     @OA\Response(response="404", description="Contact not found"),
     * )
     *
     * @throws JsonException
     * @throws Exception
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function links(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();
        $userId = $data['userId'] ?? $data['userIdentity']['id'] ?? null;
        $contactId = (int) $request->getAttribute('contactId');

        try {
            $links = $this->service->getLinks($userId, $contactId);

            $response->getBody()->write(json_encode($links, JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::OK)
                ->withHeader('Content-Type', 'application/json');
        } catch (ContactNotFoundException $e) {
            $response->getBody()->write('The contact for which you are requesting links does not exist');

            return $response->withStatus(Http::NOT_FOUND);
        }
    }

    /**
     * @OA\Get(
     *     path="/contacts/{contactId}/user",
     *     @OA\Parameter(name="contactId", in="path", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(response="200", description="User found"),
     *     @OA\Response(response="404", description="User not found"),
     * )
     *
     * @throws JsonException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function getUserByContactId(Request $request, Response $response): Response
    {
        $conId = (int) $request->getAttribute('contactId');

        /** @var UserEntity|null $user */
        $user = $this->service->getUserByContactId($conId);

        if (!$user) {
            $response->getBody()->write(json_encode(['user' => null], JSON_THROW_ON_ERROR));

            return $response->withStatus(Http::NOT_FOUND)
                ->withHeader('Content-Type', 'application/json');
        }

        $response->getBody()->write(json_encode(['user' => $this->userHydrator->extract($user)], JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Delete(
     *     path="/contacts/contact/{contactId}",
     *     @OA\PathParameter(name="contactId", required=true, @OA\Schema(type="integer", format="int32")),
     *     @OA\Response(response="204", description="Contact deleted"),
     * )
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function delete(Request $request, Response $response): Response
    {
        $contactId = (int) $request->getAttribute('contactId');

        $this->service->delete($contactId);

        return $response->withStatus(Http::NO_CONTENT);
    }

    /**
     * @OA\Post(
     *     path="/contacts/verify-timestamps",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer"),
     *             @OA\Property(property="lastUpdatedDate", type="string", nullable=true),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Contacts sync result provided",
     *         @OA\JsonContent(
     *             @OA\Property(property="match", type="array", @OA\Items(type="integer")),
     *             @OA\Property(property="nonMatch", type="array", @OA\Items(type="integer")),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws JsonException
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws Exception
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function verifyTimestamps(Request $request, Response $response): Response
    {
        $postData = (new JSONData($request->getBody()))->toArray();

        $this->logger->info('Verify list of contacts whether they are in sync with capture contacts');

        $result = $this->service->doContactsRequireSyncing($postData);

        $this->logger->info('Capture contacts that are not in sync with carlton contacts are in nonMatch array.');

        $response->getBody()->write(json_encode([
            'match' => $result->getMatch(),
            'nonMatch' => $result->getNonMatch(),
        ], JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)->withHeader('Content-Type', 'application/json');
    }

    /**
     * @OA\Post(
     *     path="/contacts/batch",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/ContactEntity"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Contact updated",
     *         @OA\JsonContent(
     *             @OA\Property(property="new", type="integer", minimum=0),
     *             @OA\Property(property="updated", type="integer", minimum=0),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws RetryableException
     * @throws Throwable
     * @throws JsonException
     *
     * @noinspection PhpUnused - Controller Action
     */
    public function batch(Request $request, Response $response): Response
    {
        $contactsData = (new JSONData($request->getBody()))->toArray();

        $contactsBatchUpdateResult = $this->service->batchUpdate($contactsData);

        $response->getBody()->write(json_encode([
            'new' => $contactsBatchUpdateResult->getNew(),
            'updated' => $contactsBatchUpdateResult->getUpdated(),
        ], JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)->withHeader('Content-Type', 'application/json');
    }
}
