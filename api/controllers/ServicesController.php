<?php

namespace api\controllers;

use api\service\LocationsServicesData\LocationsServicesDataFactory;
use api\V2\Services\Services\ServicesService;
use app\models\service\hydrators\ServiceHydrator;
use OpenApi\Annotations as OA;

/**
 * @OA\Put(
 *     path="/services/service/import",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/services/service",
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 * @OA\Put(
 *     path="/services/service/{id}",
 *     @OA\PathParameter(name="id", @OA\Schema(type="integer")),
 *     @OA\RequestBody(@OA\JsonContent(ref="#/components/schemas/TreeImportEntity")),
 *     @OA\Response(
 *         response="200",
 *         description="Imported successfully",
 *         @OA\JsonContent(ref="#/components/schemas/TreeEntityOutput"),
 *     ),
 *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
 * )
 */
class ServicesController extends AbstractAreaController
{
    public function __construct(
        ServiceHydrator $serviceHydrator,
        ServicesService $servicesService,
        LocationsServicesDataFactory $locationsServicesFactory
    ) {
        parent::__construct($servicesService, $serviceHydrator, $locationsServicesFactory);
    }
}
