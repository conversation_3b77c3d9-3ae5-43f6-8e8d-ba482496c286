<?php

declare(strict_types=1);

namespace api\docs\schemas\Contacts;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedContact",
 *     @OA\Property(property="contactEventLinkId", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="id", type="string"),
 *     @OA\Property(property="contactLinkType", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkRole", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDear", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkJobTitle", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDeceased", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactRiddorInjuryType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNumberOfDaysAway", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkIsRiddor", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTreatementRecieved", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPrimaryInjury", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPrimaryBodyPart", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkLastUpdatedDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkUpdatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAge", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNPSA", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMentalHealthActSection", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkCPA", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkStaffRoleNPSA", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAbsenceStart", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAbsenceEnd", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkBecameUnconscious", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkRemainInHostpitalFor24Hours", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPersonalProperyDamaged", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDirectOrIndirectContactMade", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPhysicalInjuryCaused", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPublicDisorder", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkHarassmentOrMaliciousCommunications", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPoliceShouldFollowUp", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAttemptedAssault", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkVerbalAbuse", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAgeBand", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDateOfAdmission", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkReasonPoliceShouldNotFollowUp", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactlinkNdependents", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactlinkFullPayInjuryDay", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactlinkSalaryContinued", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactVersion", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactTitle", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactForenames", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactMiddleName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactSurname", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLine1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLine2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLine3", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactCounty", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactCountry", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactCity", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactState", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactPostcode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactTelephoneNumber1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactTelephoneNumber2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactDateOfBirth", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactGender", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactPatientOrStaffNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactContactEmailAddress", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactEthnicity", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactSubtype", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactDateOfDeath", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactNHSNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactSocialSecurityNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactTaxId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLanguage", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactDisibility", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLocation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactService", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactCreatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactPoliceOfficerNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLoneWorkerRiskAssessed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactReligion", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactSexualOrientation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactNotes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactHasDocuments", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLawsonNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactFTE", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLocationCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactDepartment", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactSupervisorName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactJobCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactProcessLevel", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactEmploymentStatusCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactDateHired", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkIllness", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkHasIllness", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTotalDaysAway", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTotalRestrictedTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkOnRestrictedTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkHoursWorkedPerWeek", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkHourlyRate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkWeeklyRate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMonthlyRate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkClaimantIsMedicareBeneficiary", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAllPaymentsNonMedical", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDateConfirmedMedicareBemeficiary", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkClaimantMedicareNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDateLastReportedToMMSEA", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDeleteIfFiledWithMedicare", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNoFaultIndicator", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNoFaultInsuranceLimit", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNoFaultExhaustDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkICDDiagnosisCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkWagePeriod", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkEmploymentStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkEmployeeIDType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkWorkLossList", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkPhysicalRestrictionsIndicator", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDisabilityType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDiagnosis", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAgencyCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkNCCIClass", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTypeLoss", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkReportingPeriod", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDateEmployerKnewOfInitialDisability", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkInitialRtwSameEmployer", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkFirstDayOfDisability", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactVerificationNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkAccidentPremesisCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkReturnToWorkType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkReturnToWorkQualifier", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkRelationshipToBeneficiary", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTypeOfRepresentative", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkIncludeInTPOC", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMedicareSecondaryPayerEffectiveDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMedicareSecondaryPayerTerminationDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMedicareSecondaryPayerTypeIndicator", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDispositionCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkTimeEmployeeBeganWork", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkDrugSummary", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkICDClassification", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkICDProcedureCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactLinkMaritalStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactHeight", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contactWeight", nullable=true, ref="#/components/schemas/ExportCodedField"),
 * )
 */
class ExportedContact
{
}
