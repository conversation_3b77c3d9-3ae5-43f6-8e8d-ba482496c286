<?php

declare(strict_types=1);

namespace api\docs\schemas\Organisations;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedOrganisation",
 *     @OA\Property(property="organisationLinkConId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkExpensesReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinLegalReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinMedicalReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinPermanentIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinRemainingLegalReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinRemainingMedicalReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinRemainingPermanentIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinRemainingTemporaryIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkFinTemporaryIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkNotes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkResp", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkRole", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkMainRecordid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkRecordid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkRemainingExpensesReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkRemainingIndemnityReserveAssigned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkRespTotalPaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkUpdatedby", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkUpdateddate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLinkUpdateid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationCreatedby", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationLocationId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationAddress", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationCity", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationCounty", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationEmail", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationNotes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationPostcode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationState", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationTel1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationTel2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="id", type="string"),
 *     @OA\Property(property="organisationRepApproved", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationServiceId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationTaxId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationUpdatedby", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationUpdateddate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="organisationUpdateid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(
 *         property="organisationPolicies",
 *         type="array",
 *         @OA\Items(
 *             @OA\Property(property="organisationPolicyType", ref="#/components/schemas/ExportCodedField"),
 *             @OA\Property(property="organisationPolicyStartDate", ref="#/components/schemas/ExportCodedField"),
 *             @OA\Property(property="organisationPolicyEndDate", ref="#/components/schemas/ExportCodedField"),
 *         ),
 *     ),
 * )
 */
interface ExportedOrganisation
{
}
