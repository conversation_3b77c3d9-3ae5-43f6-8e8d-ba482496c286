<?php

declare(strict_types=1);

namespace api\docs\schemas\Payments;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedPayment",
 *     @OA\Property(property="id", type="string"),
 *     @OA\Property(property="paymentDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentSubtype", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentAmount", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentVatRate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentVatAmount", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentTotal", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentNotes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentLinkModule", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentLinkId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentLinkTitle", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentRecipient", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="paymentPayee", nullable=true, ref="#/components/schemas/ExportCodedField"),
 * )
 */
interface ExportedPayment
{
}
