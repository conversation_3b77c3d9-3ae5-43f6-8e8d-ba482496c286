<?php

declare(strict_types=1);

namespace api\docs\schemas\Claims;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedClaim",
 *     @OA\Property(property="id", type="string"),
 *     @OA\Property(property="claimVersion", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimName", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimHandler", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimReference", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOtherReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCaseSpecialty", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateIncident", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateOpened", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEstimatedSettlement", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateSettled", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCurrentStage", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOutcome", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimProbability", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEstimatedClaimantCosts", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEstimatedDamages", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimPercentageOurShare", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEstimatedDefenceCosts", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOurLiability", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOtherPartyName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimPercentageThirdPartShare", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOtherPartyName2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimPercentageThirdPartShare2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLastUpdatedDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLastUpdatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLocation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimService", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInvestigator", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateInvestigationStarted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateInvestigationCompleted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCauses", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInvestigationOutcome", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimActionsTaken", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLessonsLearned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimAdverseEvent", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDetail", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimStageOfCare", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCreatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInsurer", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInsurerReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateReportedToInsurer", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimManager", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCost", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFurtherInquiry", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimHasEmployee", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimHasOtherContacts", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimHasDocuments", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDescription", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimActionsTakenText", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLessonsLearnedText", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIncidentTypeTier1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIncidentTypeTier2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIncidentTypeTier3", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIncidentAffecting", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLevelOfIntervention", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLevelOfHarm", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIndemnityReserve", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimExpensesReserve", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateIndemnityReserveClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimDateExpensesReserveClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInsurerExcess", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimExpectedCostToOrganisation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimInsurerReimnursement", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIndemnityIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimExpensesIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimExpensesTotalIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimTotalIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaRegisteredEstablishment", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialMedicalReserveIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialLegalReserveIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialTemporaryIndemnityReserveIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialPermanentIndemnityReserveIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialMedicalReserveDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialLegalReserveDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialTemporaryIndemnityReserveDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialPermanentIndemnityReserveDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialIndemnityReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialExpensesReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialMedicalReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialLegalReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialTemporaryIndemnityReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialPermanentIndemnityReserveBalance", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialIndemnityReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialExpensesReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialMedicalReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialLegalReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialTemporaryIndemnityReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialPermanentIndemnityReservePaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialIndemnityReserveCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialExpensesReserveCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialMedicalReserveCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialLegalReserveCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialTemporaryIndemnityReserveCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialPermanentIndemnityReserve", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialReservesTotalIncurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialReservesTotalPaid", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialReservesTotalCollected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialReservesTotalReserved", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFinancialReservesDateFinalClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimHasOngoingResponsibilityForMedicals", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOngoingResponsibilityForMedicalsDateOfTermination", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaRecordable", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaPrivacyCase", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaCaseClassification", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaEmployeeTreatedInEmergencyRoom", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaEmployeeHospitalisedOvernight", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaEmployeeActionsBeforeIncident", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOshaCauseOfDirectHarm", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOtherLocation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimOtherService", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEDIJurisdiction", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEventReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEventTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimResponsibleReportingEntityId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimQuickReferenceId", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiDateExtracted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiDateProcessed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiAgencyClaimNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiIAIABCR1FROIFilingStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiFROICorrectionDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimEdiCauseCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimFROIMaintenenceTypeCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimAgreementToCompensate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimLateReasonCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimChangeDataElementSegmentNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimChangeReason", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimCancelReason", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimNumberManagedCareOrgs", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimNumberChangedDataElements", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimNumberCancelElements", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimIncidentOccurredOnEmployerPremises", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimNumberWitnesses", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="claimNumberFullDenialReasons", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="contacts", type="array", @OA\Items(ref="/components/ExportedContact")),
 *     @OA\Property(property="devices", type="array", deprecated=true, maxItems=0, @OA\Items(type="object"), description="Code is obsolete and array will always be empty"),
 *     @OA\Property(property="progressNotes", type="array", @OA\Items(ref="#/components/schemas/ExportedProgressNote")),
 *     @OA\Property(property="payments", type="array", @OA\Items(ref="#/components/schemas/ExportedPayment")),
 *     @OA\Property(property="tpoc", type="array", @OA\Items(ref="#/components/schemas/ExportedTpoc")),
 *     @OA\Property(property="organisations", type="array", @OA\Items(ref="#/components/schemas/ExportedOrganisation")),
 *     @OA\AdditionalProperties(description="UDF fields as additionalDataItem<field_id>", ref="#/components/schemas/ExportCodedField")
 * )
 */
interface ExportedClaim
{
}
