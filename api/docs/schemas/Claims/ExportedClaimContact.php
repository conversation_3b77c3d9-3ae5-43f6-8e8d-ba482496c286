<?php

declare(strict_types=1);

namespace api\docs\schemas\Claims;

use api\docs\schemas\Contacts\ExportedContact;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedClaimContact",
 *     @OA\Property(property="contactLinkInjuries", @OA\Property(property="count", type="integer")),
 *     @OA\Property(
 *         property="contactPolicies",
 *         type="array",
 *         @OA\Items(
 *             @OA\Property(property="contactPolicyType", ref="#/components/schemas/ExportCodedField"),
 *             @OA\Property(property="contactPolicyStartDate", ref="#/components/schemas/ExportCodedField"),
 *             @OA\Property(property="contactPolicyEndDate", ref="#/components/schemas/ExportCodedField"),
 *         ),
 *     ),
 *     @OA\Property(
 *         property="contactLinkLostTime",
 *         type="array",
 *         @OA\Items(
 *             @OA\Property(property="start_date", type="string", maxLength=10),
 *             @OA\Property(property="end_date", type="string", maxLength=10, nullable=true),
 *             @OA\Property(property="total", type="string", nullable=true),
 *         ),
 *     ),
 *     @OA\AdditionalProperties(required={"Value"}, ref="#/components/schemas/ExportCodedField", description="UDF fields named as additionalDataItem<field_id>"),
 * )
 */
final class ExportedClaimContact extends ExportedContact
{
}
