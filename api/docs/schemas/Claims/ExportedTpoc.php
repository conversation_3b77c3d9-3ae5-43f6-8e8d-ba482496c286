<?php

declare(strict_types=1);

namespace api\docs\schemas\Claims;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedTpoc",
 *     @OA\Property(property="tpocDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="tpocAmount", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="tpocDateDelayed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 * )
 */
interface ExportedTpoc
{
}
