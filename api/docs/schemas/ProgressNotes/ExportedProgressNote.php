<?php

declare(strict_types=1);

namespace api\docs\schemas\ProgressNotes;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     schema="ExportedProgressNote",
 *     @OA\Property(property="pno_progress_notes", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="pno_createdby", ref="#/components/schemas/ExportCodedField"),
 *     @OA\Property(property="pno_createddate", ref="#/components/schemas/ExportCodedField"),
 * )
 */
interface ExportedProgressNote
{
}
