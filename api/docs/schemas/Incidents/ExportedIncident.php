<?php

declare(strict_types=1);

namespace api\docs\schemas\Incidents;

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     type="array",
 *     @OA\Items(
 *         @OA\Property(property="id", type="string"),
 *         @OA\Property(property="incidentStatus", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentVersion", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPasNumber1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPasNumber2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPasNumber3", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLocation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentService", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHandler", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentManager", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentCategory", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSubcategory", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateOccurred", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateOpened", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateClosed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSeverity", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateReported", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReportedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReporterName", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLastUpdatedDate", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLastUpdatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentResult", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentInvestigators", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateInvestigationCompleted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentInvestigationOutcome", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentItem", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLossOrDamage", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateRiddorNotified", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentNotifiedParties", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRiddorLocation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAddress", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLocalAuthority", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRiddorNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAccidentType", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReportableToRiddor", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateInvestigationStarted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentConsequence", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLikelihood", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRating", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentGrade", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRootCauses", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentStageOfCare", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateExportedToNPSA", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAdverseEvent", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentActionTakenCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLessonsLearnedCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentFurtherEnquiry", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDetail", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentCost", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationStageOfError", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationError", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationDrugAdministered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationDrugCorrect", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationFormAdministered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationFormCorrect", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationDoseAdministered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationDoseCorrect", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationRouteAdministered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMedicationRouteCorrect", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentFurtherInvestigation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentCauseAnalysisRequired", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReportedToNRLS", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAggravatingFactors", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTimePoliceCalled", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPoliceAttended", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTimePoliceAttended", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPoliceActionTaken", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPoliceCrimeNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentImmediateActionTaken", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPoliceCalled", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReporterTelephoneNumber", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReporterEmail", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTypePerpetratorInvolved", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentNonPhysicalAssaultSubcategories", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentClinicalFactorsPresent", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPostcode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateExportedToCFSMS", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSubmittedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRiddorReference", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentCreatedBy", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSIRSAddress", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasPersonAffected", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasWitness", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasEmployee", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasOtherContacts", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasDevices", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasMedications", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentReportableToCFSMS", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasDocuments", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTrustPropertyDamaged", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDateFirstExportedToCFSMS", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHasAssailant", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentConsequenceInitial", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLikelihoodInitial", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentGradeInitialRating", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentGradeRating", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentGradeInitial", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTimeBand", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTypeTierOne", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTypeTierTwo", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTypeTierThree", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTypeTierZero", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDescription", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRecommendations", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentActionTaken", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentImprovementStrategies", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentExtraInformation", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLessonsLearned", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLevelOfIntervention", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentLevelOfHarm", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSubmittedTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentNeverEvent", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPatientWasInformed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPatientInformedTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPatientInformedByStaff", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentStaffWhoInformed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentPatientInformed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentApologyProvided", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentTruthfulAccountProvided", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentNextStepsAdvised", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentWrittenMemoRecorded", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMemoCopyProvided", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMemoCopyProvidedTime", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentFailuresDiscussed", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentFailuresRecordedInReport", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDetailedReportWritten", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentDetailedReportProvided", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentUnsuccessfulContactAttemptsRecorded", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSupportServicesOffered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSupportServicesAccepted", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentSupportServicesDetail", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentFollowUpDiscussionsOffered", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentEdiCauseCode", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAnzcoCoding", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentBreakdownAgency", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentAgencyOfInjury", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMechOfInjury1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentMechOfInjury2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentToocs1", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentToocs2", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentHealthServiceSite", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRepFeedbackCodes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="incidentRepFeedbackNotes", nullable=true, ref="#/components/schemas/ExportCodedField"),
 *         @OA\AdditionalProperties(description="UDF data", ref="#/components/schemas/ExportCodedField"),
 *         @OA\Property(property="contacts", type="array", @OA\Items(ref="#/components/schemas/ExportedIncidentContact")),
 *         @OA\Property(property="devices", type="array", deprecated=true, description="Unusued property, will always be empty", maxItems=0, @OA\Items(type="object")),
 *         @OA\Property(
 *             property="progressNotes",
 *             type="array",
 *             @OA\Items(
 *                 @OA\Property(property="progressNoteText", ref="#/components/schemas/ExportCodedField"),
 *                 @OA\Property(property="progressNoteAuthor", ref="#/components/schemas/ExportCodedField"),
 *                 @OA\Property(property="progressNoteDateCreated", ref="#/components/schemas/ExportCodedField"),
 *             ),
 *         ),
 *         @OA\Property(property="payments", type="array", @OA\Items(ref="#/components/schemas/ExportedPayment")),
 *     ),
 * )
 */
interface ExportedIncident
{
}
