<?php

declare(strict_types=1);

namespace api\docs\schemas\Incidents;

use api\docs\schemas\Contacts\ExportedContact;
use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *     @OA\Property(
 *         property="contactLinkInjuries",
 *         type="array",
 *         @OA\Items(
 *             @OA\Property(property="contactLinkInjury", ref="#/components/schemas/ExportCodedField"),
 *             @OA\Property(property="contactLinkBodyPart", ref="#/components/schemas/ExportCodedField"),
 *         ),
 *     ),
 * )
 */
final class ExportedIncidentContact extends ExportedContact
{
}
