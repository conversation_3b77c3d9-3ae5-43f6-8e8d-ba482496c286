<?php

declare(strict_types=1);

namespace api\ErrorHandler;

use api\V2\Services\RequestService;
use Doctrine\DBAL\Exception as DBALException;
use api\V2\Services\ResponseService;
use app\services\httpRequests\HTTPRequests;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Teapot\StatusCode\Http;
use Throwable;

use function in_array;

class ErrorHandler
{
    private LoggerInterface $logger;
    private RequestService $requestService;
    private ResponseService $responseService;

    public function __construct(
        LoggerInterface $logger,
        RequestService $requestService,
        ResponseService $responseService
    ) {
        $this->logger = $logger;
        $this->requestService = $requestService;
        $this->responseService = $responseService;
    }

    public function __invoke(
        Request $request,
        Response $response,
        Throwable $exception
    ): Response {
        $defaultError = $this->requestService->isV2Route($request) ? Http::INTERNAL_SERVER_ERROR : Http::BAD_REQUEST;
        $validStatusCodes = array_keys((new HTTPRequests())->getStatusCodes());
        $statusCode = in_array($exception->getCode(), $validStatusCodes, true) ? $exception->getCode() : $defaultError;
        $errorDetails = ($exception instanceof DBALException) ? 'A database error occurred' : $exception->getMessage();

        $this->logger->critical('An uncaught exception was found.', [
            'exception' => $exception,
        ]);

        return $this->responseService->formatErrorResponse(
            $response,
            $statusCode,
            $errorDetails,
        );
    }
}
