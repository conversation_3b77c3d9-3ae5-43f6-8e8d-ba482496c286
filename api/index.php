<?php

use api\middleware\MaintenanceModeMiddleware;
use api\middleware\LanguageSessionMiddleware;
use api\service\EntityEventService;
use app\models\framework\config\DatixConfig;
use GuzzleHttp\Psr7\ServerRequest;
use Slim\Factory\AppFactory;
use Tuupola\Middleware\HttpBasicAuthentication;
use Tuupola\Middleware\JwtAuthentication;
use src\framework\registry\Registry;
use src\system\container\ContainerFactory;

require_once __DIR__ . '/../Source/bootstrap.php';

// Set default language.
setLanguage('en_GB');

$container = (new ContainerFactory())->create(true);
$app = AppFactory::createFromContainer($container);

$registry = $container->get(Registry::class);
date_default_timezone_set($registry->getParm('SYSTEM_TIMEZONE', 'Europe/London', true)->toScalar());

(new EntityEventService($container))->registerEntityEvents();

/** @var DatixConfig $datixConfig */
$datixConfig = $container->get(DatixConfig::class);

// Middleware must be added BEFORE routing or Slim 4 will just ignore them
// Middleware runs in reverse order, please ensure we add LanguageSession BEFORE Jwt
$app->add($container->get(HttpBasicAuthentication::class));
$app->add($container->get(LanguageSessionMiddleware::class));
$app->add($container->get(JwtAuthentication::class));
$app->add($container->get(MaintenanceModeMiddleware::class));

// Routing
$app->setBasePath('/api');
foreach (glob('routes/*.php') as $route) {
    require $route;
}
foreach (glob('V2/routes/*.php') as $route) {
    require $route;
}

// Add middleware for handling HTTP error logging(must always be last)
$app->addErrorMiddleware(true, true, true);
$request = ServerRequest::fromGlobals();
$parsedBody = json_decode($request->getBody()->getContents(), true);
$app->run(
    $request->withParsedBody($parsedBody),
);
