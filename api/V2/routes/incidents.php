<?php

declare(strict_types=1);

use api\middleware\UserModuleAuthMiddleware;
use api\middleware\ValidatorMiddleware;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Slim\Routing\RouteCollectorProxy;
use Teapot\StatusCode\Http;

/** @var \Slim\App $app */
$app->group('/v2', function (RouteCollectorProxy $router) {
    $router->get('/incidents/search', 'IncidentsGetController:getRecords');
})->add($app->getContainer()->get(ValidatorMiddleware::class))
    ->add($app->getContainer()->get(UserModuleAuthMiddleware::class));

$app->group('/v2', function (RouteCollectorProxy $router) {
    $router->get('/incidents/search/mock', function (Request $request, Response $response) {
        $mockJson = '{"data":[{"id":1,"name":"INCIDENT1","ourRef":"ref1","notes":"Pleasenotethisisamock","submittedTime":null,"dateOpened":"2020-01-01","healthServiceSite":"MockHealthService","dateTimeReported":"2022-06-13","levelOfHarm":"Moderate","isNeverEvent":"N","managerTitle":"FullAdmin","actionTaken":null,"incidentTime":"10:34","typeTierZero":null,"typeTierOne":null,"typeTierTwo":"test2","typeTierThree":"test2","exactLocation":null,"otherLocation":null,"location":[{"id":1,"left":1,"right":10,"title":{"7":"Locations"}},{"id":2,"left":2,"right":9,"title":{"7":"North"}},{"id":3,"left":3,"right":8,"title":{"7":"StStephens"}},{"id":4,"left":4,"right":7,"title":{"7":"StStephensMain"}},{"id":5,"left":5,"right":6,"title":{"7":"Theatre1"}}],"service":null,"otherService":null},{"id":2,"name":"INCIDENT2","ourRef":"ref2","notes":null,"submittedTime":null,"dateOpened":"2020-01-01","healthServiceSite":"MockHealthService","dateTimeReported":"2021-09-15","levelOfHarm":"Severe","isNeverEvent":"Y","managerTitle":"SBU_DU_DATIX_SUPPORT-RLDatixSupportaccount","actionTaken":"x-ray","incidentTime":"10:22","typeTierZero":"test0","typeTierOne":"test1","typeTierTwo":"test2","typeTierThree":"test2","exactLocation":"Theatre1-walkwaytoprepstation","otherLocation":[{"id":1,"left":1,"right":10,"title":{"7":"Locations"}},{"id":2,"left":2,"right":9,"title":{"7":"North"}},{"id":3,"left":3,"right":8,"title":{"7":"StStephens"}},{"id":4,"left":4,"right":7,"title":{"7":"StStephensMain"}},{"id":5,"left":5,"right":6,"title":{"7":"Theatre1"}}],"location":[{"id":1,"left":1,"right":8,"title":{"7":"Locations"}},{"id":2,"left":2,"right":7,"title":{"7":"North"}},{"id":3,"left":3,"right":6,"title":{"7":"StStephen"}},{"id":4,"left":4,"right":5,"title":{"7":"StStephen Main"}}],"service":[{"id":1,"left":1,"right":36,"title":{"7":"Service"}},{"id":16,"left":30,"right":35,"title":{"7":"Corporate"}},{"id":18,"left":33,"right":34,"title":{"7":"Communications"}}],"otherService":null}],"metadata":{"resultset":{"count":2,"offset":0,"limit":10}}}';
        $response->getBody()->write($mockJson);

        return $response->withStatus(Http::OK)
            ->withHeader('Content-Type', 'application/json');
    });
});
