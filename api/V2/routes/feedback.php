<?php

declare(strict_types=1);

use api\middleware\UserModuleAuthMiddleware;
use api\middleware\ValidatorMiddleware;
use Slim\Routing\RouteCollectorProxy;

/** @var \Slim\App $app */
$app->group('/v2', function (RouteCollectorProxy $router) {
    $router->get('/feedback', 'FeedbackGetController:getRecords');
    $router->patch('/feedback/{id}', 'FeedbackPatchController:patchRecord');
})->add(ValidatorMiddleware::class)
    ->add(UserModuleAuthMiddleware::class);
