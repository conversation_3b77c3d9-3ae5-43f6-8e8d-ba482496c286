<?php

declare(strict_types=1);

use api\V2\Controllers\Etl\EtlTableNameValidationController;
use api\V2\Controllers\Etl\EtlTableSyncController;
use Slim\Routing\RouteCollectorProxy;

/** @var \Slim\App $app */
$app->group('/etl', function (RouteCollectorProxy $router) {
    $router->post('/table-sync', EtlTableSyncController::class . ':tableSyncAction')
        ->add('validator.etl_sync');
    $router->post('/validate-table-names', EtlTableNameValidationController::class . ':validateTableListAction')
        ->add('validator.etl_sync_table_names');
});
