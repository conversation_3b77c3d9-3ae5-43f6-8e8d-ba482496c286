<?php

declare(strict_types=1);

namespace api\V2\Controllers\Incidents;

use api\V2\Controllers\GetController;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Throwable;

class GetIncidentsController extends GetController
{
    private const FIELD_EQUALITY = [
        'incidents_main.updateddate' => 'gte',
    ];

    /**
     * @OA\Get(
     *     path="/v2/incidents/search",
     *     description="Retrieve Incident Records",
     *     tags={"incident"},
     *     @OA\Parameter(
     *         name="selectCore[]",
     *         in="query",
     *         description="Specifies what core fields needs to be included in the API response.",
     *         @OA\Schema(
     *             type="array",
     *             @OA\Items,
     *             example={
     *                 "id",
     *                 "name",
     *                 "approvalStatus",
     *                 "ourRef",
     *                 "notes",
     *                 "submittedTime",
     *                 "dateOpened",
     *                 "healthServiceSite",
     *                 "dateTimeReported",
     *                 "levelOfHarm",
     *                 "isNeverEvent",
     *                 "managerTitle",
     *                 "actionTaken",
     *                 "incidentTime",
     *                 "typeTierZero",
     *                 "typeTierOne",
     *                 "typeTierTwo",
     *                 "typeTierThree",
     *                 "exactLocation",
     *                 "otherLocation",
     *                 "location",
     *                 "service",
     *                 "otherService",
     *                 "updatedDate",
     *             },
     *         ),
     *     ),
     *     @OA\Parameter(
     *         name="selectUdf[]",
     *         in="query",
     *         description="Specifies what user defined fields needs to be included in the API response.",
     *         @OA\Schema(
     *             type="array",
     *             @OA\Items,
     *             example={
     *                 12,
     *                 123,
     *                 143,
     *             },
     *         ),
     *     ),
     *     @OA\Parameter(
     *         name="where[updatedDate]",
     *         in="query",
     *         description="Request only returns incidents updated after updatedDate. Of the format YYYY-MM-DD with optional time HH:MM:SS",
     *         required=true,
     *         @OA\Schema(
     *             type="string",
     *             example="1999-08-18 13:13:13",
     *         ),
     *     ),
     *     @OA\Parameter(
     *         name="where[]",
     *         in="query",
     *         description="Specifies filtering requirements to be applied to the results returned in the API response.",
     *         @OA\Schema(
     *             type="object",
     *             example={
     *                 "id": 12,
     *                 "location": 123,
     *                 "notes": "test",
     *             },
     *         ),
     *         style="deepObject",
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="The maximum number of records to return in the response.",
     *         @OA\Schema(
     *             type="integer",
     *             minimum=1,
     *             maximum=25,
     *             example=14,
     *         ),
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="The results page number to return",
     *         required=false,
     *         @OA\Schema(
     *             type="integer",
     *             minimum=1,
     *             example=2,
     *         ),
     *     ),
     *     @OA\Parameter(
     *         name="offset",
     *         in="query",
     *         description="Offsets result set. Page automatically offsets by page * limit",
     *         required=false,
     *         @OA\Schema(
     *             type="integer",
     *             example=15,
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Successful pull of incident info.",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     @OA\Property(
     *                         property="id",
     *                         type="integer",
     *                         example=21,
     *                     ),
     *                     @OA\Property(
     *                         property="name",
     *                         type="string",
     *                         example="Test Incident as part of DU interface testing - 01",
     *                     ),
     *                     @OA\Property(
     *                         property="approvalStatus",
     *                         type="string",
     *                         example="AWAREV",
     *                     ),
     *                     @OA\Property(
     *                         property="ourRef",
     *                         type="string",
     *                         example="TEST API 01 - NIN-2023/1084",
     *                     ),
     *                     @OA\Property(
     *                         property="notes",
     *                         type="string",
     *                         example="Notes example",
     *                     ),
     *                     @OA\Property(
     *                         property="submittedTime",
     *                         type="string",
     *                         example="09:48",
     *                     ),
     *                     @OA\Property(
     *                         property="dateOpened",
     *                         type="string",
     *                         example="2017-03-04",
     *                     ),
     *                     @OA\Property(
     *                         property="healthServiceSite",
     *                         type="string",
     *                         example="Demo System (testing use only)",
     *                     ),
     *                     @OA\Property(
     *                         property="dateTimeReported",
     *                         type="string",
     *                         example="2023-01-10",
     *                     ),
     *                     @OA\Property(
     *                         property="levelOfHarm",
     *                         type="string",
     *                         example="Severe",
     *                     ),
     *                     @OA\Property(
     *                         property="isNeverEvent",
     *                         type="string",
     *                         example="Y",
     *                     ),
     *                     @OA\Property(
     *                         property="managerTitle",
     *                         type="string",
     *                         example="SBU_DU_DATIX_SUPPORT - RLDatix Support",
     *                     ),
     *                     @OA\Property(
     *                         property="actionTaken",
     *                         type="string",
     *                         example="TEST .. and xrayed",
     *                     ),
     *                     @OA\Property(
     *                         property="incidentTime",
     *                         type="string",
     *                         example="16:00",
     *                     ),
     *                     @OA\Property(
     *                         property="typeTierZero",
     *                         type="string",
     *                         example="ME0000",
     *                     ),
     *                     @OA\Property(
     *                         property="typeTierOne",
     *                         type="string",
     *                         example="BO1010",
     *                     ),
     *                     @OA\Property(
     *                         property="typeTierTwo",
     *                         type="string",
     *                         example="CI8976",
     *                     ),
     *                     @OA\Property(
     *                         property="typeTierThree",
     *                         type="string",
     *                         example="NK3245",
     *                     ),
     *                     @OA\Property(
     *                         property="exactLocation",
     *                         type="string",
     *                         example="Theatre 1 - walkway to prep station",
     *                     ),
     *                     @OA\Property(
     *                         property="location",
     *                         type="object",
     *                         example={
     *                             "id": 56,
     *                             "location": "Locations (root) / NRLS Smoke Location",
     *                             "path": {
     *                                 {
     *                                     "id": 1,
     *                                     "left": 1,
     *                                     "right": 683,
     *                                     "title": {
     *                                         "3": "Location Arabic",
     *                                         "7": "Locations (root)",
     *                                         "14": "Location US",
     *                                         "15": "Emplacements",
     *                                     },
     *                                     "draftTitle": "PLACEHOLDER.1904f040-2ed5-4339-bbba-d44af0aa1d06",
     *                                 },
     *                                 {
     *                                     "id": 56,
     *                                     "left": 118,
     *                                     "right": 119,
     *                                     "title": {
     *                                         "7": "NRLS Smoke Location",
     *                                         "14": "NRLS smoke location",
     *                                     },
     *                                     "draftTitle": "PLACEHOLDER.876b33b2-71b5-4daf-bbb1-64ee4ee6425b",
     *                                 },
     *                             },
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="otherLocation",
     *                         type="object",
     *                         example={
     *                             "id": 561,
     *                             "location": "Locations (root) / 9.23.21 Location Parent",
     *                             "path": {
     *                                 {
     *                                     "id": 1,
     *                                     "left": 1,
     *                                     "right": 683,
     *                                     "title": {
     *                                         "7": "Locations (root)",
     *                                         "15": "Emplacements",
     *                                         "3": "Location Arabic",
     *                                         "14": "Location US",
     *                                     },
     *                                     "draftTitle": "PLACEHOLDER.1904f040-2ed5-4339-bbba-d44af0aa1d06",
     *                                 },
     *                                 {
     *                                     "id": 561,
     *                                     "left": 254,
     *                                     "right": 257,
     *                                     "title": {
     *                                         "7": "9.23.21 Location Parent",
     *                                     },
     *                                     "draftTitle": "PLACEHOLDER.d33b540d-0bdb-4dd0-9e9d-4a83ed5f94b7",
     *                                 },
     *                             },
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="service",
     *                         type="object",
     *                         example={
     *                             "id": 68,
     *                             "location": "Services / NRLS Smoke Service",
     *                             "path": {
     *                                 {
     *                                     "id": 1,
     *                                     "title": {
     *                                         "7": "Services",
     *                                         "14": "Service US",
     *                                     },
     *                                     "left": 1,
     *                                     "right": 548,
     *                                 },
     *                                 {
     *                                     "id": 68,
     *                                     "left": 148,
     *                                     "right": 149,
     *                                     "title": {
     *                                         "7": "NRLS Smoke Service",
     *                                         "14": "NRLS Smoke Service",
     *                                     },
     *                                 },
     *                             },
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="otherService",
     *                         type="object",
     *                         example={
     *                             "id": 19,
     *                             "location": "Services / Service with no tag",
     *                             "path": {
     *                                 {
     *                                     "id": 1,
     *                                     "title": {
     *                                         "7": "Services",
     *                                         "14": "Service US",
     *                                     },
     *                                     "left": 1,
     *                                     "right": 548,
     *                                 },
     *                                 {
     *                                     "id": 19,
     *                                     "left": 42,
     *                                     "right": 47,
     *                                     "title": {
     *                                         "7": "Service with no tag",
     *                                         "14": "Service US",
     *                                     },
     *                                 },
     *                             },
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="updatedDate",
     *                         type="string",
     *                         example="2023-05-17 13:13:08.000",
     *                     ),
     *                     @OA\Property(
     *                         property="lessonsLearnedDescription",
     *                         type="string",
     *                         example="The Lesson Learned",
     *                     ),
     *                     @OA\Property(
     *                         property="result",
     *                         type="object",
     *                         example={
     *                             "value": "101",
     *                             "description": "A result"
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="severity",
     *                         type="object",
     *                         example={
     *                             "value": "101",
     *                             "description": "NRLS Severity B"
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="contacts",
     *                         type="array",
     *                         @OA\Items,
     *                         example={
     *                             {
     *                                 "id": 6,
     *                                 "gender": {
     *                                     "value": "106",
     *                                     "description": "Male"
     *                                 },
     *                                 "age": null,
     *                                 "linkType": "A",
     *                                 "linkRole": ""
     *                             }
     *                         },
     *                     ),
     *                     @OA\Property(
     *                         property="udfs",
     *                         type="array",
     *                         @OA\Items,
     *                         example={
     *                             "1": {
     *                                 "fieldName": "Date Extra Field (All modules)",
     *                                 "fieldValue": "2020-07-25",
     *                                 "type": "Date"
     *                             },
     *                             "2": {
     *                                 "fieldName": "Coded Extra Field (All Modules) N12",
     *                                 "fieldValue": {
     *                                     "value": "1",
     *                                     "description": "Grade1"
     *                                 },
     *                                 "type": "Code"
     *                             },
     *                             "3": {
     *                                 "fieldName": "Yes/No Extra Field (All Modules) update N1",
     *                                 "fieldValue": {
     *                                     "value": "Y",
     *                                     "description": "Yes"
     *                                 },
     *                                 "type": "Yes/No"
     *                             },
     *                             "4": {
     *                                 "fieldName": "Multicode Extra Field (All Modules) update N1",
     *                                 "fieldValue": {
     *                                     {
     *                                         "value": "1",
     *                                         "description": "Multicode Extra Field 1 Description"
     *                                     },
     *                                     {
     *                                         "value": "2",
     *                                         "description": "Multicode Extra Field 2 Description"
     *                                     }
     *                                 },
     *                                 "type": "Multi-Select Code"
     *                             },
     *                             "5": {
     *                                 "fieldName": "String Extra Field (All Modules) N1",
     *                                 "fieldValue": "String 1",
     *                                 "type": "String"
     *                             },
     *                             "6": {
     *                                 "fieldName": "Number Extra Field (All Modules) N1",
     *                                 "fieldValue": 23,
     *                                 "type": "Number"
     *                             },
     *                             "7": {
     *                                 "fieldName": "Money Extra Field (All modules) N1",
     *                                 "fieldValue": "10.00",
     *                                 "type": "Money"
     *                             },
     *                             "8": {
     *                                 "fieldName": "Text Extra Field (All Modules) N1",
     *                                 "fieldValue": "Text 1",
     *                                 "type": "Text"
     *                             },
     *                             "9": {
     *                                 "fieldName": "Code Extra field (All modules) for Radio buttons N1",
     *                                 "fieldValue": {
     *                                     "value": "1",
     *                                     "description": "Code Extra Field 1 Description  (All modules) for Radio buttons"
     *                                 },
     *                                 "type": "Code"
     *                             },
     *                             "10": {
     *                                 "fieldName": "Multicode Extra Field (All Modules) for Check boxes N1",
     *                                 "fieldValue": {
     *                                     {
     *                                         "value": "1",
     *                                         "description": "Multicode Extra Field 1 Description (All Modules) for Check boxes"
     *                                     },
     *                                     {
     *                                         "value": "2",
     *                                         "description": "Multicode Extra Field 2 Description (All Modules) for Check boxes"
     *                                     }
     *                                 },
     *                                 "type": "Multi-Select Code"
     *                             },
     *                             "75": {
     *                                 "fieldName": "MARYA UDF CODED FIELD N1",
     *                                 "fieldValue": {
     *                                     "value": "MAR1",
     *                                     "description": "MARYA UDF CODED FIELD 1"
     *                                 },
     *                                 "type": "Code"
     *                             }
     *                         },
     *                     ),
     *                 ),
     *             ),
     *             @OA\Property(
     *                 property="metadata",
     *                 type="object",
     *                 @OA\Property(
     *                     property="count",
     *                     type="integer",
     *                     example=500,
     *                     description="Total number of results.",
     *                 ),
     *                 @OA\Property(
     *                     property="offset",
     *                     type="integer",
     *                     example=85,
     *                     description="Offset of results displayed (e.g. offset 100 with limit 10 would show 100 - 110).",
     *                 ),
     *                 @OA\Property(
     *                     property="limit",
     *                     type="integer",
     *                     example=9,
     *                     description="Max number of results retrieved and displayed.",
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     type="integer",
     *                     example=2,
     *                     description="The page of results to display.",
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="400",
     *         description="Bad Request (potentially failed validation).",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(
     *                     property="type",
     *                     type="string",
     *                     example="http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html",
     *                 ),
     *                 @OA\Property(
     *                     property="title",
     *                     type="string",
     *                     example="Bad Request",
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     type="integer",
     *                     example=400,
     *                 ),
     *                 @OA\Property(
     *                     property="detail",
     *                     type="string",
     *                     example="Failed Validation",
     *                 ),
     *                 @OA\Property(
     *                     property="validation_messages",
     *                     type="object",
     *                     example={
     *                         "where": {
     *                             "updatedDate": {
     *                                 "isEmpty": "Value is required and can't be empty",
     *                             },
     *                         },
     *                     },
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="401",
     *         description="Unauthorized Request (check JWT token).",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(
     *                     property="type",
     *                     type="string",
     *                     example="http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html",
     *                 ),
     *                 @OA\Property(
     *                     property="title",
     *                     type="string",
     *                     example="Unauthorized",
     *                 ),
     *                 @OA\Property(
     *                     property="status",
     *                     type="integer",
     *                     example=401,
     *                 ),
     *                 @OA\Property(
     *                     property="detail",
     *                     type="string",
     *                     example="Unauthorised Request",
     *                 ),
     *             ),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="500",
     *         description="Unexpected error",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(
     *                     property="error",
     *                     type="string",
     *                     example="Unexpected Error",
     *                 ),
     *             ),
     *         ),
     *     ),
     * ),
     *
     * Gets records based on data sent in the request and returns a response with the data or an appropriate error.
     *
     * @throws Throwable
     */
    public function getRecords(Request $request, Response $response): Response
    {
        [$whereParameters, $options, $fieldsToSelect] = $this->requestService->extractFromQueryString($request->getQueryParams());

        if (!empty($fieldsToSelect['module']) && !in_array('id', $fieldsToSelect['module'], true)) {
            $fieldsToSelect['module'][] = 'id';
        }

        $resultsData = $this->getResponseDataService->getResponseData(
            $whereParameters,
            $options,
            $fieldsToSelect,
            self::FIELD_EQUALITY,
        );

        return $this->responseService->formatSuccessGetResponse($response, $resultsData);
    }
}
