<?php

declare(strict_types=1);

namespace api\V2\Controllers;

use api\V2\Services\GetResponseDataService;
use api\V2\Services\RequestService;
use api\V2\Services\ResponseService;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Throwable;

class GetController
{
    protected RequestService $requestService;
    protected ResponseService $responseService;
    protected GetResponseDataService $getResponseDataService;

    public function __construct(
        RequestService $requestService,
        ResponseService $responseService,
        GetResponseDataService $getResponseDataService
    ) {
        $this->requestService = $requestService;
        $this->responseService = $responseService;
        $this->getResponseDataService = $getResponseDataService;
    }

    /**
     * Gets records based on data sent in the request and returns a response with the data or an appropriate error.
     *
     * @throws Throwable
     */
    public function getRecords(Request $request, Response $response): Response
    {
        [$whereParameters, $options, $fieldsToSelect] = $this->requestService->extractFromQueryString($request->getQueryParams());

        $resultsData = $this->getResponseDataService->getResponseData(
            $whereParameters,
            $options,
            $fieldsToSelect,
        );

        return $this->responseService->formatSuccessGetResponse($response, $resultsData);
    }
}
