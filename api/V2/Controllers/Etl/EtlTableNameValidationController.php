<?php

declare(strict_types=1);

namespace api\V2\Controllers\Etl;

use api\V2\Services\Etl\EtlTableNameValidationService;
use app\models\generic\valueObjects\JSONData;
use Doctrine\DBAL\Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Teapot\StatusCode\Http;
use Throwable;

use const JSON_THROW_ON_ERROR;

class EtlTableNameValidationController
{
    private EtlTableNameValidationService $etlTableNameValidationService;

    public function __construct(EtlTableNameValidationService $etlTableSyncService)
    {
        $this->etlTableNameValidationService = $etlTableSyncService;
    }

    /**
     * @OA\Post(
     *     path="/etl/validate-table-names",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="tables", type="array", minItems=1, maxItems=1500, @OA\Items(type="string", minLength=1)),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="Validation result provided",
     *         @OA\JsonContent(
     *             @OA\Property(property="valid", type="array", maxItems=1500, @OA\Items(type="string")),
     *             @OA\Property(property="invalid", type="array", maxItems=1500, @OA\Items(type="string")),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws Exception
     * @throws Throwable
     */
    public function validateTableListAction(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $result = $this->etlTableNameValidationService->validateTableNames($data['tables']);

        $response->getBody()->write(json_encode([
            'valid' => $result->getValid(),
            'invalid' => $result->getInvalid(),
        ], JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::OK)->withHeader('Content-Type', 'application/json');
    }
}
