<?php

declare(strict_types=1);

namespace api\V2\Controllers\Etl;

use api\V2\Services\Etl\EtlTableSyncService;
use app\models\generic\valueObjects\JSONData;
use Exception;
use OpenApi\Annotations as OA;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Log\LoggerInterface;
use Teapot\StatusCode\Http;

use const JSON_THROW_ON_ERROR;

class EtlTableSyncController
{
    private LoggerInterface $logger;
    private EtlTableSyncService $etlTableSyncService;

    public function __construct(
        EtlTableSyncService $etlTableSyncService,
        LoggerInterface $logger
    ) {
        $this->etlTableSyncService = $etlTableSyncService;
        $this->logger = $logger;
    }

    /**
     * @OA\Post(
     *     path="/etl/table-sync",
     *     @OA\RequestBody(
     *         @OA\JsonContent(
     *             @OA\Property(property="table", type="string", minLength=1),
     *             @OA\Property(property="pageSize", type="integer", minimum=1),
     *             @OA\Property(property="page", type="integer", minimum=1),
     *         ),
     *     ),
     *     @OA\Response(
     *         response="201",
     *         description="Tables synced",
     *         @OA\JsonContent(
     *             @OA\Property(property="totalRows", type="integer", minimum=0),
     *             @OA\Property(property="haltBatchProcess", type="boolean"),
     *         ),
     *     ),
     *     @OA\Response(response="503", ref="#/components/responses/MaintenanceModeResponse"),
     * )
     *
     * @throws Exception
     */
    public function tableSyncAction(Request $request, Response $response): Response
    {
        $data = (new JSONData($request->getBody()))->toArray();

        $this->logger->notice('ETL Table Sync - Starting sync');

        $resultArray = $this->etlTableSyncService->exportTable($data['table'], $data['pageSize'], $data['page']);

        $this->logger->notice('ETL Table Sync - Success');

        $response->getBody()->write(json_encode($resultArray, JSON_THROW_ON_ERROR));

        return $response->withStatus(Http::CREATED)
            ->withHeader('Content-Type', 'application/json');
    }
}
