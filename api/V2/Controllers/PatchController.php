<?php

declare(strict_types=1);

namespace api\V2\Controllers;

use api\V2\Hydrators\HydratorManagerInterface;
use api\V2\Services\GetResponseDataService;
use api\V2\Services\ResponseService;
use api\V2\Services\UserAccessServiceInterface;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\framework\model\Mapper;
use Teapot\StatusCode\Http;
use Throwable;

class PatchController
{
    protected ResponseService $responseService;
    protected EntityManagerInterface $entityManager;
    protected HydratorManagerInterface $hydratorManager;
    private GetResponseDataService $getResponseDataService;
    private UserAccessServiceInterface $userAccessService;
    private Mapper $mapper;

    public function __construct(
        ResponseService $responseService,
        EntityManagerInterface $entityManager,
        HydratorManagerInterface $hydratorManager,
        GetResponseDataService $getResponseDataService,
        UserAccessServiceInterface $userAccessService,
        Mapper $mapper
    ) {
        $this->responseService = $responseService;
        $this->entityManager = $entityManager;
        $this->hydratorManager = $hydratorManager;
        $this->getResponseDataService = $getResponseDataService;
        $this->userAccessService = $userAccessService;
        $this->mapper = $mapper;
    }

    public function patchRecord(Request $request, Response $response, array $arguments): Response
    {
        if (!$this->userAccessService->userCanUpdateRecords($request)) {
            throw new Exception('User does not have record update access', Http::FORBIDDEN);
        }

        $recordId = (int) $arguments['id'];

        $mainEntity = $this->hydratorManager->hydrateEntitiesFromRequest($request, $recordId);

        $this->entityManager->beginTransaction();

        try {
            $this->entityManager->persist($mainEntity);
            $this->entityManager->flush();
            $this->entityManager->getConnection()->commit();
        } catch (Throwable $exception) {
            $this->entityManager->getConnection()->rollBack();

            throw $exception;
        }

        $this->hydratorManager->updateLinkedDataRecords($request, $recordId);

        // Get the updated Feedback record, using GetResponseDataService, to return the updated record data in the response with masked fields
        $updatedRecord = $this->getResponseDataService->getResponseData(['feedback.id' => $recordId], ['limit' => 1, 'offset' => 0, 'orderBy' => [['recordid', 'ASC']]]);

        if (!isset($updatedRecord['data'][0])) {
            throw new Exception("An error occured when trying to return the data for the record with ID {$recordId}", Http::INTERNAL_SERVER_ERROR);
        }

        return $this->responseService->formatSuccessPatchResponse($response, $updatedRecord['data'][0]);
    }
}
