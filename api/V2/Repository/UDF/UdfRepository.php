<?php

declare(strict_types=1);

namespace api\V2\Repository\UDF;

use Doctrine\DBAL\Connection;

class UdfRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getUDFType(int $fieldId): array
    {
        $queryBuilder = $this->connection->createQueryBuilder()
            ->select('fld_type')
            ->from('udf_fields')
            ->where('recordid = :id')
            ->setParameter('id', $fieldId);

        return $queryBuilder->executeQuery()->fetchFirstColumn();
    }
}
