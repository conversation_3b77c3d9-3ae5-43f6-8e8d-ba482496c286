<?php

declare(strict_types=1);

namespace api\V2\Repository\Etl;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Exception;
use src\logger\Facade\Log;
use Throwable;

class EtlTableSyncRepository
{
    private Connection $connection;
    private array $totalRowCache = [];

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getTotalRows(string $tableName): int
    {
        if (!isset($this->totalRowCache[$tableName])) {
            try {
                $qb = $this->connection->createQueryBuilder();
                $this->totalRowCache[$tableName] = (int) $qb->select('COUNT(*)')->from($tableName)
                    ->executeQuery()
                    ->fetchOne();
            } catch (Throwable $e) {
                Log::error(
                    'Error occurred while fetching the total count.',
                    [
                        'tableName' => $tableName,
                        'exception' => $e,
                    ],
                );

                throw $e;
            }
        }

        return (int) $this->totalRowCache[$tableName];
    }

    public function updateFullTable(string $tableName, string $columnToUpdate): void
    {
        $sql = <<<SQL
            UPDATE {$tableName}
            SET {$columnToUpdate} = {$columnToUpdate}
            SQL;
        $stmt = $this->connection->prepare($sql);
        $stmt->executeQuery();
    }

    public function updateBatch(string $tableName, string $identifier, string $columnToUpdate, int $start, int $end): void
    {
        $sql = <<<SQL
            UPDATE t1
            SET t1.{$columnToUpdate} = t1.{$columnToUpdate}
            FROM (
                SELECT
                    {$columnToUpdate},
                    ROW_NUMBER() OVER (ORDER BY {$identifier}) AS RowNumber
                FROM {$tableName}
            ) AS t1
            WHERE
               RowNumber BETWEEN :startRowNumber AND :endRowNumber
            SQL;
        $stmt = $this->connection->prepare($sql);
        $stmt->executeQuery(['startRowNumber' => $start, 'endRowNumber' => $end]);
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getColumns(string $tableName): array
    {
        $sql = <<<'SQL'
            SELECT QUOTENAME(C.COLUMN_NAME) AS COLUMN_NAME,
                       CASE
                           WHEN PKs.COLUMN_NAME IS NULL
                               THEN 0
                           ELSE 1 END AS IsPrimaryKey
                FROM INFORMATION_SCHEMA.COLUMNS AS C
                    LEFT JOIN (
                    SELECT CCU.COLUMN_NAME
                    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS TC
                        JOIN INFORMATION_SCHEMA.CONSTRAINT_COLUMN_USAGE AS CCU
                            ON
                                CCU.CONSTRAINT_NAME = TC.CONSTRAINT_NAME
                    WHERE
                        TC.TABLE_NAME = :table
                      AND
                        TC.CONSTRAINT_TYPE = 'PRIMARY KEY'
                    ) AS PKs
                        ON PKs.COLUMN_NAME = C.COLUMN_NAME
                WHERE C.TABLE_NAME = :table
            SQL;

        return $this->connection->executeQuery(
            $sql,
            ['table' => $tableName],
        )->fetchAllAssociative();
    }

    public function quoteName(string $name): string
    {
        $sql = 'SELECT QUOTENAME(:name)';

        return $this->connection->executeQuery(
            $sql,
            ['name' => $name],
        )->fetchOne();
    }

    /**
     * @param $tableNames string[] - A list of table names to check
     *
     * @return string[] - A list of tables that were confirmed to exist
     *
     * @throws Exception
     * @throws \Doctrine\DBAL\Exception If there's an issue communicating with the database
     */
    public function checkTablesExists(array $tableNames): array
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder->select('TABLE_NAME')
            ->from('INFORMATION_SCHEMA.TABLES')
            ->where('TABLE_CATALOG  = :catalog')
            ->andWhere($queryBuilder->expr()->in('TABLE_NAME', ':tableName'))
            ->setParameter('catalog', $this->connection->getDatabase())
            ->setParameter('tableName', $tableNames, Connection::PARAM_STR_ARRAY);

        return $queryBuilder->executeQuery()->fetchFirstColumn();
    }
}
