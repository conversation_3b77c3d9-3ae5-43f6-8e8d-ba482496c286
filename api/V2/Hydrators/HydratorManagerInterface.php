<?php

declare(strict_types=1);

namespace api\V2\Hydrators;

use Psr\Http\Message\ServerRequestInterface as Request;

interface HydratorManagerInterface
{
    /**
     * The following constants/parameters are required for HydratorManagers, however as prior to PHP 8.1.0 constants in interfaces
     * can't be overwritten, they can not be added to the interface.
     *
     * private const READ_ONLY_FIELDS = [];
     * private const READ_ONLY_FLAG_FIELDS = [];
     * private const SECTION_ENTITIES = [];
     * private const SECTION_HYDRATORS = [];
     * private const SECTION_VALUE_OBJECT = [];
     * private const ALLOWED_NON_REQUEST_FIELDS = [];
     * private array $allowedUnmappedFields = [];
     */

    /**
     * Hydrates the main entity with data from the request data. Linked entities should also be hydrated here so that a
     * single persist call can be used to update all main record data and joined link data in one go. Linked entities to
     * be removed can also be done against the main entity and the removeElement() method called against the linked entity
     * collection. The same can also be done for new entities not currently attached to the main record using add() against
     * the linked entity collection . This method should ensure any data being inserted on the entity has been sanitised
     * to prevent changing of read only fields, values extracted from data arrays in necessary and return the main entity
     * with all data updated as needed.
     *
     * @return mixed
     */
    public function hydrateEntitiesFromRequest(Request $request, int $recordId);

    /**
     * Updates any linked data that can't be handled against the main entity, such as udfs and documents. This method should
     * do similar things to hydrateEntitiesFromRequest() to ensure data is properly sanitised and formatted before making
     * changes to the database.
     *
     * @return mixed
     */
    public function updateLinkedDataRecords(Request $request, int $recordId);
}
