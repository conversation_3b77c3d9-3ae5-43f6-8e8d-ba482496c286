<?php

declare(strict_types=1);

namespace api\V2\Hydrators;

use api\V2\ValueObjects\DocumentsData;
use app\models\document\entities\LinkedDocumentEntity;
use app\models\generic\valueObjects\Module;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\users\model\User;

use function is_array;

trait DocumentsManagerTrait
{
    private $documentsAllowedUnmappedFields = [
        LinkedDocumentEntity::class => [
            'documentId',
            'file',
            'contentType',
        ],
    ];

    private function processDocumentsData(Request $request, array $documentsData, int $recordId): void
    {
        $feedbackModule = $this->moduleService->getModuleObject(Module::FEEDBACK);

        $user = $this->requestService->extractUserFromRequest($request);
        $existingDocuments = $this->documentUploadService->getExistingLinkedDocuments($feedbackModule->getCode(), $recordId);
        $documentsToRemove = $this->getEntitiesToRemove($documentsData, $existingDocuments);

        foreach ($documentsToRemove as $documentEntity) {
            $this->documentUploadService->remove($documentEntity->getId());
        }

        $documentsToUpload = $this->prepareDocumentsDataForUpload($documentsData, $user);

        $this->documentUploadService->upload($feedbackModule->getCode(), $recordId, $documentsToUpload);

        $documentsToUpdate = $this->prepareDocumentsDataForUpdate($documentsData);

        foreach ($documentsToUpdate as $documentData) {
            $this->documentUploadService->update($documentData);
        }
    }

    /**
     * Sets up the data from the request so that it's formatted for the DocumentUploadService::upload() method.
     *
     * @param array $documentsData The array of document data from the request to use for creating new documents
     * @param User $user The user creating the record
     */
    private function prepareDocumentsDataForUpload(array $documentsData, User $user): array
    {
        $documentsToUpload = [];

        foreach ($documentsData as $documentData) {
            if (
                !is_array($documentData)
                || isset($documentData['id'])
            ) {
                continue;
            }

            $entity = new LinkedDocumentEntity();

            $documentData = $this->readOnlyFieldService->removeReadOnlyData(
                $documentData,
                $this->fieldMapService->getFieldMap('documents'),
                self::READ_ONLY_FIELDS['documents'],
            );

            $preparedData = $this->prepareDataForHydrator($documentData, $this->fieldMapService->getFieldMap('documents'), $entity);

            $documentDataObject = new DocumentsData();

            $documentDataObject->addDocumentToUpload(
                $user->getInitials(),
                $preparedData['file'],
                $preparedData['contentType'],
                $preparedData['type'],
                $preparedData['description'],
            );

            $documentsToUpload[] = $documentDataObject->getData();
        }

        return $documentsToUpload;
    }

    /**
     * Sets up the document data from the request so that it's formatted for the DocumentUploadService::update() method.
     *
     * @param array $documentsData The array of document data from the request
     */
    private function prepareDocumentsDataForUpdate(array $documentsData): array
    {
        $documentsToUpdate = [];

        foreach ($documentsData as $documentData) {
            if (
                !is_array($documentData)
                || !isset($documentData['id'])
            ) {
                continue;
            }

            $entity = $this->entityManager->find(LinkedDocumentEntity::class, $documentData['id']);

            $documentData = $this->readOnlyFieldService->removeReadOnlyData(
                $documentData,
                $this->fieldMapService->getFieldMap('documents'),
                self::READ_ONLY_FIELDS['documents'],
            );

            if ($entity instanceof LinkedDocumentEntity) {
                $preparedData = $this->prepareDataForHydrator($documentData, $this->fieldMapService->getFieldMap('documents'), $entity);

                $documentDataObject = new DocumentsData();

                $documentDataObject->addDocumentToUpdate(
                    $entity->getId(),
                    $preparedData['type'],
                    $preparedData['description'],
                );

                $documentsToUpdate[] = $documentDataObject->getData();
            }
        }

        return $documentsToUpdate;
    }
}
