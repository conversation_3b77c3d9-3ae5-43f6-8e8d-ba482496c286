<?php

declare(strict_types=1);

namespace api\V2\Hydrators;

use api\V2\ValueObjects\EntityDataInterface;
use DateTime;
use Exception;
use src\system\database\FieldInterface;
use src\users\model\User;

use function is_int;
use function is_string;
use function is_array;
use function in_array;
use function get_class;

trait LinkedEntitiesManagerTrait
{
    /**
     * Returns an array of entities that are currently attached to the main entity, but were not included in the request
     * data as either an array of data or an integer for the entity id to keep.
     *
     * @param array $requestLinkedData Linked data from request holding details of the records to keep
     * @param mixed $existingEntities Array|object of entities that already exist against the main record
     */
    private function getEntitiesToRemove(array $requestLinkedData, $existingEntities): array
    {
        $entitiesToRemove = [];
        $entitiesToKeep = [];

        foreach ($requestLinkedData as $recordData) {
            if (is_int($recordData) || is_string($recordData)) {
                $entitiesToKeep[] = (int) $recordData;
            } elseif (is_array($recordData) && isset($recordData['id'])) {
                $entitiesToKeep[] = (int) $recordData['id'];
            }
        }

        foreach ($existingEntities as $entity) {
            if (method_exists($entity, 'getRecordid')) {
                $id = (int) $entity->getRecordid();
            } else {
                $id = (int) $entity->getId();
            }

            if (!in_array($id, $entitiesToKeep, true)) {
                $entitiesToRemove[] = $entity;
            }
        }

        return $entitiesToRemove;
    }

    /**
     * Processes the linked data and uses it to hydrate linked entities against the main record and an array of new entities
     * not currently on the main entity so they can be added to the correct section.
     *
     * @param array $linkedData Array of all linked data attached to the main entity
     * @param object $mainEntity The main entity to attach linked entities to
     * @param User $user The user updating the record
     *
     * @throws Exception
     */
    private function prepareEntitiesForPersisting(array $linkedData, object $mainEntity, User $user): array
    {
        $entities = [];
        $now = (new DateTime())->format('Y-m-d H:i:s');

        foreach ($linkedData as $sectionName => $sectionData) {
            $sectionEntityClass = self::SECTION_ENTITIES[$sectionName];
            $sectionDataClass = self::SECTION_VALUE_OBJECT[$sectionName];

            foreach ($sectionData as $recordData) {
                // Items that are just an integer for an ID of a record to keep can be ignored
                if (!is_array($recordData)) {
                    continue;
                }

                $newEntity = false;
                // Clear any existing Subject entity data
                $entity = null;

                // If an id value is passed in the data use it to try and get an existing Subject entity
                if (isset($recordData['id'])) {
                    $entity = $this->entityManager->find($sectionEntityClass, $recordData['id']);
                }

                /** @var EntityDataInterface $dataObject */
                $dataObject = new $sectionDataClass($this->readOnlyFieldService->removeReadOnlyData(
                    $recordData,
                    $this->fieldMapService->getFieldMap($sectionName),
                    self::READ_ONLY_FIELDS[$sectionName],
                ));

                // If there's no existing entity create a new one and generate a new id for it
                if (!$entity instanceof $sectionEntityClass) {
                    $newEntity = true;
                    $entity = new $sectionEntityClass();

                    $idGenerator = $this->recordIdGeneratorFactory->create($this->moduleService->getTableFromSectionName($sectionName), $this->moduleService->getIdFieldFromSectionName($sectionName));

                    $recordId = $idGenerator->generateRecordId();

                    $dataObject->addNewRecord(
                        $recordId,
                        $mainEntity,
                        $now,
                        $user->getRecordid(),
                    );
                } else {
                    $dataObject->addExistingRecord($now, $user->getRecordid());
                }

                $preparedData = $this->prepareDataForHydrator(
                    $dataObject->getData(),
                    $this->fieldMapService->getFieldMap($sectionName),
                    $entity,
                );

                $hydratorClass = self::SECTION_HYDRATORS[$sectionName];
                $hydrator = new $hydratorClass();

                $hydrator->hydrate($entity, $preparedData);

                // Existing entities are updated against the main entity automatically by the entity manager, so
                // don't need to be added to the main entity again
                if ($newEntity) {
                    $entities[$sectionName][] = $entity;
                }
            }
        }

        return $entities;
    }

    /**
     * @param array $data The array of data to prepare
     * @param array $fieldMap The fieldMap for the module
     * @param object $entity The entity to use for checking Read Only fields, if required
     */
    private function prepareDataForHydrator(array $data, array $fieldMap, object $entity): array
    {
        $preparedData = [];

        foreach ($data as $fieldName => $fieldValue) {
            $field = $this->fieldDefsService->getRegistryFieldDefinitionFromMap($fieldName, $fieldMap);

            if ($field instanceof FieldInterface) {
                $preparedData[$fieldName] = $this->dataFormatterService->formatForPatch($field, $fieldValue);
            }
            // Include non mapped field data for specific entities, these shouldn't need any formatting as they are set
            // on the data manually
            elseif (in_array($fieldName, $this->allowedUnmappedFields[get_class($entity)], true)) {
                $preparedData[$fieldName] = $fieldValue;
            }
        }

        return $preparedData;
    }

    private function removeEntities(array $linkedData, object $existingEntityCollection): void
    {
        $subjectsToRemove = $this->getEntitiesToRemove($linkedData, $existingEntityCollection);

        foreach ($subjectsToRemove as $subject) {
            $existingEntityCollection->removeElement($subject);
        }
    }

    private function addEntities(array $newLinkedEntities, object $entityCollection): void
    {
        // Adds new subjects to the main entity
        foreach ($newLinkedEntities as $entity) {
            $entityCollection->add($entity);
        }
    }
}
