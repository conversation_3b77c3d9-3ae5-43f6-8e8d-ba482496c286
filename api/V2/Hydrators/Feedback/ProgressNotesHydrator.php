<?php

declare(strict_types=1);

namespace api\V2\Hydrators\Feedback;

use app\models\progressNote\entities\ProgressNoteEntity;

use function array_key_exists;

class ProgressNotesHydrator
{
    /**
     * @param ProgressNoteEntity $entity The entity to hydrate
     * @param array $data The data to hydrate the entity with
     */
    public function hydrate(ProgressNoteEntity $entity, array $data): ProgressNoteEntity
    {
        if (array_key_exists('feedback', $data)) {
            $entity->setFeedback($data['feedback']);
        }

        if (array_key_exists('id', $data)) {
            $entity->setRecordid((int) $data['id']);
        }

        if (array_key_exists('note', $data)) {
            $entity->setPnoProgressNotes($data['note']);
        }

        if (array_key_exists('created', $data)) {
            $entity->setPnoCreateddate($data['created']);
        }

        if (array_key_exists('createdBy', $data)) {
            $entity->setPnoCreatedby($data['createdBy']);
        }

        if (array_key_exists('updated', $data)) {
            $entity->setPnoUpdateddate($data['updated']);
        }

        if (array_key_exists('updatedBy', $data)) {
            $entity->setPnoUpdatedby($data['updatedBy']);
        }

        return $entity;
    }
}
