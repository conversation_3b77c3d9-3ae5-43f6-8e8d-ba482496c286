<?php

declare(strict_types=1);

namespace api\V2\Hydrators\Feedback;

use app\models\feedback\entities\FeedbackSubjectEntity;

use function array_key_exists;

class SubjectsHydrator
{
    /**
     * @param FeedbackSubjectEntity $entity The entity to hydrate
     * @param array $data The data to hydrate the entity with
     */
    public function hydrate(FeedbackSubjectEntity $entity, array $data): FeedbackSubjectEntity
    {
        if (array_key_exists('feedback', $data)) {
            $entity->setFeedback($data['feedback']);
        }

        if (array_key_exists('id', $data)) {
            $entity->setRecordid((int) $data['id']);
        }

        if (array_key_exists('subject', $data)) {
            $entity->setComSubject($data['subject']);
        }

        if (array_key_exists('subSubject', $data)) {
            $entity->setComSubsubject($data['subSubject']);
        }

        if (array_key_exists('staffType', $data)) {
            $entity->setComStafftype($data['staffType']);
        }

        if (array_key_exists('outcomeCode', $data)) {
            $entity->setComOutcome($data['outcomeCode']);
        }

        if (array_key_exists('completedDate', $data)) {
            $entity->setCsuDcompleted($data['completedDate']);
        }

        if (array_key_exists('subjectNotes', $data)) {
            $entity->setCsuNotes($data['subjectNotes']);
        }

        if (array_key_exists('serviceArea', $data)) {
            $entity->setComServiceArea($data['serviceArea']);
        }

        if (array_key_exists('location', $data)) {
            $entity->setCsuLocationId($data['location']);
        }

        if (array_key_exists('service', $data)) {
            $entity->setCsuServiceId($data['service']);
        }

        if (array_key_exists('subType', $data)) {
            $entity->setComSubtype($data['subType']);
        }

        if (array_key_exists('type', $data)) {
            $entity->setComType($data['type']);
        }

        if (array_key_exists('levelOfHarm', $data)) {
            $entity->setCsuLevelOfHarm($data['levelOfHarm']);
        }

        if (array_key_exists('issuePathway', $data)) {
            $entity->setComIssuePathway($data['issuePathway']);
        }

        if (array_key_exists('issueType', $data)) {
            $entity->setComIssueType($data['issueType']);
        }

        if (array_key_exists('order', $data)) {
            $entity->setListorder($data['order']);
        }

        return $entity;
    }
}
