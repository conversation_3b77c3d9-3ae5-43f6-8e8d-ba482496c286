<?php

declare(strict_types=1);

namespace api\V2\Hydrators\Feedback;

use api\V2\Hydrators\DocumentsManagerTrait;
use api\V2\Hydrators\HydratorManagerInterface;
use api\V2\Hydrators\LinkedEntitiesManagerTrait;
use api\V2\Hydrators\UdfManagerTrait;
use api\V2\Services\DataFormatterService;
use api\V2\Services\Feedback\FeedbackEntityCacheService;
use api\V2\Services\FieldDefsService;
use api\V2\Services\FieldMapService;
use api\V2\Services\ModuleService;
use api\V2\Services\ReadOnlyFieldService;
use api\V2\Services\RequestService;
use api\V2\ValueObjects\DocumentsData;
use api\V2\ValueObjects\ProgressNotesData;
use api\V2\ValueObjects\SubjectsData;
use api\V2\ValueObjects\UdfData;
use app\models\feedback\entities\FeedbackEntity;
use app\models\feedback\entities\FeedbackProgressNoteEntity;
use app\models\feedback\entities\FeedbackSubjectEntity;
use app\services\document\storagehandlers\DocumentApiUploadService;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\udf\UdfService;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Http\Message\ServerRequestInterface as Request;

use function get_class;

class FeedbackHydratorManager implements HydratorManagerInterface
{
    use DocumentsManagerTrait;
    use UdfManagerTrait;
    use LinkedEntitiesManagerTrait;
    private const READ_ONLY_FIELDS = [
        'feedback' => [
            'recordid',
            'uuid',
            'source_of_record',
            'com_last_updated',
        ],
        'subjects' => [
            'recordid',
        ],
        'progressNotes' => [
            'recordid',
            'pno_createddate',
            'pno_createdby',
            'pno_updateddate',
            'pno_updatedby',
        ],
        'documents' => [
            'id',
        ],
    ];
    private const READ_ONLY_FLAG_FIELDS = [
        'feedback' => [
            'flag_for_investigation',
            'flag_for_rib',
        ],
    ];
    private const SECTION_ENTITIES = [
        'subjects' => FeedbackSubjectEntity::class,
        'progressNotes' => FeedbackProgressNoteEntity::class,
    ];
    private const SECTION_HYDRATORS = [
        'subjects' => SubjectsHydrator::class,
        'progressNotes' => ProgressNotesHydrator::class,
    ];
    private const SECTION_VALUE_OBJECT = [
        'subjects' => SubjectsData::class,
        'progressNotes' => ProgressNotesData::class,
        'documents' => DocumentsData::class,
        'extraFields' => UdfData::class,
    ];
    private array $allowedUnmappedFields;
    private ReadOnlyFieldService $readOnlyFieldService;
    private FieldDefsService $fieldDefsService;
    private RequestService $requestService;
    private FeedbackEntity $moduleEntity;
    private EntityManagerInterface $entityManager;
    private ModuleService $moduleService;
    private RecordIdGeneratorFactory $recordIdGeneratorFactory;
    private FeedbackHydrator $feedbackHydrator;
    private UdfService $udfService;
    private DocumentApiUploadService $documentUploadService;
    private FieldMapService $fieldMapService;
    private DataFormatterService $dataFormatterService;
    private FeedbackEntityCacheService $entityCacheService;

    public function __construct(
        ReadOnlyFieldService $readOnlyFieldService,
        FieldDefsService $fieldDefsService,
        RequestService $requestService,
        FeedbackEntity $moduleEntity,
        EntityManagerInterface $entityManager,
        ModuleService $moduleService,
        RecordIdGeneratorFactory $recordIdGeneratorFactory,
        FeedbackHydrator $feedbackHydrator,
        UdfService $udfService,
        DocumentApiUploadService $documentUploadService,
        FieldMapService $fieldMapService,
        DataFormatterService $dataFormatterService,
        FeedbackEntityCacheService $entityCacheService
    ) {
        $this->readOnlyFieldService = $readOnlyFieldService;
        $this->fieldDefsService = $fieldDefsService;
        $this->requestService = $requestService;
        $this->moduleEntity = $moduleEntity;
        $this->entityManager = $entityManager;
        $this->moduleService = $moduleService;
        $this->recordIdGeneratorFactory = $recordIdGeneratorFactory;
        $this->feedbackHydrator = $feedbackHydrator;
        $this->udfService = $udfService;
        $this->documentUploadService = $documentUploadService;
        $this->fieldMapService = $fieldMapService;
        $this->dataFormatterService = $dataFormatterService;
        $this->entityCacheService = $entityCacheService;

        // Merge allowed non request fields from traits into the main parameter
        $this->allowedUnmappedFields = array_merge(
            [
                FeedbackEntity::class => [
                    'updatedDate',
                    'updatedBy',
                ],
                FeedbackSubjectEntity::class => [
                    'feedback',
                ],
                FeedbackProgressNoteEntity::class => [
                    'feedback',
                ],
            ],
            $this->documentsAllowedUnmappedFields,
        );
    }

    /**
     * Hydrates the main entity and handles add/update/delete of linked entities against the main record.
     *
     * @throws Exception
     */
    public function hydrateEntitiesFromRequest(Request $request, int $recordId): FeedbackEntity
    {
        $cachedEntity = $this->entityCacheService->getCachedEntity($recordId);
        if ($cachedEntity !== null) {
            return $cachedEntity;
        }

        $linkedData = [];
        $user = $this->requestService->extractUserFromRequest($request);

        $feedbackEntityClassName = get_class($this->moduleEntity);

        /** @var FeedbackEntity $recordEntity */
        $recordEntity = $this->entityManager->find($feedbackEntityClassName, $recordId);

        $feedbackData = $this->readOnlyFieldService->removeReadOnlyData(
            $this->requestService->extractMainRecordDataFromRequest($request),
            $this->fieldMapService->getFieldMap('module'),
            self::READ_ONLY_FIELDS['feedback'],
            $recordEntity,
            self::READ_ONLY_FLAG_FIELDS['feedback'],
        );

        $feedbackData['updatedDate'] = (new DateTime())->format('Y-m-d H:i:s');
        $feedbackData['updatedBy'] = $user->getInitials();

        $linkedData['subjects'] = $this->requestService->extractLinkedDataFromRequest($request, 'subjects');
        $linkedData['progressNotes'] = $this->requestService->extractLinkedDataFromRequest($request, 'progressNotes');

        // Set subjectsAdded to 'Y' if there's subjects data in the request
        if (!empty($linkedData['subjects'])) {
            $feedbackData['subjectsAdded'] = 'Y';
        }

        $preparedData = $this->prepareDataForHydrator($feedbackData, $this->fieldMapService->getFieldMap('module'), $recordEntity);

        $this->feedbackHydrator->hydrate($recordEntity, $preparedData);

        // Get the entities to be removed from the mainEntity to allow removal on persist, done before anything is
        // added/updated so that they don't get removed straight after
        if (isset($linkedData['subjects'])) {
            $this->removeEntities($linkedData['subjects'], $recordEntity->getSubjects());
        }

        if (isset($linkedData['progressNotes'])) {
            $this->removeEntities($linkedData['progressNotes'], $recordEntity->getProgressNotes());
        }

        // Update any existing entities that need updated and return any new entities to be added to the main entity
        $newLinkedEntities = $this->prepareEntitiesForPersisting($linkedData, $recordEntity, $user);

        if (!empty($newLinkedEntities['subjects'])) {
            $this->addEntities($newLinkedEntities['subjects'], $recordEntity->getSubjects());
        }

        if (!empty($newLinkedEntities['progressNotes'])) {
            $this->addEntities($newLinkedEntities['progressNotes'], $recordEntity->getProgressNotes());
        }

        return $this->entityCacheService->setCachedEntity($recordId, $recordEntity);
    }

    /**
     * Handles add/update/delete of non linked entity data against stored against the main record.
     *
     * @param Request $request The request to get linked data from
     * @param int $recordId The record id of the main record to attach data to
     *
     * @throws \Doctrine\ORM\ORMException
     */
    public function updateLinkedDataRecords(Request $request, int $recordId): void
    {
        $udfData = $this->prepareUdfData($this->requestService->extractUdfDataFromRequest($request));

        if (!empty($udfData)) {
            $this->processUdfData($udfData, $recordId);
        }

        // DOCUMENTS
        $documentsData = $this->requestService->extractLinkedDataFromRequest($request, 'documents');

        if (isset($documentsData)) {
            $this->processDocumentsData($request, $documentsData, $recordId);
        }
    }
}
