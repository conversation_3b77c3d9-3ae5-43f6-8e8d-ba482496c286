<?php

declare(strict_types=1);

namespace api\V2\Hydrators\Feedback;

use app\models\feedback\entities\FeedbackEntity;

use function array_key_exists;

class FeedbackHydrator
{
    /**
     * @param FeedbackEntity $entity The entity to hydrate
     * @param array $data The data to hydrate the entity with
     *
     * @return FeedbackEntity
     */
    public function hydrate(FeedbackEntity $entity, array $data): void
    {
        if (array_key_exists('name', $data)) {
            $entity->setComName($data['name']);
        }

        if (array_key_exists('reference', $data)) {
            $entity->setComOurref($data['reference']);
        }

        if (array_key_exists('approvalStatus', $data)) {
            $entity->setRepApproved($data['approvalStatus']);
        }

        if (array_key_exists('handler', $data)) {
            $entity->setComMgr($data['handler']);
        }

        if (array_key_exists('manager', $data)) {
            $entity->setComHead($data['manager']);
        }

        if (array_key_exists('opened', $data)) {
            $entity->setComDopened($data['opened']);
        }

        if (array_key_exists('closed', $data)) {
            $entity->setComDclosed($data['closed']);
        }

        if (array_key_exists('reopened', $data)) {
            $entity->setComDreopened($data['reopened']);
        }

        if (array_key_exists('method', $data)) {
            $entity->setComMethod($data['method']);
        }

        if (array_key_exists('type', $data)) {
            $entity->setComType($data['type']);
        }

        if (array_key_exists('subType', $data)) {
            $entity->setComSubtype($data['subType']);
        }

        if (array_key_exists('incidentType', $data)) {
            $entity->setComIncType($data['incidentType']);
        }

        if (array_key_exists('firstReceived', $data)) {
            $entity->setComDreceived($data['firstReceived']);
        }

        if (array_key_exists('consentObtained', $data)) {
            $entity->setComConsent($data['consentObtained']);
        }

        if (array_key_exists('incidentDate', $data)) {
            $entity->setComDincident($data['incidentDate']);
        }

        if (array_key_exists('description', $data)) {
            $entity->setComDetail($data['description']);
        }

        if (array_key_exists('commissioner', $data)) {
            $entity->setComPurchaser($data['commissioner']);
        }

        if (array_key_exists('otherReference', $data)) {
            $entity->setComOtherref($data['otherReference']);
        }

        if (array_key_exists('currentStage', $data)) {
            $entity->setComCurstage($data['currentStage']);
        }

        if (array_key_exists('outcomeCode', $data)) {
            $entity->setComOutcome($data['outcomeCode']);
        }

        if (array_key_exists('outcome', $data)) {
            $entity->setComSummary($data['outcome']);
        }

        if (array_key_exists('flagForInvestigation', $data)) {
            $entity->setFlagForInvestigation($data['flagForInvestigation']);
        }

        if (array_key_exists('flagForRIB', $data)) {
            $entity->setFlagForRib($data['flagForRIB']);
        }

        if (array_key_exists('requestedConsultant', $data)) {
            $entity->setRequestedConsultant($data['requestedConsultant']);
        }

        if (array_key_exists('priorityScale', $data)) {
            $entity->setPriorityScale($data['priorityScale']);
        }

        if (array_key_exists('isThisRecordSensitive', $data)) {
            $entity->setIsRecordSensitive($data['isThisRecordSensitive']);
        }

        if (array_key_exists('hROCharacteristics', $data)) {
            $entity->setComHroCharacteristics($data['hROCharacteristics']);
        }

        if (array_key_exists('specialty', $data)) {
            $entity->setComSpecialty($data['specialty']);
        }

        if (array_key_exists('mCAOrNA', $data)) {
            $entity->setMcaOrNa($data['mCAOrNA']);
        }

        if (array_key_exists('patientExpectations', $data)) {
            $entity->setComPatExpectations($data['patientExpectations']);
        }

        if (array_key_exists('patientUpdatePreference', $data)) {
            $entity->setComPatUpdatePref($data['patientUpdatePreference']);
        }

        if (array_key_exists('complaintGradingAtOutcome', $data)) {
            $entity->setComOutcomeGrading($data['complaintGradingAtOutcome']);
        }

        if (array_key_exists('linkedToASeriousIncident', $data)) {
            $entity->setComSeriousIncident($data['linkedToASeriousIncident']);
        }

        if (array_key_exists('communicateThroughWelshLanguage', $data)) {
            $entity->setComWelshLanguage($data['communicateThroughWelshLanguage']);
        }

        if (array_key_exists('escalatedToARedressCase', $data)) {
            $entity->setComRedressEscalated($data['escalatedToARedressCase']);
        }

        if (array_key_exists('outbreakImpact', $data)) {
            $entity->setOutbreakImpact($data['outbreakImpact']);
        }

        if (array_key_exists('outbreakType', $data)) {
            $entity->setOutbreakType($data['outbreakType']);
        }

        if (array_key_exists('additionalPeopleAffected', $data)) {
            $entity->setShowPerson($data['additionalPeopleAffected']);
        }

        if (array_key_exists('referredToTheOmbudsman', $data)) {
            $entity->setReferredToOmbudsman($data['referredToTheOmbudsman']);
        }

        if (array_key_exists('dateEarlySettlementProposalReceived', $data)) {
            $entity->setEarlySettlementProposalDateReceived($data['dateEarlySettlementProposalReceived']);
        }

        if (array_key_exists('dateProposalResponseRequested', $data)) {
            $entity->setEarlySettlementProposalDateRequested($data['dateProposalResponseRequested']);
        }

        if (array_key_exists('dateProposalResponseSubmitted', $data)) {
            $entity->setEarlySettlementProposalDateSubmitted($data['dateProposalResponseSubmitted']);
        }

        if (array_key_exists('dateOfFirstContact', $data)) {
            $entity->setDateFirstContact($data['dateOfFirstContact']);
        }

        if (array_key_exists('dateEvidenceDue', $data)) {
            $entity->setDateEvidenceDue($data['dateEvidenceDue']);
        }

        if (array_key_exists('dateEvidenceSubmitted', $data)) {
            $entity->setDateEvidenceSubmitted($data['dateEvidenceSubmitted']);
        }

        if (array_key_exists('ombudsmanReference', $data)) {
            $entity->setOmbudsmanReference($data['ombudsmanReference']);
        }

        if (array_key_exists('ombudsmanHandler', $data)) {
            $entity->setOmbudsmanHandler($data['ombudsmanHandler']);
        }

        if (array_key_exists('ombudsmanCurrentStage', $data)) {
            $entity->setOmbudsmanCurrentStage($data['ombudsmanCurrentStage']);
        }

        if (array_key_exists('ombudsmanEarlySettlementProposal', $data)) {
            $entity->setEarlySettlementProposal($data['ombudsmanEarlySettlementProposal']);
        }

        if (array_key_exists('dateOmbudsmanInvestigationBegan', $data)) {
            $entity->setDateInvestigationBegan($data['dateOmbudsmanInvestigationBegan']);
        }

        if (array_key_exists('dateInvestigationResponseDue', $data)) {
            $entity->setDateResponseDue($data['dateInvestigationResponseDue']);
        }

        if (array_key_exists('dateResponseDocumentsSubmitted', $data)) {
            $entity->setDateDocInvSub($data['dateResponseDocumentsSubmitted']);
        }

        if (array_key_exists('dateResponseSubmitted', $data)) {
            $entity->setDateInvSub($data['dateResponseSubmitted']);
        }

        if (array_key_exists('dateDraftReportReceived', $data)) {
            $entity->setDateDraftReceived($data['dateDraftReportReceived']);
        }

        if (array_key_exists('dateDraftReportResponseDue', $data)) {
            $entity->setDateDraftResponseDue($data['dateDraftReportResponseDue']);
        }

        if (array_key_exists('dateDraftRecommendationsReviewed', $data)) {
            $entity->setDateRecRecieved($data['dateDraftRecommendationsReviewed']);
        }

        if (array_key_exists('dateDraftRecommendationActionPlanAgreed', $data)) {
            $entity->setDateActionPlan($data['dateDraftRecommendationActionPlanAgreed']);
        }

        if (array_key_exists('dateDraftReportResponseSubmitted', $data)) {
            $entity->setDateDraftReportSub($data['dateDraftReportResponseSubmitted']);
        }

        if (array_key_exists('dateFinalReportReceived', $data)) {
            $entity->setDateReportReceived($data['dateFinalReportReceived']);
        }

        if (array_key_exists('ombudsmanFinalRepType', $data)) {
            $entity->setFinalRepType($data['ombudsmanFinalRepType']);
        }

        if (array_key_exists('ombudsmanOutcome', $data)) {
            $entity->setOmbudsmanOutcome($data['ombudsmanOutcome']);
        }

        if (array_key_exists('ombudsmanLearning', $data)) {
            $entity->setOmbudsmanLearning($data['ombudsmanLearning']);
        }

        if (array_key_exists('location', $data)) {
            $entity->setLocationId($data['location']);
        }

        if (array_key_exists('otherLocation', $data)) {
            $entity->setOtherLocation($data['otherLocation']);
        }

        if (array_key_exists('service', $data)) {
            $entity->setServiceId($data['service']);
        }

        if (array_key_exists('otherService', $data)) {
            $entity->setOtherService($data['otherService']);
        }

        if (array_key_exists('kO41Type', $data)) {
            $entity->setComKo41Type($data['kO41Type']);
        }

        if (array_key_exists('serviceAreaKO41', $data)) {
            $entity->setComKoservarea($data['serviceAreaKO41']);
        }

        if (array_key_exists('subjectKO41', $data)) {
            $entity->setComKosubject($data['subjectKO41']);
        }

        if (array_key_exists('professionKO41', $data)) {
            $entity->setComKoprof($data['professionKO41']);
        }

        if (array_key_exists('patientEthnicityKO41', $data)) {
            $entity->setComKoethnicPat($data['patientEthnicityKO41']);
        }

        if (array_key_exists('staffEthnicityKO41', $data)) {
            $entity->setComKoethnicStaff($data['staffEthnicityKO41']);
        }

        if (array_key_exists('healthSectorISD', $data)) {
            $entity->setComIsdUnit($data['healthSectorISD']);
        }

        if (array_key_exists('locationISD', $data)) {
            $entity->setComIsdLocactual($data['locationISD']);
        }

        if (array_key_exists('consentRequiredISD', $data)) {
            $entity->setComIsdConsent($data['consentRequiredISD']);
        }

        if (array_key_exists('dateConsentRequestedISD', $data)) {
            $entity->setComIsdDconsentReq($data['dateConsentRequestedISD']);
        }

        if (array_key_exists('dateConsentObtainedISD', $data)) {
            $entity->setComIsdDconsentRec($data['dateConsentObtainedISD']);
        }

        if (array_key_exists('diversityFormSentISD', $data)) {
            $entity->setComIsdDivSent($data['diversityFormSentISD']);
        }

        if (array_key_exists('refNumberAddedISD', $data)) {
            $entity->setComIsdRefAdded($data['refNumberAddedISD']);
        }

        if (array_key_exists('iASSInvolvedISD', $data)) {
            $entity->setComIsdIaasInvolved($data['iASSInvolvedISD']);
        }

        if (array_key_exists('cASInvolvedISD', $data)) {
            $entity->setComIsdCasInvolved($data['cASInvolvedISD']);
        }

        if (array_key_exists('cHINumberISD', $data)) {
            $entity->setComIsdChiNo($data['cHINumberISD']);
        }

        if (array_key_exists('responseSentWithin20DaysISD', $data)) {
            $entity->setComIsdRespSent20($data['responseSentWithin20DaysISD']);
        }

        if (array_key_exists('responseAfter20DaysReasonISD', $data)) {
            $entity->setComIsdResp20Reason($data['responseAfter20DaysReasonISD']);
        }

        if (array_key_exists('timescaleOver40DaysAgreedISD', $data)) {
            $entity->setComIsdAgree40($data['timescaleOver40DaysAgreedISD']);
        }

        if (array_key_exists('dateOfExtensionLetterISD', $data)) {
            $entity->setComIsdAgree40Date($data['dateOfExtensionLetterISD']);
        }

        if (array_key_exists('actionsTakenISD', $data)) {
            $entity->setComIsdActions($data['actionsTakenISD']);
        }

        if (array_key_exists('lastExportedISD', $data)) {
            $entity->setComIsdDexport($data['lastExportedISD']);
        }

        if (array_key_exists('reasonForResponseOver40DaysISD', $data)) {
            $entity->setComIsdResp40Reason($data['reasonForResponseOver40DaysISD']);
        }

        if (array_key_exists('serviceImprovementPlanISD', $data)) {
            $entity->setComIsdPlan($data['serviceImprovementPlanISD']);
        }

        if (array_key_exists('anyLearningsOrOutcomesToShare', $data)) {
            $entity->setLearningsToShare($data['anyLearningsOrOutcomesToShare']);
        }

        if (array_key_exists('learningTitle', $data)) {
            $entity->setLearningsTitle($data['learningTitle']);
        }

        if (array_key_exists('keyLearningsAndOutcomes', $data)) {
            $entity->setKeyLearnings($data['keyLearningsAndOutcomes']);
        }

        if (array_key_exists('investigators', $data)) {
            $entity->setComInvestigator($data['investigators']);
        }

        if (array_key_exists('investigationDateStarted', $data)) {
            $entity->setComInvDstart($data['investigationDateStarted']);
        }

        if (array_key_exists('investigationDateCompleted', $data)) {
            $entity->setComInvDcomp($data['investigationDateCompleted']);
        }

        if (array_key_exists('consequence', $data)) {
            $entity->setComConsequence($data['consequence']);
        }

        if (array_key_exists('likelihoodOfRecurrence', $data)) {
            $entity->setComLikelihood($data['likelihoodOfRecurrence']);
        }

        if (array_key_exists('grade', $data)) {
            $entity->setComGrade($data['grade']);
        }

        if (array_key_exists('outcomeOfInvestigation', $data)) {
            $entity->setComInvOutcome($data['outcomeOfInvestigation']);
        }

        if (array_key_exists('actionTakenCodes', $data)) {
            $entity->setComActionCode($data['actionTakenCodes']);
        }

        if (array_key_exists('actionTaken', $data)) {
            $entity->setComInvAction($data['actionTaken']);
        }

        if (array_key_exists('lessonsLearned', $data)) {
            $entity->setComInvLessons($data['lessonsLearned']);
        }

        if (array_key_exists('lessonsCodes', $data)) {
            $entity->setComLessonsCode($data['lessonsCodes']);
        }

        if (array_key_exists('requestReceived', $data)) {
            $entity->setComDrequest($data['requestReceived']);
        }

        if (array_key_exists('requestAcknowledgedDue', $data)) {
            $entity->setComDdueackreq($data['requestAcknowledgedDue']);
        }

        if (array_key_exists('requestAcknowledgedDone', $data)) {
            $entity->setComDackreq($data['requestAcknowledgedDone']);
        }

        if (array_key_exists('statementReceived', $data)) {
            $entity->setComDstatement($data['statementReceived']);
        }

        if (array_key_exists('decisionMadeDue', $data)) {
            $entity->setComDduedecision($data['decisionMadeDue']);
        }

        if (array_key_exists('decisionMadeDone', $data)) {
            $entity->setComDdecision($data['decisionMadeDone']);
        }

        if (array_key_exists('iRRecommended', $data)) {
            $entity->setComRecir($data['iRRecommended']);
        }

        if (array_key_exists('personProvidingFeedbackInformed', $data)) {
            $entity->setComDinform($data['personProvidingFeedbackInformed']);
        }

        if (array_key_exists('panelAppointedDue', $data)) {
            $entity->setComDduepappt($data['panelAppointedDue']);
        }

        if (array_key_exists('panelAppointedDone', $data)) {
            $entity->setComDpappt($data['panelAppointedDone']);
        }

        if (array_key_exists('draftReportPublishedDue', $data)) {
            $entity->setComDduepdraft($data['draftReportPublishedDue']);
        }

        if (array_key_exists('draftReportPublishedDone', $data)) {
            $entity->setComDpdraft($data['draftReportPublishedDone']);
        }

        if (array_key_exists('finalReportPublishedDue', $data)) {
            $entity->setComDdueppublish($data['finalReportPublishedDue']);
        }

        if (array_key_exists('finalReportPublishedDone', $data)) {
            $entity->setComDppublish($data['finalReportPublishedDone']);
        }

        if (array_key_exists('cERepliedToPersonProvidingFeedbackDue', $data)) {
            $entity->setComDduecereply($data['cERepliedToPersonProvidingFeedbackDue']);
        }

        if (array_key_exists('cERepliedToPersonProvidingFeedbackDone', $data)) {
            $entity->setComDcereply($data['cERepliedToPersonProvidingFeedbackDone']);
        }

        if (array_key_exists('layChairAppointedDue', $data)) {
            $entity->setComDduelaychair($data['layChairAppointedDue']);
        }

        if (array_key_exists('layChairAppointedDone', $data)) {
            $entity->setComDlaychair($data['layChairAppointedDone']);
        }

        if (array_key_exists('iROutcomeCode', $data)) {
            $entity->setComIrcode($data['iROutcomeCode']);
        }

        if (array_key_exists('clinicalAssessorNeeded', $data)) {
            $entity->setComAssessor($data['clinicalAssessorNeeded']);
        }

        if (array_key_exists('synopsis', $data)) {
            $entity->setComIrsynopsis($data['synopsis']);
        }

        if (array_key_exists('incidentTypeTierOne', $data)) {
            $entity->setComTypeTierOne($data['incidentTypeTierOne']);
        }

        if (array_key_exists('incidentTypeTierTwo', $data)) {
            $entity->setComTypeTierTwo($data['incidentTypeTierTwo']);
        }

        if (array_key_exists('incidentTypeTierThree', $data)) {
            $entity->setComTypeTierThree($data['incidentTypeTierThree']);
        }

        if (array_key_exists('incidentAffecting', $data)) {
            $entity->setComAffectingTierZero($data['incidentAffecting']);
        }

        if (array_key_exists('issuesAdded', $data)) {
            $entity->setComIssuesLinked($data['issuesAdded']);
        }

        if (array_key_exists('subjectsAdded', $data)) {
            $entity->setComSubjectsLinked($data['subjectsAdded']);
        }

        if (array_key_exists('lessonLearnedSubCategory', $data)) {
            $entity->setComLessonLearnedSubCategory($data['lessonLearnedSubCategory']);
        }

        if (array_key_exists('riskRating', $data)) {
            $entity->setComGradeRating($data['riskRating']);
        }

        if (array_key_exists('anyDocumentsAttached', $data)) {
            $entity->setShowDocument($data['anyDocumentsAttached']);
        }

        if (array_key_exists('updatedDate', $data)) {
            $entity->setUpdateddate($data['updatedDate']);
        }

        if (array_key_exists('updatedBy', $data)) {
            $entity->setUpdatedby($data['updatedBy']);
        }
    }
}
