<?php

declare(strict_types=1);

namespace api\V2\Hydrators;

use api\V2\ValueObjects\UdfData;
use app\models\generic\valueObjects\Module;
use src\system\database\FieldInterface;

use function in_array;

trait UdfManagerTrait
{
    /**
     * Returns an array of entities that are currently attached to the main entity, but were not included in the request
     * data as either an array of data or an integer for the entity id to keep.
     *
     * @param array $requestUdfs Linked data from request holding details of the UDFs to keep
     * @param mixed $existingUdfEntities Array|object of entities that already exist against the main record
     */
    private function getUdfsToRemove(array $requestUdfs, $existingUdfEntities): array
    {
        $udfsToRemove = [];
        $entitiesToRemove = [];

        foreach ($requestUdfs as $udfData) {
            // Only remove a UDF value if it's been set to null
            if ($udfData['udfValue'] === null) {
                $udfsToRemove[] = (int) $udfData['udfFieldId'];
            }
        }

        foreach ($existingUdfEntities as $entity) {
            $id = (int) $entity->getFieldId();

            if (in_array($id, $udfsToRemove, true)) {
                $entitiesToRemove[] = $entity;
            }
        }

        return $entitiesToRemove;
    }

    private function processUdfData(array $udfData, int $recordId): void
    {
        $feedbackModule = $this->moduleService->getModuleObject(Module::FEEDBACK);

        $existingUdfs = $this->udfService->getExistingLinkedUdfs($feedbackModule->getId(), $recordId);
        $udfsToRemove = $this->getUdfsToRemove($udfData, $existingUdfs);

        foreach ($udfsToRemove as $udfEntity) {
            $this->udfService->removeExistingValue($udfEntity);
        }

        $this->udfService->upsert($udfData, $feedbackModule->getCode(), $recordId);
    }

    /**
     * Takes the UDF data from the request and formats it for use with the UdfService::upsert method.
     *
     * @param array $udfData Array of data for UDFs against a record from the request
     *
     * @return UdfData
     */
    private function prepareUdfData(array $udfData): array
    {
        $preparedData = [];

        foreach ($udfData as $udfFieldName => $udfFieldValue) {
            $udfField = $this->fieldDefsService->getRegistryFieldDefinitionFromMap($udfFieldName);

            if ($udfField instanceof FieldInterface) {
                $formatter = $udfField->getApiFieldFormatter();
                $valueToSet = $formatter->extractValue($udfFieldValue);
                $udfFieldID = (int) str_replace('UDF_', '', $udfFieldName);

                $udfDataObject = new UdfData(
                    $udfFieldID,
                    $valueToSet,
                );

                $preparedData[] = $udfDataObject->getData();
            }
        }

        return $preparedData;
    }
}
