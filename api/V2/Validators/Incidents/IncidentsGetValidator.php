<?php

declare(strict_types=1);

namespace api\V2\Validators\Incidents;

use api\V2\Validators\GetRequestValidator;
use api\V2\Validators\System\Validators\DateCustom;
use <PERSON><PERSON>\Validator\Digits;
use Lam<PERSON>\Validator\NotEmpty;
use <PERSON><PERSON>\Validator\StringLength;

class IncidentsGetValidator extends GetRequestValidator
{
    protected array $validCoreFilterParameters = [
        'id' => [
            'required' => false,
            'error_message' => 'Invalid record ID, format should be an integer',
            'validators' => [['name' => NotEmpty::class], ['name' => Digits::class]],
        ],
    ];
    protected array $validFilterParameters = [
        'updatedDate' => [
            'required' => true,
            'validators' => [
                [
                    'name' => DateCustom::class,
                ],
            ],
        ],
        'id' => [
            'required' => false,
            'error_message' => 'Invalid filter `ID`, format should be an integer',
            'validators' => [['name' => NotEmpty::class], ['name' => Digits::class]],
        ],
        'ourRef' => [
            'required' => false,
            'error_message' => 'Invalid filter `reference`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 32]]],
        ],
        'name' => [
            'required' => false,
            'error_message' => 'Invalid filter `name`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 128]]],
        ],
        'approvalStatus' => [
            'required' => false,
            'error_message' => 'Invalid filter `approvalStatus`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'notes' => [
            'required' => false,
            'error_message' => 'Invalid filter `notes`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0]]],
        ],
        'submittedTime' => [
            'required' => false,
            'error_message' => 'Invalid filter `submittedTime`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 5]]],
        ],
        'dateOpened' => [
            'required' => false,
            'error_message' => 'Invalid filter `dateOpened`',
            'validators' => [['name' => DateCustom::class]],
        ],
        'healthServiceSite' => [
            'required' => false,
            'error_message' => 'Invalid filter `healthServiceSite`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'dateTimeReported' => [
            'required' => false,
            'error_message' => 'Invalid filter `dateTimeReported`',
            'validators' => [['name' => DateCustom::class]],
        ],
        'levelOfHarm' => [
            'required' => false,
            'error_message' => 'Invalid filter `levelOfHarm`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'isNeverEvent' => [
            'required' => false,
            'error_message' => 'Invalid filter `isNeverEvent`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 80]]],
        ],
        'managerTitle' => [
            'required' => false,
            'error_message' => 'Invalid filter `managerTitle`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'actionTaken' => [
            'required' => false,
            'error_message' => 'Invalid filter `actionTaken`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0]]],
        ],
        'incidentTime' => [
            'required' => false,
            'error_message' => 'Invalid filter `incidentTime`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 5]]],
        ],
        'typeTierZero' => [
            'required' => false,
            'error_message' => 'Invalid filter `typeTierZero`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'typeTierOne' => [
            'required' => false,
            'error_message' => 'Invalid filter `typeTierOne`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'typeTierTwo' => [
            'required' => false,
            'error_message' => 'Invalid filter `typeTierTwo`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'typeTierThree' => [
            'required' => false,
            'error_message' => 'Invalid filter `typeTierThree`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'exactLocation' => [
            'required' => false,
            'error_message' => 'Invalid filter `exactLocation`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 160]]],
        ],
        'otherLocation' => [
            'required' => false,
            'error_message' => 'Invalid filter `otherLocation`',
            'validators' => [['name' => Digits::class]],
        ],
        'location' => [
            'required' => false,
            'error_message' => 'Invalid filter `location`',
            'validators' => [['name' => Digits::class]],
        ],
        'service' => [
            'required' => false,
            'error_message' => 'Invalid filter `service`',
            'validators' => [['name' => Digits::class]],
        ],
        'otherService' => [
            'required' => false,
            'error_message' => 'Invalid filter `otherService`',
            'validators' => [['name' => Digits::class]],
        ],
        'lessonsLearnedDescription' => [
            'required' => false,
            'error_message' => 'Invalid filter `lessonsLearnedDescription`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0]]],
        ],
        'result' => [
            'required' => false,
            'error_message' => 'Invalid filter `result`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'severity' => [
            'required' => false,
            'error_message' => 'Invalid filter `severity`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'contacts.id' => [
            'required' => false,
            'error_message' => 'Invalid filter `contacts.id`',
            'validators' => [['name' => Digits::class]],
        ],
        'contacts.gender' => [
            'required' => false,
            'error_message' => 'Invalid filter `contacts.gender`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
        'contacts.age' => [
            'required' => false,
            'error_message' => 'Invalid filter `contacts.age`',
            'validators' => [['name' => StringLength::class, 'options' => ['min' => 0, 'max' => 6]]],
        ],
    ];
}
