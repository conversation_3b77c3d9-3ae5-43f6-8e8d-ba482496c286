<?php

declare(strict_types=1);

namespace api\V2\Validators\Contacts;

use api\interfaces\validator\ValidatorInterface;
use Psr\Http\Message\ServerRequestInterface as Request;

use function is_array;

class ContactsBatchValidator implements ValidatorInterface
{
    private const BODY_EMPTY_VALIDATION_ERROR = 'Request body must be a valid JSON object with contacts';
    private const CONTACT_EMPTY_VALIDATION_ERROR = 'Contact data empty';
    private const CONTACT_ARRAY_VALIDATION_ERROR = 'Contact data must be a valid array';
    private const CONTACT_NO_ID_ERROR = 'Contact data must contain the id attribute';
    private const EXPECTED_FORMAT = <<<'STR'
        Request should consist of a valid JSON object containing an array of contacts to be validated of the form
        {
            [
                {
                    "id": 1,
                    "title": "Mrs",
                    "forename": "Jude",
                    "middleName": null,
                    "surname": "bvbcvbcvbcvbcv",
                    ......
                    ......

                },
                {
                    "id": 1,
                    "title": "Mrs",
                    "forename": "<PERSON>",
                    "middleName": null,
                    "surname": "bvbcvbcvbcvbcv",
                    .......
                    .......
                }
            ]
        }
        STR;
    protected array $messages = [];

    public function validate(Request $request): bool
    {
        $body = $request->getParsedBody();

        if (empty($body) || !array_is_list($body)) {
            $this->messages[] = self::BODY_EMPTY_VALIDATION_ERROR;
        } else {
            $this->validateContacts($body);
        }

        if (!empty($this->messages)) {
            $this->messages[] = self::EXPECTED_FORMAT;
        }

        return empty($this->messages);
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    private function validateContacts(array $contactsBatch): void
    {
        foreach ($contactsBatch as $index => $contact) {
            if (empty($contact)) {
                $this->messages[$index] = self::CONTACT_EMPTY_VALIDATION_ERROR;
            } elseif (!is_array($contact)) {
                $this->messages[$index] = self::CONTACT_ARRAY_VALIDATION_ERROR;
            } elseif (empty($contact['id'])) {
                $this->messages[$index] = self::CONTACT_NO_ID_ERROR;
            }
        }
    }
}
