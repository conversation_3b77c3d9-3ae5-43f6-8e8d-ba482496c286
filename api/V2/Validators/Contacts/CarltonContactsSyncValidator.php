<?php

declare(strict_types=1);

namespace api\V2\Validators\Contacts;

use api\interfaces\validator\ValidatorInterface;
use DateTime;
use Psr\Http\Message\ServerRequestInterface;
use Throwable;

use function count;
use function array_key_exists;
use function is_string;

class CarltonContactsSyncValidator implements ValidatorInterface
{
    private const BODY_VALIDATION_ERROR = 'Request body must be a valid JSON object containing the following keys: id, lastUpdateDate';
    private const CARLTON_CONTACT_ID_VALIDATION_ERROR = '`id` is a required parameter';
    private const CARLTON_CONTACT_LAST_UPDATED_DATE_VALIDATION_ERROR = '`lastUpdatedDate` is a required parameter';
    private const CARLTON_CONTACT_ID_NON_INTEGER_VALIDATION_ERROR = '`id` must be a non-empty integer';
    private const CARLTON_CONTACT_DATE_NOT_STRING_VALIDATION_ERROR = '`lastUpdateDate` must be a valid string date';
    private const CARLTON_CONTACT_DATE_INVALID_VALIDATION_ERROR = '`lastUpdateDate` must be a valid date';
    protected array $messages = [];

    public function validate(ServerRequestInterface $request): bool
    {
        $this->messages = [];

        $body = $request->getParsedBody();
        if (empty($body)) {
            $this->messages[] = self::BODY_VALIDATION_ERROR;

            return false;
        }

        foreach ($body as $data) {
            if (!array_key_exists('id', $data)) {
                $this->messages[] = self::CARLTON_CONTACT_ID_VALIDATION_ERROR;
            }
            if (!array_key_exists('lastUpdatedDate', $data)) {
                $this->messages[] = self::CARLTON_CONTACT_LAST_UPDATED_DATE_VALIDATION_ERROR;
            }
        }

        $idArray = array_column($body, 'id');
        if (count($idArray) !== count(array_filter($idArray, 'is_int'))) {
            $this->messages[] = self::CARLTON_CONTACT_ID_NON_INTEGER_VALIDATION_ERROR;
        }

        $dateArray = array_column($body, 'lastUpdatedDate');
        foreach ($dateArray as $date) {
            if ($date !== null) {
                if (!is_string($date)) {
                    $this->messages[] = self::CARLTON_CONTACT_DATE_NOT_STRING_VALIDATION_ERROR;

                    break;
                }
                if (!$this->validateDate($date)) {
                    $this->messages[] = self::CARLTON_CONTACT_DATE_INVALID_VALIDATION_ERROR;
                }
            }
        }

        return empty($this->messages);
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    private function validateDate(string $date): bool
    {
        try {
            new DateTime($date);
            $errors = DateTime::getLastErrors();
        } catch (Throwable $ex) {
            return false;
        }

        return empty($errors['warnings']);
    }
}
