<?php

declare(strict_types=1);

namespace api\V2\Validators;

use api\V2\Services\RequestService;
use api\validators\RequestValidator;
use <PERSON><PERSON>\InputFilter\InputFilter;
use Lam<PERSON>\Validator;
use Laminas\Validator\Digits;
use Psr\Http\Message\ServerRequestInterface as Request;

class GetRequestValidator extends RequestValidator
{
    protected array $validFilterParameters = [];
    protected array $validCoreFilterParameters = [];

    public function __invoke(): bool
    {
        $this->applyConfig($this->getConfig());

        return true;
    }

    public function validate(Request $request): bool
    {
        $queryParams = $request->getQueryParams();

        $this->addExceptedParamValidation($this->filter, $queryParams, $this->getConfig());

        if (isset($queryParams['where'])) {
            $this->addExceptedParamValidation($this->filter, $queryParams['where'], $this->validFilterParameters);
        }

        if (isset($queryParams['selectUdf'])) {
            $this->addExceptedUdfParamValidation($this->filter, $queryParams['selectUdf']);
        }

        return parent::validate($request);
    }

    public function getConfig(): array
    {
        return [
            'limit' => [
                'required' => false,
                'error_message' => 'Limit should be a number and less than ' . RequestService::API_GET_LIMIT_MAX,
                'validators' => [
                    [
                        'name' => Validator\LessThan::class,
                        'options' => [
                            'max' => RequestService::API_GET_LIMIT_MAX,
                            'inclusive' => true,
                        ],
                    ],
                    ['name' => Validator\Digits::class],
                ],
            ],
            'offset' => [
                'required' => false,
                'error_message' => 'Offset must be a number and greater than 0',
                'validators' => [
                    [
                        'name' => Validator\GreaterThan::class,
                        'options' => [
                            'min' => 0,
                            'inclusive' => true,
                        ],
                    ],
                    ['name' => Validator\Digits::class],
                ],
            ],
            'page' => [
                'required' => false,
                'error_message' => 'Invalid Page number, format should be an integer',
                'validators' => [
                    ['name' => Digits::class],
                ],
            ],
            'where' => array_merge(['type' => InputFilter::class], $this->validFilterParameters),
            'selectCore' => array_merge(['type' => InputFilter::class], $this->validCoreFilterParameters),
            'selectUdf' => ['type' => InputFilter::class],
        ];
    }
}
