<?php

declare(strict_types=1);

namespace api\V2\Validators;

use api\V2\Hydrators\HydratorManagerInterface;
use api\V2\Services\EntityService;
use api\V2\Services\FieldDefsService;
use api\V2\Services\FieldMapService;
use api\V2\Services\RequestService;
use api\V2\Validators\System\FieldValidatorFactory;
use api\V2\Validators\System\Validators\Udf;
use api\validators\RequestValidator;
use Exception;
use Laminas\InputFilter\Input;
use Laminas\InputFilter\InputFilter;
use Laminas\Validator\Digits;
use Laminas\Validator\NotEmpty;
use Psr\Http\Message\ServerRequestInterface as Request;
use RuntimeException;
use src\framework\model\Mapper;
use Teapot\StatusCode\Http;

use function is_array;
use function array_key_exists;

class PatchRequestValidator extends RequestValidator
{
    protected array $unmappedLinkedRecordValidators = [];
    private array $nonUdfInputKeys = [];
    private HydratorManagerInterface $apiHydrator;
    private FieldValidatorFactory $fieldValidatorFactory;
    private EntityService $entityService;
    private RequestService $requestService;
    private FieldMapService $fieldMapService;
    private Mapper $mapper;
    private array $customMessages = [];
    private FieldDefsService $fieldDefsService;

    public function __construct(
        InputFilter $filter,
        HydratorManagerInterface $apiHydrator,
        FieldValidatorFactory $fieldValidatorFactory,
        EntityService $entityService,
        RequestService $requestService,
        FieldMapService $fieldMapService,
        Mapper $mapper,
        FieldDefsService $fieldDefsService
    ) {
        $this->apiHydrator = $apiHydrator;
        $this->fieldValidatorFactory = $fieldValidatorFactory;
        $this->entityService = $entityService;
        $this->requestService = $requestService;
        $this->fieldMapService = $fieldMapService;
        $this->mapper = $mapper;
        $this->fieldDefsService = $fieldDefsService;

        parent::__construct($filter);
    }

    public function __invoke(Request $request): bool
    {
        try {
            $body = $this->requestService->parseJsonBodyToArray($request);
        } catch (Exception $e) {
            throw new RuntimeException('Invalid json body sent in request.');
        }

        $recordId = $request->getAttribute('route')->getArgument('id');

        if (!$this->validateContextUrlId($recordId)) {
            return false;
        }

        $this->checkRecordAccess((int) $recordId);

        // Udf and Module params must be handled separately as Udf params are dynamic from user input
        // Module params are processed via maps like FeedbackMap.php
        if (!$this->validateUdfExpectedParamList($body) || !$this->validateModuleExpectedParamList($body)) {
            return false;
        }

        $linkedRecordsParamMaps = $this->fieldMapService->getFieldMaps($this->fieldMapService->getAllLinkedSectionKeys());
        $linkedRecordsParamMaps = array_merge_recursive($linkedRecordsParamMaps, $this->unmappedLinkedRecordValidators);

        $linkedRecordsInputData = $this->requestService->extractAllLinkedDataFromRequest($request) ?? [];

        if (!$this->validateLinkedRecordsExpectedParamList($linkedRecordsParamMaps, $linkedRecordsInputData)) {
            return false;
        }

        $entity = $this->apiHydrator->hydrateEntitiesFromRequest($request, (int) $recordId);

        $this->entityService->setEntity($entity);

        $this->fieldValidatorFactory->setInputFilter($this->filter);
        $this->fieldValidatorFactory->setEntityService($this->entityService);

        $this->fieldValidatorFactory->addExtraFields(
            array_keys($this->requestService->extractUdfDataFromRequest($request)),
        );

        $this->fieldValidatorFactory->addModuleInputs($this->fieldMapService->getFieldMap('module'));
        $this->fieldValidatorFactory->addLinkedRecordInputs(
            $linkedRecordsParamMaps,
            $linkedRecordsInputData,
            ['documents'],
        );

        return true;
    }

    public function validate(Request $request): bool
    {
        $this->filter->setData($this->requestService->parseJsonBodyToArray($request));

        return $this->filter->isValid();
    }

    /**
     * @return string[]
     */
    public function getMessages(): array
    {
        return array_merge($this->customMessages, $this->filter->getMessages());
    }

    private function validateContextUrlId(string $recordId): bool
    {
        $input = $this->getDigitsValidator('URL', 'id')->setRequired(true);

        $this->filter->add($input, 'id');
        $this->filter->setData(['id' => $recordId]);

        return $this->resetWhenValid(['id']);
    }

    private function checkRecordAccess(int $recordId): void
    {
        if (!$this->mapper->entityExists($recordId)) {
            throw new RuntimeException("The record with ID {$recordId} does not exist", Http::NOT_FOUND);
        }

        if (!$this->mapper->entityExistsAndIsAccessible($recordId)) {
            throw new RuntimeException("User does not have access to the record with ID {$recordId}", Http::FORBIDDEN);
        }
    }

    private function validateUdfExpectedParamList(array $body): bool
    {
        $udfInputKeys = [];

        foreach ($body as $name => $value) {
            if (stripos($name, 'UDF') === 0) {
                $udfInputKeys[$name] = $name;
                $input = new Input($name);
                $field = $this->fieldDefsService->getRegistryFieldDefinition($name);
                $input->getValidatorChain()->attach(new Udf($field));
                $this->filter->add($input);
            } else {
                $this->nonUdfInputKeys[$name] = $name;
            }
        }

        $this->filter->setData($udfInputKeys);

        return $this->resetWhenValid($udfInputKeys);
    }

    private function validateModuleExpectedParamList(array $body): bool
    {
        $this->filter->setData($body);

        $linkedSectionKeys = $this->fieldMapService->getAllLinkedSectionKeys();

        $allAllowedKeys = array_merge(array_flip($linkedSectionKeys), $this->fieldMapService->getFieldMap('module'));

        $this->addExceptedParamValidation($this->filter, $this->nonUdfInputKeys, $allAllowedKeys);

        return $this->resetWhenValid($this->nonUdfInputKeys);
    }

    private function validateLinkedRecordsExpectedParamList(
        array $linkedRecordsParamMaps,
        array $linkedRecordsInputData
    ): bool {
        foreach ($linkedRecordsParamMaps as $sectionName => $sectionMap) {
            $collectionInputFilter = new InputFilter();

            $sectionInputData = $linkedRecordsInputData[$sectionName];

            foreach ($sectionInputData as $elementIndex => $sectionInput) {
                $collectionInputFilter->add(
                    $this->prepareLinkedRecordInputFilter($sectionName, $sectionMap, $elementIndex, $sectionInput),
                    $elementIndex,
                );
            }

            $this->filter->add($collectionInputFilter, $sectionName);
        }

        $this->filter->setData($linkedRecordsInputData);

        return $this->resetWhenValid(array_keys($linkedRecordsParamMaps));
    }

    /**
     * @param mixed $elementIndex
     * @param mixed $sectionInput
     *
     * @return InputFilter|Input
     */
    private function prepareLinkedRecordInputFilter(string $sectionName, array $sectionMap, $elementIndex, $sectionInput)
    {
        $linkedRecordInputFilter = new InputFilter();

        if (is_array($sectionInput)) {
            $this->addExceptedParamValidation($linkedRecordInputFilter, $sectionInput, $sectionMap);
            $linkedRecordInputFilter->setData($sectionInput);

            if (array_key_exists('id', $sectionInput) && $linkedRecordInputFilter->isValid()) {
                // Key names are valid but before hydrating entity, need to make
                // sure linked records have valid id values as digits.
                $linkedRecordInputFilter->add($this->getDigitsValidator($sectionName, 'id'), 'id');
            }
        } else {
            $linkedRecordInputFilter->setData([$elementIndex => $sectionInput]);

            return $this->getDigitsValidator($sectionName, $elementIndex);
        }

        return $linkedRecordInputFilter;
    }

    /**
     * @param mixed $inputName
     */
    private function getDigitsValidator(string $sectionName, $inputName): Input
    {
        $input = new Input($inputName);
        $input->setRequired(false); // Default
        $input->setErrorMessage("id must be a digit for {$sectionName}[{$inputName}] and can not be a zero");
        $chain = $input->getValidatorChain();
        $chain->attach(new Digits());
        $chain->attach((new NotEmpty())->setType(NotEmpty::ZERO | NotEmpty::INTEGER));

        return $input;
    }
}
