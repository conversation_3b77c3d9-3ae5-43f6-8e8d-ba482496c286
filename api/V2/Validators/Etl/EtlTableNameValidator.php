<?php

declare(strict_types=1);

namespace api\V2\Validators\Etl;

use api\interfaces\validator\ValidatorInterface;
use Psr\Http\Message\ServerRequestInterface as Request;

use function is_array;
use function count;
use function is_string;

class EtlTableNameValidator implements ValidatorInterface
{
    private const BODY_EMPTY_VALIDATION_ERROR = 'Request body must be a valid JSON object containing the following keys: tables';
    private const TABLE_NAMES_EMPTY_VALIDATION_ERROR = 'The tables attribute must contain a non-empty array of tables to check.';
    private const TABLE_NAMES_MUST_BE_NON_EMPTY_STRINGS_VALIDATION_ERROR = 'Table names must be non empty strings';
    private const MAX_TABLE_COUNT = 1500;
    private const TOO_MANY_TABLES_VALIDATION_ERROR = 'Too many tables in one request. Please limit requests to no more than 1,500 tables.';
    private const EXPECTED_FORMAT = <<<'STR'
        Request should consist of a valid JSON object containing a list of tables to be validated of the form
        {
            "tables": [
                "table_to_be_validated_1",
                "table_to_be_validated_2",
                ...
                "table_to_be_validated_N"
            ]
        }
        STR;
    protected array $messages = [];

    public function validate(Request $request): bool
    {
        $body = $request->getParsedBody();

        if (empty($body)) {
            $this->messages[] = self::BODY_EMPTY_VALIDATION_ERROR;
        } elseif (!isset($body['tables']) || !is_array($body['tables'])) {
            $this->messages[] = self::TABLE_NAMES_EMPTY_VALIDATION_ERROR;
        } else {
            $this->validateTables($body['tables']);
        }

        if (!empty($this->messages)) {
            $this->messages[] = self::EXPECTED_FORMAT;
        }

        return empty($this->messages);
    }

    public function getMessages(): array
    {
        return $this->messages;
    }

    private function validateTables(array $tables): void
    {
        if (empty($tables)) {
            $this->messages[] = self::TABLE_NAMES_EMPTY_VALIDATION_ERROR;

            return;
        }

        if (count($tables) > self::MAX_TABLE_COUNT) {
            $this->messages[] = self::TOO_MANY_TABLES_VALIDATION_ERROR;
        }

        if (!array_filter($tables, static fn ($row) => is_string($row) && !empty($row))) {
            $this->messages[] = self::TABLE_NAMES_MUST_BE_NON_EMPTY_STRINGS_VALIDATION_ERROR;
        }
    }
}
