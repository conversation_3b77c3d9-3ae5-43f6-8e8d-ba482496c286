<?php

declare(strict_types=1);

namespace api\V2\Validators\Etl;

use api\interfaces\validator\ValidatorInterface;
use Psr\Http\Message\ServerRequestInterface;

use function is_string;
use function is_int;

class EtlTableSyncValidator implements ValidatorInterface
{
    private const BODY_VALIDATION_ERROR = 'Request body must be a valid JSON object containing the following keys: table, page, pageSize';
    private const TABLE_VALIDATION_ERROR = '`table` is a required parameter and must be a non-empty string';
    private const PAGESIZE_VALIDATION_ERROR = '`pageSize` is a required parameter and must be a positive integer';
    private const PAGE_VALIDATION_ERROR = '`page` is a required parameter and must be a positive integer';
    protected array $messages = [];

    public function validate(ServerRequestInterface $request): bool
    {
        $this->messages = [];

        $body = $request->getParsedBody();

        if (empty($body)) {
            $this->messages[] = self::BODY_VALIDATION_ERROR;

            return false;
        }

        if (empty($body['table']) || !is_string($body['table'])) {
            $this->messages[] = self::TABLE_VALIDATION_ERROR;
        }

        if (empty($body['pageSize']) || !is_int($body['pageSize']) || $body['pageSize'] < 1) {
            $this->messages[] = self::PAGESIZE_VALIDATION_ERROR;
        }

        if (empty($body['page']) || !is_int($body['page']) || $body['page'] < 1) {
            $this->messages[] = self::PAGE_VALIDATION_ERROR;
        }

        return empty($this->messages);
    }

    public function getMessages(): array
    {
        return $this->messages;
    }
}
