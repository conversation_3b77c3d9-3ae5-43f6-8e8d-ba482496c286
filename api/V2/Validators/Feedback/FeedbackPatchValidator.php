<?php

declare(strict_types=1);

namespace api\V2\Validators\Feedback;

use api\V2\Validators\PatchRequestValidator;
use api\V2\Validators\System\Validators\Base64;
use api\V2\Validators\System\Validators\MimeType;

class FeedbackPatchValidator extends PatchRequestValidator
{
    protected array $unmappedLinkedRecordValidators = [
        'documents' => [
            'contentType' => [MimeType::class],
            'file' => [Base64::class],
        ],
    ];
}
