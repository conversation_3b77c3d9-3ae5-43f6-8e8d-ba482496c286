<?php

declare(strict_types=1);

namespace api\V2\Validators\Feedback;

use api\V2\Validators\GetRequestValidator;
use Laminas\Validator\Date;
use Laminas\Validator\Digits;
use Laminas\Validator\StringLength;

class FeedbackGetValidator extends GetRequestValidator
{
    protected array $validFilterParameters = [
        'feedback.name' => [
            'required' => false,
            'error_message' => 'name should be a string of characters with a maximum length of 32',
            'validators' => [['name' => StringLength::class, 'options' => ['max' => 32]]],
        ],
        'feedback.location' => [
            'required' => false,
            'error_message' => 'location should be a number',
            'validators' => [
                ['name' => Digits::class],
            ],
        ],
        'feedback.firstReceived' => [
            'required' => false,
            'error_message' => 'Invalid date provided, format should be YYYY-MM-DD',
            'validators' => [
                [
                    'name' => Date::class,
                    'options' => [
                        'format' => 'Y-m-d',
                        'strict' => true,
                    ],
                ],
            ],
        ],
    ];
}
