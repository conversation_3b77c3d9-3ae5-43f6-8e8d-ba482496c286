<?php

declare(strict_types=1);

namespace api\V2\Validators\System;

use api\V2\Services\EntityService;
use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Inputs\FieldInputPreValidation;
use Laminas\InputFilter\Input;
use Laminas\InputFilter\InputFilter;
use Laminas\Validator\NotEmpty;
use src\system\database\FieldInterface;

use function count;
use function in_array;
use function is_string;

class FieldValidatorFactory
{
    private FieldDefsService $fieldDefsService;
    private InputFilter $inputFilter;
    private EntityService $entityService;

    public function __construct(FieldDefsService $fieldDefsService)
    {
        $this->fieldDefsService = $fieldDefsService;
    }

    public function setInputFilter(InputFilter $inputFilter): void
    {
        $this->inputFilter = $inputFilter;
    }

    public function setEntityService(EntityService $entityService): void
    {
        $this->entityService = $entityService;
    }

    public function addModuleInputs(array $requestParamMap): void
    {
        foreach ($requestParamMap as $requestParamName => $dbTableColumnName) {
            $this->buildFieldDefInput($this->inputFilter, $requestParamName, $dbTableColumnName, $requestParamMap);
        }
    }

    public function addExtraFields(array $udfRequestData): void
    {
        foreach ($udfRequestData as $requestParamName) {
            $this->buildFieldDefInput($this->inputFilter, $requestParamName, $requestParamName);
        }
    }

    public function addLinkedRecordInputs(
        array $requestParamMap,
        array $requestInputData,
        array $emptyNotAllowedSectionNames
    ): void {
        foreach ($requestParamMap as $sectionName => $sectionMap) {
            $collectionInputFilter = new InputFilter();

            $linkedRecordInputCount = count($requestInputData[$sectionName]);

            $emptyNotAllowed = in_array($sectionName, $emptyNotAllowedSectionNames, true);

            for ($i = 0; $i < $linkedRecordInputCount; ++$i) {
                $linkedRecordInputFilter = new InputFilter();
                foreach ($sectionMap as $requestParamName => $paramMapValue) {
                    if (is_string($paramMapValue)) {
                        $this->buildFieldDefInput(
                            $linkedRecordInputFilter,
                            $requestParamName,
                            $paramMapValue,
                            $sectionMap,
                            $emptyNotAllowed,
                        );

                        continue;
                    }
                    $this->buildNonFieldDefField($linkedRecordInputFilter, $requestParamName, $paramMapValue, $emptyNotAllowed);
                }
                $collectionInputFilter->add($linkedRecordInputFilter, $i);
            }

            $this->inputFilter->add($collectionInputFilter, $sectionName);
        }
    }

    public function buildFieldDefInput(
        InputFilter $inputFilter,
        string $requestParamName,
        ?string $fieldDefKey,
        array $requestParamMap = [],
        bool $emptyNotAllowed = false
    ): void {
        $fieldDef = $this->getFieldDef($fieldDefKey);

        if ($fieldDef === null || !method_exists($fieldDef, 'getFieldInput')) {
            // Can remove method_exists check once all Field and ExtraField objects implement FieldInputFactoryInterface
            return;
        }

        $fieldInput = $fieldDef->getFieldInput($this->fieldDefsService);
        $fieldInput->setInputName($requestParamName);

        /**
         * @var FieldInput|FieldInputPreValidation $fieldInput
         */
        $fieldInput instanceof FieldInputPreValidation && !empty($requestParamMap)
            ? $fieldInput->preparePreValidationInput($requestParamMap, $this->entityService, $emptyNotAllowed)
            : $fieldInput->prepareInput($emptyNotAllowed);

        $inputFilter->add($fieldInput->getInput(), $requestParamName);
    }

    private function getFieldDef(?string $fieldDefKey): ?FieldInterface
    {
        if ($fieldDefKey === null) {
            return null;
        }

        return $this->fieldDefsService->getRegistryFieldDefinition($fieldDefKey);
    }

    private function buildNonFieldDefField(
        InputFilter $inputFilter,
        string $requestParamName,
        array $validators,
        bool $emptyNotAllowed
    ): void {
        $input = new Input($requestParamName);
        $input->setRequired(false);
        $input->setContinueIfEmpty(true);

        $validationChain = $input->getValidatorChain();

        if ($emptyNotAllowed) {
            $validationChain->attach((new NotEmpty())->setType(NotEmpty::NULL | NotEmpty::STRING), true);
        }

        foreach ($validators as $validatorClassName) {
            $validationChain->attach(new $validatorClassName());
        }
        $inputFilter->add($input, $requestParamName);
    }
}
