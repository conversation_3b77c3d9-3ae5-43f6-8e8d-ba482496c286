<?php

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowEmptyStringTrait;
use api\V2\Validators\System\Fields\Traits\AllowNullTrait;
use Laminas\Validator\AbstractValidator;

use function get_class;
use function defined;
use function is_array;
use function is_int;
use function array_key_exists;

class ValueFromArray extends AbstractValidator
{
    use AllowNullTrait;
    use AllowEmptyStringTrait;
    public const MISSING_KEY = 'missingKeyValue';
    public const STRING_EMPTY = 'stringEmpty';
    public const NO_VALUES = 'noValues';
    public const LIST_NOT_GIVEN = 'noList';
    public const NOT_MULTI_DIMENSION_ARRAY = 'notMultipleCodeFormat';
    public const MAX_ARRAY_LEVELS = 'maxArrayLevels';
    protected string $key = 'id';
    protected array $messageTemplates = [
        self::MISSING_KEY => '%key% is missing',
        self::STRING_EMPTY => '%key% is an empty string',
        self::NO_VALUES => 'No values provided',
        self::LIST_NOT_GIVEN => 'No item list given',
        self::NOT_MULTI_DIMENSION_ARRAY => "format for multi codes is incorrect, please use square brackets eg. [{'value': 'CODE1', 'description': 'Code 1'},{'value:'CODE2','description':'Code 2'}]",
        self::MAX_ARRAY_LEVELS => 'to many array levels given',
    ];
    protected array $messageVariables = [
        'key' => 'key',
    ];
    private AbstractValidator $validator;
    private string $stringEmptyErrorKey = 'stringEmpty';
    private bool $arrayTypeStrict = false;
    private int $currentArrayLevel = 0;
    private int $maxArrayLevels = 2;

    public function __construct(AbstractValidator $validator, $options = null)
    {
        $this->validator = $validator;

        $this->prepareConstants();
        $this->prepareMessageTemplates();
        $this->prepareMessageVariables();

        parent::__construct($options);
    }

    public function __get($property)
    {
        return $this->validator->{$property};
    }

    public function setKey(string $key): void
    {
        $this->key = $key;
    }

    public function setArrayTypeStrict(bool $value): void
    {
        $this->arrayTypeStrict = $value;
    }

    public function setMaxArrayLevels(int $level): void
    {
        $this->maxArrayLevels = $level;
    }

    public function isValid($value): bool
    {
        if ($this->allowNull($value) || $this->allowEmptyString($value)) {
            return true;
        }

        if ($this->arrayTypeStrict && !is_array($value)) {
            $this->error(self::LIST_NOT_GIVEN);

            return false;
        }

        $result = $this->validateValue($value);
        $this->setErrors();

        return $result;
    }

    public function getValidatorName(): string
    {
        return get_class($this->validator);
    }

    private function prepareConstants(): void
    {
        $validatorName = get_class($this->validator);
        if (defined("{$validatorName}::STRING_EMPTY")) {
            $this->stringEmptyErrorKey = $this->validator::STRING_EMPTY;
        }
    }

    private function prepareMessageTemplates(): void
    {
        $this->messageTemplates = array_merge(
            $this->validator->getMessageTemplates(),
            $this->messageTemplates,
        );
    }

    private function prepareMessageVariables(): void
    {
        $vars = [];
        foreach ($this->validator->getMessageVariables() as $value) {
            $vars[$value] = $value;
        }

        $this->messageVariables = array_merge(
            $vars,
            $this->messageVariables,
        );
    }

    private function validateValue($value, int $recursiveCount = 0): bool
    {
        if ($recursiveCount > $this->maxArrayLevels) {
            $this->error(self::MAX_ARRAY_LEVELS);

            return false;
        }

        if (!$this->arrayTypeStrict && !is_array($value)) {
            $this->setValue($value);

            return $this->validator->isValid($value);
        }

        foreach ($value as $key => $subValue) {
            if (is_array($subValue)) {
                return $this->validateValue($subValue, ++$recursiveCount);
            }

            if (!is_int($key)) {
                if (!array_key_exists($this->key, $value)) {
                    $this->error(self::MISSING_KEY);

                    return false;
                }
                if ($key !== $this->key) {
                    continue;
                }
            }

            if ($subValue === '') {
                $this->error($this->stringEmptyErrorKey);

                return false;
            }

            $this->setValue($subValue);

            if (!$this->validator->isValid($subValue)) {
                return false;
            }
        }

        return true;
    }

    private function setErrors(): void
    {
        foreach ($this->validator->getMessages() as $key => $message) {
            $this->error($key);
        }
    }
}
