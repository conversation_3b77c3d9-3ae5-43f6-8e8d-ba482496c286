<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use Laminas\Stdlib\ErrorHandler;
use Laminas\Validator\Regex;

use function is_string;
use function is_int;
use function is_float;

/**
 * Use when you want to check for chars that do not match a list of specified chars in a regex
 * example '|[^a-z0-9]+|+' will error when any char is passed in that isn't a-z or 0-9 chars.
 */
class RegexNot extends Regex
{
    public function isValid($value): bool
    {
        if (!is_string($value) && !is_int($value) && !is_float($value)) {
            $this->error(static::INVALID);

            return false;
        }

        $this->setValue($value);

        ErrorHandler::start();
        $status = preg_match($this->pattern, $value);
        ErrorHandler::stop();
        if ($status === false) {
            $this->error(static::ERROROUS);

            return false;
        }

        if ($status) {
            $this->error(static::NOT_MATCH);

            return false;
        }

        return true;
    }
}
