<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowEmptyStringTrait;
use api\V2\Validators\System\Fields\Traits\AllowNullTrait;
use <PERSON><PERSON>\Validator\StringLength;

class StringLengthNull extends StringLength
{
    use AllowNullTrait;
    use AllowEmptyStringTrait;

    public function isValid($value)
    {
        if ($this->allowNull($value) || $this->allowEmptyString($value)) {
            return true;
        }

        return parent::isValid($value);
    }
}
