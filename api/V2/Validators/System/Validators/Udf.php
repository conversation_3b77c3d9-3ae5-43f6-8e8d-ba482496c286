<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use Laminas\Validator\AbstractValidator;
use src\system\database\FieldInterface;

/**
 * Custom validator class for only allowing alphanumeric chars spaces, underscores and dashes.
 */
class Udf extends AbstractValidator
{
    public const VALID_FORMAT = 'formatInvalid';
    public const VALID_FIELD = 'fieldInvalid';
    protected array $messageTemplates = [
        self::VALID_FORMAT => "'%value%' format is not correct, should be UDF_n when 'n' is a digit and UDF is caps.",
        self::VALID_FIELD => "'%value%' is not a valid UDF",
    ];
    private ?FieldInterface $field;

    public function __construct(?FieldInterface $field)
    {
        $this->field = $field;
        parent::__construct();
    }

    /**
     * @param $value
     */
    public function isValid($value): bool
    {
        $this->setValue($value);

        if (!preg_match('/^UDF_[\d]+\Z/', $value)) {
            $this->error(self::VALID_FORMAT);

            return false;
        }

        if ($this->field === null) {
            $this->error(self::VALID_FIELD);

            return false;
        }

        return true;
    }
}
