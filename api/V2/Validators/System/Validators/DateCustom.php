<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowNullTrait;
use DateTime;
use Exception;
use Laminas\Validator\Date;

use function is_string;

/**
 * Laminas Data validator is too strict when validating date format, we need to be able to accept a date in
 * YYYY-MM-DD HH:MM:SS and only validate the YYYY-MM-DD part and ignore the HH:MM:SS.
 */
class DateCustom extends Date
{
    use AllowNullTrait;
    protected $messageTemplates = [
        self::INVALID => '%value% - Invalid type given, string date time expected',
        self::INVALID_DATE => '%value% - The input does not appear to be a valid date',
        self::FALSEFORMAT => "%value% - The input does not fit the date format '%format%'",
    ];

    public function isValid($value): bool
    {
        $this->setValue($value);

        if ($this->allowNull($value)) {
            return true;
        }

        if (!is_string($value)) {
            $this->error(self::INVALID);

            return false;
        }

        if (preg_match('|[^0-9-:. ]+|', $value)) {
            $this->error(self::INVALID_DATE);

            return false;
        }

        try {
            $date = (new DateTime($value))->format($this->format);
        } catch (Exception $exception) {
            $this->error(self::FALSEFORMAT);

            return false;
        }

        return parent::isValid($date);
    }
}
