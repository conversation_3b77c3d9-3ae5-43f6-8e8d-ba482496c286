<?php

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowEmptyStringTrait;
use api\V2\Validators\System\Fields\Traits\AllowNullTrait;
use Laminas\Validator\AbstractValidator;
use src\system\database\FieldInterface;

use function is_array;
use function array_key_exists;
use function is_string;
use function strlen;

class Code extends AbstractValidator
{
    use AllowEmptyStringTrait;
    use AllowNullTrait;
    protected const NOT_ARRAY_EMPTY_STRING_OR_NULL = 'notArrayOrNull';
    protected const INVALID_APPROVAL_STATUS = 'invalidApprovalStatus';
    private const VALUE_MISSING = 'missingValue';
    private const VALUE_EMPTY = 'emptyValue';
    private const NOT_STRING_OR_NULL = 'notStringOrNull';
    private const TOO_LONG = 'stringLengthTooLong';
    private const NOT_ALPHA_NUMERIC = 'notAlphaNumeric';
    protected $messageTemplates = [
        self::VALUE_MISSING => 'value property is missing',
        self::VALUE_EMPTY => 'value is empty or null',
        self::NOT_STRING_OR_NULL => 'value is not a string or null',
        self::TOO_LONG => 'value is more than ' . FieldInterface::DEFAULT_CODE_LENGTH . ' characters long',
        self::NOT_ALPHA_NUMERIC => 'value is not alphanumeric',
        self::NOT_ARRAY_EMPTY_STRING_OR_NULL => 'value is not an array, empty string, or null',
        self::INVALID_APPROVAL_STATUS => "'%value%' is not a valid Approval Status code.",
    ];

    public function isValid($value): bool
    {
        if ($this->allowNull($value) || $this->allowEmptyString($value)) {
            return true;
        }

        if (is_array($value)) {
            if (!array_key_exists('value', $value)) {
                $this->error(self::VALUE_MISSING);

                return false;
            }

            $value = $value['value'];

            // You cannot remove a code value using the object form, only the flattened one
            if ($value === '' || $value === null) {
                $this->error(self::VALUE_EMPTY);

                return false;
            }
        }

        $this->setValue($value);

        if (!is_string($value) && $value !== null) {
            $this->error(self::NOT_STRING_OR_NULL);

            return false;
        }

        if (strlen($value) > FieldInterface::DEFAULT_CODE_LENGTH) {
            $this->error(self::TOO_LONG);

            return false;
        }

        if (!preg_match('/^[a-zA-Z0-9]*$/', $value)) {
            $this->error(self::NOT_ALPHA_NUMERIC);

            return false;
        }

        return true;
    }
}
