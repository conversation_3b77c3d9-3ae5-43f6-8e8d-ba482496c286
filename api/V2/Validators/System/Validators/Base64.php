<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use Laminas\Stdlib\ErrorHandler;

class Base64 extends RegexNotSetPattern
{
    public const NOT_MATCH = 'base64';
    private const BASE64_DECODE_ERROR = 'base64Decode';
    private const BASE64_MALFORMED = 'base64Malformed';
    protected $pattern = '|[^a-z\d/=+]+|i';
    protected $messageTemplates = [
        self::NOT_MATCH => 'invalid base64 encoded string',
        self::BASE64_DECODE_ERROR => 'unable to decode base64 string',
        self::BASE64_MALFORMED => 'unable to decode base64 string',
    ];

    public function isValid($value): bool
    {
        if (!parent::isValid($value)) {
            return false;
        }

        ErrorHandler::start();
        $result = base64_decode($value, true);
        ErrorHandler::stop();

        if ($result === false) {
            $this->error(self::BASE64_DECODE_ERROR);

            return false;
        }

        ErrorHandler::start();
        $result = base64_encode($result) === $value;
        ErrorHandler::stop();

        if (!$result) {
            $this->error(self::BASE64_MALFORMED);

            return false;
        }

        return true;
    }
}
