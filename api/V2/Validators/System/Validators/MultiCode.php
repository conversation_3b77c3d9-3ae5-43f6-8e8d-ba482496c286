<?php

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowEmptyStringTrait;
use api\V2\Validators\System\Fields\Traits\AllowNullTrait;

use function is_array;
use function is_int;
use function is_string;

class MultiCode extends Code
{
    use AllowNullTrait;
    use AllowEmptyStringTrait;
    public const NOT_MULTI_DIMENSION_ARRAY = 'notMultipleCodeFormat';
    private array $customMessages = [
        self::NOT_MULTI_DIMENSION_ARRAY => "format for multi codes is incorrect, please use square brackets eg. [{'value': 'CODE1', 'description': 'Code 1'},{'value:'CODE2','description':'Code 2'}]",
    ];

    public function __construct($options = null)
    {
        $this->messageTemplates = array_merge($this->messageTemplates, $this->customMessages);

        parent::__construct($options);
    }

    public function isValid($value): bool
    {
        if ($this->allowNull($value) || $this->allowEmptyString($value)) {
            return true;
        }

        if (!is_array($value)) {
            $this->error(self::NOT_ARRAY_EMPTY_STRING_OR_NULL);

            return false;
        }

        if (empty($value)) {
            return true;
        }

        if (!$this->allFlattenedStringValues($value) && !$this->isMultidimensionalArray($value)) {
            $this->error(self::NOT_MULTI_DIMENSION_ARRAY);

            return false;
        }

        foreach ($value as $code) {
            $result = parent::isValid($code);
            if (!$result) {
                return false;
            }
        }

        return true;
    }

    private function isMultidimensionalArray($value): bool
    {
        rsort($value);

        return isset($value[0]) && is_array($value[0]);
    }

    private function allFlattenedStringValues($value): bool
    {
        foreach ($value as $int => $val) {
            if (!is_int($int) || !is_string($val)) {
                return false;
            }
        }

        return true;
    }
}
