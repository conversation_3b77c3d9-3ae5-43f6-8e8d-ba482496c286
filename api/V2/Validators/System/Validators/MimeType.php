<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use Laminas\Validator\StringLength;
use Symfony\Component\Mime\MimeTypes;

class MimeType extends StringLength
{
    public const MIME_TYPE = 'mimeType';
    protected $messageTemplates = [
        self::MIME_TYPE => '%value% unrecognised content type for document upload',
    ];
    private MimeTypes $mimeTypes;

    public function __construct($options = [])
    {
        $this->mimeTypes = new MimeTypes();
        parent::__construct($options);
    }

    public function isValid($value): bool
    {
        if (!parent::isValid($value)) {
            return false;
        }

        if (!$this->mimeTypes->getExtensions($value)) {
            $this->error(self::MIME_TYPE);

            return false;
        }

        return true;
    }
}
