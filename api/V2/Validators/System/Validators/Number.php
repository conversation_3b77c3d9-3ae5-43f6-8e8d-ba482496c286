<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use api\V2\Validators\System\Fields\Traits\AllowNullTrait;
use Laminas\Validator\AbstractValidator;

class Number extends AbstractValidator
{
    use AllowNullTrait;
    public const NOT_NUMERIC = 'notNumeric';
    protected array $messageTemplates = [
        self::NOT_NUMERIC => '%value% - must be a valid number, example 123456.99',
    ];

    public function isValid($value): bool
    {
        $this->setValue($value);

        if ($this->allowNull($value)) {
            return true;
        }

        if (!is_numeric($value)) {
            $this->error(self::NOT_NUMERIC);

            return false;
        }

        return true;
    }
}
