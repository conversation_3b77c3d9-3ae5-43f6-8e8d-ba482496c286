<?php

namespace api\V2\Validators\System\Validators;

use src\system\database\field\ApprovalStatusField;

use function array_key_exists;

class ApprovalStatus extends Code
{
    private ApprovalStatusField $field;

    public function __construct(ApprovalStatusField $field)
    {
        $this->field = $field;
        parent::__construct();
    }

    public function isValid($value): bool
    {
        $isValid = parent::isValid($value);

        if ($isValid) {
            // We need to also assert that the approval status code is a valid one, because "bad things"
            // happen when any old garbage is used for approval statuses (since they're tied up with workflows
            // and access control).
            $validCodes = $this->field->getCodes()->toAssocArray();
            if (!array_key_exists($this->value, $validCodes)) {
                $this->error(self::INVALID_APPROVAL_STATUS);
                $isValid = false;
            }
        }

        return $isValid;
    }
}
