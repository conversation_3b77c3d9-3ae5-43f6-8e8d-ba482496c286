<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use DateTime;
use Laminas\Validator\GreaterThan;

/**
 * Custom validator class for only allowing dates less than setMin() date.
 */
class DateGreaterThan extends GreaterThan
{
    protected $messageTemplates = [
        self::NOT_GREATER => "The date %value% is not greater than '%min%'",
        self::NOT_GREATER_INCLUSIVE => "The date %value% is not greater than or equal to '%min%'",
    ];
    private DateTime $dateTimeMin;

    /**
     * @param string $min
     */
    public function setMin($min): self
    {
        parent::setMin($min);
        $this->dateTimeMin = new DateTime($min);

        return $this;
    }

    /**
     * @param string $value
     */
    public function isValid($value): bool
    {
        $this->setValue($value);

        $date = new DateTime($value);

        if ($this->inclusive) {
            if ($this->dateTimeMin > $date) {
                $this->error(self::NOT_GREATER_INCLUSIVE);

                return false;
            }
        } elseif ($this->dateTimeMin >= $date) {
            $this->error(self::NOT_GREATER);

            return false;
        }

        return true;
    }
}
