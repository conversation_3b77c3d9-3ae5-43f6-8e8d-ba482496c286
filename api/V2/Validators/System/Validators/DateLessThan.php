<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Validators;

use DateTime;
use Laminas\Validator\LessThan;

/**
 * Custom validator class for only allowing dates less than setMax() date.
 */
class DateLessThan extends LessThan
{
    protected $messageTemplates = [
        self::NOT_LESS => "The date %value% is not less than '%max%'",
        self::NOT_LESS_INCLUSIVE => "The date %value% is not less or equal than '%max%'",
    ];
    private DateTime $dateTimeMax;

    /**
     * @param string $max
     */
    public function setMax($max): self
    {
        parent::setMax($max);
        $this->dateTimeMax = new DateTime($max);

        return $this;
    }

    /**
     * @param string $value
     */
    public function isValid($value): bool
    {
        $this->setValue($value);

        $date = new DateTime($value);

        if ($this->inclusive) {
            if ($date > $this->dateTimeMax) {
                $this->error(self::NOT_LESS_INCLUSIVE);

                return false;
            }
        } elseif ($date >= $this->dateTimeMax) {
            $this->error(self::NOT_LESS);

            return false;
        }

        return true;
    }
}
