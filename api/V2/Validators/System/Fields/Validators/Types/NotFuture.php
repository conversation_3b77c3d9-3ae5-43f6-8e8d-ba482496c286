<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators\Types;

use api\V2\Validators\System\Fields\Validators\FieldValidator;
use api\V2\Validators\System\Validators\DateLessThan;
use Lam<PERSON>\Validator\AbstractValidator;
use <PERSON><PERSON>\Validator\LessThan;

class NotFuture extends FieldValidator
{
    private const ERROR_MESSAGE = "date can't be in the future";

    public function getValidator(): AbstractValidator
    {
        if (!$this->validator) {
            $this->validator = new DateLessThan([
                'max' => date('Y-m-d'),
                'inclusive' => true,
                'messages' => [
                    LessThan::NOT_LESS => self::ERROR_MESSAGE,
                    LessThan::NOT_LESS_INCLUSIVE => self::ERROR_MESSAGE,
                ],
            ]);
        }

        return $this->validator;
    }
}
