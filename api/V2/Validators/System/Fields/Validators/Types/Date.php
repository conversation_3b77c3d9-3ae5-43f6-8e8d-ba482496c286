<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators\Types;

use api\V2\Validators\System\Fields\Validators\FieldValidator;
use api\V2\Validators\System\Validators\DateCustom;
use Lam<PERSON>\Validator\AbstractValidator;
use Lam<PERSON>\Validator\Date as LaminasDate;

class Date extends FieldValidator
{
    private const ERROR_MESSAGE = 'Invalid date provided format should be YYYY-MM-DD';

    public function getValidator(): AbstractValidator
    {
        if (!$this->validator) {
            $this->validator = new DateCustom([
                'format' => 'Y-m-d',
                'strict' => false,
                'messages' => [
                    LaminasDate::INVALID => self::ERROR_MESSAGE,
                    LaminasDate::INVALID_DATE => self::ERROR_MESSAGE,
                    LaminasDate::FALSEFORMAT => self::ERROR_MESSAGE,
                ],
            ]);
        }

        return $this->validator;
    }
}
