<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators\Types;

use api\V2\Validators\System\Fields\Validators\ReferencedFieldValidator;
use api\V2\Validators\System\Validators\DateGreaterThan;
use <PERSON><PERSON>\Validator\AbstractValidator;
use Laminas\Validator\GreaterThan;

class NotEarlierThan extends ReferencedFieldValidator
{
    public function getValidator(): AbstractValidator
    {
        if (!$this->validator) {
            $errorMessage = "date must be greater than or equal to {$this->getReferencedFieldInput()->getInputName()} date";

            $this->validator = new DateGreaterThan([
                'min' => $this->getReferencedFieldInput()->getInputValue(),
                'inclusive' => true,
                'messages' => [
                    GreaterThan::NOT_GREATER => $errorMessage,
                    GreaterThan::NOT_GREATER_INCLUSIVE => $errorMessage,
                ],
            ]);
        }

        return $this->validator;
    }
}
