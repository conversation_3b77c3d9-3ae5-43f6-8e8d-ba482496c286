<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators;

use Laminas\Validator\AbstractValidator;
use Laminas\Validator\ValidatorInterface;

class SimpleFieldValidator extends FieldValidator
{
    public function __construct(ValidatorInterface $validator)
    {
        $this->validator = $validator;
    }

    public function getValidator(): AbstractValidator
    {
        return $this->validator;
    }
}
