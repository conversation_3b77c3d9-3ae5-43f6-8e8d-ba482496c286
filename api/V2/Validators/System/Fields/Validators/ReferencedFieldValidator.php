<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators;

use api\V2\Services\EntityService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldValidatorPreValidationInterface;

abstract class ReferencedFieldValidator extends FieldValidator implements FieldValidatorPreValidationInterface
{
    private FieldInput $referencedFieldInput;

    public function __construct(FieldInput $referencedFieldInput)
    {
        $this->referencedFieldInput = $referencedFieldInput;
    }

    public function getReferencedFieldInput(): FieldInput
    {
        return $this->referencedFieldInput;
    }

    public function passesPreValidationChecks(array $requestParamMap, EntityService $entityService): bool
    {
        $refFieldInput = $this->getReferencedFieldInput();

        $refRequestParamName = $this->getReferencedFieldInputNameFromMap($requestParamMap);

        $refFieldInput->setInputName($refRequestParamName);

        $refValue = $entityService->getFromUnderscoreNaming($refFieldInput->getColumnName());

        if ($refValue === null) {
            return false;
        }

        $refFieldInput->setInputValue($refValue);

        if (!$refFieldInput->isValid()) {
            return false;
        }

        return true;
    }

    private function getReferencedFieldInputNameFromMap(array $requestParamMap): string
    {
        return array_search(
            $this->getReferencedFieldInput()->getTableNameColumnName(),
            $requestParamMap,
            true,
        );
    }
}
