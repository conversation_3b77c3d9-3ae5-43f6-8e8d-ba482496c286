<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Validators;

use api\V2\Validators\System\Fields\Interfaces\FieldValidatorInterface;
use Laminas\Validator\ValidatorInterface;
use RuntimeException;

use function get_class;
use function is_array;
use function array_key_exists;

abstract class FieldValidator implements FieldValidatorInterface
{
    private const FIELD_DEFS_PARENT_KEY = 'validators';
    protected ?ValidatorInterface $validator = null;
    private bool $breakChainOnFailure = false;

    public function breakChainOnFailure(): bool
    {
        return $this->breakChainOnFailure;
    }

    public function setBreakChainOnFailure(bool $value = true): void
    {
        $this->breakChainOnFailure = $value;
    }

    public function configureFromFieldDefsExtra(array $fieldDefsExtra): void
    {
        if (!array_key_exists(self::FIELD_DEFS_PARENT_KEY, $fieldDefsExtra)) {
            return; // No custom config set in field defs for validation just return.
        }

        if (!is_array($fieldDefsExtra[self::FIELD_DEFS_PARENT_KEY])) {
            throw new RuntimeException("Field defs extra key name 'validators' must be an array.");
        }

        $class = get_class($this->getValidator());

        foreach ($fieldDefsExtra[self::FIELD_DEFS_PARENT_KEY] as $validatorName => $options) {
            if ($validatorName === $class) {
                if (!is_array($options)) {
                    throw new RuntimeException("Field defs extra config for validators[{$class}] must be an array.");
                }
                $this->overrideDefaultValidatorConfig($options);
            }
        }
    }

    private function overrideDefaultValidatorConfig(array $options): void
    {
        if (array_key_exists('breakChainOnFailure', $options)) {
            // Allow override of default break chain on failure setting for a field type
            // per FieldDefExtra field definition.
            $this->setBreakChainOnFailure($options['breakChainOnFailure']);
            unset($options['breakChainOnFailure']);
        }

        $this->getValidator()->setOptions($options);
    }
}
