<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\ValueFromArray;
use Laminas\Validator\Digits;
use Laminas\Validator\GreaterThan;
use Laminas\Validator\LessThan;
use Laminas\Validator\NotEmpty;
use src\system\database\FieldInterface;

trait DigitsFromArrayIdFactoryTrait
{
    protected $arrayTypeStrict = false;

    public function getFieldInput(FieldDefsService $fieldDefsService): FieldInputInterface
    {
        $fieldInput = new FieldInput($this, $fieldDefsService->getFieldDefinition($this));

        $notEmpty = new SimpleFieldValidator((new NotEmpty())->setType(NotEmpty::EMPTY_ARRAY));
        $notEmpty->setBreakChainOnFailure(true);
        $fieldInput->addValidator($notEmpty);

        $digits = new ValueFromArray(new Digits(), [
            'messages' => [
                Digits::NOT_DIGITS => '%value% id is not a number',
                Digits::STRING_EMPTY => '%key% is an empty string',
            ],
            'arrayTypeStrict' => $this->arrayTypeStrict,
        ]);

        $validatorDigits = new SimpleFieldValidator($digits);
        $validatorDigits->setBreakChainOnFailure(true);
        $fieldInput->addValidator($validatorDigits);

        $lessThan = new LessThan(['max' => FieldInterface::MAX_INT]);
        $lessThan->setInclusive(true);
        $fieldInput->addValidator(new SimpleFieldValidator(
            new ValueFromArray($lessThan, [
                'arrayTypeStrict' => $this->arrayTypeStrict,
            ]),
        ));

        $greaterThan = new GreaterThan(['min' => 1]);
        $greaterThan->setInclusive(true);
        $fieldInput->addValidator(new SimpleFieldValidator(
            new ValueFromArray($greaterThan, [
                'messages' => [
                    GreaterThan::NOT_GREATER_INCLUSIVE => "The input value (%value%) is not greater than or equal to '%min%'",
                ],
                'arrayTypeStrict' => $this->arrayTypeStrict,
            ]),
        ));

        return $fieldInput;
    }
}
