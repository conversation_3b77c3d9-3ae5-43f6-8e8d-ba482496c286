<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\StringLengthNull;
use src\system\database\FieldInterface;

trait TextFactoryTrait
{
    public function getFieldInput(FieldDefsService $fieldDefsService): FieldInputInterface
    {
        $fieldInput = new FieldInput($this, $fieldDefsService->getFieldDefinition($this));
        $fieldInput->setContinueIfEmpty(true);

        $maxLength = $fieldInput->getMaxLength() ?? FieldInterface::MAX_LENGTH_NVCAHR;

        $fieldInput->addValidator(new SimpleFieldValidator(new StringLengthNull(['max' => $maxLength])));

        return $fieldInput;
    }
}
