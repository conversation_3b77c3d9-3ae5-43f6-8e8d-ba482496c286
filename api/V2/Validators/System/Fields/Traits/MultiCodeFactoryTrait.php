<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\MultiCode;

trait MultiCodeFactoryTrait
{
    public function getFieldInput(FieldDefsService $fieldDefsService): FieldInputInterface
    {
        $fieldInput = new FieldInput($this, $fieldDefsService->getFieldDefinition($this));
        $fieldInput->addValidator(new SimpleFieldValidator(
            new MultiCode(),
        ));

        return $fieldInput;
    }
}
