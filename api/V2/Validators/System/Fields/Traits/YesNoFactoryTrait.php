<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\ValueFromArray;
use Laminas\Validator\InArray;

trait YesNoFactoryTrait
{
    public function getFieldInput(FieldDefsService $fieldDefsService): FieldInputInterface
    {
        $fieldInput = new FieldInput($this, $fieldDefsService->getFieldDefinition($this));
        $inArray = new InArray([
            'haystack' => ['Y', 'N', 'y', 'n'],
            'message' => "'Y' and 'N' values accepted only",
        ]);
        $fieldInput->addValidator(new SimpleFieldValidator(
            new ValueFromArray($inArray, [
                'key' => 'value',
                'messages' => [ValueFromArray::MISSING_KEY => "'Y' and 'N' values accepted only"],
            ]),
        ));

        return $fieldInput;
    }
}
