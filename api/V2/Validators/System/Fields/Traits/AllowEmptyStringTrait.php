<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

trait AllowEmptyStringTrait
{
    protected bool $allowEmptyString = true;

    public function setAllowEmptyString(bool $allowEmptyString): void
    {
        $this->allowEmptyString = $allowEmptyString;
    }

    public function allowEmptyString($value): bool
    {
        if ($this->allowEmptyString && $value === '') {
            return true;
        }

        return false;
    }
}
