<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

use api\V2\Services\FieldDefsService;
use api\V2\Validators\System\Fields\Inputs\FieldInput;
use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\Number;
use Laminas\Validator\LessThan;
use Laminas\Validator\NotEmpty;
use src\system\database\FieldInterface;

trait MoneyFactoryTrait
{
    public function getFieldInput(FieldDefsService $fieldDefsService): FieldInputInterface
    {
        $maxNumber = str_repeat('9', FieldInterface::MAX_MONEY_DIGITS) . '.';
        $maxNumber .= str_repeat('9', FieldInterface::MAX_MONEY_DECIMALS);

        $fieldInput = new FieldInput($this, $fieldDefsService->getFieldDefinition($this));

        $notEmpty = new SimpleFieldValidator((new NotEmpty())->setType(NotEmpty::STRING));
        $notEmpty->setBreakChainOnFailure(true);
        $fieldInput->addValidator($notEmpty);

        $numberValidator = new SimpleFieldValidator(new Number());
        $numberValidator->setBreakChainOnFailure(true);
        $fieldInput->addValidator($numberValidator);

        $lessThan = new LessThan(['max' => $maxNumber]);
        $lessThan->setInclusive(true);
        $fieldInput->addValidator(new SimpleFieldValidator($lessThan));

        return $fieldInput;
    }
}
