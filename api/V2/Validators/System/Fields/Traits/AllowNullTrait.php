<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Traits;

trait AllowNullTrait
{
    protected bool $allowNull = true;

    public function setAllowNull(bool $allowNull): void
    {
        $this->allowNull = $allowNull;
    }

    public function allowNull($value): bool
    {
        if ($this->allowNull && $value === null) {
            return true;
        }

        return false;
    }
}
