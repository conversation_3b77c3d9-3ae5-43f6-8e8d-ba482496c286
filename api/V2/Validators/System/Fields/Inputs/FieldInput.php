<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Inputs;

use api\V2\Validators\System\Fields\Interfaces\FieldInputInterface;
use api\V2\Validators\System\Fields\Interfaces\FieldValidatorInterface;
use api\V2\Validators\System\Fields\Validators\SimpleFieldValidator;
use api\V2\Validators\System\Validators\ValueFromArray;
use Laminas\InputFilter\Input;
use Laminas\Validator\NotEmpty;
use src\system\database\field\StringField;
use src\system\database\FieldInterface;

use function get_class;

/**
 * Wrapper class for Laminas\Input to allow it to be configurable from fieldDefs and fieldDefsExtra.
 *
 * It also handles factory methods provided on Field objects `src/system/database/field/Field.php:getFieldInput()`
 * which specify validation for that field type, although granular control is possible be overriding default field
 * config using fieldDefsExtra.
 */
class FieldInput implements FieldInputInterface
{
    /** @var array|FieldValidatorInterface[] */
    protected array $fieldValidators = [];

    /** @var mixed|null */
    private $inputValue = null;
    private string $inputName = '';
    private ?Input $input = null;
    private FieldInterface $field;
    private array $fieldDefsExtra;
    private bool $required = false;
    private bool $continueIfEmpty = true;
    private bool $allowEmpty = true;

    public function __construct(FieldInterface $field, array $fieldDefsExtra)
    {
        $this->field = $field;
        $this->fieldDefsExtra = $fieldDefsExtra;
    }

    /**
     * @param mixed $inputValue
     */
    public function setInputValue($inputValue): void
    {
        $this->inputValue = $inputValue;
    }

    /**
     * @return mixed
     */
    public function getInputValue()
    {
        return $this->inputValue;
    }

    public function setInputName(string $inputName): void
    {
        $this->inputName = $inputName;
    }

    public function getInputName(): string
    {
        return $this->inputName;
    }

    public function getColumnName(): string
    {
        return $this->field->getName();
    }

    public function getTableNameColumnName(): string
    {
        return "{$this->field->getTable()}.{$this->getColumnName()}";
    }

    public function addValidator(FieldValidatorInterface $validator): void
    {
        if ($validator instanceof SimpleFieldValidator) {
            $attachedValidator = $validator->getValidator();

            $validatorName = $attachedValidator instanceof ValueFromArray
                ? $attachedValidator->getValidatorName()
                : get_class($validator->getValidator());

            $this->fieldValidators[get_class($validator) . $validatorName] = $validator;
        } else {
            $this->fieldValidators[get_class($validator)] = $validator;
        }
    }

    public function setRequired(): void
    {
        $this->required = true;
    }

    public function setContinueIfEmpty(bool $state): void
    {
        $this->continueIfEmpty = $state;
    }

    public function isValid(): bool
    {
        foreach ($this->fieldValidators as $fieldValidator) {
            if (!$fieldValidator->getValidator()->isValid($this->getInputValue())) {
                return false; // Fail on first invalid check.
            }
        }

        return true;
    }

    public function prepareInput(bool $emptyNotAllowed): Input
    {
        if ($this->input instanceof Input) {
            return $this->input;
        }

        $required = $this->fieldDefsExtra['Required'] ?? $this->required;

        $this->input = new Input($this->getInputName());
        $this->input->setRequired($required);

        // Ignore the message about setContinueIfEmpty() method being deprecated and that a NotEmpty validator should
        // be used instead, it's not always possible to do that, because when you have a non required field and an
        // empty value is sent in the request, no validators will be run so the method setContinueIfEmpty has to be
        // used in that case, the library comment doesn't account for that!
        if (!$required || $emptyNotAllowed) {
            // Continue If Empty = true allows validators to run when an empty value is sent in a request for a
            // non required field, which then makes it possible to add NotEmpty validators.
            $this->input->setContinueIfEmpty($this->fieldDefsExtra['ContinueIfEmpty'] ?? $this->continueIfEmpty);
        } else {
            $this->input->setAllowEmpty($this->fieldDefsExtra['AllowEmpty'] ?? $this->allowEmpty);
        }

        if ($emptyNotAllowed) {
            $this->input->getValidatorChain()->attach((new NotEmpty())->setType(NotEmpty::NULL | NotEmpty::STRING), true);
        }

        foreach ($this->fieldValidators as $validator) {
            $validator->configureFromFieldDefsExtra($this->fieldDefsExtra);
            $this->input->getValidatorChain()->attach(
                $validator->getValidator(),
                $validator->breakChainOnFailure(),
            );
        }

        return $this->input;
    }

    public function getInput(): Input
    {
        return $this->input;
    }

    public function getMaxLength(): ?int
    {
        $fdrStringLength = $this->field instanceof StringField ? $this->field->getLength() : null;

        return $this->fieldDefsExtra['MaxLength'] ?? $fdrStringLength ?? null;
    }
}
