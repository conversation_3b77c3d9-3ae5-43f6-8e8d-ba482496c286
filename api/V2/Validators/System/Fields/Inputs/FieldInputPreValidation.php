<?php

declare(strict_types=1);

namespace api\V2\Validators\System\Fields\Inputs;

use api\V2\Services\EntityService;
use api\V2\Validators\System\Fields\Interfaces\FieldValidatorPreValidationInterface;
use <PERSON><PERSON>\InputFilter\Input;

class FieldInputPreValidation extends FieldInput
{
    public function preparePreValidationInput(
        array $requestParamMap,
        EntityService $entityService,
        bool $emptyNotAllowed
    ): Input {
        $this->runPreValidationTasks($requestParamMap, $entityService);

        return $this->prepareInput($emptyNotAllowed);
    }

    private function runPreValidationTasks(array $requestParamMap, EntityService $entityService): void
    {
        foreach ($this->fieldValidators as $key => $fieldValidator) {
            if (
                $fieldValidator instanceof FieldValidatorPreValidationInterface
                && !$fieldValidator->passesPreValidationChecks($requestParamMap, $entityService)
            ) {
                unset($this->fieldValidators[$key]);
            }
        }
    }
}
