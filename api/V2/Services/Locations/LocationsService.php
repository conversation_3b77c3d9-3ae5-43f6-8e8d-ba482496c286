<?php

namespace api\V2\Services\Locations;

use api\V2\Services\AbstractAreaService;
use app\models\location\entities\LocationEntity;
use app\models\location\hydrators\LocationHydrator;
use app\models\location\services\TreeFieldService;
use Doctrine\ORM\EntityManagerInterface;

class LocationsService extends AbstractAreaService
{
    private $locationHydrator;

    public function __construct(
        EntityManagerInterface $entityManager,
        TreeFieldService $treeFieldService,
        LocationHydrator $locationHydrator
    ) {
        parent::__construct($entityManager, $treeFieldService);
        $this->locationHydrator = $locationHydrator;
    }

    protected function getEntityRepository()
    {
        return $this->entityManager->getRepository(LocationEntity::class);
    }

    protected function getEntityTableName(): string
    {
        return $this->entityManager->getClassMetadata(LocationEntity::class)->getTableName();
    }

    protected function getCodeTable(): string
    {
        return 'code_location';
    }

    protected function getEntityType(): string
    {
        return 'location';
    }

    protected function hydrate($treeItem)
    {
        return $this->locationHydrator->hydrate($treeItem);
    }

    protected function getPrinceOnlyFields(): array
    {
        return $this->locationHydrator->getPrinceOnlyFields();
    }

    /**
     * @return string[] (Table Name => Identity Column)
     */
    protected function getTablesWithIds(): array
    {
        return [
            'location_tag_links' => 'location_id',
            'services_locations' => 'locationentity_id',
            'locations_id_numbers' => 'location_id',
        ];
    }

    /**
     * @return string[]
     */
    protected function getStandardTables(): array
    {
        return [
            $this->getEntityTableName(),
            $this->getCodeTable(),
            'location_descr',
        ];
    }
}
