<?php

declare(strict_types=1);

namespace api\V2\Services;

use api\V2\Formatters\FieldFormatterFactory;
use Exception;
use src\system\database\FieldInterface;
use src\system\database\FieldTypeMapper;
use Throwable;

use function is_array;

class DataFormatterService
{
    private const UDF_PREFIX = 'UDF_';
    private const UDF_SECTION = 'udfs';
    private FieldDefsService $fieldDefsService;
    private FieldFormatterFactory $fieldFormatterFactory;
    private FieldMapService $fieldMapService;
    private int $loopProtection = 0;

    public function __construct(
        FieldDefsService $fieldDefsService,
        FieldFormatterFactory $fieldFormatterFactory,
        FieldMapService $fieldMapService
    ) {
        $this->fieldDefsService = $fieldDefsService;
        $this->fieldFormatterFactory = $fieldFormatterFactory;
        $this->fieldMapService = $fieldMapService;
    }

    /**
     * Calls the extract and format method, which loops within itself over array data.
     *
     * @param array $data The data to format
     *
     * @throws Throwable
     */
    public function formatData(array $data): array
    {
        return $this->extractAndFormatDataInArray($data);
    }

    /**
     * Format a specific fields data for use in PATCH so it can be used in a hydrator or service to update the database.
     * mostly aimed at normalising data that could be sent in the request as either an integer or an array, eg. staff fields.
     *
     * @param mixed $dataToFormat
     *
     * @return mixed
     */
    public function formatForPatch(FieldInterface $field, $dataToFormat)
    {
        $fieldFormatter = $this->fieldFormatterFactory->getFormatter($field);

        if ($fieldFormatter === null) {
            return $dataToFormat;
        }

        return $fieldFormatter->extractValue($dataToFormat);
    }

    /**
     * Loops through the data arrays to get the appropriate fieldDefs and format the data based on the field type.
     *
     * @param array $dataArray The array of data to be formatted
     * @param string|null $section The name of the section being formatted, used with the fieldMap to get the correct fieldname
     *
     * @return array Returns the array of data after formatting has been applied to each field
     *
     * @throws Throwable
     */
    private function extractAndFormatDataInArray(array $dataArray, ?string $section = 'module'): array
    {
        ++$this->loopProtection;

        // If the loop has been done more than 5 times then there's probably something wrong, as data should be nested 5 levels deep
        if ($this->loopProtection > 5) {
            throw new Exception('Infinite Loop detected while trying to format data, exiting to prevent long running process');
        }

        // Pass by references are used so that the values are updated directly to the data array which can then be passed back
        foreach ($dataArray as $key => &$record) {
            if ($section === self::UDF_SECTION) {
                $field = $this->fieldDefsService->getRegistryFieldDefinitionFromMap(self::UDF_PREFIX . $key, $this->fieldMapService->getFieldMap('module'));

                if ($field) {
                    $record['fieldValue'] = $this->formatByFieldType($field, $record['fieldValue']);

                    $record['type'] = FieldTypeMapper::getHumanReadableFieldType($field);
                }
            } else {
                foreach ($record as $fieldName => &$fieldValue) {
                    /**
                     * If the fieldValue is an array, then it's a linked data section and needs iterating over again to extract
                     * the field names and values for the section. The section name is the $fieldName variable at this point.
                     */
                    if (is_array($fieldValue)) {
                        $fieldValue = $this->extractAndFormatDataInArray($fieldValue, $fieldName);

                        // We have come back a level in the loop, use protection as a depth counter.
                        --$this->loopProtection;
                    } else {
                        $field = $this->fieldDefsService->getRegistryFieldDefinitionFromMap($fieldName, $this->fieldMapService->getFieldMap($section));

                        $fieldValue = $this->formatByFieldType($field, $fieldValue);
                    }
                }
            }
        }

        return $dataArray;
    }

    /**
     * Formats the data based on the fieldDefs object passed in.
     *
     * @param FieldInterface|null $field The fieldDef object of the field being formatted
     * @param mixed $dataToFormat The data to format - hard to type hint as could be mixed data
     *
     * @return mixed The formatted data
     */
    private function formatByFieldType(?FieldInterface $field, $dataToFormat)
    {
        if (empty($field)) {
            return $dataToFormat;
        }

        $fieldFormatter = $this->fieldFormatterFactory->getFormatter($field);

        if ($fieldFormatter === null) {
            return $dataToFormat;
        }

        return $fieldFormatter->format($dataToFormat);
    }
}
