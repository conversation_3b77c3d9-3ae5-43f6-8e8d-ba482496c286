<?php

declare(strict_types=1);

namespace api\V2\Services;

use Psr\Http\Message\ServerRequestInterface as Request;

/**
 * This exists purely so we can use <PERSON><PERSON><PERSON> to mock the interface, since the class itself is declared final.
 * See https://docs.mockery.io/en/latest/reference/final_methods_classes.html.
 */
interface UserAccessServiceInterface
{
    public function userCanUpdateRecords(Request $request): bool;
}
