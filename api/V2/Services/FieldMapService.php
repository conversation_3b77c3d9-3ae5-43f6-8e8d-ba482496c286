<?php

declare(strict_types=1);

namespace api\V2\Services;

use Exception;

class FieldMapService
{
    public const MAP_LOCATIONS = [
        'feedback' => self::BASE_MAPS_URL . 'FeedbackMap.php',
        'incident' => self::BASE_MAPS_URL . 'IncidentsMap.php',
    ];
    private const BASE_MAPS_URL = __DIR__ . '/../Maps/';
    protected array $fieldMap;

    public function __construct(array $fieldMap)
    {
        if (empty($fieldMap)) {
            throw new Exception('No field map is provided. API responses require a map for fields for data security purposes');
        }

        $this->fieldMap = $fieldMap;
    }

    /**
     * Return the full fieldMap or the part of the map specified in $section.
     *
     * @param string|null $section The section of the map to return
     */
    public function getFieldMap(?string $section = null): array
    {
        if ($section) {
            return $this->fieldMap[$section];
        }

        return $this->fieldMap;
    }

    public function getFieldMaps(array $sections): array
    {
        $map = [];
        foreach ($sections as $section) {
            $map[$section] = $this->fieldMap[$section];
        }

        return $map;
    }

    public function getAllLinkedSectionKeys(): array
    {
        return array_diff(array_keys($this->fieldMap), ['module', 'extraFields']);
    }

    /**
     * Returns an array of select field that can be used in SQL to build an aliased set of data.
     *
     * @param string $section The section of the map to use for returning select fields
     *
     * @return array An array of database fields aliased to the api field names
     */
    public function getSelectFieldsFromMap(string $section, array $fieldsToSelect = []): array
    {
        $fieldMap = $this->getFieldMap($section);
        $selectFields = [];
        $sectionFieldsToSelect = [];

        if (array_key_exists($section, $fieldsToSelect)) {
            $sectionFieldsToSelect = $fieldsToSelect[$section];
        }

        if ($section !== 'module' && is_array($sectionFieldsToSelect)) {
            foreach ($fieldMap as $alias => $fieldName) {
                $selectFields[] = ['? as [' . $alias . ']', $fieldName];
            }

            return $selectFields;
        }



        // Get the fields to use for the select of the main table data
        foreach ($fieldMap as $alias => $fieldName) {
            if (empty($sectionFieldsToSelect) || in_array($alias, $sectionFieldsToSelect, true)) {
                $selectFields[] = ['? as [' . $alias . ']', $fieldName];
            }
        }

        return $selectFields;
    }
}
