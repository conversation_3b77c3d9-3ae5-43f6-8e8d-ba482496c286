<?php

declare(strict_types=1);

namespace api\V2\Services\Etl;

class EtlTableResult
{
    /** @var string[] */
    private array $invalid = [];

    /** @var string[] */
    private array $valid = [];

    /**
     * @return string[]
     */
    public function getValid(): array
    {
        return $this->valid;
    }

    /**
     * @return string[]
     */
    public function getInvalid(): array
    {
        return $this->invalid;
    }

    /**
     * @param string[] $valid
     */
    public function setValid(array $valid): void
    {
        $this->valid = $valid;
    }

    /**
     * @param string[] $invalid
     */
    public function setInvalid(array $invalid): void
    {
        $this->invalid = $invalid;
    }
}
