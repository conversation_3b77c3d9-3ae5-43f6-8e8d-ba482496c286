<?php

declare(strict_types=1);

namespace api\V2\Services\Etl;

use api\V2\Repository\Etl\EtlTableSyncRepository;

class EtlTableNameValidationService
{
    private EtlTableSyncRepository $repository;

    public function __construct(EtlTableSyncRepository $repository)
    {
        $this->repository = $repository;
    }

    public function validateTableNames(array $tableNames): EtlTableResult
    {
        $ValidatedTableData = $this->repository->checkTablesExists($tableNames);

        $etlTableResult = new EtlTableResult();
        $etlTableResult->setValid($ValidatedTableData);
        $etlTableResult->setInvalid(array_values(array_diff($tableNames, $ValidatedTableData)));

        return $etlTableResult;
    }
}
