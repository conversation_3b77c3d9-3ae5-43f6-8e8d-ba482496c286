<?php

declare(strict_types=1);

namespace api\V2\Services\Etl;

use api\V2\Repository\Etl\EtlTableSyncRepository;
use Doctrine\DBAL\Exception;
use InvalidArgumentException;
use src\logger\Facade\Log;
use Throwable;

use function in_array;

class EtlTableSyncService
{
    private const PK_COLUMN_NAMES = ['[code]', '[id]', '[uuid]', '[recordid]'];
    private EtlTableSyncRepository $repository;

    public function __construct(EtlTableSyncRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @throws Throwable
     */
    public function exportTable(string $tableName, int $pageSize, int $pageNumber): array
    {
        try {
            if ($tableName === '') {
                throw new InvalidArgumentException('Table name cannot be empty');
            }
            if ($pageNumber <= 0) {
                throw new InvalidArgumentException('Page number needs to be greater than 0');
            }
            if ($pageSize <= 0) {
                throw new InvalidArgumentException('Page size needs to be greater than 0');
            }

            $columns = $this->repository->getColumns($tableName);
            if (empty($columns)) {
                throw new InvalidArgumentException("Table '{$tableName}' doesn't have any columns for some reason!");
            }
            $primaryKey = $this->getPrimaryKeys($columns);

            $columnToUpdate = $this->getFirstNonPkColumnName($columns);
            if ($columnToUpdate === null) {
                // Table only has PK columns for some reason.
                // Reset the PK to null forcing it to do a full table update, then grab the first column name to update.
                $primaryKey = null;
                $columnToUpdate = reset($columns)['COLUMN_NAME'];
            }

            $tableName = $this->repository->quoteName($tableName);
            $totalRows = $this->repository->getTotalRows($tableName);

            $offset = ($pageNumber - 1) * $pageSize;
            if ($offset < $totalRows) {
                $this->updateEtlWithRows($tableName, $offset, $pageSize, $primaryKey, $columnToUpdate);
            }

            return [
                'totalRows' => $totalRows,
                'haltBatchProcess' => empty($primaryKey),
            ];
        } catch (Throwable $e) {
            Log::error(
                'Error occurred whilst transmitting table data to ETL.',
                [
                    'tableName' => $tableName,
                    'exception' => $e,
                ],
            );

            throw $e;
        }
    }

    private function getPrimaryKeys(array $columns): ?string
    {
        $pk = array_filter($columns, static fn ($row) => $row['IsPrimaryKey'] === '1');
        if (!empty($pk)) {
            return implode(', ', array_map(static fn ($row) => $row['COLUMN_NAME'], $pk));
        }

        $idCols = array_filter($columns, static fn ($row) => in_array($row['COLUMN_NAME'], self::PK_COLUMN_NAMES));
        if (!empty($idCols)) {
            return implode(', ', array_map(static fn ($row) => $row['COLUMN_NAME'], $idCols));
        }

        return null;
    }

    private function getFirstNonPkColumnName(array $columns): ?string
    {
        $nonPkColumns = array_filter(
            $columns,
            static fn ($row) => !$row['IsPrimaryKey'] && !in_array($row['COLUMN_NAME'], self::PK_COLUMN_NAMES),
        );
        if (empty($nonPkColumns)) {
            return null;
        }

        return reset($nonPkColumns)['COLUMN_NAME'];
    }

    /**
     * @throws Throwable
     * @throws Exception
     */
    private function updateEtlWithRows(
        string $tableName,
        int $offset,
        int $pageSize,
        ?string $identifier,
        string $columnToUpdate
    ): void {
        if ($identifier) {
            $start = $offset + 1;
            $end = $offset + $pageSize;
            $this->repository->updateBatch($tableName, $identifier, $columnToUpdate, $start, $end);
        } else {
            $this->repository->updateFullTable($tableName, $columnToUpdate);
        }
    }
}
