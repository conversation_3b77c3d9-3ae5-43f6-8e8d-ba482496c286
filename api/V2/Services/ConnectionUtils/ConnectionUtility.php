<?php

namespace api\V2\Services\ConnectionUtils;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Result;
use Throwable;

final class ConnectionUtility
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public static function create(Connection $connection): self
    {
        return new self($connection);
    }

    public function withTransaction(callable $callback): void
    {
        $this->connection->beginTransaction();

        try {
            $callback($this->connection);

            $this->entityManager->flush();
            $this->connection->commit();
        } catch (Throwable $e) {
            $this->connection->rollBack();

            throw $e;
        }
    }

    public function insert(string $table, $data): void
    {
        $this->connection->insert($table, $data);
    }

    public function query(string $query): Result
    {
        return $this->connection->executeQuery($query);
    }

    public function truncateTablesById(array $tablesWithIds, int $id): void
    {
        foreach ($tablesWithIds as $table => $idColumn) {
            $this->truncateTableById($table, $idColumn, $id);
        }
    }

    /**
     * Truncate the given list of table names.
     *
     * @param array<string> $tableNames
     */
    public function truncateTables(array $tableNames): void
    {
        foreach ($tableNames as $table) {
            $this->truncateTable($table);
        }
    }

    private function truncateTableById(string $table, string $idColumn, int $id): void
    {
        $this->connection->executeStatement('DELETE FROM ' . $table . ' WHERE ' . $idColumn . ' = ' . $id);
    }

    /**
     * Truncate (empty) the given table.
     */
    private function truncateTable(string $tableName)
    {
        $this->connection->executeStatement('DELETE FROM ' . $tableName);
    }
}
