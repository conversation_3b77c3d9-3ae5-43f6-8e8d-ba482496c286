<?php

namespace api\V2\Services\ConnectionUtils;

use Doctrine\ORM\EntityManagerInterface;
use Throwable;

final class ConnectionTemplate
{
    private EntityManagerInterface $entityManager;

    public function __construct(
        EntityManagerInterface $entityManager
    ) {
        $this->entityManager = $entityManager;
    }

    public function withTransaction(callable $callback)
    {
        $connection = $this->entityManager->getConnection();

        $connection->beginTransaction();

        try {
            $result = $callback($connection);

            $this->entityManager->flush();
            $connection->commit();

            return $result;
        } catch (Throwable $e) {
            $connection->rollBack();

            throw $e;
        }
    }
}
