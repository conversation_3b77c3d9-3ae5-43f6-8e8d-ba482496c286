<?php

declare(strict_types=1);

namespace api\V2\Services;

class EntityService
{
    public $entity;

    public function setEntity($entity): void
    {
        $this->entity = $entity;
    }

    /**
     * @return mixed
     */
    public function getFromUnderscoreNaming(string $underscoreName)
    {
        $getter = 'get' . str_replace('_', '', ucwords($underscoreName));
        if (!method_exists($this->entity, $getter)) {
            return null;
        }

        return $this->entity->{$getter}();
    }
}
