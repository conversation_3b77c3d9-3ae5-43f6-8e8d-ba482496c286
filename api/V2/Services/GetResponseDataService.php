<?php

declare(strict_types=1);

namespace api\V2\Services;

use api\exceptions\ApiValidationException;
use src\framework\model\ModelFactory;
use Throwable;

use function array_key_exists;

class GetResponseDataService
{
    private ModelFactory $modelFactory;
    private DataFormatterService $dataFormatterService;
    private FieldMapService $fieldMapService;

    public function __construct(
        ModelFactory $modelFactory,
        DataFormatterService $dataFormatterService,
        FieldMapService $fieldMapService
    ) {
        $this->modelFactory = $modelFactory;
        $this->dataFormatterService = $dataFormatterService;
        $this->fieldMapService = $fieldMapService;
    }

    /**
     * Gets the data and formats it to be returned to the response based on filter where clauses and query options, data
     * is returned in a format that is compatible with the response output for a successful request.
     *
     * @param array $whereParameters Array of data to use for the where clause, formatted ['module.fieldName' => 'searchTerm']
     * @param array $queryOptions Array of options to use for the query, formatted ['limit' => ##, 'offset' => ##, 'orderBy' => [['orderField']]]
     *
     * @throws Throwable
     */
    public function getResponseData(array $whereParameters, array $queryOptions, array $fieldsToSelect = [], $whereParametersEqualities = []): array
    {
        $moduleMapper = $this->modelFactory->getMapper();

        $filters = $this->getFiltersFromQueryStringWhere($whereParameters);

        $selectFields = $this->fieldMapService->getSelectFieldsFromMap('module', $fieldsToSelect);
        if (empty($queryOptions['orderBy'])) {
            $moduleFields = $this->fieldMapService->getFieldMap('module');
            $queryOptions['orderBy'] = [$moduleFields['id']];
        }
        $linkedDataSelectFields = [];

        foreach ($this->fieldMapService->getFieldMap() as $section => $sectionMap) {
            // Get field to use for the select for each linked data section
            if ($section !== 'module') {
                $linkedDataSelectFields[$section] = $this->fieldMapService->getSelectFieldsFromMap($section, $fieldsToSelect);
            }
        }

        if (count($fieldsToSelect['module']) > count($selectFields)) {
            throw new ApiValidationException('incorrect field passed into select clauses.', 400);
        }

        $matchingRecordsCount = $moduleMapper->findByFilter($filters, [], [], false, $whereParametersEqualities, true)[0]['count'];

        if (!empty($queryOptions['page'])) {
            $queryOptions['offset'] = ((int) $queryOptions['limit'] * ((int) $queryOptions['page'] - 1));
            $queryOptions['offset'] = max(0, $queryOptions['offset']);
        }

        $mainRecordData = $moduleMapper->findByFilter($filters, $queryOptions, $selectFields, false, $whereParametersEqualities, false);

        $mainRecordLinkedData = $moduleMapper->getRecordsLinkedData($mainRecordData, 'id', $linkedDataSelectFields, $fieldsToSelect);

        // Iterate over each record in the results data to merge in linked data for each record. Uses pass by reference to pass back changes to $mainRecordData
        foreach ($mainRecordData as &$record) {
            // Remove unnecessary data added by the SQL to enable offset limit to work in MSSQL
            unset($record['row_num']);

            $record = $this->mergeLinkedDataIntoRecord($record, $mainRecordLinkedData);
        }

        $formattedData = $this->dataFormatterService->formatData($mainRecordData);

        return [
            'metadata' => [
                'count' => (int) $matchingRecordsCount,
                'offset' => (int) $queryOptions['offset'],
                'limit' => (int) $queryOptions['limit'],
                'page' => (int) $queryOptions['page'],
            ],
            'data' => $formattedData,
        ];
    }

    /**
     * Extracts the filters from the query string where parameter and returns the filters as an array after mapping the
     * request field names to the database field names.
     *
     * @param array $queryWhere The where array from the query string
     *
     * @return array returns the array of filters after mapping
     */
    private function getFiltersFromQueryStringWhere(array $queryWhere): array
    {
        $filters = [
            'extraFields' => [],
        ];

        $moduleFieldMap = $this->fieldMapService->getFieldMap();

        // Extract filters from where parameter in the query parameters
        foreach ($queryWhere as $fieldName => $value) {
            $fieldNameParts = explode('.', $fieldName);

            if (substr($fieldName, 0, 4) === 'UDF_') {
                $udfParts = explode('_', $fieldName);
                $udfId = $udfParts[1] ?? null;

                if (empty($udfId)) {
                    continue;
                }

                $filters['extraFields'][] = [
                    'value' => $value,
                    'field_id' => $udfId,
                ];

                continue;
            }

            $fieldMaps = $moduleFieldMap[$fieldNameParts[0]] ?? $moduleFieldMap['module'];

            if (array_key_exists($fieldNameParts[1], $fieldMaps) || array_key_exists($fieldNameParts[0], $fieldMaps)) {
                $filters[$fieldMaps[$fieldNameParts[1] ?? $fieldNameParts[0]]] = $value;
            }
        }

        return $filters;
    }

    /**
     * Merged an array of linked data from the database into the main record data so that it can be returned as a single
     * response with UDF fields in line with main record data and linked data in separate arrays.
     *
     * @param array $record The main record data array
     * @param array $linkedData Array of linked data to be merged into each record
     *
     * @return array The record after having any linked data merge into it
     */
    private function mergeLinkedDataIntoRecord(array $record, array $linkedData): array
    {
        $recordLinkedData = [];
        $udfFields = [];

        // Iterate over the linked data array to merge each part into the correct place within the main data array
        foreach ($linkedData as $section => $sectionRecords) {
            // Set up the array for the section so that even if there's no data for the record, the section is shown
            $recordLinkedData[$section] = [];

            foreach ($sectionRecords as $sectionRecord) {
                if ($sectionRecord['recordid'] === $record['id']) {
                    // Get rid of the main record id, so it's not added again
                    unset($sectionRecord['recordid']);

                    // Either merge the extraFields data alongside the main record data or add it to the linkedData array by section for merging later
                    if ($section === 'extraFields') {
                        $udfValue = $sectionRecord['udf_string'] ?? $sectionRecord['udf_number'] ?? $sectionRecord['udf_date'] ?? $sectionRecord['udf_money'] ?? $sectionRecord['udf_text'];
                        $udfFields['udfs'][$sectionRecord['udf_field_id']] = ['fieldName' => $sectionRecord['udf_field_description'], 'fieldValue' => $udfValue];
                    } else {
                        $recordLinkedData[$section][] = $sectionRecord;
                    }
                }
            }
        }

        // Merge UDFs into the main record data and remove the extraFields section from $recordLinkedData, so it's not
        // added to the response as a separate section
        $record = array_merge($record, $udfFields);
        unset($recordLinkedData['extraFields']);

        // merge the rest of the linked data with the main table record data, maintaining the keyed arrays for each linked data type
        if (!empty($recordLinkedData)) {
            $record = array_merge($record, $recordLinkedData);
        }

        // Return the $record after any data that exists for it has been merged
        return $record;
    }
}
