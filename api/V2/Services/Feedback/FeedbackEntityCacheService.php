<?php

declare(strict_types=1);

namespace api\V2\Services\Feedback;

use api\V2\Services\EntityCacheServiceInterface;
use app\models\feedback\entities\FeedbackEntity;
use Exception;

class FeedbackEntityCacheService implements EntityCacheServiceInterface
{
    private $entityCache = [];

    public function getCachedEntity(int $id): ?FeedbackEntity
    {
        return $this->entityCache[$id] ?? null;
    }

    public function setCachedEntity(int $id, $entity): FeedbackEntity
    {
        if (!$entity instanceof FeedbackEntity) {
            throw new Exception('Entity to be cached is not a FeedbackEntity', 500);
        }
        $this->entityCache[$id] = $entity;

        return $entity;
    }
}
