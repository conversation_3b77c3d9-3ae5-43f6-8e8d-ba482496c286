<?php

declare(strict_types=1);

namespace api\V2\Services;

use Psr\Http\Message\ResponseInterface as Response;
use src\framework\json\Json;
use Teapot\StatusCode\Http;

class ResponseService
{
    /**
     * Formats the response for a GET request.
     *
     * @param Response $response Response object to use for returning response
     * @param array $resultsData Array of results and metadata to format into json object to be returned in response
     */
    public function formatSuccessGetResponse(Response $response, array $resultsData): Response
    {
        $response
            ->getBody()
            ->write(Json::encode([
                'data' => $resultsData['data'],
                'metadata' => [
                    'resultset' => [
                        'count' => (int) $resultsData['metadata']['count'],
                        'offset' => (int) $resultsData['metadata']['offset'],
                        'limit' => (int) $resultsData['metadata']['limit'],
                        'page' => (int) $resultsData['metadata']['page'],
                    ],
                ],
            ]));

        return $response
            ->withHeader('Content-Type', 'application/json')
            ->withstatus(Http::OK);
    }

    /**
     * Formats the response for a PATCH.
     *
     * @param Response $response Response object to use for returning response
     * @param array $resultsData Array of results and metadata to format into json object to be returned in response
     */
    public function formatSuccessPatchResponse(Response $response, array $resultsData): Response
    {
        $response->getBody()->write(Json::encode($resultsData));

        return $response
            ->withHeader('content-type', 'application/json')
            ->withStatus(Http::OK);
    }

    /**
     * Formats the response for an error.
     *
     * @param Response $response Response object to use for returning response
     */
    public function formatErrorResponse(
        Response $response,
        int $code,
        string $details,
        array $optional = []
    ): Response {
        // We set the status separately here so we can grab the reason phrase below
        $response = $response->withStatus($code);

        $response->getBody()->write(Json::encode(array_merge(
            [
                'type' => 'http://www.w3.org/Protocols/rfc2616/rfc2616-sec10.html',
                'title' => $response->getReasonPhrase(),
                'status' => $code,
                'detail' => $details,
            ],
            $optional,
        )));

        return $response->withHeader('content-type', 'application/problem+json');
    }
}
