<?php

declare(strict_types=1);

namespace api\V2\Services;

use api\helpers\UsersHelper;
use app\models\generic\valueObjects\JSONData;
use Exception;
use Psr\Http\Message\ServerRequestInterface as Request;
use src\users\model\User;
use Teapot\StatusCode\Http;

use function in_array;

use const ARRAY_FILTER_USE_KEY;

class RequestService
{
    public const API_GET_LIMIT_MAX = 25;
    public const API_GET_PAGE_DEFAULT = 1;
    private const API_GET_LIMIT_DEFAULT = 10;
    private const API_GET_OFFSET_DEFAULT = 0;
    private const LINKED_DATA_ARRAYS = [
        'subjects',
        'progressNotes',
        'documents',
    ];
    private ModuleService $moduleService;
    private UsersHelper $usersHelper;

    public function __construct(ModuleService $moduleService, UsersHelper $usersHelper)
    {
        $this->moduleService = $moduleService;
        $this->usersHelper = $usersHelper;
    }

    /**
     * Extracts data from the query string to return as an array of the where and options for the query.
     *
     * @param array $requestQueryParameters The queryParameter array from the request
     */
    public function extractFromQueryString(array $requestQueryParameters): array
    {
        $options = [];

        $options['limit'] = $requestQueryParameters['limit'] ?? self::API_GET_LIMIT_DEFAULT;
        $options['offset'] = $requestQueryParameters['offset'] ?? self::API_GET_OFFSET_DEFAULT;
        $options['orderBy'] = [];

        $options['page'] = $requestQueryParameters['page'] ?? self::API_GET_PAGE_DEFAULT;

        $fieldsToSelect['module'] = $requestQueryParameters['selectCore'] ?? [];
        $fieldsToSelect['extraFields'] = $requestQueryParameters['selectUdf'] ?? [];

        $where = $requestQueryParameters['where'] ?? [];

        return [$where, $options, $fieldsToSelect];
    }

    /**
     * Gets the three letter module code from the request by using the path to determine the code.
     *
     * @param Request $request The request object to use for getting the module code
     *
     * @return string
     */
    public function getModuleCodeFromRequest(Request $request): ?string
    {
        $moduleCode = null;

        $pathParts = explode('/', $request->getUri()->getPath());

        // Only get the code from the path if the route is coming via the v2 group
        if ($this->isV2Route($request)) {
            $moduleCode = $this->moduleService->getModuleCodeFromRoutePath($pathParts[3]);
        }

        return $moduleCode;
    }

    public function parseJsonBodyToArray(Request $request): array
    {
        try {
            return (new JSONData($request->getBody()))->toArray();
        }
        // Catches error thrown in JSONData and add Bad Request code to the exception being thrown
        catch (Exception $exception) {
            throw new Exception($exception->getMessage(), Http::BAD_REQUEST);
        }
    }

    public function extractMainRecordDataFromRequest(Request $request): array
    {
        return array_filter(
            $this->parseJsonBodyToArray($request),
            static function (string $fieldName): bool {
                if (
                    preg_match(ModuleService::UDF_REGEX_PATTERN, $fieldName)
                    || in_array($fieldName, self::LINKED_DATA_ARRAYS, true)
                ) {
                    return false;
                }

                return true;
            },
            ARRAY_FILTER_USE_KEY,
        );
    }

    public function extractUdfDataFromRequest(Request $request): array
    {
        return array_filter(
            $this->parseJsonBodyToArray($request),
            static function (string $fieldName): bool {
                if (!preg_match(ModuleService::UDF_REGEX_PATTERN, $fieldName)) {
                    return false;
                }

                return true;
            },
            ARRAY_FILTER_USE_KEY,
        );
    }

    public function extractLinkedDataFromRequest(Request $request, string $section): ?array
    {
        $data = $this->parseJsonBodyToArray($request);

        return $data[$section];
    }

    public function extractAllLinkedDataFromRequest(Request $request): array
    {
        return array_filter(
            $this->parseJsonBodyToArray($request),
            static function (string $fieldName): bool {
                if (!in_array($fieldName, self::LINKED_DATA_ARRAYS, true)) {
                    return false;
                }

                return true;
            },
            ARRAY_FILTER_USE_KEY,
        );
    }

    public function extractUserFromRequest(Request $request): ?User
    {
        $token = $request->getAttribute('token');

        return $this->usersHelper::getUserFromJWT($token);
    }

    public function isV2Route(Request $request): bool
    {
        return str_starts_with($request->getUri()->getPath(), '/api/v2/');
    }
}
