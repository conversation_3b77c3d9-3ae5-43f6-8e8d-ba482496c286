<?php

declare(strict_types=1);

namespace api\V2\Services;

use app\models\generic\valueObjects\Module;

use function array_key_exists;

class ModuleService
{
    public const UDF_REGEX_PATTERN = '/UDF_[0-9]+/';

    // Maintains a map of Table names for module codes so that a table name can be converted to a code
    private const MODULE_TABLE_CODE_MAP = [
        'compl_main' => 'COM',
        'compl_subjects' => 'COM',
        'incidents_main' => 'INC',
    ];

    // Maintains a map of request path locations to a module code eg. /feedback to COM
    private const PATH_CODE_MAP = [
        'feedback' => 'COM',
        'incidents' => 'INC',
    ];
    private const SECTION_NAME_TABLE_MAP = [
        'subjects' => 'compl_subjects',
        'progressNotes' => 'progress_notes',
        'documents' => 'link_documents',
    ];
    private const SECTION_NAME_ID_FIELD_MAP = [
        'subjects' => 'recordid',
        'progressNotes' => 'recordid',
        'documents' => 'id',
    ];

    public function getModuleCodeFromTable(string $table): string
    {
        return self::MODULE_TABLE_CODE_MAP[$table];
    }

    public function hasModuleCodeForTable(string $table): bool
    {
        return array_key_exists($table, self::MODULE_TABLE_CODE_MAP);
    }

    public function getModuleCodeFromRoutePath(string $routePath): string
    {
        return self::PATH_CODE_MAP[$routePath];
    }

    public function getTableFromSectionName(string $sectionName): string
    {
        return self::SECTION_NAME_TABLE_MAP[$sectionName];
    }

    public function getIdFieldFromSectionName(string $sectionName): string
    {
        return self::SECTION_NAME_ID_FIELD_MAP[$sectionName];
    }

    public function getModuleObject(string $moduleCode)
    {
        return new Module($moduleCode);
    }
}
