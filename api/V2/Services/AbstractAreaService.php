<?php

namespace api\V2\Services;

use api\service\LocationsServicesData\LocationsServicesData;
use api\V2\Services\ConnectionUtils\ConnectionTemplate;
use api\V2\Services\ConnectionUtils\ConnectionUtility;
use app\models\location\entities\LocationEntity;
use app\models\location\services\TreeFieldService;
use app\models\service\entities\ServiceEntity;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Exception;

use function array_key_exists;
use function sprintf;

/**
 * Shared functionality for importing data for Locations and Services.
 */
abstract class AbstractAreaService
{
    protected EntityManagerInterface $entityManager;
    private TreeFieldService $treeFieldService;
    private Connection $connection;

    public function __construct(
        EntityManagerInterface $entityManager,
        TreeFieldService $treeFieldService
    ) {
        $this->entityManager = $entityManager;
        $this->treeFieldService = $treeFieldService;

        $this->connection = $entityManager->getConnection();
    }

    /**
     * Import the given data into the system, clearing related data where necessary.
     *
     * @param LocationsServicesData $requestData Contains the Locations/Services data to import
     * @param bool $fromImportExportService Is the given request coming from the import/export service
     *
     * @return LocationEntity|ServiceEntity The entity saved by this method
     */
    public function import(LocationsServicesData $requestData, bool $fromImportExportService = false)
    {
        $nodeId = (int) $requestData->getNode()['id'];

        $connectionTemplate = new ConnectionTemplate($this->entityManager);

        $fullPublish = $requestData->isPublishing() === true || $fromImportExportService === true;

        $connectionTemplate->withTransaction(function (Connection $connection) use ($fullPublish, $requestData, $nodeId) {
            $connectionUtility = ConnectionUtility::create($this->connection);
            $princeFields = $this->getPrinceFields($connectionUtility);
            $tableNames = $this->getStandardTables();
            $connectionUtility->truncateTables($tableNames);

            if ($fullPublish) {
                $tableNames = array_keys($this->getTablesWithIds());
                $connectionUtility->truncateTables($tableNames);
            } else {
                $tablesWithIds = $this->getTablesWithIds();
                $connectionUtility->truncateTablesById($tablesWithIds, $nodeId);
            }

            $this->insertTree($connectionUtility, $requestData, $princeFields);

            $this->entityManager->flush();
        });

        $savedRecord = $this->getEntityById($nodeId);

        if (!$savedRecord->isEnabled()) {
            $this->treeFieldService->changeActiveStateOfChildren(
                $this->getEntityType(),
                [$savedRecord->getId()],
                $savedRecord->getStatus(),
            );
        }

        return $savedRecord;
    }

    abstract protected function getEntityRepository();

    abstract protected function getEntityTableName(): string;

    abstract protected function getCodeTable(): string;

    abstract protected function hydrate($treeItem);

    abstract protected function getEntityType(): string;

    abstract protected function getPrinceOnlyFields(): array;

    abstract protected function getTablesWithIds(): array;

    abstract protected function getStandardTables(): array;

    /**
     * @param int $id ID of entity to find
     *
     * @return LocationEntity|ServiceEntity
     *
     * Find an entity using a given ID
     */
    private function getEntityById(int $id)
    {
        return $this->getEntityRepository()->findOneBy(['id' => $id]);
    }

    private function insertTree(ConnectionUtility $connectionUtility, LocationsServicesData $requestData, ?array $princeFields): void
    {
        $editedRecord = $requestData->getNode();
        $tree = $requestData->getTree();

        foreach ($tree as $treeItem) {
            if ($treeItem['id'] == $editedRecord['id']) {
                $treeItem = $editedRecord; // Contains tags info
            }

            if (array_key_exists($treeItem['id'], $princeFields)) {
                $treeItem = array_merge($treeItem, $princeFields[$treeItem['id']]);
            }

            $entity = $this->hydrate($treeItem);
            $this->entityManager->persist($entity);

            $codeData = array_merge(['id' => $treeItem['id']], $princeFields[$treeItem['id']] ?? []);
            unset($codeData['cod_ncds_location']);
            if ($codeData === null) {
                $msg = error_get_last();

                throw new Exception($msg['message']);
            }

            $connectionUtility->insert($this->getCodeTable(), $codeData);
        }
    }

    /**
     * @return array<int, array<string, string>>|null
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    private function getPrinceFields(ConnectionUtility $ConnectionUtility): ?array
    {
        $princeSqlString = implode(', ', $this->getPrinceOnlyFields());

        $where = implode(' OR ', array_map(static function (string $field): string {
            return "{$field} IS NOT NULL";
        }, $this->getPrinceOnlyFields()));

        $sql = sprintf('SELECT id, %s FROM %s WHERE %s;', $princeSqlString, $this->getEntityTableName(), $where);

        return $ConnectionUtility
            ->query($sql)
            ->fetchAllAssociativeIndexed();
    }
}
