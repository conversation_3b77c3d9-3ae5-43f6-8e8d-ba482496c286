<?php

declare(strict_types=1);

namespace api\V2\Services;

use src\framework\registry\Registry;
use src\system\database\FieldInterface;

class FieldDefsService
{
    private Registry $registry;
    private ModuleService $moduleService;
    private array $fieldDefinitions;

    public function __construct(
        Registry $registry,
        ModuleService $moduleService
    ) {
        $this->registry = $registry;
        $this->moduleService = $moduleService;
    }

    /**
     * Used to build the FieldDefsExtra array by including FieldDefs.php and allow the process of merging in linked module
     * fieldDefs to build the FieldDefsExtra array so it can be returned.
     *
     * @return array The array containing FieldDefsExtra
     */
    public function buildFieldDefsExtra(): array
    {
        // Without this the $FieldDefs and $FieldDefsExtra variables aren't available in the global scope
        // for merging in linked module fields to $FieldDefs and returning $FieldDefsExtra.
        // Ideally this wouldn't be required, but it is
        global $FieldDefs, $FieldDefsExtra;

        include __DIR__ . '/../../../Source/FieldDefs.php';

        return $GLOBALS['FieldDefsExtra'];
    }

    /**
     * Gets the FieldDefsExtra definition for the field object passed in. If the FieldDefsExtra global isn't already set
     * up, it will be built before returning a value.
     */
    public function getFieldDefinition(FieldInterface $field): array
    {
        if (!isset($this->fieldDefinitions)) {
            $this->fieldDefinitions = $this->buildFieldDefsExtra();
        }

        if (!$this->moduleService->hasModuleCodeForTable($field->getTable())) {
            return [];
        }

        return $this->fieldDefinitions[$this->moduleService->getModuleCodeFromTable($field->getTable())][$field->getName()];
    }

    /**
     * Gets the Registry field definition (from field_directory) for the field name passed in.
     *
     * @param string $fieldName a fully qualified field name string eg. compl_main.recordid
     * @param array|null $fieldMap The field map to use for getting the fully qualified field name if required
     */
    public function getRegistryFieldDefinitionFromMap(string $fieldName, ?array $fieldMap = null): ?FieldInterface
    {
        $fieldDefs = $this->registry->getFieldDefs();

        if (preg_match(ModuleService::UDF_REGEX_PATTERN, $fieldName)) {
            return $fieldDefs[$fieldName];
        }

        if (isset($fieldMap[$fieldName])) {
            return $fieldDefs[$fieldMap[$fieldName]];
        }

        return null;
    }

    /**
     * Gets the Registry field definition (from field_directory) for the field name passed in.
     *
     * @param string $tableColumnName a fully qualified field name string eg. compl_main.recordid
     */
    public function getRegistryFieldDefinition(string $tableColumnName): ?FieldInterface
    {
        $fieldDefs = $this->registry->getFieldDefs();

        return $fieldDefs[$tableColumnName];
    }
}
