<?php

declare(strict_types=1);

namespace api\V2\Services;

use Psr\Http\Message\ServerRequestInterface as Request;

final class UserAccessService implements UserAccessServiceInterface
{
    private RequestService $requestService;

    public function __construct(RequestService $requestService)
    {
        $this->requestService = $requestService;
    }

    public function userCanUpdateRecords(Request $request): bool
    {
        $user = $this->requestService->extractUserFromRequest($request);
        if ($user === null) {
            return false;
        }

        $module = $this->requestService->getModuleCodeFromRequest($request);

        return $user->canUpdateExistingRecords($module);
    }
}
