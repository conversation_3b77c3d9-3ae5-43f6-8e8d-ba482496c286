<?php

declare(strict_types=1);

namespace api\V2\Services;

interface EntityCacheServiceInterface
{
    /**
     * Returns an already hydrated entity stored in the cache array or null if no entity matching the id can be found.
     *
     * @return mixed
     */
    public function getCachedEntity(int $id);

    /**
     * Sets the passed in hydrated entity in the cache array against the id, so it can be returned when required without
     * risk of rehydrating an already hydrated entity.
     *
     * @param $entity
     *
     * @return mixed
     */
    public function setCachedEntity(int $id, $entity);
}
