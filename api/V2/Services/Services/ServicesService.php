<?php

namespace api\V2\Services\Services;

use api\V2\Services\AbstractAreaService;
use app\models\location\services\TreeFieldService;
use app\models\service\entities\ServiceEntity;
use app\models\service\hydrators\ServiceHydrator;
use Doctrine\ORM\EntityManagerInterface;

class ServicesService extends AbstractAreaService
{
    private $serviceHydrator;

    public function __construct(
        EntityManagerInterface $entityManager,
        TreeFieldService $treeFieldService,
        ServiceHydrator $serviceHydrator
    ) {
        parent::__construct($entityManager, $treeFieldService);
        $this->serviceHydrator = $serviceHydrator;
    }

    protected function getEntityRepository()
    {
        return $this->entityManager->getRepository(ServiceEntity::class);
    }

    protected function getEntityTableName(): string
    {
        return $this->entityManager->getClassMetadata(ServiceEntity::class)->getTableName();
    }

    protected function getCodeTable(): string
    {
        return 'code_service';
    }

    protected function getEntityType(): string
    {
        return 'service';
    }

    protected function hydrate($treeItem)
    {
        return $this->serviceHydrator->hydrate($treeItem);
    }

    protected function getPrinceOnlyFields(): array
    {
        return $this->serviceHydrator->getPrinceOnlyFields();
    }

    /**
     * @return string[] (Table Name => Identity Column)
     */
    protected function getTablesWithIds(): array
    {
        return [
            'service_tag_links' => 'service_id',
            'services_locations' => 'serviceentity_id',
        ];
    }

    /**
     * @return string[]
     */
    protected function getStandardTables(): array
    {
        return [
            $this->getEntityTableName(),
            $this->getCodeTable(),
            'service_descr',
        ];
    }
}
