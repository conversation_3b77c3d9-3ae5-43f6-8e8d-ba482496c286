<?php

declare(strict_types=1);

namespace api\V2\Services;

use Cake\Utility\Inflector;
use Exception;
use ReflectionClass;
use src\system\database\FieldInterface;

use function in_array;
use function get_class;

class ReadOnlyFieldService
{
    private FieldDefsService $fieldDefsService;

    public function __construct(FieldDefsService $fieldDefsService)
    {
        $this->fieldDefsService = $fieldDefsService;
    }

    /**
     * <PERSON>moves read only field data from the request data so that it's not used for hydrating entity data.
     *
     * @param array $data The request data to remove read only field data from
     * @param array $fieldMap The field map for the data being processed
     * @param array $readOnlyFields array of fields that are always read only
     * @param object|null $entity The entity to use for checking existing values against
     * @param array|null $readOnlyFlagFields array of fields that are conditionally read only if value is currently Y
     *
     * @throws Exception
     */
    public function removeReadOnlyData(
        array $data,
        array $fieldMap,
        array $readOnlyFields,
        ?object $entity = null,
        ?array $readOnlyFlagFields = null
    ): array {
        foreach ($data as $fieldName => $fieldValue) {
            $field = $this->fieldDefsService->getRegistryFieldDefinitionFromMap($fieldName, $fieldMap);

            if (isset($field) && $this->isFieldReadOnly($field, $readOnlyFields, $entity, $readOnlyFlagFields)) {
                unset($data[$fieldName]);
            }
        }

        return $data;
    }

    /**
     * Checks if a field is read only based on either being an always read only field or having Y set on a conditional
     * read only field.
     *
     * @param FieldInterface $field The field object to check against
     * @param array $readOnlyFields array of fixed read only fields
     * @param mixed $entity Entity to use for checking existing data against, this currently doesn't have a common type to hint on
     * @param array|null $readOnlyFlagFields array of conditional read only fields
     *
     * @throws Exception
     */
    public function isFieldReadOnly(
        FieldInterface $field,
        array $readOnlyFields,
        ?object $entity = null,
        ?array $readOnlyFlagFields = null
    ): bool {
        if (in_array($field->getName(), $readOnlyFields, true)) {
            return true;
        }

        if (
            !empty($readOnlyFlagFields)
            && !empty($entity)
            && in_array($field->getName(), $readOnlyFlagFields, true)
        ) {
            $entityReflection = new ReflectionClass(get_class($entity));
            $getterName = 'get' . Inflector::camelize($field->getName());

            // If a flag field is set to "Y" on the existing entity, treat it as read only
            if (
                $entityReflection->hasMethod($getterName)
                && $entity->{$getterName}() === 'Y'
            ) {
                return true;
            }
        }

        return false;
    }
}
