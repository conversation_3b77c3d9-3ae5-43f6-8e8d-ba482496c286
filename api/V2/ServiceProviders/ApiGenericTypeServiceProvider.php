<?php

declare(strict_types=1);

namespace api\V2\ServiceProviders;

use api\helpers\UsersHelper;
use api\middleware\LanguageSessionMiddleware;
use api\middleware\UserModuleAuthMiddleware;
use api\service\Auth\TokenBuilder;
use api\service\LanguageSessionService;
use api\service\User\UpdateUsersService;
use api\service\ValidatorAppService;
use api\V2\Services\ModuleService;
use api\V2\Services\RequestService;
use api\V2\Services\ResponseService;
use api\V2\Validators\Contacts\CarltonContactsSyncValidator;
use api\V2\Validators\Contacts\ContactsBatchValidator;
use api\V2\Validators\Etl\EtlTableNameValidator;
use api\V2\Validators\Etl\EtlTableSyncValidator;
use app\models\framework\config\DatixConfig;
use app\models\generic\InitialsGenerator;
use app\models\user\hydrators\UserHydrator;
use app\models\user\repositories\UserRepository;
use Doctrine\ORM\EntityManagerInterface;
use League\Container\ServiceProvider\AbstractServiceProvider;
use League\Container\ServiceProvider\BootableServiceProviderInterface;
use Psr\Container\ContainerInterface;
use src\system\language\LanguageSession;

/**
 * This service provider registers types that are specific to the Slim API.
 * These definitions are registered for performance reasons.
 */
final class ApiGenericTypeServiceProvider extends AbstractServiceProvider implements BootableServiceProviderInterface
{
    public function boot(): void
    {
        $container = $this->getContainer();

        $container->addShared(ResponseService::class, ResponseService::class);

        $container
            ->addShared(RequestService::class)
            ->addArgument(ModuleService::class)
            ->addArgument(UsersHelper::class);

        $container->addShared(ModuleService::class, ModuleService::class);
    }

    public function provides(string $id): bool
    {
        return in_array($id, [
            UsersHelper::class,
            LanguageSessionService::class,
            TokenBuilder::class,
            ValidatorAppService::class,
            LanguageSessionMiddleware::class,
            UpdateUsersService::class,
            UserModuleAuthMiddleware::class,
            ContactsBatchValidator::class,
            CarltonContactsSyncValidator::class,
            EtlTableSyncValidator::class,
            EtlTableNameValidator::class,
        ], true);
    }

    public function register(): void
    {
        $container = $this->getContainer();

        $container->addShared(UsersHelper::class, UsersHelper::class);

        $container
            ->addShared(LanguageSessionService::class)
            ->addArgument(LanguageSession::class);

        $container
            ->addShared(TokenBuilder::class)
            ->addArgument(DatixConfig::class);

        $container
            ->addShared(ValidatorAppService::class)
            ->addArgument(ContainerInterface::class);

        $container
            ->addShared(LanguageSessionMiddleware::class)
            ->addArgument(LanguageSessionService::class);

        $container
            ->addShared(UpdateUsersService::class)
            ->addArgument(EntityManagerInterface::class)
            ->addArgument(InitialsGenerator::class)
            ->addArgument(UserRepository::class)
            ->addArgument(UserHydrator::class);

        $container
            ->addShared(UserModuleAuthMiddleware::class)
            ->addArgument(RequestService::class);

        $container->addShared(ContactsBatchValidator::class, ContactsBatchValidator::class);

        $container->addShared(CarltonContactsSyncValidator::class, CarltonContactsSyncValidator::class);

        $container->addShared(EtlTableSyncValidator::class, EtlTableSyncValidator::class);

        $container->addShared(EtlTableNameValidator::class, EtlTableNameValidator::class);
    }
}
