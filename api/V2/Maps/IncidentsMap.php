<?php

declare(strict_types=1);

return [
    'module' => [
        'id' => 'incidents_main.recordid',
        'name' => 'incidents_main.inc_name',
        'approvalStatus' => 'incidents_main.rep_approved',
        'ourRef' => 'incidents_main.inc_ourref',
        'notes' => 'incidents_main.inc_notes',
        'submittedTime' => 'incidents_main.inc_submittedtime',
        'dateOpened' => 'incidents_main.inc_dopened',
        'incidentDate' => 'incidents_main.inc_dincident',
        'healthServiceSite' => 'incidents_main.health_service_site',
        'dateTimeReported' => 'incidents_main.inc_dreported',
        'levelOfHarm' => 'incidents_main.inc_level_harm',
        'isNeverEvent' => 'incidents_main.inc_never_event',
        'managerTitle' => 'incidents_main.inc_mgr',
        'actionTaken' => 'incidents_main.inc_actiontaken',
        'incidentTime' => 'incidents_main.inc_time',
        'typeTierZero' => 'incidents_main.inc_affecting_tier_zero',
        'typeTierOne' => 'incidents_main.inc_type_tier_one',
        'typeTierTwo' => 'incidents_main.inc_type_tier_two',
        'typeTierThree' => 'incidents_main.inc_type_tier_three',
        'exactLocation' => 'incidents_main.exact_Location',
        'otherLocation' => 'incidents_main.other_location',
        'location' => 'incidents_main.location_id',
        'service' => 'incidents_main.service_id',
        'otherService' => 'incidents_main.other_service',
        'updatedDate' => 'incidents_main.updateddate',
        'lessonsLearnedDescription' => 'incidents_main.inc_inv_lessons',
        'result' => 'incidents_main.inc_result',
        'severity' => 'incidents_main.inc_severity',
    ],
    'extraFields' => [
        'udf_field_id' => 'udf_values.field_id',
        'udf_field_description' => 'udf_fields_descr.description',
        'udf_string' => 'udf_values.udv_string',
        'udf_number' => 'udf_values.udv_number',
        'udf_date' => 'udf_values.udv_date',
        'udf_money' => 'udf_values.udv_money',
        'udf_text' => 'udf_values.udv_text',
    ],
    'contacts' => [
        'id' => 'contacts_main.recordid',
        'gender' => 'contacts_main.con_gender',
        'age' => 'link_contacts.link_age',
        'linkType' => 'link_contacts.link_type',
        'linkRole' => 'link_contacts.link_role',
    ],
];
