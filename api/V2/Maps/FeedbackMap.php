<?php

return [
    'module' => [
        'id' => 'compl_main.recordid',
        'uuid' => 'compl_main.uuid',
        'sourceOfRecord' => 'compl_main.source_of_record',
        'name' => 'compl_main.com_name',
        'reference' => 'compl_main.com_ourref',
        'approvalStatus' => 'compl_main.rep_approved',
        'handler' => 'compl_main.com_mgr',
        'manager' => 'compl_main.com_head',
        'opened' => 'compl_main.com_dopened',
        'closed' => 'compl_main.com_dclosed',
        'reopened' => 'compl_main.com_dreopened',
        'lastUpdated' => 'compl_main.com_last_updated',
        'method' => 'compl_main.com_method',
        'type' => 'compl_main.com_type',
        'subType' => 'compl_main.com_subtype',
        'incidentType' => 'compl_main.com_inc_type',
        'firstReceived' => 'compl_main.com_dreceived',
        'consentObtained' => 'compl_main.com_consent',
        'incidentDate' => 'compl_main.com_dincident',
        'description' => 'compl_main.com_detail',
        'commissioner' => 'compl_main.com_purchaser',
        'otherReference' => 'compl_main.com_otherref',
        'currentStage' => 'compl_main.com_curstage',
        'outcomeCode' => 'compl_main.com_outcome',
        'outcome' => 'compl_main.com_summary',
        'flagForInvestigation' => 'compl_main.flag_for_investigation',
        'flagForRIB' => 'compl_main.flag_for_rib',
        'requestedConsultant' => 'compl_main.requested_consultant',
        'priorityScale' => 'compl_main.priority_scale',
        'isThisRecordSensitive' => 'compl_main.is_record_sensitive',
        'hROCharacteristics' => 'compl_main.com_hro_characteristics',
        'specialty' => 'compl_main.com_specialty',
        'mCAOrNA' => 'compl_main.mca_or_na',
        'patientExpectations' => 'compl_main.com_pat_expectations',
        'patientUpdatePreference' => 'compl_main.com_pat_update_pref',
        'complaintGradingAtOutcome' => 'compl_main.com_outcome_grading',
        'linkedToASeriousIncident' => 'compl_main.com_serious_incident',
        'communicateThroughWelshLanguage' => 'compl_main.com_welsh_language',
        'escalatedToARedressCase' => 'compl_main.com_redress_escalated',
        'outbreakImpact' => 'compl_main.outbreak_impact',
        'outbreakType' => 'compl_main.outbreak_type',
        'additionalPeopleAffected' => 'compl_main.show_person',
        'referredToTheOmbudsman' => 'compl_main.referred_to_ombudsman',
        'dateEarlySettlementProposalReceived' => 'compl_main.early_settlement_proposal_date_received',
        'dateProposalResponseRequested' => 'compl_main.early_settlement_proposal_date_requested',
        'dateProposalResponseSubmitted' => 'compl_main.early_settlement_proposal_date_submitted',
        'dateOfFirstContact' => 'compl_main.date_first_contact',
        'dateEvidenceDue' => 'compl_main.date_evidence_due',
        'dateEvidenceSubmitted' => 'compl_main.date_evidence_submitted',
        'ombudsmanReference' => 'compl_main.ombudsman_reference',
        'ombudsmanHandler' => 'compl_main.ombudsman_handler',
        'ombudsmanCurrentStage' => 'compl_main.ombudsman_current_stage',
        'ombudsmanEarlySettlementProposal' => 'compl_main.early_settlement_proposal',
        'dateOmbudsmanInvestigationBegan' => 'compl_main.date_investigation_began',
        'dateInvestigationResponseDue' => 'compl_main.date_response_due',
        'dateResponseDocumentsSubmitted' => 'compl_main.date_doc_inv_sub',
        'dateResponseSubmitted' => 'compl_main.date_inv_sub',
        'dateDraftReportReceived' => 'compl_main.date_draft_received',
        'dateDraftReportResponseDue' => 'compl_main.date_draft_response_due',
        'dateDraftRecommendationsReviewed' => 'compl_main.date_rec_recieved',
        'dateDraftRecommendationActionPlanAgreed' => 'compl_main.date_action_plan',
        'dateDraftReportResponseSubmitted' => 'compl_main.date_draft_report_sub',
        'dateFinalReportReceived' => 'compl_main.date_report_received',
        'ombudsmanFinalRepType' => 'compl_main.final_rep_type',
        'ombudsmanOutcome' => 'compl_main.ombudsman_outcome',
        'ombudsmanLearning' => 'compl_main.ombudsman_learning',
        'location' => 'compl_main.location_id',
        'otherLocation' => 'compl_main.other_location',
        'service' => 'compl_main.service_id',
        'otherService' => 'compl_main.other_service',
        'kO41Type' => 'compl_main.com_ko41_type',
        'serviceAreaKO41' => 'compl_main.com_koservarea',
        'subjectKO41' => 'compl_main.com_kosubject',
        'professionKO41' => 'compl_main.com_koprof',
        'patientEthnicityKO41' => 'compl_main.com_koethnic_pat',
        'staffEthnicityKO41' => 'compl_main.com_koethnic_staff',
        'healthSectorISD' => 'compl_main.com_isd_unit',
        'locationISD' => 'compl_main.com_isd_locactual',
        'consentRequiredISD' => 'compl_main.com_isd_consent',
        'dateConsentRequestedISD' => 'compl_main.com_isd_dconsent_req',
        'dateConsentObtainedISD' => 'compl_main.com_isd_dconsent_rec',
        'diversityFormSentISD' => 'compl_main.com_isd_div_sent',
        'refNumberAddedISD' => 'compl_main.com_isd_ref_added',
        'iASSInvolvedISD' => 'compl_main.com_isd_iaas_involved',
        'cASInvolvedISD' => 'compl_main.com_isd_cas_involved',
        'cHINumberISD' => 'compl_main.com_isd_chi_no',
        'responseSentWithin20DaysISD' => 'compl_main.com_isd_resp_sent_20',
        'responseAfter20DaysReasonISD' => 'compl_main.com_isd_resp_20_reason',
        'timescaleOver40DaysAgreedISD' => 'compl_main.com_isd_agree_40',
        'dateOfExtensionLetterISD' => 'compl_main.com_isd_agree_40_date',
        'actionsTakenISD' => 'compl_main.com_isd_actions',
        'lastExportedISD' => 'compl_main.com_isd_dexport',
        'reasonForResponseOver40DaysISD' => 'compl_main.com_isd_resp_40_reason',
        'serviceImprovementPlanISD' => 'compl_main.com_isd_plan',
        'anyLearningsOrOutcomesToShare' => 'compl_main.learnings_to_share',
        'learningTitle' => 'compl_main.learnings_title',
        'keyLearningsAndOutcomes' => 'compl_main.key_learnings',
        'investigators' => 'compl_main.com_investigator',
        'investigationDateStarted' => 'compl_main.com_inv_dstart',
        'investigationDateCompleted' => 'compl_main.com_inv_dcomp',
        'consequence' => 'compl_main.com_consequence',
        'likelihoodOfRecurrence' => 'compl_main.com_likelihood',
        'grade' => 'compl_main.com_grade',
        'outcomeOfInvestigation' => 'compl_main.com_inv_outcome',
        'actionTakenCodes' => 'compl_main.com_action_code',
        'actionTaken' => 'compl_main.com_inv_action',
        'lessonsLearned' => 'compl_main.com_inv_lessons',
        'lessonsCodes' => 'compl_main.com_lessons_code',
        'requestReceived' => 'compl_main.com_drequest',
        'requestAcknowledgedDue' => 'compl_main.com_ddueackreq',
        'requestAcknowledgedDone' => 'compl_main.com_dackreq',
        'statementReceived' => 'compl_main.com_dstatement',
        'decisionMadeDue' => 'compl_main.com_dduedecision',
        'decisionMadeDone' => 'compl_main.com_ddecision',
        'iRRecommended' => 'compl_main.com_recir',
        'personProvidingFeedbackInformed' => 'compl_main.com_dinform',
        'panelAppointedDue' => 'compl_main.com_dduepappt',
        'panelAppointedDone' => 'compl_main.com_dpappt',
        'draftReportPublishedDue' => 'compl_main.com_dduepdraft',
        'draftReportPublishedDone' => 'compl_main.com_dpdraft',
        'finalReportPublishedDue' => 'compl_main.com_ddueppublish',
        'finalReportPublishedDone' => 'compl_main.com_dppublish',
        'cERepliedToPersonProvidingFeedbackDue' => 'compl_main.com_dduecereply',
        'cERepliedToPersonProvidingFeedbackDone' => 'compl_main.com_dcereply',
        'layChairAppointedDue' => 'compl_main.com_dduelaychair',
        'layChairAppointedDone' => 'compl_main.com_dlaychair',
        'iROutcomeCode' => 'compl_main.com_ircode',
        'clinicalAssessorNeeded' => 'compl_main.com_assessor',
        'synopsis' => 'compl_main.com_irsynopsis',
        'incidentTypeTierOne' => 'compl_main.com_type_tier_one',
        'incidentTypeTierTwo' => 'compl_main.com_type_tier_two',
        'incidentTypeTierThree' => 'compl_main.com_type_tier_three',
        'incidentAffecting' => 'compl_main.com_affecting_tier_zero',
        'issuesAdded' => 'compl_main.com_issues_linked',
        'subjectsAdded' => 'compl_main.com_subjects_linked',
        'lessonLearnedSubCategory' => 'compl_main.com_lesson_learned_sub_category',
        'riskRating' => 'compl_main.com_grade_rating',
        'anyDocumentsAttached' => 'compl_main.show_document',
    ],
    'extraFields' => [
        'udf_field_id' => 'udf_values.field_id',
        'udf_string' => 'udf_values.udv_string',
        'udf_number' => 'udf_values.udv_number',
        'udf_date' => 'udf_values.udv_date',
        'udf_money' => 'udf_values.udv_money',
        'udf_text' => 'udf_values.udv_text',
    ],
    'subjects' => [
        'id' => 'compl_subjects.recordid',
        'subject' => 'compl_subjects.com_subject',
        'subSubject' => 'compl_subjects.com_subsubject',
        'staffType' => 'compl_subjects.com_stafftype',
        'outcomeCode' => 'compl_subjects.com_outcome',
        'completedDate' => 'compl_subjects.csu_dcompleted',
        'subjectNotes' => 'compl_subjects.csu_notes',
        'serviceArea' => 'compl_subjects.com_service_area',
        'location' => 'compl_subjects.csu_location_id',
        'service' => 'compl_subjects.csu_service_id',
        'subType' => 'compl_subjects.com_subtype',
        'type' => 'compl_subjects.com_type',
        'levelOfHarm' => 'compl_subjects.csu_level_of_harm',
        'issuePathway' => 'compl_subjects.com_issue_pathway',
        'issueType' => 'compl_subjects.com_issue_type',
        'order' => 'compl_subjects.listorder',
    ],
    'documents' => [
        'id' => 'link_documents.id',
        'description' => 'link_documents.description',
        'type' => 'link_documents.type',
        'uploadedBy' => 'link_documents.user_id',
        'dateUploaded' => 'link_documents.link_date',
        'documentId' => 'link_documents.document_id',
    ],
    'progressNotes' => [
        'id' => 'progress_notes.recordid',
        'note' => 'progress_notes.pno_progress_notes',
        'created' => 'progress_notes.pno_createddate',
        'createdBy' => 'progress_notes.pno_createdby',
        'updated' => 'progress_notes.pno_updateddate',
        'updatedBy' => 'progress_notes.pno_updatedby',
    ],
];
