<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use src\system\database\code\Code;
use src\system\database\CodeFieldInterface;

class CodeFieldFormatter implements FieldFormatterInterface
{
    private CodeFieldInterface $field;

    public function setField($field): self
    {
        $this->field = $field;

        return $this;
    }

    /**
     * @param string $data The data to be formatted
     *
     * @return array|string|null
     */
    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        $codeData = $this->field->getCodes()[$data];

        if ($codeData instanceof Code) {
            return [
                'value' => $codeData->getCode(),
                'description' => $codeData->getDescription(),
            ];
        }

        // If there's no code data to use, just return the code as stored in the database
        return (string) $data;
    }

    public function extractValue($data): ?string
    {
        if ($data === null || $data === '') {
            return null;
        }

        if (isset($data['value'])) {
            return (string) $data['value'];
        }

        return strtoupper((string) $data);
    }
}
