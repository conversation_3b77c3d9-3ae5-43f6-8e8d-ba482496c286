<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use src\system\database\code\Code;
use src\system\database\CodeFieldInterface;

class MultiCodeFieldFormatter implements FieldFormatterInterface
{
    private CodeFieldInterface $field;

    public function setField(CodeFieldInterface $field): self
    {
        $this->field = $field;

        return $this;
    }

    /**
     * @param string $data The data to be formatted
     *
     * @return array|string|null If there's nothing to return, it'll return null or empty sting, if there's matching
     *                           codes, it'll return an array, if there's matching codes it'll return the string from the db
     */
    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        $formattedCodes = [];

        $codes = explode(' ', (string) $data);

        foreach ($codes as $code) {
            $codeData = $this->field->getCodes()[$code];

            if ($codeData instanceof Code) {
                $formattedCodes[] = [
                    'value' => $codeData->getCode(),
                    'description' => $codeData->getDescription(),
                ];
            } else {
                // If there's no code data to use, just return the code as stored in the database
                $formattedCodes[] = (string) $code;
            }
        }

        return $formattedCodes;
    }

    public function extractValue($data): ?string
    {
        if ($data === null || $data === '') {
            return null;
        }

        return implode(' ', array_map(static function ($code) {
            return strtoupper((string) ($code['value'] ?? $code));
        }, $data));
    }
}
