<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use app\models\user\mappers\UserMapper;
use Doctrine\ORM\EntityManagerInterface;
use src\system\database\CodeFieldInterface;
use Throwable;

class StaffFieldFormatter implements FieldFormatterInterface
{
    use StaffFieldTrait;
    private CodeFieldInterface $field;
    private UserMapper $userMapper;
    private EntityManagerInterface $entityManager;

    public function __construct(UserMapper $userMapper, EntityManagerInterface $entityManager)
    {
        $this->userMapper = $userMapper;
        $this->entityManager = $entityManager;
    }

    public function setField($field): self
    {
        $this->field = $field;

        return $this;
    }

    /**
     * @param string $data The data to be formatted
     *
     * @return array|string|null
     *
     * @throws Throwable
     */
    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        return $this->getFormattedStaffArray($data);
    }

    public function extractValue($data)
    {
        if ($data === null || $data === '') {
            return null;
        }

        return $this->getExtractedStaffData($data);
    }
}
