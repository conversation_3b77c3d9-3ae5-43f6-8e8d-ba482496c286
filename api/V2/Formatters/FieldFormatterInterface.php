<?php

declare(strict_types=1);

namespace api\V2\Formatters;

interface FieldFormatterInterface
{
    /**
     * Formats data from the database to be outputted in the response.
     *
     * @param mixed $data The data to be formatted
     *
     * @return mixed
     */
    public function format($data);

    /**
     * Formats data from the request to be used to update the database.
     *
     * @param mixed $data
     *
     * @return mixed
     */
    public function extractValue($data);
}
