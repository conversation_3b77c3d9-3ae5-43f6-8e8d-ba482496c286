<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use app\models\user\mappers\UserMapper;
use Doctrine\ORM\EntityManagerInterface;
use src\system\database\CodeFieldInterface;
use Throwable;

use function is_array;

class MultiCodeStaffFieldFormatter implements FieldFormatterInterface
{
    use StaffFieldTrait;
    private CodeFieldInterface $field;
    private UserMapper $userMapper;
    private EntityManagerInterface $entityManager;

    public function __construct(UserMapper $userMapper, EntityManagerInterface $entityManager)
    {
        $this->userMapper = $userMapper;
        $this->entityManager = $entityManager;
    }

    public function setField(CodeFieldInterface $field): self
    {
        $this->field = $field;

        return $this;
    }

    /**
     * @param string $data The data to be formatted
     *
     * @return array|string|null If there's nothing to return, it'll return null or empty sting, if there's matching
     *                           codes, it'll return an array, if there's matching codes it'll return the string from the db
     */
    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        $formattedCodes = [];

        $codes = explode(' ', $data);

        foreach ($codes as $code) {
            $formattedStaffArray = $this->getFormattedStaffArray($code);

            if ($formattedStaffArray) {
                $formattedCodes[] = $formattedStaffArray;
            }
        }

        return $formattedCodes;
    }

    /**
     * @param $data
     *
     * @throws Throwable
     */
    public function extractValue($data): ?string
    {
        if ($data === null) {
            return null;
        }

        if (!is_array($data)) {
            return null;
        }

        // Convert an array of data into a space separated string of user initials based on the $data sent in
        return implode(' ', array_map(function ($userDetails) {
            if ($userDetails === '') {
                return null;
            }

            return $this->getExtractedStaffData($userDetails);
        }, $data));
    }
}
