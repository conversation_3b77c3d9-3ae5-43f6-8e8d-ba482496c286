<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use app\models\treeFields\TreeFieldCodeRetriever;

class TreeFieldFormatter implements FieldFormatterInterface
{
    private TreeFieldCodeRetriever $treeFieldCodeRetriever;

    public function setTreeFieldCodeRetriever(TreeFieldCodeRetriever $treeFieldCodeRetriever): self
    {
        $this->treeFieldCodeRetriever = $treeFieldCodeRetriever;

        return $this;
    }

    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        return [
            'id' => (int) $data,
            'location' => $this->treeFieldCodeRetriever->getBreadcrumb((int) $data),
            'path' => $this->treeFieldCodeRetriever->getTreeFieldPath((int) $data),
        ];
    }

    public function extractValue($data): ?int
    {
        if ($data === null || $data === '') {
            return null;
        }

        if (isset($data['id'])) {
            return (int) $data['id'];
        }

        return (int) $data;
    }
}
