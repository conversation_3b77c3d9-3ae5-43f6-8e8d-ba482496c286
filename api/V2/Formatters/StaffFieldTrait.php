<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use app\models\user\entities\UserEntity;
use Exception;
use src\system\database\code\Code;
use Teapot\StatusCode\Http;

trait StaffFieldTrait
{
    private function getFormattedStaffArray($data): ?array
    {
        $codeData = $this->field->getCodes()[$data];

        if ($codeData instanceof Code) {
            // If the code field stores initials, convert to a user ID, otherwise just cast to int
            $userId = (int) ($this->field->getCodeColumn() === 'initials' ? $this->userMapper->getUserIdByInitials($codeData->getCode()) : $codeData->getCode());

            return [
                'id' => $userId,
                'name' => $codeData->getDescription(),
            ];
        }

        // If there's no user data to use, return nothing
        return null;
    }

    private function getExtractedStaffData($data)
    {
        if (isset($data['id'])) {
            $userId = (int) $data['id'];
        } else {
            $userId = (int) $data;
        }

        $user = $this->entityManager->find(UserEntity::class, $userId);

        if (!$user instanceof UserEntity) {
            throw new Exception('No user was found with the ID ' . ($data['id'] ?? $data), Http::BAD_REQUEST);
        }

        if ($this->field->getCodeColumn() === 'initials') {
            return $user->getInitials();
        }

        return $userId;
    }
}
