<?php

declare(strict_types=1);

namespace api\V2\Formatters;

class TextFieldFormatter implements FieldFormatterInterface
{
    public function format($data): ?string
    {
        if ($data === null || $data === '') {
            return $data;
        }

        return (string) $data;
    }

    public function extractValue($data): ?string
    {
        if ($data === null || $data === '') {
            return null;
        }

        return (string) $data;
    }
}
