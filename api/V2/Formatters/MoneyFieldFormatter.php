<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use const FILTER_SANITIZE_NUMBER_FLOAT;
use const FILTER_FLAG_ALLOW_FRACTION;

class MoneyFieldFormatter implements FieldFormatterInterface
{
    public function format($data): ?string
    {
        if ($data === null || $data === '') {
            return $data;
        }

        return $this->formatForCurrency($data);
    }

    public function extractValue($data): ?string
    {
        if ($data === null || $data === '') {
            return null;
        }

        return $this->formatForCurrency($data);
    }

    private function formatForCurrency($data): string
    {
        $data = (float) filter_var($data, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);

        return number_format($data, 2, '.', '');
    }
}
