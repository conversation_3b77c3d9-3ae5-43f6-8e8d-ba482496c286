<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use DateTime;
use Exception;

class DateFieldFormatter implements FieldFormatterInterface
{
    public function format($data): ?string
    {
        if ($data === null || $data === '') {
            return $data;
        }

        try {
            return (new DateTime($data))->format('Y-m-d');
        } catch (Exception $exception) {
            // If the date being formatted isn't valid for use with the DateTime object, just return the data sent in
            return (string) $data;
        }
    }

    public function extractValue($data): ?string
    {
        if ($data === null || $data === '') {
            return null;
        }

        return (string) $data;
    }
}
