<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use api\V2\Services\FieldDefsService;
use app\models\treeFields\TreeFieldCodeRetrieverFactory;
use src\system\database\CodeFieldInterface;
use src\system\database\field\TreeField;
use src\system\database\FieldInterface;
use src\users\model\field\StaffField;

use function get_class;

class FieldFormatterFactory
{
    private TreeFieldCodeRetrieverFactory $treeFieldCodeRetrieverFactory;
    private FieldDefsService $fieldDefsService;
    private array $formatterCache = [];

    public function __construct(FieldDefsService $fieldDefsService, TreeFieldCodeRetrieverFactory $treeFieldCodeRetrieverFactory)
    {
        $this->fieldDefsService = $fieldDefsService;
        $this->treeFieldCodeRetrieverFactory = $treeFieldCodeRetrieverFactory;
    }

    /**
     * @param CodeFieldInterface|FieldInterface $field A field object to use for determining what formatting to use
     */
    public function getFormatter(FieldInterface $field): ?FieldFormatterInterface
    {
        // Staff Field types are either code or multicode, so need some way to specifically identify them from standard
        // code fields as they have their own formatters
        if ($field instanceof StaffField) {
            $cacheKey = get_class($field);
        } else {
            $cacheKey = $field->getType();
        }

        if (!$this->formatterCache[$cacheKey]) {
            $this->formatterCache[$cacheKey] = $field->getApiFieldFormatter();
        }

        /** @var FieldFormatterInterface $fieldFormatter */
        $fieldFormatter = $this->formatterCache[$cacheKey];

        // If the field type is a CodeField then the specific field data needs setting against the formatter
        // For Tree Fields the mapper type needs retrieving and then setting against the formatter
        if ($field instanceof CodeFieldInterface) {
            $fieldFormatter->setField($field);
        } elseif ($field instanceof TreeField) {
            $fieldDefinition = $this->fieldDefsService->getFieldDefinition($field);
            $treeFieldCodeRetriever = $this->treeFieldCodeRetrieverFactory->create($fieldDefinition['mapperType']);
            $fieldFormatter->setTreeFieldCodeRetriever($treeFieldCodeRetriever);
        }

        return $fieldFormatter;
    }
}
