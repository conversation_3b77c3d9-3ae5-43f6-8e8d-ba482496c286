<?php

declare(strict_types=1);

namespace api\V2\Formatters;

use function is_float;
use function is_string;

class NumberFieldFormatter implements FieldFormatterInterface
{
    public function format($data)
    {
        if ($data === null || $data === '') {
            return $data;
        }

        if (is_float($data)) {
            return $data;
        }

        if (is_string($data) && preg_match('/^-?\d+\.\d+/', $data) === 1) {
            return (float) $data;
        }

        return (int) $data;
    }

    public function extractValue($data): ?int
    {
        if ($data === null || $data === '') {
            return null;
        }

        return (int) $data;
    }
}
