<?php

declare(strict_types=1);

namespace api\V2\ValueObjects;

class UdfData
{
    private array $data = [];

    /**
     * @param int $id The ID of the udf field eg. 123
     * @param mixed $value The value to set for the UDF, could be any data type
     */
    public function __construct(
        int $id,
        $value
    ) {
        $this->data['udfFieldId'] = $id;
        $this->data['udfValue'] = $value;
    }

    public function getData(): array
    {
        return $this->data;
    }
}
