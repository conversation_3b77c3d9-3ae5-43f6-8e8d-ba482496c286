<?php

declare(strict_types=1);

namespace api\V2\ValueObjects;

class DocumentsData
{
    private array $data = [];

    public function addDocumentToUpload(
        string $userInitials,
        string $file,
        string $contentType,
        string $type,
        string $description
    ): void {
        $this->data = [
            'uploader-initials' => $userInitials,
            'body' => $file,
            'content-type' => $contentType,
            'type' => $type,
            'description' => $description,
        ];
    }

    public function addDocumentToUpdate(
        int $documentId,
        string $type,
        string $description
    ): void {
        $this->data = [
            'id' => $documentId,
            'type' => $type,
            'description' => $description,
        ];
    }

    public function getData(): array
    {
        return $this->data;
    }
}
