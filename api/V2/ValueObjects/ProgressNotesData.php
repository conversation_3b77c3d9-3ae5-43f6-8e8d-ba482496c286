<?php

declare(strict_types=1);

namespace api\V2\ValueObjects;

class ProgressNotesData implements EntityDataInterface
{
    private array $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function addNewRecord(int $id, $mainEntity, string $now, int $userId): void
    {
        $this->data['recordid'] = $id;
        $this->data['feedback'] = $mainEntity;
        $this->data['created'] = $now;
        $this->data['createdBy'] = $userId;
        $this->data['updated'] = $now;
        $this->data['updatedBy'] = $userId;
    }

    public function addExistingRecord(string $now, int $userId): void
    {
        $this->data['updated'] = $now;
        $this->data['updatedBy'] = $userId;
    }

    public function getData(): array
    {
        return $this->data;
    }
}
