<?php

declare(strict_types=1);

namespace api\middleware;

use api\interfaces\validator\ValidatorInterface;
use api\V2\Services\ResponseService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface;
use Teapot\StatusCode\Http;

class ValidatorMiddleware
{
    private ValidatorInterface $validator;
    private ResponseService $responseService;

    public function __construct(ValidatorInterface $validator, ResponseService $responseService)
    {
        $this->validator = $validator;
        $this->responseService = $responseService;
    }

    /**
     * @throws ContainerExceptionInterface
     */
    public function __invoke(Request $request, RequestHandlerInterface $next): Response
    {
        if (!$this->validator->validate($request)) {
            $statusCode = Http::BAD_REQUEST;

            return $this->responseService->formatErrorResponse(
                new \GuzzleHttp\Psr7\Response(),
                $statusCode,
                'Failed Validation',
                ['validation_messages' => $this->validator->getMessages()],
            );
        }

        return $next->handle($request);
    }
}
