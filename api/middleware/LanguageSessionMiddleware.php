<?php

declare(strict_types=1);

namespace api\middleware;

use api\service\LanguageSessionService;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface;

class LanguageSessionMiddleware
{
    private LanguageSessionService $languageSessionService;

    public function __construct(LanguageSessionService $languageSessionService)
    {
        $this->languageSessionService = $languageSessionService;
    }

    public function __invoke(Request $request, RequestHandlerInterface $next): Response
    {
        $this->languageSessionService->refresh();
        $this->languageSessionService->setLanguage($this->languageSessionService->getLocale());

        return $next->handle($request);
    }
}
