<?php

declare(strict_types=1);

namespace api\middleware;

use api\V2\Services\RequestService;
use app\Exceptions\Api\AuthorisationError;
use Psr\Container\ContainerExceptionInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface;
use src\users\model\User;
use Teapot\StatusCode\Http;

class UserModuleAuthMiddleware
{
    private RequestService $requestService;

    public function __construct(RequestService $requestService)
    {
        $this->requestService = $requestService;
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws AuthorisationError
     */
    public function __invoke(Request $request, RequestHandlerInterface $next): Response
    {
        $user = $this->requestService->extractUserFromRequest($request);

        if (!$user instanceof User) {
            throw new AuthorisationError('Authentication failure: Decoded JWT is empty', Http::UNAUTHORIZED);
        }

        $moduleCode = $this->requestService->getModuleCodeFromRequest($request);

        if (empty($moduleCode) || !$user->canSeeModule($moduleCode)) {
            throw new AuthorisationError('User does not have access to the module specified in the request', Http::FORBIDDEN);
        }

        return $next->handle($request);
    }
}
