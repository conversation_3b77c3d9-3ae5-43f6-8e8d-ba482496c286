<?php

declare(strict_types=1);

namespace api\middleware;

use api\service\MaintenanceModeApiService;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Http\Server\RequestHandlerInterface;
use Teapot\StatusCode\Http;

class MaintenanceModeMiddleware
{
    private MaintenanceModeApiService $service;

    public function __construct(MaintenanceModeApiService $service)
    {
        $this->service = $service;
    }

    public function __invoke(Request $request, RequestHandlerInterface $next): Response
    {
        if (!$this->service->shouldBlock($request)) {
            return $next->handle($request);
        }

        $response = new \GuzzleHttp\Psr7\Response();
        $response->getBody()->write(json_encode([
            'message' => 'DCIQ is in maintenance mode',
        ]));

        return $response->withStatus(Http::SERVICE_UNAVAILABLE)
            ->withHeader('Retry-After', '120')
            ->withHeader('Content-Type', 'application/json');
    }
}
