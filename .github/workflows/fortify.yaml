name: Fortify SAST with fcli

on:
  workflow_dispatch:

jobs:
  fortify-fcli-scan:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Write Fortify license file
        run: |
          mkdir -p ~/.fortify
          echo "${{ secrets.FORTIFY_LICENSE_BASE64 }}" | base64 -d > ~/.fortify/fortify.license

      - name: Create ScanCentral package
        run: |
          docker run --rm \
            -v "${PWD}:/src" \
            -v "${HOME}/.fortify:/root/.fortify:ro" \
            -w /src \
            fortifydocker/fcli:latest \
            fcli sc-sast package \
              --source . \
              --output my-sast.fpr

      - name: Generate HTML report from FPR
        run: |
          docker run --rm \
            -v "${PWD}:/src" \
            -v "${HOME}/.fortify:/root/.fortify:ro" \
            -w /src \
            fortifydocker/fcli:latest \
            fcli sc-sast report generate \
              --source my-sast.fpr \
              --format html \
              --outfile fortify-report.html

      - name: Upload Fortify report
        uses: actions/upload-artifact@v4
        with:
          name: fortify-report
          path: fortify-report.html
