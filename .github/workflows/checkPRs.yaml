name: Check Merge Conflicts

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  check-conflicts:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: develop
          fetch-depth: 0

      - name: Create a dummy branch
        run: |
          git checkout -b conflict-check-branch
          git push origin conflict-check-branch --force

      - name: Get open pull requests
        id: get-prs
        run: |
          prs=$(gh pr list --state open --json number --jq '.[].number')
          prs=$(echo "$prs" | tr '\n' ' ')  # Ensure space-separated list
          echo "prs=$prs" >> $GITHUB_ENV
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Configure Git Identity
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Check Merge Conflicts"

      - name: Check PRs for conflicts among each other
        run: |
          for pr1 in $prs; do
            for pr2 in $prs; do
              if [ "$pr1" -ne "$pr2" ]; then
                echo "🔍 Checking merge conflict between PR #$pr1 and PR #$pr2..."

                git checkout conflict-check-branch
                git reset --hard origin/develop
                git merge --no-commit --no-ff pr-$pr1 || continue

                merge_status=$(git merge --no-commit --no-ff pr-$pr2 2>&1)

                if [ $? -eq 0 ]; then
                  echo "✅ PR #$pr1 and PR #$pr2 can merge together."
                  comment="✅ **Check Merge Conflicts**<br><br>PR #$pr1 and PR #$pr2 can be merged together with no conflicts. 🚀"
                else
                  echo "❌ PR #$pr1 and PR #$pr2 have conflicts!"
                  comment="⚠️ **Check Merge Conflicts**<br><br>PR #$pr1 has merge conflicts with PR #$pr2. Please resolve before merging."
                fi

                echo "comment<<EOF" >> $GITHUB_ENV
                echo "$comment" >> $GITHUB_ENV
                echo "EOF" >> $GITHUB_ENV

                - name: Find existing comment for PR1 merge conflict
                  uses: peter-evans/find-comment@v1
                  id: fc-pr1
                  with:
                    issue-number: $pr1
                    comment-author: "github-actions[bot]"
                    body-includes: "Check Merge Conflicts"

                - name: Create comment for PR1 merge conflict
                  if: steps.fc-pr1.outputs.comment-id == ''
                  uses: peter-evans/create-or-update-comment@v1
                  with:
                    issue-number: $pr1
                    body: |
                      ${{ env.comment }}

                - name: Update comment for PR1 merge conflict
                  if: steps.fc-pr1.outputs.comment-id != ''
                  uses: peter-evans/create-or-update-comment@v1
                  with:
                    comment-id: ${{ steps.fc-pr1.outputs.comment-id }}
                    body: |
                      ${{ env.comment }}
                    edit-mode: replace

                - name: Find existing comment for PR2 merge conflict
                  uses: peter-evans/find-comment@v1
                  id: fc-pr2
                  with:
                    issue-number: $pr2
                    comment-author: "github-actions[bot]"
                    body-includes: "Check Merge Conflicts"

                - name: Create comment for PR2 merge conflict
                  if: steps.fc-pr2.outputs.comment-id == ''
                  uses: peter-evans/create-or-update-comment@v1
                  with:
                    issue-number: $pr2
                    body: |
                      ${{ env.comment }}

                - name: Update comment for PR2 merge conflict
                  if: steps.fc-pr2.outputs.comment-id != ''
                  uses: peter-evans/create-or-update-comment@v1
                  with:
                    comment-id: ${{ steps.fc-pr2.outputs.comment-id }}
                    body: |
                      ${{ env.comment }}
                    edit-mode: replace

                git merge --abort
              fi
            done
          done
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete dummy branch
        if: always()
        run: |
          git push origin --delete conflict-check-branch || true
