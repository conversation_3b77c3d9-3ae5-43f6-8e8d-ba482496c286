name: Build-Push-DockerScout

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [develop, release-candidate, main]
  push:
    branches: [develop, release-candidate, main]
    workflow_dispatch:

permissions:
  contents: write
  id-token: write
  pull-requests: write

jobs:
  bump-version:
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.bump.outputs.new_tag }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.DCIQ_PAT }}

      - name: Bump Version
        id: bump
        uses: rld-devops/reusable-github-actions/dciq/bump-semver@feature/reusable-build-push-dockerscout
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

  Build-Push:
    needs: bump-version
    runs-on: ubuntu-latest
    timeout-minutes: 20

    strategy:
      matrix:
        service:
          - name: capture
            context: .
            target: prod
            php: true

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.DCIQ_PAT }}

      - name: Build and Push Docker Images with DockerScout
        uses: rld-devops/reusable-github-actions/dciq/build-push-scout@feature/reusable-build-push-dockerscout
        with:
          project_name: dciq
          aws_account_id: "************"
          aws_region: "eu-west-2"
          dockerhub_username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub_token: ${{ secrets.DOCKERHUB_ACCESS_TOKEN }}
          scout_username: ${{ secrets.SCOUT_USERNAME }}
          scout_token: ${{ secrets.SCOUT_TOKEN }}
          dciq_pat: ${{ secrets.DCIQ_PAT }}
          images: ${{ toJson(matrix.service) }}
          image_tag: ${{ needs.bump-version.outputs.new_tag }}

  mergedown-RC-develop:
    needs: Build-Push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/release-candidate'
    steps:
      - name: Merge release-candidate to develop
        uses: rld-devops/reusable-github-actions/dciq/mergedown@feature/reusable-build-push-dockerscout
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
  gitposeidon-update:
    needs: Build-Push
    runs-on: ubuntu-latest
    if: >
      (github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true &&
      (github.event.pull_request.base.ref == 'develop' || github.event.pull_request.base.ref == 'release-candidate' || github.event.pull_request.base.ref == 'main')) ||
      (github.event_name == 'push' && (github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/release-candidate' || github.ref == 'refs/heads/main'))
    steps:
      - name: Update Poseidon Template
        uses: rld-devops/reusable-github-actions/dciq/poseidon@feature/reusable-build-push-dockerscout
        with:
          git_sha: ${{ github.sha }}
          ssh_private_key: ${{ secrets.BITBUCKET_SSH_KEY }}
          template_map: |
            capture=templates/deployments/prince.tmpl
            capture=templates/migrations/princemigrate.tmpl