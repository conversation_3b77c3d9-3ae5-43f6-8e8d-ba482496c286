name: pr-commit-message-enforcer-and-linker

on:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [develop, release-candidate, main]

jobs:
  pr-commit-message-enforcer-and-linker:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
    - uses: actions/checkout@v4
    - name: Azure DevOps Commit Validator and Pull Request Linker
      uses: josh<PERSON>hanning/azdo_commit_message_validator@v2
      with:
        check-pull-request: true
        check-commits: false
        azure-devops-organization: rldatix
        azure-devops-token: ${{ secrets.AZURE_DEVOPS_PAT }}