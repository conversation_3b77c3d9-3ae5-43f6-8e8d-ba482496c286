<?php

use app\services\securityGroups\SecurityGroupService;
use src\contacts\model\ContactModelFactory;
use src\framework\query\FieldCollection;
use src\framework\query\SqlWriter;
use src\framework\query\Where;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

/**
 * @deprecated
 *
 * @see SecurityGroupService::getUserSecurityGroups()
 *
 * returns an array of security group IDs the user is a member of
 * join all tables to select only valid user-groups links.
 *
 * $grp_type = 0 is a bitwise value representing all types of group.
 *
 * Current possible values or combination of values are:
 * GRP_TYPE_ACCESS == Record access
 * GRP_TYPE_EMAIL == E-mail notification
 * GRP_TYPE_ACCESS | GRP_TYPE_EMAIL == Record access and e-mail notification
 */
function GetUserSecurityGroups($con_id, $grp_type = 0): array
{
    return Container::get(SecurityGroupService::class)->getUserSecurityGroups($con_id, $grp_type);
}

// return an array of contacts IDs of the members of the groups
// which IDs have been passed as an array to the function.
// NOTE: This also picks up users in profiles which contain this group.
function GetSecurityGroupUsers($grp_id)
{
    $contacts = [];
    $groups = [];

    if (is_array($grp_id) && !empty($grp_id)) {
        $groups = $grp_id;
    } elseif ($grp_id) {
        $groups[] = $grp_id;
    } else {
        return null;
    }

    $where = new Where();
    $where->add((new FieldCollection())->field('sec_staff_group.grp_id')->in($groups));
    $where->add((new FieldCollection())->field('users_main.use_email')->notNull());

    $query = (new ContactModelFactory())->getQueryFactory()->getQuery();
    $query->overrideSecurity();
    $query->select(['users_main.recordid'])
        ->join('sec_staff_group', null, 'INNER', 'users_main.recordid = sec_staff_group.use_id');
    $query->where($where)
        ->orderBy(['users_main.recordid']);

    [$statement, $parameters] = Container::get(SqlWriter::class)->writeStatement($query);
    $contactIdsFromGroups = DatixDBQuery::PDO_fetch_all($statement, $parameters, PDO::FETCH_COLUMN);

    $where = new Where();
    $where->add((new FieldCollection())->field('link_profile_group.lpg_group')->in($groups));
    $where->add((new FieldCollection())->field('users_main.use_email')->notNull());

    $query = (new ContactModelFactory())->getQueryFactory()->getQuery();
    $query->overrideSecurity();
    $query->select(['users_main.recordid'])
        ->join('link_profile_group', null, 'INNER', 'users_main.sta_profile = link_profile_group.lpg_profile');
    $query->where($where)
        ->orderBy(['users_main.recordid']);

    [$statement, $parameters] = Container::get(SqlWriter::class)->writeStatement($query);
    $contactIdsFromProfiles = DatixDBQuery::PDO_fetch_all($statement, $parameters, PDO::FETCH_COLUMN);

    return array_merge($contactIdsFromGroups, $contactIdsFromProfiles);
}

function sectionSecurityGroups($staff, $FormAction)
{
    sectionGenericSecurityGroups('user', $staff, $FormAction);
}

function sectionProfileSecurityGroups($staff, $FormAction)
{
    sectionGenericSecurityGroups('profile', $staff, $FormAction);
}

function sectionGenericSecurityGroups($type, $staff, $FormAction)
{
    global $dtxdebug, $ModuleDefs, $scripturl;

    // set up different strings for different places this will appear
    if ($type == 'profile') {
        if ($staff['recordid']) {
            $sql = "SELECT
            link_profile_group.recordid,
            sec_groups.grp_code, sec_groups.grp_description,
            link_profile_group.lpg_group, link_profile_group.lpg_profile, sec_groups.grp_type
            from sec_groups
            join link_profile_group on sec_groups.recordid = link_profile_group.lpg_group
            WHERE link_profile_group.lpg_profile = {$staff['recordid']}";

            $resultArray = DatixDBQuery::PDO_fetch_all($sql);

            $num_rows = count($resultArray);
        } else {
            $num_rows = 0;
        }

        $linkText['add'] = ($num_rows > 0 ? _fdtk('add_another_security_group_to_this_profile') : _fdtk('add_a_security_group_to_this_profile'));
        $linkText['add_one'] = _fdtk('add_a_security_group_to_this_profile');
        $linkText['add_many'] = _fdtk('add_another_security_group_to_this_profile');
        $linkText['addlink'] = 'add_group_for_profile';
        $linkText['remove'] = _fdtk('profile_remove_security_group_from_profile');
        $linkText['removealert'] = _fdtk('profile_remove_security_group_from_profile_alert');
    } else {
        if ($staff['recordid']) {
            $sql = 'SELECT
            sec_staff_group.recordid,
            sec_groups.grp_code, sec_groups.grp_description,
            sec_staff_group.grp_id, sec_staff_group.use_id, sec_groups.grp_type
            from sec_groups
            join sec_staff_group on sec_groups.recordid = sec_staff_group.grp_id
            WHERE sec_staff_group.use_id = :use_id';

            $resultArray = DatixDBQuery::PDO_fetch_all($sql, ['use_id' => $staff['recordid']]);

            $num_rows = count($resultArray);
        } else {
            $num_rows = 0;
        }

        $linkText['add'] = 'Add this user to ' . ($num_rows > 0 ? 'another' : 'a') . ' security group';
        $linkText['add_one'] = 'Add this user to a security group';
        $linkText['add_many'] = 'Add this user to another security group';
        $linkText['addlink'] = 'add_group_for_user';
        $linkText['remove'] = 'Remove user from selected security group(s)';
        $linkText['removealert'] = 'Remove user from selected security group(s)?';
    } ?>
    <script language="javascript">
        function SelectAllGroups(checked)
        {
            var groups = document.getElementsByName('cbSelectGroup');
            for (var i = 0; i < groups.length; i++)
            {
                groups[i].checked = checked;
            }
        }

        function CheckSelectAll()
        {
            var checked = true;
            var groups = document.getElementsByName('cbSelectGroup');
            if (groups.length == 0)
            {
                checked = false;
            }
            else
            {
                for (var i = 0; i < groups.length; i++)
                {
                    if (groups[i].checked == false)
                    {
                        checked = false;
                        break;
                    }
                }
            }
            document.getElementById('cbSelectAllGroups').checked = checked;
        }

        function UnlinkSelectedUserGroups()
        {
            var urlGroups = '&type=<?php echo $type; ?>';
            var groups = document.getElementsByName('cbSelectGroup');
            for (var i = 0; i < groups.length; i++)
            {
                if (groups[i].checked == true)
                {
                    urlGroups += '&link_id[]=' + groups[i].value;
                }
            }

            if (urlGroups != '')
            {
                urlGroups = urlGroups.substr(1);
                if (confirm('<?php echo $linkText['removealert']; ?>') == false)
                {
                    return;
                }
            }
            else
            {
                alert('No group selected.');
            }

            jQuery.ajax({
                url: scripturl + '?action=httprequest&type=unlink_user_groups',
                type: "POST",
                data: urlGroups,
                dataType: 'text',
                async: true,
                success: function(response) {
                    var groups = document.getElementsByName('cbSelectGroup');
                    if (groups.length > 0)
                    {
                        for (var i = 0; i < groups.length; i++)
                        {
                            if (groups[i].checked == true)
                            {
                                var row = groups[i].parentNode.parentNode.parentNode;
                                var table = row.parentNode;
                                table.deleteRow(row.rowIndex);
                                i--;
                            }
                            if (i >= groups.length)
                            {
                                break;
                            }
                        }
                    }
                    CheckSelectAll();

                    var users = document.getElementsByName('cbSelectGroup');
                    if (users.length == 0)
                    {
                        row = document.getElementById('row_remove_selected_groups');
                        var table = row.parentNode;
                        table.deleteRow(row.rowIndex);
                        ele = document.getElementById('link_add_group');
                        ele.innerHTML = '<?php echo $linkText['add_one']; ?>';
                    }
                },
                error: function(jqXHR, textStatus, errorThrown ) {
                    alert('Error occurred trying to remove user(s) from selected security groups.');
                }
            });
        }
    </script>
    <?php
    echo '<li>
    <table class="gridlines" cellspacing="1" cellpadding="4" width="99%" align="center" border="0">
    <tr>';

    if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
        echo '<td class="windowbg" width="1%">' .
            '<div class="custom-checkbox">' .
            '<input type="checkbox" id="cbSelectAllGroups" name="cbSelectAllGroups" onclick="javascript:SelectAllGroups(this.checked);" />' .
            '<div class="custom-checkbox-border"><i class="fa fa-check"></i></div></div>' .
            '</td>';
    }

    echo '<td class="windowbg" width="20%">' . _fdtk('profile_name') . '</td>';
    echo '<td class="windowbg">' . _fdtk('profile_description') . '</td>';
    echo '<td class="windowbg">' . _fdtk('profile_type') . '</td>';
    echo '</tr>';

    if ($num_rows == 0) {
        echo '<tr><td class="windowbg2" colspan="7">' . _fdtk('profile_no_security_groups') . '.</td></tr>';
    } else {
        foreach ($resultArray as $row) {
            if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
                if ($type == 'user') {
                    $href = "{$scripturl}?action=editgroup&grp_id=" . ($row['grp_id'] ?? '') . "&con_id=" . ($row['con_id'] ?? '');
                } elseif ($type == 'profile') {
                    $href = "{$scripturl}?action=editgroup&grp_id={$row['lpg_group']}&pfl_id={$row['lpg_profile']}";
                }
            }

            echo '<tr id="' . CompatEscaper::encodeCharacters($row['grp_code']) . '">';

            if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
                echo '<td class="windowbg2"><div class="custom-checkbox"><input type="checkbox" ' .
                    'id="cbSelectGroup" ' .
                    'name="cbSelectGroup" ' .
                    'value="' . Sanitize::SanitizeInt($row['recordid']) . '" ' .
                    'onclick="javascript:CheckSelectAll();"' .
                    ' /><div class="custom-checkbox-border"><i class="fa fa-check"></i></div></div></td>';
            }
            if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
                echo '<td class="windowbg2">' . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '<a href="javascript:if(CheckChange()){SendTo(\'' . CompatEscaper::encodeCharacters($href) . '\');}">' : '') . CompatEscaper::encodeCharacters($row['grp_code']) . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '</a>' : '') . '</td>';
                echo '<td class="windowbg2">' . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '<a href="javascript:if(CheckChange()){SendTo(\'' . CompatEscaper::encodeCharacters($href) . '\');}">' : '') . CompatEscaper::encodeCharacters($row['grp_description']) . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '</a>' : '') . '</td>';
                echo '<td class="windowbg2">' . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '<a href="javascript:if(CheckChange()){SendTo(\'' . CompatEscaper::encodeCharacters($href) . '\');}">' : '');
            } else {
                echo '<td class="windowbg2">' . CompatEscaper::encodeCharacters($row['grp_code']) . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '</a>' : '') . '</td>';
                echo '<td class="windowbg2">' . CompatEscaper::encodeCharacters($row['grp_description']) . (bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '</a>' : '') . '</td>';
                echo '<td class="windowbg2">';
            }
            if ($row['grp_type'] == (GRP_TYPE_ACCESS | GRP_TYPE_EMAIL)) {
                echo 'Record access and e-mail notification';
            } elseif ($row['grp_type'] == GRP_TYPE_ACCESS) {
                echo _fdtk('security_group_record_access');
            } elseif ($row['grp_type'] == GRP_TYPE_EMAIL) {
                echo 'E-mail notification';
            } elseif ($row['grp_type'] == GRP_TYPE_DENY_ACCESS) {
                echo 'Deny access';
            }

            echo(bYN(GetParm('ADM_GROUP_SETUP', 'Y')) ? '</a>' : '') . '</td>';

            echo '</tr>';
        }
    }

    if (!($FormAction == 'Print' || $FormAction == 'ReadOnly')) {
        if ($num_rows > 0) {
            echo '<tr id="row_remove_selected_groups"><td class="windowbg2" colspan="5">';
            echo '<a href="javascript:if(CheckChange()){UnlinkSelectedUserGroups()}" >' . $linkText['remove'] . '</a>';
            echo '</td></tr>';
        }

        echo '<tr><td class="windowbg2" colspan="5">';
        $url = $scripturl . '?action=' . $linkText['addlink'];

        echo '<a href="JavaScript:if(CheckChange()){document.forms[0].redirect_url.value=\'' .
            $url . '\';selectAllMultiCodes();document.forms[0].submit();}" ><div id="link_add_group">' . $linkText['add'] . '</div></a>';
        echo '</td></tr>';
        echo '<tr><td class="windowbg2" colspan="5">';

        if ($type == 'user') {
            echo '<div style="color:red">If the user belongs to any security groups, location/type settings will be disregarded.</div><br>';
            echo '<div style="color:red">Security groups added here will operate in addition to any security groups identified on the profile selected for this user.</div>';
        }

        echo '</td></tr>';
    }

    echo '</table></li>';
}

function GetDefaultPermissionString()
{
    $permissionStr = (string) (MOD_ADMIN * 256) . ';1';

    $sql = 'SELECT mod_id FROM modules WHERE mod_id NOT IN (' . MOD_ADMIN . ', ' . MOD_ELEMENTS . ', ' . MOD_PROMPTS . ')';

    $moduleIds = DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_COLUMN);

    foreach ($moduleIds as $moduleId) {
        // Number: SUB_SETUP = 15 (from main application)
        $permissionStr .= '|' . (string) ($moduleId * 256 + 15) . ';1' . '|' . (string) ($moduleId * 256) . ';3';
    }

    return $permissionStr;
}

function ParsePermString(?string $PermString = null)
{
    if ($PermString === null) {
        return [];
    }

    $PermElems = explode('|', $PermString);

    $ModuleNo = 0;

    $Modules = GetModuleIdCodeList();

    $PermNos = [0 => 'ALL',
        1 => 'READ',
        2 => 'WRITE',
        3 => 'NONE', ];

    foreach ($PermElems as $Elem) {
        $ModPerm = explode(';', $Elem);

        // If there are only two elements in this array,
        // it contains the permissions for the module.
        if (!array_key_exists(2, $ModPerm)) {
            if (($ModPerm[0] & 0xFF) != SUB_SETUP) {
                $Module = $ModPerm[0] >> 8;
                $Form = $ModPerm[0] & 0xFF;

                if (isset($Modules[$Module])) {
                    $ModStr = $Modules[$Module];

                    $Perm = $PermNos[$ModPerm[1]];

                    $ModulePerms[$ModStr]['form'][$Form] = $Perm;
                }
            } else {
                $Module = ($ModPerm[0] & 0xFF00) / 256;
                $ModStr = $Modules[$Module] ?? '';
                $ModulePerms[$ModStr]['disallow_setup'] = ($ModPerm[1] == 1);
            }
        } elseif (($ModPerm[0] & 0xFF) == SUB_WHERE) {
            $Module = ($ModPerm[0] & 0xFF00) / 256;

            $ModStr = $Modules[$Module] ?? '';

            $ModulePerms[$ModStr]['secgroup'] = $ModPerm[1] ?? null;
            $ModulePerms[$ModStr]['seclevel'] = $ModPerm[2] ?? null;
            $ModulePerms[$ModStr]['where'] = $ModPerm[3] ?? null;
        }
    }

    return $ModulePerms;
}
