<?php

use app\models\globals\ProgNotesEditGlobal;
use src\safeguarding\models\SafeguardingAdminFields;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

function UserParamList()
{
    $ModuleDefs = Container::get(ModuleDefs::class);

    $mainParams = [
        // Access levels
        'DIF_PERMS',
        'ACT_PERMS',
        'AST_PERMS',
        'CON_PERMS',
        'DAS_PERMS',
        'COM_PERMS',
        'MED_PERMS',
        'ADM_PERMS',
        'TOD_PERMS',
        // Incidents
        'DIF_STA_EMAIL_LOCS',
        'DIF_SHOW_REJECT_BTN',
        \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS,
        'DIF2_HIDE_CONTACTS',
        'DIF_OWN_ONLY',
        'INC_SHOW_AUDIT',
        'DIF1_ONLY_FORM',
        'DIF2_DEFAULT',
        'INC_LISTING_ID',
        'DIF2_SEARCH_DEFAULT',
        'INC_LOCACTUAL',
        'INC_LOCTYPE',
        'INC_SETUP',
        'COPY_INCIDENTS',
        'DIF_TYPECAT_PERMS',
        'INC_DELETE_DOCS',
        'INC_SAVED_QUERIES_HOME_SCREEN',
        'INC_SAVED_QUERIES',
        'INC_CAN_DELETE_OWN_DOCUMENTS',
        // Contacts
        'CON_SHOW_REJECT_BTN',
        'CON_SETUP',
        'CON_ALLOW_MERGE_DUPLICATES',
        // Actions
        'ACT_OWN_ONLY',
        'ACT_SEARCH_DEFAULT',
        'ACT_SETUP',
        'DELETE_ACT_CHAIN',
        // Assets - Equipment
        'AST_SETUP',
        'AST_DELETE_DOCS',
        // All modules
        'LOGOUT_DEFAULT_MODULE',
        'CUSTOM_REPORT_BUILDER',
        ProgNotesEditGlobal::GLOBAL_NAME,
        'ENABLE_GENERATE',
        'ENABLE_BATCH_UPDATE',
        // Claims
        'CLAIM1_DEFAULT',
        'CLAIM2_DEFAULT',
        'CLA_LISTING_ID',
        'CLAIM2_SEARCH_DEFAULT',
        'CLA_OWN_ONLY',
        'CLA_SHOW_AUDIT',
        'CLA_SETUP',
        'CLA_DELETE_DOCS',
        'CLA_CAN_DELETE_OWN_DOCUMENTS',
        'CLA_SAVED_QUERIES_HOME_SCREEN',
        'CLA_SAVED_QUERIES',
        'COPY_CLA',
        // Policies
        'POL2_DEFAULT',
        'POL_LISTING_ID',
        'POL2_SEARCH_DEFAULT',
        'POL_OWN_ONLY',
        'POL_SHOW_AUDIT',
        'POL_SETUP',
        'COPY_POL',
        'POL_DELETE_DOCS',
        // Payments
        'PAY2_SEARCH_DEFAULT',
        'DELETE_PAY',
        // Medications
        'MED_LISTING_ID',
        'MED_SEARCH_DEFAULT',
        'MED_SETUP',


        // Locations
        'LOC_LISTING_ID',
        'LOC_DEFAULT',
        // Admin
        'ADM_PROFILES',
        'FULL_ADMIN',
        'ADM_DEFAULT',
        'ADM_LISTING_ID',
        'ADM_GROUP_SETUP',
        'ADM_CAN_DELEGATE_PERMISSIONS',
        'ADM_CAN_DELEGATE_ACCOUNTS',
        'ADM_NO_ADMIN_REPORTS',

        // Mortality
        'MOR1_DEFAULT',
        'MOR2_DEFAULT',
        'MOR2_SEARCH_DEFAULT',
        'MOR_LISTING_ID',
        'MOR_SHOW_AUDIT',
        'MOR_SETUP',
        'MOR_SAVED_QUERIES_HOME_SCREEN',
        'MOR_SAVED_QUERIES',
        'MOR_DELETE_DOCS',
        'MOR_CAN_DELETE_OWN_DOCUMENTS',
        'COPY_MOR',

        // COMPLAINTS
        'COM1_DEFAULT',
        'COM2_DEFAULT',
        'COM_SHOW_REJECT_BTN',
        'COM_LISTING_ID',
        'COM2_SEARCH_DEFAULT',
        'COM_OWN_ONLY',
        'COM_SHOW_AUDIT',
        'COM_SETUP',
        'COM_DELETE_DOCS',
        'COM_CAN_DELETE_OWN_DOCUMENTS',
        'COM_SHOW_ADD_NEW',
        'COM_KO41_CAN_GENERATE_REPORT',
        'COM_SAVED_QUERIES_HOME_SCREEN',
        'COM_SAVED_QUERIES',
        'SHOW_REOPEN',
        'EDITABLE_CHAIN_DUE_DATES',
        'COPY_COM',

        // Redress
        'RED1_DEFAULT',
        'RED2_DEFAULT',
        'RED2_SEARCH_DEFAULT',
        'RED_LISTING_ID',
        'RED_SHOW_AUDIT',
        'RED_SETUP',
        'RED_SAVED_QUERIES_HOME_SCREEN',
        'RED_SAVED_QUERIES',
        'RED_DELETE_DOCS',
        'RED_CAN_DELETE_OWN_DOCUMENTS',
        'COPY_RED',

    ];

    // Merge arrays of Module Fields into $mainParams to create full array of field - above could be refactored out to module fields like done for Safeguarding
    $ParamDefs = array_merge(
        $mainParams,
        SafeguardingAdminFields::getUserFields(),
    );

    foreach ($ModuleDefs as $module => $Details) {
        if ($Details['GENERIC'] && ModIsLicensed($module)) {
            if (is_array($ModuleDefs[$module]['SECURITY']['LOC_FIELDS'])) {
                foreach ($ModuleDefs[$module]['SECURITY']['LOC_FIELDS'] as $field => $fieldDetails) {
                    $ParamDefs[] = \UnicodeString::strtoupper($field);
                }
            }

            if ($ModuleDefs[$module]['SECURITY']['EMAIL_NOTIFICATION_GLOBAL']) {
                $ParamDefs[] = $ModuleDefs[$module]['SECURITY']['EMAIL_NOTIFICATION_GLOBAL'];
            }

            $ParamDefs[] = $ModuleDefs[$module]['PERM_GLOBAL'];
        }
    }

    return $ParamDefs;
}

function deleteConfigurationParameters($sta)
{
    $sql = 'DELETE FROM user_parms WHERE login = :login
        AND parameter IN (\'' . implode('\', \'', getConfigurationParameters()) . '\')';

    DatixDBQuery::PDO_query($sql, ['login' => $sta['login']]);
}

function deleteSecurityGroups($sta)
{
    $sql = 'DELETE FROM sec_staff_group WHERE use_id = :use_id';
    DatixDBQuery::PDO_query($sql, ['use_id' => $sta['recordid']]);
}

function deleteLocTypeSettings($sta)
{
    global $ModuleDefs;

    $ParametersToDelete = [
        'ACT_PERMS',
        'CON_PERMS',
        'AST_PERMS',
        'MED_PERMS',
        'DIF_PERMS',
        'DIF_STA_EMAIL_LOCS',
        'INC_LOCTYPE',
        'INC_LOCACTUAL',
        'DIF_TYPECAT_PERMS',
        'RISK_PERMS',
        'DAS_PERMS',
        'COM_PERMS',
        'ADM_PERMS',
        'TOD_PERMS', ];

    foreach ($ModuleDefs as $module => $Details) {
        if ($Details['GENERIC'] && ModIsLicensed($module) && !$ModuleDefs[$module]['NO_SECURITY_LEVEL']) {
            if (isset($ModuleDefs[$module]['PERM_GLOBAL'])) {
                $ParametersToDelete[] = $ModuleDefs[$module]['PERM_GLOBAL'];
            }

            if (is_array($ModuleDefs[$module]['SECURITY']['LOC_FIELDS'])) {
                foreach ($ModuleDefs[$module]['SECURITY']['LOC_FIELDS'] as $Field => $Details) {
                    $ParametersToDelete[] = \UnicodeString::strtoupper($Field);
                }
            }

            if (isset($ModuleDefs[$module]['SECURITY']['EMAIL_NOTIFICATION_GLOBAL'])) {
                $ParametersToDelete[] = $ModuleDefs[$module]['SECURITY']['EMAIL_NOTIFICATION_GLOBAL'];
            }
        }
    }

    $sql = 'DELETE FROM user_parms WHERE login = :login
        AND parameter IN (\'' . implode('\', \'', $ParametersToDelete) . '\')';

    DatixDBQuery::PDO_query($sql, ['login' => $sta['login']]);
}

function saveConfigurationParameters($sta)
{
    // Actions
    SetUserParm($sta['login'], 'ACT_OWN_ONLY', $sta['ACT_OWN_ONLY']);
    SetUserParm($sta['login'], 'ACT_SEARCH_DEFAULT', $sta['ACT_SEARCH_DEFAULT']);
    SetUserParm($sta['login'], 'ACT_SETUP', $sta['ACT_SETUP']);
    SetUserParm($sta['login'], 'DELETE_ACT_CHAIN', $sta['DELETE_ACT_CHAIN']);

    // All modules
    SetUserParm($sta['login'], 'LOGOUT_DEFAULT_MODULE', $sta['LOGOUT_DEFAULT_MODULE']);
    SetUserParm($sta['login'], 'CUSTOM_REPORT_BUILDER', $sta['CUSTOM_REPORT_BUILDER']);
    SetUserParm($sta['login'], ProgNotesEditGlobal::GLOBAL_NAME, $sta[ProgNotesEditGlobal::GLOBAL_NAME]);
    SetUserParm($sta['login'], 'ENABLE_GENERATE', $sta['ENABLE_GENERATE']);
    SetUserParm($sta['login'], 'ENABLE_BATCH_UPDATE', $sta['ENABLE_BATCH_UPDATE']);

    // Admin
    if ($_POST['show_field_ADM_PROFILES']) {
        $sta['ADM_PROFILES'] = CheckMultiListValue('ADM_PROFILES', $_POST) ?: '';
    }

    SetUserParm($sta['login'], 'ADM_DEFAULT', $sta['ADM_DEFAULT']);
    SetUserParm($sta['login'], 'ADM_LISTING_ID', $sta['ADM_LISTING_ID']);
    SetUserParm($sta['login'], 'ADM_PROFILES', $sta['ADM_PROFILES']);
    SetUserParm($sta['login'], 'FULL_ADMIN', $sta['FULL_ADMIN']);
    SetUserParm($sta['login'], 'ADM_GROUP_SETUP', $sta['ADM_GROUP_SETUP']);
    SetUserParm($sta['login'], 'ADM_CAN_DELEGATE_PERMISSIONS', $sta['ADM_CAN_DELEGATE_PERMISSIONS']);
    SetUserParm($sta['login'], 'ADM_CAN_DELEGATE_ACCOUNTS', $sta['ADM_CAN_DELEGATE_ACCOUNTS']);
    SetUserParm($sta['login'], 'ADM_NO_ADMIN_REPORTS', $sta['ADM_NO_ADMIN_REPORTS']);

    // Contacts
    SetUserParm($sta['login'], 'CON_SHOW_REJECT_BTN', $sta['CON_SHOW_REJECT_BTN']);
    SetUserParm($sta['login'], 'CON_SETUP', $sta['CON_SETUP']);
    SetUserParm($sta['login'], 'CON_ALLOW_MERGE_DUPLICATES', $sta['CON_ALLOW_MERGE_DUPLICATES']);

    // Equipment/Assets
    SetUserParm($sta['login'], 'AST_SETUP', $sta['AST_SETUP']);
    SetUserParm($sta['login'], 'AST_DELETE_DOCS', $sta['AST_DELETE_DOCS']);

    // Medications
    SetUserParm($sta['login'], 'MED_LISTING_ID', $sta['MED_LISTING_ID']);
    SetUserParm($sta['login'], 'MED_SETUP', $sta['MED_SETUP']);

    // Complaints
    SetUserParm($sta['login'], 'COM1_DEFAULT', $sta['COM1_DEFAULT']);
    SetUserParm($sta['login'], 'COM2_DEFAULT', $sta['COM2_DEFAULT']);
    SetUserParm($sta['login'], 'COM_SHOW_REJECT_BTN', $sta['COM_SHOW_REJECT_BTN']);
    SetUserParm($sta['login'], 'COM_LISTING_ID', $sta['COM_LISTING_ID']);
    SetUserParm($sta['login'], 'COM2_SEARCH_DEFAULT', $sta['COM2_SEARCH_DEFAULT']);
    SetUserParm($sta['login'], 'COM_OWN_ONLY', $sta['COM_OWN_ONLY']);
    SetUserParm($sta['login'], 'COM_SHOW_AUDIT', $sta['COM_SHOW_AUDIT']);
    SetUserParm($sta['login'], 'COM_SETUP', $sta['COM_SETUP']);
    SetUserParm($sta['login'], 'COM_DELETE_DOCS', $sta['COM_DELETE_DOCS']);
    SetUserParm($sta['login'], 'COM_SHOW_ADD_NEW', $sta['COM_SHOW_ADD_NEW']);
    SetUserParm($sta['login'], 'COM_KO41_CAN_GENERATE_REPORT', $sta['COM_KO41_CAN_GENERATE_REPORT']);
    SetUserParm($sta['login'], 'COM_SAVED_QUERIES_HOME_SCREEN', $sta['COM_SAVED_QUERIES_HOME_SCREEN']);
    SetUserParm($sta['login'], 'COM_CAN_DELETE_OWN_DOCUMENTS', $sta['COM_CAN_DELETE_OWN_DOCUMENTS']);
    SetUserParm($sta['login'], 'SHOW_REOPEN', $sta['SHOW_REOPEN']);
    SetUserParm($sta['login'], 'EDITABLE_CHAIN_DUE_DATES', $sta['EDITABLE_CHAIN_DUE_DATES']);
    SetUserParm($sta['login'], 'COPY_COM', $sta['COPY_COM']);

    if ($_POST['show_field_COM_SAVED_QUERIES']) {
        $sta['COM_SAVED_QUERIES'] = CheckMultiListValue('COM_SAVED_QUERIES', $_POST) ?: '';
    }
    SetUserParm($sta['login'], 'COM_SAVED_QUERIES', $sta['COM_SAVED_QUERIES']);


    // Claims
    SetUserParm($sta['login'], 'CLAIM1_DEFAULT', $sta['CLAIM1_DEFAULT']);
    SetUserParm($sta['login'], 'CLAIM2_DEFAULT', $sta['CLAIM2_DEFAULT']);
    SetUserParm($sta['login'], 'CLA_LISTING_ID', $sta['CLA_LISTING_ID']);
    SetUserParm($sta['login'], 'CLAIM2_SEARCH_DEFAULT', $sta['CLAIM2_SEARCH_DEFAULT']);
    SetUserParm($sta['login'], 'CLA_OWN_ONLY', $sta['CLA_OWN_ONLY']);
    SetUserParm($sta['login'], 'CLA_SHOW_AUDIT', $sta['CLA_SHOW_AUDIT']);
    SetUserParm($sta['login'], 'CLA_SETUP', $sta['CLA_SETUP']);
    SetUserParm($sta['login'], 'CLA_DELETE_DOCS', $sta['CLA_DELETE_DOCS']);
    SetUserParm($sta['login'], 'CLA_SAVED_QUERIES_HOME_SCREEN', $sta['CLA_SAVED_QUERIES_HOME_SCREEN']);
    SetUserParm($sta['login'], 'CLA_CAN_DELETE_OWN_DOCUMENTS', $sta['CLA_CAN_DELETE_OWN_DOCUMENTS']);
    SetUserParm($sta['login'], 'COPY_CLA', $sta['COPY_CLA']);


    if ($_POST['show_field_CLA_SAVED_QUERIES']) {
        $sta['CLA_SAVED_QUERIES'] = CheckMultiListValue('CLA_SAVED_QUERIES', $_POST) ?: '';
    }
    SetUserParm($sta['login'], 'CLA_SAVED_QUERIES', $sta['CLA_SAVED_QUERIES']);

    // Policies
    SetUserParm($sta['login'], 'POL2_DEFAULT', $sta['POL2_DEFAULT']);
    SetUserParm($sta['login'], 'POL_LISTING_ID', $sta['POL_LISTING_ID']);
    SetUserParm($sta['login'], 'POL2_SEARCH_DEFAULT', $sta['POL2_SEARCH_DEFAULT']);
    SetUserParm($sta['login'], 'POL_OWN_ONLY', $sta['POL_OWN_ONLY']);
    SetUserParm($sta['login'], 'POL_SHOW_AUDIT', $sta['POL_SHOW_AUDIT']);
    SetUserParm($sta['login'], 'POL_SETUP', $sta['POL_SETUP']);
    SetUserParm($sta['login'], 'COPY_POL', $sta['COPY_POL']);
    SetUserParm($sta['login'], 'POL_DELETE_DOCS', $sta['POL_DELETE_DOCS']);

    // Payments
    SetUserParm($sta['login'], 'PAY2_SEARCH_DEFAULT', $sta['PAY2_SEARCH_DEFAULT']);
    SetUserParm($sta['login'], 'DELETE_PAY', $sta['DELETE_PAY']);


    // Incidents
    if ($_SESSION['licensedModules'][MOD_INCIDENTS]) {
        SetUserParm($sta['login'], 'DIF_SHOW_REJECT_BTN', $sta['DIF_SHOW_REJECT_BTN']);
        SetUserParm($sta['login'], 'DIF2_HIDE_CONTACTS', $sta['DIF2_HIDE_CONTACTS']);
        SetUserParm($sta['login'], 'DIF_OWN_ONLY', $sta['DIF_OWN_ONLY']);
        SetUserParm($sta['login'], 'INC_SHOW_AUDIT', $sta['INC_SHOW_AUDIT']);
        SetUserParm($sta['login'], 'DIF1_ONLY_FORM', $sta['DIF1_ONLY_FORM']);
        SetUserParm($sta['login'], 'DIF2_DEFAULT', $sta['DIF2_DEFAULT']);
        SetUserParm($sta['login'], 'INC_LISTING_ID', $sta['INC_LISTING_ID']);
        SetUserParm($sta['login'], 'DIF2_SEARCH_DEFAULT', $sta['DIF2_SEARCH_DEFAULT']);
        SetUserParm($sta['login'], 'INC_SETUP', $sta['INC_SETUP']);
        SetUserParm($sta['login'], 'COPY_INCIDENTS', $sta['COPY_INCIDENTS']);
        SetUserParm($sta['login'], 'INC_DELETE_DOCS', $sta['INC_DELETE_DOCS']);
        SetUserParm($sta['login'], 'INC_SAVED_QUERIES_HOME_SCREEN', $sta['INC_SAVED_QUERIES_HOME_SCREEN']);
        SetUserParm($sta['login'], \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS, $sta['INC_BYPASS_MANDATORY_FIELDS']);

        if ($_POST['show_field_INC_SAVED_QUERIES']) {
            $sta['INC_SAVED_QUERIES'] = CheckMultiListValue('INC_SAVED_QUERIES', $_POST) ?: '';
        }

        SetUserParm($sta['login'], 'INC_SAVED_QUERIES', $sta['INC_SAVED_QUERIES']);
        SetUserParm($sta['login'], 'INC_CAN_DELETE_OWN_DOCUMENTS', $sta['INC_CAN_DELETE_OWN_DOCUMENTS']);
    }

    if ($_SESSION['licensedModules'][MOD_MORTALITY]) {
        SetUserParm($sta['login'], 'MOR1_DEFAULT', $sta['MOR1_DEFAULT']);
        SetUserParm($sta['login'], 'MOR2_DEFAULT', $sta['MOR2_DEFAULT']);
        SetUserParm($sta['login'], 'MOR2_SEARCH_DEFAULT', $sta['MOR2_SEARCH_DEFAULT']);
        SetUserParm($sta['login'], 'MOR_LISTING_ID', $sta['MOR_LISTING_ID']);
        SetUserParm($sta['login'], 'MOR_SHOW_AUDIT', $sta['MOR_SHOW_AUDIT']);
        SetUserParm($sta['login'], 'MOR_SETUP', $sta['MOR_SETUP']);
        SetUserParm($sta['login'], 'MOR_SAVED_QUERIES_HOME_SCREEN', $sta['MOR_SAVED_QUERIES_HOME_SCREEN']);
        SetUserParm($sta['login'], 'MOR_DELETE_DOCS', $sta['MOR_DELETE_DOCS']);
        SetUserParm($sta['login'], 'MOR_CAN_DELETE_OWN_DOCUMENTS', $sta['MOR_CAN_DELETE_OWN_DOCUMENTS']);
        SetUserParm($sta['login'], 'COPY_MOR', $sta['COPY_MOR']);


        if ($_POST['show_field_MOR_SAVED_QUERIES']) {
            $sta['MOR_SAVED_QUERIES'] = CheckMultiListValue('MOR_SAVED_QUERIES', $_POST) ?: '';
        }
        SetUserParm($sta['login'], 'MOR_SAVED_QUERIES', $sta['MOR_SAVED_QUERIES']);
    }

    // Locations
    SetUserParm($sta['login'], 'LOC_LISTING_ID', $sta['LOC_LISTING_ID']);
    SetUserParm($sta['login'], 'LOC_DEFAULT', $sta['LOC_DEFAULT']);

    if ($_SESSION['licensedModules'][MOD_REDRESS]) {
        SetUserParm($sta['login'], 'RED1_DEFAULT', $sta['RED1_DEFAULT']);
        SetUserParm($sta['login'], 'RED2_DEFAULT', $sta['RED2_DEFAULT']);
        SetUserParm($sta['login'], 'RED2_SEARCH_DEFAULT', $sta['RED2_SEARCH_DEFAULT']);
        SetUserParm($sta['login'], 'RED_LISTING_ID', $sta['RED_LISTING_ID']);
        SetUserParm($sta['login'], 'RED_SHOW_AUDIT', $sta['RED_SHOW_AUDIT']);
        SetUserParm($sta['login'], 'RED_SETUP', $sta['RED_SETUP']);
        SetUserParm($sta['login'], 'RED_SAVED_QUERIES_HOME_SCREEN', $sta['RED_SAVED_QUERIES_HOME_SCREEN']);
        SetUserParm($sta['login'], 'RED_DELETE_DOCS', $sta['RED_DELETE_DOCS']);
        SetUserParm($sta['login'], 'RED_CAN_DELETE_OWN_DOCUMENTS', $sta['RED_CAN_DELETE_OWN_DOCUMENTS']);
        SetUserParm($sta['login'], 'COPY_RED', $sta['COPY_RED']);


        if ($_POST['show_field_RED_SAVED_QUERIES']) {
            $sta['RED_SAVED_QUERIES'] = CheckMultiListValue('RED_SAVED_QUERIES', $_POST) ?: '';
        }
        SetUserParm($sta['login'], 'RED_SAVED_QUERIES', $sta['RED_SAVED_QUERIES']);
    }

    if ($_SESSION['licensedModules'][MOD_SAFEGUARDING]) {
        SetUserParm($sta['login'], SafeguardingAdminFields::DEFAULT_LEVEL_1_FORM, $sta[SafeguardingAdminFields::DEFAULT_LEVEL_1_FORM]);
        SetUserParm($sta['login'], SafeguardingAdminFields::DEFAULT_LEVEL_2_FORM, $sta[SafeguardingAdminFields::DEFAULT_LEVEL_2_FORM]);
        SetUserParm($sta['login'], SafeguardingAdminFields::DEFAULT_SEARCH_FORM, $sta[SafeguardingAdminFields::DEFAULT_SEARCH_FORM]);
        SetUserParm($sta['login'], SafeguardingAdminFields::ENABLE_PINNED_QUERIES, $sta[SafeguardingAdminFields::ENABLE_PINNED_QUERIES]);
        SetUserParm($sta['login'], SafeguardingAdminFields::SELECT_PINNED_QUERIES, $sta[SafeguardingAdminFields::SELECT_PINNED_QUERIES]);
        SetUserParm($sta['login'], SafeguardingAdminFields::ENABLE_RECORD_COPY, $sta[SafeguardingAdminFields::ENABLE_RECORD_COPY]);
        SetUserParm($sta['login'], SafeguardingAdminFields::SHOW_AUDIT, $sta[SafeguardingAdminFields::SHOW_AUDIT]);
        SetUserParm($sta['login'], SafeguardingAdminFields::SHOW_ADD_NEW, $sta[SafeguardingAdminFields::SHOW_ADD_NEW]);
    }
}
