<?php

use app\models\forms\specifications\EditableFormSpecification;
use app\models\generic\valueObjects\Module;
use app\services\forms\PageTitleProvider;
use app\services\idGenerator\UpdateIdGenerator;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use src\admin\services\CapturePermissionService;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTableFactory;
use src\framework\controller\Response;
use src\helpers\SqlInClauseHelper;
use src\security\CompatEscaper;
use src\security\Escaper;
use src\system\container\facade\Container;

function MakeRecordSecPanel($module, $table, $recordid, $FormType, $type = 'both')
{
    global $scripturl;

    // Get hold of the current url to be able to go back to it after saving or cancelling
    $url_from = $scripturl . '?';

    $data = Sanitize::SanitizeStringArray($_GET);

    foreach ($data as $key => $value) {
        $url_from .= $key . '=' . $value . '&';
    }

    if ($module == 'INC') {
        $url_from .= 'panel=permissions_user';
    } else {
        $url_from .= 'panel=permissions';
    }

    $url_from = Sanitize::SanitizeURL($url_from);

    unset($_SESSION['record_permissions']);
    $_SESSION['record_permissions']['url_from'] = $url_from; ?>
<script language="JavaScript">
    function UnlinkSelectedUsers()
    {
        var urlGroups = '';
        var groups = document.getElementsByName('cbSelectUser');

        jQuery.each(jQuery('input[type=checkbox][name=cbSelectUser]:checked'), function(i, checkbox) {urlGroups += '&link_id[]=' + checkbox.value;});

        if (urlGroups != '')
        {
            urlGroups = urlGroups.substr(1);
            if (confirm('Remove selected user(s) from record?') == false)
            {
                return;
            }

            jQuery.ajax({
                url: scripturl+'?action=editrecordperms&type=user&delete=1&token=<?php echo CSRFGuard::getCurrentToken(); ?>',
                type: "POST",
                data: urlGroups,
                dataType: 'text',
                async: true,
                success: function(response) {
                    location.reload();
                },
                error: function(jqXHR, textStatus, errorThrown ) {
                    alert('Error occurred trying to remove selected users from record.');
                }
            });
        }
        else
        {
            alert('No user selected.');
        }

    }
    function UnlinkSelectedGroups()
    {
        var urlGroups = '';
        var groups = document.getElementsByName('cbSelectUser');

        jQuery.each(jQuery('input[type=checkbox][name=cbSelectGroup]:checked'), function(i, checkbox) {urlGroups += '&link_id[]=' + checkbox.value;});

        if (urlGroups != '')
        {
            urlGroups = urlGroups.substr(1);
            if (confirm('Remove selected group(s) from record?') == false)
            {
                return;
            }

            jQuery.ajax({
                url: scripturl + '?action=editrecordperms&type=group&delete=1&token=<?php echo CSRFGuard::getCurrentToken(); ?>',
                type: "POST",
                data: urlGroups,
                dataType: 'text',
                async: true,
                success: function(response) {
                    location.reload();
                },
                error: function(jqXHR, textStatus, errorThrown ) {
                    alert('Error occurred trying to remove selected groups from record.');
                }
            });
        }
        else
        {
            alert('No group selected.');
        }

    }
    </script>
<?php

        echo '
            <input type="hidden" name="record_permissions_url" value="" />
            <input type="hidden" name="url_from" value="' . Escaper::escapeForHTMLParameter($url_from) . '" /><!--Filter moved from here-->
            <table id="tblUsers" class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">';

    $UserCanLinkUsers = UserCanLinkUsers(['module' => $module, 'table' => $table, 'recordid' => $recordid]);
    if ($type == 'user' || $type == 'both') {
        $UserList = GetRecordAccessUserList(['module' => $module, 'table' => $table, 'recordid' => $recordid]);

        if (empty($UserList)) {
            echo '<tr><td class="rowtitlebg" colspan="8">No linked users.</td></tr>';
        } else {
            echo '<tr><td class="windowbg" width="1%">' .
                '<input type="checkbox" id="cbSelectAllUsers" name="cbSelectAllUsers" onclick="jQuery(\'input[type=checkbox][name=cbSelectUser]\').attr(\'checked\', this.checked);" />' .
                '</td><td>Users</td></tr>';

            foreach ($UserList as $row) {
                $contactName = "{$row['use_surname']}, {$row['use_forenames']} {$row['use_title']}";
                $contactName = CompatEscaper::encodeCharacters(\UnicodeString::trim($contactName, ' ,'));

                if ($FormType != 'Print' && $FormType != 'ReadOnly' && $UserCanLinkUsers) {
                    $url = $scripturl . "?action=edituser&recordid={$row['use_id']}";
                    $delurl = $scripturl . '?action=editrecordperms&type=user&recordid=' . $row['recordid'];

                    echo '<tr><td width="12px">
                    <input type="checkbox" ' .
                    'name="cbSelectUser" ' .
                    'value="' . Sanitize::SanitizeInt($row['recordid']) . '" ' .
                    'onclick="if(jQuery(\'input[type=checkbox][name=cbSelectUser]\').not(\':checked\').length == 0){jQuery(\'#cbSelectAllUsers\').attr(\'checked\', true)}else{jQuery(\'#cbSelectAllUsers\').attr(\'checked\', false)}"' .
                    ' /></td>
                    <td class="rowtitlebg">
                    <a href="JavaScript:javascript:if(CheckChange()){SendTo(\'' . CompatEscaper::encodeCharacters($url) . '\');}">' .
                    $contactName .
                    '</a></td>';
                    echo '</tr>';
                } else {
                    echo '<tr><td class="rowtitlebg" colspan="2">' . $contactName . '</td></tr>';
                }
            }
        }

        if ((new EditableFormSpecification())->isSatisfiedBy($FormType) && $UserCanLinkUsers) {
            if (!empty($UserList)) {
                echo '<tr id="row_remove_selected_users"><td class="windowbg2" colspan="5">';
                echo '<a href="javascript:if(CheckChange()){UnlinkSelectedUsers()}" >Remove selected user(s) from record</a>';
                echo '</td></tr>';
            }

            $url = $scripturl . '?action=editrecordperms&type=user&module=' . $module . '&table_name=' . $table . '&link_id=' . $recordid;

            echo '<tr>
            <td class="new_link_row" colspan="8">
            <a href="javascript:if(CheckChange()){SendTo(\'' . $url . '\');}">
            Add new user</a>
            </td>
            </tr>';
        }
    }
    if ($type == 'group' || $type == 'both') {
        $GroupList = GetRecordAccessGroupList(['module' => $module, 'table' => $table, 'recordid' => $recordid]);

        if (empty($GroupList)) {
            echo '<tr><td class="rowtitlebg" colspan="8">No linked groups.</td></tr>';
        } else {
            echo '<tr><td class="windowbg" width="1%">' .
                '<input type="checkbox" id="cbSelectAllGroups" name="cbSelectAllGroups" onclick="jQuery(\'input[type=checkbox][name=cbSelectGroup]\').attr(\'checked\', this.checked);" />' .
                '</td><td>Security Groups</td></tr>';
            foreach ($GroupList as $row) {
                if ($FormType != 'Print' && $FormType != 'ReadOnly' && $UserCanLinkUsers) {
                    $url = $scripturl . "?action=editgroup&grp_id={$row['grp_id']}";
                    $delurl = $scripturl . '?action=editrecordperms&type=group&recordid=' . $row['recordid'];

                    echo '<tr><td width="12px">
                    <input type="checkbox" ' .
                    'name="cbSelectGroup" ' .
                    'value="' . Sanitize::SanitizeInt($row['recordid']) . '" ' .
                    'onclick="if(jQuery(\'input[type=checkbox][name=cbSelectGroup]\').not(\':checked\').length == 0){jQuery(\'#cbSelectAllGroups\').attr(\'checked\', true)}else{jQuery(\'#cbSelectAllGroups\').attr(\'checked\', false)};"' .
                    ' /></td><td class="rowtitlebg">
                    <a href="javascript:if(CheckChange()){SendTo(\'' . CompatEscaper::encodeCharacters($url) . '\');}">' .
                    CompatEscaper::encodeCharacters($row['grp_code']) .
                    '</a></td>';
                    echo '</tr>';
                } else {
                    echo '<tr><td class="rowtitlebg" colspan="2">' . CompatEscaper::encodeCharacters($row['grp_code']) . '</td></tr>';
                }
            }
        }

        if ((new EditableFormSpecification())->isSatisfiedBy($FormType) && $UserCanLinkUsers) {
            if (!empty($GroupList)) {
                echo '<tr id="row_remove_selected_users"><td class="windowbg2" colspan="5">';
                echo '<a href="javascript:if(CheckChange()){UnlinkSelectedGroups()}" >Remove selected group(s) from record</a>';
                echo '</td></tr>';
            }

            $url = $scripturl . '?action=editrecordperms&type=group&module=' . $module . '&table_name=' . $table . '&link_id=' . $recordid;

            echo '<tr>
            <td class="new_link_row" colspan="8">
            <a href="javascript:if(CheckChange()){SendTo(\'' . $url . '\');}">
            Add new group</a>
            </td>
            </tr>';
        }
    }

    echo '
        </table>';
}

/**
 * @throws PermissionDeniedException
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 */
function EditRecordPerms(): void
{
    global $scripturl, $dtxtitle, $ModuleDefs;

    $RecordPerms = [];

    $error = Sanitize::SanitizeString($_GET['error']);

    if ($error != '') {
        $RecordPerms['error']['message'] = $error;
    }

    $RecordPerms['delete'] = Sanitize::SanitizeInt($_GET['delete']);
    $RecordPerms['module'] = Sanitize::SanitizeString($_GET['module']);
    $RecordPerms['table_name'] = Sanitize::SanitizeString($_GET['table_name']);
    $RecordPerms['link_id'] = Sanitize::SanitizeInt($_GET['link_id']);
    $RecordPerms['use_id'] = Sanitize::SanitizeInt($_GET['use_id']);
    $RecordPerms['type'] = Sanitize::SanitizeString($_GET['type']);
    $RecordPerms['group_id'] = Sanitize::SanitizeInt($_GET['group_id']);

    if ($RecordPerms['module'] === Module::REPORTS) {
        if (!Container::get(CapturePermissionService::class)->userHasReportsAdminPermission()) {
            throw new PermissionDeniedException(_fdtk('no_module_access'));
        }
    }

    if ($RecordPerms['delete']) { // delete
        $sqlHelper = new SqlInClauseHelper();

        if ($RecordPerms['module'] == 'REP') {
            $record_id = Sanitize::SanitizeInt($_POST['record_id']);
            $user_ids = array_map(['Sanitize', 'SanitizeInt'], $_POST['link_id']);

            if ($RecordPerms['type'] == 'user') {
                $sql = 'delete from web_packaged_report_users where ' . $sqlHelper->inClause('user_id', $user_ids) . " AND web_packaged_report_id = {$record_id}";
            } elseif ($RecordPerms['type'] == 'group') {
                $sql = 'delete from web_packaged_report_groups where ' . $sqlHelper->inClause('group_id', $user_ids) . " AND web_packaged_report_id = {$record_id}";
            } elseif ($RecordPerms['type'] == 'profile') {
                $sql = 'delete from web_packaged_report_profiles where ' . $sqlHelper->inClause('profile_id', $user_ids) . " AND web_packaged_report_id = {$record_id}";
            }
        } else {
            if ($RecordPerms['type'] == 'user') {
                $sql = 'delete from sec_record_permissions where ' . $sqlHelper->inClause('recordid', $_POST['link_id']);
            } elseif ($RecordPerms['type'] == 'group') {
                $sql = 'delete from sec_group_record_permissions where ' . $sqlHelper->inClause('recordid', $_POST['link_id']);
            }
        }

        $result = db_query($sql);
        $location = $_SESSION['record_permissions']['url_from'];
        Container::get(Response::class)->redirect($location);
    }

    if ($RecordPerms['module'] == 'REP') {
        if ($RecordPerms['type'] == 'user') {
            $dtxtitle = _fdtk('user_report_access');
        } elseif ($RecordPerms['type'] == 'group') {
            $dtxtitle = _fdtk('group_report_access');
        } elseif ($RecordPerms['type'] == 'profile') {
            $dtxtitle = _fdtk('profile_report_access');
        }
    } else {
        if ($RecordPerms['type'] == 'user') {
            $dtxtitle = _fdtk('user_record_access');
        } elseif ($RecordPerms['type'] == 'group') {
            $dtxtitle = _fdtk('group_record_access');
        }
    }

    $module = $RecordPerms['module'] ?? '';
    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            $module,
            $dtxtitle,
        );

    GetSideMenuHTML(['module' => $RecordPerms['module']]);

    template_header();

    echo '
<form enctype="multipart/form-data" method="post" name="frmRecordPerms" action="' . $scripturl . '?action=saverecordperms">
<input type="hidden" name="recordid" value="' . $RecordPerms['recordid'] . '" />
<input type="hidden" name="link_id" value="' . $RecordPerms['link_id'] . '" />
<input type="hidden" name="module" value="' . $RecordPerms['module'] . '" />
<input type="hidden" name="table_name" value="' . $RecordPerms['table_name'] . '" />
<input type="hidden" name="updateid" value="' . $RecordPerms['updateid'] . '" />
<input type="hidden" name="type" value="' . $RecordPerms['type'] . '" />';

    if ($RecordPerms['group_id']) {
        echo '
    <input type="hidden" name="group_id" value="' . $RecordPerms['group_id'] . '" />';
    }

    echo '
<input type="hidden" name="rbWhat" value="save" />';

    RecordPermsSection($RecordPerms);

    echo '<div class="button_wrapper">';

    echo '
            <input type="submit" value="' . _fdtk('btn_save') . '" onclick="Javascript:document.frmRecordPerms.rbWhat.value=\'save\'" />
            <input type="submit" value="' . _fdtk('btn_cancel') . '" onclick="Javascript:document.frmRecordPerms.rbWhat.value=\'cancel\'" />
';
    if ($RecordPerms['recordid']) {
        echo '<input type="submit" value="Delete" onClick="Javascript:frmRecordPerms.rbWhat.value=\'delete\'" />';
    }

    echo '
    </form>';

    footer();

    obExit();
}

function RecordPermsSection($RecordPerms)
{
    $ListArray = [];
    $sqlHelper = new SqlInClauseHelper();

    if ($RecordPerms['type'] == 'user') {
        if ($RecordPerms['module'] == 'REP') {
            $sql = 'SELECT recordid, initials, fullname, jobtitle as use_jobtitle, sta_forenames as use_forenames,
                        sta_surname as use_surname, sta_title as use_title
                FROM staff
                WHERE ' . GetActiveStaffWhereClause() . '
                AND recordid NOT IN (SELECT user_id FROM web_packaged_report_users WHERE web_packaged_report_id = :link_id)';
            $parameters = ['link_id' => $RecordPerms['link_id']];
        } else {
            $sql = 'SELECT recordid, initials, fullname, jobtitle as use_jobtitle, sta_forenames as use_forenames,
                        sta_surname as use_surname, sta_title as use_title
                FROM staff
                WHERE ' . GetActiveStaffWhereClause() . '
                AND recordid NOT IN (SELECT use_id FROM sec_record_permissions WHERE module = :module and link_id = :link_id)
                ORDER BY use_surname, use_forenames';
            $parameters = ['module' => $RecordPerms['module'], 'link_id' => $RecordPerms['link_id']];
        }

        $users = DatixDBQuery::PDO_fetch_all($sql, $parameters);

        // Sort contact list into surname order (case insensitive)
        usort($users, function ($a, $b) {
            return strcasecmp($a['use_surname'], $b['use_surname']);
        });

        foreach ($users as $row) {
            $ListArray[$row['recordid']] = FormatUserNameForList(['data' => $row]);
        }
    } elseif ($RecordPerms['type'] == 'group') {
        $GroupList = GetRecordAccessGroupList(['recordid' => $RecordPerms['link_id'], 'module' => $RecordPerms['module'], 'table' => $RecordPerms['table_name']]);

        foreach ($GroupList as $Group) {
            if ($RecordPerms['group_id'] != $Group['group_id']) {
                $CurrentGroups[] = $Group['group_id'];
            }
        }

        $sql = 'SELECT recordid as group_id, grp_code
            FROM sec_groups ' .
            (!empty($CurrentGroups) ? 'WHERE ' . $sqlHelper->notInClause('recordid', $CurrentGroups) : '') . ' ORDER BY grp_code';

        $request = db_query($sql);

        while ($row = db_fetch_array($request)) {
            $ListArray[$row['group_id']] = $row['grp_code'];
        }
    } elseif ($RecordPerms['type'] == 'profile') {
        $ProfileList = GetRecordAccessProfileList(['recordid' => $RecordPerms['link_id'], 'module' => $RecordPerms['module'], 'table' => $RecordPerms['table_name']]);

        foreach ($ProfileList as $Profile) {
            if ($RecordPerms['profile_id'] != $Profile['profile_id']) {
                $CurrentProfiles[] = $Profile['profile_id'];
            }
        }

        $sql = 'SELECT recordid as profile_id, pfl_name
            FROM profiles ' .
            (!empty($CurrentProfiles) ? 'WHERE ' . $sqlHelper->notInClause('recordid', $CurrentProfiles) : '') . ' ORDER BY pfl_name';

        $request = db_query($sql);

        while ($row = db_fetch_array($request)) {
            $ListArray[$row['profile_id']] = $row['pfl_name'];
        }
    }


    $CTable = FormTableFactory::create();
    if ($RecordPerms['module'] == 'REP') {
        $CTable->makeTitleRow(_fdtk($RecordPerms['type'] . '_report_access'));
    } else {
        $CTable->makeTitleRow(_fdtk($RecordPerms['type'] . '_record_access'));
    }

    if ($RecordPerms['error']) {
        $CTable->Contents .= '<tr><td class="windowbg2" colspan="2"><font color="red">' . $RecordPerms['error']['message'] . '</font></td></tr>';
    }

    if ($RecordPerms['type'] == 'user') {
        $field = SelectFieldFactory::createSelectField('use_id', $RecordPerms['module'], $RecordPerms['use_id'], '', true);
        $field->setCustomCodes($ListArray);
        $field->setIgnoreMaxLength();
        $CTable->makeRow(_fdtk('add_users') . GetValidationErrors($RecordPerms, 'use_id'), $field);
    } elseif ($RecordPerms['type'] == 'group') {
        $field = SelectFieldFactory::createSelectField('group_id', $RecordPerms['module'], $RecordPerms['group_id'], '', true);
        $field->setCustomCodes($ListArray);
        $field->setIgnoreMaxLength();
        $CTable->makeRow(_fdtk('add_security_groups') . GetValidationErrors($RecordPerms, 'group_id'), $field);
    } elseif ($RecordPerms['type'] == 'profile') {
        $field = SelectFieldFactory::createSelectField('profile_id', $RecordPerms['module'], $RecordPerms['profile_id'], '', true);
        $field->setCustomCodes($ListArray);
        $field->setIgnoreMaxLength();
        $CTable->makeRow(_fdtk('add_profiles') . GetValidationErrors($RecordPerms, 'profile_id'), $field);
    }

    $CTable->makeTable();

    echo '
        <tr>
            <td>' . $CTable->getFormTable() . '</td>
        </tr>
    ';
}

// Alternate function for reports, which now use a different set of tables.
function SaveRecordPerms_Report()
{
    global $scripturl, $ModuleDefs;

    $RecordPerms = QuotePostArray($_POST);

    if ($RecordPerms['rbWhat'] == 'cancel') {
        $location = $_SESSION['record_permissions']['url_from'];
        Container::get(Response::class)->redirect($location);
    } else {
        if ($RecordPerms['type'] == 'user') {
            if (empty($RecordPerms['use_id'])) {
                AddValidationMessage('use_id', 'Please select a user');

                $location = $scripturl . '?action=editrecordperms&type=' . $RecordPerms['type'] . '&module=' . $RecordPerms['module'] . '&table_name=' . $RecordPerms['table_name'] . '&link_id=' . $RecordPerms['link_id'];
                Container::get(Response::class)->redirect($location);
            }

            foreach ($RecordPerms['use_id'] as $ConID) {
                // Check whether link already exists between user and record
                $sql = "SELECT *
                FROM web_packaged_report_users
                WHERE
                web_packaged_report_id = {$RecordPerms['link_id']}
                AND user_id = {$ConID}";

                $request = db_query($sql);

                if ($row = db_fetch_array($request)) {
                    continue;
                }

                $sql = "insert into web_packaged_report_users (web_packaged_report_id, user_id)
                    values (
                    '{$RecordPerms['link_id']}',
                    '{$ConID}')";
                db_query($sql);
            }
        } elseif ($RecordPerms['type'] == 'group') {
            if (empty($RecordPerms['group_id'])) {
                AddValidationMessage('group_id', 'Please select a group');
                $location = $scripturl . '?action=editrecordperms&type=' . $RecordPerms['type'] . '&module=' . $RecordPerms['module'] . '&table_name=' . $RecordPerms['table_name'] . '&link_id=' . $RecordPerms['link_id'];
                Container::get(Response::class)->redirect($location);
            }

            foreach ($RecordPerms['group_id'] as $GroupID) {
                // Check whether link already exists between user and record
                $sql = "SELECT *
                FROM web_packaged_report_groups
                WHERE
                web_packaged_report_id = {$RecordPerms['link_id']}
                AND group_id = {$GroupID}";

                $request = db_query($sql);

                if ($row = db_fetch_array($request)) {
                    continue;
                }

                $sql = "insert into web_packaged_report_groups (web_packaged_report_id, group_id)
                    values (
                    '{$RecordPerms['link_id']}',
                    '{$GroupID}')";
                db_query($sql);
            }
        } elseif ($RecordPerms['type'] == 'profile') {
            if (empty($RecordPerms['profile_id'])) {
                AddValidationMessage('profile_id', 'Please select a profile');
                $location = $scripturl . '?action=editrecordperms&type=' . $RecordPerms['type'] . '&module=' . $RecordPerms['module'] . '&table_name=' . $RecordPerms['table_name'] . '&link_id=' . $RecordPerms['link_id'];
                Container::get(Response::class)->redirect($location);
            }

            foreach ($RecordPerms['profile_id'] as $ProfileID) {
                // Check whether link already exists between user and record
                $sql = "SELECT *
                FROM web_packaged_report_profiles
                WHERE
                web_packaged_report_id = {$RecordPerms['link_id']}
                AND profile_id = {$ProfileID}";

                $request = db_query($sql);

                if ($row = db_fetch_array($request)) {
                    continue;
                }

                $sql = "insert into web_packaged_report_profiles (web_packaged_report_id, profile_id)
                    values (
                    '{$RecordPerms['link_id']}',
                    '{$ProfileID}')";
                db_query($sql);
            }
        }
    }
}

/**
 * @return never
 */
function SaveRecordPerms(): void
{
    global $scripturl, $ModuleDefs;

    $RecordPerms = QuotePostArray($_POST);

    if ($RecordPerms['module'] == 'REP') {
        // Hack until this functionality is refactored.
        SaveRecordPerms_Report();
    } else {
        if ($RecordPerms['rbWhat'] == 'cancel') {
            $location = $_SESSION['record_permissions']['url_from'];
            Container::get(Response::class)->redirect($location);
        } elseif ($RecordPerms['rbWhat'] == 'delete') {
            if ($RecordPerms['type'] == 'user') {
                $sql = "delete from sec_record_permissions where recordid = {$RecordPerms['recordid']}";
            } elseif ($RecordPerms['type'] == 'group') {
                $sql = "delete from sec_group_record_permissions where recordid = {$RecordPerms['recordid']}";
            }
        } else {
            $updateIdGenerator = new UpdateIdGenerator();

            if ($RecordPerms['type'] == 'user') {
                if (empty($RecordPerms['use_id'])) {
                    AddValidationMessage('use_id', 'Please select a user');

                    $location= $scripturl . '?action=editrecordperms&type=' . $RecordPerms['type'] . '&module=' . $RecordPerms['module'] . '&table_name=' . $RecordPerms['table_name'] . '&link_id=' . $RecordPerms['link_id'];
                    Container::get(Response::class)->redirect($location);
                }

                foreach ($RecordPerms['use_id'] as $userId) {
                    // Check whether link already exists between user and record
                    $sql = "SELECT *
                    FROM sec_record_permissions
                    WHERE
                    module = '{$RecordPerms['module']}'
                    AND tablename = '{$RecordPerms['table_name']}'
                    AND link_id = {$RecordPerms['link_id']}
                    AND use_id = {$userId}";

                    $request = db_query($sql);

                    if ($row = db_fetch_array($request)) {
                        continue;
                    }

                    $sql = "insert into sec_record_permissions (module, tablename, link_id, use_id, updatedby, updateddate, updateid)
                        values (
                        '{$RecordPerms['module']}',
                        '{$RecordPerms['table_name']}',
                        {$RecordPerms['link_id']},
                        {$userId},
                        '{$_SESSION['initials']}',
                        '" . date('d-M-Y H:i:s') . "',
                        '" . $updateIdGenerator->generateUpdateId($row['updateid']) . "')";

                    db_query($sql);
                }
            } elseif ($RecordPerms['type'] == 'group') {
                if (empty($RecordPerms['group_id'])) {
                    AddValidationMessage('group_id', 'Please select a group');
                    $location = $scripturl . '?action=editrecordperms&type=' . $RecordPerms['type'] . '&module=' . $RecordPerms['module'] . '&table_name=' . $RecordPerms['table_name'] . '&link_id=' . $RecordPerms['link_id'];
                    Container::get(Response::class)->redirect($location);
                }

                foreach ($RecordPerms['group_id'] as $GroupID) {
                    // Check whether link already exists between group and record
                    $sql = "SELECT *
                    FROM sec_group_record_permissions
                    WHERE
                    module = '{$RecordPerms['module']}'
                    AND tablename = '{$RecordPerms['table_name']}'
                    AND link_id = {$RecordPerms['link_id']}
                    AND group_id = {$GroupID}";

                    $request = db_query($sql);

                    if ($row = db_fetch_array($request)) {
                        continue;
                    }

                    $sql = "insert into sec_group_record_permissions (module, tablename, link_id, group_id, updatedby, updateddate, updateid)
                    values (
                    '{$RecordPerms['module']}',
                    '{$RecordPerms['table_name']}',
                    {$RecordPerms['link_id']},
                    {$GroupID},
                    '{$_SESSION['initials']}',
                    '" . date('d-M-Y H:i:s') . "',
                    '" . $updateIdGenerator->generateUpdateId($row['updateid']) . "')";

                    db_query($sql);
                }
            }
        }
    }

    $location = $_SESSION['record_permissions']['url_from'];
    Container::get(Response::class)->redirect($location);
}

function UserCanLinkUsers($aParams)
{
    global $ModuleDefs;

    if ($aParams['module'] == 'REP') {  // in Reports module (packaged reports) access is always available
        return true;
    }

    // In 'Document Template Administration, if the user is an admin, they will allways be able to link,
    // @see DW-10896
    if ($aParams['module'] == 'TEM' && IsSubAdmin(true)) {
        return true;
    }

    if ($_SESSION['AdminUser']) { // user permissions explicity grant access
        return true;
    }

    // user permissions must be set to 'Choose' to get to this point. If 'GRANT_ACCESS_PERMISSIONS' is
    // set to 'H', then we can still allow them to edit if they are the handler. Otherwise, return false.

    if (GetParm('GRANT_ACCESS_PERMISSIONS') == 'H') {
        $sql = 'select ' . $ModuleDefs[$aParams['module']]['FIELD_NAMES']['HANDLER'] . " as handler
        from {$aParams['table']}
        where recordid = {$aParams['recordid']}";

        $row = DatixDBQuery::PDO_fetch($sql);

        if ($row && $row['handler'] && $row['handler'] == $_SESSION['initials']) {
            return true;
        }
    } else {
        return false;
    }
}

function GetRecordAccessUserList($aParams)
{
    $UserList = [];

    if ($aParams['module'] == 'REP') {
        $sql = 'select c.recordid, c.use_surname, c.use_forenames, c.use_title, c.recordid as use_id
        from users_main c
        join web_packaged_report_users p on g.recordid = p.group_id
        where
        p.web_packaged_report_id = :link_id
        order by c.use_surname, c.use_forenames, c.use_title';

        $parameters = [
            'link_id' => $aParams['recordid'],
        ];
    } else {
        $sql = 'SELECT p.recordid, c.use_surname, c.use_forenames, c.use_title, c.recordid AS use_id
        FROM users_main c
        JOIN sec_record_permissions p ON c.recordid = p.use_id
        WHERE
        p.module = :module
        AND p.tablename = :tablename
        AND p.link_id = :link_id
        ORDER BY c.use_surname, c.use_forenames, c.use_title';

        $parameters = [
            'module' => $aParams['module'],
            'tablename' => $aParams['table'],
            'link_id' => $aParams['recordid'],
        ];
    }

    $result = DatixDBQuery::PDO_fetch_all($sql, $parameters);

    foreach ($result as $row) {
        $UserList[] = $row;
    }

    return $UserList;
}

function GetRecordAccessGroupList($aParams)
{
    $GroupList = [];

    if ($aParams['module'] == 'REP') {
        $sql = 'select g.recordid, g.grp_code, g.recordid as grp_id
        from sec_groups g
        join web_packaged_report_groups p on g.recordid = p.group_id
        where
        p.web_packaged_report_id = :link_id
        order by g.grp_code';

        $parameters = [
            'link_id' => $aParams['recordid'],
        ];
    } else {
        $sql = 'select p.recordid, p.group_id, g.grp_code, g.recordid as grp_id
        from sec_groups g
        join sec_group_record_permissions p on g.recordid = p.group_id
        where
        p.module = :module
        AND p.tablename = :tablename
        and p.link_id = :link_id
        order by g.grp_code';

        $parameters = [
            'module' => $aParams['module'],
            'tablename' => $aParams['table'],
            'link_id' => $aParams['recordid'],
        ];
    }

    $result = DatixDBQuery::PDO_fetch_all($sql, $parameters);

    foreach ($result as $row) {
        $GroupList[] = $row;
    }

    return $GroupList;
}

// Only used for reporting
function GetRecordAccessProfileList($aParams)
{
    $ProfileList = [];

    if ($aParams['module'] != 'REP') {
        return false;
    }

    $sql = 'select p.profile_id, g.pfl_name, g.recordid as grp_id
    from profiles g
    join web_packaged_report_profiles p on g.recordid = p.profile_id
    where
    p.web_packaged_report_id = :link_id
    order by g.pfl_name';


    $result = DatixDBQuery::PDO_fetch_all($sql, [
        'link_id' => $aParams['recordid'],
    ]);

    foreach ($result as $row) {
        $ProfileList[] = $row;
    }

    return $ProfileList;
}
