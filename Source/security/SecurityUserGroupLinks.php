<?php

use app\framework\DBALConnectionFactory;
use app\models\generic\valueObjects\Module;
use app\services\audit\FullAudit;
use app\services\audit\FullAuditFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTableFactory;

function UnlinkUserGroups()
{
    if (!empty($_POST['link_id']) && in_array($_POST['type'], ['user', 'profile'])) {
        if (is_array($_POST['link_id'])) {
            $link_id = implode(',', Sanitize::SanitizeStringArray($_POST['link_id']));
        } else {
            $link_id = Sanitize::SanitizeString($_POST['link_id']);
        }

        $db = (new DBALConnectionFactory())->getInstance();
        $fullAudit = (new FullAuditFactory())->create($db);

        if ($_POST['type'] == 'user') {
            $auditDetailsSql = "SELECT recordid, grp_id, use_id FROM sec_staff_group WHERE recordid IN ({$link_id})";
            $tableName = 'sec_staff_group';
        } elseif ($_POST['type'] == 'profile') {
            $auditDetailsSql = "SELECT recordid, lpg_group, lpg_profile FROM link_profile_group where recordid in ({$link_id})";
            $tableName = 'link_profile_group';
        }

        $results = $db->fetchAllAssociative($auditDetailsSql);

        $deleteSql = "delete from {$tableName} where recordid in ({$link_id})";
        $db->executeStatement($deleteSql);

        foreach ($results as $result) {
            $fullAudit->add('ADM', $result['recordid'], FullAudit::SOURCE_WEB . " UNLINK USER GROUP ({$tableName})", $result);
        }
    }
}

// unlink users from profiles
function UnlinkProfiles()
{
    if (!empty($_POST['link_id'])) {
        if (is_array($_POST['link_id'])) {
            $link_id = implode(',', Sanitize::SanitizeStringArray($_POST['link_id']));
        } else {
            $link_id = Sanitize::SanitizeString($_POST['link_id']);
        }

        $db = (new DBALConnectionFactory())->getInstance();
        $fullAudit = (new FullAuditFactory())->create($db);

        $auditDetailsSql = "SELECT recordid, sta_profile FROM users_main WHERE recordid IN ({$link_id})";
        $results = $db->fetchAllAssociative($auditDetailsSql);

        $db->executeStatement('UPDATE users_main SET sta_profile = NULL WHERE recordid IN (' . $link_id . ')');

        foreach ($results as $result) {
            $fullAudit->add('ADM', $result['recordid'], FullAudit::SOURCE_WEB . ' REMOVE USER FROM PROFILE', $result);
        }
    }
}

function SectionAddGroup($Link)
{
    $CTable = FormTableFactory::create();

    $field = SelectFieldFactory::createSelectField('grp_id', 'ADM', $Link['grp_id'], 'Edit', true);
    $field->setSelectFunction('getSecurityGroups', ['use_id' => '"' . $Link['use_id'] . '"', 'pfl_id' => '"' . $Link['pfl_id'] . '"']);
    $field->setIgnoreMaxLength();
    $CTable->makeRow(((string) $error['doc_type']) . _fdtk('security_group') . ':', $field);

    $CTable->makeTable();

    echo $CTable->getFormTable();
}

function SectionAddUser($Link)
{
    $Where = "
    initials is not null AND initials != ''
	AND " . GetActiveStaffWhereClause() . "
    AND recordid NOT IN (
        SELECT use_id
        FROM sec_staff_group
        WHERE grp_id = {$Link['grp_id']})";

    $Where = MakeSecurityWhereClause($Where, 'USE');

    $sql = "SELECT recordid as use_id, use_forenames, use_surname, use_title
    FROM staff
    WHERE {$Where}
    ORDER BY use_surname, use_forenames, use_title";

    $request = db_query($sql);
    $Users = [];
    while ($row = db_fetch_array($request)) {
        $Users[$row['use_id']] = UnicodeString::trim("{$row['use_surname']}, {$row['use_forenames']} {$row['use_title']}");
    }

    $CTable = FormTableFactory::create();

    $field = SelectFieldFactory::createSelectField('use_id', 'USE', $Link['use_id'], '', true);
    $field->setCustomCodes($Users);
    $field->setIgnoreMaxLength();
    $CTable->makeRow("{$error['doc_type']}User:", $field);

    $CTable->makeTable();

    echo '
        <li>
            <ol>' . $CTable->getFormTable() . '</ol>
        </li>
    ';
}

function SectionAddUserProfile($Link)
{
    $where = "
    initials IS NOT NULL AND initials != ''
	AND (sta_profile IS NULL OR sta_profile = '')
	AND " . GetActiveStaffWhereClause();

    $where = MakeSecurityWhereClause($where, Module::USERS);

    $sql = "SELECT recordid as use_id, use_forenames, use_surname, use_title
	FROM staff
	WHERE {$where}
	ORDER BY use_surname, use_forenames, use_title";

    $results = DatixDBQuery::PDO_fetch_all($sql, []);

    $users = [];
    foreach ($results as $row) {
        $users[$row['use_id']] = UnicodeString::trim("{$row['use_surname']}, {$row['use_forenames']} {$row['use_title']}");
    }

    $CTable = FormTableFactory::create();

    $field = SelectFieldFactory::createSelectField('use_id', Module::USERS, '', '', true);
    $field->setCustomCodes($users);
    $field->setIgnoreMaxLength();
    $CTable->makeRow("{$error['doc_type']}User:", $field);

    $CTable->makeTable();

    echo $CTable->getFormTable();
}
