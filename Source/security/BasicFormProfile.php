<?php

use app\models\generic\valueObjects\Module;
use app\models\globals\ProgNotesEditGlobal;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\safeguarding\models\SafeguardingAdminFields;
use src\system\container\facade\Container;

$progNotesEditField = SelectFieldFactory::createSelectField(
    ProgNotesEditGlobal::GLOBAL_NAME,
    'ADM',
    $data[ProgNotesEditGlobal::GLOBAL_NAME],
    $FormType,
);
$progNotesEditField->setCustomCodes(['OWN' => 'Edit own notes only', 'ALL' => 'Edit all notes']);

$isFullAdmin = (new UserSessionFactory())->create()->isFullAdmin();
$restrictAdminParameters = Container::get(Registry::class)->getParm('RESTRICT_ADMIN_PARAMETERS', 'N')->toBool();
$showRestrictedAdminParameters = $isFullAdmin || !$restrictAdminParameters;

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],
    'details' => [
        'Title' => _fdtk('profile_details'),
        'Rows' => [
            ['Name' => 'recordid', 'Condition' => ($data['recordid'] != '')],
            'pfl_name',
            'pfl_description',
        ],
    ],
    'parameters' => [
        'Title' => '<span margin-right:5px;">' . _fdtk('profile_configuration_parameters') . '</span>',
        'TitleSuffix' => ($FormType != 'Design' ? ' ' . getUserSecTitleDropdown() : ''),
        'TitleDropdown' => ($FormType != 'Design' ? true : false),
        'Condition' => ($FormType != 'Search'),
        'Rows' => [],
    ],
    'ALL_MODULES_parameters' => [
        'Title' => _fdtk('profile_all_modules'),
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search'),
        'Rows' => [
            'LOGOUT_DEFAULT_MODULE',
            'CUSTOM_REPORT_BUILDER',
            [
                'Type' => 'formfield',
                'Name' => ProgNotesEditGlobal::GLOBAL_NAME,
                'FormField' => $progNotesEditField,
            ],
            'ENABLE_GENERATE',
            ['Name' => 'ENABLE_BATCH_UPDATE', 'Condition' => $showRestrictedAdminParameters],
            'AUTOPOPULATE_USER_LOCATION',
        ],
    ],
    'ACT_parameters' => [
        'Title' => $ModuleDefs['ACT']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('ACT')),
        'Rows' => [
            'ACT_SEARCH_DEFAULT',
            'ACT_OWN_ONLY',
            ['Name' => 'ACT_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'DELETE_ACT_CHAIN',
        ],
    ],
    'INC_parameters' => [
        'Title' => $ModuleDefs['INC']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('INC')),
        'Rows' => [
            ['Name' => 'DIF1_ONLY_FORM', 'OverrideFormDesignGlobal' => true],
            'DIF2_DEFAULT',
            'INC_LISTING_ID',
            'DIF2_SEARCH_DEFAULT',
            'DIF_SHOW_REJECT_BTN',
            'DIF2_HIDE_CONTACTS',
            'DIF_OWN_ONLY',
            'INC_SHOW_AUDIT',
            ['Name' => 'INC_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COPY_INCIDENTS',
            'INC_DELETE_DOCS',
            'INC_CAN_DELETE_OWN_DOCUMENTS',
            'INC_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'INC_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries'),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data['INC_SAVED_QUERIES'], null, $FormType, 'INC'),
            ],
            \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS,
        ],
    ],
    'COM_parameters' => [
        'Title' => $ModuleDefs['COM']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('COM')),
        'Rows' => [
            'COM_SHOW_ADD_NEW',
            'COM1_DEFAULT',
            'COM2_DEFAULT',
            'COM_LISTING_ID',
            'COM2_SEARCH_DEFAULT',
            'COM_SHOW_REJECT_BTN',
            'COM_OWN_ONLY',
            'COM_SHOW_AUDIT',
            ['Name' => 'COM_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COM_DELETE_DOCS',
            'COM_CAN_DELETE_OWN_DOCUMENTS',
            'COM_KO41_CAN_GENERATE_REPORT',
            'COM_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'COM_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries'),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data['COM_SAVED_QUERIES'], null, $FormType, 'COM'),
            ],
            'SHOW_REOPEN',
            'EDITABLE_CHAIN_DUE_DATES',
            'COPY_COM',
        ],
    ],
    'CLA_parameters' => [
        'Title' => $ModuleDefs['CLA']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('CLA')),
        'Rows' => [
            'CLAIM1_DEFAULT',
            'CLAIM2_DEFAULT',
            'CLA_LISTING_ID',
            'CLAIM2_SEARCH_DEFAULT',
            'CLA_OWN_ONLY',
            'CLA_SHOW_AUDIT',
            ['Name' => 'CLA_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'CLA_DELETE_DOCS',
            'CLA_CAN_DELETE_OWN_DOCUMENTS',
            'CLA_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'CLA_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries'),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data['CLA_SAVED_QUERIES'], null, $FormType, 'CLA'),
            ],
            'COPY_CLA',
        ],
    ],
    'MOR_parameters' => [
        'Title' => $ModuleDefs['MOR']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('MOR')),
        'Rows' => [
            'MOR1_DEFAULT',
            'MOR2_DEFAULT',
            'MOR2_SEARCH_DEFAULT',
            'MOR_LISTING_ID',
            'MOR_SHOW_AUDIT',
            ['Name' => 'MOR_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'MOR_DELETE_DOCS',
            'MOR_CAN_DELETE_OWN_DOCUMENTS',
            'MOR_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'MOR_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries'),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data['MOR_SAVED_QUERIES'], null, $FormType, 'MOR'),
            ],
            'COPY_MOR',
        ],
    ],
    'PAY_parameters' => [
        'Title' => $ModuleDefs['PAY']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('CLA')),
        'Rows' => [
            'PAY2_SEARCH_DEFAULT',
            'DELETE_PAY',
        ],
    ],
    'CON_parameters' => [
        'Title' => $ModuleDefs['CON']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('CON')),
        'Rows' => [
            'CON_SHOW_REJECT_BTN',
            ['Name' => 'CON_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'CON_ALLOW_MERGE_DUPLICATES',
        ],
    ],
    'ADM_parameters' => [
        'Title' => $ModuleDefs['ADM']['NAME'],
        'NoTitle' => true,
        'Condition' => $FormType != 'Search' && ModIsLicensed('ADM') && $showRestrictedAdminParameters,
        'Rows' => [
            ['Name' => 'FULL_ADMIN', 'Condition' => (new UserSessionFactory())->create()->isFullAdmin(), 'Type' => 'yesno'],
            'ADM_DEFAULT',
            'ADM_LISTING_ID',
            ['Name' => 'ADM_PROFILES', 'Title' => 'Profiles', 'Type' => 'custom', 'HTML' => MakeProfileSetupField($data['ADM_PROFILES'])],
            'ADM_GROUP_SETUP',
            'ADM_CAN_DELEGATE_PERMISSIONS',
            'ADM_CAN_DELEGATE_ACCOUNTS',
            'ADM_NO_ADMIN_REPORTS',
        ],
    ],
    'POL_parameters' => [
        'Title' => $ModuleDefs['POL']['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != 'Search' && ModIsLicensed('POL')),
        'Rows' => [
            'POL2_DEFAULT',
            'POL_LISTING_ID',
            'POL2_SEARCH_DEFAULT',
            'POL_OWN_ONLY',
            'POL_SHOW_AUDIT',
            ['Name' => 'POL_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COPY_POL',
            'POL_DELETE_DOCS',
        ],
    ],
    'RED_parameters' => [
        'Title' => $ModuleDefs[Module::REDRESS]['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType != FormTable::MODE_SEARCH && ModIsLicensed(Module::REDRESS)),
        'Rows' => [
            'RED1_DEFAULT',
            'RED2_DEFAULT',
            'RED2_SEARCH_DEFAULT',
            'RED_LISTING_ID',
            'RED_SHOW_AUDIT',
            ['Name' => 'RED_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'RED_DELETE_DOCS',
            'RED_CAN_DELETE_OWN_DOCUMENTS',
            'RED_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'RED_SAVED_QUERIES',
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data['RED_SAVED_QUERIES'], null, $FormType, Module::REDRESS),
            ],
            'COPY_RED',
        ],
    ],
    'SFG_parameters' => [
        'Title' => $ModuleDefs[Module::SAFEGUARDING]['NAME'],
        'NoTitle' => true,
        'Condition' => ($FormType !== FormTable::MODE_SEARCH && ModIsLicensed(Module::SAFEGUARDING)),
        'Rows' => [
            SafeguardingAdminFields::DEFAULT_LEVEL_1_FORM,
            SafeguardingAdminFields::DEFAULT_LEVEL_2_FORM,
            SafeguardingAdminFields::DEFAULT_SEARCH_FORM,
            SafeguardingAdminFields::ENABLE_PINNED_QUERIES,
            [
                'Name' => SafeguardingAdminFields::SELECT_PINNED_QUERIES,
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($data[SafeguardingAdminFields::SELECT_PINNED_QUERIES], null, $FormType, Module::SAFEGUARDING),
            ],
            SafeguardingAdminFields::ENABLE_RECORD_COPY,
            SafeguardingAdminFields::SHOW_AUDIT,
            SafeguardingAdminFields::SHOW_ADD_NEW,
        ],
    ],
    'groups' => [
        'Title' => _fdtk('profile_security_groups'),
        'Condition' => ($FormType != 'Search'),
        'Include' => 'Source/security/SecurityBase.php',
        'Function' => 'sectionProfileSecurityGroups',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'users' => [
        'Title' => _fdtk('profile_users'),
        'NotModes' => ['New', 'Search'],
        'Include' => 'Source/security/Profiles.php',
        'Function' => 'sectionProfileUsers',
        'NoFieldAdditions' => true,
        'NewPanel' => true,
        'Rows' => [],
        'Condition' => $isFullAdmin || ($data['FULL_ADMIN'] !== 'Y'),
    ],
];

return $FormArray;
