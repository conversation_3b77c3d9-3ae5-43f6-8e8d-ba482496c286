<?php

use app\models\globals\ProgNotesEditGlobal;

$GLOBALS['FormTitle'][7] = 'Datix User Form';

$GLOBALS['Show_all_section'] = 'N';

$GLOBALS['HideFields'] = [
    'contact' => true,
    'profile' => true,
];

$GLOBALS['MandatoryFields'] = [
];

$GLOBALS['UserExtraText'] = [
    'LOGOUT_DEFAULT_MODULE' => [7 => 'Select module to be presented to user upon logging out.'],
    'CUSTOM_REPORT_BUILDER' => [7 => 'Identify whether users will have access to the customised report building section when accessing the reporting area.'],
    ProgNotesEditGlobal::GLOBAL_NAME => [7 => 'Users can be granted access to edit any progress note, or only those that they have created themselves.'],
    'ENABLE_GENERATE' => [7 => 'Users can be granted access to generate a new record in another module if available and they have permission to add records.'],
    'ACT_SEARCH_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'ACT_OWN_ONLY' => [7 => 'If you set this option, this user will only be able to see actions where their name appears in \'Responsibility\'.'],
    'DIF2_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'DIF2_SEARCH_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'DIF2_HIDE_CONTACTS' => [7 => 'If set to Yes, the Contacts side-menu item and linked contacts page will be hidden for this user.  Note: this setting affects only those users with an access level of \'DIF2 read-only access\' or \'DIF2 access only - no review of DIF1 forms\'.'],
    'DIF_OWN_ONLY' => [7 => 'If you set this option, the user will only be able to see records where their name appears in the Handler dropdown. This option does not apply to users with input-only and final approval access.'],
    'RISK2_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'RISK2_SEARCH_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'RISK_OWN_ONLY' => [7 => 'If you set this option, the user will only be able to see records where their name appears in the Handler dropdown. This option does not apply to users with input-only and final approval access.'],
    'COM1_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'COM2_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'COM2_SEARCH_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'COM_OWN_ONLY' => [7 => 'If you set this option, the user will only be able to see records where their name appears in the Handler dropdown. This option does not apply to users with input-only and final approval access.'],
    'use_SEARCH_DEFAULT' => [7 => 'This option overrides the setting in \'Design forms\'.'],
    'ADM_PROFILES' => [7 => 'Identify the profiles which the user will be able to apply to users they manage.  Leaving this field blank will give the user access to all profiles'],
    'parameters' => [7 => 'Any settings made here will override settings selected on the profile selected for this user.'],
];

$GLOBALS['HelpTexts'] = [
    'sta_profile' => [7 => 'Any configuration settings made on the user screen will override any settings made on the profile.  This allows you to make specific changes for individual users, while having the majority of their settings based upon a profile'],
    \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS => [7 => 'Users can be granted permission to save incident records without completing mandatory fields. This excludes any records in Final approval, mandatory fields must be completed in this status'],
];

$GLOBALS['FieldOrders'] = [
    'details' => [
        1 => 'login',
        2 => 'initials',
        3 => 'use_title',
        4 => 'use_forenames',
        5 => 'use_surname',
        6 => 'use_jobtitle',
        7 => 'use_email',
    ],
];

$GLOBALS['NewPanels'] = [
    'loctype' => true,
    'parameters' => true,
    'groups' => true,
];

$GLOBALS['MoveFieldsToSections'] = [
    'use_title' => [
        'Original' => 'contact',
        'New' => 'details',
    ],
    'use_forenames' => [
        'Original' => 'contact',
        'New' => 'details',
    ],
    'use_surname' => [
        'Original' => 'contact',
        'New' => 'details',
    ],
    'use_jobtitle' => [
        'Original' => 'contact',
        'New' => 'details',
    ],
    'use_email' => [
        'Original' => 'contact',
        'New' => 'details',
    ],
];
