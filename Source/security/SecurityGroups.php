<?php

use app\models\generic\valueObjects\Module;
use src\admin\services\CapturePermissionService;
use src\admin\services\CapturePermissionServiceFactory;
use src\component\field\CustomFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\component\form\FormTableMakeFieldService;
use src\framework\controller\Request;
use src\framework\query\Query;
use src\framework\query\SqlWriter;
use src\framework\query\Where;
use src\framework\registry\Registry;
use src\framework\session\WhereClauseSessionFactory;
use src\security\CompatEscaper;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

function saveGroupPermission($grp_id, $permCode, $permValue)
{
    if (empty($permValue)) {
        DatixDBQuery::PDO_query('DELETE FROM sec_group_permissions WHERE grp_id = :grp_id AND item_code = :item_code', ['grp_id' => $grp_id, 'item_code' => $permCode]);
    } else {
        if ($permValue == 'on') {
            $permValue = 'Y';
        }

        $sql = 'UPDATE sec_group_permissions SET
		perm_value = :perm_value,
		updateddate = :updateddate,
		updatedby = :updatedby
		WHERE
		grp_id = :grp_id
		AND item_code = :item_code';

        $PDOParameters = [
            'perm_value' => $permValue,
            'updateddate' => date('Y-m-d H:i:s'),
            'updatedby' => $_SESSION['initials'],
            'grp_id' => $grp_id,
            'item_code' => $permCode,
        ];

        $query = new DatixDBQuery($sql);
        $query->prepareAndExecute($PDOParameters);

        if ($query->getRowsAffected() == 0) {
            DatixDBQuery::PDO_build_and_insert('sec_group_permissions', ['grp_id' => $grp_id, 'item_code' => $permCode, 'perm_value' => $permValue, 'updateddate' => date('Y-m-d H:i:s'), 'updatedby' => $_SESSION['initials']]);
        }

        return $permCode;
    }

    return '';
}

function MakeGroupWhereRow($Module, $Where, $FormType, $error = '')
{
    // $QBE : "Query By Example", indicates that we are coming back from
    //            defining a WHERE close from a query by example and we need to
    //            set the "WHERE" clause field for the relevant module

    $QBE = (isset($_SESSION['security_group']) && $_SESSION['security_group']['success'] == true && $_SESSION['security_group']['module'] == $Module);

    $ReadOnly = in_array($FormType, ['ReadOnly', 'Print'], true);
    $registry = Container::get(Registry::class);
    $whereClauseSession = (new WhereClauseSessionFactory())->create();

    // Remove REP_APPROVE WHERE clause if present
    $ModuleWhere = $_SESSION[$Module]['WHERE'];
    if ($_SESSION[$Module]['NEW_WHERE'] instanceof Where) {
        $table = Container::get(ModuleDefs::class)[$Module]->getDbReadObj();

        if (!$_SESSION[$Module]['NEW_WHERE']->isLinkedDataSearch($table)) {
            $where = clone $_SESSION[$Module]['NEW_WHERE'];

            $query = (new Query())->select([$table . '.recordid'])
                ->from($table)
                ->where($where)
                ->groupBy([$table . '.recordid']);

            $query->overrideSecurity();

            $writer = Container::get(SqlWriter::class);
            $writer->retainAtCodes(true);

            [$statement, $parameters] = $writer->writeStatement($query);

            foreach ($parameters as $parameter) {
                $statement = $writer->replacePlaceholdersInStatement($parameter, $statement);
            }

            $ModuleWhere = $table . '.recordid IN (' . $statement . ')';
            $ModuleWhere = ParseSecurityGroupWhere($ModuleWhere);
        }
    }

    $preventSQLEditing = $registry->getParm('BLOCK_SQL_EDITING')->isTrue();

    if ($ReadOnly) {
        $TextAreaField = ($QBE ? $ModuleWhere : $Where);
    } else {
        $securityGroupWhereClauseValue = $QBE ? $ModuleWhere : $Where;
        if ($preventSQLEditing) {
            $TextAreaField = '<div id="permissionwhere_' . $Module . '">' . $securityGroupWhereClauseValue . '</div>';
            $whereClauseSession->setNewSecurityGroupSQL($Module, $securityGroupWhereClauseValue);
        } else {
            $TextAreaField =
                '<textarea name="permissionwhere_' . $Module . '" class="form-control formclasses-set-changed" id="permissionwhere_' . $Module . '" cols="70" rows="7">' .
                $securityGroupWhereClauseValue . '</textarea><br />
                <input type="hidden" name="CHANGED-permissionwhere_' . $Module . '" id="CHANGED-permissionwhere_' . $Module . '" value="' . $securityGroupWhereClauseValue . '">';
        }

        // The standards module doesn't currently have a search facility
        if ($ModuleWhere && !$QBE && !$preventSQLEditing) {
            $grpWhere = '';

            if (isset($ModuleWhere) && $ModuleWhere != '') {
                $grpWhere = $ModuleWhere;
            }

            $TextAreaField .= '<input type="hidden" name="searchwhere_' . $Module . '" id="searchwhere_' . $Module . '" value="' . $grpWhere . '" />';

            $TextAreaField .= '<input type="button" value="Use current search criteria"' .
            ' onclick="javascript:document.getElementById(\'permissionwhere_' . $Module . '\').value = document.getElementById(\'searchwhere_' . $Module . '\').value;">&nbsp;';
        }

        $TextAreaField .= '<button type="submit" class="prince-button button-small button-outline button-margin-right" onclick="selectAllMultiple(document.getElementById(\'ldap_dn\'));document.frmEditGroup.user_action.value=\'search\';document.frmEditGroup.hide_linked_sections.value=1; document.frmEditGroup.search_module.value=\'' . $Module . '\';" />'
            . '<span>Define criteria using query by example</span>'
            . '</button>';
    }

    $FieldObj = CustomFieldFactory::create($FormType, $TextAreaField);
    echo GetDivFieldHTML(
        _fdtk('criteria_for_sec_group') . '<br>' . GetValidationErrors([], 'permissionwhere_' . $Module),
        $FieldObj->GetField(),
        $error,
        25,
        'permissionwhere_' . $Module . '_row',
    );
}

/**
 * uses in GroupTemplateController only.
 */
function panelGroupDetails($grp, $FormType)
{
    global $ModuleDefs;

    $error = $grp['error'];

    echo '<ol>
    ';

    echo '
        <li class="section_title_row">
            <div class="section_title_group">
            <div class="section_title">' . ($grp['grp_id'] ? 'Security Group Details' : 'New Security Group') . '</div>
            </div>
        </li>';

    // $FieldMode = ($FormType == "ReadOnly" ? "Print" : $FormType);
    $FieldMode = $FormType;

    $table = FormTableFactory::create($FormType, 'USE');

    $fld['grp_id'] = src\component\field\InputFieldFactory::create('Print', 'grp_id', 0, 0, $grp['grp_id']);
    echo GetDivFieldHTML('Group ID', $fld['grp_id']->GetField());

    $warningImg = '<img src="images/Warning.gif" alt="Mandatory" class="mandatory_image" />';
    $grpCodeObj = src\component\field\InputFieldFactory::create($FieldMode, 'grp_code', 60, 254, $grp['grp_code']);
    $groupNameError = '<div class="field_error">' . $error['grp_code'] . '</div>';
    echo GetDivFieldHTML($warningImg . 'Group name', $grpCodeObj->GetField(), $groupNameError);

    $grpDescObj = src\component\field\InputFieldFactory::create($FieldMode, 'grp_description', 60, 254, $grp['grp_description']);
    $groupDescriptionError = '<div class="field_error">' . $error['grp_code'] . '</div>';
    echo GetDivFieldHTML($warningImg . 'Group description', $grpDescObj->GetField(), $groupDescriptionError);

    echo $table->Contents;

    // Group type
    if (!isset($grp['grp_type']) || $grp['grp_type'] == '') {
        $grp['grp_type'] = GRP_TYPE_ACCESS;
    }

    $typeOptions = [
        GRP_TYPE_ACCESS => _fdtk('security_group_record_access'),
        (GRP_TYPE_ACCESS | GRP_TYPE_EMAIL) => 'Record access and e-mail notification',
        GRP_TYPE_EMAIL => 'E-mail notification',
        GRP_TYPE_DENY_ACCESS => 'Deny access', ];

    $field = SelectFieldFactory::createSelectField('grp_type', 'ADM', $grp['grp_type'], $FieldMode);
    $field->setCustomCodes($typeOptions);
    $field->setOnChangeExtra('showAccessLevels(jQuery("#grp_type").val())');
    echo GetDivFieldHTML('Group type', $field->getField(), '', '', 'tr_group_type');

    $ModuleNames = [];

    // Select all permissions for security group
    if ($grp['grp_id'] != '') {
        $data = DatixDBQuery::PDO_fetch_all('SELECT item_code, perm_value FROM sec_group_permissions WHERE grp_id = :grp_id', ['grp_id' => $grp['grp_id']]);

        foreach ($data as $perm_value) {
            $grp[\UnicodeString::strtoupper($perm_value['item_code'])] = $perm_value['perm_value'];
        }
    }

    $excludedModules = [Module::USERS];

    echo '
        <li class="section_title_row">
            <div class="section_title_group">
                <div class="section_title">Group security settings</div>
                ' .
                    getModuleDropdown([
                        'name' => 'lbModule',
                        'onchange' => 'showModuleSettings(jQuery(\'#lbModule\').val())',
                        'current' => ($grp['default_selected_module'] ?: getCurrentModuleSelect($grp, $excludedModules)),
                        'not' => $excludedModules,
                    ])
                    . '

            </div>
        </li>';


    echo '<li>';


    foreach ($ModuleDefs as $Module => $Defs) {
        if (ModIsLicensed($Module) && !$Defs['DUMMY_MODULE'] && !in_array($Module, $excludedModules)) {
            MakeModuleSection([
                'module' => $Module,
                'data' => $grp,
                'form_type' => $FormType,
            ]);
        }
    }

    echo '</li></ol>';
}

/**
 * Returns current module for module list.
 * It takes first from top of the $ModuleListOrder which
 * has set criteria afainst it in Group security settings.
 *
 * @param array $data Group security settings
 * @param array $ExcludeArray List of modules
 *
 * @return string MODULE (default INC)
 */
function getCurrentModuleSelect($data, $ExcludeArray)
{
    global $ModuleDefs;

    $CurrentModuleSelect = 'INC';

    if (isset($data['DIF_PERMS'])) {
        return $CurrentModuleSelect;
    }

    $ModArray = getModArray($ExcludeArray);

    foreach ($ModArray as $Module => $desc) {
        if (isset($data[$ModuleDefs[$Module]['PERM_GLOBAL']]) || isset($data['permission'][$Module]['where'])) {
            return $Module;
        }
    }

    return $CurrentModuleSelect;
}

function panelProfiles($grp, $FormAction)
{
    $sql = "
        SELECT
        	profiles.recordid,
        	profiles.pfl_name,
        	profiles.pfl_description
        FROM
        	link_profile_group
        	JOIN profiles ON link_profile_group.lpg_profile = profiles.recordid
        WHERE
        	link_profile_group.lpg_group = {$grp['grp_id']}
	";

    $ProfileArray = DatixDBQuery::PDO_fetch_all($sql); ?>
<tr>
	<td>
	<table border="0" width="100%" cellspacing="1" cellpadding="4" class="gridlines" align="center">
<?php

    echo '<tr>';
    echo '<td class="windowbg" width="1%">Id</td>';
    echo '<td class="windowbg">Name</td>';
    echo '<td class="windowbg">Description</td>';
    echo '</tr>';

    if (count($ProfileArray) == 0) {
        echo '<tr><td class="windowbg2" colspan="7">No profiles</td></tr>';
    } else {
        foreach ($ProfileArray as $row) {
            $url = "{$scripturl}?action=editprofile&recordid={$row['recordid']}&from_grp_id={$grp['grp_id']}";
            echo '<tr>';

            echo '<td class="windowbg2"><a href="' . CompatEscaper::encodeCharacters($url) . '">' . CompatEscaper::encodeCharacters($row['recordid']) . '</a></td>';
            echo '<td class="windowbg2"><a href="' . CompatEscaper::encodeCharacters($url) . '">' . CompatEscaper::encodeCharacters($row['pfl_name']) . '</a></td>';
            echo '<td class="windowbg2"><a href="' . CompatEscaper::encodeCharacters($url) . '">' . CompatEscaper::encodeCharacters($row['pfl_description']) . '</a></td>';

            echo '</tr>';
        }
    }
    echo '</table></td></tr>';
}

function panelUsers($grp, $FormAction)
{
    global $dtxdebug;

    $Where = MakeSecurityWhereClause("sec_staff_group.grp_id = {$grp['grp_id']}", 'USE');

    $sql = "SELECT
	staff.use_surname, staff.use_forenames, staff.use_title,
	sec_staff_group.grp_id, sec_staff_group.use_id, sec_staff_group.recordid as link_id
	from
	sec_staff_group
	join staff ON sec_staff_group.use_id = staff.recordid
	WHERE
	{$Where}
	ORDER BY
	staff.use_surname, staff.use_forenames, staff.use_title";

    $contactarray = DatixDBQuery::PDO_fetch_all($sql); ?>
<tr>
	<td>
	<input type="hidden" name="redirect_url" value="" />
	<script language="javascript">
	function SelectAllUsers(checked)
	{
		var groups = document.getElementsByName('cbSelectUser');
		for (var i = 0; i < groups.length; i++)
		{
			groups[i].checked = checked;
		}
	}

	function CheckSelectAll()
	{
		var checked = true;
		var groups = document.getElementsByName('cbSelectUser');
		if (groups.length == 0)
		{
			checked = false;
		}
		else
		{
			for (var i = 0; i < groups.length; i++)
			{
				if (groups[i].checked == false)
				{
					checked = false;
					break;
				}
			}
		}
		document.getElementById('cbSelectAllUsers').checked = checked;
	}

	function UnlinkSelectedUserGroups()
	{
		var urlGroups = '';
		var groups = document.getElementsByName('cbSelectUser');
		for (var i = 0; i < groups.length; i++)
		{
			if (groups[i].checked == true)
			{
				urlGroups += '&link_id[]=' + groups[i].value;
			}
		}

		if (urlGroups != '')
		{
			urlGroups = urlGroups.substr(1);
			if (confirm('Remove selected user(s) from security group?') == false)
			{
				return;
			}
		}
		else
		{
			alert('No user selected.');
		}

		jQuery.ajax({
			url: scripturl + '?action=httprequest&type=unlink_user_groups',
			type: "POST",
			data: urlGroups + '&type=user',
			dataType: 'text',
			async: true,
			success: function(response) {
				var users = document.getElementsByName('cbSelectUser');
				if (users.length > 0)
				{
					for (var i = 0; i < users.length; i++)
					{
						if (users[i].checked == true)
						{
							//var row = groups[i].parentNode.parentNode.removeNode(true);
							var row = users[i].parentNode.parentNode;
							var table = row.parentNode;
							table.deleteRow(row.rowIndex);
							i--;
						}
						if (i >= users.length)
						{
							break;
						}
					}
				}
				CheckSelectAll();

				var users = document.getElementsByName('cbSelectUser');
				if (users.length == 0)
				{
					row = document.getElementById('row_remove_selected_users');
					var table = row.parentNode;
					table.deleteRow(row.rowIndex);
					ele = document.getElementById('link_add_user');
					ele.innerHTML = 'Add a user to this security group';
				}
			},
			error: function(jqXHR, textStatus, errorThrown ) {
				alert('Error occurred trying to remove selected users from group.');
			}
		});
	}
	</script>
	<table border="0" width="100%" cellspacing="1" cellpadding="4" class="gridlines" align="center">
<?php

    echo '<tr>';

    if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
        echo '<td class="windowbg" width="1%">' .
            '<input type="checkbox" id="cbSelectAllUsers" name="cbSelectAllUsers" onclick="javascript:SelectAllUsers(this.checked);" />' .
            '</td>';
    }

    echo '<td class="windowbg">Name</td>';
    echo '</tr>';

    if (count($contactarray) == 0) {
        echo '<tr><td class="windowbg2" colspan="7">No users</td></tr>';
    } else {
        foreach ($contactarray as $row) {
            $contactName = "{$row['use_surname']}, {$row['use_forenames']} {$row['use_title']}";
            $contactName = \UnicodeString::trim($contactName, ' ,');

            $url = "{$scripturl}?action=edituser&grp_id={$row['grp_id']}&recordid={$row['use_id']}";
            $href = $url;

            echo '<tr>';

            if ($FormAction != 'Print' && $FormAction != 'ReadOnly') {
                echo '<td class="windowbg2"><input type="checkbox" ' .
                    'id="cbSelectUser" ' .
                    'name="cbSelectUser" ' .
                    'value="' . Sanitize::SanitizeInt($row['link_id']) . '" ' .
                    'onclick="javascript:CheckSelectAll();"' .
                    ' /></td>';
            }

            echo '<td class="windowbg2"><a href="' . CompatEscaper::encodeCharacters($href) . '">' . CompatEscaper::encodeCharacters($contactName) . '</a></td>';

            echo '</tr>';
        }
    }

    if (!($FormAction == 'Print' || $FormAction == 'ReadOnly')) {
        if (count($contactarray) > 0) {
            echo '<tr id="row_remove_selected_users"><td class="windowbg2" colspan="5">';
            $href = 'javascript:UnlinkSelectedUserGroups()';
            echo '<a href="' . $href . '" >Remove selected user(s) from security group</a>';
            echo '</td></tr>';
        }

        echo '<tr><td class="windowbg2" colspan="5">';
        $url = "{$scripturl}?action=add_user_to_group";
        echo '<a href="JavaScript:document.forms[0].redirect_url.value=\'' .
            $url . '\';selectAllMultiCodes();document.forms[0].submit();" ><div id="link_add_user">Add ' . ($num_rows > 0 ? 'another' : 'a') . ' user to this security group</div></a>';
        echo '</td></tr>';
    }

    echo '</table></td></tr>';
}

function MakeModuleSection($Options)
{
    global $ModuleDefs, $JSFunctions;

    $Module = $Options['module'];
    $Data = $Options['data'];
    $FormType = $Options['form_type'];
    $permissionService = (new CapturePermissionServiceFactory())->create();
    $securityGroupsReadOnly = $permissionService->isReadOnly($Module, CapturePermissionService::PERMISSION_SECURITY_GROUPS);
    if ($securityGroupsReadOnly) {
        $FormType = FormTable::MODE_READONLY;
    }

    if (isset($ModuleDefs[$Module]['PERM_GLOBAL'])) {
        $AccessLevelParam = $ModuleDefs[$Module]['PERM_GLOBAL'];
    }

    echo '<ol id="div' . $Module . '" style="display:none">';

    if (isset($ModuleDefs[$Module]['PERM_GLOBAL']) && !$ModuleDefs[$Module]['NO_ACCESS_LEVEL']) {
        $makeFieldService = new FormTableMakeFieldService(new Request());
        $fieldValue = $Data[$AccessLevelParam] ?? '';
        $accessLevelField = $makeFieldService->makeAccessLevelField($AccessLevelParam, $Module, $fieldValue, $FormType);

        echo GetDivFieldHTML('<br/>Access level' . GetValidationErrors($Data, 'access_level'), $accessLevelField->getField(), 'Note: where users are linked to more than one security group, the highest access level identified will apply', '', 'tr_access_level', !($Data['grp_type'] & GRP_TYPE_ACCESS == GRP_TYPE_ACCESS));
    }

    if ($ModuleDefs[$Module]['DISABLE_SEC_WHERE_CLAUSE']) {
        // disabling WHERE clause removes all access to records so e.g. user/group record permissions can be used exclusively to grant access
        if ($Data['permission'][$Module]['where'] == '1=2') {
            $value = true;
            $Data['permission'][$Module]['where'] = '';
        }

        $JSFunctions[] = '
            function OnChange_disablewhere_' . $Module . '()
            {
                if (jQuery("#disablewhere_' . $Module . ':checked").val() != null)
                {
                    jQuery("#permissionwhere_' . $Module . '_row").hide();
                    jQuery("#permissionwhere_' . $Module . '").attr("disabled", true);
                }
                else
                {
                    jQuery("#permissionwhere_' . $Module . '_row").show();
                    jQuery("#permissionwhere_' . $Module . '").removeAttr("disabled");
                }
            }
            OnChange_disablewhere_' . $Module . '();';
    }

    if (!$ModuleDefs[$Module]['NO_SEC_WHERE_CLAUSE']) {
        $modWhere = $Data['permission'][$Module]['where'] ?? $Data['permissionwhere_' . $Module];

        MakeGroupWhereRow(
            $Module,
            $modWhere,
            $FormType,
            $Data['error']['permissionwhere_' . $Module],
        );
    }

    echo '</ol>';
}

function ParseSecurityGroupWhere($where)
{
    $ret = $where;
    // Remove references to rep_approved
    $ret = preg_replace("/(\band\b\s+)?[a-zA-Z]+_[a-zA-Z]+.rep_approved\s*=\s*'[a-zA-Z]'\s*/iu", '', $ret);
    $ret = preg_replace("/(\band\b\s+)?\brep_approved\b\s*=\s*'[a-zA-Z]'\s*/iu", '', $ret);
    $ret = \UnicodeString::ltrim($ret, ' ');

    if (\UnicodeString::strpos(\UnicodeString::strtolower($ret), 'and ') === 0) {
        $ret = \UnicodeString::substr($ret, 4);
    }

    $ret = \UnicodeString::ltrim($ret, ' ');

    // Remove references to submit_stage
    $ret = preg_replace("/(\band\b\s+)?\bsubmit_stage\b\s*=\s*'[0-9]'\s*/iu", '', $ret);
    $ret = \UnicodeString::ltrim($ret, ' ');

    if (\UnicodeString::strpos(\UnicodeString::strtolower($ret), 'and ') === 0) {
        $ret = \UnicodeString::substr($ret, 4);
    }

    return \UnicodeString::ltrim($ret, ' ');
}

function GetGroupModules($GroupID)
{
    global $ModuleDefs;

    $sql = 'SELECT item_code FROM sec_group_permissions WHERE grp_id = :grp_id';
    $resultArray = DatixDBQuery::PDO_fetch_all($sql, ['grp_id' => $GroupID]);

    $GroupPerms = [];

    foreach ($resultArray as $row) {
        $GroupPerms[] = $row['item_code'];
    }

    $GroupModule = [];

    foreach ($ModuleDefs as $Mod => $ModDef) {
        if (in_array($ModDef['PERM_GLOBAL'], $GroupPerms)) {
            $GroupModule[] = $Mod;
        }
    }

    return $GroupModule;
}
