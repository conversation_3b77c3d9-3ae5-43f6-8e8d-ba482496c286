<?php

use app\models\generic\valueObjects\Module;
use app\models\globals\ProgNotesEditGlobal;
use src\component\field\SelectFieldFactory;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\safeguarding\models\SafeguardingAdminFields;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

$progNotesEditField = SelectFieldFactory::createSelectField(
    ProgNotesEditGlobal::GLOBAL_NAME,
    'ADM',
    $staff[ProgNotesEditGlobal::GLOBAL_NAME],
    $FormType,
);
$progNotesEditField->setCustomCodes(['OWN' => 'Edit own notes only', 'ALL' => 'Edit all notes']);

$ModuleDefs = Container::get(ModuleDefs::class);
$isFullAdmin = (new UserSessionFactory())->create()->isFullAdmin();
$restrictAdminParameters = Container::get(Registry::class)->getParm('RESTRICT_ADMIN_PARAMETERS', 'N')->toBool();
$showRestrictedAdminParameters = $isFullAdmin || !$restrictAdminParameters;

$isNotOnSearchMode = ($FormType != 'Search');

$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],
    'details' => [
        'Title' => _fdtk('user_details', $useFormDesignLanguage),
        'Rows' => [
            ['Name' => 'recordid', 'ReadOnly' => true],
            ['Name' => 'lockout', 'ReadOnly' => true, 'Type' => 'string', 'Table' => 'users_main'],
            ['Name' => 'use_staff_include', 'ReadOnly' => true, 'Type' => 'yesno'],
            ['Name' => 'sta_lockout_dt', 'ReadOnly' => true, 'Type' => 'date', 'Table' => 'users_main'],
            ['Name' => 'sta_lockout_reason', 'ReadOnly' => true, 'Type' => 'string', 'Table' => 'users_main'],
            ['Name' => 'location_id', 'ReadOnly' => true, 'Type' => 'tree'],
            ['Name' => 'service_id', 'ReadOnly' => true, 'Type' => 'tree'],
        ],
    ],
    'contact' => [
        'Title' => _fdtk('contact_details', $useFormDesignLanguage),
        'Rows' => [
            ['Name' => 'use_title', 'ReadOnly' => true],
            ['Name' => 'use_forenames', 'ReadOnly' => true],
            ['Name' => 'use_surname', 'ReadOnly' => true],
            ['Name' => 'use_jobtitle', 'ReadOnly' => true],
            ['Name' => 'use_email', 'ReadOnly' => true],
            ['Name' => 'use_dopened', 'ReadOnly' => true],
            ['Name' => 'use_dclosed', 'ReadOnly' => true],
            ['Name' => 'use_tel1', 'ReadOnly' => true],
            ['Name' => 'use_tel2', 'ReadOnly' => true],
            ['Name' => 'use_number', 'ReadOnly' => true],
            ['Name' => 'use_language', 'ReadOnly' => true],
        ],
    ],
];

// Stop adding panels to the form when the user isn't an Admin
if (!(new UserSessionFactory())->create()->isFullOrLocalAdmin()) {
    return $FormArray;
}

/** @var array $FormArrayAdmin The Form Array sections that should visible only to Admin users */
$FormArrayAdmin = [
    'profile' => [
        'Title' => _fdtk('profile', $useFormDesignLanguage),
        'Condition' => $isNotOnSearchMode,
        'NoFieldAdditions' => true,
        'Rows' => [
            [
                'Name' => 'sta_profile',
                'Title' => 'Profile',
                'Type' => 'custom',
                'HTML' => MakeProfileField($staff['sta_profile'], $FormType),
            ],
        ],
    ],
    'parameters' => [
        'Title' => _fdtk('config_params', $useFormDesignLanguage),
        'Condition' => $isNotOnSearchMode,
        'Rows' => [],
    ],
    'ALL_MODULES_parameters' => [
        'Title' => _fdtk('all_module_settings', $useFormDesignLanguage),
        'Condition' => $isNotOnSearchMode,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'LOGOUT_DEFAULT_MODULE',
            'CUSTOM_REPORT_BUILDER',
            ['Type' => 'formfield', 'Name' => ProgNotesEditGlobal::GLOBAL_NAME, 'FormField' => $progNotesEditField],
            'ENABLE_GENERATE',
            ['Name' => 'ENABLE_BATCH_UPDATE', 'Condition' => $showRestrictedAdminParameters],
        ],
    ],
    'ACT_parameters' => [
        'Title' => $ModuleDefs['ACT']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('ACT')),
        'NoFieldRemoval' => true,
        'NoFieldAdditions' => true,
        'Rows' => [
            'ACT_SEARCH_DEFAULT',
            'ACT_OWN_ONLY',
            ['Name' => 'ACT_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'DELETE_ACT_CHAIN',
        ],
    ],
    'INC_parameters' => [
        'Title' => $ModuleDefs['INC']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('INC')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            ['Name' => 'DIF1_ONLY_FORM', 'OverrideFormDesignGlobal' => true],
            'DIF2_DEFAULT',
            'INC_LISTING_ID',
            'DIF2_SEARCH_DEFAULT',
            'DIF_SHOW_REJECT_BTN',
            'DIF2_HIDE_CONTACTS',
            'DIF_OWN_ONLY',
            'INC_SHOW_AUDIT',
            ['Name' => 'INC_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COPY_INCIDENTS',
            'INC_DELETE_DOCS',
            'INC_CAN_DELETE_OWN_DOCUMENTS',
            'INC_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'INC_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff['INC_SAVED_QUERIES'], null, $FormType, 'INC'),
            ],
            \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS,
        ],
    ],
    'POL_parameters' => [
        'Title' => $ModuleDefs['POL']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('POL')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'POL2_DEFAULT',
            'POL_LISTING_ID',
            'POL2_SEARCH_DEFAULT',
            'POL_OWN_ONLY',
            'POL_SHOW_AUDIT',
            ['Name' => 'POL_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COPY_POL',
            'POL_DELETE_DOCS',
        ],
    ],
    'COM_parameters' => [
        'Title' => $ModuleDefs['COM']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('COM')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'COM_SHOW_ADD_NEW',
            ['Name' => 'COM1_DEFAULT', 'OverrideFormDesignGlobal' => true],
            'COM2_DEFAULT',
            'COM_LISTING_ID',
            'COM2_SEARCH_DEFAULT',
            'COM_SHOW_REJECT_BTN',
            'COM_OWN_ONLY',
            'COM_SHOW_AUDIT',
            ['Name' => 'COM_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'COM_DELETE_DOCS',
            'COM_CAN_DELETE_OWN_DOCUMENTS',
            'COM_KO41_CAN_GENERATE_REPORT',
            'COM_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'COM_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff['COM_SAVED_QUERIES'], null, $FormType, 'COM'),
            ],
            'SHOW_REOPEN',
            'EDITABLE_CHAIN_DUE_DATES',
            'COPY_COM',
        ],
    ],
    'CLA_parameters' => [
        'Title' => $ModuleDefs['CLA']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('CLA')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'CLAIM1_DEFAULT',
            'CLAIM2_DEFAULT',
            'CLA_LISTING_ID',
            'CLAIM2_SEARCH_DEFAULT',
            'CLA_OWN_ONLY',
            'CLA_SHOW_AUDIT',
            ['Name' => 'CLA_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'CLA_DELETE_DOCS',
            'CLA_CAN_DELETE_OWN_DOCUMENTS',
            'CLA_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'CLA_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff['CLA_SAVED_QUERIES'], null, $FormType, 'CLA'),
            ],
            'COPY_CLA',
        ],
    ],
    'MOR_parameters' => [
        'Title' => $ModuleDefs['MOR']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('MOR')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'MOR1_DEFAULT',
            'MOR2_DEFAULT',
            'MOR2_SEARCH_DEFAULT',
            'MOR_LISTING_ID',
            'MOR_SHOW_AUDIT',
            ['Name' => 'MOR_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'MOR_DELETE_DOCS',
            'MOR_CAN_DELETE_OWN_DOCUMENTS',
            'MOR_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'MOR_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff['MOR_SAVED_QUERIES'], null, $FormType, 'MOR'),
            ],
            'COPY_MOR',
        ],
    ],
    'PAY_parameters' => [
        'Title' => $ModuleDefs['PAY']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('PAY')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'PAY2_SEARCH_DEFAULT',
            'DELETE_PAY',
        ],
    ],
    'CON_parameters' => [
        'Title' => $ModuleDefs['CON']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('CON')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'CON_SHOW_REJECT_BTN',
            ['Name' => 'CON_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'CON_ALLOW_MERGE_DUPLICATES',
        ],
    ],
    'AST_parameters' => [
        'Title' => $ModuleDefs['AST']['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed('AST')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'AST_SETUP',
            'AST_DELETE_DOCS',
        ],
    ],
    'MED_parameters' => [
        'Title' => $ModuleDefs['MED']['NAME'],
        'Condition' => ($isNotOnSearchMode && bYN(GetParm(
            'MULTI_MEDICATIONS',
            'N',
        )) && ModIsLicensed('MED')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'MED_LISTING_ID',
            'MED_SETUP',
        ],
    ],

    'ADM_parameters' => [
        'Title' => $ModuleDefs['ADM']['NAME'] . ' settings',
        'Condition' => ($FormType != 'Search' && $isFullAdmin),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            ['Name' => 'FULL_ADMIN', 'Condition' => $isFullAdmin, 'Type' => 'yesno'],
            'ADM_DEFAULT',
            'ADM_LISTING_ID',
            [
                'Name' => 'ADM_PROFILES',
                'Title' => 'Profiles',
                'Type' => 'custom',
                'HTML' => MakeProfileSetupField($staff['ADM_PROFILES']),
            ],
            'ADM_GROUP_SETUP',
            'ADM_CAN_DELEGATE_PERMISSIONS',
            'ADM_CAN_DELEGATE_ACCOUNTS',
            'ADM_NO_ADMIN_REPORTS',
        ],
    ],
    'RED_parameters' => [
        'Title' => $ModuleDefs[Module::REDRESS]['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed(Module::REDRESS)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'RED1_DEFAULT',
            'RED2_DEFAULT',
            'RED2_SEARCH_DEFAULT',
            'RED_LISTING_ID',
            'RED_SHOW_AUDIT',
            ['Name' => 'RED_SETUP', 'Condition' => $showRestrictedAdminParameters],
            'RED_DELETE_DOCS',
            'RED_CAN_DELETE_OWN_DOCUMENTS',
            'RED_SAVED_QUERIES_HOME_SCREEN',
            [
                'Name' => 'RED_SAVED_QUERIES',
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff['RED_SAVED_QUERIES'], null, $FormType, Module::REDRESS),
            ],
            'COPY_RED',
        ],
    ],
    'SFG_parameters' => [
        'Title' => $ModuleDefs[Module::SAFEGUARDING]['NAME'] . ' settings',
        'Condition' => ($isNotOnSearchMode && ModIsLicensed(Module::SAFEGUARDING)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            SafeguardingAdminFields::DEFAULT_LEVEL_1_FORM,
            SafeguardingAdminFields::DEFAULT_LEVEL_2_FORM,
            SafeguardingAdminFields::DEFAULT_SEARCH_FORM,
            SafeguardingAdminFields::ENABLE_PINNED_QUERIES,
            [
                'Name' => SafeguardingAdminFields::SELECT_PINNED_QUERIES,
                'Title' => _fdtk('choose_saved_queries', $useFormDesignLanguage),
                'Type' => 'custom',
                'HTML' => MakeSavedQueriesSetupField($staff[SafeguardingAdminFields::SELECT_PINNED_QUERIES], null, $FormType, Module::SAFEGUARDING),
            ],
            SafeguardingAdminFields::ENABLE_RECORD_COPY,
            SafeguardingAdminFields::SHOW_AUDIT,
            SafeguardingAdminFields::SHOW_ADD_NEW,
        ],
    ],
    'groups' => [
        'Title' => _fdtk('table_sec_groups', $useFormDesignLanguage),
        'Condition' => $isNotOnSearchMode,
        'Include' => 'Source/security/SecurityBase.php',
        'Function' => 'sectionSecurityGroups',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
];

return $FormArray = array_merge($FormArray, $FormArrayAdmin);
