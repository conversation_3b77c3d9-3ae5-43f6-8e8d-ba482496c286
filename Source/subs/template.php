<?php

use app\framework\DoctrineEntityManagerFactory;
use app\models\framework\config\DatixConfig;
use app\models\framework\config\DatixConfigFactory;
use app\models\framework\modules\CurrentModuleFactory;
use app\models\framework\modules\ModuleRepository;
use app\models\framework\navigation\LoggedOutExtraLinksFactory;
use app\models\language\entities\LanguageEntity;
use app\services\carlton\api\CarltonAPIService;
use app\services\carlton\api\CarltonAPIServiceFactory;
use app\services\carlton\user\UserServiceFactory;
use app\services\forms\PageTitleProvider;
use app\services\menus\SubMenuFactory;
use app\services\navigation\CaptureNavigationServiceFactory;
use src\documents\helpers\DragAndDropAssetsHelper;
use src\documents\services\DragAndDropService;
use src\email\AwsSesEmailLimits;
use src\framework\controller\AssetManager;
use src\framework\controller\TemplateController;
use src\framework\registry\Registry;
use src\framework\session\Session;
use src\framework\session\SessionFactory;
use src\framework\session\UserSession;
use src\framework\session\UserSessionFactory;
use src\framework\views\ViewParams;
use src\framework\views\ViewRenderer;
use src\generic\services\DefaultModuleProviderFactory;
use src\logger\DatixLogger;
use src\logger\Facade\Log;
use src\security\CompatEscaper;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use app\services\systemConfiguration\SystemConfigurationServiceFactory;

/** string DEFAULT_LOGO_URL */
const DEFAULT_LOGO_URL = 'assets\icons\RLDatix-logo-170x30.png';

$yyheaderdone = 0;    // this is used to make the fatal error thing work better

$tabIndex = []; // global variable for storing tabindex values, starts as 2 as 1 is used for the skip navigation link in the header, see template.php

/**
 * Function to get the next available tabIndex value and increment the global variable.
 *
 * @return int returns value for tabindex
 */
function getTabIndexValue($start = 'main')
{
    global $tabIndex;

    if (!$tabIndex[$start]) {
        $tabIndex[$start] = ($start == 'main' ? 2 : (max($start, $tabIndex['main'])));
    }

    $output = $tabIndex[$start];

    $tabIndex[$start] = $tabIndex[$start] + 1;

    return $output;
}

/**
 * @desc Called at the bottom of every page - constructs the band along the bottom of the page containing the
 * copyright information and any additional contents from template.php
 *
 * Takes one parameter and returns nothing, but echos HTML instead.
 */
function footer($template = null): void
{
    global $dtxtitle, $Development, $JSFunctions,
    $scripturl, $dtxprint_footer, $dtx_system_js_bottom, $dtxsessionmessage;

    if ($template === null) {
        $template = new Template();
    }

    $mainContent = ob_get_clean();

    // Form Design globals don't seem to be picked up in the Help popup for some reason,
    // so need to store the Help Texts in the session:
    if (is_array($_SESSION['HelpTexts'] ?? null) && is_array($GLOBALS['HelpTexts'] ?? null)) {
        $_SESSION['HelpTexts'] = array_merge($_SESSION['HelpTexts'], $GLOBALS['HelpTexts']);
    } elseif (is_array($GLOBALS['HelpTexts'] ?? null)) {
        $_SESSION['HelpTexts'] = $GLOBALS['HelpTexts'];
    }
    $systemConfiguration = SystemConfigurationServiceFactory::create();
    $registry = Container::get(Registry::class);
    $userSession = Container::get(UserSession::class);
    $currentUser = $userSession->getCurrentUser();
    $userRecordId = $currentUser->recordid;
    $moduleRepo = Container::get(ModuleRepository::class); // todo: was true

    $loggedOutExtraLinks = (new LoggedOutExtraLinksFactory())->create();
    $hyperlinks = $loggedOutExtraLinks->getLinks();

    $moduleObject = (new CurrentModuleFactory())->create();
    $moduleTitle = $moduleObject->getLabel();
    $moduleCode = $moduleObject->getModule();

    $navigationService = CarltonAPIServiceFactory::createNavigationService(
        Container::get('http_client.carlton'),
        Container::get(DatixLogger::class),
        Container::get(Session::class),
    );
    $navigationService->connect();
    $resources = $navigationService->getAttribute('data.covidResource');
    $moduleRepo->setCarltonNavigationMenu($navigationService->getAttributes());

    $defaultModule = $_GET['module'] ?? '';
    $defaultModule = (new DefaultModuleProviderFactory())->create()->provide($defaultModule);

    $lowerNavigationModule = $defaultModule === 'ADM' ? '' : $defaultModule;
    $userForename = $userSession->isLoggedIn() ? $userSession->getCurrentUser()->getsta_forenames() : '';
    $userSurname = $userSession->isLoggedIn() ? $userSession->getCurrentUser()->getsta_surname() : '';
    $userNameWithoutInitials = $userForename . ' ' . $userSurname;
    $userInitials = explode(' ', $userNameWithoutInitials);
    $initials = '';

    foreach ($userInitials as $userInitial) {
        $initials .= substr($userInitial, 0, 1);
    }
    $userInitials = strtoupper($initials);

    $datixConfig = (new DatixConfigFactory())->getInstance();

    $captureNavigationService = (new CaptureNavigationServiceFactory())->create();
    $captureModules = $captureNavigationService->getViewableCaptureModules();
    $coreDataModules = $captureNavigationService->getViewableCodeDataModules();

    $viewRenderer = Container::get(ViewRenderer::class);

    echo $viewRenderer->render('./app/views/template.php', new ViewParams([
        'css' => $GLOBALS['dtx_system_css'],
        'user' => $currentUser,
        'headJs' => $GLOBALS['dtx_system_js_top'],
        'bottomJavascript' => $GLOBALS['dtx_system_js_bottom'] . getJSFunctions(),
        'noHeader' => !$template->hasHeader(),
        'footerEnabled' => $systemConfiguration->getSystemConfigurationFromCache('footer_enable'),
        'footer' => $systemConfiguration->getSystemConfigurationFromCache('footer_text'),
        'noMenu' => !$template->hasMenu(),
        'noPadding' => !$template->hasPadding(),
        'noTitle' => !$template->hasTitle(),
        'enablePageLoadingClickBlocker' => $registry->getParm('ENHANCED_ACCESSIBILITY', 'N')->toBool(),
        'isLoggedIn' => $userSession->isLoggedIn(),
        'pageHeading' => $GLOBALS['dtxheading'] ?? null,
        'leftMenu' => $GLOBALS['dtxleft_menu'],
        'mainContent' => $mainContent,
        'sidePanel' => ($_GET['action'] ?? null) == 'dashboard' ? $GLOBALS['dtxside_panel'] : '',
        'printMode' => ($_GET['print'] ?? null) == 1,
        'showAccessiblityNavigation' => $registry->getParm('ENHANCED_ACCESSIBILITY', 'N')->toBool(),
        'currentModule' => $moduleTitle,
        'captureModules' => $captureModules,
        'evaluateModules' => $moduleRepo->getEvaluateModules(),
        'strategyModules' => $moduleRepo->getStrategyModules(),
        'implementModules' => $moduleRepo->getImplementModules(),
        'assessModules' => $moduleRepo->getAssessModules(),
        'coreDataModules' => $coreDataModules,
        'userModules' => $moduleRepo->getUserModules(),
        'userMenu' => ($userSession->isLoggedIn() ? '<a href="#" onclick="if(CheckChange()){SendTo(\'index.php?action=logout\');}" tabindex="16">Logout</a>' : ''),
        'loggedOutLinks' => $hyperlinks,
        'lowerNavigation' => $userSession->isLoggedIn() ? (new SubMenuFactory())->create($lowerNavigationModule, 'dropdown', CanAccessRecord($moduleCode))->getItems() : [],
        'userInitials' => $userInitials,
        'userNameWithoutInitials' => $userNameWithoutInitials,
        'siteLogoUrl' => getSiteLogoUrl($navigationService),
        'topRightLogoPath' => $datixConfig->getTopRightLogo(),
        'clientLogo' => '<img id="client-logo" src="' . ($GLOBALS['logo_topright'] ?? '') . '" />',
        'userRecordId' => $userRecordId,
        'sessionMessage' => $dtxsessionmessage,
        'pendingUsersUrl' => $datixConfig->getCarltonBaseUrl() . 'users/pending',
        'myPreferencesUrl' => $datixConfig->getCarltonBaseUrl() . 'my-preferences',
        'todolistUrl' => getenv('TODOLIST_URL'),
        'pageTitle' => $GLOBALS['dtxtitle'],
        'resources' => $resources,
        'isActiveUserDelegation' => $userSession->isActiveUserDelegation(),
    ]));
}

/**
 * Returns the current logo for Prince.
 *
 * Checks in the following sequence and returns on the first value found:
 *  - the logo is already set locally in `$GLOBALS['dtx_logo_url'],
 *  - the logo is set on Carlton and transmitted through the Navigation API,
 *  - returns a default logo when the previous where null (also sets locally the default logo)
 *
 * @return mixed|string
 */
function getSiteLogoUrl(CarltonAPIService $navigationService)
{
    /** @var Session $session */
    $session = (new SessionFactory())->create();

    if ($session->has('assetManager', 'dtx_logo_url')) {
        return $session->get('assetManager', 'dtx_logo_url');
    }

    $logoUrl = $navigationService->getAttribute('data.logoUrl');
    if (!$logoUrl) {
        $logoUrl = DEFAULT_LOGO_URL;
    }
    $session->set('assetManager', 'dtx_logo_url', $logoUrl);

    return $logoUrl;
}

/**
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \app\models\framework\config\InvalidConfigurationException
 */
function PopulateTemplateStrings(Template $template, ?TemplateController $instance = null, ?AssetManager $assetManagerToMerge = null): void
{
    global $dtx_system_css, $dtx_system_js_top, $dtx_system_js_bottom, $scripturl;

    $systemConfiguration = SystemConfigurationServiceFactory::create();
    $registry = Container::get(Registry::class);
    $deviceDetect = $registry->getDeviceDetector();
    $assetManager = new AssetManager();
    $assetManager->clearSessionAssets();
    $userSession = (new UserSessionFactory())->create();

    $icon_links = '
    <link rel="icon" type="image/png" href="images/favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="images/favicon-16x16.png" sizes="16x16" />';

    if ($deviceDetect->isTablet() && $deviceDetect->isIOS()) {
        $icon_links .= '
<link rel="apple-touch-icon" href="images/icons/homescreen-icons/datix-logo-60x60.png">
<link rel="apple-touch-icon" sizes="76x76" href="images/icons/homescreen-icons/datix-logo-76x76.png">
<link rel="apple-touch-icon" sizes="120x120" href="images/icons/homescreen-icons/datix-logo-120x120.png">
<link rel="apple-touch-icon" sizes="152x152" href="images/icons/homescreen-icons/datix-logo-152x152.png">
<link rel="apple-touch-icon" sizes="167x167" href="images/icons/homescreen-icons/datix-logo-167x167.png">
<link rel="apple-touch-icon" sizes="180x180" href="images/icons/homescreen-icons/datix-logo-180x180.png">';
    } elseif ($deviceDetect->isTablet() && $deviceDetect->isAndroidOS()) {
        $icon_links .= '
<link rel="icon" sizes="128x128" href="images/icons/homescreen-icons/datix-logo-128x128.png">
<link rel="icon" sizes="192x192" href="images/icons/homescreen-icons/datix-logo-192x192.png">';
    }

    $datixConfig = (new DatixConfigFactory())->getInstance();
    if ($instance instanceof TemplateController) {
        // Only do this if the controller is set to render assets
        if ($instance->getOutputAssets()) {
            // Output require CSS to the page
            $dtx_system_css .= $instance->getAssetManager()->buildCss();
            $dtx_system_css .= $icon_links;

            // Output required javascript to the page
            $dtx_system_js_top .= $instance->getAssetManager()->buildJs();
            $dtx_system_js_bottom .= $instance->getAssetManager()->buildJs('bottom');
        }
    } else {
        $datixConfig = (new DatixConfigFactory())->getInstance();
        $languageId = LanguageSessionFactory::getInstance()->getLanguage();
        $languageRepository = (new DoctrineEntityManagerFactory())->getInstance()->getRepository(LanguageEntity::class);
        $language = $languageRepository->find($languageId);

        $printMode = !empty($_GET['print']) && $_GET['print'] == 1;

        $assetManager->addCss([
            'vendor/twbs/bootstrap/dist/css/bootstrap.css',
            'css/global.css',
            'css/prince.css',
            'css/header.css',
            'css/sidebar.css',
            'css/tables.css',
            'css/footer.css',
            'css/nav.css',
            'css/bodymap.css',
            'css/dropdowns.css',
            'css/modal.css',
            'css/buttons.css',
            'src/dashboard/css/dashboard.css',
            'css/crosstab.css',
            'css/reporting.css',
            'css/jquery-ui-1.10.3.custom.css',
            'css/jquery.datixweb.css',
            'js_functions/qTip/jquery.qtip.min.css',
            'vendor/fortawesome/font-awesome/css/font-awesome.css',
            'node_modules/select2/dist/css/select2.min.css',
            'https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700',
            'css/mandatory-field-messages.css',
            'css/uncompleted-search-messages.css',
            ['file' => 'css/print.css', 'attrs' => 'media="print"', 'conditions' => $printMode],
            ['file' => 'css/accessibility.css', 'conditions' => $registry->getParm('ENHANCED_ACCESSIBILITY', 'N')->isTrue()],
            ['file' => 'css/right_to_left.css', 'conditions' => $language->isRightToLeft()],
        ]);

        $redirectUrl = '';
        $userService = (new UserServiceFactory())->create();
        $dif1url = $userService->getDefaultFormOnLogout();
        if ($dif1url) {
            $redirectUrl .= '?' . http_build_query(['url' => $dif1url]);
        }

        $assetManager->sendVariablesToJs(['scripturl' => $scripturl], false);
        $assetManager->sendVariablesToJs(['globals' => (object) []], false);
        $assetManager->sendVariablesToJs([
            'scripturl' => $scripturl,
            'logoutUrl' => getenv('LOGOUT_URL') . $redirectUrl,
            'logoutUrlNoRedirect' => $datixConfig->getLogoutUrl(),
            'logoutDestination' => $dif1url ?: $datixConfig->getLogoutUrl(),
            'renewTokenUrl' => getenv('TOKEN_RENEW_API'),
            'deviceDetect' => $registry->getDeviceDetector()->getDetectValues(),
            'sessionId' => $_SESSION['session_id'],
            'moduleFromRequest' => ($_GET['module'] ?? null),
            'formId' => is_numeric($_GET['form_id'] ?? null) ? $_GET['form_id'] : null,
            'language' => str_replace('_', '-', LanguageSessionFactory::getInstance()->getLocale() ?: 'en-GB'),
            'isRightToLeft' => $language->isRightToLeft(),
            'isFmtDateWebUS' => $registry->getParm('FMT_DATE_WEB')->is('US'),
        ]);

        $assetManager->sendVariablesToJs(['developerTools' => (object) []], false);
        $assetManager->sendVariablesToJs([
            'developerTools.devModeEnabled' => $GLOBALS['developmentMode'],
        ], false);

        // Add CSRF assets if required
        if ($registry->getParm('CSRF_PREVENTION', 'N')->isTrue()) {
            $assetManager->sendVariablesToJs([
                'token' => \CSRFGuard::getCurrentToken(),
                'suppressClientSideCSRF' => false,
            ], false);
            $assetManager->addJs(['js_functions/tokenizer.js']);
        } else {
            $assetManager->sendVariablesToJs(['token' => ''], false);
        }

        $recordLockTimeoutInMinutes = $registry->getParm('RECORDLOCK_TIMEOUT', 10)->toScalar();
        // If the value is 0 set it to 10, otherwise continuous AJAX calls are made.
        $recordLockTimeoutInMinutes = $recordLockTimeoutInMinutes !== '0' ? $recordLockTimeoutInMinutes : '10';

        $sessionTimeoutInMinutes = (int) Container::get(Registry::class)->getParm('DIF_TIMER_MINS')->toScalar();

        $hasRecordLockTimeout = CheckLockRecord() && $recordLockTimeoutInMinutes > '-1';
        $hasSessionTimeout = $sessionTimeoutInMinutes > 0;
        $hasTimeout = $hasRecordLockTimeout || $hasSessionTimeout;

        $enablePageLoadingClickBlocker = $registry->getParm('ENHANCED_ACCESSIBILITY', 'N')->toBool();

        // Add record timeout variables if a timeout is being used
        if ($hasTimeout) {
            $assetManager->sendVariablesToJs([
                'recordLockTimeoutInMinutes' => $hasRecordLockTimeout ? $recordLockTimeoutInMinutes : null,
                'currentRecordLock' => $hasRecordLockTimeout ? $_SESSION['Record_Locks']['Current_Lock'] : null,
                'sessionTimeoutInMinutes' => $hasSessionTimeout ? $sessionTimeoutInMinutes : null,
            ]);
        }

        // Add text strings from db to javascript txt. global
        $assetManager->sendTextToJs([
            'unsaved_changes_alert' => _fdtk('unsaved_changes_alert'),
            'cancel' => _fdtk('cancel'),
            'new_equipment' => _fdtk('new_equipment'),
            'new_medication' => _fdtk('new_medication'),
            'add_another_equipment' => _fdtk('add_another_equipment'),
            'add_another_medication' => _fdtk('add_another_medication'),
            'please_type_at_least' => _fdtk('please_type_at_least'),
            'no_codes_available' => _fdtk('no_codes_available'),
        ]);

        $isFullAdmin = (new UserSessionFactory())->create()->isFullAdmin();

        $assetManager->addJs(
            [
                'js_functions/jquery/jquery-3.6.0.min.js',
                'js_functions/jquery/jquery-migrate-3.3.2.min.js',
                'js_functions/jquery/jquery-ui-1.10.3.custom.js',
                'js_functions/lib/jquery.navigation.js',
                'js_functions/jquery/jquery.cookie.js',
                'src/generic/js/modernizr.datix.min.js',
                'js_functions/browserUtilities.js',
                'src/framework/js/framework.js',
                'js_functions/polyfills/isinteger.min.js',
                'js_functions/polyfills/promise.min.js',
                'js_functions/common.js',
                'js_functions/formSubmission.js',
                'js_functions/formSubmissionMandatoryFieldMessages.js',
                'js_functions/formSubmissionIdSearchCheck.js',
                'js_functions/numberFieldFunctions.js',
                ['file' => 'js_functions/Listing.js', 'conditions' => !$printMode],
                ['file' => 'js_functions/listbox.js', 'conditions' => !$printMode],
                ['file' => 'js_functions/timerClass.js', 'conditions' => $hasTimeout],
                ['file' => 'js_functions/email.js', 'conditions' => !$printMode],
                'js_functions/jquery/jquery.ready.js',
                'js_functions/polyfills/object.assign.min.js',
                'js_functions/polyfills/object.entries.min.js',
                ['file' => 'src/generic/js/limitTextareaChars.js', 'conditions' => !$printMode],
                'js_functions/forms.js',
                'js_functions/jwt.js',
                'dist/dist.js',
                'js_functions/idle.js',
                'js_functions/FormButtons.js',
                'js_functions/accessibility.js',
                'js_functions/listeners.FormClasses.js',
                'js_functions/languageSelection.js',
                'js_functions/qTip/jquery.qtip.min.js',
                'js_functions/datepicker/datepicker.js',
                'js_functions/datepicker/datepicker-ar.js',
                'js_functions/datepicker/datepicker-de.js',
                'js_functions/datepicker/datepicker-en-GB.js',
                'js_functions/datefield.js',
                'js_functions/dateTimeHelper.js',
                ['file' => 'js_functions/jquery.sidemenu.ready.js', 'conditions' => !$printMode],
                ['file' => 'src/generic/js/atpromptable.js', 'conditions' => $deviceDetect->isTablet()],
                ['file' => 'thirdpartylibs/phpspellcheck/include.js', 'conditions' => $registry->getParm('WEB_SPELLCHECKER', 'N')->isTrue() && !$printMode],
                ['file' => 'js_functions/AdobeFlashDetection.js', 'conditions' => !isset($_SESSION['FlashAvailable'])],
                ['file' => 'js_functions/timeZones.js', 'conditions' => $registry->getParm('DETECT_TIMEZONES', 'N')->isTrue() && !isset($_SESSION['Timezone'])],
                ['file' => 'js_functions/header.js', 'conditions' => $template->hasHeader()],
                ['file' => 'js_functions/pendingUsersNotification.js', 'conditions' => $template->hasHeader() && $isFullAdmin],
                ['file' => 'js_functions/footer.js', 'conditions' => $systemConfiguration->getSystemConfigurationFromCache('footer_enable')],
                'js_functions/fieldSetup.js',
                'js_functions/Hijri.js/Hijri.js',
                'src/component/js/hijrihelpers.js',
                'node_modules/select2/dist/js/select2.js',
                'js_functions/select2Js/select2.js',
                'js_functions/autopopulate.js',
            ],
            ['sub_array' => 'first'],
        );

        // If drag and drop enabled, bring in the required javascript and CSS files.
        $dragAndDropService = Container::get(DragAndDropService::class);
        if ($dragAndDropService->isDragAndDropAllowed()) {
            $dragAndDropAssetsHelper = Container::get(DragAndDropAssetsHelper::class);
            $dragAndDropAssetsHelper->modifyAssetManager($assetManager);
        }

        // Script to bind the calendar to date fields
        if (GetParm('FMT_DATE_WEB') == 'US') {
            $date_fmt = 'mm/dd/yy';
        } else {
            $date_fmt = 'dd/mm/yy';
        }

        /**
         * doCreateCalender will only be generated if we're not in a controller context, but initialiseCalendars() will
         * always be available, so we use that function to feed back to this function if necessary. It's not great, but
         * hopefully gets round the problem of needing to trigger two different functions depending on the context.
         * Ideally this will be removed entirely when we're not having to balance both controller and non controller contexts.
         */
        $assetManager->addJs(['<script language="javascript" type="text/javascript">
            function doCreateCalendar() {
                createCalendar(jQuery(\'.date\'), "' . $date_fmt . '", ' . GetParm('WEEK_START_DAY', 2) . ', ' . GetParm('CALENDAR_WEEKEND', 1) . ', globals.language);
            }
        </script>'], ['sub_array' => 'first']);

        $assetManager->addJs(
            [
                'js_functions/xml.js',
                ['file' => 'js_functions/FloatingWindowClass.js', 'conditions' => true],
                ['file' => 'js_functions/jquery/jquery.class.js', 'conditions' => true],
                ['file' => 'js_functions/encoder.js', 'conditions' => true],
                ['file' => 'js_functions/dropdowns.js', 'conditions' => true],
                ['file' => 'js_functions/ContactMatchCtrl.js', 'conditions' => true],
                ['file' => 'js_functions/OrganisationMatchCtrl.js', 'conditions' => true],
                'js_functions/export.js',
                'js_functions/contactSearch.js',
                'js_functions/organisationSearch.js',
                ['file' => 'js_functions/updateDynamicMultiListWidth.js', 'conditions' => !$printMode && $registry->getParm('ALLOW_DYNAMIC_SIZING', 'Y')->toBool()],
                ['file' => 'js_functions/jquery.timerResetLoggedOut.js', 'conditions' => $hasTimeout && !$userSession->isLoggedIn() && $_GET['action'] == '' && !$registry->getParm('DIF_1_NO_TIMEOUT', 'N')->toBool()],
                ['file' => 'js_functions/jquery.timerResetLoggedIn.js', 'conditions' => $hasTimeout && $userSession->isLoggedIn()],
                ['file' => 'js_functions/pageLoadingClickBlocker.js', 'conditions' => $enablePageLoadingClickBlocker],
            ],
            ['conditions' => !$printMode, 'sub_array' => 'bottom'],
        );

        if ($assetManagerToMerge instanceof AssetManager) {
            $assetManager->mergeAssets($assetManagerToMerge);
        }

        /**
         * Add the javascript variables set using AssetManager->sendVariablestoJs(), has to be done after merging any other assets so that any
         * variables set are added to the output.
         */
        $assetManager->addJs([$assetManager->outputVarsToJs()], ['sub_array' => 'first']);

        // Add css assets to global variable for css
        $dtx_system_css .= $assetManager->buildCss();
        $dtx_system_css .= $icon_links;

        // Add js assets to global variable for javascript at top and bottom of the page
        $dtx_system_js_top .= $assetManager->buildJs();
        $dtx_system_js_bottom .= $assetManager->buildJs('bottom');
    }
}

/**
 * @desc Called at the top of every page - constructs the band along the top of the page containing the
 * currently logged in user, the top menu and any logos. Takes no parameters and echos HTML.
 *
 * @param Template|null $Template replaces <datix> tags with the contents of variables, or null if not set
 * @param TemplateController $instance Header stuff
 */
function template_header(
    $Template = null,
    ?TemplateController $instance = null,
    ?AssetManager $assetManager = null
) {
    // Warning: These global variables are referenced via variable variables,
    // so please do not remove them
    global $dtxtitle, $scripturl, $yyheaderdone, $dtxdebug, $dtxleft_menu, $dtx_system_css,
    $dtx_system_js_top, $dtxside_panel, $MinifierDisabled, $JSFunctions, $dtx_system_js_bottom, $dtxsessionmessage;

    $dtxtitle = StripHTML($dtxtitle); // strip HTML from title (needed for form designs)

    $addMinExtension = ($MinifierDisabled ? '' : '.min');

    if ($yyheaderdone) {
        return;
    }

    if ($Template === null) {
        $Template = new Template();
    }

    PopulateTemplateStrings($Template, $instance, $assetManager);

    // print stuff to prevent cacheing of pages
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: no-store, no-cache, must-revalidate');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Content-Type: text/html; charset=utf-8');
    header('Pragma: no-cache');

    $datixConfig = Container::get(DatixConfig::class);

    // ensures that client-defined logo is pushed into the proper global and wrapped in <img> tags.
    $GLOBALS['dtxlogo_topright'] = $datixConfig->getTopRightLogo();

    // Make sure we don't blank this out if we are about to get a reload
    if (!(bYN(GetParm('DETECT_TIMEZONES', 'N')) && !isset($_SESSION['Timezone']))) {
        $_SESSION['first_login'] = '';
    }

    $dtxsessionmessage = GetSessionMessages();

    // HACK: This is a terrible way of handing this and should be removed asap.
    if (!empty($_SESSION['ajaxEmailsToSend'])) {
        $dtx_system_js_bottom .= '
            <script language="javascript" type="text/javascript" src="js_functions/EmailAlerts' . $addMinExtension . '.js"></script>';

        foreach ($_SESSION['ajaxEmailsToSend'] as $emailScript) {
            $JSFunctions[] = $emailScript;
        }

        unset($_SESSION['ajaxEmailsToSend']);
    }

    $yyheaderdone = 1;

    ob_start();
}

function GetSideMenuHTML($Parameters): string
{
    global $dtxleft_menu;

    $LeftMenu = new LeftMenu($Parameters);
    $LeftMenu->ExtraLinks = $Parameters['extra_links'] ?? null;

    $userLabels = $Parameters['userLabels'] ?? [];

    $MenuHTML = $LeftMenu->GetHTML($userLabels);
    $FloatingMenuHTML = '';

    $shouldShowFloatingMenu = ($Parameters['floating_menu'] ?? null) !== false
        && (($Parameters['table'] ?? null)
            || ($Parameters['panels'] ?? null)
            || ($Parameters['formarray'] ?? null)
            || ($Parameters['buttons'] ?? null));

    if ($shouldShowFloatingMenu) {
        $FloatingMenuHTML = $LeftMenu->GetFloatingMenuHTML();
    }

    $dtxleft_menu = $MenuHTML . $FloatingMenuHTML;

    return $dtxleft_menu;
}

/**
 * @return never
 */
function obExit(): void
{
    @ob_end_flush();

    exit;
}

/**
 * @desc called when something goes wrong - interrupts the flow of the script to display a page with
 * an error message.
 *
 * @param string|array $error The error message to display
 * @param string $title the title to give the page
 *
 * @return never
 *
 * @codeCoverageIgnoreStart
 * No unit test because there is no real testable output.
 */
function fatal_error($error, $title = 'Error', $module = 'ADM', $cssClass = 'error_div'): void
{
    global $yyheaderdone, $dtxtitle;

    $LoggedIn = (isset($_SESSION['logged_in']));

    Log::error(
        'Fatal Error, Exiting And Displaying Error To User.',
        [
            'error' => $error,
            'logged_in' => $LoggedIn,
            'stack_trace' => (new RuntimeException())->getTrace(),
        ],
    );

    if (!$yyheaderdone) {
        $dtxtitle = $title;

        Container::get(PageTitleProvider::class)
            ->preGeneratePageTitleHtml(
                '',
                $module,
                $dtxtitle,
            );

        if ($LoggedIn) {
            GetSideMenuHTML(['module' => $module, 'error' => true]);

            $template = null;

            template_header();
        } else {
            $template = new Template(['noMenu' => true]);

            template_header($template, null);
        }
    }

    if (is_array($error)) {
        foreach ($error as $value) {
            echo '
            <div class="' . $cssClass . '">' . $value . '</div>';
        }
    } else {
        echo '
        <div class="' . $cssClass . '">' . $error . '</div>';
    }

    footer($template);
    obExit();
}

function getJSFunctions()
{
    global $JSFunctions;

    $JSFunctionString = '';

    if (is_array($JSFunctions) && ($_GET['print'] ?? null) != 1) {
        $JSFunctionString .= '<script language="JavaScript" type="text/javascript">
        ';

        foreach ($JSFunctions as $Function) {
            $JSFunctionString .= $Function . '
            ';
        }

        $JSFunctionString .= '</script>';
    }

    return $JSFunctionString;
}

function echoJSFunctions()
{
    echo getJSFunctions();
}

function getSecuritySettingJS($aParams = [])
{
    global $JSFunctions;

    $JS = '
            var currentModule = new Array();

            var ModuleList = [\'' . implode('\', \'', array_merge(array_keys(getModArray()), ['ALL_MODULES', 'ACR'])) . '\'];

            window.onload = function showFirstModuleSettings()
            {';

    if (!$aParams['not_parameters']) {
        $JS .= '
                jQuery.each(ModuleList, function(index, value) {

                    jQuery(\'#\' + value + \'_parameters\').hide();
                    jQuery(\'#\' + value + \'_notifications\').hide();

                });

                if (jQuery("#parametersModule").length)
                {
                    currentModule[\'parameters\'] = jQuery("#parametersModule").val();
                    jQuery(\'#\' + currentModule[\'parameters\'] + \'_parameters\').show();
                }';
    }

    $JS .= '
                try {
                    currentModule[\'security_settings\'] = jQuery("#securitySettingsModule").val();
                    jQuery(\'#\' + currentModule[\'security_settings\'] + \'_security_settings\').show();
                }
                catch(e) {
                    // Do nothing
                }
            }';

    $JSFunctions[] = $JS;

    $JSFunctions[] = '
            function showModuleSection(module, sectionType)
            {
                if (currentModule[sectionType] != module)
                {
                    document.getElementById(currentModule[sectionType] + \'_\' + sectionType).style.display = "none";

                    if (currentModule[sectionType] == "ACR")
                    {
                        if (document.getElementById(currentModule[sectionType] + \'_notifications\'))
                        {
                            document.getElementById(currentModule[sectionType] + \'_notifications\').style.display = "none";
                        }
                    }
                }

                var div = document.getElementById(module + \'_\' + sectionType);
                div.style.display = "block";
                currentModule[sectionType] = module;

                if (module == "ACR")
                {
                    if (document.getElementById(module + \'_notifications\'))
                    {
                        document.getElementById(module + \'_notifications\').style.display = "block";
                    }
                }
            }';
}

function ParseHTMLTableForExport($html, $output_format, $pdf = null)
{
    libxml_use_internal_errors(true); // required to prevent IIS throwing a wobbly on some installations when loading invalid HTML markup

    $html = '<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/></head>' . $html . '</html>';

    $doc = new DOMDocument('1.0', 'utf-8');
    @$doc->loadHTML($html);
    $telements = $doc->getElementsByTagName('table');

    // remove attributes from tables
    foreach ($telements as $telement) {
        $telement->removeAttribute('class');
        $telement->removeAttribute('border');
        $telement->removeAttribute('width');
        $telement->removeAttribute('bgcolor');
        $telement->setAttribute('border', '1');

        if ($output_format == 'pdf') {
            $telement->removeAttribute('width');
            $telement->setAttribute('width', $pdf->width);
        }
    }

    if ($_REQUEST['exportmode'] == 'listing') {
        // deal with cell widths
        $thelements = $doc->getElementsByTagName('th');
        $lineWidth = 0;

        foreach ($thelements as $thelement) {
            if ($thelement->getAttribute('excelwidth') != '') {
                $lineWidth += $thelement->getAttribute('excelwidth');
            }
        }

        foreach ($thelements as $thelement) {
            $thelement->setAttribute('bgcolor', '#162D54');

            if ($output_format == 'pdf') {
                $thelement->setAttribute('color', '#FFFFFF');
            } else {
                $thelement->setAttribute('style', 'color: #FFFFFF;');
            }

            $excelwidth = $thelement->getAttribute('excelwidth');

            if ($output_format == 'excel' && $excelwidth != '' && $excelwidth > 0) {
                $thelement->setAttribute('width', round($excelwidth * 10 / $lineWidth * 100 + 20));
                $thelement->removeAttribute('excelwidth');
            }
        }
    }

    $tdelements = $doc->getElementsByTagName('td');

    foreach ($tdelements as $tdelement) {
        // some colouring
        if ($_REQUEST['exportmode'] == 'listing') {
            if ($tdelement->getAttribute('class') == 'titlebg2') {
                $tdelement->setAttribute('bgcolor', '#6E94B7');

                if ($output_format == 'pdf') {
                    $tdelement->setAttribute('color', '#FFFFFF');
                } else {
                    $tdelement->setAttribute('style', 'color: #FFFFFF;');
                }
            }
        }

        if ($_REQUEST['exportmode'] == 'crosstab' && ($tdelement->getAttribute('class') == 'crosstab header'
            || $tdelement->getAttribute('class') == 'crosstab header top-header')) {
            $tdelement->setAttribute('bgcolor', '#162D54');

            if ($output_format == 'pdf') {
                $tdelement->setAttribute('color', '#FFFFFF');
            } else {
                $felement = $tdelement->firstChild;

                if ($felement != null) {
                    $fvalue = $felement->textContent;
                    $fontelement = $doc->createElement('font');
                    $fontelement->setAttribute('color', '#FFFFFF');
                    $newelement = $doc->createTextNode($fvalue);
                    $fontelement->appendChild($newelement);
                    $tdelement->replaceChild($fontelement, $felement);
                }
            }
        }

        if ($_REQUEST['exportmode'] == 'crosstab' && $tdelement->getAttribute('class') == 'crosstab total data') {
            $tdelement->setAttribute('bgcolor', '#afc6db');
        }

        $tdelement->removeAttribute('onmouseover');
        $tdelement->removeAttribute('onmouseout');
        $tdelement->removeAttribute('onclick');
        $tdelement->removeAttribute('onClick');

        if ($tdelement->hasChildNodes()) {
            $aelement = $tdelement->firstChild;

            if ($aelement->nodeName == 'a') {
                // This deals with every type of content that we have in cells specially progress notes
                $children = $aelement->childNodes;

                $newelement = $doc->createElement('span');

                foreach ($children as $child) {
                    if ($child->nodeName == 'br') {
                        $newelement->appendChild($doc->createElement('br'));
                    } else {
                        $newelement->appendChild($doc->createTextNode($child->nodeValue));
                    }
                }

                $tdelement->replaceChild($newelement, $aelement);
            }
        }

        if ($output_format == 'pdf') {
            // truncate text to 1024 chars
            foreach ($tdelement->childNodes as $node) {
                if ($node->nodeName == '#text' && \UnicodeString::strlen($node->nodeValue) > 1024) {
                    $node->nodeValue = \UnicodeString::substr($node->nodeValue, 0, 1024) . '...';
                }
            }
        }
    }

    return $doc->saveHTML();
}

function OutputPDFTable($html, $mode, $orientation, $paperSize, PDFTable $pdf)
{
    // PDFTable doesn't seem to like th elements, so convert to td
    $html = str_replace('<th', '<td', $html);
    $html = str_replace('</th>', '</td>', $html);
    // Remove blue background on PDF printouts
    $html = str_replace('bgcolor="#E3EFFF"', '', $html);

    // Fix encoded symbols
    $html = str_replace('&amp;', '&', $html);

    // Replace UTF-8 pound code for ASCII because the font used to generate the PDF doesn't support UTF-8
    $html = str_replace('£', chr(163), $html);

    if ($mode == 'crosstab') {
        // Remove portion of javascript from the PDF output
        // This is being added in the WriteCrosstabReport.php view file
        $html = substr($html, 0, strpos($html, '<script'));
        $html .= '</body></html>';
    }

    $doc = new DOMDocument('1.0', 'utf-8');
    @$doc->loadHTML($html);

    $body = $doc->documentElement->getElementsByTagName('body')->item(0);
    $reportTable = $doc->getElementById($mode . 'table');

    // get table attributes
    $cellspacing = $reportTable->getAttribute('cellspacing');
    $cellpadding = $reportTable->getAttribute('cellpadding');
    $border = $reportTable->getAttribute('border');
    $width = $reportTable->getAttribute('width');
    $id = $mode . 'table';

    // count the number of rows in the report table
    $totalRows = 0;
    foreach ($reportTable->childNodes as $node) {
        if ($node->nodeName == 'tr') {
            ++$totalRows;
        }
    }

    // count the number of columns in the report table
    $totalCols = 0;

    foreach ($reportTable->firstChild->childNodes as $node) {
        if ($node->nodeName == 'td') {
            ++$totalCols;
        }
    }

    $pdf->AddPage($orientation, $paperSize);
    $pdf->htmltable($html);

    return $pdf;
}

/**
 * very basic code to strip out HTML tags - we may need to extend this in the future if more complex situations arise.
 */
function StripHTML($String)
{
    return preg_replace('/\<[^\<\>]+\>/u', '', $String);
}

/**
 * allows us to access certain labels from javascript. If this becomes too unweildy, we may need to make
 * it more general or work out another way of doing it.
 */
function AddCustomTextToJSGlobal()
{
    global $JSFunctions;

    $JSFunctions[] =
        "txt['email_address_missing_err'] = '" . addslashes(_fdtk('email_address_missing_err')) . "';" . " \r\n " .
        "txt['email_attachment_maximum_size_err'] = '" . addslashes(sprintf(_fdtk('email_attachment_maximum_size_err'), AwsSesEmailLimits::AWS_SES_MAIL_SIZE_MB_LIMIT)) . "';" . " \r\n " .
        "txt['email_attachment_maximum_selected_err'] = '" . addslashes(sprintf(_fdtk('email_attachment_maximum_selected_err'), AwsSesEmailLimits::AWS_SES_MAIL_SIZE_MB_LIMIT)) . "';" . " \r\n " .
        "txt['select_word_template_err'] = '" . addslashes(_fdtk('select_word_template_err')) . "';" . " \r\n " .
        "txt['fbk_email_check_recipients'] = '" . addslashes(_fdtk('fbk_email_check_recipients')) . "';" . " \r\n " .
        "txt['fbk_email_check_amend'] = '" . addslashes(_fdtk('fbk_email_check_amend')) . "';" . " \r\n " .
        "txt['btn_send_and_save'] = '" . addslashes(_fdtk('btn_send_and_save')) . "';" . " \r\n " .
        "txt['btn_save_without_sending'] = '" . addslashes(_fdtk('btn_save_without_sending')) . "';" . " \r\n " .
        "txt['btn_feedback_cancel'] = '" . addslashes(_fdtk('btn_feedback_cancel')) . "';" . " \r\n " .
        "txt['profile_clear_check'] = '" . addslashes(_fdtk('profile_clear_check')) . "';" . " \r\n " .
        "txt['retain_user_settings'] = '" . addslashes(_fdtk('retain_user_settings')) . "';" . " \r\n " .
        "txt['ajax_wait_msg'] = '" . addslashes(_fdtk('ajax_wait_msg')) . "';" . " \r\n " .
        "txt['wait_loading_section_all'] = '" . addslashes(_fdtk('loading_all_sections_content')) . "';" . " \r\n " .
        "txt['btn_cancel'] = '" . addslashes(_fdtk('btn_cancel')) . "';" . " \r\n" .
        "txt['btn_continue'] = '" . addslashes(_fdtk('btn_continue')) . "';" . " \r\n" .
        "txt['save_wait_msg'] = '" . addslashes(_fdtk('dashboard_please_wait')) . "';" . " \r\n " .
        "txt['import_table_data'] = '" . addslashes(_fdtk('import_table_data')) . "';" . " \r\n " .
        "txt['isd_validate_year'] = '" . addslashes(_fdtk('isd_validate_year')) . "';" . " \r\n " .
        "txt['isd_validate_edition'] = '" . addslashes(_fdtk('isd_validate_edition')) . "';" . " \r\n " .
        "txt['isd_export_error_title'] = '" . addslashes(_fdtk('isd_export_error_title')) . "';" . " \r\n " .
        "txt['validate_step_numbers_error_msg'] = '" . addslashes(_fdtk('validate_step_numbers_error_msg')) . "';" . " \r\n " .
        "txt['locked'] = '" . addslashes(_fdtk('locked')) . "';" . " \r\n " .
        "txt['unlocked'] = '" . addslashes(_fdtk('unlocked')) . "';" . " \r\n " .
        "txt['confirm_or_cancel'] = '" . addslashes(_fdtk('confirm_cancel_form')) . "';" . " \r\n " .
        "txt['alert_title'] = '" . addslashes(_fdtk('alert')) . "';" . " \r\n " .
        "txt['alert_message'] = '" . addslashes(_fdtk('alert_message')) . "';" . " \r\n " .
        "txt['no_notification_emails_sent'] = '" . addslashes(_fdtk('no_notification_emails_sent')) . "';" . " \r\n " .
        "txt['no_messages_for_inc'] = '" . addslashes(_fdtk('no_messages_for_inc')) . "';" . " \r\n " .
        "txt['delegation_rule_sub_module_access_notice'] = '" . addslashes(_fdtk('delegation_rule_sub_module_access_notice')) . "';" . " \r\n " .
        "txt['delegation_rule_receive_email_access_notice'] = '" . addslashes(_fdtk('delegation_rule_receive_email_access_notice')) . "';" . " \r\n " .
        "txt['delegation_revoke_delegates_notice'] = '" . addslashes(_fdtk('delegation_revoke_delegates_notice')) . "';" . " \r\n " .
        "txt['delegation_reinstate_delegates_notice'] = '" . addslashes(_fdtk('delegation_reinstate_delegates_notice')) . "';" . " \r\n " .
        "txt['submit_disabled_section_load'] = '" . addslashes(_fdtk('submit_disabled_section_load')) . "';" . " \r\n " .
        "txt['delete_packaged_report_q'] = '" . addslashes(_fdtk('delete_packaged_report_q')) . "';" . " \r\n " .
        "txt['minimum_one_char'] = '" . addslashes(_fdtk('search_minimum_characters')) . "';" . " \r\n " .
        "txt['search_minimum_entry'] = '" . addslashes(_fdtk('search_minimum_entry')) . "';" . " \r\n " .
        "txt['valid_date'] = '" . addslashes(_fdtk('invalid_date')) . "';" . " \r\n " .
        "txt['valid_time'] = '" . addslashes(_fdtk('invalid_time')) . "';" . " \r\n " .
        "txt['valid_number'] = '" . addslashes(_fdtk('invalid_number')) . "';" . " \r\n " .
        "txt['valid_value'] = '" . addslashes(_fdtk('invalid_value')) . "';" . " \r\n " .
        "txt['matching_contacts'] = '" . addslashes(_fdtk('matching_contacts')) . "';" . " \r\n " .
        "txt['loading_content']= '" . addslashes(_fdtk('loading_content')) . "';" . " \r\n " .
        "txt['wait_loading'] = '" . addslashes(_fdtk('loading_section_content')) . "';" . " \r\n " .
        "txt['select_value_for'] = '" . addslashes(_fdtk('select_value_for')) . "';" .
        "txt['generate_confirm'] = '" . addslashes(_fdtk('generate_confirm')) . "';" .
        "txt['report_designer_please_select'] = '" . addslashes(_fdtk('report_designer_please_select')) . "';" .
        "txt['report_designer_first'] = '" . addslashes(_fdtk('report_designer_first')) . "';" .
        "txt['mendatory_field_message'] = '" . addslashes(_fdtk('mendatory_field_message')) . "';" .
        "txt['next_field'] = '" . addslashes(_fdtk('next_field')) . "';" .
        "txt['see_more_results_header'] = '" . addslashes(_fdtk('see_more_results_header')) . "';" .
        "txt['see_more_results_see_more_main'] = '" . addslashes(_fdtk('see_more_results_see_more_main')) . "';" .
        "txt['see_more_results_see_more_sub'] = '" . addslashes(_fdtk('see_more_results_see_more_sub')) . "';";

    $extendTxt = '';

    foreach ([
        'drag_and_drop_or_browse_files_prefix',
        'documents',
        'drag_and_drop_alert_button_ok',
        'add_file',
        'replace_file',
        'drag_and_drop_errors_cannot_save_while_files_in_progress',
        'drag_and_drop_errors_should_not_leave_while_files_in_progress',
        'drag_and_drop_errors_cannot_save_file_size_limit_exceeded',
        'drag_and_drop_errors_cannot_save_empty_file',
        'drag_and_drop_errors_file_size_limit_exceeded',
        'drag_and_drop_errors_empty_file',
        'drag_and_drop_errors_unknown_issue_with_file',
        'inc_psims_patient_missing_warning',
    ] as $translation) {
        $translationKey = json_encode($translation, JSON_THROW_ON_ERROR);
        $translationString = json_encode(_fdtk($translation), JSON_THROW_ON_ERROR);

        $extendTxt .= <<<JS
                txt[{$translationKey}] = {$translationString};
            JS;
    }

    $JSFunctions[] = $extendTxt;
}

/**
 * used by SaveXXX.php to print out errors when saving (can probably be replaced in the future).
 *
 * @return never
 */
function SaveError($error): void
{
    global $yyheaderdone;

    Log::error(
        'Error With Saving, Exiting Leaving Blank Record.',
        [
            'error' => $error,
            'stack_trace' => (new RuntimeException())->getTrace(),
        ],
    );

    if ($yyheaderdone) {
        echo '<script language="JavaScript" type="text/javascript">
document.getElementById("waiting").style.display="none";
</script>';
    }

    fatal_error($error);
    template_header();
    echo CompatEscaper::encodeCharacters($error);
    obExit();
}

/**
 * Processes values in an array with the htmlentities function.
 *
 * This function reurns an array after the htmlentities function has been applied to all values in the array.
 * Can be useful for processing data which needs to be displayed without any HTML being applied to the output.
 * REFACTOR: This can be replaced with array_walk_recursive($array, 'Escaper::escapeForHTML')
 *
 * @param string[] $ValuesToProssess The source array
 *
 * @return string[]
 */
function ProcessHTMLEntities($ValuesToProssess)
{
    $ProcessedHTMLEntities = [];

    foreach ($ValuesToProssess as $key => $value) {
        if (!is_array($value)) {
            $ProcessedHTMLEntities[$key] = Escaper::escapeForHTML($value);
        } else {
            // Recursively process values in sub-arrays
            $ProcessedHTMLEntities[$key] = ProcessHTMLEntities($value);
        }
    }

    return $ProcessedHTMLEntities;
}

/**
 * @desc Adds javascript instructions to disable fields on the DIF1 form, based on the fact that they
 * were previously matched with db records.
 *
 * @param string $InputFieldName the id of the field to be disabled
 * @param array $FieldDef Array of field parameters
 */
function AddJSDisableScript($InputFieldName, $FieldDef)
{
    global $JSFunctions;

    switch ($FieldDef['Type']) {
        case 'ff_select':
        case 'multilistbox':
            $JSFunctions[] = '
                jQuery(document).ready(function()
                {
                    if (jQuery("input[id=' . $InputFieldName . '_title]").length)
                    {
                        jQuery("input[id=' . $InputFieldName . '_title]").disable();
                    }
                });';

            break;
        default:
            $JSFunctions[] = '
                jQuery(document).ready(function()
                {
                    if (jQuery("#' . $InputFieldName . '").length)
                    {
                        jQuery("#' . $InputFieldName . '").attr("disabled", true);
                        AddHiddenField(jQuery("#' . $InputFieldName . '"));
                        if (jQuery("#img_cal_' . $InputFieldName . '").length)
                        {
                            jQuery("#img_cal_' . $InputFieldName . '").css("visibility", "hidden");
                        }
                    }
                });';

            break;
    }

    $JSFunctions[] = '
        jQuery(document).ready(function()
        {
            if (jQuery("#check_btn_' . $InputFieldName . '").length)
            {
                jQuery("#check_btn_' . $InputFieldName . '").attr("disabled", true);
            }
            AddCustomHiddenField("' . $InputFieldName . '_disabled", "' . $InputFieldName . '_disabled", "1");
        });';
}

/**
 * @desc constructs a header to be used in place of {@link template_header()} when we don't want the full datix
 * banner top and bottom.
 *
 * @return string the HTML describing the page header
 */
function getBasicHeader(?AssetManager $assetManagerToMerge = null)
{
    global $dtxtitle;

    $assetManager = new AssetManager();
    $assetManager->addCss([
        'css/global.css',
        'css/nav.css',
        'src/dashboard/css/dashboard.css',
        'css/crosstab.css',
        'css/prince.css',
        'vendor/twbs/bootstrap/dist/css/bootstrap.css',
        'css/mandatory-field-messages.css',
        'css/uncompleted-search-messages.css',
    ]);

    $assetManager->addJs([
        'js_functions/jquery/jquery-3.6.0.min.js',
        'js_functions/jquery/jquery-migrate-3.3.2.min.js',
        'dist/dist.js',
    ]);

    $registry = Container::get(Registry::class);
    if ($registry->getParm('CSRF_PREVENTION', 'N')->isTrue()) {
        $assetManager->sendVariablesToJs([
            'token' => \CSRFGuard::getCurrentToken(),
            'suppressClientSideCSRF' => false,
        ], false);
        $assetManager->addJs(['js_functions/tokenizer.js']);
    } else {
        $assetManager->sendVariablesToJs(['token' => ''], false);
    }

    if ($assetManagerToMerge instanceof AssetManager) {
        $assetManager->mergeAssets($assetManagerToMerge);
    }

    return '
    <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html>
            <head>
                <title>' . $dtxtitle . '</title>
                ' . $assetManager->buildCss() . '
                ' . $assetManager->buildJs() . '
                ' . $assetManager->outputVarsToJs() . '
            </head>
            <body>';
}

/**
 * @desc constructs a footer to be used in place of {@link footer()} when we don't want the full datix banner top
 * and bottom.
 *
 * @return string the HTML describing the page footer
 */
function getBasicFooter()
{
    return '
            </body>
        </html>';
}

/**
 * Returns Javascript used to confirm form cancellation.
 *
 * @return string the javascript for confirming form cancellation
 */
function getConfirmCancelJavascript($formID = '')
{
    return 'if(confirm(\'' . addslashes(_fdtk('confirm_cancel_form')) . '\')){' .
        'if (' . ($formID ?: 'this.form') . '.rbWhat != null){' . ($formID ?: 'this.form') . '.rbWhat.value = \'Cancel\';} submitClicked = true; ' . ($formID ?: 'this.form') . '.submit(); }';
}
