<?php

use app\models\datetime\DateTimeGeneratorFactory;
use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\securityGroups\SecurityGroupServiceFactory;
use Source\generic_modules\ModuleDefKeys;
use src\framework\query\Query;
use src\framework\query\QueryFactory;
use src\framework\query\SqlWriter;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\medications\helpers\MedicationSearchFieldsHelper;
use src\search\helpers\AtCodesHelper;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\system\moduledefs\ModuleDefs;

/**
 * @deprecated Use DatixDBQuery instead
 */
function db_query($sql, $suppress_errors = false)
{
    // we need to use PDO here to prevent transaction conflicts between the two connection methods.
    $query = new DatixDBQuery($sql);
    $query->prepare();
    $result = $query->execute([], $suppress_errors);
    $PDOStatement = $query->PDOStatement;
    unset($query);

    // sometimes we want to return bool, and sometimes a result set - we should clean up these parts
    // of the code as we find them

    if ($result) {
        return $PDOStatement;
    }

    return $result;
}

/**
 * @deprecated Use DatixDBQuery instead
 */
function db_fetch_array($result)
{
    $result_array = $result->fetch();

    if ($result_array) {
        $result_array = array_map(static function (?string $row): string {
            if ($row === null) {
                return '';
            }

            return rtrim($row);
        }, $result_array);
    }

    return $result_array;
}

/**
 * Append to an SQL 'where' statement. Pass the field and the value that's going to search the field, and generate
 * part of the where statement for it.
 *
 * @param string $Module Module the field belongs to - to retrieve field definition
 * @param string $name The name of the field
 * @param string $value The content of the field
 * @param string $search The where clause returned
 * @param string $tableprefix Table prefix of column (optional)
 * @param array $PDOPlaceholders [Not sure what this is - CA]
 *
 * @return bool Was the amendment to the 'where statement' made correctly. This can fail if the provided
 *              information was improperly formatted. E.g. searching for a date 30/11/2014 in U.S. system.
 *
 * @throws InvalidDataException
 *
 * @todo IQ-41816 refactor so date are inline with other usages such as UserDateToSQLDate
 */
function MakeFieldWhere(
    $Module, // Module the field belongs to - to retrieve field definition
    $name, // The name of the field
    $value, // The content of the field
    &$search, // The where clause returned
    $tableprefix = '',
    $PDOPlaceholders = []
) {
    global $FieldDefsExtra, $ModuleDefs;

    $isOneToOneTable = in_array($tableprefix, [
        Tables::INCIDENTS_TIME_CHAIN,
        Tables::PSIMS_INCIDENT,
        Tables::PSIMS_INCIDENT_CODED_FIELDS,
        Tables::PSIMS_INCIDENT_EQUIPMENT_FIELDS,
        Tables::PSIMS_INCIDENT_MEDICATION_FIELDS,
        Tables::PSIMS_CONTACT_FIELDS,
        Tables::INCIDENT_PSIMS_RESPONSE,
        Tables::INCIDENT_PSIMS_RESPONSE_WARNINGS,
    ], true);
    $moduleTable = $ModuleDefs[$Module][ModuleDefKeys::VIEW] ?? $ModuleDefs[$Module][ModuleDefKeys::TABLE];
    $search = '';

    $parts = explode('_', $name);
    $isUDF = ($parts[0] == 'udv');

    if ($parts[2] == '1' && $parts[3] == '1') {
        $name = substr($name, 0, strlen($name) - 2);
    }

    if (is_string($value)) {
        $value = trim($value);
    }

    if (
        $value == ''
        || (
            !$isUDF
            && !isset($FieldDefsExtra[$Module][$name])
            && !in_array($name, MedicationSearchFieldsHelper::SEARCH_DRUG_REFERENCE_FIELDS, true)
        )
    ) {
        // Field not defined or blank - skip field
        return true;
    }

    $FieldType = $isUDF
        ? $parts[1]
        : $FieldDefsExtra[$Module][$name]['Type'] ?? 'string';

    if (is_array($value)) {
        $value = implode('|', $value);
    }

    // Check whether the field contains multiple values
    $sepPipePos = \UnicodeString::strpos($value, '|');
    $sepAmpPos = \UnicodeString::strpos($value, '&');
    $containsAtSymbol = \UnicodeString::strpos($value, '@') !== false;
    $atCodeHelper = Container::get(AtCodesHelper::class);

    if ($FieldType == 'money' && !($sepAmpPos || $sepPipePos)) {
        $value = preg_replace("/[^0-9\.\<\>=!?]/u", '', $value);
    }

    if ($FieldType === FieldInterface::DATE_DB) {
        $value = correctDateValueForSearch($value);
        if ($tableprefix != '' && \UnicodeString::strpos($name, '.') === false) {
            $datename = $name;
            $name = $tableprefix . '.' . $name;
        }

        // picks up just the date part, ignoring the time. Otherwise we get problems with '=' matches.
        $name = 'CAST( FLOOR( CAST( ' . $name . ' AS FLOAT ) ) AS DATETIME)';
    }

    if ($FieldType == 'checkbox') {
        // checkboxes are basically yes/no fields and pass their values as Y and N
        $FieldType = 'yesno';
    }

    $valueTmp = $value;
    $FieldWhere = '';

    $strDataTypes = [
        'string',
        'textarea',
        'ff_select',
        'yesno',
        'grading',
        'combobox',
        'string_search',
        'email',
        'formfield',
        'time',
        'text',
        'money',
        'number',
        'duration',
    ];
    $rangeDataTypes = [
        'date',
        'number',
        'money',
    ];

    if ($sepPipePos !== false || $sepAmpPos !== false) {
        $search = '';
        $sepPrev = '';
        $valueStr = $valueTmp;

        while (\UnicodeString::strlen($valueStr) > 0) {
            // Get the position and type of the first separator
            $sepPipePos = \UnicodeString::strpos($valueStr, '|');
            $sepAmpPos = \UnicodeString::strpos($valueStr, '&');

            if ($sepPipePos !== false || $sepAmpPos !== false) {
                if ($sepPipePos !== false && $sepAmpPos !== false) {
                    $sepPos = min($sepPipePos, $sepAmpPos);
                    $sep = ($sepPipePos < $sepAmpPos ? '|' : '&');
                } elseif ($sepPipePos !== false) {
                    $sepPos = $sepPipePos;
                    $sep = '|';
                } else {
                    // ($sepAmpPos !== false)
                    $sepPos = $sepAmpPos;
                    $sep = '&';
                }
            } else {
                $sepPos = false;
                $sep = '';
            }

            // Extract the value on the left of the separator (if there is one) and build where clause
            if ($sepPos !== false) {
                $valueStrLeft = \UnicodeString::substr($valueStr, 0, $sepPos);
            } else {
                // No more separators this will be the last pass,
                // use the whole of the remaining string
                $valueStrLeft = $valueStr;
            }

            // If date, then need to pass MakeFieldWhere the real field name rather than the CAST value
            // in order to handle AND and OR symbols.
            if ($FieldType == 'date') {
                if ($isUDF) {
                    $name = 'udv_date';
                } else {
                    $name = $datename;
                }
            }

            if (MakeFieldWhere($Module, $name, $valueStrLeft, $FieldWhere, $tableprefix) === false) {
                return false;
            }

            $searchWhereTmp = $FieldWhere;

            if ($search != '' && $sepPrev != '') {
                $search .= ($sepPrev == '&' ? ' AND ' : ' OR ');
            }

            $search .= $searchWhereTmp;

            if ($sepPos !== false) {
                $valueStr = \UnicodeString::substr($valueStr, $sepPos + 1);
                $sepPrev = $sep;
            } else {
                $valueStr = '';
                $sepPrev = '';
            }
        }

        if ($search) {
            $search = "({$search})";
        }

        return true;
    }

    if ($isUDF && $FieldType === FieldInterface::MULTI_SELECT_DB) { // multicode vals are stored in udv_string.
        $name = 'udv_string';
    }

    if ($tableprefix != '' && \UnicodeString::strpos($name, '.') === false) {
        $name = $tableprefix . '.' . $name;
    }

    // checking for null and not null values
    if (\UnicodeString::strtolower($valueTmp) == 'is not null' || $valueTmp == '==') {
        if ($FieldType == 'date') {
            if (empty($datename)) {
                $datename = $name;
            }

            $search = "{$name} is not null and {$datename} != ''";
        } elseif (in_array($FieldType, $strDataTypes)) {
            $search = "{$name} is not null and {$name} not like ''";
        } else {
            $search = "{$name} is not null";
        }

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }

    if (\UnicodeString::strtolower($valueTmp) == 'is null' || $valueTmp == '=') {
        if (in_array($FieldType, $strDataTypes)) {
            $search = "({$name} is null or {$name} like '')";
        } else {
            $search = "{$name} is null";
        }

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }

    if (($FieldType == 'number' || $FieldType == 'money') && preg_match('/[^ -<>=:.,\$£!0-9]/u', $value) > 0
        && !in_array($value, $PDOPlaceholders) && \UnicodeString::stripos($value, '@USER_') === false) {
        AddSessionMessage('ERROR', 'An invalid character was used in the field "' . Labels_FormLabel::GetFormFieldLabel($name, '', $Module, '') . '"');

        return false;
    }

    if ($FieldType == 'money' && $value === '') {
        AddSessionMessage('ERROR', 'An invalid character was used in the field "' . Labels_FormLabel::GetFormFieldLabel($name, '', $Module, '') . '"');

        return false;
    }

    $value = str_replace("'", "''", $value); // single to double singles

    // searching on Date and numeric ranges
    if (in_array($FieldType, $rangeDataTypes) && \UnicodeString::strpos($value, ':') !== false
        && !in_array($value, $PDOPlaceholders)) {
        $valueparts = explode(':', $value);

        if (!$valueparts[0] && !$valueparts[1]) {
            return false;
        }

        $valueparts[0] = \UnicodeString::trim($valueparts[0]);
        $valueparts[1] = \UnicodeString::trim($valueparts[1]);

        if ($valueparts[0] && $valueparts[1]) {
            if ($FieldType == 'date') {
                if (!validStringDate($valueparts[0]) || !validStringDate($valueparts[1])) {
                    AddSessionMessage('ERROR', 'One or both of the dates entered as part of a date range in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

                    return false;
                }

                $value1 = '\'' . UserDateToSQLDate($valueparts[0]) . '\'';
                $value2 = '\'' . UserDateToSQLDate($valueparts[1]) . '\'';

                if ($value1 == 'NULL' || $value2 == 'NULL') { // one or both of the dates entered is invalid
                    AddSessionMessage('ERROR', 'One or both of the dates entered as part of a date range in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

                    return false;
                }
            } else {
                $value1 = $valueparts[0];
                $value2 = $valueparts[1];
            }

            $search = "({$name} BETWEEN {$value1} AND {$value2})";

            if ($isOneToOneTable) {
                $search = sprintf(
                    '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                    $moduleTable,
                    $tableprefix,
                    $search,
                );
            }

            return true;
        }

        if ($FieldType == 'date') {
            if (!validStringDate($valueparts[0])) {
                AddSessionMessage('ERROR', 'The date entered in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

                return false;
            }

            $value = '\'' . UserDateToSQLDate($valueparts[$valueparts[0] ? 0 : 1]) . '\'';
        } else {
            $value = $valueparts[$valueparts[0] ? 0 : 1];
        }

        $search = $name . ($valueparts[0] ? '>=' : '<=') . $value;

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }

    // checking for two character SQL operator, this stuff is terrifying.
    $operator = mb_substr($value, 0, 2);
    if (in_array($operator, ['==', '!=', '>=', '<='], true)) {
        $value = trim(mb_substr($value, 2));
        $shiftFormat = correctDateValueForSearch($value);

        if (
            $FieldType === FieldInterface::DATE_DB
            && (
                ($containsAtSymbol && !$atCodeHelper->isAtCode($shiftFormat))
                || (!$containsAtSymbol && !validStringDate($shiftFormat))
            )
        ) {
            AddSessionMessage('ERROR', 'The date entered in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

            return false;
        }

        if ($FieldType == 'duration') {
            $fieldDefs = Container::get(Registry::class)->getFieldDefs();
            $fieldDef = $fieldDefs[$name];

            try {
                $value = $fieldDef->formatForSearch($value);
            } catch (Exception $e) {
                return false;
            }
        }

        // Provide NOT LIKE search facility - eg: !=AA*
        if ($operator === '!=' && \UnicodeString::strpos($value, '*') !== false) {
            if (!in_array($FieldType, $strDataTypes)) {
                return false;
            }
            if ($FieldType == 'duration') {
                $search = "{$name} {$operator} {$value[0]}";

                if ($isOneToOneTable) {
                    $search = sprintf(
                        '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                        $moduleTable,
                        $tableprefix,
                        $search,
                    );
                }

                return true;
            }
            $search = "{$name} not like " . QuoteData($value, $FieldType);

            if ($isOneToOneTable) {
                $search = sprintf(
                    '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                    $moduleTable,
                    $tableprefix,
                    $search,
                );
            }

            return true;
        }

        if ($operator === '!=') {
            if ($FieldType == 'multilistbox') {
                $search = " NOT ({$name} = '" . $value . "' OR {$name} LIKE '" . $value . " %' OR {$name} LIKE '% " . $value . "' OR {$name} LIKE '% " . $value . " %')";

                if ($isOneToOneTable) {
                    $search = sprintf(
                        '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                        $moduleTable,
                        $tableprefix,
                        $search,
                    );
                }

                return true;
            }

            $search = "({$name} {$operator} " . QuoteData($value, $FieldType) . " OR {$name} IS NULL)";

            if ($isOneToOneTable) {
                $search = sprintf(
                    '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                    $moduleTable,
                    $tableprefix,
                    $search,
                );
            }

            return true;
        }

        if (is_array($value)) {
            $search = "{$name} {$operator} " . QuoteData($value[0], $FieldType);
        } else {
            $search = "{$name} {$operator} " . QuoteData($value, $FieldType);
        }

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }


    // checking for single character SQL operator
    $operator = $value[0];
    if (in_array($operator, ['>', '<', '='], true)) {
        $value = trim(mb_substr($value, 1));

        // If the field being searched is a date field, then we need to ensure it's formatted correctly before
        // plugging it into the SQL query
        if ($FieldType === FieldInterface::DATE_DB) {
            $value = correctDateValueForSearch($value);
            if (
                ($containsAtSymbol && !$atCodeHelper->isAtCode($value))
                || (!$containsAtSymbol && !validStringDate($value))
            ) {
                AddSessionMessage('ERROR', 'The date entered in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

                return false;
            }
        } elseif ($FieldType == 'time') {
            $value = str_replace(':', '', $value);
            $search = "{$name} {$operator} " . QuoteData($value, $FieldType) . " AND {$name} != ''";
        } elseif ($FieldType == 'duration') {
            $fieldDefs = Container::get(Registry::class)->getFieldDefs();
            $fieldDef = $fieldDefs[$name];

            try {
                $value = $fieldDef->formatForSearch($value);
            } catch (Exception $e) {
                return false;
            }
            $search = "{$name} {$operator} {$value[0]}";
        } else {
            $search = "{$name} {$operator} " . QuoteData($value, $FieldType);
        }

        if (is_array($value)) {
            $search = "{$name} {$operator} " . QuoteData($value[0], $FieldType);
        } else {
            $search = "{$name} {$operator} " . QuoteData($value, $FieldType);
        }

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }

    if ($FieldType == 'time') {
        if (in_array($value, $PDOPlaceholders)) {
            $value2 = $value;
            $search = "{$name} LIKE {$value} OR {$name} LIKE {$value2}";
        } else {
            $value = str_replace(':', '', $value);
            $value2 = substr($value, 0, 2) . ':' . substr($value, 2);
            $search = "{$name} LIKE " . QuoteData($value, $FieldType) . " OR {$name} LIKE " . QuoteData($value2, $FieldType);
        }

        if ($isOneToOneTable) {
            $search = sprintf(
                '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                $moduleTable,
                $tableprefix,
                $search,
            );
        }

        return true;
    }

    if ($FieldType == 'duration') {
        $fieldDefs = Container::get(Registry::class)->getFieldDefs();
        $fieldDef = $fieldDefs[$name];

        try {
            $value = $fieldDef->formatForSearch($value);
            // Format for search returns an upper and lower bound
            // For exact searching, both values will be the same which MSSQL treats as an = search
            $search = "{$name} BETWEEN {$value[0]} AND {$value[1]}";

            if ($isOneToOneTable) {
                $search = sprintf(
                    '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
                    $moduleTable,
                    $tableprefix,
                    $search,
                );
            }

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    if (in_array($value, $PDOPlaceholders)) {
        if (in_array($FieldType, $strDataTypes)) {
            $search = "{$name} LIKE {$value}";
        } else {
            $search = "{$name} = {$value}";
        }
    } elseif (in_array($FieldType, $strDataTypes)) {
        $search = "{$name} like " . QuoteData($value, $FieldType);
    } elseif ($FieldType == 'multilistbox') {
        // searching on multicoded fields
        $search = "({$name} = '{$value}'";
        $search .= " OR {$name} like '{$value} %'";
        $search .= " OR {$name} like '% {$value}'";
        $search .= " OR {$name} like '% {$value} %')";
    } elseif ($FieldType === FieldInterface::DATE_DB) {
        $invalidAtSymbolEntry = $containsAtSymbol && !$atCodeHelper->isAtCode($value);
        $invalidDateEntry = !$containsAtSymbol && !validStringDate($value);

        if ($invalidAtSymbolEntry || $invalidDateEntry) {
            AddSessionMessage('ERROR', 'The date entered in the field "' . Labels_FormLabel::GetFormFieldLabel($datename, '', $Module, '') . '" is invalid');

            return false;
        }

        $search = "{$name} = " . QuoteData($value, $FieldType);
    }

    if ($isOneToOneTable) {
        $search = sprintf(
            '%s.recordid IN (SELECT recordid FROM %s WHERE %s)',
            $moduleTable,
            $tableprefix,
            $search,
        );
    }

    return true;
}

/**
 * @deprecated use (new SecurityGroupServiceFactory())->create()->makeSecurityWhereClause() instead
 */
function MakeSecurityWhereClause(
    $currentWhere,
    $module = Module::INCIDENTS,
    $initials = '',
    $tableSearch = '',
    $tableReplace = '',
    $email = false,
    $useCache = true,
    $parent_module = '',
    $recursiveHistory = [],
    $recordId = null
) {
    if (is_array($currentWhere) && !empty($currentWhere)) {
        $currentWhere = implode(' AND ', $currentWhere);
    }

    if (!$currentWhere) {
        $currentWhere = '';
    }

    $module ??= Module::INCIDENTS;

    if (empty($initials)) {
        // Sets the initials as the current user if not set
        $userSession = (new UserSessionFactory())->create();
        $initials = $userSession->getCurrentUser()->initials ?? '';
    }

    if (is_numeric($recordId)) {
        $recordId = (int) $recordId;
    }

    $securityGroupService = (new SecurityGroupServiceFactory())->create();

    return $securityGroupService->makeSecurityWhereClause(
        $currentWhere,
        $module,
        $initials,
        $tableSearch,
        $tableReplace,
        $email,
        $useCache,
        $recordId,
    );
}

function MakeUserSecurityWhereClause(
    $module,            // module code
    $con_id,            // user's contact ID
    $tableSearch = '',  // the table name to be substituted e.g. incidents_main
    $tableReplace = '' // the replacement table name e.g. incidents_report
) {
    $modParams = [
        'COM' => [
            'handler' => 'com_mgr',
        ],
        'INC' => [
            'handler' => 'inc_mgr',
        ],
    ];

    if (!array_key_exists($module, $modParams)) {
        // Defaults for other modules
        $modParams[$module] = [
            'handler' => null,
        ];
    }

    $where = [];

    if (!empty($where)) {
        $returnWhere = implode(' AND ', $where);
    }

    if ($returnWhere) {
        $returnWhere = \UnicodeString::strtoupper($returnWhere);
        $tableSearch = \UnicodeString::strtoupper($tableSearch);
        $tableReplace = \UnicodeString::strtoupper($tableReplace);

        // Also replace any occurrences of the table specified
        if ($tableSearch && $tableReplace && $tableSearch != $tableReplace) {
            $returnWhere = str_replace($tableSearch . '.', $tableReplace . '.', $returnWhere);
        }
    }

    return $returnWhere;
}

function GetActiveStaffWhereClause(): string
{
    $dateTimeGenerator = (new DateTimeGeneratorFactory())->create();
    $today = $dateTimeGenerator->getCurrentDateTimeAtClient();

    return "
        (sta_dclosed IS NULL OR sta_dclosed > '" . $today->format('Y-m-d H:i:s') . "')
        AND (sta_dopened IS NULL OR sta_dopened < '" . $today->format('Y-m-d H:i:s') . "')
        ";
}

/**
 * @param string $table The database table from which to select the data
 * @param string|array $columns The columns to be select from the table
 * @param string $where WHERE clause to restrict the number of records returned
 * @param string|array $sortcolumns The columns on which to sort the data returned
 * @param string $sortorder The sort order for the specified columns, "ASC" or "DESC"
 * @param int $pageNumber The page number to be displayed
 * @param int $recordsPerPage The number of records to be displayed on each page
 *                            Can be set to custom value, otherwise
 *                            it takes the value from LISTING_DISPLAY_NUM
 *                            It will then be set to the actual number of records returned
 * @param int $totalRecordCount The overral number of records, including all possible pages, for display purposes
 * @param int $actualRecordCount The actual number of records returned for the page,
 *                               useful for last page if less than $recordsPerPage is returned
 * @param string $sql The overral SQL statement for debug purposes
 *
 * @return bool|null
 */
function GetPagedResults(
    $table,
    $columns,
    $where,
    $sortcolumns,
    $sortorder,
    $pageNumber,
    &$recordsPerPage,
    &$totalRecordCount,
    &$actualRecordCount,
    &$sql
) {
    $where = replaceAtPrompts($where, $_GET['module']);
    $_SESSION[$_GET['module']]['PROMPT']['ORIGINAL_WHERE'] = $where;

    DoPromptSection(['module' => Sanitize::getModule($_GET['module'])]);

    if ($_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE']) {
        $where = $_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE'];
        $_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE'] = '';
    }

    if (!$recordsPerPage) {
        $recordsPerPage = GetParm('LISTING_DISPLAY_NUM', 20);
    }

    if ($pageNumber == 0) {
        $pageNumber = 1;
    }

    if (!is_array($columns)) {
        $columns = str_replace(' ', '', $columns);
        $columns = explode(',', $columns);
    }

    if (!is_array($sortcolumns)) {
        $sortcolumns = str_replace(' ', '', $sortcolumns);
        $sortcolumns = explode(',', $sortcolumns);
    }

    // Make sure that any sort column is also selected
    foreach (array_diff($sortcolumns, $columns) as $sortCol) {
        if (!isset($columns[$sortCol])) {
            $columns[$sortCol] = $sortCol;
        }
    }

    $sortorder = \UnicodeString::strtoupper($sortorder);
    $columns = implode(', ', $columns);
    $sortcolumns_rev = implode($sortorder == 'ASC' ? ' DESC,' : ' ASC,', $sortcolumns) . ($sortorder == 'ASC' ? ' DESC' : ' ASC');
    $sortcolumns = implode(" {$sortorder},", $sortcolumns) . " {$sortorder}";

    // Get overal number of records
    $sql = "SELECT count(*) as [count] FROM {$table}" . ($where ? " WHERE {$where}" : '');
    $request = db_query($sql);
    $row = db_fetch_array($request);
    $totalRecordCount = (int) $row['count'];

    // Create most inner SELECT
    $sql = 'SELECT TOP ' . min($recordsPerPage * $pageNumber, $totalRecordCount)
        . " {$columns} FROM {$table}"
        . ($where ? " WHERE {$where}" : '')
        . " ORDER BY {$sortcolumns}";

    // Create SELECT to requested range of records
    // and nest previous SELECT

    $sql = 'SELECT TOP ' . ($recordsPerPage * $pageNumber > $totalRecordCount ? ($totalRecordCount - $recordsPerPage * ($pageNumber - 1)) : $recordsPerPage)
        . " {$columns} FROM ({$sql}) AS [i]"
        . " ORDER BY {$sortcolumns_rev}";

    // Create outer SELECT to reverse the data in the correct sort order
    $sql = "SELECT {$columns} FROM ({$sql}) AS [j] ORDER BY {$sortcolumns}";

    $request = db_query($sql);

    $actualRecordCount = count($request->fetchAll());

    return db_query($sql);
}

/**
 * @return array List of reserved sql keywords that require escaping
 */
function getMsSqlReservedKeywords()
{
    return [
        'LEFT',
        'RIGHT',
    ];
}

/**
 * @desc Generates an update-style SQL string (key=parameter1, key2=parameter2...) from arrays of fields
 * and data for PDO connection method.
 * (Similar to old GenerateSQLFromArrays but for new PDO method of connection replacement and additionally returns
 * in receive paramater array $PDOParamsArray the key/value pairs for each variable used in the SQL statement)
 *
 * @param array $aParams array of parameters
 * @param array $pdoParamsArray recieve array parameter for key/value pairs
 *
 * @return string string containing key/value pairs formated for a SQL update statement
 */
function GeneratePDOSQLFromArrays($aParams, &$pdoParamsArray)
{
    global $FieldDefs;

    $module = $aParams['Module'];

    $reservedKeywords = getMsSqlReservedKeywords();

    $suffixString = '';
    if ($aParams['Suffix']) {
        $suffixString = '_' . $aParams['Suffix'];
    }

    $sqlArray = [];

    if (!$pdoParamsArray) {
        $pdoParamsArray = [];
    }

    if (is_array($aParams['FieldArray']) && is_array($aParams['DataArray'])) {
        foreach ($aParams['FieldArray'] as $fieldKey) {
            if (array_key_exists($fieldKey . $suffixString, $aParams['DataArray'])
                && !$FieldDefs[$module][$fieldKey]['Computed']) {
                $value = $aParams['DataArray'][$fieldKey . $suffixString];

                $type = $FieldDefs[$module][$fieldKey]['Type'];
                if ((in_array($type, [FieldInterface::NUMBER_DB, FieldInterface::DECIMAL_DB, FieldInterface::MONEY_DB], true)
                        && ($value === '' || $value === 'NULL')) // 0 is a valid value for number and money
                    || (($FieldDefs[$module][$fieldKey]['NullZeros']
                            || $type === 'date') && ($value === '' || $value === 'NULL'))) {
                    $value = null;
                }

                $fieldKeyParam = $fieldKey;

                $pdoParamsArray[$fieldKeyParam] = $value;

                if (in_array($fieldKey, $reservedKeywords, true)) {
                    $fieldKey = '[' . $fieldKey . ']';
                }

                $sqlArray[] = $fieldKey . ' = :' . $fieldKeyParam;
            }
        }
    }

    if (!empty($sqlArray)) {
        if ($aParams['end_comma']) {
            return implode(', ', $sqlArray) . ', ';
        }

        return implode(', ', $sqlArray);
    }
}

/**
 * @desc Generates an insert-style SQL string (key, key2, ...) VALUES (value, value2, ...) from arrays of fields
 * and data for PDO connection method.
 * (Similar to old {@link GenerateSQLFromArrays()} but for new PDO method of connection replacement and additionally
 * returns in receive paramater array $PDOParamsArray the key/value pairs for each variable used in the SQL statement).
 *
 * @param array $aParams array of parameters
 *
 * @return string string containing key/value pairs formated for a SQL insert statement
 */
function GeneratePDOInsertSQLFromArrays($aParams, &$PDOParamsArray)
{
    global $FieldDefs;

    if (is_array($aParams['FieldArray']) && !empty($aParams['FieldArray']) && is_array($aParams['DataArray'])
        && !empty($aParams['DataArray'])) {
        $Module = $aParams['Module'];

        $reservedKeywords = getMsSqlReservedKeywords();

        if ($aParams['Suffix']) {
            $SuffixString = '_' . $aParams['Suffix'];
        }
        $fieldArray = [];
        $valArray = [];

        foreach ($aParams['FieldArray'] as $FieldKey) {
            if (array_key_exists($FieldKey . $SuffixString, $aParams['DataArray']) && !$FieldDefs[$Module][$FieldKey]['CalculatedField']) {
                IncludeFieldDefs();

                $Value = $aParams['DataArray'][$FieldKey . $SuffixString];

                if (($FieldDefs[$Module][$FieldKey]['Type'] == 'number'
                        || $FieldDefs[$Module][$FieldKey]['Type'] == 'money'
                        || $FieldDefs[$Module][$FieldKey]['Type'] == 'date')
                    && ($Value === '' || $Value === 'NULL')) {
                    $Value = null;
                }

                // Escape sql reserved keywords
                $fieldArray[] = (in_array($FieldKey, $reservedKeywords) ? '[' . $FieldKey . ']' : $FieldKey);
                $valArray[] = ':' . $FieldKey;
                $PDOParamsArray[$FieldKey] = $Value;
            }
        }

        if (is_array($aParams['Extras'])) {
            foreach ($aParams['Extras'] as $field => $val) {
                $fieldArray[] = $field;
                $valArray[] = ':' . $field;
                $PDOParamsArray[$field] = $val;
            }
        }

        $sql = '(' . implode(', ', $fieldArray) . ') VALUES (' . implode(', ', $valArray) . ')';
    }

    return $sql;
}

/**
 * @desc Used when emailing via AJAX - returns a data array for a particular record.
 *
 * @param array $aParams Array of parameters
 *
 * @return array data array for the record in question
 */
function GetRecordData($aParams)
{
    global $ModuleDefs, $FieldDefs;

    $Fields = $ModuleDefs[$aParams['module']]['FIELD_ARRAY'];

    $table = $ModuleDefs[$aParams['module']]['TABLE'];
    if (empty($table)) {
        throw new Exception('No TABLE defined for module "' . $aParams['module'] . '"');
    }

    $Fields[] = 'recordid';

    $sql = '
        SELECT '
        . implode(',', $Fields) . '
        FROM '
        . $table . '
        WHERE
            recordid = :recordid
    ';

    return DatixDBQuery::PDO_fetch($sql, ['recordid' => $aParams['recordid']]);
}

/**
 * @desc Called when a record cannot be found in the database - checks whether it exists or not and
 * then treats it as a security issue or a non-existant record as appropriate.
 *
 * @param array $Parameters Array of parameters
 *
 * @return never
 */
function CheckRecordNotFound($Parameters): void
{
    $moduleDefs = Container::get(ModuleDefs::class);

    // need to check whether record exists at all.
    $sql = '
        SELECT
            1 as records_exist
        WHERE (EXISTS(
            SELECT
                recordid
            FROM
                ' . $moduleDefs[$Parameters['module']]->getDbReadObj() . '
            WHERE
                recordid = ' . $Parameters['recordid'] . '
        ))
    ';

    $row = db_fetch_array(db_query($sql));

    $RecordExists = ($row['records_exist'] == '1');

    if ($RecordExists) {
        $Message = _fdtk('no_perms_view');

        AuditOpenRecord($Parameters['module'], $Parameters['recordid'], 'Access denied by security WHERE clause');
    } else {
        // record has been deleted or never existed.
        $Message = _fdtk('not_found');
    }

    if ($dtxdebug) {
        $Message .= "\n{$sql}";
    }

    fatal_error($Message, 'Information', $Parameters['module']);
}

function GetContactListSQLByAccessLevel($aParams)
{
    global $ModuleDefs;

    $aWhere = $aParams['where'];
    $aLevels = $aParams['levels'];
    $aNotLevels = $aParams['notlevels'];
    $table = $aParams['table'] ?: 'con';
    $Module = $aParams['module'];

    $AccessLevelWhere = '';

    if ($aLevels) {
        $AccessLevelWhere .= " AND sec_group_permissions.perm_value in ('" . implode("', '", $aLevels) . "') ";
    }

    if ($aNotLevels) {
        $AccessLevelWhere .= " AND
            sec_group_permissions.perm_value not in ('" . implode("', '", $aNotLevels) . "')
            AND sec_group_permissions.perm_value != ''";
    }

    $UserParmsWhere = '';

    if ($aLevels) {
        $UserParmsWhere .= " AND user_parms.parmvalue in ('" . implode("', '", $aLevels) . "') ";
    }

    if ($aNotLevels) {
        $UserParmsWhere .= " AND
            user_parms.parmvalue not in ('" . implode("', '", $aNotLevels) . "')
            AND user_parms.parmvalue != ''";
    }

    $NewWhere = "
    {$table}.recordid in ((SELECT sec_staff_group.use_id
                            FROM sec_group_permissions
                            JOIN sec_staff_group ON sec_group_permissions.grp_id = sec_staff_group.grp_id
                            WHERE sec_group_permissions.item_code = '" . $ModuleDefs[$Module]['PERM_GLOBAL'] . "'"
        . $AccessLevelWhere .
        " )
                            UNION
                            (
                            SELECT users_main.recordid FROM users_main, profiles, link_profile_group
                                WHERE
                                users_main.sta_profile = profiles.recordid
                                AND profiles.recordid = lpg_profile AND lpg_group IN
                                    (SELECT grp_id FROM sec_group_permissions
                                    WHERE
                                    item_code = '" . $ModuleDefs[$Module]['PERM_GLOBAL'] . "'" . $AccessLevelWhere . ")
                            ))
            OR
            (
                (SELECT count(*)
                    FROM sec_staff_group l
                    JOIN sec_groups g on l.grp_id = g.recordid
                    WHERE l.use_id = {$table}.recordid AND g.grp_type = (g.grp_type | 0)
                ) = 0
                AND
                {$table}.login in (SELECT user_parms.login
                                FROM user_parms
                                WHERE user_parms.parameter = '" . $ModuleDefs[$Module]['PERM_GLOBAL'] . "'"
        . $UserParmsWhere .
        ' )
            )';

    if ($aParams['return_where']) {
        return $NewWhere;
    }

    $aWhere[] = $NewWhere;

    $sql = 'SELECT recordid, initials, use_jobtitle, use_forenames,
        use_surname, use_title
        FROM users_main con
        WHERE (' . implode(') AND (', $aWhere) . ')' . GetUserListOrderBy();

    return $sql;
}

function GetUserListOrderBy()
{
    switch (GetParm('STAFF_NAME_DROPDOWN', 'N')) {
        case 'N':
            if (bYN(GetParm('CONTACT_SURNAME_SORT', 'Y'))) {
                $OrderBy = ' ORDER BY use_surname, use_forenames';
            } else {
                $OrderBy = ' ORDER BY use_title, use_surname, use_forenames';
            }

            break;
        case 'A':
            if (bYN(GetParm('CONTACT_SURNAME_SORT', 'Y'))) {
                $OrderBy = ' ORDER BY use_surname, use_forenames, use_jobtitle';
            } else {
                $OrderBy = ' ORDER BY use_title, use_forenames, use_surname, use_jobtitle';
            }

            break;
        case 'B':
            if (bYN(GetParm('CONTACT_SURNAME_SORT', 'Y'))) {
                $OrderBy = ' ORDER BY use_jobtitle, use_surname, use_forenames';
            } else {
                $OrderBy = ' ORDER BY use_jobtitle, use_title, use_forenames, use_surname';
            }

            break;
    }

    return $OrderBy;
}

/**
 * Saves all linked records associated with a main record.
 *
 * Iterates through each instance on the form of this linked record type
 * and calls {@link SaveLinkedRecord()} to save a record for each one.
 */
function SaveLinkedRecords($aParams)
{
    $SaveData = true;

    // The 'save_controller' is set a modules AppVars.php definition, and represents the field that
    // says whether the module has associated records in the $aParams['table'].
    // If the save controller's value is evaluated as false then the linked records are automatically deleted.
    if ($aParams['save_controller'] && isset($_POST[$aParams['save_controller']])
        && !bYN($_POST[$aParams['save_controller']])) {
        $SaveData = false;
    }

    if ($SaveData) {
        $MaxSuffix = Sanitize::SanitizeString($_POST[$aParams['type'] . '_max_suffix']);

        for ($i = 0; $i <= $MaxSuffix; ++$i) {
            $aParams['suffix'] = $i;
            // Check for existing linked recordid in order to perform update rather than insert.
            $aParams['recordid'] = Sanitize::SanitizeInt($_POST[$aParams['type'] . '_link_id_' . $aParams['suffix']]);

            if (!RecordNotFilledIn($aParams)) {
                if ($aParams['useIdentity']) {
                    $RecordIdsNoDelete[] = saveLinkedRecordIdentity($aParams);
                } else {
                    $RecordIdsNoDelete[] = SaveLinkedRecord($aParams);
                }
            }
        }
    }

    // Delete records which have been removed and make sure not to delete the ones that have been saved.
    $sql = 'DELETE FROM ' . $aParams['table'] . ' WHERE ' . $aParams['main_recordid_label'] . ' = :main_recordid';

    if (is_array($RecordIdsNoDelete)) {
        $ListDeleteExclude = implode(',', $RecordIdsNoDelete);
        $sql .= ' AND recordid NOT IN (' . $ListDeleteExclude . ')';
    }

    DatixDBQuery::PDO_query($sql, ['main_recordid' => $aParams['main_recordid']]);
}

/**
 * Saves an individual linked record attached to a main record.
 *
 * Called by {@link SaveLinkedRecords()}.
 *
 * @global array $FieldDefs
 */
function SaveLinkedRecord($aParams)
{
    global $FieldDefs;

    $data = ParseSaveData([
        'module' => $aParams['module'],
        'data' => $_POST,
        'suffix' => '_' . $aParams['suffix'],
    ]);

    // we need to use the "real table" if $aParams['table'] is a view,
    // so the correct table name is inserted into the controls table
    $table = array_key_exists('real_table', $aParams) ? $aParams['real_table'] : $aParams['table'];

    if (empty($aParams['recordid'])) {
        $recordIdGenerator = (new RecordIdGeneratorFactory())->create($table);
        $aParams['recordid'] = $recordIdGenerator->generateRecordId();

        $insertArray = [
            $aParams['recordid_field'] => $aParams['recordid'],
            $aParams['main_recordid_label'] => $aParams['main_recordid'],
        ];
        InsertUpdateData(Query::INSERT, $aParams['table'], [], $insertArray);
    }

    if ($aParams['save_listorder'] && isset($data[$aParams['type'] . '_listorder_' . $aParams['suffix']])) {
        $Listorder = (int) $data[$aParams['type'] . '_listorder_' . $aParams['suffix']];
        $UpdateArray['listorder'] = ($Listorder == 0 ? null : $Listorder);
    }
    $FormDesign = Forms_FormDesign::GetFormDesign(['module' => $aParams['module'], 'level' => $data['formlevel'], 'parent_module' => null]);
    foreach ($aParams['basic_form']['Rows'] as $field) {
        if (!$FieldDefs[$aParams['module']][$field]['DummyField']
            && ($data['show_field_' . $field . '_' . $aParams['suffix']] == '1'
                || ($FormDesign->HideFields[$field] && $FormDesign->DefaultValues[$field]))) {
            if (empty($data[$field . '_' . $aParams['suffix']])) {
                $UpdateArray[$field] = null;
            } else {
                $UpdateArray[$field] = $data[$field . '_' . $aParams['suffix']];
            }
        }
    }

    if ($aParams['main_recordid']) {
        $UpdateArray[$aParams['main_recordid_label']] = $aParams['main_recordid'];
    }

    if (!empty($UpdateArray)) {
        $where = [$aParams['recordid_field'] => $aParams['recordid']];
        InsertUpdateData(Query::UPDATE, $aParams['table'], $where, $UpdateArray);
    }

    if (is_array($aParams['RunAfterSaveIncludes'])) {
        foreach ($aParams['RunAfterSaveIncludes'] as $includes) {
            require_once $includes;
        }
    }

    if (is_array($aParams['RunAfterSave'])) {
        foreach ($aParams['RunAfterSave'] as $function) {
            $function($aParams);
        }
    }

    return $aParams['recordid'];
}

/**
 * Saves a record to a link table which uses an identity column for the recordid.
 *
 * Similar to {@link SaveLinkedRecords()}, but without the need to create the recordid manually.
 *
 * @global array $FieldDefs
 */
function saveLinkedRecordIdentity($aParams)
{
    global $FieldDefs;

    // construct values array to hold the data being inserted into the table
    $values = [];
    $data = ParseSaveData([
        'module' => $aParams['module'],
        'data' => $_POST,
        'suffix' => '_' . $aParams['suffix'],
    ]);

    if ($aParams['save_listorder'] && isset($data[$aParams['type'] . '_listorder_' . $aParams['suffix']])) {
        $values['listorder'] = $data[$aParams['type'] . '_listorder_' . $aParams['suffix']];
    }
    $FormDesign = Forms_FormDesign::GetFormDesign(['module' => $aParams['module'], 'level' => $data['formlevel'], 'parent_module' => null]);
    foreach ($aParams['basic_form']['Rows'] as $field) {
        if (!$FieldDefs[$aParams['module']][$field]['DummyField']
            && ($data['show_field_' . $field . '_' . $aParams['suffix']] == '1'
                || ($FormDesign->HideFields[$field] && $FormDesign->DefaultValues[$field]))) {
            if ($FieldDefs[$aParams['module']][$field]['Type'] == 'multilistbox'
                && is_array($data[$field . '_' . $aParams['suffix']])) {
                $values[$field] = implode(' ', $data[$field . '_' . $aParams['suffix']]);
            } else {
                $values[$field] = $data[$field . '_' . $aParams['suffix']];
            }
        }
    }

    if ($aParams['main_recordid']) {
        $values[$aParams['main_recordid_label']] = $aParams['main_recordid'];
    }

    if (empty($aParams['recordid'])) {
        // insert a new record
        $sql = 'INSERT INTO ' . $aParams['table'] . ' ';
        $sql .= GeneratePDOInsertSQLFromArrays(
            [
                'Module' => $aParams['module'],
                'FieldArray' => array_keys($values),
                'DataArray' => $values,
            ],
            $PDOParamsArray,
        );
        $aParams['recordid'] = DatixDBQuery::PDO_insert($sql, $PDOParamsArray);
    } else {
        // update an existing record
        $sql = 'UPDATE ' . $aParams['table'] . ' SET ';
        $sql .= GeneratePDOSQLFromArrays(
            [
                'Module' => $aParams['module'],
                'FieldArray' => array_keys($values),
                'DataArray' => $values,
            ],
            $PDOParamsArray,
        );
        $sql .= ' WHERE ' . $aParams['recordid_field'] . ' = :' . $aParams['recordid_field'];
        $PDOParamsArray[$aParams['recordid_field']] = $aParams['recordid'];
        DatixDBQuery::PDO_query($sql, $PDOParamsArray);
    }

    if (is_array($aParams['RunAfterSaveIncludes'])) {
        foreach ($aParams['RunAfterSaveIncludes'] as $includes) {
            require_once $includes;
        }
    }

    if (is_array($aParams['RunAfterSave'])) {
        foreach ($aParams['RunAfterSave'] as $function) {
            $function($aParams);
        }
    }

    return $aParams['recordid'];
}

/**
 * Determines whether any of a given set of form fields have been filled in (i.e. are not empty).
 *
 * Used when saving linked records ({@link SaveLinkedRecords()}),
 * checks to see if the submitted form holds any data for the linked record.
 *
 * @return bool whether or not any of the linked record fields have been filled in
 */
function RecordNotFilledIn($aParams)
{
    global $FieldDefs;

    $Data = $_POST;
    $Suffix = $aParams['suffix'];
    $Rows = $aParams['basic_form']['Rows'];

    foreach ($Rows as $Row) {
        if ($Data[$Row . '_' . $Suffix] != '') {
            return false;
        }

        // If this is a text field, need to check there isn't any history data either/
        if ($FieldDefs[$aParams['module']][CheckFieldMappings($aParams['module'], $Row, true)]['Type'] == 'textarea') {
            if ($Data['CURRENT_' . $Row . '_' . $Suffix] != '') {
                return false;
            }
        }
    }

    return true;
}

/**
 * Inserts or Update data in the specified Database table.
 *
 * @param int $type Type of Query (Insert or Update)
 * @param string $table Table name
 * @param array $where
 * @param array $fields
 *
 * @throws \src\framework\query\exceptions\IncompleteQueryException
 */
function InsertUpdateData($type, $table, $where, $fields)
{
    $dbQuery = new DatixDBQuery();
    $dbQuery->beginTransaction();

    $queryFactory = new QueryFactory();
    $query = $queryFactory->getQuery();
    switch ($type) {
        case Query::INSERT: $query->insert($fields)->into($table);

            break;
        case Query::UPDATE: $query->update($table);
            $query->set($fields);
            $query->where($where);

            break;
    }

    $writer = Container::get(SqlWriter::class);
    [$querySql, $parameters] = $writer->writeStatement($query);

    $dbQuery->setSQL($querySql);
    $dbQuery->prepareAndExecute($parameters);
    $dbQuery->commit();
}
