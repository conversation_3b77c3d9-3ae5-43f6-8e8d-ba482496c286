<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\contact\ContactTypes;
use app\models\contact\services\ContactService;
use app\models\framework\config\DatixConfig;
use app\models\framework\flashMessages\FlashMessageContainer;
use app\models\framework\flashMessages\FlashMessageContainerFactory;
use app\models\framework\flashMessages\InfoMessage;
use app\models\generic\valueObjects\Module;
use app\services\forms\FormService;
use app\services\psims\PsimsTriggerService;
use app\services\records\RecordsFactory;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Ramsey\Uuid\Uuid;
use Source\classes\SuffixHelper;
use src\component\field\CustomFieldFactory;
use src\component\field\InputFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\contacts\helpers\ContactsDataHelper;
use src\documents\services\DragAndDropService;
use src\documents\services\DragAndDropSvgService;
use src\dynamicsectiondatacollection\model\DynamicSectionDataCollection;
use src\equipment\controllers\EquipmentController;
use src\formdesign\forms\factories\FormDesignFactory;
use src\formdesign\forms\FormDesignGlobals;
use src\formdesign\forms\service\Loaders\FormDesignInstanceLoader;
use src\formdesign\model\FormDesign;
use src\formdesign\model\FormDesignModelFactory;
use src\formdesign\RightHandLinks;
use src\framework\controller\Loader;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\medications\controllers\MedicationController;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\system\moduledefs\ModuleDefs;

/**
 * @desc Gets the current version number from the db and returns it (also stores it in a global).
 * Used to ensure that we don't accidentally change versions while maintaining our session and for comparing
 * versions when migrating.
 *
 * @return string The version number as <mainversion>.<patchlevel>
 */
function GetVersion(): string
{
    return '16.1.199';
}

function checkGeneralMigrationNeeded(): bool
{
    if (skipMigration()) {
        return false;
    }

    if (CompareVersions(GetVersion(), GetParm('FORM_MIGRATE_VERSION'))) {
        // the current version is later than the last version migrated.
        return true;
    }

    return false;
}

/**
 * Defines specific revisions which should not run the form migrate.
 */
function skipMigration(): bool
{
    $skip = [
        ['from' => '12.1', 'to' => '12.1.1'],
    ];

    foreach ($skip as $revision) {
        if ($revision['from'] == GetParm('FORM_MIGRATE_VERSION') && $revision['to'] == GetVersion()) {
            return true;
        }
    }

    return false;
}

// compares two version numbers of the form x.x.x.x...
// if versionA is a later version than versionB, returns true, otherwise false
function CompareVersions(string $VersionA, string $VersionB): bool
{
    $VersionAArray = explode('.', $VersionA);
    $VersionBArray = explode('.', $VersionB);

    while (count($VersionAArray) > count($VersionBArray)) {
        $VersionBArray[] = '0';
    }
    while (count($VersionBArray) > count($VersionAArray)) {
        $VersionAArray[] = '0';
    }

    foreach ($VersionAArray as $index => $number) {
        if ((int) $number > (int) $VersionBArray[$index]) {
            return true;
        }

        if ((int) $number < (int) $VersionBArray[$index]) {
            return false;
        }
    }

    return false;
}

/**
 * Removes the last bit of an UDF formatted field name, if numeric (ie: "UDF_3_5" returns "UDF_3").
 *
 * @param string $FieldName
 *
 * @return string
 */
function RemoveSuffix($FieldName)
{
    return preg_replace('/_\d+$/', '', $FieldName);
}

function AddSuffixToData($Data, $Suffix, $Rows)
{
    $NewData = $Data;

    foreach ($Rows['Rows'] as $Field) {
        $NewData[$Field . '_' . $Suffix] = $Data[$Field];
        unset($NewData[$Field]);
    }

    return $NewData;
}

/**
 * @desc Returns the suffix text for a rejected record to identify who rekected it.
 *
 * @param array $data Current record data
 * @param string $module the current module
 * @param string $HandlerField the field to use to match the staff code of the contact who rejected the record
 * @param int $ReasonName The record id of the contact who rejected the record
 *
 * @return string text to display after the form name for a rejected record
 */
function GetRejectedSuffix($data, $module, $HandlerField, $ReasonName): string
{
    if (empty($data['rep_approved']) || $data['rep_approved'] !== 'REJECT') {
        return '';
    }

    if (bYN(GetParm('REJECT_REASON', 'Y')) && $ReasonName) {
        return ' (' . _fdtk('rejected_by') . ' ' . $ReasonName . ')';
    }
    if (!empty($data['updatedby'])) {
        return ' (' . _fdtk('rejected_by') . ' ' . code_descr($module, $HandlerField, $data['updatedby'], '', false) . ')';
    }

    return ' (rejected)';
}

/**
 * @desc Runs through all the form design globals and unsets them. Used when switching between form designs
 * such as when we have contact form designs embedded in incidents ones.
 */
function unsetFormDesignSettings()
{
    foreach (FormDesignGlobals::FORM_DESIGN_GLOBALS as $globalName) {
        unset($GLOBALS[$globalName]);
    }
}

/**
 * @desc Puts all form design globals in an array and returns it to be stored for late use.
 * Used when switching between form designs such as when we have contact form designs embedded in incidents ones.
 *
 * @return array array of form design global names and their values
 */
function saveFormDesignSettings()
{
    $settings = [];
    foreach (FormDesignGlobals::FORM_DESIGN_GLOBALS as $globalName) {
        if (isset($GLOBALS[$globalName])) {
            $settings[$globalName] = $GLOBALS[$globalName];
        }
    }

    return $settings;
}

/**
 * @desc Loads new form design global values from a passed array.
 *
 * @param array $newGlobals array of global names and values to be loaded into the session
 */
function loadFormDesignSettings($newGlobals)
{
    foreach ($newGlobals as $key => $value) {
        $GLOBALS[$key] = $value;
    }
}

function MakeDynamicContactSection($aParams)
{
    global $MaxSuffixField, $ModuleDefs, $MaxSuffix;

    $data = $aParams['data']; // need to check whether there are multiple sections to put into the form.
    $module = $aParams['module'];
    $contactsToShow = []; // initialise variables!

    if ($data['getContactData']) {
        $recordId = (int) ($data['recordid'] ?? null);
        $contactType = $aParams['contacttype'] ?? null;

        if (
            $recordId !== null
            && $module !== null
            && $contactType !== null
        ) {
            $data = addContactData($data, $recordId, $module, $contactType);
        }
    }

    if (!empty($data['con']) && !$data['temp_record']) {
        $numToShow = count($data['con'][$aParams['contacttype']] ?? []);

        if (!$MaxSuffixField) {
            echo '<input type="hidden" name="contact_max_suffix" id="contact_max_suffix" value="' . (Container::get(ContactsDataHelper::class)->getTotalNumContacts($data) + SuffixHelper::DEFAULT_CONTACT_SUFFIX) . '" />';
        }

        if (!$numToShow) {
            if ($aParams['FormType'] !== 'Print' && $aParams['FormType'] !== 'ReadOnly') {
                $numToShow = 1;
            }
        }
    } else {
        if ($data['contact_max_suffix']) {
            $contactsToShow = Container::get(ContactsDataHelper::class)->getContactSuffixesFromFormDataByType($data, $aParams['contacttype']);
            $aParams['suffixpresent'] = true; // prevent additional suffix being added.
        } elseif ($data['temp_record']) {
            $contactsToShow = $data['con'][$aParams['contacttype']];
        }

        $numToShow = isset($contactsToShow) && is_iterable($contactsToShow) ? count($contactsToShow) : 0;

        if ($numToShow === 0) {
            $contactsToShow[] = $aParams['suffix'];

            if ($aParams['FormType'] !== 'Print' && $aParams['FormType'] !== 'ReadOnly') {
                $numToShow = 1;
            }
        }

        if (!$MaxSuffixField) {
            $suffixesFromData = array_map(
                'intval',
                Container::get(ContactsDataHelper::class)->getContactSuffixesFromFormData($data),
            );

            $contactMaxSuffixFromData = !empty($suffixesFromData) ? max($suffixesFromData) : $data['contact_max_suffix'] ?? null;

            $contactMaxSuffix = Sanitize::IntOrDefault($contactMaxSuffixFromData, SuffixHelper::DEFAULT_CONTACT_SUFFIX);

            $contactMaxSuffix = max($contactMaxSuffix, SuffixHelper::DEFAULT_CONTACT_SUFFIX);

            echo '<input type="hidden" name="contact_max_suffix" id="contact_max_suffix" value="' . $contactMaxSuffix . '" />';
        }
    }

    if ($MaxSuffix) {
        $MaxSuffix += $numToShow;
    } else {
        $suffixesFromData = array_map(
            'intval',
            Container::get(ContactsDataHelper::class)->getContactSuffixesFromFormData($data),
        );

        $contactMaxSuffixFromData = !empty($suffixesFromData) ? max($suffixesFromData) : $data['contact_max_suffix'] ?? null;

        $contactMaxSuffix = Sanitize::IntOrDefault($contactMaxSuffixFromData, SuffixHelper::DEFAULT_CONTACT_SUFFIX);

        $contactMaxSuffix = max($contactMaxSuffix, SuffixHelper::DEFAULT_CONTACT_SUFFIX);

        $MaxSuffix = Sanitize::IntOrDefault($contactMaxSuffix, SuffixHelper::DEFAULT_CONTACT_SUFFIX);
    }

    for ($i = 0; $i < $numToShow; ++$i) {
        if (($_SESSION['logged_in'] && !isset($data['error']) && !isset($data['dif1_snapshot'])) || $data['temp_record']) {
            $contactData = $data['con'][$aParams['contacttype']][$i];

            $aParams['data'] = $contactData ? Sanitize::SanitizeStringArray($contactData) : [];

            if ($i != 0 || !$aParams['suffix']) { // first contact should take the default suffix
                $aParams['suffix'] = $i + $MaxSuffix;
            }
        } else {
            if (!empty($contactsToShow[$i])) {
                $aParams['suffix'] = $contactsToShow[$i];
            }
        }

        if ($i == 0) {
            $aParams['clear'] = true;
        }

        echo '

        <div id="contact_section_div_' . Sanitize::SanitizeString($aParams['suffix']) . '">';

        get_contact_section($aParams);

        echo '</div>';
    }

    if (
        $aParams['FormType'] !== 'ReadOnly' && $aParams['FormType'] !== 'Print'
        && ($numToShow < $ModuleDefs[$module]['LEVEL1_CON_OPTIONS'][$aParams['contacttype']]['Max']
            || $ModuleDefs[$module]['LEVEL1_CON_OPTIONS'][$aParams['contacttype']]['Max'] == 0)
    ) {
        $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] === 'Y' ? 'true' : 'false';
        echo '
            <li class="new_windowbg" id="add_another_' . Sanitize::SanitizeString($aParams['contacttype']) . '_button_list">
                <button type="button" id="contact_section_' . Sanitize::SanitizeString($aParams['contacttype']) . '" class="dtx-button dtx-button-small button-secondary button-margin-bottom" onclick="AddSectionToForm(\'contact\', \'' . Sanitize::SanitizeString($aParams['contacttype']) . '\', jQuery(\'#add_another_' . Sanitize::SanitizeString($aParams['contacttype']) . '_button_list\').prev(), \'' . $module . '\', \'\', true, ' . $spellChecker . ', ' . (Sanitize::SanitizeInt($aParams['form_id']) ?: 'null') . ');">
                    <span>' . _fdtk('button_add_another') . '</span>
                </button>
            </li>';
    }

    echo "<script>dif1_section_suffix['contact'] = {$MaxSuffix} </script>";
}

function addContactData(array $data, int $recordId, string $module, string $contactType): array
{
    $dynamicSectionDataCollection = Container::get(DynamicSectionDataCollection::class);

    $data['con'] = $dynamicSectionDataCollection->getDynamicSectionData($recordId, $module, $contactType);

    return $data;
}

/**
 * @throws NotFoundExceptionInterface
 * @throws ContainerExceptionInterface
 * @throws DOMException
 */
function MakeDynamicDocumentSection($aParams)
{
    $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';

    $dragAndDropService = Container::get(DragAndDropService::class);

    if ($dragAndDropService->isDragAndDropAllowed()) {
        $dragAndDropTextPrefix = htmlspecialchars(_fdtk('drag_and_drop_or_browse_files_prefix'), ENT_QUOTES);
        $dragAndDropTextOr = htmlspecialchars(_fdtk('drag_and_drop_or_browse_files_or'), ENT_QUOTES);
        $dragAndDropTextButton = htmlspecialchars(_fdtk('drag_and_drop_or_browse_files_button'), ENT_QUOTES);
        $url = getenv('BASE_URL');
        $urlUpload = htmlspecialchars($url . '?action=savedocumentmultipartchunk', ENT_QUOTES);
        $urlChunkingBegin = htmlspecialchars($url . '?action=savedocumentmultipartbegin', ENT_QUOTES);
        $urlChunkingEnded = htmlspecialchars($url . '?action=savedocumentmultipartend', ENT_QUOTES);
        $dciqModule = htmlspecialchars($aParams['module'], ENT_QUOTES);
        $dciqSpellchecker = htmlspecialchars($spellChecker, ENT_QUOTES);
        $uuidForTrackingInstance = Uuid::uuid4();
        $uuidForTrackingInstanceEscaped = htmlspecialchars($uuidForTrackingInstance->toString(), ENT_QUOTES);
        echo '<li>';
        echo <<<DROPZONE
            <div
                class="form-control"
                data-drag-and-drop-instance="{$uuidForTrackingInstanceEscaped}"
                data-dciq-module="{$dciqModule}"
                data-dciq-spellchecker="{$dciqSpellchecker}"
                data-dropzone-url-upload="{$urlUpload}"
                data-dropzone-url-chunking-begin="{$urlChunkingBegin}"
                data-dropzone-url-chunking-ended="{$urlChunkingEnded}"
            >
                <div class="drag-and-drop-files--message dz-message">
                    <p>{$dragAndDropTextPrefix}<br />{$dragAndDropTextOr}<br /><button type="button" class="dtx-button button-clear">{$dragAndDropTextButton}</button></p>
                </div>
            </div>
            DROPZONE;
        $dragAndDropSvgService = Container::get(DragAndDropSvgService::class);
        echo $dragAndDropSvgService->loadIcons()->saveXML();
        echo $dragAndDropService->generateDropzoneObject($uuidForTrackingInstance, $aParams['data']);
        echo '</li>';
    } else {
        $Data = $aParams['data']; // need to check whether there are multiple sections to put into the form.

        if ($Data['document_max_suffix']) {
            $NumToShow = $Data['document_max_suffix'];
        }

        if (!$NumToShow) {
            $NumToShow = 1;
        }

        echo '<input type="hidden" name="max_doc_suffix" id="max_doc_suffix" value="' . $NumToShow . '" />';
        for ($i = 1; $i <= $NumToShow; ++$i) {
            if (($i == 1) || ($Data['doc_type_' . $i] && $Data['doc_notes_' . $i])) {
                $aParams['suffix'] = $i;

                if ($i == 1) {
                    $aParams['clear'] = true;
                }

                echo '<div id="document_section_div_' . $aParams['suffix'] . '">';


                get_document_section($aParams);
                echo '</div>';
            }
        }

        echo '
        <li class="new_windowbg" id="add_another_document_button_list">
            <script language="javascript">dif1_section_suffix[\'document\'] = ' . (($Data['document_max_suffix']) ? Sanitize::SanitizeString($Data['document_max_suffix']) : 2) . '</script>
            <input type="button" id="documents" value="' . _fdtk('button_add_another') . '" onclick="AddSectionToForm(\'document\', \'\', jQuery(\'#add_another_document_button_list\').prev(), \'' . $aParams['module'] . '\', \'\', true, ' . $spellChecker . ', null);">
        </li>';
    }
}

function get_contact_section($aParams = []): void
{
    global $FormDesigns, $ModuleDefs, $JSFunctions, $FieldDefs;

    $registry = Container::get(Registry::class);

    $FormType = $aParams['FormType'];
    $data = $aParams['data'];
    $AJAX = (empty($aParams));

    // Persist ajax calls to later decide if we need to show the search button
    if ($aParams['ajax'] === true) {
        $data['ajax'] = true;
    }

    if ($AJAX) {
        $aParams = $_POST;
    }

    $Module = Sanitize::SanitizeString($aParams['module']);
    $Type = Sanitize::SanitizeString($aParams['contacttype']);
    $Suffix = Sanitize::SanitizeString($aParams['suffix']);
    $clear = Sanitize::SanitizeString($aParams['clear']);

    if ($Suffix && !$aParams['suffixpresent'] && !empty($data)) {
        $Rows = getContactLinkFields($Type);

        $OtherRows = [
            'recordid', 'link_recordid', 'link_exists', 'con_id', 'updateid', 'show_injury',
            'lcom_iscomplpat', 'lcom_primary', 'ipp_damage_type', 'ipp_description', 'ipp_value',
        ];

        $Rows = array_merge($Rows, $OtherRows, $ModuleDefs['CON']['FIELD_ARRAY']);

        $data = AddSuffixToData($data, $Suffix, ['Rows' => $Rows]);
    }

    $OptionArray = $ModuleDefs[$Module]['LEVEL1_CON_OPTIONS'];

    $Attributes = $OptionArray[$Type];

    $SectionName = 'contact_' . $Suffix;

    $contactFormDesign = Container::get(FormDesignFactory::class)->create([
        'id' => $aParams['form_id'] ?? null,
        'module' => Module::CONTACTS,
        'level' => Sanitize::SanitizeString($aParams['level']),
        'parent_level' => Sanitize::SanitizeString($aParams['level']),
        'parent_module' => $Module,
        'link_type' => $Type,
    ]);

    /*
     * Make this section read only except for linked data if we are on incidents, the type of the contact is reporter
     * and the global "Reporter is linked as a contact" is true.
     */
    $isLoggedIn = (new UserSessionFactory())->create()->isLoggedIn();
    $isReporterSection = in_array($Module, ['INC', 'COM', 'MOR', 'CLA'], true) && $Type === ContactTypes::REPORTER;
    if ($isReporterSection && $isLoggedIn) {
        if (($_POST['clearsection'] ?? '') !== '1') {
            $aParams['FormType'] = FormTable::MODE_READONLY_EXPECT_LINKED_DATA;

            $linkedFields = getContactLinkFields();

            if (is_array($linkedFields)) {
                foreach ($contactFormDesign->MandatoryFields as $mandatoryField => $section) {
                    $fieldHasData = $data[$mandatoryField . '_' . $Suffix] !== null;

                    if (
                        !in_array($mandatoryField, $linkedFields, true)
                        && $fieldHasData
                    ) {
                        unset($contactFormDesign->MandatoryFields[$mandatoryField]);
                    }
                }
            }
        }
    }

    $contactFormDesign->AddSuffixToFormDesign($Suffix, $ModuleDefs[$Module]['LEVEL1_CON_OPTIONS'][$Type]['Title']);

    $contactTable = FormTableFactory::create(Sanitize::SanitizeString($aParams['FormType']), 'CON', $contactFormDesign);

    $Title = '<div>' . $Attributes['Title'] . '</div>';

    if (
        $aParams['FormType'] != 'ReadOnly'
        && $aParams['FormType'] != 'Print'
        // If the user is logged in and the global HIDE_REPORTER_CLEAR_BUTTON set to Y the Reporter section should not have a clear or delete button
        && !($isLoggedIn && $isReporterSection && $registry->getParm('HIDE_REPORTER_CLEAR_BUTTON')->isTrue())
    ) {
        if (!$clear) {
            $RightHandLink['onclick'] = 'jQuery(\'#contact_section_div_' . $Suffix . '\').remove()';
            $RightHandLink['text'] = _fdtk('delete_section');
        } else {
            if (is_numeric($_REQUEST['form_id'])) {
                $parentFormId = Sanitize::SanitizeInt($_REQUEST['form_id']);
            } elseif (is_numeric($_REQUEST['parent_form_id'])) {
                $parentFormId = Sanitize::SanitizeInt($_REQUEST['parent_form_id']);
            } else {
                $parentFormId = '';
            }

            $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';
            $RightHandLink['onclick'] = 'ReplaceSection(\'' . $Module . '\',' . $Suffix . ', \'contact\', \'' . $Type . '\', ' . $spellChecker . ', \'' . $parentFormId . '\')';
            $RightHandLink['text'] = _fdtk('button_clear_section');
        }
    }

    $GLOBALS['CON']['form_id'] = GetParm($Attributes['Global']);

    $Role = $Attributes['Role'];
    $Show = $Attributes['Show'];

    include 'Source/contacts/BasicForm_Simple.php';

    if ($RightHandLink) {
        // Decide which section the clear/delete link should be attached to
        $FormArray = (new RightHandLinks($contactFormDesign))->addLinkToFirstSection($FormArray, $RightHandLink, $Suffix);
    }

    // set link role
    if ($Role) {
        $FormArray[$SectionName]['LinkRole'] = $Role;
    }

    if (bYN(GetParm('CON_PAS_CHECK', 'N'))) {
        if (is_array($GLOBALS['ContactMatch'])) {
            // Add external contact search button to fields.
            $ConMatchFields = array_keys($contactFormDesign->ContactMatch);

            foreach ($ConMatchFields as $FieldName) {
                if ($contactFormDesign->ContactMatch[$FieldName]) {
                    $FieldName = \UnicodeString::substr($FieldName, 0, \UnicodeString::strlen($FieldName) - \UnicodeString::strlen('_' . $Suffix));
                    $contactTable->FieldDefs[$FieldName]['OriginalType'] = $FieldDefs['CON']["{$FieldName}"]['Type'];
                    $contactTable->FieldDefs["{$FieldName}"]['Type'] = 'string_con_search';
                }
            }
        }
    }

    if ($isReporterSection && $isLoggedIn && $_POST['clearsection'] == '1') {
        $FormArray[$SectionName]['Rows'] = (new ContactService())
            ->removeRowFromForm($FormArray[$SectionName]['Rows'], 'link_position');
    }

    foreach ($FormArray as $section => &$details) {
        // Define the containing section for each contact to ensure proper functioning of mandatory field checks
        // i.e. don't trigger mandatory field checks if the containing section is hidden
        $details['ContainedIn'] = $Attributes['DivName'];
    }

    $contactTable->makeForm($FormArray, $data, $Module, ['dynamic_section' => true]);

    $returnHTML = $contactTable->getFormTable();

    $returnHTML .= '<input type="hidden" id="rep_approved_' . $Suffix . '" name="rep_approved_' . $Suffix . '" value="' . Escaper::escapeForHTMLParameter($data['rep_approved_' . $Suffix]) . '" />';
    $returnHTML .= '<input type="hidden" id="link_recordid_' . $Suffix . '" name="link_recordid_' . $Suffix . '" value="' . Escaper::escapeForHTMLParameter($data['link_recordid_' . $Suffix]) . '" />';
    $returnHTML .= '<input type="hidden" id="con_id_' . $Suffix . '" name="con_id_' . $Suffix . '" value="' . Escaper::escapeForHTMLParameter($data['con_id_' . $Suffix]) . '" />';
    $returnHTML .= '<input type="hidden" id="updateid_' . $Suffix . '" name="updateid_' . $Suffix . '" value="' . Escaper::escapeForHTMLParameter($data['updateid_' . $Suffix]) . '" />';
    $returnHTML .= '<input type="hidden" id="con_clear_' . $Suffix . '" name="con_clear_' . $Suffix . '" value="' . $clear . '" />';

    if ($Attributes['DivName']) {
        $returnHTML .= '<input type="hidden" id="contact_div_name_' . $Suffix . '" name="contact_div_name_' . $Suffix . '" value="' . $Attributes['DivName'] . '" />';
    }

    if ($data['link_exists_' . $Suffix]) {
        $returnHTML .= '<input type="hidden" id="link_exists_' . $Suffix . '" name="link_exists_' . $Suffix . '" value="1" />';
    }

    echo $returnHTML;

    unset($GLOBALS['CON']['form_id']);
}

/**
 * @throws NotFoundExceptionInterface
 * @throws ContainerExceptionInterface
 * @throws \Doctrine\DBAL\Exception
 */
function get_document_section($aParams = [])
{
    $AJAX = (empty($aParams));

    if ($AJAX) {
        $aParams = $_POST;
    }

    $suffix = $aParams['suffix'];

    $dragAndDropService = Container::get(DragAndDropService::class);

    if ($dragAndDropService->isDragAndDropAllowed()) {
        $formObject = \app\services\document\DocumentFormFactory::create();

        $formObject->setDragAndDropEnabled(true);

        $defaults['setDefaultType'] = true;
        $defaults['typeName'] = 'doc_type_' . $suffix;
        $defaults['descriptionName'] = 'doc_notes_' . $suffix;
        $defaults['remoteFilenameName'] = 'doc_remote_filename_' . $suffix;
        $defaults['suffix'] = $suffix;
        $defaults['sectionTitle'] = _fdtk('new_document');

        if ($aParams['FormType'] != 'ReadOnly' && $aParams['FormType'] != 'Print') {
            $defaults['deleteButtonTitle'] = _fdtk('delete_section');
        }

        echo $formObject->generate($defaults);
    } else {
        global $JSFunctions;

        $data = $aParams['data'];
        $clear = $aParams['clear'];
        $module = $aParams['module'] ?? '';

        $documentTable = \src\component\form\FormTableFactory::create('', $module);

        $Title = '<div class="section_title_group">' . _fdtk('new_document') . '</div>';

        if ($aParams['FormType'] != 'ReadOnly' && $aParams['FormType'] != 'Print') {
            if (!$clear) {
                $Title .= '<div class="title_rhs_container"><button type="button" id="delete_section_' . $suffix . '" class="prince-button button-inline red-gradient"
                onclick="jQuery(\'#document_section_div_' . $suffix . '\').remove();"><span>' . _fdtk('delete_section') . '</span></button></div>';
            } else {
                if (is_numeric($_REQUEST['form_id'])) {
                    $parentFormId = Sanitize::SanitizeInt($_REQUEST['form_id']);
                } elseif (is_numeric($_REQUEST['parent_form_id'])) {
                    $parentFormId = Sanitize::SanitizeInt($_REQUEST['parent_form_id']);
                } else {
                    $parentFormId = '';
                }

                $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';
                $Title .= '<div class="title_rhs_container">
                <button type="button" id="clear_section_' . $suffix . '" class="prince-button button-inline red-gradient" onclick="ReplaceSection(\'' . $module . '\',' . $suffix . ', \'document\', \'\', ' . $spellChecker . ', \'' . $parentFormId . '\')">
                        <span>' . _fdtk('button_clear_section') . '</span>
                    </button>
                </div>';
            }
        }

        $documentTable->makeTitleRow($Title);

        $codes = (new CodeInfoRetrieverFactory())->create()->retrieveAll('link_documents.doc_type');

        $LinkTitle = '<label for="doc_type_' . $suffix . '_title"><img src="images/Warning.gif" alt="Warning" /> ' . _fdtk('link_as') . GetValidationErrors($data, 'doc_type_' . $suffix) .
            '<div id="errdoc_type_' . $suffix . '" style="display:none"><font color="red">' . _fdtk('mandatory_err') . '</font></div></label><input type="hidden" id="show_field_doc_type_' . $suffix . '" value="1" />';

        $field = SelectFieldFactory::createSelectField('doc_type_' . $suffix, $module, Sanitize::SanitizeString($data['doc_type_' . $suffix]), '');
        $field->setCustomCodes($codes);
        $documentTable->setCurrentSection('extra_documents');
        $documentTable->makeRow($LinkTitle, $field, false, false, [
            'dbfield' => 'doc_type_' . $suffix,
        ]);

        $DescTitle = '
        <label for="doc_notes_' . $suffix . '">
            <img src="images/Warning.gif" alt="Warning" /> ' .
            _fdtk('description') .
            GetValidationErrors($data, 'doc_notes_' . $suffix) .
            '<div id="errdoc_notes_' . $suffix . '" style="display:none">
                <font color="red">' . _fdtk('mandatory_err') . '</font>
            </div>
        </label>
        <input type="hidden" id="show_field_doc_notes_' . $suffix . '" value="1" />';

        $documentTable->makeRow(
            $DescTitle,
            InputFieldFactory::create(
                'New',
                'doc_notes_' . $suffix,
                50,
                50,
                Sanitize::SanitizeString($data['doc_notes_' . $suffix]),
                '',
            ),
            false,
            false,
            [
                'dbfield' => 'doc_notes_' . $suffix,
            ],
        );

        $uploadNoFileChosenMessage = _fdtk('no_file_chosen');
        $UploadTitle = '<label for="userfile_' . $suffix . '"><img src="images/Warning.gif" alt="Warning" /> ' . _fdtk('attach_this_file') . GetValidationErrors($data, 'userfile_' . $suffix) .
            '<div id="erruserfile_' . $suffix . '" style="display:none"><font color="red">' . _fdtk('mandatory_err') . '</font></div></label><input type="hidden" id="show_field_userfile_' . $suffix . '" value="1" />';
        $UploadField = '<div>
        <button id="userfile_' . $suffix . '_button">' . _fdtk('choose_a_file') . '</button>
        <span id="userfile_' . $suffix . '_file_chosen">' . $uploadNoFileChosenMessage . '</span>
        <input contentEditable="false" name="userfile_' . $suffix . '" id="userfile_' . $suffix . '" type="file" size="50" style="display: none;" />
        <script>
            if (typeof window.mandatoryPopovers === "undefined") window.mandatoryPopovers = [];

            window.mandatoryPopovers.push({
                "dbField": "doc_type_' . $suffix . '",
                "sectionTitle": "documents"
            });
            window.mandatoryPopovers.push({
                "dbField": "doc_notes_' . $suffix . '",
                "sectionTitle": "documents"
            });
            window.mandatoryPopovers.push({
                "dbField": "userfile_' . $suffix . '",
                "sectionTitle": "documents"
            });

            $("#userfile_' . $suffix . '_button").click(function () {$("#userfile_' . $suffix . '").trigger("click");});

            $("#userfile_' . $suffix . '").change(function () {
                const file = this.files?.[0] || null;
                $("#userfile_' . $suffix . '_error").remove();

                if (file && !isFileSizeWithinLimit(file.size)) {
                    const errorMessage = file?.size === 0
                        ? (file?.name ? file.name + ": " : "") + getZeroFileSizeLimitErrorMessage()
                        : getMaxFileSizeLimitErrorMessage();

                    $("<div>", {
                        id: "userfile_' . $suffix . '_error",
                        class: "userfile_error_element badge badge-error badge-red",
                        text: errorMessage,
                        css: {
                            "display": "block"
                        }
                    }).insertAfter($("#userfile_' . $suffix . '_file_chosen"));
                }
                toggleSubmitButtons();
                $("#userfile_' . $suffix . '_file_chosen").html($(this).val().split("\\\").pop() || "' . $uploadNoFileChosenMessage . '");
            });

            $(document).on("click", "[id^=clear_section_], [id^=delete_section_]", function() {
                let sectionAttribute = $(this).attr("id");
                let suffix = sectionAttribute.split("_")[2];
                resetSubmitBtnVisibility(suffix);
            });

            function resetSubmitBtnVisibility(suffix) {
                $("#userfile_" + suffix + "_error").remove();
                toggleSubmitButtons();
            }

            function toggleSubmitButtons() {
                $(".dtx-button.button-primary, #icon_link_btnSave").prop("disabled", $(".userfile_error_element:visible").length > 0);
            }
        </script>
    </div>' .
            ($_FILES['userfile_' . $suffix]['name'] ? '<div><i>(You will need to re-select this file. The file was ' . $_FILES['userfile_' . $suffix]['name'] . ')</i></div>' : '');

        $documentTable->makeRow(
            $UploadTitle,
            CustomFieldFactory::create('', $UploadField),
            false,
            false,
            [
                'dbfield' => 'userfile_' . $suffix,
            ],
        );

        $documentTable->makeTable();

        if ($aParams['FormType'] != 'ReadOnly' && $aParams['FormType'] != 'Print') {
            $JSFunctions[] = '
            mandatoryArray.push(new Array("doc_type_' . $suffix . '","document_section_div_' . $suffix . '","' . _fdtk('link_as') . ' (Document ' . $suffix . ')"));
            mandatoryArray.push(new Array("doc_notes_' . $suffix . '","document_section_div_' . $suffix . '","' . _fdtk('description') . ' (Document ' . $suffix . ')"));
            mandatoryArray.push(new Array("userfile_' . $suffix . '","document_section_div_' . $suffix . '","' . _fdtk('Attach this file') . ' (Document ' . $suffix . ')"));
                       ';
        }

        echo $documentTable->getFormTable();
    }
}

/**
 * @desc Returns a string of JavaScript to jump to the specified panel or section of a form.
 *
 * @param bool $Show_all_section 'Y' if all panels/sections are displayed on the one page, 'N' if panels are used
 * @param string $panel the id of the panel to display
 * @param FormTable $Table the FormTable object describing the form
 */
function JavascriptPanelSelect($Show_all_section, $panel, $Table, $Field = '')
{
    global $FieldSectionReference;

    if ($Field) {
        $panel = $FieldSectionReference[$Field];
    }

    // If the table does not have panels, we just want to return.
    if (!$Table->hasPanels()) {
        return;
    }

    if ($panel) {
        if (bYN($Show_all_section)) {
            $MySection = $panel;
        } else {
            // We need to check if this is a panel or a section
            $Panels = $Table->getPanelArray();

            if ($Panels[$panel]) {
                $MyPanel = $panel;
            } else { // try to find the panel this section is in.
                $MySection = $panel;

                foreach ($Panels as $panel_id => $sections) {
                    if (in_array($panel, $sections)) {
                        $MyPanel = $panel_id;
                    }
                }
            }
        }
    }

    if (empty($MyPanel) && count($Table->PanelArray) > 0) {
        $MyPanel = $Table->getFirstPanel();
    }

    $HTML = '';

    if (!empty($MyPanel)) {
        $HTML .= '<script language="javascript" type="text/javascript">showFormPanel(\'panel-' . Escape::EscapeEntities($MyPanel) . '\')</script>';
    }

    if (isset($MySection)) {
        $HTML .= '<script language="javascript" type="text/javascript">window.location.hash=\'' . Escape::EscapeEntities($MySection) . '\'</script>';
    }

    return $HTML;
}

/**
 * Only works for Level 2 forms.
 *
 * @deprecated deprecated since version 11.0
 */
function IncludeCurrentFormDesign($module = 'INC', $formlevel = 2, $FormID = null, $FormType = '')
{
    $formDesignLoader = Container::get(FormDesignInstanceLoader::class);

    $FormDesign = Forms_FormDesign::GetFormDesign([
        'module' => $module,
        'level' => $formlevel,
        'id' => $FormID,
        'form_type' => $FormType,
    ]);

    $formDesignLoader->load($FormDesign);

    $aReturn['sSettingsFilename'] = $FormDesign->getFileName();

    return $aReturn;
}

function ArrayToSelect($aParams)
{
    $sID = $aParams['id'];
    $aOptionArray = $aParams['options'];
    $CurrentValue = $aParams['value'];
    $disabled = ($aParams['disabled'] === true ? 'disabled=disabled' : '');

    $sSelect = '<select id="' . $sID . '" name="' . $sID . '" ' . $disabled . '>';

    if (is_array($aOptionArray)) {
        foreach ($aOptionArray as $value => $text) {
            // Make sure we don't accidentally have null = ''
            if ($CurrentValue === $value || ($CurrentValue != null && $CurrentValue == $value)) {
                $sSelect .= '<option value="' . $value . '" selected="selected">' . $text . '</option>';
            } else {
                $sSelect .= '<option value="' . $value . '">' . $text . '</option>';
            }
        }
    }

    $sSelect .= '</select>';

    return $sSelect;
}

/**
 * @desc Generic function. Picks up an array of functions from AppVars and runs them all,
 * taking arrays of data they return and returning a merged array of them all. Used to load in extra
 * non-standard data after all main table/normal linked data has been collected
 *
 * @param string $module the current module
 * @param int $recordid the current recordid
 *
 * @return array array of additional data
 *
 * @deprecated
 *
 * @see \src\helpers\FormHelper::GetExtraData()
 */
function GetExtraData($module, $recordid, $data = [])
{
    return Container::get(\src\helpers\FormHelper::class)->GetExtraData($module, $recordid, $data);
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckMultiListValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckMultiListsFromArray($aFields, $aData)
{
    $aValues = [];

    foreach ($aFields as $sFieldName) {
        if (isset($aData[$sFieldName]) || $aData['CHANGED-' . $sFieldName] == '1') {
            $FieldVal = CheckMultiListValue($sFieldName, $aData);
            $aValues[$sFieldName] = $FieldVal;
        }
    }

    return $aValues;
}

/**
 * @desc Called by {@link CheckMultiListsFromArray()} to prepare multicode field data for
 * input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string|null string representation of the multicode value in string form, ready for insertion
 */
function CheckMultiListValue($sName, array $aData)
{
    if (is_array($aData[$sName])) {
        return implode(' ', $aData[$sName]);
    }

    if ($aData[$sName] != '') { // read only/default hidden value
        return $aData[$sName];
    }

    if (!is_array($aData[$sName]) && isset($aData[$sName])) {
        return null;
    }
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckDateValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckDatesFromArray($aFields, $aData)
{
    $aValues = [];

    foreach ($aFields as $sFieldName) {
        $Value = CheckDateValue($sFieldName, $aData);

        if (isset($aData[$sFieldName])) {
            $aValues[$sFieldName] = $Value;
        }
    }

    return $aValues;
}

/**
 * @desc Called by {@link CheckDatesFromArray()} to prepare date field data for input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string representation of the date value in string form, ready for insertion
 */
function CheckDateValue($sName, $aData)
{
    if (isset($aData[$sName]) && $aData[$sName] != null) {
        return UserDateToSQLDate($aData[$sName]);
    }

    if (isset($aData[$sName])) {
        return null;
    }
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckNumberValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $fields array of fields to modify
 * @param array $formData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckNumbersFromArray(array $fields, $formData)
{
    $values = [];

    foreach ($fields as $fieldName) {
        if (is_array($formData[$fieldName])) {
            $arrayValues = [];

            foreach ($formData[$fieldName] as $index => $value) {
                $fieldValue = CheckNumberValue($index, $formData[$fieldName]);

                if (isset($formData[$fieldName][$index])) {
                    $arrayValues[$index] = $fieldValue;
                }
            }

            if (isset($formData[$fieldName])) {
                $values[$fieldName] = $arrayValues;
            }
        } else {
            $fieldValue = CheckNumberValue($fieldName, $formData);

            if (isset($formData[$fieldName])) {
                $values[$fieldName] = $fieldValue;
            }
        }
    }

    return $values;
}

/**
 * @desc Called by {@link CheckNumbersFromArray()} to prepare number field data for input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string representation of the number value in string form, ready for insertion
 */
function CheckNumberValue($sName, $aData)
{
    if ($aData[$sName] !== null && $aData[$sName] !== '') {
        return (int) $aData[$sName];
    }

    if (isset($aData[$sName])) {
        return null;
    }
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckDecimalValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckDecimalsFromArray($aFields, $aData)
{
    $aValues = [];

    foreach ($aFields as $sFieldName) {
        $Value = CheckDecimalValue($sFieldName, $aData);

        if (isset($aData[$sFieldName])) {
            $aValues[$sFieldName] = $Value;
        }
    }

    return $aValues;
}

/**
 * @desc Called by {@link CheckDecimalsFromArray()} to prepare decimal field data for input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string representation of the decimal value in string form, ready for insertion
 */
function CheckDecimalValue($sName, $aData)
{
    if ($aData[$sName] != null && $aData[$sName] != '') {
        return (float) $aData[$sName];
    }

    if (isset($aData[$sName])) {
        return null;
    }
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckMoneyValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckMoneyFromArray($aFields, $aData)
{
    $aValues = [];

    foreach ($aFields as $sFieldName) {
        $Value = CheckMoneyValue($sFieldName, $aData);

        if (isset($aData[$sFieldName])) {
            $aValues[$sFieldName] = $Value;
        }
    }

    return $aValues;
}

/**
 * @desc Loops through a list of provided field names and calls {@link CheckTreeValue()} for each.
 * Returns an array of values ready to be used in a SQL update statement.
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array array of field/value pairs ready for input into database
 */
function CheckTreeFromArray($aFields, $aData)
{
    $aValues = [];

    foreach ($aFields as $sFieldName) {
        $Value = CheckTreeValue($sFieldName, $aData);

        if (isset($aData[$sFieldName])) {
            $aValues[$sFieldName] = $Value;
        }
    }

    return $aValues;
}

/**
 * Checks all textarea fields on the form to see if they have been set up
 * as Timestamp fields and reformats posted values accordingly.
 *
 * @global array  $TimestampFields
 *
 * @param string[] $aFields the textarea fields on the form
 * @param string $aSuffix identifier for linked data
 * @param array $aData the submitted form data
 *
 * @return array $aValues  the textarea contents with any necessary modifications
 */
function CheckTextAreaFromArray($aFields, $aSuffix, $aData, $FormDesign = null)
{
    global $TimestampFields, $FieldDefs;

    if ($FormDesign) {
        $TimestampFields = $FormDesign->TimestampFields;
    }

    $aValues = [];

    $user = $_SESSION['logged_in'] ? DatixDBQuery::PDO_fetch('SELECT (use_forenames + \' \' + use_surname) AS name FROM users_main WHERE initials = \'' . $_SESSION['initials'] . '\'', [], PDO::FETCH_COLUMN) : 'the reporter';

    $date = GetParm('FMT_DATE_WEB') == 'US' ? date('m/d/Y H:i:s') : date('d/m/Y H:i:s');

    foreach ($aFields as $sFieldName) {
        if ($TimestampFields[$sFieldName] && !$FieldDefs[$_GET['module']][$sFieldName]['MaxLength']) {
            if ($aData[$sFieldName . $aSuffix] != '') {
                if (!isLevelOneForm() && $aData['CURRENT_' . $sFieldName . $aSuffix] === null) {
                    // prevent field being date-stamped if read-only
                    $newValue = $aData[$sFieldName . $aSuffix];
                } else {
                    $newValue = '[' . $date . ' ' . $user . '] ' . $aData[$sFieldName . $aSuffix]
                        . "\r\n" . $aData['CURRENT_' . $sFieldName . $aSuffix];
                }
            } else {
                $newValue = $aData['CURRENT_' . $sFieldName . $aSuffix];
            }

            if (\UnicodeString::substr($sFieldName, 0, 4) == 'UDF_') {
                // we need to modify the $_POST array, since UDFs are saved using $_POST directly
                $_POST[$sFieldName . $aSuffix] = $newValue;
            } else {
                $aValues[$sFieldName . $aSuffix] = $newValue;
            }
        }
    }

    return $aValues;
}

/**
 * @desc Called by {@link CheckMoneyFromArray()} to prepare money field data for input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string representation of the money value in string form, ready for insertion
 */
function CheckMoneyValue($sName, $aData)
{
    if ($aData[$sName] != null && $aData[$sName] != '') {
        return (float) preg_replace("/[^0-9\.-]/u", '', $aData[$sName]);
    }
    if (isset($aData[$sName])) {
        return null;
    }
}

/**
 * @desc Called by {@link CheckTreeFromArray()} to prepare tree field data for input into a SQL update string.
 *
 * @param string $sName field in question
 * @param array $aData data array
 *
 * @return string representation of the tree value in string form, ready for insertion
 */
function CheckTreeValue($sName, $aData)
{
    if ($aData[$sName] != null && $aData[$sName] != '') {
        return (int) $aData[$sName];
    }

    if (isset($aData[$sName])) {
        return null;
    }
}

function ParseRootCauses($aParams)
{
    if (isset($_POST['numrootcauses'])) { // otherwise no data sent - means readonly or hidden
        // Create root causes list from radio buttons
        // only if global INC_INV_RC is NOT set.
        // Also, check to see if $_POST["inc_root_causes"] is set:
        // if it is, use this instead of trying the other values
        if ($_POST['inc_root_causes']) {
            $inc_root_cause_array = $_POST['inc_root_causes'];
        } else {
            $NumRootCauses = $_POST['numrootcauses'];
            if (Container::get(Registry::class)->getParm('INC_INV_RC', 'N', true) == 'N') {
                $cause_group = 1;
                if (isset($_POST['incrootcause_'])) {
                    $inc_root_cause_array[] = $_POST['incrootcause_'];
                }

                while ($cause_group <= $NumRootCauses) {
                    $cause = $_POST["incrootcause_{$cause_group}"];
                    if (!empty($cause)) {
                        $inc_root_cause_array[] = $cause;
                    }

                    ++$cause_group;
                }
            } else {
                for ($i = 0; $i < $NumRootCauses; ++$i) {
                    if ($RootCause = $_POST["incrootcause_{$i}"]) {
                        $inc_root_cause_array[] = $RootCause;
                    }
                }
            }
        }

        if ($inc_root_cause_array) {
            $inc_root_causes = implode(' ', $inc_root_cause_array);
        }

        \UnicodeString::ltrim($inc_root_causes);

        if (!$_POST['inc_root_causes']) {
            $aParams['data']['inc_root_causes'] = $inc_root_causes;
        }
    }

    return $aParams['data'];
}

function GetTitleTable($aParams)
{
    $Section = $aParams['section'];
    $Module = $aParams['module'];

    $TitleDiv = '
        <div class="section_title_group">';

    if (!empty($aParams['sectionarray']['TitleSuffix']) && empty($aParams['sectionarray']['TitleDropdown'])) {
        $aParams['title'] .= $aParams['sectionarray']['TitleSuffix'];
    }

    $TitleDiv .= '
        <div class="section_title">' . Sanitize::SanitizeHtml($aParams['title']) . '</div>';

    if (!empty($aParams['sectionarray']['TitleSuffix']) && !empty($aParams['sectionarray']['TitleDropdown'])) {
        $TitleDiv .= $aParams['sectionarray']['TitleSuffix'];
    }

    if (!empty($aParams['subtitle'])) {
        $TitleDiv .= '
            <div class="section_subtitle">' . Sanitize::SanitizeHtml($aParams['subtitle']) . '</div>';
    }

    if (!empty($aParams['sectionarray']['OrderField']) && ($aParams['formtype'] ?? '') !== 'Search') {
        $TitleDiv .= '
        <div class="field_extra_text">
            ' . _fdtk('form_ass_order') . ': ';

        if ($aParams['formtype'] == 'Print' || $aParams['formtype'] == 'ReadOnly') {
            $TitleDiv .= $aParams['sectionarray']['OrderField']['value'];
        } else {
            $TitleDiv .= '
            <input type="text" size="3" maxlength="3" name="' . $aParams['sectionarray']['OrderField']['id'] . '" id="' . $aParams['sectionarray']['OrderField']['id'] . '" value="' . $aParams['sectionarray']['OrderField']['value'] . '" onKeyPress="return IsPositiveInteger(this, event)" title="order"/>';
        }

        $TitleDiv .= '</div>';
    }

    $TitleDiv .= '
        </div>';

    if ($aParams['twisty']) {
        $TwistyDiv = '
            <div class="title_rhs_container">
                <a class="toggle-trigger">
                    <img id=\'twisty_image_' . $Section . '\' src="images/collapse.gif" alt="+" border="0"/>
                </a>
            </div>';

        $TitleRowHTML = '
            <div class="section_title_wrapper">' .
            $TitleDiv .
            $TwistyDiv .
            '</div>';
    } elseif (!empty($aParams['sectionarray']['right_hand_link'])) {
        $DeleteDiv = '<span style="cursor:pointer" onclick="' . $aParams['sectionarray']['right_hand_link']['onclick'] . '">
            ' . $aParams['sectionarray']['right_hand_link']['text'] . '</span>';

        $DeleteDiv = '
            <div class="title_rhs_container">
                <button type="button" class="dtx-button dtx-button-small button-warning" onclick="' . $aParams['sectionarray']['right_hand_link']['onclick'] . '">
                    <span>' . $aParams['sectionarray']['right_hand_link']['text'] . '</span>
                </button>
            </div>';

        $TitleRowHTML = '
            <div class="section_title_wrapper">' .
            $TitleDiv .
            $DeleteDiv .
            '</div>';
    } elseif (
        !empty($aParams['sectionarray']['ClearSectionOption']) && $aParams['formtype'] != 'Print'
        && $aParams['formtype'] != 'ReadOnly' && $aParams['formtype'] != 'Search'
    ) {
        $Suffix = $aParams['sectionarray']['ContactSuffix'];
        $Type = $aParams['sectionarray']['DynamicSectionType'];
        $Subtype = $aParams['sectionarray']['DynamicSectionSubtype'];
        $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';

        if (is_numeric($_REQUEST['form_id'])) {
            $parentFormId = (int) $_REQUEST['form_id'];
        } elseif (is_numeric($_REQUEST['parent_form_id'])) {
            $parentFormId = (int) $_REQUEST['parent_form_id'];
        } else {
            $parentFormId = '';
        }

        $DeleteDiv = '
            <div class="title_rhs_container">
                <button type="button" class="dtx-button dtx-button-small button-warning" onclick="ReplaceSection(\'' . $Module . '\',' . $Suffix . ', \'' . $Type . '\', \'' . $Subtype . '\', ' . $spellChecker . ', \'' . $parentFormId . '\')">
                    <span>' . _fdtk('button_clear_section') . '</span>
                </button>
            </div>';

        $TitleRowHTML = '
            <div class="section_title_wrapper">' .
            $TitleDiv .
            $DeleteDiv .
            '</div>';
    } elseif (
        !empty($aParams['sectionarray']['DeleteSectionOption']) && $aParams['formtype'] != 'Print'
        && $aParams['formtype'] != 'ReadOnly'
    ) {
        $Suffix = $aParams['sectionarray']['ContactSuffix'];
        $Type = $aParams['sectionarray']['DynamicSectionType'];

        $DeleteDiv = '
            <div class="title_rhs_container">
                <button type="button" class="dtx-button dtx-button-small button-warning" onclick="deleteSectionFromForm(jQuery(\'#' . $Type . '_section_div_' . $Suffix . '\'),\'' . $Type . '\',' . $Suffix . ');">
                    <span>' . _fdtk('delete_section') . '</span>
                </button>
            </div>';

        $TitleRowHTML = '
            <div class="section_title_wrapper">' .
            $TitleDiv .
            $DeleteDiv .
            '</div>';
    } else {
        $TitleRowHTML = $TitleDiv;
    }

    return $TitleRowHTML;
}

/**
 * @desc Returns a list of form design names for a particular module and level, keyed by the form id.
 *
 * @param array $aParams array of parameters
 *
 * @return array list of form designs keyed by form id
 */
function GetListOfFormDesigns($aParams)
{
    $moduleDefs = Container::get(ModuleDefs::class);
    $module = $moduleDefs[$aParams['module']];

    $mapper = (new FormDesignModelFactory())->getMapper();
    $formDesigns = $mapper->selectAllGroupedByModuleAndLevel();
    $forms = [];

    if ($aParams['level'] == 1 && $module->hasLevelOneForms()) {
        /** @var FormDesign $formDesign */
        foreach ($formDesigns[$module->getCode()][1] as $formDesign) {
            $forms[$formDesign->getId()] = $formDesign->getName();
        }
    }

    if ($aParams['level'] == 2 && $module->hasLevelTwoForms()) {
        /** @var FormDesign $formDesign */
        foreach ($formDesigns[$module->getCode()][2] as $formDesign) {
            $forms[$formDesign->getId()] = $formDesign->getName();
        }
    }

    // When listing linked form designs we need to have a "None selected" option to allow users to fall back on globals.
    if (in_array($aParams['module'], ['ACT', 'CON', 'AST', 'PAY'])) {
        $forms[null] = 'None Selected';
    }

    return $forms;
}


function GetSectionVisibility(
    string $module,
    int $level,
    array $data,
    ?string $parentModule = null,
    ?int $parentLevel = null
) {
    global $ModuleDefs, $SectionVisibility, $FieldDefs;

    $FormDesign = Forms_FormDesign::GetFormDesign([
        'module' => $module,
        'level' => $level,
        'parent_module' => $parentModule,
        'parent_level' => $parentLevel,
    ]);

    $FormFile = GetBasicFormFileName($module, $level);
    require_once $FormFile;

    // add user defined sections to the form array
    if (is_array($FormDesign->ExtraSections)) {
        foreach ($FormDesign->ExtraSections as $SectionID => $SectionName) {
            $FormArray['section' . $SectionID] = ['Title' => 'User Defined Section ' . $SectionID];
        }
    }

    $SectionActions = [];
    $SectionVisibility = [];

    if (is_array($FormDesign->ExpandSections)) {
        foreach ($FormDesign->ExpandSections as $Field => $Actions) {
            foreach ($Actions as $Action) {
                $Action['field'] = $Field;
                $SectionActions[$Action['section']][] = $Action;
            }
        }
    }

    $isPsimsEnabled = Container::get(DatixConfig::class)->getPsimsEnabled();
    $psimsTriggerService = Container::get(PsimsTriggerService::class);

    foreach ($FormArray as $Section => $SectionArray) {
        if ($Section == 'Parameters') {
            continue;
        }

        $SectionVisibility[$Section] = false;

        if (!empty($SectionActions[$Section])) {
            foreach ($SectionActions[$Section] as $SectionActionDetails) {
                // Special case for fields that are displayed as checkboxes and trigger a section
                // This is needed because the data arrives here as a string separated by spaces instead of an array
                // Some fields may arrive here as an array, make sure you're not exploding an array
                // TODO: Refactor this section, make sure you're not getting in to a situation where you've already exploded a string and attempting to explode it a second time
                if ($FormDesign->DisplayAsCheckboxes[$SectionActionDetails['field']] === true && !is_array($data[$SectionActionDetails['field']])) {
                    $data[$SectionActionDetails['field']] = explode(' ', $data[$SectionActionDetails['field']]);
                }

                if (is_array($data[$SectionActionDetails['field']])) {
                    foreach ($data[$SectionActionDetails['field']] as $value) {
                        // the triggered section will only be shown if the action is active,
                        // (i.e. the triggering field is shown).
                        // otherwise, we risk saving data triggered by data that is not saved
                        if (
                            in_array($value, $SectionActionDetails['values'], true)
                            && $_POST['show_field_' . $SectionActionDetails['field']] == '1'
                        ) {
                            $SectionVisibility[$Section] = true;
                        }
                    }
                } else {
                    if ($FieldDefs[$module][$SectionActionDetails['field']]['Type'] == 'checkbox') {
                        if ($data[$SectionActionDetails['field']] == 'on') {
                            $data[$SectionActionDetails['field']] = 'Y';
                        }

                        if ($data[$SectionActionDetails['field']] != 'Y') {
                            $data[$SectionActionDetails['field']] = 'N';
                        }
                    }

                    // DW-10822 This is a special case for data to be saved in 2 scenarios:
                    // There is data in some section: *S*, that is triggered by some field: *F*
                    // Scenario 1, *F* is in a shown section, hard hidden and has a default value that is the trigger value for *S* (note: you are an idiot, just hard show the section)
                    // Scenario 2 *A* is visible and has the trigger value set in it.
                    if (
                        in_array($data[$SectionActionDetails['field']], $SectionActionDetails['values'], true)
                        && ($_POST['show_field_' . $SectionActionDetails['field']] == '1') || $FormDesign->HideFields[$SectionActionDetails['field']]
                    ) {
                        $SectionVisibility[$Section] = true;
                    }

                    // IQ-36788: When dealing with tree fields, the trigger value needs to take into account parent values.
                    // If the trigger to show a section is the parent of the value selected, the section should still be shown
                    // eg. if the trigger value is service/estate and the value of the service field is service/estate/catering, the section is visible

                    $fieldDefinition = $FieldDefs[$module][$SectionActionDetails['field']];

                    // Tree fields should have an entry in $FieldDefs for data around MapperType etc. If this data
                    // doesn't exist, then the following tree field action checks won't function properly
                    if ($fieldDefinition !== null) {
                        $isTreeField = $fieldDefinition['Type'] === FieldInterface::TREE_DB;

                        // Tree field action trigger checks only need to be done against tree fields
                        if ($isTreeField === true) {
                            $isFieldVisible = $_POST['show_field_' . $SectionActionDetails['field']] === '1';
                            $fieldValue = $data[$SectionActionDetails['field']];
                            $actionTriggerValues = $SectionActionDetails['values'];

                            $treeFieldHasTriggeredAction = Container::get(FormService::class)->hasTreeFieldTriggeredAction(
                                $fieldDefinition,
                                $fieldValue,
                                $actionTriggerValues,
                            );

                            if (
                                $isFieldVisible === true
                                && $fieldValue !== null
                                && $treeFieldHasTriggeredAction === true
                            ) {
                                $SectionVisibility[$Section] = true;
                            }
                        }
                    }
                }
            }
        }

        if ($isPsimsEnabled && $psimsTriggerService->isSectionVisibleByData($Section, $data)) {
            $SectionVisibility[$Section] = true;
        }

        if ($SectionArray['Show'] && $SectionVisibility[$Section] !== true) { // hard coded show/hide
            if (!$GLOBALS['HideFields'][$SectionArray['Show']]) {
                if ($data[$SectionArray['Show']] == 'Y' || $data[$SectionArray['Show']] == 'on') {
                    $SectionVisibility[$Section] = true;
                } elseif ($data[$SectionArray['Show']] == 'N' || $data[$SectionArray['Show']] == '') {
                    $SectionVisibility[$Section] = false;
                }
            } elseif (empty($SectionActions[$Section])) {
                // the hard coded show/hide is hidden and there is no user-defined one to replace it
                $SectionVisibility[$Section] = true;
            }
        } elseif (empty($SectionActions[$Section])) {
            // there is no show/hide toggle on this section
            $SectionVisibility[$Section] = true;
        }

        if (
            $FormDesign->HideFields[$Section]
            || (isset($SectionArray['Condition']) && $SectionArray['Condition'] === false)
        ) {
            $SectionVisibility[$Section] = null;
        }
    }
}

/**
 *  This steals some code from BlankOutPostValues (Subs) to deal with medications which needs special
 *  functionality *sigh*. I've stripped out parts on the original function to the minimum requirements.
 *
 * @param string $module the module used for getting the form design
 * @param int $level the level of the form, used for getting the form design
 * @param string|null $parentModule the module of the parent form design, used for getting the form design
 * @param int|null $parentLevel the level of the parent form design, used for getting the form design
 * @param Request|null $request the request object, which is also 'blanked out' if provided
 */
function BlankOutPostValues(
    string $module,
    int $level,
    ?string $parentModule = null,
    ?int $parentLevel = null,
    ?Request $request = null
) {
    global $ModuleDefs, $SectionVisibility, $HideFields, $DefaultValues;

    $FormDesign = Forms_FormDesign::GetFormDesign([
        'module' => $module,
        'level' => $level,
        'parent_module' => $parentModule,
        'parent_level' => $parentLevel,
    ]);

    $FormFile = GetBasicFormFileName($module, $level);
    require $FormFile;

    // add user defined sections to the form array
    if (is_array($FormDesign->ExtraSections)) {
        foreach ($FormDesign->ExtraSections as $SectionID => $SectionName) {
            $FormArray['section' . $SectionID] = ['Title' => 'User Defined Section ' . $SectionID];
        }
    }

    // add user defined fields to the form array
    if (is_array($FormDesign->ExtraFields)) {
        foreach ($FormDesign->ExtraFields as $Field => $SectionName) {
            [$FieldID, $GroupID] = explode('_', $Field);
            $ExtraField = new Fields_ExtraField($FieldID, $GroupID);
            $FormArray[$SectionName]['Rows'][] = $ExtraField->getName();
        }
    }

    if (is_array($FormDesign->MoveFieldsToSections)) {
        foreach ($FormDesign->MoveFieldsToSections as $Field => $aDetails) {
            $aDetails['Field'] = $Field;
            $MoveFieldsByNewSection[$aDetails['New']][] = $aDetails;
        }
    }

    if (is_array($FormArray)) {
        foreach ($FormArray as $Section => $SectionArray) {
            if ($Section != 'Parameters' && $Section != 'ram_control_grading' && $Section != 'grading') {
                if ($SectionVisibility[$Section] === false) {
                    if (is_array($SectionArray['Rows'])) {
                        foreach ($SectionArray['Rows'] as $Field) {
                            if (is_array($Field)) {
                                $FieldName = $Field['Name'];
                            } else { // just a string.
                                $FieldName = $Field;
                            }

                            if ($FieldName) {
                                if ($FieldName == 'recordid') { // don't want to blank this out accidentally.
                                    continue;
                                }

                                if ($FieldName == 'inc_type' && is_array($Field) && $Field['Condition'] === false) {
                                    // don't want to blank out inc_type here as a condition of false means it appears elsewhere on the form.
                                    continue;
                                }

                                if (!$FormDesign->MoveFieldsToSections[$FieldName]) {
                                    unset($_POST[$FieldName], $_POST['Select' . $FieldName]);
                                    // in case we are dealing with extra field multicodes - otherwise the saveUDF
                                    // function will wipe out the value

                                    if ($request !== null) {
                                        $request->unSetParameter($FieldName);
                                        $request->unSetParameter('Select' . $FieldName);
                                    }
                                }
                            }
                        }
                    }

                    if (is_array($MoveFieldsByNewSection[$Section])) {
                        foreach ($MoveFieldsByNewSection[$Section] as $aDetails) {
                            $FieldName = $aDetails['Field'];

                            if ($FieldName) {
                                if ($FieldName == 'inc_type' && is_array($Field) && $Field['Condition'] === false) {
                                    // don't want to blank out inc_type here as a condition of false means it appears elsewhere on the form.
                                    continue;
                                }

                                unset($_POST[$FieldName], $_POST['Select' . $FieldName]);
                                // in case we are dealing with extra field multicodes - otherwise the saveUDF
                                // function will wipe out the value


                                if ($request !== null) {
                                    $request->unSetParameter($FieldName);
                                    $request->unSetParameter('Select' . $FieldName);
                                }
                            }
                        }
                    }
                } else {
                    if (is_array($SectionArray['Rows'])) {
                        foreach ($SectionArray['Rows'] as $Field) {
                            if (is_array($Field)) {
                                $FieldName = $Field['Name'];
                            } else { // just a string.
                                $FieldName = $Field;
                            }

                            if ($FieldName == 'recordid') { // don't want to blank this out accidentally.
                                continue;
                            }

                            if ($FieldName == 'inc_type' && is_array($Field) && $Field['Condition'] === false) {
                                // don't want to blank out inc_type here as a condition of false means it appears elsewhere on the form.
                                continue;
                            }

                            if (
                                !$FormDesign->MoveFieldsToSections[$FieldName]
                                && (!($FormDesign->HideFields[$FieldName]
                                    && isset($FormDesign->DefaultValues[$FieldName])))
                            ) {
                                if (!$_POST['show_field_' . $FieldName]) {
                                    unset($_POST[$FieldName], $_POST['Select' . $FieldName]);
                                    // in case we are dealing with extra field multicodes - otherwise the saveUDF
                                    // function will wipe out the value

                                    if ($request !== null) {
                                        $request->unSetParameter($FieldName);
                                        $request->unSetParameter('Select' . $FieldName);
                                    }
                                }
                            }
                        }
                    }

                    if (is_array($MoveFieldsByNewSection[$Section])) {
                        foreach ($MoveFieldsByNewSection[$Section] as $aDetails) {
                            $FieldName = $aDetails['Field'];

                            if (!$_POST['show_field_' . $FieldName] && (!($FormDesign->HideFields[$FieldName]
                                && isset($FormDesign->DefaultValues[$FieldName])))) {
                                if ($FieldName == 'inc_type' && is_array($Field) && $Field['Condition'] === false) {
                                    // don't want to blank out inc_type here as a condition of false means it appears elsewhere on the form.
                                    continue;
                                }

                                unset($_POST[$FieldName], $_POST['Select' . $FieldName]);
                                // in case we are dealing with extra field multicodes - otherwise the saveUDF
                                // function will wipe out the value

                                if ($request !== null) {
                                    $request->unSetParameter($FieldName);
                                    $request->unSetParameter('Select' . $FieldName);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

function GetBasicFormFileName($Module, $level)
{
    global $ModuleDefs;

    if (!empty($ModuleDefs[$Module]['GENERIC'])) {
        if (file_exists('Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/BasicForm' . $level . '.php')) {
            $Return = 'Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/BasicForm' . $level . '.php';
        } else {
            $Return = 'Source/generic/BasicForm' . $level . '.php';
        }
    } else {
        $Return = $ModuleDefs[$Module]['BASIC_FORM_FILES'][$level];
    }

    return $Return;
}

/**
 * @desc Constructs the HTML wrapper for a field to be placed in a Datix form.
 *
 * @param string $Label The Field label to appear beside the field
 * @param string $FieldHTML The HTML of the field itself
 * @param int $Width the width in percent that the label should take up
 * @param string $RowId the name and id to give to the <li> object that represents the field row
 * @param bool $Hide true if this fieldrow should be hidden initially
 * @param bool $UseLists true if this fieldrow should use <li> rather than <div> tags
 * @param string $ExtraClasses extra classes to add to the "class" attribute of the row
 *
 * @return string HTML for this fieldrow
 */
function GetDivFieldHTML(
    $Label,
    $FieldHTML,
    $ExtraText = '',
    $Width = 25,
    $RowId = '',
    $Hide = false,
    $UseLists = true,
    $ExtraClasses = ''
) {
    if ($Width == '') {
        $Width = 25;
    }

    if ($UseLists) {
        $Field = '<li class="new_windowbg field_div ' . $ExtraClasses . '"';

        if ($RowId) {
            $Field .= ' id="' . $RowId . '" name="' . $RowId . '"';
        }

        if ($Hide) {
            $Field .= ' style="display:none"';
        }

        $Field .= '>';
    } else {
        $Field = '<div class="new_windowbg field_div"';

        if ($RowId) {
            $Field .= ' id="' . $RowId . '" name="' . $RowId . '"';
        }

        if ($Hide) {
            $Field .= ' style="display:none"';
        }

        $Field .= ' >';
    }

    $Field .= '
        <div class="field_label_div">';

    $Field .= '
            <div class="field_label">' . $Label . '</div>';

    if ($ExtraText) {
        $Field .= '
            <div class="field_extra_text">' . $ExtraText . '</div>';
    }

    $Field .= '
        </div>';

    $Field .= '
        <div>
            ' . $FieldHTML . '
        </div>';

    if ($UseLists) {
        $Field .= '</li>';
    } else {
        $Field .= '</div>';
    }

    return $Field;
}

function getRowList()
{
    switch ($_POST['type']) {
        case 'com_subject':
        case 'pal_subject':
            $Rows = getBasicSubjectForm(['rows' => true, 'module' => $_POST['module']]);

            break;
        case 'com_issue':
            require_once 'Source/generic_modules/COM/ModuleFunctions.php';
            $Rows = getBasicIssueForm(['rows' => true, 'module' => $_POST['module']]);

            break;
        case 'inc_causal_factor':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => src\causalfactors\controllers\CausalFactorsController::class],
            );
            $controller->setRequestParameter('aParams', ['rows' => true, 'module' => $_POST['module']]);
            $Rows = $controller->doAction('getBasicCausalFactorForm');

            break;
    }

    echo implode(',', $Rows['Rows']);
}

function AddDynamicSection()
{
    $moduleDefs = Container::get(ModuleDefs::class);

    $savedFormDesignSettings = saveFormDesignSettings();
    unsetFormDesignSettings();

    $data = $_POST;
    $type = RemoveSuffix(Sanitize::SanitizeString($data['type']));
    $module = Sanitize::getModule($data['module']);
    $level = Sanitize::SanitizeString($data['level']);
    $suffix = Sanitize::SanitizeString($data['suffix']);
    $clear = Sanitize::SanitizeString($data['clearsection']);

    // Persist the reporter role when we are clearing the section (same as in NewDIF1Controller)
    if ($data['subtype'] === 'R') {
        $data['link_role'] = Container::get(Registry::class)->getParm('REPORTER_ROLE', 'REP', true);
    }

    switch ($type) {
        case 'pal_subject':
        case 'com_subject':
            echo getSubjectSectionHTML([
                'module' => $module,
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'clearsection' => $clear,
                'subject_name' => $type,
            ]);

            break;
        case 'com_issue':
            require_once 'Source/generic_modules/COM/ModuleFunctions.php';
            echo getIssueSectionHTML([
                'module' => $module,
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'clearsection' => $clear,
                'issue_name' => $type,
            ]);

            break;
        case 'cla_causal_factor':
        case 'inc_causal_factor':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => src\causalfactors\controllers\CausalFactorsController::class],
            );
            $controller->setRequestParameter('aParams', [
                'module' => $module,
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'clearsection' => $clear,
                'causal_factor_name' => $type,
            ]);
            echo $controller->doAction('getCausalFactorsSectionHTML');

            break;
        case 'document':
            echo get_document_section([
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'clear' => $clear,
                'module' => $module,
            ]);

            break;
        case 'contact':
            get_contact_section([
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'module' => $module,
                'contacttype' => Sanitize::SanitizeString($_POST['subtype']),
                'clear' => $clear,
                'FormType' => 'New',
                'form_id' => $data['form_id'],
            ]);

            break;
        case 'step':
            $loader = new src\framework\controller\Loader();
            $controller = $loader->getController([
                'controller' => src\admin\actionchains\controllers\ActionChainStepsController::class,
            ]);
            echo $controller->doAction('getStep');

            break;
        case 'organisation':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => src\respondents\controllers\RespondentsController::class],
            );
            $controller->setRequestParameter('aParams', [
                'data' => $data,
                'ajax' => true,
                'formtype' => FormTable::MODE_NEW,
                'level' => $level,
                'suffix' => $suffix,
                'module' => $module,
                'organisationType' => Sanitize::SanitizeString($_POST['subtype']),
                'clear' => $clear,
                'FormType' => FormTable::MODE_NEW,
            ]);
            echo $controller->doAction('getOrganisationSection');

            break;
        case 'medication':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => MedicationController::class],
            );
            $controller->setRequestParameters([
                'suffix' => $suffix,
                'level' => $level,
                'FormType' => FormTable::MODE_NEW,
                'formId' => $data['form_id'] ?? null,
            ]);
            echo $controller->doAction('renderMedicationForm');

            break;
        case 'equipment':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => EquipmentController::class],
            );
            $controller->setRequestParameters([
                'suffix' => $suffix,
                'level' => $level,
                'FormType' => FormTable::MODE_NEW,
                'formId' => $data['form_id'] ?? null,
            ]);
            echo $controller->doAction('renderEquipmentForm');

            break;
        default:
            // check whether this is a generic "add another" section
            if ($moduleDefs[$module]['LINKED_RECORDS'][$type]) {
                $loader = new Loader();
                $controller = $loader->getController(
                    ['controller' => src\addanother\controllers\GenericAddAnotherController::class],
                );
                $controller->setRequestParameter('extraParameters', [
                    'linkedRecordSpecificationKey' => $type,
                    'suffix' => $suffix,
                    'level' => $level,
                ]);
                echo $controller->doAction('dynamicAddAnotherSection');
            }

            break;
    }

    // Script to bind the calendar to date fields
    echo '
        <script language="javascript" type="text/javascript">
             $(document).ready(function() { initialiseCalendars(); });
        </script>
    ';

    loadFormDesignSettings($savedFormDesignSettings);
}

function getDefaultRepApprovedValue($aParams)
{
    if ($aParams['perms'] == '') {
        $aParams['perms'] = ['NONE'];
    } elseif (!is_array($aParams['perms'])) {
        $aParams['perms'] = [$aParams['perms']];
    }

    $CurrentPerms = $aParams['perms'];

    if ($GLOBALS['DefaultValues']['rep_approved'] && $aParams['data']['rep_approved_old'] == '') {
        if (checkApprovalStatusTransferLegitimate([
            'to' => $GLOBALS['DefaultValues']['rep_approved'],
            'from' => ($aParams['data']['rep_approved_old'] ?: 'NEW'),
            'perm' => $CurrentPerms,
            'module' => $aParams['module'],
        ])) {
            $value = $GLOBALS['DefaultValues']['rep_approved'];
        }
    }

    if ($value == '' && $aParams['data']['rep_approved_old']) {
        if (checkApprovalStatusTransferLegitimate([
            'to' => $aParams['data']['rep_approved_old'],
            'from' => $aParams['data']['rep_approved_old'],
            'perm' => $CurrentPerms,
            'module' => $aParams['module'],
        ])) {
            $value = $aParams['data']['rep_approved_old'];
        }
    }

    if ($value == '') {
        $ApproveArray = GetLevelsTo($aParams['module'], $CurrentPerms, 'NEW');

        if (count($ApproveArray)) {
            foreach ($ApproveArray as $status => $desc) {
                if (!$value) {
                    $value = $status;
                }
            }
        }
    }

    if (!$value) {
        AddSessionMessage('ERROR', 'Warning: You may not be able to save this record due to a problem with the approval status workflow setup');
    }

    return $value;
}

function FormIDExists($ID, $module, $level = 1)
{
    $mapper = (new FormDesignModelFactory())->getMapper();

    return $mapper->findByModuleLevelAndId($module, $level, $ID) !== null;
}

/**
 * @desc Determines which buttons should be displayed at the base of the form, either using the workflows
 * (if approval buttons are switched on) or using a default set of buttons.
 *
 * @param array $aParams Array of parameters
 *
 * @return string HTML code describing buttons
 */
function GetFormButtonsHTML($aParams)
{
    global $ModuleDefs, $OnSubmitJS, $JSFunctions;

    $registry = Container::get(Registry::class);

    $module = $aParams['module'];

    $aParams['perms'] = $registry->getParm($ModuleDefs[$module]['PERM_GLOBAL'])->toScalar();

    $HTML = '';

    $recordsService = (new RecordsFactory())->create();
    $allowsStatusEdit = $recordsService->allowsReadonlyApprovalStatusEditing($module, $aParams['data']['rep_approved']);

    $rejectedStatus = $aParams['data']['rep_approved'] == 'REJECT';

    if (($allowsStatusEdit || $rejectedStatus)
        && $aParams['formtype'] != 'Search'
        && (CanEditRecord($aParams) || CanMoveRecord($aParams))
    ) {
        // not an ideal solution, but some proper work needs to be done on re-structuring the
        // formtype/formmode options available, since they are not currently particularly useful.
        $aParams['formtype'] = 'Edit';
    }

    $BACK_BUTTON = '';
    if (!empty($_GET['fromlisting']) && $_GET['fromlisting'] == 1) {
        $BACK_BUTTON = '<button type="button" class="dtx-button button-clear" name="btnBack" id="btnBack"'
            . ' onclick="submitClicked=true;selectAllMultiCodes();document.forms[0].rbWhat.value=\'BackToListing\';this.form.submit();">' . _fdtk('btn_back_to_report') . '</button>';
    }

    switch ($aParams['formtype']) {
        case 'ReadOnly':
        case 'Locked':
            $HTML = '<button type="button" class="dtx-button button-clear" name="btnCancel" id="btnCancel"'
                . ' onclick="submitClicked=true;selectAllMultiCodes();document.forms[0].rbWhat.value=\'Cancel\';this.form.submit();">' . _fdtk('btn_cancel') . '</button>'
                . $BACK_BUTTON;

            break;
        case 'Search':
            $HTML = '<button type="button" class="dtx-button button-clear" name="btnCancel" id="btnCancel"'
                . ' onclick="submitClicked=true;selectAllMultiCodes();document.forms[0].rbWhat.value=\'Cancel\';this.form.submit();">' . _fdtk('btn_cancel') . '</button>'

                . $BACK_BUTTON

                . '<button type="button" class="dtx-button button-primary" name="btnSave" id="btnSave"'
                . ' onclick="submitClicked=true;selectAllMultiCodes();document.forms[0].rbWhat.value=\'Search\';this.form.submit();">' . _fdtk('form_search') . '</button>';

            break;
        case 'Print': // no buttons
            break;
        default:
            $recordSubmitDuration = ($aParams['formtype'] == 'New' && $ModuleDefs[$aParams['module']]['RECORD_SUBMIT_DURATION']) ? 'true' : 'false';

            $HTML = '<button type="button" class="dtx-button button-clear" name="btnCancel" id="btnCancel"'
                . ' onclick="' . getConfirmCancelJavascript() . '">' . _fdtk('btn_cancel') . '</button>';

            $HTML .= $BACK_BUTTON;

            $FormIdentifier = '#frmIncidentDIF1';

            if (!bYN(GetParm('DIF_1_NO_PRINT')) && !$_SESSION['logged_in'] && !$registry->getDeviceDetector()->isTablet()) {
                $HTML .= '<button type="button" class="dtx-button button-primary" '
                    . 'onclick="submitDtxForm(' . $recordSubmitDuration . ', \'' . $FormIdentifier . '\', true);" name="btnSubmitPrint" id="btnSubmitPrint" />' . _fdtk('form_submit_print') . '</button>';
            }

            $HTML .= '<button type="button" class="dtx-button button-primary" name="btnSave" id="btnSave"'
                . ' onclick="submitDtxForm(' . $recordSubmitDuration . ', \'' . $FormIdentifier . '\');">' . _fdtk('btn_save') . '</button>';

            $nextStatusSTLC = isset($aParams['formDesign']->DefaultValues['rep_approved']) && $aParams['formDesign']->DefaultValues['rep_approved'] === 'STCL';
            $JSFunctions[] = (new \ButtonGroup())->GetSaveLabelJS($FormIdentifier, $nextStatusSTLC);

            break;
    }

    return $HTML;
}

function GetFormLevel($Module, $Perms, $ApprovalStatus)
{
    $sql = 'SELECT las_form_level FROM link_access_approvalstatus WHERE
            access_level = \'' . $Perms . '\' AND
            module = \'' . $Module . '\' AND
            code = \'' . $ApprovalStatus . '\' AND
            las_workflow = ' . GetWorkflowID($Module);

    $row = db_fetch_array(db_query($sql));

    return $row['las_form_level'] ?: 2;
}

/**
 * @desc Checks whether the current form design is formatted correctly or not. If the form is set
 * up in such a way as to allow the form to be saved without a rep_approved value, the user is warned.
 * Doesn't return anything, but stores errors in the flash message container.
 */
function ValidateFormDesign(?FlashMessageContainer $flashMessageContainer = null)
{
    global $HideFields, $DefaultValues, $MoveFieldsToSections, $ExpandFields, $ExpandSections;

    if ($flashMessageContainer === null) {
        $flashMessageContainer = (new FlashMessageContainerFactory())->create();
    }

    $rep_approved_section = GetDefaultRepApprovedSection([
        'module' => $_GET['module'],
        'level' => ($_GET['formlevel'] ?: 1),
    ]);

    if ($rep_approved_section) {
        $rep_approved_section_hard_hide = false;
        $rep_approved_section_soft_hide = false;
        $rep_approved_field_hard_hide = false;
        $rep_approved_field_soft_hide = false;

        if (isset($MoveFieldsToSections['rep_approved'])) {
            $rep_approved_section = $MoveFieldsToSections['rep_approved']['New'];
        }

        if ($HideFields['rep_approved'] && !$DefaultValues['rep_approved']) {
            $rep_approved_field_hard_hide = true;
        }

        if ($HideFields[$rep_approved_section]) {
            $rep_approved_section_hard_hide = true;
        }

        if (is_array($ExpandSections)) {
            foreach ($ExpandSections as $field => $actions) {
                if ($rep_approved_section_hard_hide) {
                    break;
                }

                foreach ($actions as $details) {
                    if ($details['section'] == $rep_approved_section) {
                        $rep_approved_section_soft_hide = true;

                        break;
                    }
                }
            }
        }

        if (is_array($ExpandFields)) {
            foreach ($ExpandFields as $field => $actions) {
                if ($rep_approved_field_hard_hide) {
                    break;
                }

                foreach ($actions as $details) {
                    if ($details['field'] == 'rep_approved') {
                        $rep_approved_field_soft_hide = true;

                        break;
                    }
                }
            }
        }

        if ($rep_approved_field_hard_hide) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('rep_approved_field_hard_hide')));
        } elseif ($rep_approved_section_hard_hide) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('rep_approved_section_hard_hide')));
        } elseif ($rep_approved_section_soft_hide) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('rep_approved_section_soft_hide')));
        } elseif ($rep_approved_field_soft_hide) {
            $flashMessageContainer->addMessage(new InfoMessage(_fdtk('rep_approved_field_soft_hide')));
        }
    }
}

/**
 * Returns the id of the section where the rep_approved field is found by default.
 *
 * Used when validating the form design, since we need to know where this field is, if not specified by the user
 *
 * @return string Section ID
 *
 * @todo refactor so that either return type is string|null or a default string value is returned
 */
function GetDefaultRepApprovedSection($aParams)
{
    if ($aParams['level'] == 1) {
        switch ($aParams['module']) {
            case 'INC':
            case 'COM':
                return 'details';
        }
    } else {
        switch ($aParams['module']) {
            case 'INC':
                return 'name';
            case 'CON':
                return 'contact';
            case 'COM':
                return 'header';
        }
    }
}

/**
 * Determines whether or not we're on a level one form.
 *
 * @return bool
 */
function isLevelOneForm()
{
    return !$_SESSION['logged_in'] || $_GET['action'] == 'newdif1' || $_REQUEST['level'] == '1';
}

/**
 * @desc Function that parses form data and decides if a checkbox should be checked/unchecked
 *
 * @param array $aFields array of fields to modify
 * @param array $aData data array
 *
 * @return array Form checkboxes checked/unchecked
 */
function CheckCheckboxesFromArray($aFields, $aData, $FormDesign = null)
{
    global $MandatoryFields;

    if ($FormDesign) {
        $MandatoryFields = $FormDesign->MandatoryFields;
    }

    $aValues = [];

    foreach ($aFields as $sFieldName) {
        // mandatory and read only checkboxes are yes/no fields, so ignore
        // read only fields are hard to detect (they might move, and either the field or section could be read only), so we use the CHANGED- flag for now
        if (!($MandatoryFields[$sFieldName] || $aData['CHANGED-' . $sFieldName] != '1')) {
            if (isset($aData[$sFieldName])) {
                if ($aData[$sFieldName] != 'Y') {
                    $aValues[$sFieldName] = $aData[$sFieldName];
                } else {
                    $aValues[$sFieldName] = 'Y';
                }
            } elseif ($aData['show_field_' . $sFieldName] == 1) {
                $aValues[$sFieldName] = 'N';
            }
        }
    }

    return $aValues;
}

function ParseSaveData($aParams)
{
    global $FieldDefs;

    if (is_array($aParams['data'])) {
        /* If whitespaces are saved in the database, they will not be matched correctly when searching for matching
         * contacts, since whitespaces are stripped out of that search. This can cause duplicate contacts to be added.
         * To prevent this, we strip the whitespace here before persisting the data.
         */
        if ($aParams['data']['con_nhsno' . $aParams['suffix']]) {
            $aParams['data']['con_nhsno' . $aParams['suffix']] = preg_replace(
                '/\s+/',
                '',
                $aParams['data']['con_nhsno' . $aParams['suffix']],
            );
        }

        /** Handles saving grades */
        if (isset($aParams['data']['incidents_main:inc_grade_initial'])) {
            $aParams['data']['inc_grade_initial'] = $aParams['data']['incidents_main:inc_grade_initial'];
        }
        if (isset($aParams['data']['incidents_main:inc_grade'])) {
            $aParams['data']['inc_grade'] = $aParams['data']['incidents_main:inc_grade'];
        }
        if (isset($aParams['data']['incidents_main:inc_grade_initial_rating'])) {
            $aParams['data']['inc_grade_initial_rating'] = $aParams['data']['incidents_main:inc_grade_initial_rating'];
        }
        if (isset($aParams['data']['incidents_main:inc_grade_rating'])) {
            $aParams['data']['inc_grade_rating'] = $aParams['data']['incidents_main:inc_grade_rating'];
        }

        $aDateValues = CheckDatesFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'date'), $aParams['suffix']),
            $aParams['data'],
        );
        $aMultiListValues = CheckMultiListsFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'multilistbox'), $aParams['suffix']),
            $aParams['data'],
        );
        $aNumberValues = CheckNumbersFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'number'), $aParams['suffix']),
            $aParams['data'],
        );
        $aDecimalValues = CheckDecimalsFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'decimal'), $aParams['suffix']),
            $aParams['data'],
        );
        $aMoneyValues = CheckMoneyFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'money'), $aParams['suffix']),
            $aParams['data'],
        );
        $aTreeValues = CheckTreeFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'tree'), $aParams['suffix']),
            $aParams['data'],
        );
        $aTextAreaValues = CheckTextAreaFromArray(
            array_merge(
                GetAllFieldsByType($aParams['module'], 'textarea'),
                getAllUdfFieldsByType('L', $aParams['suffix'], $aParams['data']),
            ),
            $aParams['suffix'],
            $aParams['data'],
            $aParams['form_design'],
        );
        $aCheckboxesValues = CheckCheckboxesFromArray(
            AddSuffixToFields(GetAllFieldsByType($aParams['module'], 'checkbox'), $aParams['suffix']),
            $aParams['data'],
            $aParams['form_design'],
        );

        $aParams['data'] = array_merge(
            $aParams['data'],
            $aMultiListValues,
            $aDateValues,
            $aNumberValues,
            $aDecimalValues,
            $aMoneyValues,
            $aTreeValues,
            $aTextAreaValues,
            $aCheckboxesValues,
        );

        foreach ($FieldDefs[$aParams['module']] as $field => $details) {
            if (($details['UpperCase'] ?? false) && isset($aParams['data'][$field])) {
                $aParams['data'][$field] = \UnicodeString::strtoupper($aParams['data'][$field]);
            }
        }
    }

    return $aParams['data'];
}

function PopulateLevel1FormFields($aParams)
{
    $data = $aParams['data'];
    $module = $aParams['module'];

    if ($module == 'INC') {
        if ($data['show_equipment'] == '') {
            $data['show_equipment'] = ($data['inc_eqpt_type'] != '' || $data['inc_manufacturer'] != '' || $data['inc_serialno'] != '' || $data['inc_defect'] != '');
        }

        if ($data['show_medication'] == '') {
            $data['show_medication'] = ($data['inc_med_stage'] != '' || $data['inc_med_error'] != '' || $data['inc_med_drug'] != '' || $data['inc_med_drug_rt'] != '' || $data['inc_med_form'] != '' || $data['inc_med_form_rt'] != '' || $data['inc_med_dose'] != '' || $data['inc_med_dose_rt'] != '' || $data['inc_med_route'] != '' || $data['inc_med_route_rt'] != '');
        }

        if ($data['show_pars'] == '') {
            $data['show_pars'] = ($data['inc_agg_issues'] != '' || $data['inc_user_action'] != '' || $data['inc_pol_crime_no'] != '' || $data['inc_pol_called'] != '' || $data['inc_pol_call_time'] != '' || $data['inc_pol_attend'] != '' || $data['inc_pol_att_time'] != '' || $data['inc_pol_action'] != '' || $data['inc_pars_pri_type'] != '' || $data['inc_pars_sec_type'] != '' || $data['inc_pars_clinical'] != '' || $data['inc_pars_address'] != '' || $data['inc_postcode'] != '');
        }

        if ($data['show_person'] == '') {
            $data['show_person'] = (!empty($data['con']['A']));
        }

        if ($data['show_witness'] == '') {
            $data['show_witness'] = (!empty($data['con']['W']));
        }

        if ($data['show_employee'] == '') {
            $data['show_employee'] = (!empty($data['con']['E']));
        }

        // logic for this section is slightly different, since police, assailant etc may have ended up in here on save
        // so might need to be shown even if unticked on form
        $data['show_other_contacts'] = (!empty($data['con']['N']));

        if ($data['show_assailant'] == '') {
            $data['show_assailant'] = ($data['con_title_4'] != '' || $data['con_forenames_4'] != '' || $data['con_surname_4'] != '' || $data['con_nhsno_4'] != '' || $data['con_dob_4'] != '' || $data['con_line1_4'] != '' || $data['con_line2_4'] != '' || $data['con_line3_4'] != '' || $data['con_city_4'] != '' || $data['con_county_4'] != '' || $data['con_country_4'] != '' || $data['con_postcode_4'] != '' || $data['con_tel1_4'] != '' || $data['con_subtype_4'] != '' || $data['con_gender_4'] != '' || $data['con_number_4'] != '' || $data['use_jobtitle_4'] != '' || $data['con_ethnicity_4'] != '');
        }
    } elseif ($module == 'CLA') {
        if ($data['show_employee'] == '') {
            $data['show_employee'] = (!empty($data['con']['E']));
        }

        if ($data['show_other_contacts'] == '') {
            $data['show_other_contacts'] = (!empty($data['con']['N']));
        }
    }

    return $data;
}

/**
 * @desc Takes a module and a field and finds the parents for that field.
 *
 * @param array $aParams Array of parameters
 *
 * @return array An array containing up to two parents as array(parent1, parent2)
 */
function GetParents($aParams)
{
    $parent1 = $_SESSION['CachedValues']['ChildParent'][$aParams['module']][$aParams['field']][0] ?? '';
    $parent2 = $_SESSION['CachedValues']['ChildParent'][$aParams['module']][$aParams['field']][1] ?? '';

    if ($parent1 === $parent2) {
        $parent2 = '';
    }

    return [$parent1, $parent2];
}

/**
 * @desc Takes a module and a field and finds the children for that field.
 *
 * @param array $aParams Array of parameters
 *
 * @return array An array containing an unlimited number of children as array(child1, child2, ...)
 */
function GetChildren($aParams)
{
    global $ModuleDefs;

    $children = [];

    if (!empty($_SESSION['CachedValues']['ParentChild'][$aParams['module']][$aParams['field']])) {
        $children = array_unique($_SESSION['CachedValues']['ParentChild'][$aParams['module']][$aParams['field']]);
    }

    return $children;
}

/**
 * Called from ajax when cascading parent values upwards.
 */
function GetParentValue()
{
    // Suffix manipulation needed for dealing with parenting on e.g. subjects forms.
    $NameArray = explode('_', $_GET['field']);
    if (is_numeric($NameArray[count($NameArray) - 1])) {
        $SuffixString = '_' . $NameArray[count($NameArray) - 1];
    }

    $parents = GetParents(['module' => $_GET['module'], 'field' => RemoveSuffix($_GET['field'])]);
    $SelectedCodeInfo = get_code_info(
        $_GET['module'],
        CheckFieldMappings($_GET['module'], RemoveSuffix($_GET['field']), true),
        $_GET['value'],
    );
    $ParentCodeInfo = get_code_info(
        $_GET['module'],
        CheckFieldMappings($_GET['module'], $parents[0], true),
        $SelectedCodeInfo['cod_parent'],
    );

    $parent_test = explode(' ', $SelectedCodeInfo['cod_parent']);
    $JSONdata['multiparent'] = count($parent_test);
    $JSONdata['value'] = $SelectedCodeInfo['cod_parent'];
    $JSONdata['description'] = $ParentCodeInfo['description'];
    $JSONdata['colour'] = $ParentCodeInfo['cod_web_colour'];
    $JSONdata['fieldname'] = $parents[0] . $SuffixString;
    echo json_encode($JSONdata);
}

/**
 * @deprecated use BasicFormSectionHelper::createReasonsForRejectionSection()
 */
function GenericRejectionArray($module, $data, $useFormDesignLanguage = false)
{
    return [
        'Condition' => bYN(GetParm('REJECT_REASON', 'Y')),
        'Title' => _fdtk('details_rejection', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'NoReadOnly' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            [
                'Name' => 'rea_dlogged',
                'Title' => _fdtk('rea_dlogged', $useFormDesignLanguage),
                'ReadOnly' => true,
                'Type' => 'date',
                'Module' => $module,
            ],
            [
                'Name' => 'rea_con_name',
                'Title' => _fdtk('rea_con_name', $useFormDesignLanguage),
                'ReadOnly' => true,
                'Type' => 'string',
                'Module' => $module,
            ],
            [
                'Name' => 'rea_code',
                'Title' => _fdtk('rea_code', $useFormDesignLanguage),
                'EditableWhenReadonly' => $data['ReasonSectionEditable'] ?? null,
                'Type' => 'ff_select',
                'NoReadOnly' => true,
                'Module' => $module,
            ],
            [
                'Name' => 'rea_text',
                'Title' => _fdtk('rea_text', $useFormDesignLanguage),
                'EditableWhenReadonly' => $data['ReasonSectionEditable'] ?? null,
                'Type' => 'textarea',
                'Rows' => 10,
                'Columns' => 70,
                'NoReadOnly' => true,
                'Module' => $module,
            ],
        ],
    ];
}
