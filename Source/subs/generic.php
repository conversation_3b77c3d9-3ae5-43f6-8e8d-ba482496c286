<?php

use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @desc Takes an array of variables and returns the first one that is not empty
 *
 * @param array $aChoices set of variables to check for values
 *
 * @return string contents of the first variable to contain a value
 */
function FirstNonNull($aChoices)
{
    if (!empty($aChoices)) {
        foreach ($aChoices as $Choice) {
            if ($Choice != null && $Choice !== 0 && $Choice != '' && !(is_array($Choice) && empty($Choice))) {
                return $Choice;
            }
        }
    }

    return null;
}

/**
 * @desc Inserts an array entry (or entries) before a particular key in an existing array, passed by reference.
 *
 * @param array $array the array to be amended
 * @param string $position the key before which the new entries shoud be placed
 * @param array $insert_array array of entries to be inserted
 */
function array_insert_datix(&$array, $position, $insert_array)
{
    if (!is_int($position)) {
        $i = 0;

        foreach ($array as $key => $value) {
            if ($key == $position) {
                $position = $i;

                break;
            }

            ++$i;
        }
    }

    $first_array = array_splice($array, 0, $position);
    $array = array_merge($first_array, $insert_array, $array);
}

/**
 * @desc Merges arrays even if the variables passed are not actually array type. Put in because in php v5
 * array_merge required array-type parameters, where some of the parameters we were passing were null. Anything
 * that is not an array will be ignored.
 *
 * @param array $aArrays array of "arrays" to be merged
 *
 * @return array merge of any arrays passed
 */
function SafelyMergeArrays($aArrays)
{
    $TotalArray = [];

    foreach ($aArrays as $Array) {
        if (is_array($Array)) {
            $TotalArray = array_merge($TotalArray, $Array);
        }
    }

    return $TotalArray;
}


function RemoveTableFromFieldName($Field)
{
    if (\UnicodeString::strpos($Field, '.') === false) {
        $RealFieldName = \UnicodeString::strtolower($Field);
    } else {
        $RealFieldName = \UnicodeString::strtolower(\UnicodeString::substr($Field, \UnicodeString::strpos($Field, '.') + 1));
    }

    return $RealFieldName;
}

function getRecordHash($recordid)
{
    return hash('sha512', 'LEVEL1RECORDID' . $recordid);
}

function HashesMatch($module, $recordid)
{
    return $_SESSION[$module]['LOGGEDOUTRECORDHASH'] == getRecordHash($recordid);
}

function mod_pos($k, $m)
{
    return $k - abs($m) * floor($k / abs($m));
}

/**
 *  Gets the type (calendar or working) and number of days after which an record is considered overdue.
 *
 *  Defaults to 14 days for overall settings.
 *  Can also (optionally) return information based upon a specific approval status.
 *  Caches database-based values to prevent repeated queries.
 *
 *  @param string $Module the code for this module
 *  @param string $approvalStatus the approval status code
 *
 *  @return array $OverdueDays Contains overall and optionally approval status-specific overdue settings
 * (individual array elements described below):
 *  <ul>
 *  <li> int $OverdueDays['overall'] The overall number of days before a record is considered overdue.</li>
 *  <li> string $OverdueDays['overall_type'] The method by which overall overdue status is calculated
 * (calendar or working days).</li>
 *  <li> int $OverdueDays[$approvalStatus] The number of days specific to this approval stage before a record is
 * considered overdue.</li>
 *  <li> string $OverdueDays[$approvalStatus.'_type'] The method by which this approval stage's overdue status is
 * calculated (calendar or working days).</li>
 *  <li> string $OverdueDays['overdue_date_field'] A custom date field that can be specified for use when calculating
 * overdue days for this approval status
 *  (instead of using dates from the audit table).</li>
 *  </ul>
 */
function GetOverdueDays($Module = 'INC', $approvalStatus = '')
{
    global $ModuleDefs;

    $OverdueDays = [];
    $OverdueDays['overall'] = GetParm($ModuleDefs[$Module]['OVERDUE_DAY_GLOBAL'] ?? null, 14);
    $OverdueDays['overall_type'] = GetParm($ModuleDefs[$Module]['OVERDUE_TYPE_GLOBAL'] ?? null, 'C');

    if (!empty($approvalStatus)) {
        // values cached in $_SESSION['CachedValues']['OverdueDays'][$Module][$approvalStatus][GetWorkflowID()]
        if (isset($_SESSION['CachedValues']['OverdueDays'][$Module][$approvalStatus][GetWorkflowID($Module)])) {
            $row = $_SESSION['CachedValues']['OverdueDays'][$Module][$approvalStatus][GetWorkflowID($Module)];
        } else {
            $result = db_query("SELECT t.tim_overdue_days, t.tim_type, cas.overdue_date_field
                                FROM timescales t
                                INNER JOIN code_approval_status cas
                                    ON cas.module = t.tim_module
                                    AND cas.code = t.tim_approval_status
                                WHERE tim_module = '{$Module}'
                                AND tim_approval_status = '{$approvalStatus}'
                                AND cas.workflow = " . GetWorkflowID($Module));

            $row = db_fetch_array($result);
            $_SESSION['CachedValues']['OverdueDays'][$Module][$approvalStatus][GetWorkflowID($Module)] = $row;
        }

        if ($row) {
            $OverdueDays[$approvalStatus] = $row['tim_overdue_days'];
            $OverdueDays[$approvalStatus . '_type'] = $row['tim_type'];
            $OverdueDays['overdue_date_field'] = $row['overdue_date_field'];
        } else {
            $OverdueDays[$approvalStatus] = -1;
        }
    }

    return $OverdueDays;
}

/**
 * @desc Gets the url of a given record, ready for a redirect.
 *
 * @param array $Parameters Array of parameters
 *
 * @return string the url of the record in question
 */
function getRecordURL($Parameters)
{
    $registry = Container::get(Registry::class);
    $scriptUrl = $registry->getScriptUrl();
    $moduleDefs = $registry->getModuleDefs();

    if ($Parameters['module'] == '' || $Parameters['recordid'] == '') {
        return '';
    }

    $URL = $scriptUrl . '?';

    if (
        !empty($moduleDefs[$Parameters['module']]['GENERIC'])
        && empty($moduleDefs[$Parameters['module']]['MAIN_URL_OVERRIDE'])
    ) {
        $URL .= 'action=record&module=' . $Parameters['module'];
    } elseif ($Parameters['module'] == 'AST' && !empty($Parameters['linkMode'])) {
        $URL .= 'action=linkequipment';
    } elseif (!empty($moduleDefs[$Parameters['module']]['MAIN_URL'])) {
        $URL .= $moduleDefs[$Parameters['module']]['MAIN_URL'];
        $URL .= '&module=' . $Parameters['module'];
    } else {
        $URL .= 'action=' . $moduleDefs[$Parameters['module']]['ACTION'];
        $URL .= '&module=' . $Parameters['module'];
    }

    $URL .= (!empty($_REQUEST['fromsearch']) ? '&fromsearch=1' : '');
    $URL .= '&' . ($moduleDefs[$Parameters['module']]['PK'] ?? 'recordid') . '=' . $Parameters['recordid'];

    if (!empty($Parameters['panel'])) {
        $URL .= '&panel=' . $Parameters['panel'];
    }

    return $URL;
}

/**
 * @desc Formats a money value, adding currency, commas and correct decimal places.
 */
function FormatMoneyVal($value, $escape = true, $showCurrencySymbol = true)
{
    if (!isset($value) || !is_numeric($value)) {
        return '';
    }

    if ($showCurrencySymbol) {
        if ($escape) {
            $currencySymbol = htmlspecialchars(GetParm('CURRENCY_CHAR', '£'), ENT_COMPAT | ENT_HTML401);
        } else {
            $currencySymbol = GetParm('CURRENCY_CHAR', '£');
        }
    } else {
        $currencySymbol = '';
    }

    if ($value >= 0) {
        $value = $currencySymbol . number_format($value, 2);
    } else {
        $value = '-' . $currencySymbol . number_format((float) ($value * -1), 2);
    }

    return $value;
}

/**
 * Re-formats a number value by emulating the specified picture format used by Gupta.
 *
 * Returns the reformatted value.
 *
 * @param float $Value number value to be re-formatted
 * @param string $format_pattern gupta picture format
 *
 * @return float the re-formatted value
 */
function GuptaFormatEmulate($Value, $format_pattern)
{
    $decimal_places = 0;
    $decimal_char = '';
    $split_format = explode('.', $format_pattern);

    if (count($split_format) > 1) {
        $decimal_places = \UnicodeString::strlen($split_format[1]);
        $decimal_char = '.';
    }

    if (\UnicodeString::stripos($format_pattern, ',')) {
        $commas = true;
    }

    return number_format((float) $Value, $decimal_places, $decimal_char, $commas ? ',' : '');
}
