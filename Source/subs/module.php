<?php

use app\models\generic\valueObjects\Module;
use app\services\licence\LicensedModuleManager;
use app\services\module\ModuleResolver;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Source\services\ModuleService;
use src\component\field\SelectFieldFactory;
use src\framework\registry\Registry;
use src\helpers\ModuleHelper;
use src\logger\DatixLogger;
use src\system\container\ContainerFactory;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

/**
 * @desc Turns a module id number into a three character module code (the inverse of {@link ModuleCodeToID()})
 *
 * @param int $mod_id The module id
 *
 * @return string the module code
 */
function ModuleIDToCode($mod_id)
{
    $ModuleIdCodeList = GetModuleIdCodeList();

    return $ModuleIdCodeList[$mod_id];
}

/**
 * @desc Turns a module code into a module id number (the inverse of {@link ModuleIDToCode()})
 *
 * @param string $mod_code The module code
 *
 * @return int the module id
 */
function ModuleCodeToID($mod_code)
{
    $ModuleCodeIdList = GetModuleCodeIdList();

    return $ModuleCodeIdList[$mod_code];
}

/**
 * @deprecated
 *
 * @see ModuleHelper::getModuleCodeIdList()
 *
 * @desc Gets a list of module ids, keyed by the codes (contrast with {@link GetModuleIdCodeList()}).
 * Includes a caching system to prevent huge numbers of queries.
 *
 * @return array<string, int> array of module ids
 */
function GetModuleCodeIdList(): array
{
    return Container::get(ModuleHelper::class)->getModuleCodeIdList();
}

/**
 * @deprecated use LicensedModuleManager::isModuleLicenced
 *
 * @desc Returns a boolean describing whether or not the given module is licensed.
 *
 * @param string $module the module to check
 *
 * @return bool true if the module is licensed, false otherwise
 */
function ModIsLicensed($module): bool
{
    return Container::get(LicensedModuleManager::class)->isModuleLicenced((string) $module);
}

/**
 * @param $module
 *
 * @deprecated use ModuleService->canSeeModule
 *
 * @return bool
 *
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 */
function CanSeeModule($module)
{
    return Container::get(ModuleService::class)->canSeeModule($module);
}

/**
 * Returns list of licensed modules and respective names.
 *
 * @param array $Exclude excludes this modules from the list (format: ['INC','COM'])
 * @param bool $groupModules TODO: document this
 *
 * @return array (ie: ['INC' => 'Incidents', 'CLA' => 'Claims', ...])
 */
function getModArray($Exclude = [], $groupModules = false)
{
    global $ModuleDefs;

    $modOrder = [];
    $modArray = [];

    $Listorder = array_flip(ModuleHelper::MODULE_LIST_ORDER);

    foreach ($ModuleDefs as $Module => $Details) {
        $isDummyModule = isset($Details['DUMMY_MODULE']) && $Details['DUMMY_MODULE'] === true;

        if (ModIsLicensed($Module) && !$isDummyModule && !in_array($Module, $Exclude)) {
            if ($groupModules && !empty($Details['MODULE_GROUP'])) {
                $Code = $Details['MODULE_GROUP'];
                $Name = GetModuleGroupName($Details['MODULE_GROUP']);

                // Grouped module should take list position of first module in group where possible
                if (!isset($Listorder[$Code])) {
                    if (isset($Listorder[$Details['CODE']])) {
                        $Listorder[$Code] = $Listorder[$Details['CODE']];
                    } else {
                        $Listorder[$Code] = max($Listorder) + 1;
                    }
                }
            } else {
                $Code = $Details['CODE'];
                $Name = $Details['NAME'];

                if (!isset($Listorder[$Code])) {
                    $Listorder[$Code] = max($Listorder) + 1;
                }
            }

            $modArray[$Code] = $Name;
            $modOrder[$Code] = $Listorder[$Code];
        }
    }

    array_multisort($modOrder, $modArray);

    return $modArray;
}

/**
 * Some modules are collected into groups. This function accesses the names for those groups.
 *
 * @param $moduleGroup the code for the module group being referenced (defined in AppVars)
 *
 * @return ?string the human readable name for the group of modules
 */
function GetModuleGroupName($moduleGroup): ?string
{
    $ModuleGroups = [];

    return $ModuleGroups[$moduleGroup] ?? null;
}

function getModuleDropdown(array $aParams)
{
    global $ModuleDefs;

    if (empty($aParams['not']) || !is_array($aParams)) {
        $aParams['not'] = [];
    }

    $ModArray = getModArray([], $aParams['includeModuleGroups'] ?? false);

    if (!empty($aParams['add']) && is_array($aParams['add'])) {
        $ModArray = array_merge($aParams['add'], $ModArray);
    }

    if (!array_key_exists($aParams['current'] ?? null, $ModArray)) {
        $aParams['current'] = '';
    }

    foreach ($ModArray as $code => $Name) {
        if (!empty($aParams['use_menu_names']) && !empty($ModuleDefs[$code]['MENU_NAME'])) {
            $Name = $ModuleDefs[$code]['MENU_NAME'];
        }

        if (in_array($code, $aParams['not'])) {
            unset($ModArray[$code]);
        }
    }

    if (!empty($aParams['include_choose'])) {
        $TempModArray[''] = _fdtk('choose');
        $ModArray = array_merge($TempModArray, $ModArray);
    }

    if ($aParams['name'] === 'parametersModule' && isset($ModArray['ADM'])) {
        $ModArray['ADM'] = _fdtk('profile_users');
    }

    $value = $aParams['current'] ?: array_keys($ModArray)[0];

    $field = SelectFieldFactory::createSelectField($aParams['name'], 'ADM', $value, '');
    $field->setCustomCodes($ModArray);

    if (!empty($aParams['onchange'])) {
        $field->setOnChangeExtra($aParams['onchange']);
    }
    $field->setAddChangedFlag($aParams['setChangedFlag'] ?? false);

    return $field->getField(false);
}

/**
 * Gets ID of current workflow.
 *
 * Very simple at the moment, but separated in case we need to add more layers here. If necessary, this can be
 * folded back into the main code body. Returns the appropriate workflow for the module, or 0 if this module
 * does not use workflows.
 *
 * @param string the module we are looking at
 *
 * @return int the ID of the current workflow
 *
 * @todo determine if this should be returning numeric-string or if uses of this function should be typecasting the return value
 */
function GetWorkflowID($module)
{
    $moduleDefs = Container::get(ModuleDefs::class);

    if (isset($moduleDefs[$module]['USE_WORKFLOWS'])) {
        return GetParm($moduleDefs[$module]['WORKFLOW_GLOBAL'], $moduleDefs[$module]['DEFAULT_WORKFLOW']);
    }

    return 0;
}

/**
 * @desc Function that returns the modules to exclude for Actions and Action Chains
 *
 * @param string $module actions/action_chains
 *
 * @return array $ExcludeArray Array of modules that doesn't allow creation of Actions or Action Chains
 */
function getExcludeArray($module)
{
    global $ModuleDefs;

    switch ($module) {
        case 'actions':
            $IncludeArray = $ModuleDefs['ACT']['CAN_LINK_ACTIONS'];

            break;
        case 'action_chains':
            $IncludeArray = $ModuleDefs['ACT']['CAN_LINK_ACTIONS_CHAINS'];

            break;
        case 'action_plans':
            $IncludeArray = $ModuleDefs['ACT']['CAN_LINK_ACTION_PLANS'];

            break;
    }

    $ExcludeArray = [];
    $AllModules = getModArray();

    foreach ($AllModules as $ModKey => $ModLabel) {
        if (!in_array($ModKey, $IncludeArray)) {
            $ExcludeArray[] = $ModKey;
        }
    }

    return $ExcludeArray;
}

/**
 * @param string $table
 *
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 *
 * @deprecated Use ModuleResolver::class)->getModuleFromTable($table) instead
 */
function getModuleFromTable($table): ?string
{
    return Container::get(ModuleResolver::class)->getModuleFromTable($table);
}

/**
 * Returns the module id based on the module short name.
 *
 * @param string $ModShortName The module short name (e.g. 'INC')
 *
 * @return int $_SESSION["ModuleCache"][$ModShortName] Module id
 */
function GetModIDFromShortName($ModShortName)
{
    // TODO: Consider replacing this function with calls to GetModuleCodeIdList()
    $ModuleCodeIdList = GetModuleCodeIdList();

    return $ModuleCodeIdList[$ModShortName] ?? 0;
}

/**
 * @return array
 */
function getModuleDropdownCode(array $aParams)
{
    if (empty($aParams['not']) || !is_array($aParams)) {
        $aParams['not'] = [];
    }

    $modArray = getModArray([], $aParams['includeModuleGroups']);

    if (is_array($aParams['add'])) {
        $modArray = array_merge($aParams['add'], $modArray);
    }

    foreach ($modArray as $code => $name) {
        if (in_array($code, $aParams['not'], true)) {
            unset($modArray[$code]);
        }
    }

    return array_keys($modArray);
}

function addGlobalAccessLevels(string $name, string $value, string $module, string $title, string $order): void
{
    $GLOBALS['AccessLvlDefs'][$name][$value] = ['module' => $module, 'title' => $title, 'order' => $order];
}

function loadGenericModuleDefs(): void
{
    // do not remove $ModuleDefs, $registry it's used in the require_once
    global $ModuleDefs;
    $registry = Container::get(Registry::class);

    $genericModuleDir = __DIR__ . '/../generic_modules';
    $handler = opendir($genericModuleDir);
    if ($handler === false) {
        /** @var DatixLogger $logger */
        $logger = (new ContainerFactory())->create()['logger'];
        $logger->error('Failed to open generic modules directory');

        return;
    }

    while ($subDir = readdir($handler)) {
        if (!in_array($subDir, ['.', '..'], true) && file_exists("{$genericModuleDir}/{$subDir}/AppVars.php")) {
            require_once "{$genericModuleDir}/{$subDir}/AppVars.php";
        }
    }
    closedir($handler);
}
