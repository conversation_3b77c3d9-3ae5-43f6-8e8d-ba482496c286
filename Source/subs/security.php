<?php

use src\logger\DatixLogger;
use src\search\helpers\AtCodesHelper;
use src\security\CompatEscaper;
use src\system\container\ContainerFactory;

/**
 * Function adds escape characters to a string depending on whether they are SQLServer ('') or SQLBase (\').
 *
 * @param string $InsertUpdate string to be altered
 *
 * @return string the $InsertUpdate sting with added quotes
 */
function EscapeQuotes($InsertUpdate)
{
    return str_replace("'", "''", $InsertUpdate ?? '');
}

/**
 * Function adds escape characters to each member of a Posted array depending on whether they are
 * SQLServer ('') or SQLBase (\').
 *
 * @param array $PostArray array to be altered
 * @param bool $GPC TRUE by default. Means that the values of php.ini settings magic_quotes_sybase
 *                  and magic_quotes_gpc are taken
 *                  into account when working out whether to insert extra quotes.  Set it to  FALSE if you are escaping
 *                  a string which has not come from a GET, POST or cookie.
 *
 * @return array the $PostArray array with added quotes
 */
function QuotePostArray($PostArray, $GPC = true)
{
    $QuotedArray = [];

    foreach ($PostArray as $Name => $Value) {
        $QuotedArray[$Name] = EscapeQuotes($Value, $GPC);
    }

    return $QuotedArray;
}

/**
 * Formats and surrounds a given value with quotes (if necessary).
 *
 * Used when building WHERE clauses e.g. when searching.
 *
 * @param string $value the data value
 * @param string $datatype the data type
 *
 * @return string $value     the value having been reformatted and quoted (if necessary)
 */
function QuoteData($value, $datatype)
{
    $strDataTypes = [
        'string',
        'textarea',
        'ff_select',
        'yesno',
        'grading',
        'combobox',
        'string_search',
        'multilistbox',
        'email',
        'time',
        'text',
        'duration',
    ];

    $datatimeDataTypes = ['date'];

    if (in_array($datatype, $strDataTypes)) {
        if (\UnicodeString::strlen($value) > 0) {
            $value = '\'' . str_replace(['*', '?'], ['%', '_'], $value) . '\'';
        } else {
            $value = '\'\'';
        }

        return $value;
    }

    if (in_array($datatype, $datatimeDataTypes)) {
        if ($value[0] == '@') {
            foreach (AtCodesHelper::VALID_CODES as $AtSign) {
                if (\UnicodeString::stripos($value, $AtSign) !== false) {
                    return '\'' . $value . '\'';
                }
            }
        }

        return '\'' . UserDateToSQLDate($value) . '\'';
    }

    return $value;
}

function CreatePermString($ModulePerms)
{
    /** @var DatixLogger $logger */
    $logger = (new ContainerFactory())->create()['logger'];

    if (empty($ModulePerms) || !is_array($ModulePerms)) {
        return '';
    }
    $logger->info('CreatePermString() $ModulePerms: ' . json_encode($ModulePerms));

    $PermString = '';
    $PermArray = [];

    $Modules = GetModuleCodeIdList();
    $logger->info('CreatePermString() $Modules: ' . json_encode($Modules));

    $PermNos = [
        'ALL' => 0,
        'READ' => 1,
        'WRITE' => 2,
        'NONE' => 3,
    ];

    foreach ($ModulePerms as $ModStr => $ModPerms) {
        if (empty($ModPerms) || !is_array($ModPerms)) {
            continue;
        }

        if (isset($ModPerms['form']) && is_array($ModPerms['form'])) {
            foreach ($ModPerms['form'] as $SubformId => $PermValue) {
                $PermArray[] = ($Modules[$ModStr] * 256 + $SubformId) . ';' . $PermNos[$PermValue];
            }
        }

        if (isset($ModPerms['disallow_setup']) && $ModPerms['disallow_setup'] = true) {
            $PermArray[] = ($Modules[$ModStr] * 256 + SUB_SETUP) . ';' . $PermNos[$PermValue];
        }

        if (isset($ModPerms['secgroup']) && $ModPerms['secgroup'] != ''
            || isset($ModPerms['seclevel']) && $ModPerms['seclevel'] != ''
            || isset($ModPerms['where']) && $ModPerms['where'] != '') {
            $PermArray[] =
                ($Modules[$ModStr] * 256 + SUB_WHERE) .
                ';' . $ModPerms['secgroup'] .
                ';' . $ModPerms['seclevel'] .
                ';' . $ModPerms['where'];
        }
    }
    $logger->info('CreatePermString() $PermArray: ' . json_encode($PermArray));

    if (empty($PermArray)) {
        return '';
    }

    return implode('|', $PermArray);
}

/**
 * not an ideal function to have to use, but it gets rid of a lot of weird characters
 * produced by copying from word into fields.
 * function copied from php.net comments.
 * FIXME: Get rid of this function and inline it where necessary.
 *
 * @deprecated
 */
function htmlfriendly($var)
{
    return CompatEscaper::encodeCharacters(stripslashes($var));
}
