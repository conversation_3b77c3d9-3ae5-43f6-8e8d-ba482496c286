<?php

use src\framework\session\UserSessionFactory;

/**
 * @desc Makes an entry in the full_audit log, but only if FULL_AUDIT_OPEN is Y. Used to log when a record was opened
 *
 * @param string $Module Code of the current module
 * @param int $RecordID Recordid of the current record
 * @param string $Comments Comments to include in the audit
 */
function AuditOpenRecord($Module, $RecordID, $Comments)
{
    if (bYN(GetParm('FULL_AUDIT_OPEN', 'N'))) {
        InsertFullAuditRecord($Module, $RecordID, 'OPEN', $Comments);
    }
}

/**
 * @desc Inserts an entry into the full_audit table, logging an action, a timestamp and the initials
 * of the logged in user.
 *
 * @param string $Module Code of the current module
 * @param int $RecordID Recordid of the current record
 * @param string $Action Code describing what is being audited
 * @param string $Comments Comments to include in the audit
 * @param bool $hideUsersNameFromSubmission
 */
function InsertFullAuditRecord($Module, $RecordID, $Action, $Comments, $hideUsersNameFromSubmission = false, bool $includeWebIp = true): void
{
    $AuditDate = date('Y-m-d H:i:s');

    $loggedInUserInitials = (new UserSessionFactory())->create()->getInitials();

    if ($includeWebIp) {
        $Comments = 'WEB ' . $_SERVER['REMOTE_ADDR'] . ' ' . $Comments;
    }

    $InsertArray = [
        'aud_module' => $Module,
        'aud_record' => $RecordID,
        'aud_login' => $hideUsersNameFromSubmission ? '' : $loggedInUserInitials,
        'aud_date' => $AuditDate,
        'aud_action' => $Action,
        'aud_detail' => $Comments,
    ];

    DatixDBQuery::PDO_build_and_insert('full_audit', $InsertArray);
}
