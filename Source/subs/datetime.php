<?php

use app\models\datetime\DateTimeGeneratorFactory;
use app\models\generic\valueObjects\Module;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\helpers\DateHelper;
use src\system\container\facade\Container;

/**
 * @desc Takes a date string (separated by . / or - and converts it into SQL Server format. Also zeros the time.
 *
 * @param string $datestring The date in a string format
 *
 * @return string the date in SQL Server format
 *
 * @throws InvalidDataException
 */
function UserDateToSQLDate($datestring)
{
    $US = Container::get(Registry::class)->getParm('FMT_DATE_WEB')->is('US');

    $date = DateHelper::toSQLDate($datestring, $US);
    if ($date instanceof DateTime) {
        return $date->format('Y-m-d H:i:s') . '.000';
    }

    return $date;
}

/**
 * This function should only be used to convert from a storage format to a display format.
 *
 * @param string $sqlDate
 * @param bool $returnTime
 *
 * @return string
 *
 * @throws InvalidDataException
 */
function formatDateForDisplay($sqlDate, $returnTime = false, $useUtcOffset = false)
{
    if ($sqlDate == '') {
        return '';
    }

    if (preg_match('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(\.\d{3})?/', $sqlDate) !== 1) {
        // This is to handle situations where the date comes in in a non-ISO format. this can be removed once we
        // are handling dates consistently inside the system
        $sqlDate = UserDateToSQLDate($sqlDate);

        if (preg_match('/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(\.\d{3})?/', $sqlDate) !== 1) {
            // We want to use this but it causes too many errors, so we'll need to replace it during development of a future version:
            throw new InvalidDataException('Date provided is not in a valid format');
        }
    }

    if (Container::get(Registry::class)->getParm('FMT_DATE_WEB')->is('US')) {
        $format = 'm/d/Y';
    } else {
        $format = 'd/m/Y';
    }

    if ($returnTime) {
        $format .= ' H:i:s';
    }

    if ($useUtcOffset) {
        return (new DateTimeGeneratorFactory())->create()->getCurrentDateTimeAtSystem(new DateTime($sqlDate))->format($format);
    }

    return (new DateTime($sqlDate))->format($format);
}

/**
 * Returns true if the date sting matches the format yyyy-mm-dd.
 *
 * @deprecated use build in DateTime object instead
 *
 * @param $date string The date string to check
 */
function validStringDate($date): bool
{
    return DateTime::createFromFormat('Y-m-d', $date) !== false;
}

/**
 * Calculates the number of calendar days that exist within a span of working days.
 *
 * e.g. how many calendar days exist within the span of today + 10 working days.
 *
 * @param int $numDays the number of working days
 * @param DateTime $from the start date
 * @param bool $reverse whether we're counting backwards from the start date
 *
 * @return int $totalDays the number of calendar days
 */
function CalculateWorkingDays($numDays, ?DateTime $from = null, $reverse = false)
{
    $totalDays = 0;
    $workingDays = 0;

    if ($from === null) {
        $currentDate = new DateTime();
    } else {
        $currentDate = clone $from;
    }

    // TODO the condition below will need to change to deal with
    //      countries where the weekend is not Sat/Sun (e.g. Saudi)
    if ($reverse && in_array($currentDate->format('w'), range(1, 5))) { // mon-fri
        // count the from date as the first working day if we're counting backwards
        $workingDays = 1;
    }

    while ($workingDays < $numDays) {
        $currentDate->modify(($reverse ? '-' : '+') . '1 day');

        if (!isHoliday($currentDate)) {
            ++$workingDays;
        }

        ++$totalDays;
    }

    return $totalDays;
}

/**
 * Determines whether the provided date is a weekend or a public holiday.
 *
 * @param DateTime $date the date we're interested in
 * @param Registry $registry the application registry, used to retrieve holiday definitions
 *
 * @return bool
 */
function isHoliday(DateTime $date, ?Registry $registry = null)
{
    if (in_array($date->format('w'), [6, 0])) { // sat/sun
        // TODO this will need to take locale into account to deal with
        //      countries where the weekend is not Sat/Sun (e.g. Saudi)
        return true;
    }

    $registry ??= Container::get(Registry::class);
    $holidays = $registry->getHolidays();

    return in_array($date->format('Y-m-d 00:00:00.000'), $holidays);
}

function ValidatePostedDates($module, $suffix = '', $conlink = false)
{
    global $FieldDefs;

    $registry = Container::get(Registry::class);

    $suffixText = '';
    if ($suffix) {
        $suffixText = '_' . $suffix;
    }

    $Dates = GetAllFieldsByType($module, 'date');

    // this is an unapologetic hack until we can put the link details into the 'CON' area of fielddefs.
    if ($conlink) {
        $LinkedDates = GetAllFieldsByType('INC', 'date');

        foreach ($LinkedDates as $id => $Date) {
            if (\UnicodeString::substr($Date, 0, 5) != 'link_') {
                unset($LinkedDates[$id]);
            }
        }
    }

    $Dates = SafelyMergeArrays([$Dates, $LinkedDates]);

    $dateTimeGenerator = (new DateTimeGeneratorFactory())->create();

    // The date needs to either be validated the server timezone and making sure the same timezone is used for the date field
    // or if DETECT_TIMEZONES is set the timezone needs to be UTC and the current date spoofed so it has the clients timezone
    // offset added it to it so that validation works for dates in the future of UTC current date
    if ($registry->getParm('DETECT_TIMEZONES', 'N')->isTrue()) {
        $currentDate = $dateTimeGenerator->getCurrentDateTimeAtClient();
        $timezone = $currentDate->getTimezone();
    } else {
        $currentDate = new DateTime('now');
        $timezone = $currentDate->getTimezone();
    }

    $request = new Request();
    $changedLcomDreceived = $request->getParameter('CHANGED-lcom_dreceived');
    $chainDatesAreEditable = $registry->getParm('EDITABLE_CHAIN_DUE_DATES', 'Y') === 'Y';

    $error = [];
    foreach ($Dates as $Field) {
        // this is an unapologetic hack until we can put the link details into the 'CON' area of fielddefs.
        $mod = $module;

        if ($conlink && \UnicodeString::substr($Field, 0, 5) == 'link_') {
            $mod = Module::INCIDENTS;
        }

        $fieldDef = $FieldDefs[$mod][$Field];

        // date cannot be in the future.
        if (isset($fieldDef['NotFuture']) && !empty($_POST[$Field . $suffixText])) {
            $date = $dateTimeGenerator->getDateTime(
                UserDateToSQLDate($_POST[$Field . $suffixText]),
                $timezone,
            )->setTime(0, 0);

            if ($currentDate < $date) {
                $error[$Field . $suffixText] = _fdtk('date_later_today');
            }
        }

        if (is_array($fieldDef['NotEarlierThan'])) {
            foreach ($fieldDef['NotEarlierThan'] as $earlyDateField) {
                if ($earlyDateField === 'lcom_dreceived'
                    && $changedLcomDreceived
                    && !$chainDatesAreEditable) {
                    continue;
                }
                // Check closed date not before open date
                if (($_POST[$earlyDateField . $suffixText] ?? null) != '' && ($_POST[$Field . $suffixText] ?? null) != '') {
                    $earlyDate = new DateTime(UserDateToSQLDate($_POST[$earlyDateField . $suffixText]));
                    $checkDate = new DateTime(UserDateToSQLDate($_POST[$Field . $suffixText]));

                    if ($checkDate < $earlyDate) {
                        $error[$Field . $suffixText] = _fdtk('date_earlier_than') . ' ' . Labels_FormLabel::GetFormFieldLabel($earlyDateField, '', $mod);
                        $error[$earlyDateField . $suffixText] = _fdtk('date_cannot_be_later') . ' ' . Labels_FormLabel::GetFormFieldLabel($Field, '', $mod);
                    }
                }
            }
        }
    }

    return $error;
}

function ValidatePostedTimes($module, $suffix = '')
{
    global $FieldDefs;

    if ($suffix) {
        $SuffixText = '_' . $suffix;
    }

    $error = [];

    $Times = GetAllFieldsByType($module, 'time');

    foreach ($Times as $Field) {
        if ($FieldDefs[$module][$Field]['NotFuture']) {
            // time cannot be in the future.
            if ($_POST[$Field . $SuffixText] != '') {
                $Time = str_replace(':', '', $_POST[$Field . $SuffixText]);
                $CurrentTime = date('Hi', time());

                if ((int) $Time > (int) $CurrentTime) {
                    $error[$Field . $SuffixText] = _fdtk('time_cannot_be_later');
                }
            }
        }

        if (is_array($FieldDefs[$module][$Field]['NotEarlierThan'])) {
            foreach ($FieldDefs[$module][$Field]['NotEarlierThan'] as $EarlyTimeField) {
                // Check time not before another time
                if ($_POST[$EarlyTimeField . $SuffixText] != '' && $_POST[$Field . $SuffixText] != '') {
                    $EarlyTime = str_replace(':', '', $_POST[$EarlyTimeField . $SuffixText]);
                    $CheckTime = str_replace(':', '', $_POST[$Field . $SuffixText]);

                    if ((int) $EarlyTime > (int) $CheckTime) {
                        $error[$Field . $SuffixText] = _fdtk('time_cannot_be_earlier') . ' ' . Labels_FormLabel::GetFormFieldLabel($EarlyTimeField, '', $module);
                    }
                }
            }
        }
    }

    return $error;
}

/**
 * Returns a SQL Server-formatted string representing today's date in the current timezone.
 *
 * @param bool $withoutTime whether or not to omit the hours/mins/secs
 *
 * @return string
 */
function GetTodaysDate($withoutTime = false)
{
    $dateTimeGenerator = (new DateTimeGeneratorFactory())->create();
    $today = $dateTimeGenerator->getCurrentDateTimeAtClient();

    $format = $withoutTime ? 'Y-m-d 00:00:00.000' : 'Y-m-d H:i:s.000';

    return $today->format($format);
}

/**
 * @deprecated no replace do not use, need standardize date format in db.php MakeFieldWhere with the reset if the app.
 *
 * @see DateSearchableTrait
 *
 * @todo should be removed once IQ-41816 has been completed.
 */
function correctDateValueForSearch(string $value): string
{
    // checks for sql operator or @ code
    if (in_array($value[0], ['@', '>', '<', '!', '='], true)) {
        return $value;
    }

    $dates = array_map('trim', explode(':', $value, 2));
    $format = Container::get(Registry::class)->getParm('FMT_DATE_WEB', 'GB')->is('US') ? 'm/d/Y' : 'd/m/Y';

    $values = [];
    foreach ($dates as $date) {
        $dateTime = DateTimeImmutable::createFromFormat($format, $date);
        if ($dateTime === false) {
            // FIXME Dates can come in from level 2 contact search in this format. Larger fix will be handled as a part of IQ-41816
            $dateTime = DateTimeImmutable::createFromFormat('Y-m-d', $date);
        }
        if ($dateTime !== false) {
            $values[] = $dateTime->format('Y-m-d');
        }
    }

    return implode(':', $values);
}
