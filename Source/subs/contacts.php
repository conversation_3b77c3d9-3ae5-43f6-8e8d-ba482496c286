<?php

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

function GetContactLinkValues($recordid)
{
    $con['linkedincnum'] =
        DatixDBQuery::PDO_fetch(
            '
            SELECT
                COUNT(incidents_main.recordid)
            FROM
                incidents_main
            INNER JOIN
                link_contacts ON incidents_main.recordid = link_contacts.inc_id
            RIGHT OUTER JOIN
                contacts_main ON link_contacts.con_id = contacts_main.recordid
            WHERE
                contacts_main.recordid = :recordid',
            ['recordid' => $recordid],
            PDO::FETCH_COLUMN,
        );

    $con['linkedcomnum'] =
        DatixDBQuery::PDO_fetch(
            '
            SELECT
                COUNT(compl_main.recordid)
            FROM
                compl_main
            INNER JOIN
                link_contacts ON compl_main.recordid = link_contacts.com_id
            RIGHT OUTER JOIN
                contacts_main ON link_contacts.con_id = contacts_main.recordid
            WHERE
                contacts_main.recordid = :recordid',
            ['recordid' => $recordid],
            PDO::FETCH_COLUMN,
        );


    $con['linkedclanum'] =
        DatixDBQuery::PDO_fetch(
            '
            SELECT
                COUNT(claims_main.recordid)
            FROM
                claims_main
            INNER JOIN
                link_contacts ON claims_main.recordid = link_contacts.cla_id
            RIGHT OUTER JOIN
                contacts_main ON link_contacts.con_id = contacts_main.recordid
            WHERE
                contacts_main.recordid = :recordid',
            ['recordid' => $recordid],
            PDO::FETCH_COLUMN,
        );

    return $con;
}

function StoreContactLabels()
{
    // Store contact labels in session for later use in dropdown popups
    if (!is_array($_SESSION['CONTACTLABELS'])) {
        $_SESSION['CONTACTLABELS'] = [];
    }

    if (is_array($GLOBALS['UserLabels'])) {
        $_SESSION['CONTACTLABELS'] = array_merge($_SESSION['CONTACTLABELS'], $GLOBALS['UserLabels']);
    }
}

function getContactLinkFields($link_type = '', $calculatedFields = false)
{
    global $FieldDefs;
    $ModuleDefs = Container::get(ModuleDefs::class);

    if ($link_type == 'O') { // individual respondents
        if ($calculatedFields) {
            $tempArray = [];
            foreach ($ModuleDefs['CON']['LINKED_FIELD_ARRAY_RESPONDENT'] as $field) {
                if (!$FieldDefs['CON'][$field]['CalculatedField']) {
                    $tempArray[] = $field;
                }
            }

            return $tempArray;
        }

        return $ModuleDefs['CON']['LINKED_FIELD_ARRAY_RESPONDENT'];
    }

    return $ModuleDefs['CON']['LINKED_FIELD_ARRAY'];
}

/**
 * @desc Return an array of contacts linked to a given record with approval status "Unapproved". Used to decide
 * whether to return to the record after saving or not. Filtered by current user's access permissions.
 *
 * @param array $aParams Array of parameters
 *
 * @return array list of unapproved contacts attached to the record
 */
function GetUnapprovedContacts($aParams)
{
    global $ModuleDefs;

    $conPermWhere = MakeSecurityWhereClause('', 'CON', $_SESSION['initials']);

    $sql = '
        SELECT
            link_contacts.link_type, contacts_main.con_title, contacts_main.con_forenames, contacts_main.con_surname
        FROM
            link_contacts, contacts_main
        WHERE
            link_contacts.' . $ModuleDefs[$aParams['module']]['FK'] . ' = ' . $aParams['recordid'] . '
            AND link_contacts.con_id = contacts_main.recordid
            AND contacts_main.rep_approved = \'UN\'
    ' . ($conPermWhere ? ' AND (' . $conPermWhere . ')' : '');

    return DatixDBQuery::PDO_fetch_all($sql);
}

/**
 * @desc Returns an array of all contacts linked to a particular record, keyed by their link_type.
 *
 * @param array $aParams Array of parameters
 * @param bool $overrideSecurity overrides the security applied to the where clause if we are coming from a logged out form
 *
 * @return array Array of contact data keyed by link_type
 */
function GetLinkedContacts($aParams, $overrideSecurity = false)
{
    $ModuleDefs = Container::get(ModuleDefs::class);

    $accessViaCascadePermissions = Container::get(Registry::class)->getParm('CASCADE_PERMISSIONS', 'N')->isTrue() && $aParams['module'] == 'INC';
    // If user doesn't have access to the module he should not see any data
    if ($overrideSecurity === false && !CanSeeModule('CON') && !$accessViaCascadePermissions) {
        return [];
    }

    $recordid = $aParams['recordid'];
    $module = $aParams['module'];
    $formlevel = $aParams['formlevel'];

    // If recordid is empty (eg. on a search form) then don't try and get any linked records as this will give an error
    if ($recordid == '') {
        return [];
    }

    //  This section populates $con, which will be passed on holding the contacts info

    $sqlArray = ['contacts_main.recordid as recordid', 'contacts_main.recordid as con_id', 'link_contacts.link_recordid as link_recordid',
        'contacts_main.updateid as updateid', $ModuleDefs[$module]['FK'], ];

    foreach ($ModuleDefs['CON']['FIELD_ARRAY'] as $ConField) {
        $sqlArray[] = 'contacts_main.' . $ConField . ' as ' . $ConField;
    }

    foreach (getContactLinkFields() as $ConField) {
        $sqlArray[] = 'link_contacts.' . $ConField . ' as ' . $ConField;
    }

    $sql = 'SELECT ' . implode(' , ', $sqlArray) . '
        FROM link_contacts, contacts_main
        WHERE link_contacts.con_id = contacts_main.recordid
        AND ' . $ModuleDefs[$module]['FK'] . ' = ' . $recordid;

    // Apply security where clause to linked contacts so that users see the appropriate linked data
    if ($overrideSecurity === false) {
        $loggedInUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;
        $securityWhere = MakeSecurityWhereClause('', 'CON', $loggedInUserInitials);
    }

    if ($securityWhere != '') {
        $sql .= ' AND (' . $securityWhere . ')';
    }

    $sql .= ' ORDER BY contacts_main.recordid asc';

    $request = db_query($sql);
    $con = [];

    while ($con1 = db_fetch_array($request)) {
        if ($module == 'INC') {
            $con1 = GetLinkedInjuries(['data' => $con1]);
        } elseif ($module == 'CLA') {
            $sql = 'SELECT injury, bodypart, death_result_injury, permanent_impairment_percentage, listorder
                FROM link_injuries
                WHERE link_id = :link_id
                ORDER BY listorder ASC';

            $injuries = DatixDBQuery::PDO_fetch_all($sql, ['link_id' => $con1['link_recordid']]);

            foreach ($injuries as $injury) {
                $con1['injury_table'][$injury['listorder']] = $injury;
            }

            if (!empty($con1['injury_table'])) {
                $con1['show_injury'] = 'Y';
            }
        }

        if ($formlevel == 1) { // to split into 'fake' reporter section
            if ($con1['link_type'] == 'N' && $con1['link_role'] == GetParm('REPORTER_ROLE', 'REP')) {
                $con1['link_type'] = 'R';
            }

            // Fake witness section
            if ($con1['link_type'] == 'N' && $con1['link_role'] == 'WITN') {
                $con1['link_type'] = 'W';
            }

            // Fake assailant section
            if ($con1['link_type'] == 'N' && $con1['link_role'] == 'PERP') {
                $con1['link_type'] = 'L';
            }
        }

        // put spaces between the nhs number value
        if (strlen($con1['con_nhsno']) == 10) {
            $con1['con_nhsno'] = substr($con1['con_nhsno'], 0, 3) . ' ' . substr($con1['con_nhsno'], 3, 3) . ' ' . substr($con1['con_nhsno'], 6, 4);
        }

        $con1['link_exists'] = true;

        // If property damage is set to Y, get the property for the contact on the record
        if ($con1['link_pprop_damaged'] == 'Y') {
            // Check for property information in inc_personal_property
            $sql = 'SELECT recordid, ipp_description, ipp_damage_type, ipp_value, listorder,
                con_id, inc_id
                FROM inc_personal_property
                WHERE con_id = :con_id
                AND inc_id = :inc_id
                ORDER BY listorder ASC';

            $rows = DatixDBQuery::PDO_fetch_all($sql, ['con_id' => $con1['recordid'], 'inc_id' => $recordid]);

            foreach ($rows as $row) {
                $con1['property_table'][$row['listorder']] = $row;
            }
        }

        $con[$con1['link_type']][] = array_merge($con1, getComplLinkData($con1['recordid']));
    }

    if (in_array($aParams['module'], [Module::CLAIMS, Module::REDRESS], true)) {
        // Get individual respondents
        $sqlArray = ['contacts_main.recordid as recordid', 'contacts_main.recordid as con_id',
            'link_respondents.recordid as link_recordid', 'contacts_main.updateid as updateid', ];
        foreach ($ModuleDefs['CON']['FIELD_ARRAY'] as $ConField) {
            $sqlArray[] = 'contacts_main.' . $ConField . ' as ' . $ConField;
        }
        foreach (['link_role', 'link_type', 'link_notes', 'link_resp'] as $ConField) {
            $sqlArray[] = 'link_respondents.' . $ConField . ' as ' . $ConField;
        }
        $sql = 'SELECT ' . implode(' , ', $sqlArray) . '
        FROM link_respondents, contacts_main
        WHERE link_respondents.con_id = contacts_main.recordid
        AND link_respondents.main_recordid = :main_recordid
        AND link_respondents.main_module = :main_module';

        $result = DatixDBQuery::PDO_fetch_all($sql, ['main_recordid' => $recordid, 'main_module' => $aParams['module']]);

        foreach ($result as $respondent) {
            $con['O'][] = $respondent;
        }

        // Get organisations
        $sqlArray = ['organisations_main.recordid as recordid', 'organisations_main.recordid as org_id',
            'link_respondents.recordid as link_recordid', 'organisations_main.updateid as updateid', ];
        foreach ($ModuleDefs['ORG']['FIELD_ARRAY'] as $OrgField) {
            $sqlArray[] = 'organisations_main.' . $OrgField . ' as ' . $OrgField;
        }
        foreach (['link_role', 'link_type', 'link_notes', 'link_resp'] as $OrgField) {
            $sqlArray[] = 'link_respondents.' . $OrgField . ' as ' . $OrgField;
        }
        $sql = 'SELECT ' . implode(' , ', $sqlArray) . '
        FROM link_respondents, organisations_main
        WHERE link_respondents.org_id = organisations_main.recordid
        AND link_respondents.main_recordid = :main_recordid
        AND link_respondents.main_module = :main_module';

        $result = DatixDBQuery::PDO_fetch_all($sql, ['main_recordid' => $recordid, 'main_module' => $aParams['module']]);

        foreach ($result as $respondent) {
            $con['G'][] = $respondent;
        }
    }

    return $con;
}

/**
 * Validates contact form data.
 *
 * Calls various validation functions which each in turn validate a specific field/field type
 * and returns any error messages generated.
 *
 * @return array $error any error messages generated from the validation checks
 */
function ValidateContactData($aParams)
{
    $DateError = ValidatePostedDates('CON', $aParams['suffix'], true);
    $MoneyError = ValidatePostedMoney('CON', $aParams['suffix']);

    $error = array_merge($DateError, $MoneyError);
    $error = ValidateNHSNumber($aParams, $error);

    return validateSSN($aParams, $error);
}

function autoApproveContact(&$ContactFromUser)
{
    global $FieldDefs;

    $sql = 'SELECT recordid, con_number, con_nhsno, con_police_number,
        con_surname, con_title, con_forenames,  con_gender, con_dob,
        con_dod, con_line1, con_line2, con_line3, con_city, con_county,
        con_country,con_postcode, con_subtype, con_type,
        con_tel1, con_tel2, con_email,
        con_ethnicity, con_disability, con_language, con_name,
        location_id, service_id,
        con_notes, con_remote_id, con_empl_grade
        FROM contacts_main
        WHERE con_number = :con_number
        AND con_surname = :con_surname
        AND rep_approved = :rep_approved
        ';

    $ContactFromDB = DatixDBQuery::PDO_fetch_all($sql, [
        'con_number' => $ContactFromUser['con_number'],
        'con_surname' => $ContactFromUser['con_surname'],
        'rep_approved' => 'FA',
    ]);

    if (!count($ContactFromDB) == 1) { // only auto-match if there is a single contact record returned.
        return false;
    }

    $ContactFromDB = $ContactFromDB[0];


    foreach ($ContactFromDB as $key => $val) {
        if (!is_numeric($key)) {
            if ($FieldDefs['CON'][$key]['Type'] == 'date') {
                if ($val == null) {
                    $ContactFromDB[$key] = 'NULL';
                } else {
                    $val = formatDateForDisplay($val);
                }
            }

            if ($ContactFromUser[$key] != '' && strtolower($ContactFromUser[$key]) != strtolower($val) && $val != '') {
                return false;
            }

            if ($ContactFromUser[$key] == '' && $val != '') {
                // non-conflicting data from DB - we don't actually need to re-save this.
                if ($key == 'recordid') {
                    $ContactToReturn[$key] = $ContactFromDB[$key];
                }
            } elseif ($ContactFromUser[$key] != '' && $val == '') {
                if ($FieldDefs['CON'][$key]['Type'] == 'date') {
                    $ContactToReturn[$key] = UserDateToSQLDate($ContactFromUser[$key]);
                } else {
                    $ContactToReturn[$key] = $ContactFromUser[$key];
                }
            } elseif ($ContactFromUser[$key] == $val) {
                $ContactToReturn[$key] = $ContactFromUser[$key];
            }
        }
    }

    // can't just edit contactfromuser throughout in case it turns out not to match after it has been changed.
    $ContactFromUser = $ContactToReturn;

    $ContactFromUser['rep_approved'] = 'FA';
}

function ContactHidden($Data, $Suffix, $Module = 'INC')
{
    global $SectionVisibility, $ModuleDefs;

    if ($Suffix >= 6) {
        return !$SectionVisibility[$Data['contact_div_name_' . $Suffix]];
    }

    // loop through basicform - only ever needed for level1 forms, however the $level variable needs to be set
    $level = 1;
    include GetBasicFormFileName($Module, $level);

    foreach ($FormArray as $section => $details) {
        if (($details['Special'] ?? null) == 'DynamicContact' && $details['suffix'] && $details['suffix'] == $Suffix) {
            return !$SectionVisibility[$section];
        }
    }

    return false;
}

/**
 * @desc Checks whether the user should be prompted about unapproved contacts, based
 * on the current and previous values of rep_approved
 */
function CheckUnapprovedContactRedirect($Parameters)
{
    $sql = '
        SELECT
            apac_check_contacts
        FROM
            approval_action
        WHERE
            apac_from = :from
            AND apac_to = :to
            AND module = :module
            AND access_level = :access_level
    ';

    $CheckContacts = DatixDBQuery::PDO_fetch($sql, [
        'from' => $Parameters['from'],
        'to' => $Parameters['to'],
        'module' => $Parameters['module'],
        'access_level' => $Parameters['access_level'],
    ], PDO::FETCH_COLUMN);

    return $CheckContacts == 'Y';
}
