<?php


/**
 * @deprecated Please use services\RecordLocks
 *
 * @desc Checks whether a given record is locked or not by trying to lock it.
 * Updates FormType and sLockMessage parameters with results.
 *
 * @param string $module The module we are checking
 * @param array $data The record data
 * @param string $FormType The current form state (e.g. New, Edit, ReadOnly, Search, Design, Print...)
 * @param string $sLockMessage the message to display at the top of the form
 */
function CheckForRecordLocks($module, $data, &$FormType, &$sLockMessage)
{
    global $ModuleDefs;

    if ($data['recordid'] && $FormType != 'Locked' && $FormType != 'ReadOnly' && $FormType != 'Print'
        && $FormType != 'Search' && $FormType != 'Design') {
        $aReturn = LockRecord([
            'formtype' => $FormType,
            'link_id' => $data['recordid'],
            'table' => $ModuleDefs[$module]['TABLE'] ?? '',
        ]);

        $sLockMessage = $aReturn['lock_message'] ?? null;

        if ($aReturn['formtype']) {
            $FormType = $aReturn['formtype'];
        }
    }
}

function UnlockLinkedRecords($aParams)
{
    global $ModuleDefs;

    if ($aParams['main']['recordid'] && $aParams['link']['recordid']) {
        UnlockRecord([
            'table' => $ModuleDefs[$aParams['link']['module']]['TABLE'],
            'link_id' => $aParams['link']['recordid'],
        ]);

        UnlockRecord([
            'table' => $ModuleDefs[$aParams['main']['module']]['TABLE'],
            'link_id' => $aParams['main']['recordid'],
        ]);
    }
}

/**
 * @desc Generic version of UnlockRecord - takes just the module and recordid and calls
 * the unlock record stored procedure
 *
 * @param string $module The current module
 * @param int $recordid The ID of the record to be unlocked
 */
function Unlock($module, $recordid)
{
    global $ModuleDefs;

    if ($recordid) {
        UnlockRecord(['table' => $ModuleDefs[$module]['TABLE'], 'link_id' => $recordid]);
    }
}
