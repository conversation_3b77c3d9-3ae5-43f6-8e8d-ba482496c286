<?php

use app\models\globals\ProgNotesEditGlobal;
use src\safeguarding\models\SafeguardingAdminFields;

function getConfigurationParameters()
{
    $mainArray = [
        // Actions
        'ACT_OWN_ONLY',
        'ACT_LISTING_ID',
        'ACT_SEARCH_DEFAULT',
        'ACT_SETUP',
        'DELETE_ACT_CHAIN',

        // All modules
        'LOGOUT_DEFAULT_MODULE',
        'CUSTOM_REPORT_BUILDER',
        ProgNotesEditGlobal::GLOBAL_NAME,
        'ENABLE_GENERATE',
        'ENABLE_BATCH_UPDATE',
        'AUTOPOPULATE_USER_LOCATION',

        // Admin
        'FULL_ADMIN',
        'ADM_DEFAULT',
        'ADM_LISTING_ID',
        'ADM_PROFILES',
        'ADM_GROUP_SETUP',
        'ADM_CAN_DELEGATE_PERMISSIONS',
        'ADM_CAN_DELEGATE_ACCOUNTS',
        'ADM_NO_ADMIN_REPORTS',

        // Contacts
        'CON_SHOW_REJECT_BTN',
        'CON_SETUP',
        'CON_ALLOW_MERGE_DUPLICATES',

        // Equipment/Assets
        'AST_SETUP',
        'AST_DELETE_DOCS',

        // Medications
        'MED_LISTING_ID',
        'MED_SETUP',


        // COM
        'COM1_DEFAULT',
        'COM2_DEFAULT',
        'COM_LISTING_ID',
        'COM2_SEARCH_DEFAULT',
        'COM_OWN_ONLY',
        'COM_SHOW_AUDIT',
        'COM_SETUP',
        'COM_DELETE_DOCS',
        'COM_SHOW_ADD_NEW',
        'COM_KO41_CAN_GENERATE_REPORT',
        'COM_SAVED_QUERIES_HOME_SCREEN',
        'COM_SAVED_QUERIES',
        'COM_CAN_DELETE_OWN_DOCUMENTS',
        'COM_SHOW_REJECT_BTN',
        'SPSC_WEB_PORTAL_API_URL',
        'SPSC_WEB_PORTAL_API_USERNAME',
        'SPSC_WEB_PORTAL_API_PASSWORD',
        'SPSC_PATIENT_APP_API_URL',
        'SPSC_PATIENT_APP_API_USERNAME',
        'SPSC_PATIENT_APP_API_PASSWORD',
        'SHOW_REOPEN',
        'EDITABLE_CHAIN_DUE_DATES',
        'COPY_COM',

        // Claims
        'CLAIM1_DEFAULT',
        'CLAIM2_DEFAULT',
        'CLA_LISTING_ID',
        'CLAIM2_SEARCH_DEFAULT',
        'CLA_OWN_ONLY',
        'CLA_SHOW_AUDIT',
        'CLA_SETUP',
        'CLA_DELETE_DOCS',
        'CLA_SAVED_QUERIES_HOME_SCREEN',
        'CLA_SAVED_QUERIES',
        'CLA_CAN_DELETE_OWN_DOCUMENTS',
        'COPY_CLA',

        // Payments
        'PAY2_SEARCH_DEFAULT',
        'DELETE_PAY',

        // Incidents
        'DIF_SHOW_REJECT_BTN',
        'DIF2_HIDE_CONTACTS',
        'DIF_OWN_ONLY',
        'INC_SHOW_AUDIT',
        'DIF1_ONLY_FORM',
        'DIF2_DEFAULT',
        'INC_LISTING_ID',
        'DIF2_SEARCH_DEFAULT',
        'INC_SETUP',
        'COPY_INCIDENTS',
        'INC_DELETE_DOCS',
        'INC_SAVED_QUERIES_HOME_SCREEN',
        'INC_SAVED_QUERIES',
        'INC_CAN_DELETE_OWN_DOCUMENTS',
        \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS,

        // Policy
        'POL2_DEFAULT',
        'POL_LISTING_ID',
        'POL2_SEARCH_DEFAULT',
        'POL_OWN_ONLY',
        'POL_SHOW_AUDIT',
        'POL_SETUP',
        'COPY_POL',
        'POL_DELETE_DOCS',

        // Mortality
        'MOR1_DEFAULT',
        'MOR2_DEFAULT',
        'MOR2_SEARCH_DEFAULT',
        'MOR_LISTING_ID',
        'MOR_SHOW_AUDIT',
        'MOR_SETUP',
        'MOR_SAVED_QUERIES_HOME_SCREEN',
        'MOR_SAVED_QUERIES',
        'MOR_DELETE_DOCS',
        'MOR_CAN_DELETE_OWN_DOCUMENTS',
        'COPY_MOR',

        // Locations
        'LOC_DEFAULT',

        // Redress
        'RED1_DEFAULT',
        'RED2_DEFAULT',
        'RED2_SEARCH_DEFAULT',
        'RED_LISTING_ID',
        'RED_SHOW_AUDIT',
        'RED_SETUP',
        'RED_SAVED_QUERIES_HOME_SCREEN',
        'RED_SAVED_QUERIES',
        'RED_DELETE_DOCS',
        'RED_CAN_DELETE_OWN_DOCUMENTS',
        'COPY_RED',

    ];

    // Merge arrays of Module Fields into $mainParams to create full array of field - above could be refactored out to module fields like done for Safeguarding
    return array_merge(
        $mainArray,
        SafeguardingAdminFields::getUserFields(),
    );
}
