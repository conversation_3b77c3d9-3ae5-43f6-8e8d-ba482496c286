<?php

use app\models\framework\config\DatixConfigFactory;
use app\models\framework\flashMessages\ErrorMessage;
use app\models\framework\flashMessages\FlashMessageContainer;
use app\models\framework\flashMessages\InfoMessage;
use app\models\framework\flashMessages\PopUpMessage;
use app\models\framework\session\ClientSession;
use src\search\atprompt\AtPromptSession;
use src\system\container\ContainerFactory;
use src\system\container\facade\Container;

/**
 * @desc Calls a stored procedure to update the last time a user session was active with the current time.
 *
 * @return bool true if there is a valid session for this user (or sessions are not used) and false if a valid
 *              session was not found
 */
function SetSessionActive()
{
    if (isset($_SESSION['logged_in'])) {
        if (empty($_SESSION['session_id'])) {
            return false;
        }

        $sql = 'SELECT count(*) as total FROM SESSIONS WHERE RECORDID = :recordid';
        $result = DatixDBQuery::PDO_fetch($sql, [':recordid' => $_SESSION['session_id']]);
        $ValidSession = ($result['total'] > 0);

        DatixDBQuery::CallStoredProcedure([
            'procedure' => 'SetSessionActive',
            'parameters' => [
                ['@session_id', $_SESSION['session_id'], 'SQLINT4'],
                ['@lock_id', null, 'SQLINT4'],
            ],
        ]);

        return $ValidSession;
    }

    return true;
}

/**
 * @desc Updates the current session if sessions are used and then redirects the user to the login page if their
 * session has expired
 * (or doesn't exist) or if they are not logged in. Used to protect internal pages from non-logged-in users.
 */
function LoggedIn()
{
    global $scripturl;

    $ValidSession = SetSessionActive();

    // Goes to the login page if the user is not logged in
    // or the user is logged in but has gone to a different URL!
    if (!isset($_SESSION['logged_in'])
        || strtolower($_SESSION['scripturl']) != strtolower($scripturl)
        || !$ValidSession
    ) {
        // log reason for automatic logout to assist diagnosis of instances where this occurs in error
        if (!isset($_SESSION['logged_in'])) {
            $reason = 'No session found';
        } elseif (strtolower($_SESSION['scripturl']) != strtolower($scripturl)) {
            $reason = 'Session URL "' . strtolower($_SESSION['scripturl']) . '" does not match actual URL "' . strtolower($scripturl) . '"';
        } elseif (!$ValidSession) {
            $reason = 'No Datix session found';
        }

        $logger = (new ContainerFactory())->create()['logger'];
        $logger->debug('Automatic logout. Reason: ' . $reason);
        $config = (new DatixConfigFactory())->getInstance();
        $from = urlencode($scripturl . '?' . $_SERVER['QUERY_STRING']);

        header('Clear-Site-Data: "cache","cookies","storage"');
        header("Location: {$config->getLoginUrl()}?from={$from}");
        obExit();
    }
}

/**
 * @desc Called from ajax.
 * Collects $_GET values and uses them to set SESSION variables.
 *
 * @codeCoverageIgnoreStart
 */
function SetSessionAJAX()
{
    // CB 2010-11-02 - we need to be very careful which variables we configure this way
    // to prevent malicious users manipulating the session.
    $clientSession = Container::get(ClientSession::class);

    if ($_GET['var'] == 'FlashAvailable') {
        $clientSession->setFlashAvailability((bool) $_GET['value']);
    }

    if ($_GET['var'] == 'Timezone') {
        $clientSession->setTimezoneOffset((int) $_GET['value']);
    }
}
// @codeCoverageIgnoreEnd

function AddMangledSearchError($name, $value)
{
    AddSessionMessage('ERROR', 'There was a problem with the value entered in the \'' . Labels_FormLabel::GetFormFieldLabel($name) . '\' field. The system does not understand what \'' . $value . '\' means');
}

/**
 * Renders any messages registered in the session as HTML and empties the message array
 * Duplicate messages are removed.
 *
 * @deprecated use FlashMessageContainer->getMessages() instead
 *
 * @return string Rendered HTML
 */
function GetSessionMessages()
{
    return Container::get(FlashMessageContainer::class)->getMessages();
}

/**
 * Registers a message in the user session.
 *
 * @deprecated use FlashMessageContainer->addMessage() instead
 *
 * @param string $type Either 'ERROR' or 'INFO'
 * @param string $message The message
 */
function AddSessionMessage($type, $message)
{
    switch ($type) {
        case 'INFO':
            $message = new InfoMessage($message);

            break;
        case 'ERROR':
            $message = new ErrorMessage($message);

            break;
        case 'POPUP':
            $message = new PopUpMessage($message);

            break;
    }

    $messages = Container::get(FlashMessageContainer::class);
    $messages->addMessage($message);
}

function AddValidationMessage($Field, $Message)
{
    $_SESSION['MESSAGES']['VALIDATION'][$Field][] = $Message;
}

function AddSectionMessage($Section, $Type, $Message)
{
    $_SESSION['MESSAGES']['SECTION'][$Section][$Type][] = $Message;
}

function GetSectionMessages($Section)
{
    $HTML = '';

    if (!empty($_SESSION['MESSAGES']['SECTION'][$Section]['ERROR'])) {
        foreach ($_SESSION['MESSAGES']['SECTION'][$Section]['ERROR'] as $Message) {
            $HTML .= '<div class="error_div">' . $Message . '</div>';
        }
    }

    if (!empty($_SESSION['MESSAGES']['SECTION'][$Section]['INFO'])) {
        foreach ($_SESSION['MESSAGES']['SECTION'][$Section]['INFO'] as $Message) {
            $HTML .= '<div class="info_div">' . $Message . '</div>';
        }
    }

    $_SESSION['MESSAGES']['SECTION'][$Section]['ERROR'] = [];
    $_SESSION['MESSAGES']['SECTION'][$Section]['INFO'] = [];

    return $HTML;
}

/**
 * Clears any search related SESSION data for a specified module.
 *
 * @param string $Module the short name for the module to reset
 */
function ClearSearchSession(string $Module): void
{
    $_SESSION[$Module]['WHERE'] = '';
    $_SESSION[$Module]['DUPLICATE_SEARCH'] = '';
    $_SESSION['PROMPT']['PROMPT_WHERE'] = '';
    $_SESSION[$Module]['WHERETABLE'] = '';
    $_SESSION[$Module]['SEARCHLISTURL'] = '';
    $_SESSION[$Module]['RECORDLIST'] = null;

    ClearAtPromptSession($Module);

    $_SESSION[$Module]['current_drillwhere'] = '';

    $_SESSION[$Module]['NEW_WHERE'] = null;
    $_SESSION[$Module]['DRILL_QUERY'] = null;
    $_SESSION[$Module]['DRILL_QUERY'] = null;
    $_SESSION[$Module]['list_type'] = null;
}

function ClearAtPromptSession(string $Module): void
{
    $_SESSION[$Module]['PROMPT']['PROMPT_WHERE'] = '';
    $_SESSION[$Module]['PROMPT']['NEW_WHERE'] = '';
    $_SESSION[$Module]['PROMPT']['ORIGINAL_WHERE'] = '';
    $_SESSION[$Module]['PROMPT']['CURRENT_QUERY_ORIGINAL_WHERE'] = '';
    $_SESSION[$Module]['PROMPT']['VALUES'] = [];

    Container::get(AtPromptSession::class)->clear($Module);
}
