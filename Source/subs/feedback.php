<?php

/**
 * @desc Called when a contact is unlinked from a complaint - cleans up the link_compl entry which would
 * otherwise be left orphaned.
 *
 * @param array $aParams Array of parameters
 *
 * @return obj The result of the SQL delete request
 */
function UnlinkComplLinkData($aParams)
{
    if ($aParams['data']['link_recordid']) {
        $sql = '
            SELECT
                link_type, lcom_iscomplpat
            FROM
                link_contacts, link_compl
            WHERE
                link_contacts.link_recordid = ' . $aParams['data']['link_recordid'] . '
                AND link_contacts.com_id = link_compl.com_id
                AND link_contacts.con_id = link_compl.con_id
        ';

        $row = db_fetch_array(db_query($sql));

        // We want to delete the linked complainant record, but only if the link we are removing is a complainant
        // and not just he same person linked as an Employee or Other Contact
        if ($row['link_type'] == 'C' || ($row['link_type'] == 'A' && $row['lcom_iscomplpat'] == 'Y')) {
            $sql = '
                DELETE FROM
                    LINK_COMPL
                WHERE
                    com_id = ' . $aParams['data']['main_recordid'] . '
                    AND con_id = ' . $aParams['data']['con_recordid'];

            return db_query($sql);
        }
    }
}

function EnsureUniquePrimary($aParams)
{
    global $ModuleDefs;

    $sql = '
        UPDATE
            LINK_COMPL
        SET
            lcom_primary = \'N\'
        WHERE
            ' . $ModuleDefs[$aParams['module']]['FK'] . ' = ' . $aParams['main_recordid'] . '
            AND con_id != ' . $aParams['recordid'];

    db_query($sql);
}

function GetComplaintDateFields()
{
    return [
        'lcom_dreceived',
        'lcom_ddueack',
        'lcom_ddueact',
        'lcom_ddueresp',
        'lcom_dduehold',
        'lcom_dduehold1',
        'lcom_dhold1',
        'lcom_dduerepl',
        'lcom_dack',
        'lcom_dactioned',
        'lcom_dresponse',
        'lcom_dholding',
        'lcom_dreplied',
        'lcom_dreopened',
    ];
}

function HasPrimaryComplLinkData($com_id)
{
    if (!empty($com_id)) {
        $sql = "SELECT count(*) as PrimaryComplainantCheck
                FROM LINK_COMPL WHERE com_id = {$com_id} AND lcom_current = 'Y' AND lcom_primary = 'Y'";

        $row = DatixDBQuery::PDO_fetch($sql);
    }

    if ($row['PrimaryComplainantCheck'] > 0) {
        return true;
    }

    return false;
}
