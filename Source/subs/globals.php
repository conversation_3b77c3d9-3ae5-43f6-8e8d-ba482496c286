<?php

use app\models\framework\config\DatixConfig;
use app\services\globals\SessionGlobalService;
use src\framework\registry\Registry;
use src\search\helpers\AtCodesHelper;
use src\system\container\facade\Container;

/**
 * @desc Called from ajax.
 * Collects $_GET values and uses them to call {@link SetGlobal} or {@link SetUserParm()} depending on whether
 * we are setting user or global parameters.
 *
 * @codeCoverageIgnoreStart
 * No unit test, since this function just covers two other functions which already have unit tests.
 */
function SetGlobalAJAX()
{
    if ($_GET['user']) {
        SetUserParm($_GET['user'], $_GET['global'], $_GET['value']);
    } else {
        SetGlobal($_GET['global'], $_GET['value']);

        if ($_GET['update_session'] == 1) {
            $_SESSION['Globals'][$_GET['global']] = $_GET['value'];

            if (!isset($_SESSION['GlobalObj'])) {
                System_Globals::InitialiseSessionObject();
            }

            $_SESSION['GlobalObj']->setGlobalValue($_GET['global'], $_GET['value']);
        }
    }
}

/**
 * @desc Takes a global/value pair and sets them in the globals table, removing any previous setting.
 *
 * @param string $sGlobalName The name of the parameter to be set
 * @param string $sGlobalValue The value of the global
 * @param bool $UpdateSession If true, the session will be updated (so the user doesn't need to log out), useful
 *                            when setting globals automatically
 */
function SetGlobal($sGlobalName, $sGlobalValue, $UpdateSession = false)
{
    if ($sGlobalName != '') {
        // Delete the parameter.
        $sql = 'DELETE FROM globals WHERE parameter = :parameter';
        DatixDBQuery::PDO_query($sql, ['parameter' => $sGlobalName]);

        if (is_array($sGlobalValue)) {
            $sGlobalValue = implode(' ', $sGlobalValue);
        }

        $sql = 'INSERT INTO globals
            (parameter, parmvalue)
            VALUES
            (:parameter, :paramvalue)';

        DatixDBQuery::PDO_query($sql, ['parameter' => $sGlobalName, 'paramvalue' => $sGlobalValue]);

        if ($UpdateSession) {
            $_SESSION['Globals'][$sGlobalName] = $sGlobalValue;
        }
    }
}

function GetParms($username = '', $setSessionGlobals = true)
{
    // Get global parameters
    $sql = 'SELECT parameter, parmvalue as [value] FROM globals';
    $globals = DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_KEY_PAIR);

    if ($username) {
        // Get profile parameters
        $sql = '
            SELECT lpp_parameter as parameter, lpp_value as [value]
            FROM link_profile_param, users_main
            WHERE link_profile_param.lpp_profile = users_main.sta_profile
            AND users_main.login = :username';
        $result = DatixDBQuery::PDO_fetch_all($sql, ['username' => $username]);

        foreach ($result as $glob) {
            $globals[$glob['parameter']] = $glob['value'];
        }

        // Get user parameters
        $sql = 'SELECT parameter, parmvalue as [value] FROM user_parms WHERE login = :username';
        $result = DatixDBQuery::PDO_fetch_all($sql, ['username' => $username]);

        foreach ($result as $glob) {
            $globals[$glob['parameter']] = $glob['value'];
        }
    }

    // if $setSessionGlobals is set (which the default)
    // then also initialise $_SESSION["Globals"]
    // usefull to only get the settings for a given user without setting the session
    if ($setSessionGlobals) {
        // Global parameters also includes the access levels of the currently logged on user
        // get hold of those before resetting the globals array
        $UserAccessLvls = [];
        if (isset($_SESSION['Globals'])) {
            $accessLvlDefs = Container::get(Registry::class)->getAccessLvlDefs();
            $AccessLvls = array_keys($accessLvlDefs);

            foreach ($AccessLvls as $AccessLvl) {
                if (array_key_exists($AccessLvl, $_SESSION['Globals']) && isset($_SESSION['Globals'][$AccessLvl])) {
                    $UserAccessLvls[$AccessLvl] = $_SESSION['Globals'][$AccessLvl];
                }
            }

            unset($_SESSION['Globals']);
        }

        $_SESSION['Globals'] = $globals;

        if (!empty($UserAccessLvls)) {
            $_SESSION['Globals'] = array_merge($_SESSION['Globals'], $UserAccessLvls);
        }

        System_Globals::InitialiseSessionObject();
    }

    return $globals;
}

/**
 * @desc Grabs the global value that applies to a particular user. If no specific value is found,
 * then just returns the global using {@link GetParm()}
 *
 * @deprecated use SessionGlobalService::getUserParm() instead
 *
 * @see SessionGlobalService::getUserParm()
 *
 * @param string $login The login of the user
 *
 * @return string The global value
 */
function GetUserParm($login, $param, $default = '')
{
    return Container::get(SessionGlobalService::class)->getUserParm($login, $param, $default);
}

/**
 * @desc Grabs a global value, either from the session, the database or from a provided default.
 *
 * @deprecated Use SessionGlobalService::getParm()
 *
 * @see SessionGlobalService::getParm()
 *
 * @param string $glob The name of the global
 * @param string $default The value to return if no other is found in the session or db
 * @param bool $bGetLive whether to check the db or not - if true will check the db, otherwise will rely on the session
 *
 * @return string The global value
 */
function GetParm($glob, $default = '', $bGetLive = false)
{
    return Container::get(SessionGlobalService::class)->getParm($glob, $default, $bGetLive);
}

/**
 * @desc Translates a yes/no value into a boolean
 *
 * @param string $parm The value to be translated ("Y" or "N")
 *
 * @return bool true if passed "Y" or "y" false otherwise
 */
function bYN($parm): bool
{
    return $parm == 'Y' || $parm == 'y';
}

/**
 * Adds selected global parameters to a global JavaScript array,
 * so they are accessible in JS functions.
 *
 * @global array $JSFunctions
 */
function addGlobalsToJSGlobal()
{
    global $JSFunctions;

    $globalValuesToTransfer = [
        'AJAX_WAIT_MSG' => getParm('AJAX_WAIT_MSG', 'N'),
        'AJAX_WAIT_TIME' => getParm('AJAX_WAIT_TIME', '20'),
        'COMBO_LINK_IN_SEARCH' => getParm('COMBO_LINK_IN_SEARCH', 'N'),
        'MinifierDisabled' => Container::get(DatixConfig::class)->isMinifierOn() ? 'false' : 'true',
        'WEB_SPELLCHECKER' => (getParm('WEB_SPELLCHECKER', 'N') == 'Y' ? 'true' : 'false'),
        'CSRF_PREVENTION' => GetParm('CSRF_PREVENTION', 'N'),
        'dateFormatCode' => GetParm('FMT_DATE_WEB'),
        'currencyChar' => GetParm('CURRENCY_CHAR', '£'),
        'dateFormat' => (GetParm('FMT_DATE_WEB') === 'US' ? 'mm/dd/yy' : 'dd/mm/yy'),
        'weekStartDay' => GetParm('WEEK_START_DAY', 2),
        'calendarWeekend' => GetParm('CALENDAR_WEEKEND', 1),
    ];

    $JS = '';

    foreach ($globalValuesToTransfer as $global => $globalVal) {
        $JS .= "globals['" . $global . "'] = " . ($globalVal === 'true' || $globalVal === 'false' ? $globalVal : "'" . $globalVal . "'") . ';
        ';
    }

    $nonStringGlobalValuesToTransfer = [
        'deviceDetect' => json_encode(Container::get(Registry::class)->getDeviceDetector()->getDetectValues()),
        'validSearchCodes' => json_encode(AtCodesHelper::VALID_CODES),
    ];

    foreach ($nonStringGlobalValuesToTransfer as $global => $globalVal) {
        $JS .= "globals['" . $global . "'] = " . $globalVal . ';
        ';
    }

    $JSFunctions[] = $JS;
}
