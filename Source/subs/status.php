<?php

use app\services\approvalStatus\ApprovalStatus;
use app\services\records\Records;
use app\services\records\RecordsFactory;
use app\services\webColour\WebColourRepositoryFactory;
use src\component\field\CustomFieldFactory;
use src\component\field\SelectFieldFactory;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;
use src\system\language\LanguageServiceFactory;
use src\system\language\LanguageSession;

/**
 * @desc Determines the approval statuses that a record can be moved to from the one it is currently at.
 *
 * @param string $Module the current module
 * @param string $ModPerm the set of access levels the user has on this module
 * @param string $CurrLevel the approval status the record is currently at
 *
 * @return array<string, array{description: string, colour: string}> an array of code/description pairs for the statuses the record can be moved to
 */
function GetLevelsTo($Module, $ModPerm, $CurrLevel): array
{
    $registry = Container::get(Registry::class);
    $languageId = Container::get(LanguageSession::class)->getLanguage();
    $LevelLabels = [];

    if ($ModPerm == '') {
        $ModPerm = ['NONE'];
    } elseif (!is_array($ModPerm)) {
        $ModPerm = [$ModPerm];
    }

    $CurrLevel = ($CurrLevel ?: 'NEW');

    $sql = '
        SELECT code_approval_status.code, code_approval_status.description as descriptionLabel , code_approval_status_descr.description AS descr, code_approval_status.cod_web_colour
        FROM code_approval_status
        JOIN approval_action
        ON
            code_approval_status.code = approval_action.apac_to
        AND
            code_approval_status.module = approval_action.module
        AND
            code_approval_status.workflow = approval_action.apac_workflow
        LEFT JOIN code_approval_status_descr
            ON (code_approval_status.code = code_approval_status_descr.code
            AND code_approval_status.module = code_approval_status_descr.module
            AND code_approval_status_descr.language = ' . $languageId . ')
        WHERE
            approval_action.access_level IN (\'' . implode('\', \'', $ModPerm) . '\')
        AND
            approval_action.module LIKE :module
        AND
            approval_action.apac_from LIKE :apac_from
        AND
            approval_action.apac_workflow = :apac_workflow
        ORDER BY code_approval_status.cod_listorder';

    $result = DatixDBQuery::PDO_fetch_all(
        $sql,
        [
            'module' => $Module,
            'apac_from' => $CurrLevel,
            'apac_workflow' => GetWorkflowID($Module),
        ],
    );

    $webColourRepository = (new WebColourRepositoryFactory())->create();

    foreach ($result as $row) {
        $LevelLabels[$row['code']]['description'] = $row['descr'] ?: $row['descriptionLabel'];

        $webColour = $webColourRepository->get($row['code'], $Module);
        $colour = $webColour ?: $row['cod_web_colour'];

        $LevelLabels[$row['code']]['colour'] = $colour;
    }



    $hideRejectStatus = (
        ($Module == 'INC' && $registry->getParm('DIF_SHOW_REJECT_BTN', 'Y')->isFalse())
        || ($Module == 'CON' && $registry->getParm('CON_SHOW_REJECT_BTN', 'Y')->isFalse())
        || ($Module == 'COM' && $registry->getParm('COM_SHOW_REJECT_BTN', 'Y')->isFalse())
    );

    if ($CurrLevel != 'REJECT' && $hideRejectStatus) {
        unset($LevelLabels['REJECT']);
    }

    return $LevelLabels;
}

/**
 * @desc Determines the approval statuses that a user with a given access level is able to view.
 *
 * @param string $Module the current module
 * @param string|null $ModPerm the access level the user has on this module
 *
 * @return array<string, array{description: string, colour: string}> an array of code/description pairs for the statuses the user can see
 */
function GetLevelstoView(string $Module, $ModPerm): array
{
    $LevelLabels = [];
    $statusLanguage = Container::get(LanguageSession::class)->getLanguage();
    $sql = <<<'SQL'
        SELECT code_approval_status.code AS code, code_approval_status.description as descritpionLabel, code_approval_status_descr.description AS descr, cod_web_colour
        FROM code_approval_status
        JOIN link_access_approvalstatus
            ON
              code_approval_status.code = link_access_approvalstatus.code
            AND
              code_approval_status.module = link_access_approvalstatus.module
            AND
              code_approval_status.workflow = link_access_approvalstatus.las_workflow
        LEFT JOIN code_approval_status_descr
            ON (code_approval_status.code = code_approval_status_descr.code
            AND code_approval_status.module = code_approval_status_descr.module
            AND code_approval_status_descr.language = :lang)
        WHERE
              code_approval_status.module = :module
            AND
              link_access_approvalstatus.access_level = :modPerms
            AND
              code_approval_status.workflow = :workflowid
        ORDER BY code_approval_status.cod_listorder
        SQL;

    $result = DatixDBQuery::PDO_fetch_all(
        $sql,
        [
            'module' => $Module,
            'workflowid' => GetWorkflowID($Module),
            'lang' => $statusLanguage,
            'modPerms' => $ModPerm,
        ],
    );
    $webColourRepository = (new WebColourRepositoryFactory())->create();

    foreach ($result as $row) {
        $LevelLabels[$row['code']]['description'] = $row['descr'] ?: $row['descritpionLabel'];

        $webColour = $webColourRepository->get($row['code'], $Module);
        $colour = $webColour ?: $row['cod_web_colour'];

        $LevelLabels[$row['code']]['colour'] = $colour;
    }

    return $LevelLabels;
}

/**
 * @desc Populates $CurrentApproveObj and $ApprovalObj, which are referred to in BasicFormXXX.php.
 *
 * @param string $Module Current module
 * @param array $Data Data array for current record
 * @param obj $CurrentApproveObj Reference to object to populate with field object for the current approval status field
 * @param obj $ApproveObj Reference to object to populate with field object for the approval status field
 * @param string $FormType Type of form (Print, ReadOnly, Design etc...)
 * @param object $FormDesign contains current form design settings
 * @param string $PermOverride permissions to use if CASCADE_PERMISSIONS global is set (used in contacts)
 * @param string $parentModule Parent module
 */
function SetUpApprovalArrays(
    $Module,
    $Data,
    &$CurrentApproveObj,
    &$ApproveObj,
    $FormType = '',
    $FormDesign = null,
    $PermOverride = '',
    $HighestAccessLevel = '',
    $linkType = '',
    $parentModule = ''
) {
    global $ExpandSections, $ExpandFields, $DefaultValues;

    $registry = Container::get(Registry::class);
    $moduleDefs = $registry->getModuleDefs();

    if (bYN(GetParm('USE_ADVANCED_ACCESS_LEVELS', 'N')) && $HighestAccessLevel) {
        $UserPerm = $HighestAccessLevel;
    } else {
        $UserPerm = GetParm($moduleDefs[$Module]['PERM_GLOBAL']);
    }
    // "cascade permissions" mean that permissions for the main module cascade onto linked modules.
    $Perms = (bYN(GetParm('CASCADE_PERMISSIONS', 'Y')) && $PermOverride ? $PermOverride : $UserPerm);

    $ApprovalLabel = $FormDesign->UserLabels['rep_approved'] ?? null;

    // The following code is normally executed in FormClasses under makeForm, but that function
    // hasn't been called yet, so we have to put it in earlier for rep_approved.
    // This could maybe be removed later if this condition handling is improved/made more generic.

    // Reformat $ExpandSections and $ExpandFields
    $ExpandSections = setupExpandAlertCondition($ExpandSections ?? []);
    $ExpandFields = setupExpandAlertCondition($ExpandFields ?? []);
    // /-- end of Expand reformating

    // Current approval status field is always read only.
    $CurrentApproveArray = GetLevelFieldLabels($Module);
    $CurrentApproveObj = SelectFieldFactory::createSelectField(
        'rep_approved_display',
        $Module,
        $Data['rep_approved_display'] ?? null,
        'ReadOnly',
    );
    $CurrentApproveObj->setCustomCodes($CurrentApproveArray);

    $recordsService = (new RecordsFactory())->create();
    $dataRepApproved = $Data['rep_approved'] ?? null;
    $allowsStatusEdit = $recordsService->allowsReadonlyApprovalStatusEditing($Module, $dataRepApproved);

    $isSearchForm = in_array($FormType, ['Search', 'linkedDataSearch']);
    $canMoveRecord = CanMoveRecord(['module' => $Module, 'data' => $Data, 'perms' => $Perms]);
    $isRejectStatus = $dataRepApproved == 'REJECT';

    if (($allowsStatusEdit || $isRejectStatus)
        && !$isSearchForm
        && $canMoveRecord
    ) {
        $FieldType = 'Edit';
    } else {
        $FieldType = $FormType;
    }

    $ApproveArray = [];
    $ApprovalValue = '';
    if (!empty($FormDesign->HideFields['rep_approved']) && $FormType != 'Design') {
        if ($FormType != 'Search' && !empty($FormDesign->DefaultValues['rep_approved'])) {
            $ApproveObj = CustomFieldFactory::create($FieldType, '<input type="hidden" name="rep_approved" id="rep_approved" value="' . $FormDesign->DefaultValues['rep_approved'] . '" />');
        }
    } else {
        if ($FormType == 'Search') {
            if (bYN(GetParm('USE_ADVANCED_ACCESS_LEVELS', 'N'))) {
                $unorderedApprovalStatuses = [];

                $currentUser = (new UserSessionFactory())->create()->getCurrentUser();
                $accessLevels = $currentUser->getAccessLevels($Module);

                foreach ($accessLevels as $accessLevel) {
                    $unorderedApprovalStatuses = array_merge($unorderedApprovalStatuses, GetLevelstoView($Module, $accessLevel));
                }

                $sql = "
                        SELECT code_approval_status.code as code
                        FROM code_approval_status
                        JOIN link_access_approvalstatus
                            ON
                              code_approval_status.code = link_access_approvalstatus.code
                            AND
                              code_approval_status.module = link_access_approvalstatus.module
                            AND
                              code_approval_status.workflow = link_access_approvalstatus.las_workflow
                        WHERE
                              code_approval_status.module like '{$Module}'
                            AND
                              code_approval_status.workflow = " . GetWorkflowID($Module) . '
                        ORDER BY code_approval_status.cod_listorder';

                $orderedApprovalStatuses = DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_COLUMN);

                foreach ($orderedApprovalStatuses as $orderedApprovalStatusCode) {
                    if (isset($unorderedApprovalStatuses[$orderedApprovalStatusCode])) {
                        $ApproveArray[$orderedApprovalStatusCode] = $unorderedApprovalStatuses[$orderedApprovalStatusCode];
                    }
                }
            } else {
                $ApproveArray = GetLevelstoView($Module, $Perms);
            }

            $ApprovalValue = $dataRepApproved;

            unset($ApproveArray['NEW']);
        } elseif ($FormType == 'Design') {
            $ApproveArray = GetLevelFieldLabels($Module);

            $ApprovalValue = $DefaultValues['rep_approved'] ?? null;

            unset($ApproveArray['NEW']);
        } else {
            if (bYN(GetParm('USE_ADVANCED_ACCESS_LEVELS', 'N')) && $FormType == 'New') {
                $unorderedApprovalStatuses = [];

                $accessLevels = [];

                $userSession = (new UserSessionFactory())->create();
                if ($userSession->isLoggedIn()) {
                    $accessLevels = $userSession->getCurrentUser()->getAccessLevels($Module);
                }

                if (count($accessLevels) > 0) {
                    foreach ($accessLevels as $accessLevel) {
                        $unorderedApprovalStatuses = array_merge($unorderedApprovalStatuses, GetLevelsTo($Module, $accessLevel, $dataRepApproved));
                    }

                    $sql = "
                        SELECT code_approval_status.code as code
                        FROM code_approval_status
                        JOIN link_access_approvalstatus
                            ON
                              code_approval_status.code = link_access_approvalstatus.code
                            AND
                              code_approval_status.module = link_access_approvalstatus.module
                            AND
                              code_approval_status.workflow = link_access_approvalstatus.las_workflow
                        WHERE
                              code_approval_status.module like '{$Module}'
                            AND
                              code_approval_status.workflow = " . GetWorkflowID($Module) . '
                        ORDER BY code_approval_status.cod_listorder';

                    $orderedApprovalStatuses = DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_COLUMN);

                    foreach ($orderedApprovalStatuses as $orderedApprovalStatusCode) {
                        if (isset($unorderedApprovalStatuses[$orderedApprovalStatusCode])) {
                            $ApproveArray[$orderedApprovalStatusCode] = $unorderedApprovalStatuses[$orderedApprovalStatusCode];
                        }
                    }
                } else {
                    $ApproveArray = GetLevelsTo($Module, 'NONE', $dataRepApproved);
                }
            } else {
                $ApproveArray = GetLevelsTo($Module, $Perms ?: 'NONE', $dataRepApproved);
            }

            if (isset($Data['rep_approved'], $ApproveArray[$Data['rep_approved']])) {
                $ApprovalValue = $Data['rep_approved'];
            }

            if ($ApprovalValue == '' && $ApproveArray[$FormDesign->DefaultValues['rep_approved']]) {
                $ApprovalValue = $FormDesign->DefaultValues['rep_approved'];
            }

            if ($ApprovalValue == '' && isset($DefaultValues['rep_approved'])) {
                $ApprovalValue = $DefaultValues['rep_approved'];
            }
        }

        if (empty($ApprovalLabel)) {
            $table = $registry->getModuleDefs()[$Module]['TABLE'];
            $languageService = (new LanguageServiceFactory())->create();
            $domain = $languageService->getModuleDomain($Module);
            $ApprovalLabel = $registry->getFieldLabels()->getLabel($table, 'rep_approved', $domain) ?: 'Approval status';
            $ApprovalLabel .= ' ' . _fdtk('after_save') . ' ';
        }

        if (empty($ApproveArray)) { // no permissions to save
            $ApproveObj = CustomFieldFactory::create($FieldType, _fdtk('no_permissions_to_save_record'));
        } elseif (in_array($FormType, ['Search', 'linkedDataSearch'])) {
            $table = $moduleDefs[$Module]->getDbReadObj();

            if (null !== ($fieldset = ($moduleDefs[$parentModule]['FIELDSET_MAPPINGS'][$linkType] ?? null))) {
                $table = $fieldset . '|' . $table;
            }

            $ApproveObj = SelectFieldFactory::createSelectField(
                'rep_approved',
                $Module,
                $Data[$table . '|rep_approved'] ?? null,
                'Search',
                false,
                $ApprovalLabel,
                '',
                '',
                '',
                $table,
            );
            $ApproveObj->setCustomCodes($ApproveArray);
        } else {
            $ApproveObj = SelectFieldFactory::createSelectField(
                'rep_approved',
                $Module,
                $ApprovalValue,
                $FieldType,
                false,
                $ApprovalLabel,
            );
            $ApproveObj->setCustomCodes($ApproveArray);
        }
    }
}

function setupExpandAlertCondition(array $expandData): array
{
    if (empty($expandData['rep_approved'])) {
        return $expandData;
    }

    foreach ($expandData['rep_approved'] as &$expandDatum) {
        $expandStrings = array_map(
            static fn (string $value) => "parentValue == '{$value}'",
            $expandDatum['values'] ?? [],
        );
        $expandDatum['alertcondition'] = implode('||', $expandStrings);
    }

    return $expandData;
}

/**
 * @desc Returns basicform array for rep_approved field. This field has complex logic associated with it, and putting
 * all the logic in the BasicForm file has become too confusing.
 *
 * @param array $aParams Array of parameters
 *
 * @return array basicForm array entry
 */
function RepApprovedBasicFormArray($aParams)
{
    $useFormDesignLanguage = $aParams['useFormDesignLanguage'] ?? false;

    $extras = [];
    $isStatusEditable = Container::get(Records::class)->allowsReadonlyApprovalStatusEditing(
        $aParams['module'],
        $aParams['data']['rep_approved'] ?? null,
    );
    $isStatusReject = ($aParams['data']['rep_approved'] ?? null) === ApprovalStatus::REJECTED;

    if ($isStatusEditable || $isStatusReject) {
        $extras['EditableWhenReadonly'] = true;
    }

    return [
        'Name' => 'rep_approved',
        'Title' => Container::get(ApprovalStatus::class)->getLabel($aParams['module'], $aParams['formtype'], $aParams['data']['recordid'] ?? null, $useFormDesignLanguage),
        'Type' => 'formfield',
        'Condition' => RepApprovedBasicFormArrayCondition($aParams['module'], $aParams['formtype'], $aParams['approveobj'], $aParams['data']['rep_approved'] ?? null, $isStatusEditable),
        'IgnoreHidden' => true,
        'AlwaysMandatory' => true,
        'NoReadOnly' => true,
        'AllowDefault' => true,
        'ValueRequired' => true,
        'FormField' => $aParams['approveobj'],
    ] + $extras;
}

/**
 * Runs through the logic to check whether the approval status dropdown should be displayed.
 *
 * @param $module
 * @param $formType
 * @param $appObj
 * @param $statusCode
 *
 * @return bool
 */
function RepApprovedBasicFormArrayCondition($module, $formType, $appObj, $statusCode, bool $allowsStatusEdit)
{
    if (in_array($formType, ['Search', 'Design'])) {
        return true;
    }

    if ($formType == 'Print') {
        return false;
    }

    if ($formType == 'ReadOnly' && $allowsStatusEdit) {
        return true;
    }

    if ($formType == 'ReadOnly' && $statusCode != 'REJECT') {
        return false;
    }

    if (!isset($appObj)) {
        return false;
    }

    return true;
}

/**
 * @desc Returns basicform array for rep_approved_display field. This field has complex logic associated with it,
 * and putting all the logic in the BasicForm file has become too confusing.
 *
 * @param array $aParams Array of parameters
 *
 * @todo Document array shape of $aParams and use TFormField
 *
 * @return array basicForm array entry
 *
 * @template TFormField of object
 *
 * @psalm-return array{
 *     Name:'rep_approved_display',
 *     Title:string,
 *     Type:'formfield',
 *     NoMandatory:true,
 *     NoReadOnly:true,
 *     Condition:bool,
 *     FormField:TFormField
 * }
 */
function RepApprovedDisplayBasicFormArray(array $aParams): array
{
    $useFormDesignLanguage = $aParams['useFormDesignLanguage'] ?? false;

    return [
        'Name' => 'rep_approved_display',
        'Title' => _fdtk('current_approval_status', $useFormDesignLanguage),
        'Type' => 'formfield',
        'NoMandatory' => true,
        'NoReadOnly' => true,
        'Condition' => RepApprovedDisplayBasicFormArrayCondition($aParams),
        'FormField' => $aParams['currentapproveobj'],
    ];
}

/**
 * @desc Runs through the logic to check whether the current approval status description should be displayed.
 *
 * @param array $aParams Array of parameters
 *
 * @return bool true if this field should be available for display, false if not
 */
function RepApprovedDisplayBasicFormArrayCondition($aParams)
{
    global $ModuleDefs;

    if ($aParams['formtype'] == 'Print') {
        return true;
    }

    if (empty($_SESSION['logged_in'])) {
        return false;
    }

    if ($aParams['formtype'] == 'Design') {
        return true;
    }

    if ($aParams['formtype'] == 'Search' || $aParams['formtype'] == 'linkedDataSearch') {
        return false;
    }

    if (!isset($aParams['currentapproveobj'])) {
        return false;
    }

    return true;
}

function SetUpFormTypeAndApproval($module, &$data, &$FormType, $form_action, $Perms)
{
    global $ModuleDefs;
    $dataRepApproved = $data['rep_approved'] ?? $data['rep_approved'] ?? null;

    $AccessFlag = GetAccessFlag($module, $Perms, $dataRepApproved);

    if ($AccessFlag == 'R') {
        $FormType = 'ReadOnly';
    } elseif ($AccessFlag == 'E') {
        $FormType = 'Edit';
    }

    if (empty($data['error'])) {
        $data['rep_approved_display'] = $dataRepApproved;
    }

    if (empty($data) || (($data['recordid'] ?? null) == '')) {
        $FormType = 'New';
        $data['rep_approved_display'] = _fdtk('rep_approved_display_new');
    }

    if ($form_action == 'search') {
        $FormType = 'Search';
    }

    if ($form_action == 'ReadOnly') {
        $FormType = 'ReadOnly';
    }

    if (!empty($_GET['print']) || $form_action == 'Print') {
        $FormType = 'Print';
    }
}

/**
 * Checks that the transfer of approval status is legitimate by querying the approval_action table.
 *
 * @return bool whether or not the transfer is legitimate
 */
function checkApprovalStatusTransferLegitimate($aParams)
{
    if (!is_array($aParams['perm'])) {
        $aParams['perm'] = [$aParams['perm']];
    }

    $sql = 'SELECT count(*) as num
            FROM approval_action
            WHERE apac_to = :apac_to
            AND apac_from = :apac_from
            AND module = :module
            AND access_level in (\'' . implode('\',\'', $aParams['perm']) . '\')
            AND apac_workflow = :apac_workflow';

    $result = DatixDBQuery::PDO_fetch(
        $sql,
        [
            'apac_to' => $aParams['to'],
            'apac_from' => $aParams['from'],
            'module' => $aParams['module'],
            'apac_workflow' => GetWorkflowID($aParams['module']),
        ],
        PDO::FETCH_COLUMN,
    );

    return (int) $result > 0;
}

/**
 * @desc Checks whether the approval status change being made according to the $_POST
 * array is valid or not. Returns a valid target approval status.
 *
 * @param array $aParams Array of Parameters
 *
 * @return string the code for a valid approval status
 */
function GetValidApprovalStatusValueFromPOST($aParams)
{
    if (isset($_SESSION['CurrentUser'])) {
        $accessLevels = $_SESSION['CurrentUser']->getAccessLevels($aParams['module']);
        $perm = $accessLevels ?: 'NONE';
    } else {
        $perm = 'NONE';
    }

    // rep_approved should always be passed in $_POST when saving a record. This lets us know where to save the record.
    // We need to check the value passed is valid before saving.
    if (isset($_POST['rep_approved']) && $_POST['rep_approved'] != ''
        && checkApprovalStatusTransferLegitimate([
            'to' => Sanitize::SanitizeString($_POST['rep_approved']),
            'from' => Sanitize::SanitizeString($_POST['rep_approved_old']),
            'perm' => $perm,
            'module' => $aParams['module'],
        ])) {
        // valid approval status transition
        return Sanitize::SanitizeString($_POST['rep_approved']);
    }
    // invalid or missing value - needs to be set automatically;
    return getDefaultRepApprovedValue([
        'data' => $_POST,
        'perms' => (!empty($_SESSION['CurrentUser']) ? $_SESSION['CurrentUser']->getAccessLevels($aParams['module']) : 'NONE'),
        'module' => $aParams['module'],
    ]);
}
