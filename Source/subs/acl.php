<?php

use app\models\framework\config\GlobalValue;
use app\services\approvalStatus\ApprovalStatus;
use Doctrine\DBAL\Connection;
use src\framework\registry\Registry;
use src\framework\session\UserSession;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;

/**
 * @desc Determines whether the current user can access a record.
 *   Specific record ID is specified by the 'recordid' element in the URL query string.
 *
 * @param string $module which module the check should be applied for
 *
 * @return bool
 */
function CanAccessRecord($module)
{
    global $ModuleDefs;

    $recordId = isset($_GET['recordid']) ? (int) $_GET['recordid'] : null;

    if ($module === 'ADM') {
        $module = 'USE';
    }

    $table = $ModuleDefs[$module]['VIEW'] ?? $ModuleDefs[$module]['TABLE'] ?? null;
    if (empty($table)) {
        return false;
    }

    if ($recordId) {
        // need to check we have access to the record before allowing generation.
        $sql = 'SELECT count(*) as num FROM ' . $table
            . ' WHERE ' . MakeSecurityWhereClause("recordid = {$recordId}", $module, $_SESSION['initials']);

        return DatixDBQuery::PDO_fetch($sql, [], PDO::FETCH_COLUMN) == '1';
    }

    return false;
}

/**
 * @desc Determines whether a user can save a record from its current location
 * (includes saving back to the same status).
 *
 * @param array $aParams array of parameters
 *
 * @return bool true if this record can be saved, false if not
 */
function CanMoveRecord($aParams)
{
    global $ModuleDefs;

    $module = $aParams['module'] ?? '';
    $permGlobal = isset($ModuleDefs[$module]['PERM_GLOBAL']) ? $ModuleDefs[$module]['PERM_GLOBAL'] : '';
    $repApproved = $aParams['data']['rep_approved'] ?? '';

    $Levels = GetLevelsTo($module, GetParm($permGlobal), $repApproved);

    if (empty($Levels)) {
        return false;
    }

    return true;
}

/**
 * @desc Determines the type of access that a user has for a particular approval status
 * (if approval statuses are used) in a particular module.
 *
 * @deprecated use ApprovalStatus->getAccessFlag
 *
 * @global array $ModuleDefs
 *
 * @param string $Module the current module
 * @param string $ModPerm the access level the user has on this module
 * @param string $CurrLevel the approval status the record is currently at
 *
 * @return string 'E' if the user has permission to edit the record, 'R' if they have read-only access
 *                and '' if something has gone wrong
 */
function GetAccessFlag($Module, $ModPerm, $CurrLevel)
{
    return Container::get(ApprovalStatus::class)->getAccessFlag($Module, $ModPerm, $CurrLevel);
}

/**
 * @desc Checks whether a combination of permissions and approval statuses has permission to see
 * contact information linked to a record.
 * This access is decided by Datix at the moment, with no user configuration.
 *
 * @param string $Module The current module
 * @param ?string $Perms The permission level in the current module
 * @param string $ApprovalStatus The approval status of the main record in question
 *
 * @return bool true if contact information can be displayed, false otherwise
 */
function CanSeeContacts($Module, $Perms, $ApprovalStatus)
{
    $sql = '
        SELECT
            las_restrict_contacts
        FROM
            link_access_approvalstatus
        WHERE
            access_level = :access_level
        AND
            module = :module
        AND
            code = :code
        AND
            las_workflow = :las_workflow
    ';

    $sqlParams = [
        'access_level' => $Perms,
        'module' => $Module,
        'code' => $ApprovalStatus,
        'las_workflow' => GetWorkflowID($Module),
    ];

    $row = DatixDBQuery::PDO_fetch($sql, $sqlParams);

    return !bYN($row['las_restrict_contacts'] ?? 'N');
}

/**
 * @desc Shortcut function. Gets the access level for the currently logged in user for a particular module.
 *
 * @param string $module The module in question
 *
 * @return string the code for the access level that the current user has
 */
function GetAccessLevel($module)
{
    global $ModuleDefs;

    return GetParm($ModuleDefs[$module]['PERM_GLOBAL']);
}

/**
 * @see CentralAdminService::currentUserIsCentralAdmin()
 *
 * @desc Shortcut function. Returns true if the user is a central administrator in a centrally administered system,
 * false otherwise. Put in a function so that if we ever change the admin requirements, we don't have
 * to change hundreds of places.
 *
 * @return bool true if the user is a central admin, false otherwise
 */
function IsCentralAdmin()
{
    $registry = Container::get(Registry::class);

    return IsCentrallyAdminSys() && $registry->getUserParm($_SESSION['login'], 'CENTRAL_ADMINISTRATOR', 'N')->isTrue();
}

/**
 * @see CentralAdminService::isCentrallyAdministeredSystem()
 *
 * @desc Shortcut function. Returns true if we are on a centrally administered system, false otherwise.
 *
 * @return bool true if the system is centrally administered, false otherwise
 */
function IsCentrallyAdminSys()
{
    if (GetParm('CENTRAL_ADMINISTRATION', 'N') == 'L') {
        return true;
    }

    return false;
}

/**
 * @desc Checks whether the current user has user admin permissions.
 *
 * @deprecated use UserSession->isSubAdmin
 *
 * @param bool $OrFullAdmin If true, then will return whether the current user is a user admin OR a full admin.
 *                          If false, will only return whether they are a user admin. True by default.
 *
 * @return bool true if the user has user admin permissions, false otherwise
 */
function IsSubAdmin($OrFullAdmin = true)
{
    return Container::get(UserSession::class)->isSubAdmin($OrFullAdmin);
}

/**
 * @desc Shortcut function. Returns true if the user has setup permsisions on the module provided, false otherwise.
 *
 * @param string $module the module code we are checking
 *
 * @return bool true if the user has setup permsisions, false otherwise
 */
function HasSetupPermissions($module)
{
    // if user is a full admin, they can automatically set up all modules.
    if ((new UserSessionFactory())->create()->isFullAdmin()) {
        return true;
    }

    return bYN(GetParm($module . '_SETUP', 'N'));
}

/**
 * @param array{
 *         module: non-empty-string,
 *         perms: non-empty-string|GlobalValue,
 *         data:array{
 *             rep_approved: non-empty-string
 *         }
 *     } $aParams
 */
function CanEditRecord($aParams): bool
{
    // $aParams is untyped garbage so here we do some simple type checks
    // to make sure it's good to pass into the method that'll make the API call
    // See the docblock for the structure we're expecting
    if (
        !is_string($aParams['module'] ?? null)
        || $aParams['module'] === ''
        || !is_string($aParams['data']['rep_approved'] ?? null)
        || $aParams['data']['rep_approved'] === ''
        || empty($aParams['perms'])
        || !(
            $aParams['perms'] instanceof GlobalValue
            || is_string($aParams['perms'])
        )
    ) {
        return false;
    }

    return GetEditPermissions(
        $aParams['module'],
        (string) $aParams['perms'],
        $aParams['data']['rep_approved'],
    ) == 'E';
}

/**
 * @desc Finds the read/write permissions for a user and a module.
 *
 * @param string $moduleCode Module code(INC, COM, CLA, MOR, etc)
 * @param string $permissions User permission type(DIF1, RM, DIF2_READ_ONLY, etc)
 * @param string $repApproved Rep approved value for the record(current status, NEW, PEND, REJECT, FA, etc)
 *
 * @return string 'E' for editable, 'R' for read only
 */
function GetEditPermissions(
    string $moduleCode,
    string $permissions,
    string $repApproved
): ?string {
    $db = Container::get(Connection::class);
    $qb = $db->createQueryBuilder();

    $qb->select('access')
        ->from('link_access_approvalstatus')
        ->andWhere($qb->expr()->eq('module', ':module'))
        ->andWhere($qb->expr()->eq('access_level', ':access_level'))
        ->andWhere($qb->expr()->eq('code', ':repApprovedStatus'))
        ->andWhere($qb->expr()->eq('las_workflow', ':las_workflow'))
        ->setParameters([
            'module' => $moduleCode,
            'access_level' => $permissions,
            'repApprovedStatus' => $repApproved,
            'las_workflow' => GetWorkflowID($moduleCode),
        ]);
    $rows = $qb->executeQuery()->fetchFirstColumn();

    return $rows[0] ?? null;
}

/**
 * @desc Simple function used when drawing contacts sections.
 *
 * @return bool true if this user has permission to link contacts to records, false otherwise
 */
function CanLinkNewContacts()
{
    // "cascade permissions" mean that permissions for the main module cascade onto linked
    // modules. Since the user has permission to the main module, he has permission to the contacts linked.
    if (bYN(GetParm('CASCADE_PERMISSIONS', 'N'))) {
        return true;
    }
    // otherwise, find the user's contact module permissions.
    $AccessFlag = GetAccessFlag('CON', GetAccessLevel('CON'), 'NEW');

    return $AccessFlag == 'E';
}

/**
 * Determines whether the current user has permission to delete a generic record (only used in payments atm).
 */
function CanDeleteRecord($Module)
{
    return $Module === 'PAY' && CanSeeModule($Module) && bYN(GetParm('DELETE_' . $Module, 'N'));
}
