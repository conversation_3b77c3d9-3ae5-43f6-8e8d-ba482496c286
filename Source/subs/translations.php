<?php

use src\search\helpers\AtCodesHelper;
use src\system\container\facade\Container;
use src\system\language\LanguageSession;

function _fdtk(string $key, bool $useFormDesignLanguage = false): string
{
    return Container::get(LanguageSession::class)->getStaticStringDefault($key, $useFormDesignLanguage);
}

function translateStaticAtCodes($WhereClause)
{
    $atHelper = Container::get(AtCodesHelper::class);
    while ($atHelper->isAtTodayCode($WhereClause)) {
        $WhereClause = $atHelper->getAtTodayDate($WhereClause);
    }

    if (\UnicodeString::stripos($WhereClause, '@LASTWEEK') !== false) {
        GetLastWeekDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@LASTWEEK', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@LASTMONTH') !== false) {
        GetLastMonthDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@LASTMONTH', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@LASTQUARTER') !== false) {
        GetLastQuarterDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@LASTQUARTER', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@LASTYEAR') !== false) {
        GetLastYearDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@LASTYEAR', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@WEEK') !== false) {
        GetWeekDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@WEEK', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@MONTH') !== false) {
        GetMonthDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@MONTH', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@QUARTER') !== false) {
        GetQuarterDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@QUARTER', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@YEAR') !== false) {
        GetYearDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@YEAR', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@FINYEAR') !== false) {
        GetFinYearDateRange(getdate(), $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@FINYEAR', $dtStart, $dtEnd);
    }

    if (\UnicodeString::stripos($WhereClause, '@LASTFINYEAR') !== false) {
        $dtToday = getdate();
        $dtToday['year'] = $dtToday['year'] - 1;
        GetFinYearDateRange($dtToday, $dtStart, $dtEnd);
        $WhereClause = TranslateDateRangeCom($WhereClause, '@LASTFINYEAR', $dtStart, $dtEnd);
    }

    return $WhereClause;
}

function TranslateWhereCom($WhereClause, $Initials = '', $module = '', $allowNull = false)
{
    if ($Initials == '' && !$allowNull) {
        $Initials = $_SESSION['initials'] ?? '';
    }

    $WhereClause = \UnicodeString::str_ireplace('@USER_INITIALS', $Initials, $WhereClause);

    $WhereClause = translateStaticAtCodes($WhereClause);

    $numberRemainingAtSymbols = substr_count(\UnicodeString::strtoupper($WhereClause), '@USER_');

    for ($i = 0; $i < $numberRemainingAtSymbols; ++$i) {
        $WhereClause = TranslateConCode($WhereClause, $Initials, $module);
    }

    return $WhereClause;
}
