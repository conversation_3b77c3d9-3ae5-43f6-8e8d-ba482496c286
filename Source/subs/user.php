<?php

use app\services\globals\SessionGlobalService;
use src\system\container\facade\Container;

/**
 * @desc Adds a user parameter to the database
 *
 * @param string $login The login of the user to whom this parameter is attached
 * @param string $parm The name of the parameter being changed
 * @param string $parmvalue The value of the parameter to save
 */
function SetUserParm($login, $parm, $parmvalue)
{
    if ($parmvalue === null) {
        // the field has been hidden on the form, so don't touch the data
        return;
    }

    // Delete any existing parameter value.
    $sql = 'DELETE FROM user_parms
        WHERE (parameter = :parameter
        OR parameter LIKE :paramlike)
        AND login = :login';
    DatixDBQuery::PDO_query($sql, ['parameter' => $parm, 'paramlike' => $parm . ':%', 'login' => $login]);

    if ($parmvalue == '') {
        // rather than saving a blank value, we can just leave the table empty
        return;
    }

    if ($parmvalue == 'on') {
        $parmvalue = 'Y';
    }

    // Need to cope with parameters where there are more than 254
    // characters.  To do this, split into chunks of 254 and then
    // call them PARM, PARM:1, PARM:2, etc.
    // Not sure whether this is used, so no unit test yet: @codeCoverageIgnoreStart
    $Parms = chunk_split($parmvalue, 254, '|');
    $ParmArray = explode('|', $Parms);

    $i = 1;
    $parmname = $parm;

    foreach ($ParmArray as $Parmpart) {
        if ($Parmpart !== '') {
            $insertSql = 'INSERT INTO user_parms(login, parameter, parmvalue) VALUES (:login1, :parameter1, :parmvalue1)';
            $selectSql = 'SELECT * FROM user_parms WHERE parameter = :parameter2 AND parmvalue = :parmvalue2 AND login = :login2';
            $sql = 'IF NOT EXISTS (' . $selectSql . ') ' . $insertSql;

            DatixDBQuery::PDO_query(
                $sql,
                [
                    'login1' => $login,
                    'login2' => $login,
                    'parameter1' => $parmname,
                    'parmvalue1' => $Parmpart,
                    'parameter2' => $parmname,
                    'parmvalue2' => $Parmpart,
                ],
            );
        }

        $parmname = $parm . ":{$i}";
        ++$i;
    }
}

/**
 * @desc Gets contact data array for currently logged in user.
 *
 * Since splitting users and contacts, we base the data off the user record, but present it in contact fields.
 *
 * @return array contact data array for logged in user
 */
function SetUpDefaultReporter(): array
{
    if (!$_SESSION['initials']) {
        return [];
    }

    $sql = '
        SELECT
            use_title as con_title,
            use_forenames as con_forenames,
            use_surname as con_surname,
            use_email as con_email,
            users_main.contact_id as recordid,
            users_main.contact_id as con_id,
            contacts_main.rep_approved,
            contact_addresses.line1 as con_line1,
            contact_addresses.line2 as con_line2,
            contact_addresses.line3 as con_line3,
            contact_addresses.county as con_county,
            contact_addresses.country as con_country,
            contact_addresses.postcode as con_postcode,
            contact_addresses.address as con_address,
            contact_addresses.city as con_city,
            contact_phone_numbers.phone_number as con_tel1
        FROM users_main
        LEFT JOIN contacts_main ON users_main.contact_id = contacts_main.recordid
        LEFT JOIN contact_addresses ON users_main.contact_id = contact_addresses.contact_id
        LEFT JOIN contact_phone_numbers ON users_main.contact_id = contact_phone_numbers.contact_id
        WHERE users_main.initials = :initials';

    return DatixDBQuery::PDO_fetch($sql, ['initials' => $_SESSION['initials']]);
}

/**
 * @desc Gets contact data array for currently logged in user and (because this is called from incidents)
 * populates fields used with non-contact-form-reporter section.
 *
 * @return array contact data array for logged in contact
 */
function SetUpDefaultReporterINC()
{
    global $ModuleDefs;

    if ($_SESSION['initials']) {
        $sql = 'SELECT ' .
            implode(', ', $ModuleDefs['USE']['FIELD_ARRAY']) .
            ' FROM users_main
            WHERE users_main.initials = \'' . $_SESSION['initials'] . '\'';

        $con = db_fetch_array(db_query($sql));

        $inc['inc_repname'] = $con['use_forenames']
            . ($con['use_forenames'] ? ' ' : '') . $con['use_surname'];
        $inc['inc_rep_tel'] = $con['use_tel1'];
        $inc['inc_rep_email'] = $con['use_email'];

        $ReportedBy = $_SESSION['Globals']['DIF_2_REPORTEDBY'];

        if ($ReportedBy == '') {
            $ReportedBy = 'use_subtype';
        }

        $inc['inc_reportedby'] = code_descr('CON', $ReportedBy, $con[$ReportedBy]);
    }

    return $inc;
}

function GetUserFormLevel($Module, $Perms)
{
    global $ModuleDefs;

    if ($Perms == '' || (is_array($ModuleDefs[$Module]['LEVEL1_PERMS'])
            && in_array($Perms, $ModuleDefs[$Module]['LEVEL1_PERMS']))) {
        return 1;
    }

    return 2;
}

/**
 * @deprecated
 *
 * @see \app\services\user\UserDisplayDataService::formatArrayDisplayValue()
 *
 * @param $aParams
 *
 * @return string
 *
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 */
function FormatUserNameForList($aParams, ?bool $contactSurnameSort = null, ?string $staffNameDropdown = null)
{
    $contactSurnameSort ??= bYN(Container::get(SessionGlobalService::class)->getParm('CONTACT_SURNAME_SORT', 'Y'));
    $staffNameDropdown ??= Container::get(SessionGlobalService::class)->getParm('STAFF_NAME_DROPDOWN', 'N');

    if ($contactSurnameSort) {
        $ContactName = $aParams['data']['use_surname'] . ', ' . $aParams['data']['use_title'] . ' ' . $aParams['data']['use_forenames'];
    } else {
        $ContactName = $aParams['data']['use_title'] . ' ' . $aParams['data']['use_forenames'] . ' ' . $aParams['data']['use_surname'];
    }

    if ($staffNameDropdown === 'A') {
        $ContactName .= ' - ' . $aParams['data']['use_jobtitle'];
    } elseif ($staffNameDropdown === 'B') {
        $ContactName = $aParams['data']['use_jobtitle'] . ' - ' . $ContactName;
    }

    return $ContactName;
}
