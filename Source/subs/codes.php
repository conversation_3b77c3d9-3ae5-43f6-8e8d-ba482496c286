<?php

use app\consts\Globals;
use app\models\generic\Tables;
use src\framework\registry\Registry;
use src\helpers\ModuleHelper;
use src\search\helpers\AtCodesHelper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use src\system\language\service\DefaultLanguageResolverFactory;

/**
 * @desc Maps a code to a description for a given coded field.
 *
 * @global array $FieldDefs
 *
 * @param string $module the module that the field belongs to
 * @param string $field the name of the coded field
 * @param string $value the code in question
 * @param string $table the table that the field is in (if required)
 *
 * @return string the description of the code
 */
function code_descr($module, $field, $value, $table = '', $EscapeData = true)
{
    global $FieldDefs;

    $sqlParams = [];

    if ($value == '') {
        return '';
    }

    if ($FieldDefs[$module][$field]['Type'] == 'yesno') {
        if ($value == 'Y') {
            return _fdtk('yes');
        }

        if ($value == 'N') {
            return _fdtk('no');
        }

        if ($value == 'U') {
            return _fdtk('unknown');
        }
    }

    if ($_SESSION['CachedValues']['cod_descr'][$module][$field][$value][$table ?: 'T']
        && !$GLOBALS['devNoCache']) {
        $CodeDescr = $_SESSION['CachedValues']['cod_descr'][$module][$field][$value][$table ?: 'T'];
    } else {
        if (is_array($FieldDefs[$module][$field]['fformat'])) {
            // the format is stored in FieldDefs, rather than the DB, which allows you to change fformats on-the-fly
            $row = $FieldDefs[$module][$field]['fformat'];
        } else {
            $row = getFieldFormat($field, $module, $table);
        }

        $code_table = $row['fmt_code_table'];
        $code_where = \UnicodeString::rtrim($row['fmt_code_where']);

        if ($FieldDefs[$module][$field]['FieldList']) {
            // a custom field type where the codes are fields themselves
            // TODO I think this was specific to Hotspots, so can probably be deleted
            $row['description'] = Container::get(Registry::class)->getFieldLabels()->getLabel($table, $value);
        } elseif ($code_table[0] === '!') {
            $code_type = \UnicodeString::substr($code_table, 1);

            $sql = 'SELECT COALESCE(ctd.cod_descr, ct.cod_descr) AS description
                FROM code_types as ct
                LEFT JOIN code_types_descr ctd on ct.cod_code = ctd.cod_code AND ct.cod_type = ctd.cod_type AND ctd.language = :language
                WHERE ct.cod_code = :val AND ct.cod_type = :codType';

            $languageResolver = DefaultLanguageResolverFactory::create();

            $sqlParams['language'] = $languageResolver->getUserLanguage();
            $sqlParams['codType'] = $code_type;

            $code_ok = true;
        } elseif (\UnicodeString::rtrim($row['fmt_code_descr']) != '' && \UnicodeString::rtrim($code_table) != ''
            && \UnicodeString::rtrim($row['fmt_code_field']) != '') {
            $sql = 'SELECT';

            if ($code_table == 'staff' || $code_table == 'vw_staff_combos') {
                // Need to set as staff table even though already set as vw_staff_combo,
                // since vw_staff_combos does not include closed or excluded contacts, but we still need
                // to retieve a contacts name.
                $code_table = 'staff';

                if (bYN(GetParm('CONTACT_SURNAME_SORT', 'Y'))) {
                    $coalesce_sql = "
                        coalesce(sta_surname, '') + ', ' + coalesce(sta_title, '')  + ' ' + coalesce(sta_forenames, '')
                    ";
                } else {
                    $coalesce_sql = "
                        coalesce(sta_title, '') + ' ' + coalesce(sta_forenames, '')  + ' ' + coalesce(sta_surname, '')
                    ";
                }

                if (GetParm('STAFF_NAME_DROPDOWN', 'N') == 'A') {
                    $coalesce_sql .= " + ' - ' + coalesce(jobtitle, '')";
                } elseif (GetParm('STAFF_NAME_DROPDOWN', 'N') == 'B') {
                    $coalesce_sql = " coalesce(jobtitle, '') + ' - ' + " . $coalesce_sql;
                }

                $sql .= $coalesce_sql . " AS description FROM {$code_table} WHERE {$row['fmt_code_field']} = :val";
            } elseif ($code_table === Tables::ORGANISATIONS_MAIN) {
                // Organisations table doesn't have a separate description table, so needs to be handled differently to standard code tables

                $sql .= " {$code_table}.{$row['fmt_code_descr']} AS description FROM {$code_table} WHERE {$code_table}.{$row['fmt_code_field']} = :val";
            } else {
                /** @var int $language */
                $language = $_SESSION['CurrentUser']->use_language ?? LanguageSessionFactory::getInstance()->getLanguage();

                $sql .= " {$code_table}_descr.{$row['fmt_code_descr']} AS description FROM {$code_table}_descr
                LEFT JOIN {$code_table} ON {$code_table}.code={$code_table}_descr.code WHERE {$code_table}_descr.{$row['fmt_code_field']} = :val";
                $sql .= ' AND language = ' . $language;
            }

            if (!empty($code_where)) {
                $sql .= ' AND ' . $code_where;
            }

            $code_ok = true;
        }

        $fmt_field = $row['fmt_field'];
        if ($code_ok) {
            $sqlParams['val'] = $value;
            $row = DatixDBQuery::PDO_fetch($sql, $sqlParams);

            // handle relabelled approval statuses
            $new_desc = LanguageSessionFactory::getInstance()->getStatusString($module, $value);
            if ($fmt_field == 'rep_approved' && !empty($new_desc)) {
                $row['description'] = $new_desc;
                unset($new_desc);
            }
        }

        if ($row['description'] == '') {
            $CodeDescr = $value;
        } else {
            $CodeDescr = $row['description'];
        }

        $_SESSION['CachedValues']['cod_descr'][$module][$field][$value][$table ?: 'T'] = $CodeDescr;
    }

    return $EscapeData === true ? Escape::EscapeEntities($CodeDescr) : $CodeDescr;
}

/**
 * @desc Gets the description of a coded value and the colour it should appear. Caches values to prevent multiple
 * identical queries.
 *
 * @param string $module The current module
 * @param string $field The coded field
 * @param string $value The code
 *
 * @return array an array containing the description and colour of the code
 */
function get_code_info($module, $field, $value, $table = '')
{
    if ($value == '') {
        return [];
    }

    if ($_SESSION['CachedValues']['cod_info'][$module][$table][$field][$value]) {
        return $_SESSION['CachedValues']['cod_info'][$module][$table][$field][$value];
    }

    $row = getFieldFormat($field, $module, $table);

    $code_table = $row['fmt_code_table'];
    $code_where = $row['fmt_code_where'];

    if ($code_table[0] == '!') {
        $code_type = \UnicodeString::substr($code_table, 1);

        $sql = "
                SELECT
                    cod_descr AS description,
                    cod_colour as colour,
                    cod_web_colour,
                    cod_parent,
                    cod_parent2
                FROM
                    code_types
                WHERE
                    cod_code = :value
                    AND
                    cod_type = '{$code_type}'
            ";

        $code_ok = true;
    } elseif ($row['fmt_code_descr'] != '' && $code_table != '' && $row['fmt_code_field'] != '') {
        if ($code_table == 'vw_staff_combos') {
            $code_table = 'staff';
            $row['fmt_code_field'] = str_replace('vw_staff_combos', 'staff', $row['fmt_code_field']);
        }

        $sql = "
                SELECT
                    {$row['fmt_code_descr']} AS description" .
                (bYN($row['fmt_custom_code']) || $code_table == 'staff' ? '' : ',
                    cod_colour as colour,
                    cod_web_colour,
                    cod_parent,
                    cod_parent2') . "
                FROM
                    {$code_table}
                WHERE
                    {$row['fmt_code_field']} = :value
            ";

        $code_ok = true;

        if (!empty($code_where)) {
            $sql .= ' AND ' . $code_where;
        }
    }

    $fmt_field = $row['fmt_field'];
    if ($code_ok) {
        $values = explode(' ', $value);

        if (count($values) > 1) {
            foreach ($values as $val) {
                $result = DatixDBQuery::PDO_fetch($sql, ['value' => $val]);

                if ($result) {
                    $row['description'] .= $result['description'] . ', ';
                }
            }

            $row['description'] = \UnicodeString::substr($row['description'], 0, -2);
        } else {
            $row = DatixDBQuery::PDO_fetch($sql, ['value' => $value]);
        }

        // handle relabelled approval statuses
        $new_desc = LanguageSessionFactory::getInstance()->getStatusString($module, $value);
        if ($fmt_field == 'rep_approved' && !empty($new_desc)) {
            $row['description'] = $new_desc;
            unset($new_desc);
        }
    }

    if ($row == '') {
        $row['description'] = $value;
    }

    $_SESSION['CachedValues']['cod_info'][$module][$table][$field][$value] = $row;

    // basic data satinization for output
    $row['colour'] = filter_var($row['colour'], FILTER_SANITIZE_NUMBER_INT);
    $row['cod_web_colour'] = filter_var($row['cod_web_colour'], FILTER_SANITIZE_SPECIAL_CHARS);

    return $row;
}

/**
 * @deprecated
 *
 * @see ModuleHelper::getModuleIdCodeList()
 *
 * @desc Gets a list of module codes, keyed by the ids (contrast with {@link GetModuleCodeIdList()}).
 * Includes a caching system to prevent huge numbers of queries.
 *
 * @return array<int, string> array of module codes
 */
function GetModuleIdCodeList(): array
{
    return Container::get(ModuleHelper::class)->getModuleIdCodeList();
}

function GetStatusesNoMandatory($aParams, int $level): array
{
    $queryFactory = new src\framework\query\QueryFactory();
    $query = $queryFactory->getQuery();
    $query->select(['approval_action.apac_to'])
        ->from('approval_action');

    $fieldCollection = new src\framework\query\FieldCollection();
    $fieldCollection->field('approval_action.apac_from')->eq($aParams['from'] ?: 'NEW')
        ->field('approval_action.module')->eq($aParams['module'])
        ->field('approval_action.apac_ignore_mandatory')->eq('Y')
        ->field('approval_action.apac_workflow')->eq(GetWorkflowID($aParams['module']))
        ->field('approval_action.access_level')->in($aParams['level']);

    $where = new src\framework\query\Where();


    if (shouldBypassMandatoryFields($level)) {
        $fieldCollection2 = new src\framework\query\FieldCollection();
        $fieldCollection2->field('approval_action.apac_from')->eq($aParams['from'] ?: 'NEW')
            ->field('approval_action.module')->eq($aParams['module'])
            ->field('approval_action.apac_workflow')->eq(GetWorkflowID($aParams['module']))
            ->field('approval_action.access_level')->in($aParams['level'])
            ->field('bypass_mandatory_fields')->eq('Y');
        $where->add('OR', $fieldCollection, $fieldCollection2);
    } else {
        $where->add($fieldCollection);
    }

    $query->where($where);

    [$sql, $parameters] = $queryFactory->getSqlWriter()->writeStatement($query);
    $db = new \DatixDBQuery();
    $db->setSQL($sql);
    $db->prepareAndExecute($parameters);

    return $db->fetchAll(PDO::FETCH_COLUMN);
}

function shouldBypassMandatoryFields(int $level): bool
{
    if ($level !== 2) {
        return false;
    }

    $registry = Container::get(Registry::class);

    return $registry->getParm(Globals::INC_BYPASS_MANDATORY_FIELDS, Globals::INC_BYPASS_MANDATORY_FIELDS_VAL_N)->toBool();
}

function CheckForCorrectAtCodes($WhereClause)
{
    global $FieldDefs;

    preg_match_all('/([<>=\' -]|^)(@[^<>=\' +-]+)([<>=\' +-]|$)/u', $WhereClause, $Matches);

    $helper = Container::get(AtCodesHelper::class);
    foreach ($Matches[2] as $Match) {
        if (!$helper->isAtCode($Match)) {
            $ConCodeCheck = \UnicodeString::str_ireplace('@USER_', '', $Match);

            if (!array_key_exists(\UnicodeString::strtolower($ConCodeCheck), $FieldDefs['CON'])) {
                return false;
            }
        }
    }

    return true;
}

function GetCodeDescriptions($module, $fieldname, $fielddata, $table = '', $Delimiter = '; ')
{
    $MultiCode = explode(' ', \UnicodeString::rtrim($fielddata));

    foreach ($MultiCode as $SingleCode) {
        $CodeDescriptions[] .= code_descr($module, $fieldname, $SingleCode, $table);
    }

    return implode($Delimiter, $CodeDescriptions);
}


/**
 * Iterates $data looking for code fields with can-add param. If the value
 * matches !NEWCODE!, a new code is created and $data is changed accordingly.
 *
 * @param $module
 * @param $data Post data
 * @param $FieldDefs
 *
 * @return \Post $data modified
 */
function ProcessNewCodeFieldValues($module, $data, $FieldDefs)
{
    $registry = Container::get(Registry::class);
    $moduleTable = $registry->getModuleDefs()[$module]['TABLE'];
    $defs = $registry->getFieldDefs();

    foreach ($FieldDefs[$module] as $field => $params) {
        // code field with can-add option?
        if ($params['Type'] == 'ff_select' && !empty($params['data']['can-add'])) {
            // new value?
            if (\UnicodeString::substr($data[$field], 0, 10) == '!NEWCODE!=') {
                // TODO: Add code to deal with UDF codes

                // find table
                $codeTable = $defs[$moduleTable . '.' . $field]->getCodeTable();

                // find next number
                $result = DatixDBQuery::PDO_fetch('SELECT COALESCE(MAX(CAST(code AS INTEGER))+1,1) AS nextId FROM ' . $codeTable . ' WHERE ISNUMERIC(code) = 1');

                // create new code
                $description = \UnicodeString::substr($data[$field], 10);
                $description = \UnicodeString::substr($description, 0, 254); // truncate description
                DatixDBQuery::PDO_insert("INSERT INTO {$codeTable} (code, description) VALUES (?, ?)", [$result['nextId'], $description]);
                DatixDBQuery::PDO_insert('INSERT INTO ' . $codeTable . '_descr' . ' (code, description, language) VALUES (?, ?, ?)', [$result['nextId'], $description, LanguageSessionFactory::getInstance()->getLanguage()]);
                $data[$field] = $result['nextId'];
            }
        }
    }

    return $data;
}
