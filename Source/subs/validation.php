<?php

use src\contacts\helpers\ContactsDataHelper;
use src\contacts\specifications\ContactWasFilledInSpecification;
use src\contacts\specifications\ValidNHSNumberSpecification;
use src\contacts\specifications\ValidSSNSpecification;
use src\framework\registry\Registry;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

/**
 * @desc Sets up javascript function to enforce client-side mandatory field validation.
 *
 * @param string $module The current module
 * @param obj $FormDesign the Form Design object to refer to
 *
 * @return string javascript code to be inserted into the page
 */
function MakeJavaScriptValidation($module = '', $FormDesign = '', $FieldNames = [])
{
    global $MandatoryFields, $formlevel, $FieldDefs, $ModuleDefs;

    $recordid = is_numeric($_GET['recordid']) ? Sanitize::SanitizeInt($_GET['recordid']) : '';

    if ($FormDesign) {
        $MandatoryFields = $FormDesign->MandatoryFields;
    }

    $Function = '';
    if (is_array($MandatoryFields)) {
        foreach ($MandatoryFields as $Name => $Div) {
            $fieldModule = getModuleFromField($Name);

            if ($fieldModule && is_array($FieldDefs[$fieldModule][$Name])
                && !empty($FieldDefs[$fieldModule][$Name]['MandatoryField'])) {
                $MandatoryName = $FieldDefs[$fieldModule][$Name]['MandatoryField'];
            } else {
                $MandatoryName = $Name;
            }

            $SectionSuffixArray = ['contact_reporter' => 3, 'assailant' => 4, 'police_officer' => 5];

            $FormFieldName = '';
            if (isset($SectionSuffixArray[$Div])) {
                $FormFieldName = $Name;
                $Name = RemoveSuffix($Name);
                $Suffix = $SectionSuffixArray[$Div];
            }

            // Use form design label if available
            if (!empty($FormDesign->UserLabels[$Name])) {
                $FieldName = $FormDesign->UserLabels[$Name];
            } else {
                // get label for UDF fields
                if (preg_match('/^UDF.*_([0-9]+)$/ui', $Name, $matches)) {
                    $FieldName = (new Fields_ExtraField($matches[1]))->getLabel();
                } else {
                    $FieldName = Labels_FormLabel::GetFormFieldLabel($Name, '', $fieldModule, $FormFieldName, $ModuleDefs[$fieldModule]['TABLE'] ?? null);
                }
            }

            if ((($Name == 'inc_mgr' && empty($FormDesign->FieldLabels['inc_mgr']))
                    || ($Name == 'com_mgr' && empty($FormDesign->FieldLabels['com_mgr']))
                    || ($Name == 'pal_handler' && empty($FormDesign->FieldLabels['pal_handler']))) && empty($_SESSION['logged_in'])) {
                // hack because we have no way at the moment of dealing with a field that
                // appears in two places with different names.
                $FieldName = 'Your Manager';
            }

            if (empty($FieldName)) {
                $FieldName = $FieldNames[$Name] ?? null;
                if (empty($FieldName)) {
                    continue;
                }
            }

            $Function .= 'mandatoryArray.push(new Array("' . $MandatoryName . '","' . $Div . '","' . CompatEscaper::encodeCharacters($FieldName) . '"));
            ';

            // Special case for reasons for rejection section:
            // Normally mandatory fields are ignored when saving rejected incidents, but in this case they need to be enforced.
            // If we can find a non-hard-coded way of setting this up, we should do it.
            if (in_array($Name, ['rea_code', 'rea_text'])) {
                $Function .= 'mandatoryArraySpecial[\'REJECT\'].push(new Array("' . $MandatoryName . '","' . $Div . '","' . Labels_FormLabel::GetFormFieldLabel($Name, '', $fieldModule, $FormFieldName) . '"));
                ';
            }
        }
    }

    $Function .= 'function validateOnSubmit() {
        if (' . (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] ? 'true' : 'false') . ') {
            const token = sessionStorage.getItem(\'datix.jwtToken\');

            if (timeLeftTillJWTTokenExpires(token) <= 5000) { // give it time for the actual network call
                alert(\'Your session token has expired. Please contact your Datix administrator.\');
                return false;
            }
        }

        if(preventSubmit || !submitClicked) {
            return false;
        }

        const rbWhat = document.forms[0].rbWhat;
        if(rbWhat) {
            switch(rbWhat.value) {
                case "Cancel":
                case "BackToListing":
                case 16:
                case "Delete":
                return true;
            }
        }

        const isLevelTwoForm = ' . ($formlevel == 2 || $formlevel == '' ? 'true' : 'false') . ';
        const isRepApproved = ' . (($ModuleDefs[$module]['NO_REP_APPROVED'] ?? false) ? 'false' : 'true') . ';
        let isValid = dtxValidateOnSubmit(isLevelTwoForm, isRepApproved);

        // check whether feedback validation required
        const module = ' . ($module ? '"' . $module . '"' : 'null') . ';
        const recordid = "' . ($recordid ?: 'null') . '";
        if(isValid && recordid && module && FeedbackValidationNeeded) {
            isValid = DoFeedbackValidation(module, recordid);
        }
        return isValid;
    }';

    return $Function;
}

/**
 * @desc Picks up validation messages from $_POST (via the $data array) and $_SESSION for a particular field
 * and returns them wrapped in the appropriate HTML.
 *
 * @param array $Data array data for the current record
 * @param string $InputFieldName the field to check for validation errors
 *
 * @return string HTML validation error to be inserted into the page
 */
function GetValidationErrors($Data, $InputFieldName, $Parameters = [])
{
    if (isset($Data['error']['Validation']) && is_array($Data['error']['Validation'])) {
        $ErrorArray = $Data['error']['Validation'][$InputFieldName] ?? [];
    }

    if (
        empty($ErrorArray)
        && isset($_SESSION['MESSAGES']['VALIDATION'])
        && is_array($_SESSION['MESSAGES']['VALIDATION'])
    ) {
        $ErrorArray = $_SESSION['MESSAGES']['VALIDATION'][$InputFieldName] ?? [];
        $_SESSION['MESSAGES']['VALIDATION'][$InputFieldName] = [];
    }

    $tag = (!empty($Parameters['inline']) ? 'span' : 'div');

    $ErrorText = '';
    if (!empty($ErrorArray)) {
        if (is_array($ErrorArray)) {
            foreach ($ErrorArray as $Error) {
                $ErrorText .= '<' . $tag . ' class="badge badge-error badge-red"> '
                    . $Error .
                    '</' . $tag . '>';
            }
        } else {
            $ErrorText = '<' . $tag . ' class="field_error"><img src="images/Warning.gif" /> '
                . $ErrorArray .
                '</' . $tag . '>';
        }
    }

    return $ErrorText;
}

function ValidateInjuryData($aParams)
{
    $error = [];

    if (isset($aParams['data']['injury' . $aParams['suffixstring']]) && is_array($aParams['data']['injury' . $aParams['suffixstring']])) {
        foreach ($aParams['data']['injury' . $aParams['suffixstring']] as $id => $injury) {
            if ($injury == '' && $aParams['data']['bodypart' . $aParams['suffixstring']][$id] != '') {
                $error['injury' . $aParams['suffixstring']] = 'Please enter the injury that this body part relates to.';
            }
        }
    }

    return $error;
}

function ValidatePostedMoney($module, $suffix = '')
{
    global $FieldDefs;

    $SuffixText = '';
    if ($suffix) {
        $SuffixText = '_' . $suffix;
    }

    $error = [];

    $moneyFields = array_merge(getAllUdfFieldsByType('M', $SuffixText, $_POST), GetAllFieldsByType($module, 'money'));

    foreach ($moneyFields as $Field) {
        // These pay_type_ fields don't need validation. They're not real fields
        if (preg_match('/pay_type_/', $Field)) {
            continue;
        }

        if (empty($FieldDefs[$module][$Field]['Computed'])) {
            if (!empty($_POST["{$Field}{$SuffixText}"])
                && !preg_match('/^-?(' . preg_quote(GetParm('CURRENCY_CHAR', '£')) . ')?-?[0-9,\.]+$/u', $_POST["{$Field}{$SuffixText}"])) {
                $error["{$Field}{$SuffixText}"] = 'Invalid character found';
            }

            if ((float) preg_replace('/[^0-9\.\-]/', '', $_POST[$Field . $SuffixText] ?? '') > 99999999999.99) {
                $error["{$Field}{$SuffixText}"] = _fdtk('money_larger');
            }
        }
    }

    return $error;
}

/**
 * @desc Sanitises the $_GET array, ensuring that there are no SQL-injections being attempted.
 * At the moment we only check recordid and module, though we will clearly need to expand this to other values
 * in future versions.
 */
function ValidateURL()
{
    $_GET['recordid'] = ValidateValue(['value' => $_GET['recordid'] ?? null, 'type' => 'int']);
    $_GET['module'] = ValidateValue(['value' => $_GET['module'] ?? null]);
}

/**
 * @desc Takes a value and expected type and returns a sanitised version of the value without any unexpected
 * data.
 *
 * @param array $aParams Array of parameters
 *
 * @return string Validated input
 */
function ValidateValue($aParams)
{
    if ($aParams['value']) {
        if (($aParams['type'] ?? null) === 'string') { // no validation to do - everything is allowed
            return $aParams['value'];
        }
        if (($aParams['type'] ?? null) === 'int') {
            return preg_replace('/[^0-9]/u', '', $aParams['value']);
        }

        // default: get rid of spaces, hyphens and quotes
        return preg_replace('/[ \-\'\"]/u', '', $aParams['value']);
    }
}

/**
 * Validates an NHS number.
 *
 * Validation can be toggled on/off with global 'VALID_NHSNO'.
 * Determines whether this contact has a valid NHS number (using {@link ValidNHSNumberSpecification})
 * and produces an error message if not.
 *
 * @global array of user-definable text.
 *
 * @param array $error the current error array as created by previous contacts validation
 *
 * @return array $error contains any error information resulting from invalid data
 */
function ValidateNHSNumber($aParams, $error)
{
    if (!is_array($aParams['data'])) {
        $aParams['data'] = $_POST;
    }

    $registry = Container::get(Registry::class);
    if ($registry->getParm('MULTI_ID_NUMBER_SECTION', 'N')->isTrue()
        && $registry->getParm('VALID_NHSNO', 'Y')->isTrue()) {
        $sql = 'SELECT code FROM code_con_number_type WHERE cod_format = :nhsnumber';
        $parameters = ['nhsnumber' => 'NHS_NUMBER'];
        $nhsNumberTypes = DatixDBQuery::PDO_fetch_all($sql, $parameters, PDO::FETCH_COLUMN);
        $suffix = $aParams['suffix'] ?? 0;
        if (isset($aParams['data']['idNumbers'][$suffix]) && is_array($aParams['data']['idNumbers'][$suffix])) {
            foreach ($aParams['data']['idNumbers'][$suffix] as $index => $idNumber) {
                if (in_array($idNumber['type'], $nhsNumberTypes)) {
                    if (!(new ValidNHSNumberSpecification())->isSatisfiedBy($idNumber['number'])) {
                        $error['con_id_number_' . $suffix . '_' . $index] = _fdtk('enter_valid_nhs_number');
                        $error['message'] = _fdtk('error_invalid_nhs_number');
                    }
                }
            }
        }
    } else {
        $field = isset($aParams['suffix']) ? 'con_nhsno_' . $aParams['suffix'] : 'con_nhsno';

        if (bYN(GetParm('VALID_NHSNO', 'Y')) && array_key_exists($field, $aParams['data'])
            && $aParams['data'][$field] != '') {
            if (!(new ValidNHSNumberSpecification())->isSatisfiedBy($aParams['data'][$field])) {
                $error[$field] = _fdtk('enter_valid_nhs_number');
            }
        }
    }

    return $error;
}

/**
 * Validates Social Security Number.
 *
 * @param $aParams
 * @param $error
 */
function validateSSN($aParams, $error)
{
    if (!is_array($aParams['data'])) {
        $aParams['data'] = $_POST;
    }

    $field = isset($aParams['suffix']) ? 'con_social_security_number_' . $aParams['suffix'] : 'con_social_security_number';

    if (array_key_exists($field, $aParams['data']) && $aParams['data'][$field] != '') {
        if (!(new ValidSSNSpecification())->isSatisfiedBy($aParams['data'][$field])) {
            $error[$field] = _fdtk('valid_ssn');
        }
    }

    return $error;
}

function ValidateLinkedContactData(array $data, string $module): array
{
    $usedSuffixes = Container::get(ContactsDataHelper::class)->getContactSuffixesFromFormData($data);

    $error = [];

    if (empty($usedSuffixes)) {
        return [];
    }

    $contactHasDataSpec = new ContactWasFilledInSpecification();

    foreach ($usedSuffixes as $contactSuffix) {
        if (!ContactHidden($data, $contactSuffix, $module) && $contactHasDataSpec->isSatisfiedBy($data, $contactSuffix)) {
            $NewErrors = ValidateContactData(['data' => $data, 'suffix' => $contactSuffix]);
            $injuryErrors = ValidateInjuryData(['data' => $data, 'suffixstring' => '_' . $contactSuffix]);

            $error = array_merge($error, $NewErrors, $injuryErrors);
        }
    }

    return $error;
}
