<?php

use app\models\accessLevels\AccessLevelResolver;
use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\generic\Tables;
use app\Repository\UdfValuesRepository;
use app\services\webColour\WebColourRepositoryFactory;
use Psr\SimpleCache\CacheInterface;
use Psr\Log\LoggerInterface;
use Source\generic_modules\ModuleDefKeys;
use src\combolinking\services\ComboLinkServiceFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\helpers\SqlInClauseHelper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;

/**
 * @desc Returns the contents of the field_formats table for a particular field.
 *
 * @param string $field The name of the field to be retrieved
 * @param string $module The module this field is in
 * @param string $table The table this field is in
 *
 * @return array record data from the field_formats table
 */
function getFieldFormat(
    $field,         // field_formats.fmt_module
    $module = '',   // field_formats.fmt_field
    $table = ''     // field_formats.fmt_table
) {
    global $ModuleDefs;

    if (!$field || $module == '') {
        return null;
    }

    $field = \UnicodeString::strtolower($field);
    $table = \UnicodeString::strtolower($table);

    if ($module == 'ADM') {
        $module = 'CON';
    }

    if (empty($table) && !empty($module) && !empty($ModuleDefs[$module][ModuleDefKeys::TABLE])) {
        $table = $ModuleDefs[$module][ModuleDefKeys::TABLE];
    }

    $cache = Container::get(CacheInterface::class);

    if (empty($cache->get('ff_loaded_' . $module))) {
        InitFieldFormats($module);
    }

    $cacheKey = Tables::FIELD_FORMATS . '_' . $module . '_' . $table . '_' . $field;

    // hack for payment summary screen
    if ($module == 'CLA' && \UnicodeString::substr($field, 0, 9) == 'pay_type_') {
        if (!$cache->has($cacheKey)) {
            $ExplodeField = explode('_', $field);
            $ColName = DatixDBQuery::PDO_fetch('SELECT description from code_fin_type WHERE code = :code', [
                'code' => $ExplodeField[2],
            ], PDO::FETCH_COLUMN) . ' (Payment summary)';

            $cache->set($cacheKey, [
                'fmt_module' => 'CLA',
                'fmt_title' => $ColName,
                'fmt_data_type' => 'M',
            ]);
        }

        return $cache->get($cacheKey);
    }

    return $cache->get($cacheKey);
}

/**
 * @desc Initialise field formats into Cache
 *
 * @param string $module the module to be loaded
 *
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\SimpleCache\InvalidArgumentException
 */
function InitFieldFormats($module = '')
{
    $cache = Container::get(CacheInterface::class);

    $sql = 'SELECT * FROM field_formats WHERE fmt_module = :module';

    $result = DatixDBQuery::PDO_fetch_all($sql, ['module' => $module]);

    foreach ($result as $fformats) {
        if (is_array($fformats) && !empty($fformats)) {
            $fformats = array_change_key_case($fformats, CASE_LOWER);
        }

        $cacheKey = Tables::FIELD_FORMATS . '_' . $module . '_' . \UnicodeString::strtolower($fformats['fmt_table']) . '_' . \UnicodeString::strtolower($fformats['fmt_field']);
        $cache->set($cacheKey, $fformats);
    }

    $cache->set('ff_loaded_' . $module, 1);
}
/**
 * @desc MakeUDFFieldWhere puts a where clause into $udf, by hijacking the MakeFieldWhere
 *  function. The where clause checks both that the value in the udf_values table is appropriate
 *  and that the udf field would appear on the record.
 *
 * @param array $explodeArray the udf field name split by '_' into an array
 * @param string $value the value this field holds
 * @param int $module_num The module we are searching in
 * @param string $search reference to a variable which will be populated by the where clause
 */
function MakeUDFFieldWhere(
    $explodeArray,
    $value,
    $module_num,
    &$search // The where clause returned
) {
    $moduleDefs = Container::get(ModuleDefs::class);

    if ($value == '') {
        return true;
    }

    $module = ModuleIDToCode($module_num);
    $table = $moduleDefs[$module]->getDbReadObj();

    $fieldid = $explodeArray[3];
    $groupid = $explodeArray[2];
    $Type = $explodeArray[1];

    $FieldNames = [
        'Y' => 'udv_string',
        'D' => 'udv_date',
        'S' => 'udv_string',
        'N' => 'udv_number',
        'M' => 'udv_money',
        'T' => 'udv_multilistbox',
        'L' => 'udv_text',
    ];

    if (is_array($value)) {
        $value = implode('|', $value);
    }

    if (!isset($FieldNames[$Type])) {
        $Type = 'S';
    }

    $valid = MakeFieldWhere($module, $FieldNames[$Type], $value == '=' ? '==' : $value, $FieldWhere);

    if ($value == '=') {
        $search = 'NOT ((' . $table . '.recordid IN (SELECT cas_id FROM udf_values WHERE field_id = ' . $fieldid . ($groupid !== '' ? ' AND group_id = ' . $groupid : '') . ' AND mod_id = ' . $module_num . ' AND ' . $FieldWhere . ')))';
    } else {
        $search = '(' . $table . '.recordid IN (SELECT cas_id FROM udf_values WHERE field_id = ' . $fieldid . ($groupid !== '' ? ' AND group_id = ' . $groupid : '') . ' AND mod_id = ' . $module_num . ' AND ' . $FieldWhere . '))';
    }

    return $valid;
}

/**
 * @desc Retrieved the fld_code_like value from udf_fields for a given field id. Uses caching to prevent multiple calls.
 *
 * @return string The value of the fld_code_like field for the given field id.
 *                This value is the name of the main-table field this extra field is linked to for coding purposes.
 */
function GetFieldCodeLike($FieldID)
{
    if (!isset($_SESSION['CachedValues']['fld_code_like'])) {
        $result = DatixDBQuery::PDO_fetch_all('SELECT recordid, fld_code_like FROM udf_fields', [], PDO::FETCH_KEY_PAIR);

        $_SESSION['CachedValues']['fld_code_like'] = $result;
    }

    return $_SESSION['CachedValues']['fld_code_like'][$FieldID] ?? '';
}

/**
 * @desc Returns array of approval status code/description pairs for a given module.
 *
 * @param string $Module the module in question
 *
 * @return array array of approval status code/description pairs
 */
function GetLevelFieldLabels($Module)
{
    $languageId = LanguageSessionFactory::getInstance()->getLanguage();
    $sql = 'SELECT
                code_approval_status.code, code_approval_status.description as descriptionLabel, code_approval_status_descr.description, code_approval_status.cod_web_colour
             FROM
                code_approval_status
             LEFT JOIN
                code_approval_status_descr
                ON code_approval_status.code = code_approval_status_descr.code
                AND code_approval_status.module = code_approval_status_descr.module
                AND code_approval_status_descr.language = :lang
             WHERE
                code_approval_status.module = :module
             AND
                code_approval_status.workflow = :workflowid';


    $result = DatixDBQuery::PDO_fetch_all(
        $sql,
        [
            'module' => $Module,
            'workflowid' => GetWorkflowID($Module),
            'lang' => $languageId,
        ],
    );

    $FieldLabels = [];
    $webColourRepository = (new WebColourRepositoryFactory())->create();

    foreach ($result as $row) {
        $FieldLabels[$row['code']]['description'] = $row['description'] ?: $row['descriptionLabel'];
        $FieldLabels[$row['code']]['colour'] = $row['cod_web_colour'];
    }

    return $FieldLabels;
}

function MakeProfileSetupField($value, $FieldType = '')
{
    global $UserLabels;

    $sql = 'SELECT recordid, pfl_name FROM profiles ORDER BY pfl_name ASC';
    $result = db_query($sql);

    $Profiles = [];

    while ($row = db_fetch_array($result)) {
        $Profiles[$row['recordid']] = $row['pfl_name'];
    }

    $Title = ($UserLabels['ADM_PROFILES'] ?? 'Profiles');

    $field = \src\component\field\SelectFieldFactory::createSelectField('ADM_PROFILES', 'ADM', $value, $FieldType, true, $Title);
    $field->setCustomCodes($Profiles);
    $field->setIgnoreMaxLength();

    return $field->getField();
}

function MakeSavedQueriesSetupField($value, $FieldType, $formType, $module)
{
    global $UserLabels;

    $parameterName = $module . '_SAVED_QUERIES';

    $savedQueries = getSavedQueriesAccessibleToEveryone($module);

    if (!$savedQueries) {
        $savedQueries = [];
    }

    $Title = $UserLabels[$parameterName] ?? _fdtk('choose_saved_queries');

    if ($formType != 'ReadOnly') {
        $field = \src\component\field\SelectFieldFactory::createSelectField($parameterName, $module, $value, $FieldType, true, $Title);
        $field->setCustomCodes($savedQueries);

        return $field->getField();
    }

    return null;
}

function MakeProfileField($value, $FieldType = '')
{
    $sql = 'SELECT recordid, pfl_name FROM profiles WHERE 1=1 ';
    $params = [];
    $ADMProfiles = Container::get(Registry::class)->getParm('ADM_PROFILES')->toArray();
    if ($ADMProfiles) {
        $placeholders = '';
        foreach ($ADMProfiles as $key => $profileId) {
            $placeholders .= ':admProfile_' . $key . ', ';
            $params['admProfile_' . $key] = $profileId;
        }
        $sql .= ' AND recordid IN (' . substr($placeholders, 0, -2) . ') ';
    }

    $currentUser = (new UserSessionFactory())->create();
    if (!$currentUser->isFullAdmin()) {
        $sql .= " AND recordid NOT IN (SELECT lpp_profile FROM link_profile_param WHERE lpp_parameter = 'FULL_ADMIN' AND lpp_value = 'Y') ";
    }

    $sql .= ' ORDER BY pfl_name ASC';

    $result = DatixDBQuery::PDO_fetch_all($sql, $params);

    $Profiles = [];

    foreach ($result as $row) {
        $Profiles[$row['recordid']] = $row['pfl_name'];
    }

    $value = !empty($value) ? $value : '';

    $field = SelectFieldFactory::createSelectField('sta_profile', '', $value, $FieldType);
    $field->setCustomCodes($Profiles);

    return $field->getField();
}

/**
 * @desc Loops through FieldDefs and picks out all fields for a given module and type - used when we
 * need to do particular actions on particular types of fields (e.g. validation when saving).
 *
 * @param string $module The current module
 * @param string $type The type of fields to check for
 * @param bool $useFieldDefsExtra Whether to use FieldDefsExtra, or just FieldDefs
 *
 * @return array Array of fields that match the given type
 */
function GetAllFieldsByType($module, $type, $useFieldDefsExtra = true)
{
    $FieldDefs = $useFieldDefsExtra ? $GLOBALS['FieldDefsExtra'] : $GLOBALS['FieldDefs'];

    $FieldArray = [];

    if (is_array($FieldDefs[$module])) {
        foreach ($FieldDefs[$module] as $FieldName => $Properties) {
            if (($Properties['Type'] ?? null) == $type) {
                $FieldArray[] = $FieldName;
            }
        }
    }

    return $FieldArray;
}

/**
 * Loops through the current form fields and picks out UDFs of a certain type.
 *
 * @param string $type the field type to check for
 * @param string $suffix identifier for linked data
 * @param array $data the form data
 *
 * @return array $fields  array of fields that match the given type
 */
function getAllUdfFieldsByType($type, $suffix, $data)
{
    $fields = [];

    foreach ($data as $field => $value) {
        if (\UnicodeString::substr($field, 0, 4) == 'UDF_') {
            $udfParts = explode('_', $field);

            if (isset($udfParts[1]) && $udfParts[1] == $type && ((!$suffix && empty($udfParts[4])) || ('_' . ($udfParts[4] ?? '') == $suffix))) {
                $fields[] = $field;
            }
        }
    }

    if ($suffix) {
        $fields = array_map('RemoveSuffix', $fields);
    }

    return $fields;
}

/**
 * Returns a FormField object containing a dropdown list of all
 * available managers.  Finds all of those whose perms are in PermArray.
 */
function MakeManagerDropdownGeneric($Module, $Data, $FieldMode, $PermArray, $ContactSuffix = '')
{
    global $ModuleDefs;

    $registry = Container::get(Registry::class);

    if ($ContactSuffix) {
        $ContactSuffix = '_' . $ContactSuffix;
    }

    if ($registry->getParm('STAFF_EMPL_FILTERS', 'N', true)->isTrue()) {
        $MgrField = $ModuleDefs[$Module]['FIELD_NAMES']['HANDLER'];

        $FieldObj = \src\component\field\SelectFieldFactory::createSelectField(
            $MgrField,
            $Module,
            $Data[$MgrField],
            $FieldMode,
            false,
            _fdtk('your_manager'),
            'your_manager',
            $Data['CHANGED-' . $MgrField],
            $ContactSuffix,
        );

        $staffFieldParents = (new ComboLinkServiceFactory())->create()->getStaffParentFieldNames($Module);

        $FieldObj->setParents($staffFieldParents);
    } else {
        $sql = "SELECT recordid, initials, fullname, use_jobtitle, use_forenames,
            use_surname, use_title
            FROM users_main
            WHERE use_email IS NOT NULL
            AND users_main.login IS NOT NULL
            AND use_dclosed is null
            AND initials IS NOT NULL AND initials <> ''
            AND (use_staff_include IS NULL OR use_staff_include != 'N')
            ";

        $usePermWhere = MakeSecurityWhereClause('', 'USE', $_SESSION['initials'] ?? '');

        if ($usePermWhere) {
            $sql .= ' AND ' . $usePermWhere;
        }

        $sql .= GetUserListOrderBy();

        $userList = DatixDBQuery::PDO_fetch_all($sql);

        $MgrArray = [];

        $accessLevelResolver = Container::get(AccessLevelResolver::class);

        foreach ($userList as $row) {
            $userAccessLevels = $accessLevelResolver->getUserAccessLevels($row['recordid']);

            $permissionGlobalName = $ModuleDefs[$Module]['PERM_GLOBAL'];

            if (in_array($userAccessLevels[$permissionGlobalName], $PermArray)) {
                $MgrArray[$row['initials']] = FormatUserNameForList(['data' => $row]);
            }
        }

        // Value is stored under inc_mgr in database, so need to add suffix back on.
        if ($Module == 'INC' && empty($Data['inc_mgr' . $ContactSuffix])) {
            $Data['inc_mgr' . $ContactSuffix] = $Data['inc_mgr'] ?? '';
        }

        $ManagerField = $ModuleDefs[$Module]['FIELD_NAMES']['HANDLER'] . $ContactSuffix;

        $FieldObj = \src\component\field\SelectFieldFactory::createSelectField(
            $ManagerField,
            $Module,
            $Data[$ManagerField] ?? '',
            $FieldMode,
            false,
            _fdtk('your_manager'),
            'your_manager',
            $Data['CHANGED-' . $MgrField] ?? '',
            $ContactSuffix,
        );
        $FieldObj->setCustomCodes($MgrArray);
    }

    return $FieldObj;
}

function getModuleFromField($TargetField)
{
    global $FieldDefs;

    foreach ($FieldDefs as $module => $aFields) {
        if (isset($aFields[$TargetField])) {
            return $module;
        }
    }

    return '';
}

function AddUDFValueToDataArray($aParams, &$Data): void
{
    $formObject = $aParams['formobj'] ?? null;

    if ($formObject instanceof FormTable && $formObject->getFormMode() === FormTable::MODE_SEARCH) {
        return;
    }

    $fieldName = $aParams['fieldname'] ?? null;
    if (!$fieldName) {
        return;
    }

    $module = $aParams['module'] ?? null;
    if (!$module) {
        return;
    }

    $recordId = $Data['recordid'] ?? null;
    if (!is_numeric($recordId)) {
        return;
    }

    $udfValuesRepository = Container::get(UdfValuesRepository::class);
    $moduleDefs = Container::get(ModuleDefs::class);

    $fieldNameParts = explode('_', $fieldName);
    $modId = $moduleDefs->getModuleData($module)->getID();

    $udfValueArray = $udfValuesRepository->getValue(
        (int) $recordId,
        (int) $fieldNameParts[3],
        (int) $modId,
    );

    $Data[$aParams['fieldname']] = $udfValueArray['udv_string'] ?? null;
}

function CheckFieldMappings($Module, $Field, $Reverse = false)
{
    global $ModuleDefs;

    if (isset($ModuleDefs[$Module]['FIELD_MAPPINGS']) && is_array($ModuleDefs[$Module]['FIELD_MAPPINGS'])) {
        if ($Reverse) {
            $ReverseFieldMapping = array_flip($ModuleDefs[$Module]['FIELD_MAPPINGS']);

            if (!empty($ReverseFieldMapping[$Field])) {
                return $ReverseFieldMapping[$Field];
            }
        } elseif (isset($ModuleDefs[$Module]['FIELD_MAPPINGS'][$Field])) {
            return $ModuleDefs[$Module]['FIELD_MAPPINGS'][$Field];
        }
    }

    return $Field;
}

function IncludeFieldDefs()
{
    global $FieldDefs, $FieldDefsExtra;

    require_once 'Source/FieldDefs.php';
}

function getUserSecTitleDropdown()
{
    $ExcludeArray = ['DAS', 'ORG', 'USE'];

    if (!(new UserSessionFactory())->create()->isFullAdmin()) {
        $ExcludeArray[] = 'ADM';
    }

    // This should be refactored to use module groups.
    $AdditionalArray = [
        'ALL_MODULES' => _fdtk('profile_all_modules'),
    ];

    $HTML = getModuleDropdown([
        'name' => 'parametersModule',
        'onchange' => 'showModuleSection(jQuery(\'#parametersModule\').val(), \'parameters\')',
        'current' => 'ALL',
        'not' => $ExcludeArray,
        'add' => $AdditionalArray,
        'use_menu_names' => true,
    ]);

    getSecuritySettingJS();

    return $HTML;
}

function AddSuffixToFields($aFields, $suffix)
{
    $aFieldsWithSuffix = [];

    foreach ($aFields as $sFieldName) {
        if ($suffix != '') {
            $sFieldName = $sFieldName . $suffix;
        }

        $aFieldsWithSuffix[] = $sFieldName;
    }

    return $aFieldsWithSuffix;
}

/**
 * Adds a custom help bubble for a non-standard form.
 *
 * Used in e.g. Capture configuration where there is no form design
 *
 * @param array $aParams array of required data
 *
 * @return string The HTML code for the actual help link
 */
function AddHelpBubble($aParams)
{
    global $scripturl;

    $_SESSION['CustomHelpTexts'][$aParams['field']] = $aParams['help'];

    return '
        <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $aParams['title'] . '\', \'' . $scripturl . '?action=fieldhelp&module=ADM&level=2&field=' . $aParams['field'] . '\', \'\', \'\', \'300px\')">
            <img id="' . $aParams['field'] . '_help_image" src="images/Help.gif" style="cursor:pointer" alt="Help (' . $aParams['title'] . ')"  style="border:0" />
        </a>';
}

/**
 * @desc Gets an array of fields which should not be appearing in the reporting setup. This is
 * a hard coded hack because some "user" fields are classed as "contact" fields and so are available
 * for contact reporting as soon as they are added to field formats.
 *
 * @return array array of fields to exclude
 */
function GetHardCodedReportExcludedFields()
{
    return [
        'use_staff_include',
        'sta_lockout_dt',
        'sab_issued_time',
        'pay_vat',
        'act_chain_id',
        'act_chain_instance',
        'act_step_no',
        'act_start_after_step',
        'act_start_after_days_no',
        'act_due_days_no',
        'updateddate',
        'con_hourly_rate',
        'con_hours_worked',
        'con_ncci_code',
        'con_occupation',
        'con_marital_status',
    ];
}

/**
 * Temporary function required for retrieving the table from the field directory for a field
 * by checking against the main module table fields, otherwise assuming only a maximum of one other duplicate
 * entry for the field.
 * E.g. inc_injury can be found in the incidents_main and inc_injuries.
 * This function should be deprecated once all parts of the system has been converted to use field_directory and
 * form design stores the table information.
 *
 * @param string Field to find table for
 * @param string module for field
 *
 * @return string table for field
 */
function GetTableForField($Field, $Module)
{
    global $ModuleDefs;

    if (in_array($Field, $ModuleDefs[$Module]['FIELD_MAPPINGS'] ?? [], true)) {
        $Field = array_flip($ModuleDefs[$Module]['FIELD_MAPPINGS'])[$Field];
    }

    if (in_array($Field, $ModuleDefs[$Module]['VIEW_FIELD_ARRAY'] ?? [], true)) {
        $Table = $ModuleDefs[$Module]['VIEW'];
    } elseif (in_array($Field, $ModuleDefs[$Module]['FIELD_ARRAY'] ?? [], true) || $Field === 'recordid') {
        $Table = $ModuleDefs[$Module]['TABLE'];
    } else {
        $cache = Container::get(CacheInterface::class);
        $cacheKey = 'TableForFields';

        $tableForFields = $cache->get($cacheKey);

        if (empty($tableForFields)) {
            $sql = 'SELECT fdr_name, fdr_table FROM field_directory WHERE fdr_table != :Table';
            // Default table required because Persons Affected doesn't send a module resulting in NULL and 0 results
            $PDOParams = ['Table' => $ModuleDefs[$Module]['TABLE'] ?? 'incidents_main'];

            $Rows = DatixDBQuery::PDO_fetch_all($sql, $PDOParams);

            $tableForFields = [];
            foreach ($Rows as $Row) {
                if (!isset($tableForFields[$Row['fdr_name']])) {
                    $tableForFields[$Row['fdr_name']] = $Row['fdr_table'];
                }
            }

            $cache->set($cacheKey, $tableForFields);
        }

        $Table = $tableForFields[$Field] ?? '';
    }

    return $Table;
}

/**
 * Rubbish procedural solution to enable udf display in listings by pulling the data out only for the records that are required,
 * rather than risking messing up the core listing query. Used in e.g. BrowseList.php when we need to have the value formatted for display.
 *
 * @param $module
 * @param $recordids
 * @param $udf_field_ids
 *
 * @return array
 */
function getUDFDisplayData($module, $recordids, $udf_field_ids)
{
    $udfData = [];
    $sqlHelper = new SqlInClauseHelper();

    if (empty($recordids) || empty($udf_field_ids)) {
        return [];
    }

    $udfRawData = DatixDBQuery::PDO_fetch_all(
        'SELECT cas_id, field_id, fld_type, fld_format, udv_string, udv_text, udv_number, udv_money, udv_date FROM udf_values
            LEFT JOIN udf_fields ON udf_fields.recordid = udf_values.field_id
            WHERE ' . $sqlHelper->inClause('field_id', $udf_field_ids) . '
                    AND ' . $sqlHelper->inClause('udf_values.cas_id', $recordids) . '
                    AND udf_values.mod_id = :mod_id AND group_id = 0',
        ['mod_id' => ModuleCodeToID($module)],
    );

    foreach ($udfRawData as $udfRow) {
        $value = '';
        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
        switch ($udfRow['fld_type']) {
            case 'S':
                $value = $udfRow['udv_string'];

                break;
            case 'Y':
            case 'C':
                $value = $codeInfoRetriever->retrieve('UDF_' . $udfRow['field_id'], $udfRow['udv_string'])->getDescription();

                break;
            case 'T':
                $multiCodes = [];
                foreach (explode(' ', $udfRow['udv_string']) as $multiCode) {
                    $code = $codeInfoRetriever->retrieve('UDF_' . $udfRow['field_id'], $multiCode);
                    $multiCodes[] = $code->getDescription();
                }
                $value = implode(', ', $multiCodes);

                break;
            case 'D':
                $value = formatDateForDisplay($udfRow['udv_date']);

                break;
            case 'N':
                // If format specified in UDF setup, then try and emulate. Otherwise use default format.
                if ($udfRow['fld_format'] != '') {
                    $value = GuptaFormatEmulate($udfRow['udv_number'], $udfRow['fld_format']);
                } else {
                    $value = number_format((float) $udfRow['udv_number'], 2, '.', '');
                }

                break;
            case 'M':
                $value = FormatMoneyVal($udfRow['udv_money']);

                break;
            case 'L':
                $value = $udfRow['udv_text'];

                break;
        }

        $udfData[$udfRow['cas_id']][$udfRow['field_id']] = $value;
    }

    return $udfData;
}

/**
 * Rubbish procedural solution to enable udf display in listings by pulling the data out only for the records that are required,
 * rather than risking messing up the core listing query. Used when we are going to use ListingDisplayClass to format for display,
 * so just returns the value.
 *
 * @param $module
 * @param $recordids
 * @param $udf_field_ids
 *
 * @return array
 */
function getUDFData($module, $recordids, $udf_field_ids)
{
    $udfData = [];
    $sqlHelper = new SqlInClauseHelper();

    if (empty($recordids) || empty($udf_field_ids)) {
        return [];
    }

    $udfRawData = DatixDBQuery::PDO_fetch_all(
        'SELECT cas_id, field_id, fld_type, fld_format, udv_string, udv_text, udv_number, udv_money, udv_date FROM udf_values
            LEFT JOIN udf_fields ON udf_fields.recordid = udf_values.field_id
            WHERE ' . $sqlHelper->inClause('field_id', $udf_field_ids) . '
                    AND ' . $sqlHelper->inClause('udf_values.cas_id', $recordids) . '
                    AND udf_values.mod_id = :mod_id AND group_id = 0',
        ['mod_id' => ModuleCodeToID($module)],
    );

    foreach ($udfRawData as $udfRow) {
        $value = '';
        switch ($udfRow['fld_type']) {
            case 'S':
            case 'Y':
            case 'C':
            case 'T':
                $value = $udfRow['udv_string'];

                break;
            case 'D':
                $value = $udfRow['udv_date'];

                break;
            case 'N':
                $value = $udfRow['udv_number'];

                break;
            case 'M':
                $value = $udfRow['udv_money'];

                break;
            case 'L':
                $value = $udfRow['udv_text'];

                break;
        }

        $udfData[$udfRow['cas_id']][$udfRow['field_id']] = $value;
    }

    return $udfData;
}

/**
 * @param array $mainModule The FieldDefs array
 *
 * @psalm-param non-empty-list<array> $linkedFieldDefs array of module fieldDefs to merge
 */
function mergeLinkedFieldDefs(array $mainModule, array $linkedFieldDefs): array
{
    // Sets the output variable to the main module fieldDefs so that if no other fieldDefs are merged in their main module ones are still there
    $linkedFieldDefs[] = $mainModule;

    // Creates a combined array of the fieldDefs to merge into the main one
    // Merges combined additional fieldDefs and main module fieldDefs, making sure the main module defs aren't overwritten
    return array_merge(...$linkedFieldDefs);
}

function loadGenericModuleFieldDefs(): void
{
    // do not remove $ModuleDefs its used in the require
    global $FieldDefs;

    $logger = Container::get(LoggerInterface::class);
    $registry = Container::get(Registry::class);

    $genericModuleDir = __DIR__ . '/../generic_modules';
    $handler = opendir($genericModuleDir);
    if ($handler === false) {
        $logger->error('Failed to open generic modules directory');

        return;
    }

    while ($subDir = readdir($handler)) {
        if (!in_array($subDir, ['.', '..'], true) && file_exists("{$genericModuleDir}/{$subDir}/FieldDefs.php")) {
            require_once "{$genericModuleDir}/{$subDir}/FieldDefs.php";
        }
    }
    closedir($handler);
}
