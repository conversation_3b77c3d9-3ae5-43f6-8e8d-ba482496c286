<?php

use src\equipment\models\EquipmentFields;
use src\incidents\model\IncidentsFields;
use src\incidents\model\IncidentsSections;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\incidents\model\PSIMSIncidentMedicationFields;
use src\medications\models\MedicationFields;
use src\medications\models\MedicationSections;

$GLOBALS['FormTitle'][7] = _fdtk('dif2_title');

$GLOBALS['MandatoryFields'] = [
    'learnings_title' => 'learning',
    'key_learnings' => 'learning',
    PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT_OTHER => IncidentsSections::PSIMS_REPORTER,
    PSIMSIncidentFields::PSIMS_REPORTER_ROLE_OTHER => IncidentsSections::PSIMS_REPORTER,
    PSIMSIncidentMedicationFields::DRUG_REACTION_OTHER => IncidentsSections::PSIMS_MEDICATIONS,
    PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES_OTHER => IncidentsSections::PSIMS_MEDICATIONS,
    PSIMSIncidentEquipmentFields::DEVICE_TYPE_OTHER => IncidentsSections::PSIMS_EQUIPMENT,
    PSIMSIncidentFields::PSIMS_GOOD_CARE_DETECTION_FACTOR_OTHER => IncidentsSections::PSIMS_WENT_WELL,
    PSIMSIncidentFields::PSIMS_HOW_FUTURE_OCCURRENCE => IncidentsSections::PSIMS_WENT_WELL,
    PSIMSIncidentFields::PSIMS_RISK_DESCRIPTION => IncidentsSections::PSIMS_RISK,
    PSIMSIncidentFields::PSIMS_RISK_THEME_OTHER => IncidentsSections::PSIMS_RISK,
    PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME => IncidentsSections::PSIMS,
    PSIMSIncidentFields::PSIMS_RISK_TIMEFRAME_OTHER => IncidentsSections::PSIMS,
    PSIMSIncidentFields::PSIMS_INVOLVED_PERSONS_ACTIONS_OTHER => IncidentsSections::PSIMS_INVOLVED_PERSONS,
    PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT => IncidentsSections::PSIMS,
    PSIMSIncidentFields::PSIMS_DETECTION_POINT_OTHER => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => IncidentsSections::PSIMS,
    PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_INCIDENT_FRAMEWORK => IncidentsSections::PSIMS_PSIRF,
    PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS => IncidentsSections::PSIMS_PSIRF,
    PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT => IncidentsSections::PSIMS_PSIRF,
    PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED => IncidentsSections::PSIMS_PSIRF,
    PSIMSIncidentFields::PSIMS_PATIENT_EVENT => IncidentsSections::PSIMS,
    PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN => IncidentsSections::PSIMS,
    MedicationFields::ADMINISTERED_MED_NAME => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_ROUTE => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_DOSE => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_FORM => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::CORRECT_MED_NAME => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_ROUTE => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_DOSE => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_FORM => MedicationSections::CORRECT_DRUG,
    MedicationFields::STAGE_OF_ERROR => MedicationSections::MEDICATIONS_OTHER_FIELDS,
    MedicationFields::TYPE_OF_ERROR => MedicationSections::MEDICATIONS_OTHER_FIELDS,
    MedicationFields::OTHER_IMPORTANT_FACTORS => MedicationSections::MEDICATIONS_OTHER_FIELDS,
    EquipmentFields::SEARCH => IncidentsSections::EQUIPMENT,
    EquipmentFields::DEVICE_TYPE => IncidentsSections::EQUIPMENT,
    EquipmentFields::QUANTITY_USED => IncidentsSections::EQUIPMENT,
    EquipmentFields::BATCH_NUMBER => IncidentsSections::EQUIPMENT,
    EquipmentFields::EXPIRY_DATE => IncidentsSections::EQUIPMENT,
    EquipmentFields::CATELOGUE_NUMBER => IncidentsSections::EQUIPMENT,
    EquipmentFields::OPERATOR => IncidentsSections::EQUIPMENT,
    EquipmentFields::USAGE => IncidentsSections::EQUIPMENT,
    EquipmentFields::CURRENT_LOCATION => IncidentsSections::EQUIPMENT,
];

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'location_id' => true,
    'service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
];

$GLOBALS['ReadOnlyFields'] = [
    'reportedby' => true,
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV1 => true,
    IncidentsFields::INC_TOTAL_TIME_TO_HANDLE => true,
    IncidentsFields::REFERENCE_NUMBER => true,
    IncidentsFields::SUBMISSION_DATE_TIME => true,
    IncidentsFields::STATUS => true,
    IncidentsFields::WARNINGS => true,

];

$GLOBALS['ExpandSections'] = [
    'show_equipment' => [
        [
            'section' => 'iq_equipment',
            'values' => ['Y'],
        ],
    ],
    'rep_approved' => [
        [
            'section' => 'rejection',
            'alerttext' => 'Please complete the \'Details of rejection\' section before saving this form.',
            'values' => ['REJECT'],
        ],
    ],
    'inc_report_npsa' => [
        [
            'section' => 'nrls',
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    'inc_is_riddor' => [
        [
            'section' => 'riddor',
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    'inc_causal_factors_linked' => [
        [
            'section' => 'causal_factor',
            'values' => ['Y'],
        ],
    ],
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV1 => [
        [
            'section' => IncidentsSections::PSIMS,
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'section' => IncidentsSections::PSIMS_REPORTER,
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_GOVERNANCE,
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV2 => [
        [
            'section' => IncidentsSections::PSIMS,
            'alerttext' => '',
            'values' => ['N'],
        ],
        [
            'section' => IncidentsSections::PSIMS_REPORTER,
            'alerttext' => '',
            'values' => ['N'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_GOVERNANCE,
            'alerttext' => '',
            'values' => ['N'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE => [
        [
            'section' => IncidentsSections::PSIMS_RISK,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'section' => IncidentsSections::PSIMS_WENT_WELL,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'section' => IncidentsSections::PSIMS_PSIRF,
            'values' => ['1'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS => [
        [
            'section' => IncidentsSections::PSIMS_MEDICATIONS,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'section' => IncidentsSections::PSIMS_EQUIPMENT,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'section' => 'iq_equipment',
            'values' => ['3'],
        ],
        [
            'section' => IncidentsSections::PSIMS_TISSUES_ORGANS,
            'alerttext' => '',
            'values' => ['8'],
        ],
        [
            'section' => IncidentsSections::PSIMS_IT_SYSTEMS_SOFTWARE,
            'alerttext' => '',
            'values' => ['9'],
        ],
        [
            'section' => IncidentsSections::PSIMS_INVOLVED_PERSONS,
            'alerttext' => '',
            'values' => ['10'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD,
            'alerttext' => '',
            'values' => ['11'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BUILDINGS_INFRASTRUCTURE,
            'alerttext' => '',
            'values' => ['13'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_ESTATES_SERVICES,
            'alerttext' => '',
            'values' => ['14'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_AGENT,
            'alerttext' => '',
            'values' => ['12', '9', '3', '4'],
        ],
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD_PRODUCTS,
            'alerttext' => '',
            'values' => ['12'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => [
        [
            'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
            'alerttext' => '',
            'values' => ['4', '7'],
        ],
    ],
];

$GLOBALS['HideFields'] = [
    'inc_injury' => true,
    'inc_bodypart' => true,
    'additional' => true,
    'pas' => true,
    'progress_notes' => true,
    'rejection_history' => true,
    'inc_time_band' => true,
    'causal_factor_header' => true,
    'causal_factor' => true,
    'payments' => true,
    'action_chains' => true,
    'dum_inc_grading_initial' => true,
    'dum_inc_grading' => true,
    'imed_reference_admin' => true,
    'imed_reference_correct' => true,
    'ccs2' => true,
    'inc_level_intervention' => true,
    'inc_level_harm' => true,
    'inc_submittedtime' => true,
    'inc_never_event' => true,
    'inc_last_updated' => true,
    'openness_transparency' => true,
    'imed_other_factors' => true,
    'imed_right_wrong_medicine_admin' => true,
    'imed_manufacturer_special_admin' => true,
    'imed_parallel_import_admin' => true,
    'imed_bnf_classification' => true,
    'imed_clinical_trial' => true,
    'osha' => true,
    'incident_occurred_on_employer_premises' => true,
    'safeguard_provided' => true,
    'safeguard_used' => true,
    'osha_recordable' => true,
    'edi_cause_code' => true,
    'learning' => true,
    'falls' => true,
    'fire' => true,
    'blood_transfusion' => true,
    'spsc_national_data' => true,
    'inc_consultants' => true,
    'inc_inv_lessons_sub_category' => true,
    'hro_characteristics' => true,
    'time_taken_to_submit' => true,
    'pressure_ulcers' => true,
    'skin_lesions' => true,
    'sharps' => true,
    'inc_result_initial' => true,
    'inc_severity_initial' => true,
    'sac_score' => true,
    'sac_score_initial' => true,
    IncidentsFields::SAC_DATE_DIFF_DAYS => true,
    IncidentsFields::SAC_DATE_DIFF_HOURS => true,
    IncidentsFields::SAC_SCORE_DATE => true,
    IncidentsFields::SAC_SCORE_TIME => true,
    'approved_by' => true,
    'flag_for_rib' => true,
    'inc_ot_q21' => true,
    'inc_ot_q22' => true,
    IncidentsFields::INC_SPECIALTY => true,
    IncidentsFields::CLASSIFICATION_TREE => true,
    'adverse_drug_reactions' => true,
    'worker_details' => true,
    'health_service_site' => true,
    'rep_feedback_codes' => true,
    'rep_feedback_notes' => true,
    'anon_reporting' => true,
    IncidentsFields::SPSC_BLOOD_TYPE_PRODUCT => true,
    IncidentsFields::SPSC_WAS_RED_CODE_ACTIVATED => true,
    IncidentsFields::SPSC_WAS_FIRE_ALARM_ACTIVATED => true,
    IncidentsFields::SPSC_WAS_BUILDING_EVACUATED => true,
    IncidentsFields::SPSC_WAS_PORTABLE_FIRE_EXTINGUISHERS_USED => true,
    IncidentsFields::SPSC_WAS_LOCAL_FIRE_DEPARTMENT_INFORMED => true,
    IncidentsFields::SPSC_WERE_ANY_DAMAGES_TO_PROPERTY => true,
    IncidentsFields::SPSC_MORBIDITY_RECORD_IS => true,
    IncidentsFields::SPSC_IS_TRIGGER_RELATED_TO_IMPROPER_ASSESSMENT_OF_PATIENT => true,
    IncidentsFields::SPSC_ADDITIONAL_MORBIDITY_TRIGGERS => true,
    IncidentsFields::SEND_TO_SFDA => true,
    IncidentsFields::SOURCE_OF_RECORD => true,
    IncidentsFields::INC_GRADE_RATING => true,
    IncidentsFields::INC_GRADE_INITIAL_RATING => true,
    IncidentsFields::DIAGNOSTIC => true,
    IncidentsFields::PROCEDURES => true,
    IncidentsFields::ALLERGY_REACTION => true,
    IncidentsFields::ALLERGY_SEVERITY => true,
    IncidentsFields::ALLERGEN_TYPE => true,
    IncidentsFields::ALLERGY_CLINICAL_STATUS => true,
    IncidentsFields::ONSET_DATE => true,
    IncidentsFields::DATE_OF_ADMISSION => true,
    IncidentsFields::RECOVERY_DATE => true,
    IncidentsSections::OUTBREAK => true,
    IncidentsFields::OUTBREAK_IMPACT => true,
    IncidentsFields::OUTBREAK_TYPE => true,
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV1 => true,
    IncidentsSections::TIME_FIELDS => true,
    IncidentsSections::PSIMS_RESPONSE => true,
    IncidentsSections::TIME_CHAIN => true,
];

$GLOBALS['UserExtraText'] = [
    'inc_notes' => ['7' => _fdtk('user_extra_text_inc_notes')],
    'inc_actiontaken' => ['7' => 'Enter action taken at the time of the incident'],
    'dum_fbk_to' => ['7' => 'Only staff and contacts with e-mail addresses are shown.'],
    'dum_fbk_gab' => ['7' => 'Only users with e-mail addresses are shown.'],
    'dum_fbk_email' => ['7' => 'Enter e-mail addresses of other recipients not listed above. You can<br />enter multiple addresses, separated by commas.'],
    'flag_for_investigation' => ['7' => 'Selecting yes will submit this record for investigation review.'],
    'flag_for_rib' => ['7' => 'Selecting yes will create a RIB from this record.'],
    IncidentsSections::PSIMS_RESPONSE => ['7' => 'Submission to LFPSE may take a few minutes. If section is blank, refresh page to load data'],
];

$GLOBALS['DefaultValues'] = [
    'equipment_search_category' => 'genericName',
];

$GLOBALS['HelpTexts'] = [
    'inc_type' => ['7' => 'Field: Incident Type<br /><br />Please make a selection from the dropdown list.<br /><br />The Incident Type indicates the type of party affected by the incident.'],
    'inc_dincident' => ['7' => 'Field: Incident Date<br /><br />Please enter the date on which the incident occurred in the format ' . $_SESSION['DATE_FORMAT'] . '.  You can click on the Calendar button if you want to choose the date from a calendar window.'],
    'psims_safety_incident_occurred' => ['7' => 'To indicate if there is cause to worry or suspect patient safety incident occurred'],
    'psims_estimated_time' => ['7' => 'Estimated time of the event'],
    'psims_outcome_type' => ['7' => 'The type of outcome that is being recorded'],
    'psims_involved_agents' => ['7' => 'What people and or things were involved in the event'],
    'psims_medication_admin' => ['7' => 'Was a the medication administered by using a device'],
    'psims_problem_description_actions' => ['7' => 'A description of the problem involving people\'s actions'],
    'psims_problem_description_involvement' => ['7' => 'A description of the problem in regards to people involved'],
    'psims_level_of_concern' => ['7' => 'Indication of how concerned the reporter feels about this adverse event given its wider implications'],
    'psims_detection_point' => ['7' => 'High level detection factors'],
    'psims_detection_point_other' => ['7' => 'Other detection factors'],
    'duty_of_candour' => ['7' => 'Indication of whether the adverse event meets the requirements for duty of candour'],
    'psims_went_well' => ['7' => 'Details of a positive patient safety event'],
    'psims_safety_challenges' => ['7' => 'What safety challenges were faced in the incident'],
    PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE => ['7' => (
        'For example, Level 2 / 13ff / 13cc / 13dd / MD13hh / CF3a / CF2c / CF1c'
        . "\n"
        . '<br />'
        . 'TSRT9 - '
        . "\n"
        . '<br />'
        . 'Guidance on the radiotherapy taxonomy and its application'
    )],
    'psims_responsible_specialty' => ['7' => 'Specialty caring for the patient at the time the problem occurred'],
    'psims_responsible_specialty_other' => ['7' => 'Other responsible specialty'],
    'psims_service_area' => ['7' => 'Service areas involved in the event'],
    'psims_risk_theme' => ['7' => 'The theme of the risk'],
    'psims_risk_theme_other' => ['7' => 'Other theme of the event'],
    'psims_risk_service_area' => ['7' => 'What service areas could be affected by this risk'],
    'psims_risk_imminent' => ['7' => 'Is the risk of death or severe harm imminent'],
    'psims_risk_population' => ['7' => 'What population could be affected by this risk'],
    'psims_risk_time_frame' => ['7' => 'The timeframe in which a risk is most likely to manifest itself and cause harm'],
    'psims_risk_timeframe_other' => ['7' => 'Other timeframe where a risk may manifest'],
    'psims_risk_description' => ['7' => 'Description of the risk'],
    'psims_care_detection_factor' => ['7' => 'To determine how the example of good care came to the reporters attention'],
    'psims_good_care_detection_factor_other' => ['7' => 'Other factor which is not listed involved in noticing the example of good care'],
    'psims_how_future_occurrence' => ['7' => 'To indicate how the care that went well could be amplified or re-created in future'],
    PSIMSIncidentCodedFields::PSIMS_RISK_IDENTIFIED_LOCATION => ['7' => 'Start typing and select the organisation from the list. If you are unsure of your organisation\'s ODS code, you can use the ODS portal here to find it.'],
    'psims_location_at_risk' => ['7' => 'The location where a risk is present'],
    'psims_location_within_service' => ['7' => 'The location within the service where an adverse event occurred'],
    'drug_insufficient_details' => ['7' => 'How too little of a drug was used'],
    'drug_involvement_factors' => ['7' => 'How drugs were involved in an unexpected way'],
    'drug_reaction' => ['7' => 'Indication of whether an adverse event was due to an adverse drug reaction'],
    'drug_reaction_other' => ['7' => 'Other indication of drug reaction'],
    'drug_used_too_much' => ['7' => 'How too much of a drug was used'],
    'drug_wrong_details' => ['7' => 'How the right drug was not used as it should have been'],
    'drug_involved_processes' => ['7' => 'Processes involved in what went wrong'],
    'drug_involved_processes_other' => ['7' => 'Processes involved in what went wrong other response'],
    'drug_given_incorrectly' => ['7' => 'Reason the medication was administered incorrectly'],
    'problem_meds_packaging' => ['7' => 'What was wrong with the medication or its packaging'],
    'problem_description_drugs' => ['7' => 'A description of the problem involving drugs'],
    'psims_device_type' => ['7' => 'The type of medical device that was involved in an incident'],
    'device_type_other' => ['7' => 'Other device type'],
    'device_broken_details' => ['7' => 'How a device was broken'],
    'device_not_enough_details' => ['7' => 'How the use of devices was insufficient'],
    'device_usage_factors' => ['7' => 'How devices were involved in an unexpected way'],
    'device_used_unnecessarily' => ['7' => 'How a device was used unnecessarily'],
    'device_usage_details' => ['7' => 'How a device was used incorrectly'],
    'device_problem_description' => ['7' => 'A description of the problem involving devices'],
    'psims_problem_description_built_environment' => ['7' => 'A description of the problem involving built environment'],
    'psims_problem_description_furniture_fittings' => ['7' => 'A description of the problem involving furniture and fittings'],
    'psims_it_involvement_factors' => ['7' => 'How IT systems and software were involved in an unexpected way'],
    'psims_problem_description_systems_software' => ['7' => 'A description of the problem involving IT systems and software'],
    'psims_blood_not_used' => ['7' => 'Reasons for why blood or blood products were not used'],
    'psims_problem_description_blood' => ['7' => 'A description of the problem involving blood and blood products'],
    'psims_tissue_deficient_details' => ['7' => 'How insufficient tissue or organs for transplant were involved'],
    'psims_tissue_involvement_factor' => ['7' => 'How tissue and organs for transplant were involved in an unexpected way'],
    'psims_tissue_not_used' => ['7' => 'Reasons for tissues or organs not being used when they should have been'],
    'psims_tissue_used_too_much' => ['7' => 'How too much tissue and organs for transplant were used'],
    'psims_tissue_wrong_details' => ['7' => 'How the wrong tissues or organs for transplant were involved'],
    'psims_tissue_damaged_details' => ['7' => 'How damaged tissue or organs for transplant were involved'],
    'psims_problem_description_tissues_organs' => ['7' => 'A description of the problem involving tissue and organs for transplant'],
    'psims_involved_persons_actions' => ['7' => 'The role(s) of the people involved in the event'],
    'psims_involved_persons_actions_other' => ['7' => 'Other role description'],
    'psims_people_action_factors' => ['7' => 'How the actions of people differed from expected'],
    'psims_people_action_too_much' => ['7' => 'How people took too much of an action'],
    'psims_people_action_details' => ['7' => 'How people did not take enough of a necessary action'],
    'psims_people_involvement_factor' => ['7' => 'How the involvement of people differed from expected'],
    'psims_people_omitted_action' => ['7' => 'How people did not do something that was needed'],
    'psims_people_unavailable_detail' => ['7' => 'How people were not available who should have been'],
    'psims_people_wrong_action' => ['7' => 'How people took the wrong actions'],
    'psims_reporter_involvement' => ['7' => 'How was the reporter involved in the event'],
    'psims_reporter_involvement_other' => ['7' => 'Other involvement in the event'],
    'psims_reporter_role' => ['7' => 'The role of the reporter'],
    'psims_reporter_role_other' => ['7' => 'Other role'],
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV2 => ['7' => 'A patient safety event is any event that could have or did impact the safety of one or more patients during the provision of health care, including risks to patient safety in the future, and positive events that could be learned from to improve safety.'],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => ['7' => 'View the Patient Safety Incident Response Framework supporting guidance for examples of events requiring a specific type of response as set out in policies or regulations'],
    PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS => ['7' => 'Please provide details of the findings from the patient safety response'],
    PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT => ['7' => 'Please describe each area for improvement separately'],
    PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED => ['7' => 'Include who the safety actions are being made to, and what they entail. Please add each safety action separately'],
    IncidentsFields::SUBMISSION_DATE_TIME => ['7' => (
        'Search for date or time in the following formats:'
        . "\n"
        . '<br />'
        . 'dd* OR *dd/mm* OR *dd/mm/yyyy* OR *yyyy* OR *mm/yyyy'
        . "\n"
        . '<br />'
        . '*hh:mm:ss OR *hh:mm* OR *hh*'
        . "\n"
        . '<br />'
        . 'For example:'
        . "\n"
        . '<br />'
        . '"June 2024" search *06/2024*'
        . "\n"
        . '<br />'
        . '"26th June 2024" search 26/06/2024*'
        . "\n"
        . '<br />'
        . '"11?am" search *11:*'
        . "\n"
        . '<br />'
        . '*10* will return any date or time, e.g. 10/06/2024 or 9:10'
    )],
    IncidentsFields::STATUS => ['7' => (
        'Search for individual status in the following format:'
        . "\n"
        . '<br />'
        . 'accepted'
        . "\n"
        . '<br />'
        . 'acceptedwithwarnings'
        . "\n"
        . '<br />'
        . 'notaccepted'
    )],
    IncidentsFields::WARNINGS => ['7' => (
        'Search for warnings with asterisks on either side of the warning message.'
        . "\n"
        . '<br />'
        . 'For example:'
        . "\n"
        . '<br />'
        . 'For "LocationKnown was not included in the submission" search for *LocationKnown*'
        . "\n"
        . '<br />'
        . 'For multiple warnings, use a pipe symbol for OR e.g. *ReporterOrganisation*|*LocationKnown*'
    )],
];

$GLOBALS['NewPanels'] = [
    'medication' => true,
    'investigation' => true,
    'feedback' => true,
    'linked_records' => true,
    'contacts_type_A' => true,
    'causes' => true,
    'notepad' => true,
    'progress_notes' => true,
    'linked_actions' => true,
    'documents' => true,
    'equipment' => true,
    'pars' => true,
    'pas' => true,
    'history' => true,
    'message' => true,
    'word' => true,
    'rejection' => true,
    'payments' => true,
    'action_chains' => true,
    'vanessas_law' => true,
];

$GLOBALS['ExpandFields'] = [
    'inc_ot_q1' => [
        [
            'field' => 'inc_ot_q2',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q3',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q4',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q5',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q6',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q7',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q8',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q9',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q10',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q11',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q12',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q13',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q14',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q15',
            'alerttext' => '',
            'values' => ['Y', 'N'],
        ],
        [
            'field' => 'inc_ot_q16',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q17',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q18',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q19',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'inc_ot_q20',
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    'learnings_to_share' => [
        [
            'field' => 'key_learnings',
            'alerttext' => '',
            'values' => ['Y'],
        ],
        [
            'field' => 'learnings_title',
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT => [
        [
            'field' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT_OTHER,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_ROLE => [
        [
            'field' => PSIMSIncidentFields::PSIMS_REPORTER_ROLE_OTHER,
            'alerttext' => '',
            'values' => ['9'],
        ],
    ],
    PSIMSIncidentMedicationFields::DRUG_INVOLVEMENT_FACTORS => [
        [
            'field' => PSIMSIncidentMedicationFields::DRUG_USED_TOO_MUCH,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentMedicationFields::DRUG_INSUFFICIENT_DETAILS,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentMedicationFields::DRUG_GIVEN_INCORRECTLY,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentMedicationFields::PROBLEM_MEDS_PACKAGING,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentMedicationFields::PROBLEM_DESCRIPTION_DRUGS,
            'alerttext' => '',
            'values' => ['5'],
        ],
    ],
    PSIMSIncidentMedicationFields::DRUG_REACTION => [
        [
            'field' => PSIMSIncidentMedicationFields::DRUG_REACTION_OTHER,
            'alerttext' => '',
            'values' => ['8'],
        ],
    ],
    PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES => [
        [
            'field' => PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES_OTHER,
            'alerttext' => '',
            'values' => ['4'],
        ],
    ],
    PSIMSIncidentEquipmentFields::PSIMS_DEVICE_TYPE => [
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_TYPE_OTHER,
            'alerttext' => '',
            'values' => ['88'],
        ],
    ],
    PSIMSIncidentEquipmentFields::DEVICE_USAGE_FACTORS => [
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_USED_UNNECESSARILY,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_USAGE_DETAILS,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_BROKEN_DETAILS,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_PROBLEM_DESCRIPTION,
            'alerttext' => '',
            'values' => ['6'],
        ],
        [
            'field' => PSIMSIncidentEquipmentFields::DEVICE_NOT_ENOUGH_DETAILS,
            'alerttext' => '',
            'values' => ['2'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_CARE_DETECTION_FACTOR => [
        [
            'field' => PSIMSIncidentFields::PSIMS_GOOD_CARE_DETECTION_FACTOR_OTHER,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_THEME => [
        [
            'field' => PSIMSIncidentFields::PSIMS_RISK_THEME_OTHER,
            'alerttext' => '',
            'values' => ['8'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME,
            'alerttext' => '',
            'values' => ['n', 'u'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME => [
        [
            'field' => PSIMSIncidentFields::PSIMS_RISK_TIMEFRAME_OTHER,
            'alerttext' => '',
            'values' => ['4'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_INVOLVEMENT_FACTOR => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TISSUE_USED_TOO_MUCH,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DEFICIENT_DETAILS,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TISSUE_WRONG_DETAILS,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TISSUE_NOT_USED,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DAMAGED_DETAILS,
            'alerttext' => '',
            'values' => ['5'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_TISSUES_ORGANS,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_IT_INVOLVEMENT_FACTORS => [
        [
            'field' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_SYSTEMS_SOFTWARE,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS => [
        [
            'field' => PSIMSIncidentFields::PSIMS_INVOLVED_PERSONS_ACTIONS_OTHER,
            'alerttext' => '',
            'values' => ['5'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_TOO_MUCH,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_DETAILS,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_WRONG_ACTION,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_OMITTED_ACTION,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_ACTIONS,
            'alerttext' => '',
            'values' => ['5'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR,
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_UNAVAILABLE_DETAIL,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_INVOLVEMENT,
            'alerttext' => '',
            'values' => ['4'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_ESTIMATED_TIME,
            'alerttext' => '',
            'values' => ['1', '2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN,
            'alerttext' => '',
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY,
            'alerttext' => '',
            'values' => ['1', '3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT,
            'alerttext' => '',
            'values' => ['2', '1'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_WENT_WELL,
            'alerttext' => '',
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE,
            'alerttext' => '',
            'values' => ['1', '2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA,
            'alerttext' => '',
            'values' => ['1', '2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN,
            'alerttext' => '',
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY,
            'alerttext' => '',
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_RESPONSIBLE_SPECIALTY_OTHER,
            'alerttext' => '',
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_RISK_POPULATION,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PATIENT_EVENT,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN,
            'alerttext' => '',
            'values' => ['1', '2', '4'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED => [
        [
            'alerttext' => [7 => 'Please change the type of event to Incident'],
            'values' => ['y'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN,
            'alerttext' => ['7' => 'Provide your email address in the Reporter section so that the MHRA can follow up with you about this incident if they need to.'],
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN,
            'alerttext' => ['7' => 'Provide your email address in the Reporter section so that the MHRA can follow up with you about this incident if they need to.'],
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_SABRE_REPORT_NUMBER,
            'alerttext' => '',
            'values' => ['11'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_SHOT_REPORT_NUMBER,
            'alerttext' => '',
            'values' => ['11'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_NHSBT_REPORT_NUMBER,
            'alerttext' => '',
            'values' => ['8'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS,
            'alerttext' => '',
            'values' => ['10'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_YELLOW_CARD_REFERENCE,
            'alerttext' => '',
            'values' => ['3', '4', '9', '12'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT => [
        [
            'field' => PSIMSIncidentFields::PSIMS_DETECTION_POINT_OTHER,
            'alerttext' => '',
            'values' => ['8'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => [
        [
            'field' => PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER,
            'alerttext' => '',
            'values' => ['7'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_INVOLVED => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_USED,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_USED,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_BLOOD_WAS_NOT_USED,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD,
            'alerttext' => '',
            'values' => ['5'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_BLOOD_PROBLEM,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_INVOLVED => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_PRODUCTS,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_PRODUCTS,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD_PRODUCTS,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_NOT_USED,
            'alerttext' => '',
            'values' => ['4'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD_PRODUCTS,
            'alerttext' => '',
            'values' => ['5'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_PROBLEM,
            'alerttext' => '',
            'values' => ['6'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE => [
        [
            'field' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_OTHER,
            'alerttext' => '',
            'values' => ['16'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES => [
        [
            'field' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_OTHER,
            'alerttext' => '',
            'values' => ['9'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_INVOLVED => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_NOT_USED,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_WRONG_BUILDINGS_INFRASTRUCTURE,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_PROBLEM,
            'alerttext' => '',
            'values' => ['5'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_CQC_NOTIFY => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_CQC_CRITERIA,
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_INVOLVED => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_NOT_USED,
            'alerttext' => '',
            'values' => ['2'],
        ],
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_WRONG_ESTATES_SERVICES,
            'alerttext' => '',
            'values' => ['3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_PROBLEM,
            'alerttext' => '',
            'values' => ['5'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_CQC_CRITERIA => [
        [
            'field' => PSIMSIncidentFields::PSIMS_DESIGNATIONS_MENTAL_HEALTH,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_DEPRIVATION_OF_LIBERTY,
            'alerttext' => '',
            'values' => ['1'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_LOCAL_AUTHORITY_SAFEGUARDING,
            'alerttext' => '',
            'values' => ['3'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_NEVER_EVENT => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_NEVER_EVENT_TYPE,
            'alerttext' => '',
            'values' => ['Y'],
        ],
    ],


    MedicationFields::REPORTED_TO_MANUFACTURER => [
        [
            'field' => MedicationFields::DATE_REPORTED_TO_MANUFACTURER,
            'alerttext' => '',
            'values' => ['yes'],
        ],
        [
            'field' => MedicationFields::MANUFACTURER_REF,
            'alerttext' => '',
            'values' => ['yes'],
        ],
    ],
    MedicationFields::ACTION_TAKEN => [
        [
            'field' => MedicationFields::ADR_LESSENED,
            'alerttext' => '',
            'values' => ['withdrawn', 'dose_reduced'],
        ],
        [
            'field' => MedicationFields::DATE_LAST_USED,
            'alerttext' => '',
            'values' => ['withdrawn'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_INCIDENT_FRAMEWORK => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE,
            'values' => ['1'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND,
            'values' => ['1', '2', '3'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS,
            'values' => ['1', '2'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT,
            'values' => ['1', '2'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED,
            'values' => ['1', '2'],
        ],
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED => [
        [
            'field' => PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED,
            'values' => ['Y'],
        ],
        [
            'field' => PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT_ADDRESSED,
            'values' => ['N'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPONDED,
            'values' => ['1', '3'],
        ],
    ],
    PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN => [
        [
            'field' => PSIMSIncidentCodedFields::PSIMS_ORGANISATION,
            'values' => ['n'],
        ],
    ],
];
