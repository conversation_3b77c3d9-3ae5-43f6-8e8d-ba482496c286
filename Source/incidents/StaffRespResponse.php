<?php

use src\framework\query\Query;
use src\framework\query\SqlWriter;
use src\framework\registry\Registry;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

// output
function StaffRespResponse()
{
    global $scripturl,$FieldDefs;

    $initial = $_REQUEST['initial'];
    if ($_REQUEST['print'] == '1') {
        $PrintMode = true;
    }

    // Filter incidents by the given (clicked) user's where clause
    $where = MakeSecurityWhereClause('', 'INC', $initial, '', '');

    // Filter incidents by the logged in user's where clause
    // Since we now allow UDFs in default listings, the quick fix for this area works as such: UDF fields are manually
    // removed from the listing, if the lsting then only has 0 or 1 columns left then we use 4 default fields: Recordid,
    // approval status, manager & description.
    $selectfields = preg_replace('/UDF_[A-Z]__[0-9]+[,]*/', '', $_SESSION['INC']['staffrespresponse']['selectfields']);
    $selectfields = rtrim($selectfields, ', ');
    $list_columns = $_SESSION['INC']['staffrespresponse']['list_columns'];
    foreach ($list_columns as $key => $val) {
        if (preg_match('/UDF_[A-Z]__[0-9]+/', $key) == 1) {
            unset($list_columns[$key]);
        }
    }
    if (count($list_columns) < 2) {
        $selectfields = 'recordid, rep_approved, inc_mgr';
        $list_columns = ['recordid' => 'recordid', 'rep_approved' => 'rep_approved', 'inc_mgr' => 'inc_mgr', 'inc_notes' => 'inc_notes'];
    }

    if ($where) {
        $query = new Query();
        $query->select(explode(', ', $selectfields))->from('incidents_main');

        $wherestring = " rep_approved IN ('AWAREV')";
        $wherestring .= ($where != '') ? ' AND ' . $where : '';
        $wherestring .= ($inc_where != '') ? ' AND ' . $inc_where : '';

        $query->setWhereStrings([$wherestring]);
        $query->orderBy(['recordid']);

        $sqlWriter = Container::get(SqlWriter::class);
        [$sql, $params] = $sqlWriter->writeStatement($query);
        $resultArray = DatixDBQuery::PDO_fetch_all($sql, $params);

        // Columns
        if (count($resultArray) == 0) {
            $output .= '<div class="staff_rec_info">No access to incidents or no incidents in the holding area.</div>';
        } else {
            $output .= '
            <div class="staff_rec_title">
                ' . count($resultArray) . ' records found
            </div>
            <table class="dtx-table centre-first-col no-bottom-border">
                <thead><tr>';
            if (is_array($list_columns)) {
                $module = 'INC';
                $fieldLabels = Container::get(Registry::class)->getFieldLabels();

                foreach ($list_columns as $col_field => $col_info) {
                    $output .= '<th>';
                    if ($col_field == 'recordid') {
                        if ($col_fieldinfo_extra['colrename']) {
                            $currentCols[$col_field] = $col_fieldinfo_extra['colrename'];
                        } else {
                            $currentCols[$col_field] = $fieldLabels->getLabel('users_main', $col_field);
                        }
                    } elseif (is_array($GLOBALS['UserLabels']) && array_key_exists($col_field, $GLOBALS['UserLabels'])) {
                        $currentCols[$col_field] = $GLOBALS['UserLabels'][$col_field];
                    } else {
                        $currentCols[$col_field] = $fieldLabels->getLabel('users_main', $col_field);
                    }
                    $output .= $currentCols[$col_field];
                    if ($FieldDefs[$module][$col_field]['Type'] != 'textarea') {
                        $output .= '</a>';
                    }
                    $output .= '</th>';
                }
            }

            $output .= '</tr></thead><tr>';

            foreach ($resultArray as $row) {
                foreach ($list_columns as $col_field => $col_info_extra) {
                    $output .= '<td';

                    if ($col_info_extra['dataalign']) {
                        $output .= ' align="' . $col_info_extra['dataalign'] . '"';
                    }
                    $output .= '>';

                    if (!$PrintMode) {
                        $output .= '<a href="' . $scripturl . '?action=incident&recordid=' . $row['recordid'] . '">';
                    }

                    if ($col_field == 'recordid') {
                        if ($_SESSION['Globals']['DIF_1_REF_PREFIX']) {
                            $output .= $_SESSION['Globals']['DIF_1_REF_PREFIX'];
                        }
                        $output .= $row[$col_field];
                    } elseif ($FieldDefs[$module][$col_field]['Type'] == 'ff_select') {
                        $output .= code_descr($module, $col_field, $row[$col_field]);
                    } elseif ($FieldDefs[$module][$col_field]['Type'] == 'date') {
                        $output .= formatDateForDisplay($row[$col_field]);
                    } elseif ($FieldDefs[$module][$col_field]['Type'] == 'textarea') {
                        $output .= CompatEscaper::encodeCharacters($row[$col_field]);
                    } elseif ($FieldDefs[$module][$col_field]['Type'] == 'time') {
                        if (\UnicodeString::strlen($row[$col_field]) == 4) {
                            $row[$col_field] = $row[$col_field][0] . $row[$col_field][1] . ':' . $row[$col_field][2] . $row[$col_field][3];
                        }

                        $output .= $row[$col_field];
                    } else {
                        $output .= $row[$col_field];
                    }

                    if (!$PrintMode) {
                        $output .= '</a>';
                    }

                    $output .= '</td>';
                }
                $output .= '</tr>';
            }
        }
    } else {
        // staff can see all the unapproved incidents in the system
        $output .= '<tr><td class="windowbg2" colspan="' . count($list_columns) . '">Access to all incidents</td></tr>';
    }

    $output .= '</table>';

    echo $output;
}
