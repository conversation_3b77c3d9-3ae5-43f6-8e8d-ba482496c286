<?php

use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\EQU\EquipmentFormSectionHelperFactory;
use Source\generic_modules\MED\MedicationFormSectionHelperFactory;
use Source\incidents\SharedSections;
use src\component\form\FormProperties;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\incidents\model\IncidentsFields;
use src\incidents\model\IncidentsSections;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\incidents\model\PSIMSIncidentMedicationFields;
use src\medications\models\MedicationPrefixes;
use src\psims\form\PsimsFieldPropertyManager;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$config = Container::get(DatixConfig::class);

// variables for globals table values
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showNationalDataset = $registry->getParm('ENABLE_NATIONAL_DATASET', 'N')->isTrue();
$showPressureUlcerDataset = $registry->getParm('ENABLE_PRESSURE_ULCER_DATASET', 'N')->isTrue();
$showSkinLesionDataset = $registry->getParm('ENABLE_SKIN_LESION_DATASET', 'N')->isTrue();
$showSharpsDataset = $registry->getParm('ENABLE_SHARPS_DATASET', 'N')->isTrue();
$showAdverseReactionDataset = $registry->getParm('ENABLE_ADVERSE_REACTION_DATASET', 'N')->isTrue();
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();
$showPsimsFields = $config->getPsimsEnabled();
$showLastChildFirst = $config->showLastChildFirst();

$isVanessaLawEnabled = $config->getVanessaLawEnabled();
$isLoggedIn = (new \src\framework\session\UserSessionFactory())->create()->isLoggedIn();
$submittedViaMobile = isset($inc['inc_mob_category']) || isset($inc['inc_mob_severity']) || isset($inc['inc_mob_location']) || !empty($inc['mobile_contacts']);

if (!$managerDropdownField && empty($GLOBALS['HideFields']['inc_mgr']) && bYN(GetParm('DIF_EMAIL_MGR', 'N'))) {
    $managerDropdownField = \src\incidents\controllers\IncidentFormTemplateController::MakeManagerDropdown($inc, $FormMode);
}

// If the user is logged in from the contacts table, they are the
// reporter of the incident.
if ($_SESSION['contact_login'] && $inc['inc_repname'] == '') {
    $inc['inc_repname'] = $_SESSION['fullname'];
}

if ($FormMode == 'Print' || $FormMode == 'ReadOnly') {
    $gradereadonly = true;
}

if ($FormMode == 'Search') {
    $Searchmode = true;
}

// Validate $FormType to make sure is set or if not then use fallback default
$basicFormHelper = new GenericBasicFormHelper();
$FormType = $basicFormHelper->getValidFormMode($FormType);

if ($FormType && !$FormMode) {
    $FormMode = $FormType;
}

$Table = \src\component\form\FormTableFactory::create($FormMode, 'INC');
$UDFSection = \src\component\form\FormTableFactory::create($FormMode, 'INC');
$fieldLabels = $registry->getFieldLabels();
$useFormDesignLanguage = $FormType == 'Design';

$medicationsSectionHelper = (new MedicationFormSectionHelperFactory())->create($FormType, 1, Module::INCIDENTS);
$equipmentSectionHelper = (new EquipmentFormSectionHelperFactory())->create($FormType, 1);

$FormArray = [
    'Parameters' => [
        'ExtraContacts' => 5,
        'Condition' => false,
    ],
    'details' => [
        'Title' => _fdtk('incident_details', $useFormDesignLanguage),
        'Rows' => [
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => 'INC',
                'perms' => $DIFPerms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $inc,
                'module' => 'INC',
                'perms' => $DIFPerms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'inc_type',
            'inc_dincident',
            'inc_time',
            'inc_time_band',
            'inc_notes',
            'inc_rc_required',
            'inc_report_npsa',
            'inc_is_riddor',
            'inc_actiontaken',
            'inc_dnpsa',
            [
                'Name' => 'inc_cnstitype',
                'Condition' => !($_SESSION['Globals']['DIF_1_CCS'] == 'TCS' || $_SESSION['Globals']['DIF_1_CCS'] == 'TCS_CCS'),
            ],
            'inc_notify',
            ['Name' => 'inc_injury', 'Type' => 'ff_select', 'Title' => 'Loss/damage', 'Module' => 'INC'],
            ['Name' => 'inc_bodypart', 'Type' => 'ff_select', 'Title' => 'Item', 'Module' => 'INC'],
            ['Name' => 'inc_level_intervention', 'Condition' => bYN(GetParm('CCS2_INC', 'N'))],
            ['Name' => 'inc_level_harm', 'Condition' => bYN(GetParm('CCS2_INC', 'N'))],
            'inc_never_event',
            // FIX GLOBAL DECLARATIONS WHEN IQ-4040 MERGED IN
            ['Name' => 'incident_occurred_on_employer_premises', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_provided', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_used', 'Condition' => $showUsClaimsFields],
            'health_service_site',
            ['Name' => 'hro_characteristics', 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::INC_SPECIALTY, 'Condition' => $showSpscFields],
            [
                FormProperties::FIELD_NAME => PSIMSIncidentFields::INC_PSIMS_RECORD_LV1,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::NO_ADVANCED => false,
                FormProperties::NO_FIELD_LABEL => true,
                FormProperties::NO_HELP_TEXT => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
                FormProperties::NO_MANDATORY => true,
            ],
        ],
    ],
    IncidentsSections::OUTBREAK => [
        'Title' => _fdtk('inc_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => IncidentsFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => IncidentsFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],
    'mobile' => [
        'Title' => 'Datix Anywhere',
        'Condition' => $isLoggedIn && ($submittedViaMobile || $FormMode == 'Design'),
        'Rows' => [
            [
                'Name' => 'inc_mob_severity',
                'Type' => 'custom',
                'HTML' => $inc['mobile_severity_html'],
                'NoOrder' => true,
            ],
            [
                'Name' => 'inc_mob_category',
                'NoOrder' => true,
                'Type' => 'custom',
                'HTML' => $inc['mobile_category_html'],
                'Condition' => $showSpscFields,
            ],
            [
                'Name' => 'inc_mob_location',
                'Type' => 'custom',
                'HTML' => $inc['mobile_locations_html'],
                'NoOrder' => true,
            ],
            [
                'Name' => 'mobile_contacts',
                'NoOrder' => true,
            ],
            [
                'Name' => 'inc_mob_anonymous',
                'NoOrder' => true,
                'Condition' => $showSpscFields,
            ],
        ],
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            'exact_location',
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'tcs' => [
        'Title' => _fdtk('incident_coding', $useFormDesignLanguage),
        'Condition' => ($_SESSION['Globals']['DIF_1_CCS'] == 'TCS' || $_SESSION['Globals']['DIF_1_CCS'] == 'TCS_CCS'),
        'Rows' => [
            'inc_category',
            'inc_subcategory',
            'inc_cnstitype',
        ],
    ],
    'ccs' => [
        'Title' => _fdtk('datix_ccs', $useFormDesignLanguage),
        'Condition' => ($_SESSION['Globals']['DIF_1_CCS'] == 'CCS' || $_SESSION['Globals']['DIF_1_CCS'] == 'TCS_CCS'),
        'Rows' => [
            'inc_carestage',
            'inc_clin_detail',
            'inc_clintype',
        ],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_INC', 'N')),
        'Rows' => [
            'inc_affecting_tier_zero',
            'inc_type_tier_one',
            'inc_type_tier_two',
            'inc_type_tier_three',
        ],
    ],
    'severity' => [
        'Title' => (_fdtk('incident_severity', $useFormDesignLanguage) . ($HideFields['inc_result'] ? '' : ' ' . _fdtk('report_title_and', $useFormDesignLanguage) . ' ' . $fieldLabels->getLabel('incidents_main', 'inc_result', 'INCIDENTS', null, '', $useFormDesignLanguage))),
        'Rows' => [
            'inc_result',
            'inc_severity',
            'dum_inc_grading_initial',
            'dum_inc_grading',
            [
                'Name' => IncidentsFields::CLASSIFICATION_TREE,
                'Condition' => !$Searchmode && $registry->getParm('CLASSIFICATION_TREE', 'N')->isTrue(),
            ],
        ],
    ],
    'riddor' => [
        'Title' => 'RIDDOR',
        'Rows' => [
            'inc_dnotified',
            'inc_riddor_ref',
            'inc_ridloc',
            'inc_address',
            'inc_localauth',
            'inc_acctype',
            'inc_riddorno',
        ],
    ],
    'additional' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_person',
            'show_witness',
            'show_employee',
            'show_other_contacts',
            'show_equipment',
            'show_medication',
            'show_pars',
            'show_document',
            'anon_reporting',
        ],
    ],

    'iq_medications' => $medicationsSectionHelper->createMedicationFormSection(),
    'iq_administered_drug_design' => $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::ADMINISTERED_FIELD_PREFIX),
    'iq_correct_drug_design' => $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::CORRECT_FIELD_PREFIX),
    'iq_medications_other_design' => $medicationsSectionHelper->createOtherFieldsFormDesignSection(),

    'iq_equipment' => $equipmentSectionHelper->createEquipmentFormSection(),

    'notepad' => [
        'Title' => _fdtk('notepad', $useFormDesignLanguage),
        'Rows' => [
            'notes',
        ],
    ],
    'investigation' => [
        'Title' => _fdtk('details_of_review', $useFormDesignLanguage),
        'Rows' => [
            'inc_investigator',
            'inc_inv_dstart',
            'inc_inv_dcomp',
            'inc_inv_outcome',
            'inc_root_causes',
            'inc_lessons_code',
            'inc_inv_lessons',
            ['Name' => 'inc_inv_lessons_sub_category', 'Condition' => $showSpscFields],
            'inc_action_code',
            'inc_inv_action',
        ],
    ],
    'pars' => [
        'Title' => _fdtk('sirs_reporting_information', $useFormDesignLanguage),
        'Rows' => [
            'inc_pars_clinical',
            'inc_user_action',
            'inc_agg_issues',
            'inc_pars_pri_type',
            'inc_pars_sec_type',
            'inc_pol_called',
            'inc_pol_call_time',
            'inc_pol_attend',
            'inc_pol_att_time',
            'inc_pol_action',
            'inc_pol_crime_no',
            'inc_pars_address',
            'inc_postcode',
            'show_assailant',
            'inc_tprop_damaged',
        ],
    ],
    'pas' => [
        'Title' => 'PAS',
        'Condition' => bYN(GetParm('SHOW_OLD_PAS', 'N')),
        'Rows' => [
            'inc_pasno1',
            'inc_pasno2',
            'inc_pasno3',
        ],
    ],
    'causal_factor_header' => [
        'Title' => _fdtk('causal_factors_for', $useFormDesignLanguage) . ' ' . _fdtk('INCNameTitle', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            'inc_causal_factors_linked',
        ],
    ],
    'causal_factor' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design'),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'DoCausalFactorsSection' => [
                'controller' => src\causalfactors\controllers\CausalFactorsController::class,
            ],
        ],
        'ExtraParameters' => ['causal_factor_name' => 'inc_causal_factor'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'causal_factor_design' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'causal_factor',
        'NoSectionActions' => true,
        'Rows' => [
            'caf_level_1',
            'caf_level_2',
        ],
    ],
    'openness_transparency' => [
        'Title' => _fdtk('openness_transparency_title', $useFormDesignLanguage),
        'Rows' => [
            'inc_ot_q1',
            'inc_ot_q2',
            'inc_ot_q3',
            'inc_ot_q4',
            'inc_ot_q5',
            'inc_ot_q6',
            'inc_ot_q7',
            'inc_ot_q8',
            'inc_ot_q9',
            'inc_ot_q10',
            'inc_ot_q11',
            'inc_ot_q12',
            'inc_ot_q13',
            'inc_ot_q14',
            'inc_ot_q15',
            'inc_ot_q16',
            'inc_ot_q17',
            'inc_ot_q18',
            'inc_ot_q19',
            'inc_ot_q20',
        ],
        'NotModes' => ['Search'],
    ],
    'falls' => [
        'Title' => _fdtk('falls', $useFormDesignLanguage),
        'Rows' => [
            'fall_unassisted_assisted',
            'fall_observed_by',
            'fall_physical_injury',
            'fall_patient_doing',
            'fall_patient_doing_other',
            'fall_risk_assessment',
            'fall_protocols_in_place',
            'fall_score',
            'fall_medication_risk',
            'fall_fell_from',
            'fall_post_fall_action',
            'fall_risk_assessment_score',
        ],
    ],
    'fire' => [
        'Title' => _fdtk('fire', $useFormDesignLanguage),
        'Rows' => [
            'fire_detected',
            'fire_cause_known',
            'fire_action_taken',
            'fire_department_response',
            'fire_department_time_taken',
        ],
    ],
    'blood_transfusion' => [
        'Title' => _fdtk('blood_transfusion', $useFormDesignLanguage),
        'Rows' => [
            'blood_date_transfusion_started',
            'blood_time_transfusion_started',
            'blood_time_transfusion_stopped',
            'blood_date_reaction_started',
            'blood_time_reaction_started',
            'blood_amount_transfused',
        ],
    ],
    'spsc_national_data' => [
        'Title' => _fdtk('spsc_national_data', $useFormDesignLanguage),
        'Condition' => ($showSpscFields || $showNationalDataset),
        'Rows' => [
            ['Name' => IncidentsFields::SPSC_INTERNATIONAL_GOALS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_NATIONAL_GOALS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_SURGERY_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_MORBIDITY_ANTICIPATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_REASON_FOR_LAMA, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_RECOVERY_DATE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VISIT_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_HAZARDOUS_SUBSTANCE_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_INJURY_LEVEL, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_CODE_WHITE_PERSON_INJURY, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_CODE_WHITE_GENDER, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_OCCURRED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_LOCATION_DVT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CONFIRMED_DVT, 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::SPSC_WAS_VTE_RISK_ASSESSMENT_DOCUMENTED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PATIENTS_DOCUMENTED_RISK_OF_VTE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_DOCUMENTED_RISK_OF_BLEEDING, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PHARMACOLOGICAL_MECHANICAL_PROPHYLAXIS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PARMACOLOGICAL_ANTICOAGULANT_ADMINISTERED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHY_ANTICOAGULANT_NOT_GIVE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_MECHANICAL_PROPHYLAXIS_APPLIED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CORFIRMED_PE, 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::SPSC_VTE_OUTCOME, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_SUB_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_FEMALE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_BLOOD_TYPE_PRODUCT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_RED_CODE_ACTIVATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_FIRE_ALARM_ACTIVATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_BUILDING_EVACUATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_PORTABLE_FIRE_EXTINGUISHERS_USED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_LOCAL_FIRE_DEPARTMENT_INFORMED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WERE_ANY_DAMAGES_TO_PROPERTY, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_MORBIDITY_RECORD_IS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_IS_TRIGGER_RELATED_TO_IMPROPER_ASSESSMENT_OF_PATIENT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_ADDITIONAL_MORBIDITY_TRIGGERS, 'Condition' => $showSpscFields || $showNationalDataset],
        ],
    ],
    'pressure_ulcers' => [
        'Title' => _fdtk('pressure_sores', $useFormDesignLanguage),
        'NotModes' => ['Search', 'Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showPressureUlcerDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'pressure_ulcers'],
        'Rows' => [],
    ],
    'pressure_ulcers_design' => [
        'Title' => _fdtk('pressure_sores', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showPressureUlcerDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'pressure_ulcers',
        'NoSectionActions' => true,
        'Rows' => [
            'hospital',
            'location',
            'nature',
            'advanced_stage',
            'admission_status',
            'admission_status_3_4',
            'skin_inspection',
            'risk_assessment',
            'risk_assessment_type',
            'increased_risk',
            'preventative_intervention_yn',
            'preventative_intervention',
            'preventative_intervention_detail',
            'device',
            'device_type',
            'device_detail',
            'secondary_morbidity',
            'risk_assessment_score',
        ],
    ],
    'skin_lesions' => [
        'Title' => _fdtk('skin_lesions', $useFormDesignLanguage),
        'NotModes' => ['Search', 'Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showSkinLesionDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'skin_lesions'],
        'Rows' => [],
    ],
    'skin_lesions_design' => [
        'Title' => _fdtk('skin_lesions', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showSkinLesionDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'skin_lesions',
        'NoSectionActions' => true,
        'Rows' => [
            'site',
            'development_level',
        ],
    ],
    'sharps' => [
        'Title' => _fdtk('sharps', $useFormDesignLanguage),
        'NotModes' => ['Search', 'Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showSharpsDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'sharps'],
        'Rows' => [],
    ],
    'sharps_design' => [
        'Title' => _fdtk('sharps', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showSharpsDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'sharps',
        'NoSectionActions' => true,
        'Rows' => [
            'nature_of_injury',
            'type_of_tool',
            'contamination',
            'contamination_type',
            'contamination_other',
        ],
    ],
    'adverse_drug_reactions' => [
        'Title' => _fdtk('adverse_drug_reactions', $useFormDesignLanguage),
        'Condition' => $showSpscFields || $showAdverseReactionDataset,
        'Rows' => [
            IncidentsFields::INC_NARANJO_TOTAL_SCORE,
            IncidentsFields::INC_ADR_PROBABILITY_SCALE,
            IncidentsFields::INC_ADR_ACTION_TAKEN,
            IncidentsFields::INC_ADR_ORGAN_SYSTEM_FIELDS,
            IncidentsFields::INC_ADR_TOTAL_SCORE,
        ],
    ],
    'contacts_type_L' => [
        'Condition' => !($_GET['submitandprint'] == 1),
        'Title' => _fdtk('alleged_assailant_details', $useFormDesignLanguage),
        'ContactTitle' => 'Alleged assailant',
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'L',
        'suffix' => 4,
        'LinkedForms' => ['L' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_P' => [
        'Condition' => !($_GET['submitandprint'] == 1),
        'Title' => _fdtk('police_officer_details', $useFormDesignLanguage),
        'ContactTitle' => 'Police officer',
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'P',
        'suffix' => 5,
        'LinkedForms' => ['P' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_R' => [
        'Title' => _fdtk('details_of_person_reporting_incident', $useFormDesignLanguage),
        'Condition' => $inc['anon_reporting'] != true,
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => 'INC',
        'Special' => 'DynamicContact',
        'contacttype' => 'R',
        'Role' => GetParm('REPORTER_ROLE', 'REP'),
        'LinkedForms' => ['R' => ['module' => 'CON']],
        'suffix' => 3,
        'ContactSuffix' => 3,
        'Rows' => [],
    ],
    'your_manager' => [
        'Title' => _fdtk('your_manager', $useFormDesignLanguage),
        'module' => 'INC',
        'Condition' => bYN(GetParm('DIF_EMAIL_MGR', 'N')),
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Type' => 'formfield',
                'Name' => 'inc_mgr',
                'NoReadOnly' => true,
                'Title' => 'Your Manager',
                'FormField' => $managerDropdownField,
            ],
        ],
    ],
    'contacts_type_A' => [
        'Title' => _fdtk('table_link_contacts_INC_A', $useFormDesignLanguage),
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'A',
        'suffix' => 6,
        'LinkedForms' => ['A' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_W' => [
        'Title' => _fdtk('witnesses', $useFormDesignLanguage),
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'W',
        'suffix' => 8,
        'LinkedForms' => ['W' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_E' => [
        'Title' => _fdtk('employee_plural', $useFormDesignLanguage),
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'E',
        'suffix' => 7,
        'LinkedForms' => ['E' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_N' => [
        'Title' => _fdtk('contacts', $useFormDesignLanguage),
        'module' => 'INC',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'N',
        'LinkedForms' => ['N' => ['module' => 'CON']],
        'suffix' => 9,
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'extra_document' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true),
        'NoReadOnly' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],
    'previous_documents' => [
        'Title' => _fdtk('previously_uploaded_documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_NEW], true),
        'NoReadOnly' => true,
        'ControllerAction' => [
            'listalreadylinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'linked_documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_PRINT,
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'vanessas_law' => [
        'Title' => _fdtk('vanessas_law_compliance', $useFormDesignLanguage),
        'Condition' => $isVanessaLawEnabled,
        'Rows' => [
            ['Name' => IncidentsFields::VLC_DRUG_DOCUMENTED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DRUG_REACTION_EFFECT, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_MEDICAL_CONDITION, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HEALTH_RESTORED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_CONCOMITANT_USED, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_DOCUMENTED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_EFFECT_HEALTH, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_CONTRIBUTION, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HOSPITAL_REP_EMAIL_ID, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HEALTH_CANADA_INST_ID, 'Condition' => $isVanessaLawEnabled],
        ],
    ],
    IncidentsSections::PSIMS => [
        'Title' => _fdtk('inc_psims_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_DETECTION_POINT_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_WENT_WELL, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RESPONSIBLE_SPECIALTY_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PATIENT_EVENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTIMATED_TIME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_TIMEFRAME_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_POPULATION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ORGANISATION, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_RISK => [
        'Title' => _fdtk('inc_psims_risk_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_THEME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_THEME_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_SERVICE_AREA, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_DESCRIPTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_IDENTIFIED_LOCATION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_AT_RISK, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_WENT_WELL => [
        'Title' => _fdtk('inc_psims_went_well_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_CARE_DETECTION_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_GOOD_CARE_DETECTION_FACTOR_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_HOW_FUTURE_OCCURRENCE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_MEDICATIONS => [
        'Title' => _fdtk('inc_psims_medications_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVEMENT_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_USED_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INSUFFICIENT_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_GIVEN_INCORRECTLY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::PROBLEM_DESCRIPTION_DRUGS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::PROBLEM_MEDS_PACKAGING, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_REACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_REACTION_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_EQUIPMENT => [
        'Title' => _fdtk('inc_psims_equipment_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USAGE_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::PSIMS_DEVICE_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_TYPE_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_BROKEN_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_NOT_ENOUGH_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USED_UNNECESSARILY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USAGE_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_PROBLEM_DESCRIPTION, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_IT_SYSTEMS_SOFTWARE => [
        'Title' => _fdtk('inc_psims_it_systems_software_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_IT_INVOLVEMENT_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_SYSTEMS_SOFTWARE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_TISSUES_ORGANS => [
        'Title' => _fdtk('inc_psims_tissues_organs_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_NHSBT_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_INVOLVEMENT_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DEFICIENT_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_WRONG_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_USED_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DAMAGED_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_TISSUES_ORGANS, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_INVOLVED_PERSONS => [
        'Title' => _fdtk('inc_psims_involved_persons_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_INVOLVED_PERSONS_ACTIONS_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_WRONG_ACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_OMITTED_ACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_ACTIONS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_UNAVAILABLE_DETAIL, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_INVOLVEMENT, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_REPORTER => [
        'Title' => _fdtk('inc_psims_reporter', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_ROLE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_ROLE_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_AGENT => [
        'Title' => _fdtk('inc_psims_adverse_event_agent', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_SABRE_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_SHOT_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_YELLOW_CARD_REFERENCE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BUILDINGS_INFRASTRUCTURE => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_buildings_infrastructure', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BUILDINGS_INFRASTRUCTURE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_PROBLEM, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_ESTATES_SERVICES => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_estates_services', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_ESTATES_SERVICES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_PROBLEM, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_blood', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_WAS_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PROBLEM, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD_PRODUCTS => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_blood_products', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BRAND, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BATCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_PROBLEM, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES => [
        'Title' => _fdtk('inc_psims_adverse_event_safety_challenges', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::TIME_FIELDS => SharedSections::getTimeFieldSection($useFormDesignLanguage),
];

$lfpseSectionKeys = IncidentsSections::getLFPSESections();
$FormArray = PsimsFieldPropertyManager::preventPsimsFieldConfiguration(
    $FormArray,
    $lfpseSectionKeys,
    $formlevel ?? $formLevel ?? $level,
);

if (is_array($RejectionArray) && bYN(GetParm('REJECT_REASON', 'Y'))) {
    $FormArray['rejection'] = ['Title' => _fdtk('details_rejection', $useFormDesignLanguage), 'Rows' => $RejectionArray];
}

return $FormArray;
