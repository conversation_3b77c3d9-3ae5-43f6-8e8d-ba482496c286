<?php

declare(strict_types=1);

namespace Source\incidents;

use src\component\form\FormProperties;
use src\incidents\model\IncidentsFields;

class SharedSections
{
    public static function getTimeFieldSection(bool $useFormDesignLanguage = false): array
    {
        return [
            FormProperties::TITLE => _fdtk('time_field_section', $useFormDesignLanguage),
            FormProperties::ROWS => [
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_1],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_2],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_3],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_4],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_5],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_1],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_2],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_3],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_4],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_5],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_6],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_FROM_7],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_1],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_2],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_3],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_4],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_5],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_6],
                [FormProperties::FIELD_NAME => IncidentsFields::TIME_FIELD_TO_7],
            ],
        ];
    }
}
