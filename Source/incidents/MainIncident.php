<?php

function ListContacts($data, $con, $ReadOnly, $FormType)
{
    ListContactsSection([
        'data' => $data,
        'module' => 'INC',
        'formtype' => $FormType,
    ]);
}

function ListContactType($inc, $FormType, $ConType, $Header, $EmailCheckBox = false, $ConItemName = 'contact')
{
    global $scripturl;

    $AdminUser = $_SESSION['AdminUser'];
    $DIFPerms = $_SESSION['Globals']['DIF_PERMS'];

    if ($DIFPerms == 'DFI2_READ_ONLY') {
        $EmailCheckBox = false;
    }

    if ($EmailCheckBox) {
        $ColSpan = 8;
    } else {
        $ColSpan = 7;
    }

    $LinkPerms = (!$_GET['print']);

    $IncID = $inc['recordid'];

    $sql = "SELECT con_id, link_type, con_title, con_forenames, con_surname,
        con_type, con_subtype, con_specialty
        FROM contacts_main, link_contacts
        WHERE link_contacts.inc_id = {$IncID}
        AND contacts_main.recordid = link_contacts.con_id";

    $ContactArray = DatixDBQuery::PDO_fetch_all($sql);

    foreach ($ContactArray as $con1) {
        $con[] = $con1;
    }
    // $con[$con1["link_type"]][] = $con1;

    echo '
<tr><td colspan="2" class="windowbg2">
    <table class="titlebg" cellspacing="1" cellpadding="0" width="100%" align="center" border="0">
<tr><td class="titlebg">';

    echo '
<tr>
<td>
<table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">';
    echo '
<tr>
    <td class="titlebg" colspan="' . $ColSpan . '">
    ' . $Header . '
    </td>
</tr>
</table>';

    $IncludeColsList = 'con_subtype,con_specialty';
    DoContactSection('Contacts', $ConType, $IncID, $con, $ColSpan, $EmailCheckBox, $FormType, $IncludeColsList);

    echo '</td></tr></table></td></tr>';
}

function DoContactSection(
    $Header,
    $ConType,
    $IncID,
    $con,
    $ColSpan,
    $EmailCheckBox,
    $FormType,
    $IncludeColsList
) {
    $Div = $Header;

    $IncludeCols = explode(',', $IncludeColsList);

    if ($_GET['print']) {
        $EmailCheckBox = false;
        --$ColSpan;
    }

    $contactSectionId = $ConType . '_' . $Div;

    echo '
<tr height="20">
    <td class="titlebg" colspan="' . $ColSpan . '">
        <span class="toggle-trigger closed" data-target="' . $contactSectionId . '"></span>
    </td>
</tr>
<tr>
<td>
<div id="' . $contactSectionId . '" style="display:none">
<table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
<tr>
';
    if ($EmailCheckBox) {
        echo '<td class="windowbg" width="1%">
                    <input type="checkbox" id = "email_check" name="email_check" ' .
                    'onclick="ToggleCheckAll(\'email_include_' . $ConType . '_' . $Div . '\', this.checked)"/>
                </td>';
    }

    echo '<td class="windowbg" width="25%">
                ' . _fdtk('name') . '
            </td>';

    echo '</tr>';

    if (!$con) {
        echo '
<tr>
    <td class="windowbg2" colspan="' . $ColSpan . '">
    ' . _fdtk('no_contacts') . '
    </td>
</tr>';
    } else {
        foreach ($con as $contact) {
            echo '
<tr>';
            if ($EmailCheckBox) {
                echo '<td class="windowbg2" width="1%">
                <input type="checkbox" id="email_include_' . $ConType . '_' . $Div . '" name="email_include_' .
                    $contact['con_id'] . '" onclick="" />
                </td>';
            }

            echo '<td class="windowbg2" valign="middle">';
            echo "{$contact['con_title']} {$contact['con_forenames']} {$contact['con_surname']}";
            if (!$_GET['print']) {
                echo '</a>';
            }
            echo '</td>';
            echo '</tr>';
        }
    }
    echo '</td></tr></table></div>';
}

function getDIF1OldValuesUDF($inc_num)
{
    $sql = "

		SELECT
			REPLACE(a.AUD_ACTION,'WEB:',''),
			(	SELECT TOP 1 AUD_DETAIL
				FROM full_audit
				WHERE AUD_MODULE = 'INC' AND AUD_RECORD = :aud_record AND AUD_ACTION = a.AUD_ACTION
				ORDER BY AUD_DATE
			) as AUD_DETAIL
		FROM (SELECT DISTINCT AUD_ACTION, AUD_RECORD, AUD_MODULE FROM full_audit) a
		WHERE a.AUD_RECORD = :aud_record2 AND a.AUD_MODULE = 'INC' AND a.AUD_ACTION LIKE 'WEB:UDF_%'

	";

    return DatixDBQuery::PDO_fetch_all($sql, [
        'aud_record' => $inc_num,
        'aud_record2' => $inc_num,
    ], PDO::FETCH_KEY_PAIR);
}
