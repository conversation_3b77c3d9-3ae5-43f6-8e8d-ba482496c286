<?php

use app\models\generic\valueObjects\Module;
use src\component\field\CustomFieldFactory;
use src\component\field\NumberFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTableFactory;
use src\formdesign\forms\service\Loaders\FormDesignInstanceLoader;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;

function SectionInjuryDetails($inc, $FormType, $module, $suffix = 0, $section = '', $Type = '')
{
    global $ReadOnlyFields, $HideFields, $UserLabels, $MandatoryFields, $JSFunctions, $ModuleDefs, $hideAddInjuryButton;

    $registry = Container::get(Registry::class);

    $language = LanguageSessionFactory::getInstance()->getLanguage();

    $SuffixString = (is_numeric($suffix) && $suffix > 0 ? '_' . $suffix : '');

    $showEdiFields = $registry->getParm('SHOW_EDI_FIELDS')->isTrue();

    if (!empty($section) && is_array($ReadOnlyFields) && array_key_exists($section, $ReadOnlyFields)) {
        $FormType = 'ReadOnly';
    }

    if (is_array($HideFields)) {
        if (array_key_exists('dum_injury' . $SuffixString, $HideFields)) {
            $injuryHidden = true;
        }
        if (array_key_exists('dum_bodypart' . $SuffixString, $HideFields)) {
            $bodyHidden = true;
        }
        if (array_key_exists('dum_treatment' . $SuffixString, $HideFields)) {
            $treatmentHidden = true;
        }
        if (array_key_exists('dum_death_result' . $SuffixString, $HideFields) || !$showEdiFields) {
            $deathResultHidden = true;
        }
        if (array_key_exists('dum_impaired_percent' . $SuffixString, $HideFields) || !$showEdiFields) {
            $impairedPercentHidden = true;
        }
    }

    if (is_array($ReadOnlyFields)) {
        if (array_key_exists('dum_injury' . $SuffixString, $ReadOnlyFields)) {
            $injuryReadOnly = true;
        }
        if (array_key_exists('dum_bodypart' . $SuffixString, $ReadOnlyFields)) {
            $bodyReadOnly = true;
        }
        if (array_key_exists('dum_treatment' . $SuffixString, $ReadOnlyFields)) {
            $treatmentReadOnly = true;
        }
        if (array_key_exists('dum_death_result' . $SuffixString, $ReadOnlyFields) && $showEdiFields) {
            $deathResultReadOnly = true;
        }
        if (array_key_exists('dum_impaired_percent' . $SuffixString, $ReadOnlyFields) && $showEdiFields) {
            $impairedPercentReadOnly = true;
        }
    }

    $Table = FormTableFactory::create($FormType, 'INC');
    $Table->setCurrentSection('injuries');

    $InjuryKey = 'injury' . $SuffixString;
    $BodyPartKey = 'bodypart' . $SuffixString;

    // determine the route through to the injuries table for this contact type
    if ($Type == '') {
        // use the default, contact type-independent route
    } else {
        $fieldsetMap = [
            'A' => 62,
            'E' => 75,
            'N' => 76,
        ];
        $fieldset = $fieldsetMap[$Type];
    }

    $Injuries = '
    <div style="overflow:auto">
';

    if ($FormType != 'Print' && $registry->getParm('ENABLE_BODYMAP', 'N')->isTrue()) {
        $Injuries .= constructBodyMap($language, $suffix);
    }

    if ($FormType != 'Print') {
        $RetainComboChildren = bYN(GetParm('RETAIN_COMBO_CHILDREN', 'Y'));
        $Injuries .= '<input type="hidden" id="RETAIN_COMBO_CHILDREN" value="' . $RetainComboChildren . '"/>';
    }

    if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked' && !($injuryReadOnly && $bodyReadOnly)) {
        $fieldLabels = $registry->getFieldLabels();

        $InjuryTitle =
            $UserLabels['dum_injury' . $SuffixString][$language] ??
            $fieldLabels->getLabel('inc_injuries', 'inc_injury', 'CONTACTS') ??
            _fdtk('injury');

        $BodyTitle =
            $UserLabels['dum_bodypart' . $SuffixString][$language] ??
            $fieldLabels->getLabel('inc_injuries', 'inc_bodypart', 'CONTACTS') ??
            _fdtk('body_part');

        $deathResultTitle =
            $UserLabels['dum_death_result' . $SuffixString][$language] ??
            $fieldLabels->getLabel('link_injuries', 'death_result_injury', 'CONTACTS') ??
            _fdtk('death_result_injury');


        $impairedPercentTitle =
            $UserLabels['dum_impaired_percent' . $SuffixString][$language] ??
            $fieldLabels->getLabel('link_injuries', 'permanent_impairment_percentage', 'CONTACTS') ??
            _fdtk('permanent_impairment_percentage');

        $Injuries .= '
        <table id="injury_table' . $SuffixString . '" class="bordercolor" cellspacing="1" cellpadding="5" align="left" border="0" style="min-width:570px;">
        <tr>';


        if (!$injuryHidden) {
            $Injuries .= '
            <td class="titlebg" align="left" width="250">';

            if ($MandatoryFields['dum_injury' . $SuffixString] && $FormType != 'Print') {
                $Injuries .= '<img src="images/Warning.gif" />';
            }
            $Injuries .= $InjuryTitle . '</td>';
        }

        if (!$bodyHidden) {
            $Injuries .= '
            <td class="titlebg" align="left" width="250">';

            if ($MandatoryFields['dum_bodypart' . $SuffixString] && $FormType != 'Print') {
                $Injuries .= '<img src="images/Warning.gif" />';
            }

            $Injuries .= $BodyTitle . '</td>';
        }

        if ($showEdiFields && !$deathResultHidden) {
            $Injuries .= '
            <td class="titlebg" align="left" width="250">';

            if ($MandatoryFields['dum_death_result' . $SuffixString] && $FormType != 'Print') {
                $Injuries .= '<img src="images/Warning.gif" />';
            }

            $Injuries .= $deathResultTitle . '</td>';
        }

        if ($showEdiFields && !$impairedPercentHidden) {
            $Injuries .= '
            <td class="titlebg" align="left" width="250">';

            if ($MandatoryFields['dum_impaired_percent' . $SuffixString] && $FormType != 'Print') {
                $Injuries .= '<img src="images/Warning.gif" />';
            }

            $Injuries .= $impairedPercentTitle . '</td>';
        }

        $Injuries .= '
            <td class="titlebg" align="left" width="50" style="width: 5%; min-width: 60px;">
            </td>
        </tr>
        ';
    }

    if ($inc['injury_table']) { // from database
        $inc['injury'] = Sanitize::SanitizeStringArray($inc['injury_table']);
    } else { // from post
        if (is_array($inc[$InjuryKey])) {
            foreach ($inc[$InjuryKey] as $i => $inj) {
                $injArray[] = [$inj, $inc[$BodyPartKey][$i]];
            }
            $inc['injury'] = $injArray;
        }
    }

    $children = GetChildren(['module' => 'INC', 'field' => 'inc_injury']);

    $RowSuffix = 1;
    $mainModule = $inc['main_module'];

    if ($inc['injury'] != '') {
        if (is_array($inc['injury'])) {
            foreach ($inc['injury'] as $Injury) {
                if ($inc['main_module'] == 'INC' && !array_key_exists('inc_injury', $Injury)) {
                    $Injury['inc_injury'] = Sanitize::SanitizeString($Injury[0]); // Injected to page later
                    $Injury['inc_bodypart'] = Sanitize::SanitizeString($Injury[1]); // Injected to page later
                    $Injury['recordid'] = Sanitize::SanitizeString($Injury[2]); // Injected to page later
                    $Injury['death_result_injury'] = Sanitize::SanitizeString($Injury['death_result_injury']);
                    $Injury['permanent_impairment_percentage'] = Sanitize::SanitizeString($Injury['permanent_impairment_percentage']);
                    $Injury['listorder'] = Sanitize::SanitizeString($Injury[3]); // Injected to page later
                } elseif (in_array($mainModule, [Module::CLAIMS, Module::SAFEGUARDING], true) && !array_key_exists('inc_injury', $Injury)) {
                    $Injury['inc_injury'] = Sanitize::SanitizeString($Injury['injury']);
                    $Injury['inc_bodypart'] = Sanitize::SanitizeString($Injury['bodypart']);
                    $Injury['death_result_injury'] = Sanitize::SanitizeString($Injury['death_result_injury']);
                    $Injury['permanent_impairment_percentage'] = Sanitize::SanitizeString($Injury['permanent_impairment_percentage']);
                    $Injury['listorder'] = Sanitize::SanitizeString($Injury['listorder']);
                }

                if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked') {
                    $Injuries .= getInjuryRow([
                        'data' => [
                            'link_injury' => $Injury['inc_injury'],
                            'link_bodypart' => $Injury['inc_bodypart'],
                            'death_result_injury' => $Injury['death_result_injury'],
                            'permanent_impairment_percentage' => $Injury['permanent_impairment_percentage'],
                        ],
                        'module' => $module,
                        'type' => $Type,
                        'person_suffix' => $suffix,
                        'row_suffix' => $RowSuffix,
                        'formtype' => $FormType,
                        'hidden' => [
                            'injury' => $injuryHidden,
                            'bodypart' => $bodyHidden,
                            'death_result' => $deathResultHidden,
                            'impaired_percent' => $impairedPercentHidden,
                        ],
                        'readonly' => [
                            'injury' => $injuryReadOnly,
                            'bodypart' => $bodyReadOnly,
                            'death_result' => $deathResultReadOnly,
                            'impaired_percent' => $impairedPercentReadOnly,
                        ],
                        'fieldset' => $fieldset,
                    ]);
                } else {
                    $Injuries .= code_descr('INC', 'link_injury1', $Injury['inc_injury']);
                    if ($Injury['inc_bodypart']) {
                        $Injuries .= ' - ' . code_descr('INC', 'link_bodypart1', $Injury['inc_bodypart']);
                    }
                    $Injuries .= '<br />';
                }

                if ((!($FormType == 'Search' && bYN(GetParm('NO_SEARCH_ALERTS', 'N')))) || !empty($children[0])) {
                    $OnChangeFunc = 'function OnChange_injury_' . $RowSuffix . '() {
                    ';

                    foreach ($children as $childField) {
                        if ($childField != '') {
                            if ($childField == 'inc_bodypart') {
                                $childField = 'bodypart';
                            }
                            $OnChangeFunc .= 'if (jQuery(\'#' . $childField . '_' . $RowSuffix . '_title\').length){jQuery(\'#' . $childField . '_' . $RowSuffix . '_title\').checkClearChildField(' . ($RetainComboChildren ? 'true' : 'false') . ');}';
                        }
                    }

                    $OnChangeFunc .= '}';

                    $JSFunctions[] = $OnChangeFunc;
                }

                ++$RowSuffix;
            }
        }
    } elseif ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked') {
        $Injuries .= getInjuryRow([
            'data' => $inc,
            'module' => $module,
            'type' => $Type,
            'row_suffix' => 1,
            'person_suffix' => $suffix,
            'formtype' => $FormType,
            'hidden' => [
                'injury' => $injuryHidden,
                'bodypart' => $bodyHidden,
                'death_result' => $deathResultHidden,
                'impaired_percent' => $impairedPercentHidden,
            ],
            'readonly' => [
                'injury' => $injuryReadOnly,
                'bodypart' => $bodyReadOnly,
                'death_result' => $deathResultReadOnly,
                'impaired_percent' => $impairedPercentReadOnly,
            ],
            'fieldset' => $fieldset,
        ]);

        if ((!($FormType == 'Search' && bYN(GetParm('NO_SEARCH_ALERTS', 'N')))) || !empty($children[0])) {
            $OnChangeFunc = 'function OnChange_injury' . ($suffix ? '_' . $suffix : '') . '_1() {
            ';

            foreach ($children as $childField) {
                if ($childField != '') {
                    if ($childField == 'inc_bodypart') {
                        $childField = 'bodypart';
                    }
                    $OnChangeFunc .= 'if (jQuery(\'#' . $childField . ($suffix ? '_' . $suffix : '') . '_1_title\').length){jQuery(\'#' . $childField . ($suffix ? '_' . $suffix : '') . '_1_title\').checkClearChildField(' . ($RetainComboChildren ? 'true' : 'false') . ');}';
                }
            }

            $OnChangeFunc .= '}';

            $JSFunctions[] = $OnChangeFunc;
        }
    }

    if (!$hideAddInjuryButton && $FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked' && $FormType != 'Search' && !(($injuryReadOnly && $bodyReadOnly) || ($injuryReadOnly && $bodyHidden) || ($injuryHidden && $bodyReadOnly))) {
        $Injuries .= '<tr><td colspan="3"><input type="button" value="' . _fdtk('add_another_injury') . '" style="align:right" nextSuffix="' . ($RowSuffix + 1) . '" onclick="addInjury(this, \'' . $suffix . '\', \'' . $Type . '\', ' . (is_numeric($_REQUEST['form_id']) ? Sanitize::SanitizeInt($_REQUEST['form_id']) : 'null') . ').catch(function(msg){alert(msg);})" /></td></tr>';
    }

    if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked' && !($injuryReadOnly && $bodyReadOnly)) {
        $Injuries .= '</table>';
    }

    if ($FormType != 'Print') {
        $Injuries .= '
            <input type="hidden" name="CHANGED-link_treatment" id="CHANGED-link_treatment" />
            <script type="text/javascript" src="js_functions/bodymap.min.js" ></script>
        ';

        if ($registry->getParm('ENABLE_BODYMAP', 'N')->isTrue()) {
            $Injuries .= '
        </div>
        ';
            $JSFunctions[] = 'bodymapInitialise("' . $suffix . '");';
        }
    }

    if (!($injuryHidden && $bodyHidden)) {
        $InjuriesObj = CustomFieldFactory::create($FormType, $Injuries);
        $Table->makeRow(_fdtk('injuries') . '<br/>' . GetValidationErrors($inc, 'injury' . ($suffix ? '_' . $suffix : '')), $InjuriesObj);
    }

    $TreatmentLabel = FirstNonNull([$UserLabels['dum_treatment' . $SuffixString][$language], _fdtk('dum_treatment')]);

    $TreatmentTitle = $TreatmentLabel;

    if ($MandatoryFields['dum_treatment' . $SuffixString] && $FormType != 'Print') {
        // This is a bit of a hack - we should think about making the injury sections on DIF1 and DIF2 have the same name.
        if (!$suffix) { // implies we're on DIF2
            $sectionPrefix = 'injury';
        } else {
            $sectionPrefix = 'injury_section';
        }

        $contactSectionLabel = $ModuleDefs[$inc['main_module']]['LEVEL1_CON_OPTIONS'][$Type]['Title'];
        $treatementLabelWithSection = $TreatmentLabel . ' (' . $contactSectionLabel . ')';

        $MandatoryFields['link_treatment' . $SuffixString] = $sectionPrefix . $SuffixString;
        $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("link_treatment' . $SuffixString . '","' . $sectionPrefix . $SuffixString . '","' . $treatementLabelWithSection . '"))};';
        $TreatmentTitle = '<img src="images/Warning.gif" /> ' . $TreatmentTitle . '<div class="field_error" id="errlink_treatment' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
    }

    if ($treatmentReadOnly) {
        $FormType = 'ReadOnly';
    }

    if (!$treatmentHidden) {
        $table = 'link_contacts';

        $treatmentFieldset = $ModuleDefs['INC']['FIELDSET_MAPPINGS'][$Type];

        if ($treatmentFieldset > 0) {
            $table = $treatmentFieldset . '|' . $table;
        }

        $data = ($FormType == 'Search') ? $inc[$table . '|link_treatment'] : $inc['link_treatment' . $SuffixString];

        $TreatmentObj = SelectFieldFactory::createSelectField('link_treatment', 'INC', Sanitize::SanitizeString($data), $FormType, false, $TreatmentLabel, 'injuries', Sanitize::SanitizeInt($inc['CHANGED-link_treatment' . $SuffixString]), $suffix, $table);
        $TreatmentObj->setAltFieldName('link_treatment' . $SuffixString);
        $Table->makeRow($TreatmentTitle, $TreatmentObj);
        // In print mode, the form is static, so we can ignore currently hidden sections and fields.
        if ($FormType != 'Print') {
            echo '
            <input type="hidden" name="show_field_link_treatment' . $SuffixString . '" id="show_field_link_treatment' . $SuffixString . '" value="1" />';
        }
    }

    $Table->makeTable();

    echo '<li>' . $Table->getFormTable() . '</li>';
}

function constructBodyMap($language, $suffix = null)
{
    $html = '
            <svg
               xmlns:dc="http://purl.org/dc/elements/1.1/"
               xmlns:cc="http://creativecommons.org/ns#"
               xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
               xmlns:svg="http://www.w3.org/2000/svg"
               xmlns="http://www.w3.org/2000/svg"
               viewBox="0 0 748.79999 711.59998"
               height="711.59998"
               width="748.79999"
               version="1.1"
               id="bodymapsvg2">
                  <metadata
                     id="metadata8">
                    <rdf:RDF>
                      <cc:Work
                         rdf:about="">
                        <dc:format>image/svg+xml</dc:format>
                        <dc:type
                           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                        <dc:title></dc:title>
                      </cc:Work>
                    </rdf:RDF>
                  </metadata>
              <defs
                 id="defs6" />
               <g
             id="group_front">
     ';

    $sql = 'SELECT b.bodypart_group, b.bodypart_name, b.meta, COALESCE (c.code, b.bodypart_code) as bodypart_code, COALESCE(d.description, c.description) as description
            FROM bodymap_meta AS b
            LEFT JOIN code_inc_bodypart AS c
            ON (b.bodypart_code = c.cod_bodymap)
            LEFT JOIN code_inc_bodypart_descr AS d
            ON (c.code = d.code)
            WHERE d.language = :language or d.language IS NULL
            ';

    $bodyParts = DatixDBQuery::PDO_fetch_all($sql, ['language' => $language], PDO::FETCH_GROUP | PDO::FETCH_ASSOC);

    foreach ($bodyParts['group_front'] as $bodyPartFront) {
        $bodyPartFrontPath = '';
        $bodyPartFrontPath = '
            <path
               ' . $bodyPartFront['meta'] . '
               id="' . $bodyPartFront['bodypart_name'] . '"
               class="bodymappath"
               data-code="' . $bodyPartFront['bodypart_code'] . '"
               data-description="' . $bodyPartFront['description'] . '"
               data-suffix="' . $suffix . '"
            />
            ';
        $html .= $bodyPartFrontPath;
    }

    $html .= '
        <g
       id="facefeatures"
       style="stroke-width:0.9992126;stroke-miterlimit:4;stroke-dasharray:none;stroke-linejoin:round;stroke-linecap:round">
          <path
             id="path4231" class="bodymappath"
             d="m 202.2805,35.773294 -5.24515,-0.22805 -8.14646,6.157354 0,6.157355 0,8.437856"
             style="fill:none;fill-rule:evenodd;stroke:#222222;stroke-width:0.9992126;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:1;stroke-miterlimit:4;stroke-dasharray:none" />
          <path
             id="path4233" class="bodymappath"
             d="m 176.51083,69.980819 4.10491,-1.026225 4.561,-1.938427 3.71215,1.026226 3.01533,-0.912201 4.33295,1.824402 3.87686,1.14025"
             style="fill:none;fill-rule:evenodd;stroke:#222222;stroke-width:0.9992126;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:1;stroke-miterlimit:4;stroke-dasharray:none" />
          <path
             id="path4235" class="bodymappath"
             d="m 181.29989,73.857672 7.589,1.482326 6.66413,-1.482326"
             style="fill:none;fill-rule:evenodd;stroke:#222222;stroke-width:0.9992126;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:1;stroke-miterlimit:4;stroke-dasharray:none" />
        </g>
      </g>
      <g
         transform="translate(0,-4)"
         id="group_back">
     ';

    foreach ($bodyParts['group_back'] as $bodyPartBack) {
        $bodyPartBackPath = '';
        $bodyPartBackPath = '
            <path
                id="' . $bodyPartBack['bodypart_name'] . '"
                class="bodymappath"
                data-code="' . $bodyPartBack['bodypart_code'] . '"
                data-description="' . $bodyPartBack['description'] . '"
                data-suffix="' . $suffix . '"
                ' . $bodyPartBack['meta'] . '
            />
        ';
        $html .= $bodyPartBackPath;
    }

    $html .= '
            </g>
        </svg>
        <div class="bodymap-description"></div>
        ';

    return $html;
}

function constructInjuryCells($aParams)
{
    global $MandatoryFields, $JSFunctions, $UserLabels, $ModuleDefs;

    $language = LanguageSessionFactory::getInstance()->getLanguage();

    $InjuryCells = [];

    $row_suffix = $aParams['row_suffix'];
    $person_suffix = $aParams['person_suffix'];
    $table = 'inc_injuries';

    if ($aParams['fieldset'] > 0) {
        $table = $aParams['fieldset'] . '|' . $table;
    }

    // This is a bit of a hack - we should think about making the injury sections on DIF1 and DIF2 have the same name.
    if (!$person_suffix) { // implies we're on DIF2
        $sectionPrefix = 'injury';
    } else {
        $sectionPrefix = 'injury_section';
    }

    $ShortSuffixString = ($person_suffix ? '_' . $person_suffix : '');
    $SuffixString = ($person_suffix ? '_' . $person_suffix . '_' . $row_suffix : '_' . $row_suffix);

    $InjuryName = FirstNonNull([$UserLabels['dum_injury' . $ShortSuffixString][$language], _fdtk('injury')]);
    $BodyPartName = FirstNonNull([$UserLabels['dum_bodypart' . $ShortSuffixString][$language], _fdtk('body_part')]);
    $deathResultName = FirstNonNull([$UserLabels['dum_death_result' . $ShortSuffixString][$language], _fdtk('death_result_injury')]);
    $impairedPercentName = FirstNonNull([$UserLabels['dum_impaired_percent' . $ShortSuffixString][$language], _fdtk('permanent_impairment_percentage')]);

    $module = ($aParams['formtype'] == 'Search') ? 'CON' : ($_GET['module'] ?? $_POST['parent_module']);

    $contactSectionLabel = $ModuleDefs[$module]['LEVEL1_CON_OPTIONS'][$aParams['type']]['Title'];

    if (!$aParams['readonly']['injury']) {
        $name = ($aParams['formtype'] == 'Search') ? 'inc_injury' : 'link_injury1';
        $data = ($aParams['formtype'] == 'Search') ? $aParams['data'][$table . '|inc_injury'] : $aParams['data']['link_injury'];

        $Injury1 = SelectFieldFactory::createSelectField($name, $module, $data, $aParams['formtype'], false, $InjuryName, 'injuries', $aParams['data']['CHANGED-link_injury'], '', $table);
        $Injury1->setChildren(['bodypart' . $SuffixString]);
        $Injury1->setAltFieldName('injury' . $SuffixString);
        $Injury1->setOnChangeExtra('jQuery(\'#hidden_injury' . $SuffixString . '\').val(jQuery(\'#injury' . $SuffixString . '\').val())');
        $InjuryField = $Injury1->getField();

        // In print mode, the form is static, so we can ignore currently hidden sections and fields.
        if ($aParams['formtype'] != 'Print') {
            $InjuryField .= '
            <input type="hidden" name="show_field_injury' . $SuffixString . '" id="show_field_injury' . $SuffixString . '" value="1" />';
        }
    } else {
        $InjuryField = code_descr('INC', 'link_injury1', $aParams['data']['link_injury']);
    }

    $InjuryField .= '<input type="hidden" id="' . 'hidden_injury' . $SuffixString . '" name="injury' . $ShortSuffixString . '[]" value="' . $aParams['data']['link_injury'] . '" />';

    if ($MandatoryFields['dum_injury' . $ShortSuffixString]) {
        $injuryNameWithSection = $InjuryName . ' (' . $contactSectionLabel . ')';

        $InjuryField = $InjuryField . '<div class="field_error" id="errinjury' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
        $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("injury' . $SuffixString . '","' . $sectionPrefix . $ShortSuffixString . '","' . $injuryNameWithSection . '"))};';
    }

    if (!$aParams['hidden']['injury']) {
        $InjuryCells[] = $InjuryField;
    }

    if (!$aParams['readonly']['bodypart']) {
        $name = ($aParams['formtype'] == 'Search') ? 'inc_bodypart' : 'link_bodypart1';
        $data = ($aParams['formtype'] == 'Search') ? $aParams['data'][$table . '|inc_bodypart'] : $aParams['data']['link_bodypart'];

        $Bodypart1 = SelectFieldFactory::createSelectField($name, $module, $data, $aParams['formtype'], false, $BodyPartName, 'injuries', $aParams['data']['CHANGED-link_bodypart'], '', $table);
        $Bodypart1->setParents(['injury' . $SuffixString]);
        $Bodypart1->setAltFieldName('bodypart' . $SuffixString);
        $Bodypart1->setOnChangeExtra('jQuery(\'#hidden_bodypart' . $SuffixString . '\').val(jQuery(\'#bodypart' . $SuffixString . '\').val())');
        $BodyField = $Bodypart1->getField();

        // In print mode, the form is static, so we can ignore currently hidden sections and fields.
        if ($aParams['formtype'] != 'Print') {
            $BodyField .= '
            <input type="hidden" name="show_field_bodypart' . $SuffixString . '" id="show_field_bodypart' . $SuffixString . '" value="1" />';
        }
    } else {
        if ($aParams['readonly']['injury']) {
            $BodyField = ' - ';
        }
        $BodyField .= code_descr('INC', 'link_bodypart1', $aParams['data']['link_bodypart']);
    }

    $BodyField .= '<input type="hidden" id="' . 'hidden_bodypart' . $SuffixString . '" name="bodypart' . $ShortSuffixString . '[]" value="' . $aParams['data']['link_bodypart'] . '" />';

    if ($MandatoryFields['dum_bodypart' . $ShortSuffixString]) {
        $bodyPartNameWithSection = $BodyPartName . ' (' . $contactSectionLabel . ')';

        $BodyField = $BodyField . '<div class="field_error" id="errbodypart' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
        $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("bodypart' . $SuffixString . '","' . $sectionPrefix . $ShortSuffixString . '","' . $bodyPartNameWithSection . '"))};';
    }

    if (!$aParams['hidden']['bodypart']) {
        $InjuryCells[] = $BodyField;
    }

    if (!$aParams['readonly']['death_result']) {
        $name = ($aParams['formtype'] == 'Search') ? 'death_result_injury' : 'death_result_injury1';
        $data = ($aParams['formtype'] == 'Search') ? $aParams['data'][$table . '|death_result_injury'] : $aParams['data']['death_result_injury'];

        $deathResult1 = SelectFieldFactory::MakeYesNoSelect($name, $module, $data, $aParams['formtype'], $ExtraChange = '', $deathResultName, '', $aParams['data']['CHANGED-death_result_injury'], ['Y' => _fdtk('yes'), 'N' => _fdtk('no'), 'U' => _fdtk('unknown')], $table);
        $deathResult1->setAltFieldName('death_result_injury' . $SuffixString);
        $deathResult1->setOnChangeExtra('jQuery(\'#hidden_death_result_injury' . $SuffixString . '\').val(jQuery(\'#death_result_injury' . $SuffixString . '\').val())');
        $deathResultField = $deathResult1->getField();

        // In print mode, the form is static, so we can ignore currently hidden sections and fields.
        if ($aParams['formtype'] != 'Print') {
            $deathResultField .= '
            <input type="hidden" name="show_field_death_result_injury' . $SuffixString . '" id="show_field_death_result_injury' . $SuffixString . '" value="1" />';
        }
    } else {
        if ($aParams['readonly']['injury']) {
            $deathResultField = ' - ';
        }
        $deathResultField .= code_descr('CON', 'death_result_injury', $aParams['data']['death_result_injury']);
    }

    $deathResultField .= '<input type="hidden" id="' . 'hidden_death_result_injury' . $SuffixString . '" name="death_result_injury' . $ShortSuffixString . '[]" value="' . $aParams['data']['death_result_injury'] . '" />';

    if ($MandatoryFields['dum_death_result' . $ShortSuffixString]) {
        $deathResultNameWithSection = $deathResultName . ' (' . $contactSectionLabel . ')';

        $deathResultField = $deathResultField . '<div class="field_error" id="errdeath_result_injury' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
        $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("death_result_injury' . $SuffixString . '","' . $sectionPrefix . $ShortSuffixString . '","' . $deathResultNameWithSection . '"))};';
    }

    if (!$aParams['hidden']['death_result']) {
        $InjuryCells[] = $deathResultField;
    }

    if (!$aParams['readonly']['impaired_percent']) {
        $name = ($aParams['formtype'] == 'Search') ? 'permanent_impairment_percentage' : 'permanent_impairment_percentage' . $SuffixString;
        $data = ($aParams['formtype'] == 'Search') ? $aParams['data'][$table . '|permanent_impairment_percentage'] : $aParams['data']['permanent_impairment_percentage'];

        $onBlurCustom = "jQuery('#hidden_permanent_impairment_percentage{$SuffixString}').val(jQuery('#permanent_impairment_percentage{$SuffixString}').val())";

        $impairedPrecent1 = NumberFieldFactory::create($aParams['formtype'], $name, $data, 3, 3, '100', '0', false, $onBlurCustom, false, $table);
        $impairedPrecentField = $impairedPrecent1->getField();

        // In print mode, the form is static, so we can ignore currently hidden sections and fields.
        if ($aParams['formtype'] != 'Print') {
            $impairedPrecentField .= '
            <input type="hidden" name="show_field_permanent_impairment_percentage' . $SuffixString . '" id="show_field_permanent_impairment_percentage' . $SuffixString . '" value="1" />';
        }
    } else {
        if ($aParams['readonly']['injury']) {
            $impairedPrecentField = ' - ';
        }
        $impairedPrecentField .= code_descr('INC', 'permanent_impairment_percentage1', $aParams['data']['permanent_impairment_percentage']);
    }

    $impairedPrecentField .= '<input type="hidden" id="' . 'hidden_permanent_impairment_percentage' . $SuffixString . '" name="permanent_impairment_percentage' . $ShortSuffixString . '[]" value="' . $aParams['data']['permanent_impairment_percentage'] . '" />';

    if ($MandatoryFields['dum_impaired_percent' . $ShortSuffixString]) {
        $impairedPercentNameWithSection = $impairedPercentName . ' (' . $contactSectionLabel . ')';

        $impairedPrecentField = $impairedPrecentField . '<div class="field_error" id="errpermanent_impairment_percentage' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
        $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("permanent_impairment_percentage' . $SuffixString . '","' . $sectionPrefix . $ShortSuffixString . '","' . $impairedPercentNameWithSection . '"))};';
    }

    if (!$aParams['hidden']['impaired_percent']) {
        $InjuryCells[] = $impairedPrecentField;
    }

    if ($row_suffix != 1 && !($aParams['readonly']['injury'] && $aParams['readonly']['bodypart'])) {
        $InjuryCells[] = '<input type="button" name="btnAdd" value="Delete" onclick="deleteInjuryRow(' . $row_suffix . ', ' . ($person_suffix ?: '0') . ')" />';
    } else {
        $InjuryCells[] = '';
    }

    return $InjuryCells;
}

function getInjuryRow($aParams)
{
    $SuffixString = ($aParams['person_suffix'] ? '_' . $aParams['person_suffix'] : '') . '_' . $aParams['row_suffix'];

    return '<tr id="injury_row' . $SuffixString . '" name="injury_row' . $SuffixString . '">' .
        '    <td>' .
            implode('</td><td>', constructInjuryCells($aParams)) .
        '    </td>' .
        '</tr>';
}

/**
 * Renders a new injury row (via ajax) when adding injuries to a contact.
 */
function printInjuryRow()
{
    $formDesignLoader = Container::get(FormDesignInstanceLoader::class);

    $aParams['data']['link_injury'] = Sanitize::SanitizeString($_GET['default_inj']);
    $aParams['data']['link_bodypart'] = Sanitize::SanitizeString($_GET['default_bpt']);
    $aParams['row_suffix'] = Sanitize::SanitizeInt($_GET['row_suffix']);
    $aParams['person_suffix'] = Sanitize::SanitizeInt($_GET['person_suffix']);
    $aParams['linktype'] = Sanitize::SanitizeString($_GET['linktype']);

    header('Content-type: application/x-json');

    // bit of a hack, but we need to know whether we're looking at a dif1 or dif2
    if (mb_substr_count($_SESSION['LASTUSEDFORMDESIGN'], 'DIF2')) {
        $FormLevel = 2;
    } else {
        $FormLevel = 1;
    }

    $SuffixString = ($aParams['person_suffix'] ? '_' . $aParams['person_suffix'] : '');

    /** @var Forms_FormDesign $formDesign */
    $formDesign = Forms_FormDesign::GetFormDesign([
        'module' => 'CON',
        'level' => $FormLevel,
        'form_type' => '',
        'link_type' => $aParams['linktype'],
        'parent_module' => 'INC',
    ]);

    if ($aParams['person_suffix'] != '') {
        $formDesign->AddSuffixToFormDesign($aParams['person_suffix']);
    }

    $formDesignLoader->load($formDesign);

    foreach (['dum_injury', 'dum_bodypart', 'dum_death_result', 'dum_impaired_percent'] as $injuryField) {
        $fieldSuffixedName = $injuryField . $SuffixString;
        $fieldShortName = preg_replace('/^dum_/', '', $injuryField);

        if (is_array($GLOBALS['HideFields']) && array_key_exists($fieldSuffixedName, $GLOBALS['HideFields'])) {
            $aParams['hidden'][$fieldShortName] = true;
        }

        if (is_array($GLOBALS['ReadOnlyFields']) && array_key_exists($fieldSuffixedName, $GLOBALS['ReadOnlyFields'])) {
            $aParams['readonly'][$fieldShortName] = true;
        }

        if (is_array($GLOBALS['MandatoryFields']) && array_key_exists($fieldSuffixedName, $GLOBALS['MandatoryFields'])) {
            $aParams['mandatoryFields'][$injuryField] = true;
        }
    }

    $JSONdata['cells'] = constructInjuryCells($aParams);
    $JSONdata['row_suffix'] = $aParams['row_suffix'];
    $JSONdata['person_suffix'] = $aParams['person_suffix'];
    $JSONdata['mandatoryFields'] = $aParams['mandatoryFields'] ?? [];
    $JSONdata['js'] = getJSFunctions();

    return $JSONdata;
}

/**
 * @desc Gets the injuries linked to a particlar contact and incident.
 *
 * @param array $aParams Array of parameters
 *
 * @return array Record data array with injury data included
 */
function GetLinkedInjuries($aParams)
{
    $sql = 'SELECT inc_injury, inc_bodypart, death_result_injury, permanent_impairment_percentage, recordid, listorder,
        con_id, inc_id
        FROM inc_injuries
        WHERE con_id = :con_id
        AND inc_id = :inc_id
        ORDER BY listorder ASC';

    $injuries = DatixDBQuery::PDO_fetch_all($sql, ['con_id' => $aParams['data']['con_id'], 'inc_id' => $aParams['data']['inc_id']]);

    foreach ($injuries as $injury) {
        $aParams['data']['injury_table'][$injury['listorder']] = $injury;
    }

    if (!empty($aParams['data']['injury_table'])) {
        $aParams['data']['show_injury'] = 'Y';
    }

    return $aParams['data'];
}
