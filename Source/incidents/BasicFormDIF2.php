<?php

use app\models\framework\config\DatixConfig;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormSectionHelperFactory;
use Source\generic_modules\EQU\EquipmentFormSectionHelperFactory;
use Source\generic_modules\FieldDefKeys;
use Source\generic_modules\MED\MedicationFormSectionHelperFactory;
use Source\incidents\SharedSections;
use src\component\form\FormProperties;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\incidents\controllers\TimeChainController;
use src\incidents\model\IncidentsFields;
use src\incidents\model\IncidentsSections;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\incidents\model\PSIMSIncidentMedicationFields;
use src\medications\models\MedicationPrefixes;
use src\psims\form\PsimsFieldPropertyManager;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$FormType = $basicFormHelper->getValidFormMode($FormType ?? null);

$sectionHelper = (new BasicFormSectionHelperFactory())->create($FormType);
$medicationsSectionHelper = (new MedicationFormSectionHelperFactory())->create($FormType, 2, Module::INCIDENTS);
$equipmentSectionHelper = (new EquipmentFormSectionHelperFactory())->create($FormType, 2);

$moduleRepository ??= Container::get(ModuleRepository::class);
$investigationsIsLicensed = $moduleRepository->getModuleByCode('INV')->isEnabled();

try {
    $ribIsLicensed = $moduleRepository->getModuleByCode('RIB')->isEnabled();
} catch (\Exception $e) {
    $ribIsLicensed = false;
}

$registry ??= Container::get(Registry::class);
$config ??= Container::get(DatixConfig::class);

// variables for globals table values
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showOshaFields = $registry->getParm('SHOW_OSHA_FIELDS', 'N')->isTrue();
$showEdiFields = $registry->getParm('SHOW_EDI_FIELDS', 'N')->isTrue();
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showTimeToSubmit = $registry->getParm('TIME_TO_SUBMIT', 'N')->isTrue();
$showSacFields = $registry->getParm('SAC_SCORING', 'N')->isTrue();
$showNationalDataset = $registry->getParm('ENABLE_NATIONAL_DATASET', 'N')->isTrue();
$showPressureUlcerDataset = $registry->getParm('ENABLE_PRESSURE_ULCER_DATASET', 'N')->isTrue();
$showSkinLesionDataset = $registry->getParm('ENABLE_SKIN_LESION_DATASET', 'N')->isTrue();
$showSharpsDataset = $registry->getParm('ENABLE_SHARPS_DATASET', 'N')->isTrue();
$showAdverseReactionDataset = $registry->getParm('ENABLE_ADVERSE_REACTION_DATASET', 'N')->isTrue();
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();
$showPsimsFields = $config->getPsimsEnabled();
$showPsimsPsirfFields = $showPsimsFields && $registry->getParm('LFPSE_PSIRF', 'N')->isTrue();
$showLastChildFirst = $config->showLastChildFirst();
$isVanessaLawEnabled = $config->getVanessaLawEnabled();
$isLoggedIn = (new \src\framework\session\UserSessionFactory())->create()->isLoggedIn();
$submittedViaMobile = isset($inc['inc_mob_severity']) || isset($inc['inc_mob_location']) || !empty($inc['mobile_contacts']);
$incTimeChainEnabled = $registry->getParm('INC_TIME_CHAIN_ENABLED', 'N')->isTrue();

if (in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true)) {
    $gradereadonly = true;
}

if ($FormType === FormTable::MODE_SEARCH) {
    $gradereadonly = false;
    $Searchmode = true;
}

$fieldLabels = $registry->getFieldLabels();
$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;

$medicationsSearchTitle = _fdtk('mod_medications_title', $useFormDesignLanguage);
$inc ??= [];
$learningsToShare = isset($inc['learnings_to_share']) && $inc['learnings_to_share'] === 'Y';

// sections and fields available in design mode
$FormArray = [
    'Parameters' => ['Panels' => 'True', 'Condition' => false, 'LinkType' => ($FormType == 'linkedDataSearch') ? 'INC' : ''],
    'name' => [
        'Title' => _fdtk('name_ref_section', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'recordid',
                'Condition' => (!empty($inc['recordid']) || $FormType == 'Design' || $FormType == 'Search' || $FormType == 'linkedDataSearch'),
            ],
            [
                'Name' => IncidentsFields::SOURCE_OF_RECORD,
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'ReadOnly' => true,
            ],
            'inc_name',
            [
                'Name' => 'inc_ourref',
                'ReadOnly' => ($_GET['action'] !== 'addnewincident' && $registry->getParm('ENABLE_INC_REF_EDIT', 'N')->isFalse()),
            ],
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => 'INC',
                'perms' => $DIFPerms ?? null,
                'currentapproveobj' => $CurrentApproveObj ?? null,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $inc,
                'module' => 'INC',
                'perms' => $DIFPerms ?? null,
                'approveobj' => $ApproveObj ?? null,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            [
                'Name' => 'approved_by',
                'Condition' => in_array($FormType, ['Search', 'Design']) || ($inc['rep_approved'] ?? null) === 'FA',
            ],
            'inc_dreported',
            'inc_dopened',
            'inc_submittedtime',
            [
                'Name' => 'inc_mgr',
                'ReadOnly' => !(GetParm('DIF_PERMS') == 'RM' || (GetParm('DIF_PERMS') == 'DIF2' && (GetParm('REASSIGN_HANDLER', 'N') == 'Y' && $_SESSION['initials'] == ($inc['inc_mgr'] ?? '')) || GetParm('REASSIGN_HANDLER', 'N') == 'A') || $FormType == 'Search'),
            ],
            'inc_head',
            'inc_consultants',
            ['Name' => 'time_taken_to_submit', 'Condition' => ($showSpscFields || $showTimeToSubmit) && $FormType !== 'New'],
            [
                FormProperties::FIELD_NAME => PSIMSIncidentFields::INC_PSIMS_RECORD_LV1,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::NO_ADVANCED => false,
                FormProperties::NO_FIELD_LABEL => true,
                FormProperties::NO_HELP_TEXT => true,
                FieldDefKeys::NO_DEFAULT => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
                FormProperties::NO_MANDATORY => true,
            ],
            [
                FormProperties::FIELD_NAME => PSIMSIncidentFields::INC_PSIMS_RECORD_LV2,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::NO_ADVANCED => false,
                FormProperties::NO_FIELD_LABEL => true,
                FormProperties::NO_HELP_TEXT => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
            ],
        ],
    ],
    IncidentsSections::OUTBREAK => [
        'Title' => _fdtk('inc_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => IncidentsFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => IncidentsFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],
    IncidentsSections::PSIMS => [
        'Title' => _fdtk('inc_psims_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_DETECTION_POINT_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_WENT_WELL, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RESPONSIBLE_SPECIALTY_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PATIENT_EVENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTIMATED_TIME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_TIMEFRAME_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_POPULATION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ORGANISATION, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_RISK => [
        'Title' => _fdtk('inc_psims_risk_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_THEME, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_THEME_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_SERVICE_AREA, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_RISK_DESCRIPTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_RISK_IDENTIFIED_LOCATION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_LOCATION_AT_RISK, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_WENT_WELL => [
        'Title' => _fdtk('inc_psims_went_well_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_CARE_DETECTION_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_GOOD_CARE_DETECTION_FACTOR_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_HOW_FUTURE_OCCURRENCE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_MEDICATIONS => [
        'Title' => _fdtk('inc_psims_medications_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVEMENT_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_USED_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INSUFFICIENT_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_GIVEN_INCORRECTLY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::PROBLEM_DESCRIPTION_DRUGS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::PROBLEM_MEDS_PACKAGING, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_REACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_REACTION_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_EQUIPMENT => [
        'Title' => _fdtk('inc_psims_equipment_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USAGE_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::PSIMS_DEVICE_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_TYPE_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_BROKEN_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_NOT_ENOUGH_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USED_UNNECESSARILY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_USAGE_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentEquipmentFields::DEVICE_PROBLEM_DESCRIPTION, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_IT_SYSTEMS_SOFTWARE => [
        'Title' => _fdtk('inc_psims_it_systems_software_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_IT_INVOLVEMENT_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_SYSTEMS_SOFTWARE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_TISSUES_ORGANS => [
        'Title' => _fdtk('inc_psims_tissues_organs_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_NHSBT_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_INVOLVEMENT_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DEFICIENT_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_WRONG_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_USED_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TISSUE_DAMAGED_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_TISSUES_ORGANS, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_INVOLVED_PERSONS => [
        'Title' => _fdtk('inc_psims_involved_persons_title', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_INVOLVED_PERSONS_ACTIONS_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_TOO_MUCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_WRONG_ACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_OMITTED_ACTION, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_ACTIONS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_UNAVAILABLE_DETAIL, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_INVOLVEMENT, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_REPORTER => [
        'Title' => _fdtk('inc_psims_reporter', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT_OTHER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_ROLE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_REPORTER_ROLE_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_AGENT => [
        'Title' => _fdtk('inc_psims_adverse_event_agent', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_SABRE_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_SHOT_REPORT_NUMBER, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_YELLOW_CARD_REFERENCE, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BUILDINGS_INFRASTRUCTURE => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_buildings_infrastructure', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BUILDINGS_INFRASTRUCTURE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_PROBLEM, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_ESTATES_SERVICES => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_estates_services', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_ESTATES_SERVICES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_PROBLEM, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_OTHER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_blood', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_WAS_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PROBLEM, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_PROBLEM_BLOOD_PRODUCTS => [
        'Title' => _fdtk('inc_psims_adverse_event_problem_blood_products', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_DETAILS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BRAND, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BATCH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_INVOLVED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_NOT_USED, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD_PRODUCTS, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_PROBLEM, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES => [
        'Title' => _fdtk('inc_psims_adverse_event_safety_challenges', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_ADVERSE_EVENT_GOVERNANCE => [
        'Title' => _fdtk('inc_psims_adverse_event_governance', $useFormDesignLanguage),
        'Condition' => $showPsimsFields,
        'Rows' => [
            ['Name' => PSIMSIncidentFields::PSIMS_DESIGNATIONS_MENTAL_HEALTH, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_DEPRIVATION_OF_LIBERTY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_CQC_NOTIFY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_CQC_CRITERIA, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_LOCAL_AUTHORITY_SAFEGUARDING, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_NEVER_EVENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_SERIOUS_INCIDENT, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::PSIMS_HSIB_NOTIFY, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_NEVER_EVENT_TYPE, 'Condition' => $showPsimsFields],
            ['Name' => PSIMSIncidentFields::DUTY_OF_CANDOUR, 'Condition' => $showPsimsFields],
        ],
    ],
    IncidentsSections::PSIMS_PSIRF => [
        'Title' => _fdtk('inc_psims_psirf_section'),
        'Condition' => $showPsimsPsirfFields,
        'Rows' => [
            ['Name' => PSIMSIncidentCodedFields::PSIMS_INCIDENT_FRAMEWORK, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT_ADDRESSED, 'Condition' => $showPsimsPsirfFields],
            ['Name' => PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPONDED, 'Condition' => $showPsimsPsirfFields],
        ],

    ],
    'mobile' => [
        'Title' => 'Datix Anywhere',
        'Condition' => $isLoggedIn && ($submittedViaMobile || (!empty($FormMode) && $FormMode == 'Design')),
        'Rows' => [
            [
                'Name' => 'inc_mob_severity',
                'Type' => 'custom',
                'HTML' => $inc['mobile_severity_html'] ?? '',
                'NoOrder' => true,
            ],
            [
                'Name' => 'inc_mob_category',
                'Type' => 'custom',
                'HTML' => $inc['mobile_category_html'] ?? '',
                'NoOrder' => true,
                'Condition' => $showSpscFields,
            ],
            [
                'Name' => 'inc_mob_location',
                'Type' => 'custom',
                'HTML' => $inc['mobile_locations_html'] ?? '',
                'NoOrder' => true,
            ],
            [
                'Name' => 'mobile_contacts',
                'NoOrder' => true,
            ],
            [
                'Name' => 'inc_mob_anonymous',
                'Condition' => $showSpscFields,
            ],
        ],
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
    ],
    'additional' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_person',
            'show_witness',
            'show_employee',
            'show_other_contacts',
            'show_equipment',
            'show_medication',
            'show_pars',
            'show_document',
            [
                'Name' => 'anon_reporting',
                'ReadOnly' => isset($inc['rep_approved']) && !in_array($inc['rep_approved'], ['NEW', 'STCL']),
            ],
        ],
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            'exact_location',
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'osha' => [
        'Title' => 'OSHA',
        'Condition' => $showOshaFields,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'osha_recordable',
        ],
    ],
    'tcs' => [
        'Title' => _fdtk('incident_coding', $useFormDesignLanguage),
        'Condition' => ($_SESSION['Globals']['DIF_2_CCS'] == 'TCS_CCS' || $_SESSION['Globals']['DIF_2_CCS'] == 'TCS' || $_SESSION['Globals']['DIF_2_CCS'] == ''),
        'Rows' => [
            'inc_type',
            'inc_category',
            'inc_subcategory',
            'inc_cnstitype',
            [
                'Name' => IncidentsFields::CLASSIFICATION_TREE,
                'Condition' => empty($Searchmode) && $registry->getParm('CLASSIFICATION_TREE', 'N')->isTrue(),
            ],
        ],
    ],
    'ccs' => [
        'Title' => _fdtk('datix_ccs', $useFormDesignLanguage),
        'Condition' => ($_SESSION['Globals']['DIF_2_CCS'] == 'TCS_CCS' || $_SESSION['Globals']['DIF_2_CCS'] == 'CCS'),
        'Rows' => [
            [
                'Name' => 'inc_type',
                'Condition' => $_SESSION['Globals']['DIF_2_CCS'] == 'CCS',
            ],
            'inc_carestage',
            'inc_clin_detail',
            'inc_clintype',
        ],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_INC', 'N')),
        'Rows' => [
            'inc_affecting_tier_zero',
            'inc_type_tier_one',
            'inc_type_tier_two',
            'inc_type_tier_three',
        ],
    ],
    'severity' => [
        'Title' => (
            _fdtk('incident_severity', $useFormDesignLanguage)
            . (
                !empty($HideFields['inc_result'])
                    ? ''
                    : ' ' . _fdtk('report_title_and', $useFormDesignLanguage)
                    . ' '
                    . $fieldLabels->getLabel('incidents_main', 'inc_result', 'INCIDENTS', null, '', $useFormDesignLanguage)
            )
        ),
        'Rows' => [
            'inc_result',
            'inc_severity',
            [
                'Name' => 'sac_score',
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => 'inc_result_initial',
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => 'inc_severity_initial',
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => 'sac_score_initial',
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => IncidentsFields::SAC_DATE_DIFF_DAYS,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => IncidentsFields::SAC_DATE_DIFF_HOURS,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => IncidentsFields::SAC_SCORE_DATE,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => IncidentsFields::SAC_SCORE_TIME,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showSacFields,
            ],
            [
                'Name' => IncidentsFields::MAX_PHYSICAL_HARM_PERSON,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showPsimsFields,
            ],
            [
                'Name' => IncidentsFields::MAX_PSYCHOLOGICAL_HARM_PERSON,
                'ReadOnly' => true,
                'NoMandatory' => true,
                'NoReadOnly' => true,
                'NoDefault' => true,
                'Condition' => $showPsimsFields,
            ],
        ],
    ],
    'details' => [
        'Title' => _fdtk('details', $useFormDesignLanguage),
        'Rows' => [
            ['Name' => 'inc_last_updated', 'ReadOnly' => true],
            'inc_dincident',
            'inc_time',
            'inc_time_band',
            'inc_notes',
            'inc_actiontaken',
            'inc_dnpsa',
            [
                'Name' => 'inc_cnstitype',
                'Condition' => !($_SESSION['Globals']['DIF_2_CCS'] == 'TCS_CCS' || $_SESSION['Globals']['DIF_2_CCS'] == 'TCS' || $_SESSION['Globals']['DIF_2_CCS'] == ''),
            ],
            'inc_rc_required',
            'inc_report_npsa',
            'inc_is_riddor',
            'inc_dsched',
            'inc_notify',
            ['Name' => 'inc_injury', 'Type' => 'ff_select', 'Title' => 'Loss/damage', 'Module' => 'INC'],
            ['Name' => 'inc_bodypart', 'Type' => 'ff_select', 'Title' => 'Item', 'Module' => 'INC'],
            ['Name' => 'inc_level_intervention', 'Condition' => bYN(GetParm('CCS2_INC', 'N'))],
            ['Name' => 'inc_level_harm', 'Condition' => bYN(GetParm('CCS2_INC', 'N'))],
            'inc_never_event',
            [
                'Name' => 'flag_for_investigation',
                'ReadOnly' => isset($inc['flag_for_investigation']) && $inc['flag_for_investigation'] === 'Y',
                'Condition' => $investigationsIsLicensed,
            ],
            [
                'Name' => 'flag_for_rib',
                'ReadOnly' => isset($inc['flag_for_rib']) && $inc['flag_for_rib'] === 'Y',
                'Condition' => $ribIsLicensed,
            ],
            // FIX GLOBAL DECLARATIONS WHEN IQ-4040 MERGED IN
            ['Name' => 'incident_occurred_on_employer_premises', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_provided', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_used', 'Condition' => $showUsClaimsFields],
            ['Name' => 'edi_cause_code', 'Condition' => $showEdiFields],
            'health_service_site',
            ['Name' => 'hro_characteristics', 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::INC_SPECIALTY, 'Condition' => $showSpscFields],
        ],
    ],
    'worker_details' => [
        'Title' => _fdtk('worker_incident_details', $useFormDesignLanguage),
        'Rows' => [
            'anzco_coding',
            'breakdown_agency',
            'agency_of_injury',
            'mech_of_injury_1',
            'mech_of_injury_2',
            'nature_of_injury_1',
            'nature_of_injury_2',
            'toocs_1',
            'toocs_2',
        ],
    ],
    'riddor' => [
        'Title' => 'RIDDOR',
        'Rows' => [
            'inc_dnotified',
            'inc_riddor_ref',
            'inc_ridloc',
            'inc_address',
            'inc_localauth',
            'inc_acctype',
            'inc_riddorno',
        ],
    ],

    'learning' => [
        'Title' => _fdtk('learnings', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            [
                'Name' => 'learnings_to_share',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $learningsToShare,

            ],
            [
                'Name' => 'learnings_title',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $learningsToShare,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'key_learnings',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $learningsToShare,
                'NoMandatory' => true,
            ],
        ],
    ],
    'reportedby' => [
        'Title' => _fdtk('reporter', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => empty($inc['anon_reporting'])
            && (CanSeeContacts('INC', $DIFPerms ?? null, $inc['rep_approved'] ?? null) || !bYN(GetParm('DIF2_HIDE_CONTACTS', 'N'))),
        'Rows' => [
            'inc_rep_id',
            'inc_repname',
            'inc_reportedby',
            'inc_rep_email',
            'inc_rep_tel',
        ],
    ],

    'iq_medications' => $medicationsSectionHelper->createMedicationFormSection(),
    'iq_administered_drug_design' => $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::ADMINISTERED_FIELD_PREFIX),
    'iq_correct_drug_design' => $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::CORRECT_FIELD_PREFIX),
    'iq_medications_other_design' => $medicationsSectionHelper->createOtherFieldsFormDesignSection(),
    'medication_search' => $medicationsSectionHelper->createMedicationSearchFormSection(),

    'iq_equipment' => $equipmentSectionHelper->createEquipmentFormSection(),
    'equipment_search' => $equipmentSectionHelper->createEquipmentSearchFormSection(),

    'investigation' => [
        'Title' => _fdtk('review', $useFormDesignLanguage),
        'Rows' => [
            'inc_investigator',
            'inc_inv_dstart',
            'inc_inv_dcomp',
            'inc_cost',
            'dum_inc_grading_initial',
            'dum_inc_grading',
            'inc_inv_outcome',
            'inc_lessons_code',
            'inc_inv_lessons',
            ['Name' => 'inc_inv_lessons_sub_category', 'Condition' => $showSpscFields],
            'inc_inquiry',
            'inc_action_code',
            'inc_inv_action',
            'rep_feedback_codes',
            'rep_feedback_notes',
        ],
    ],
    'feedback' => [
        'Title' => _fdtk('feedback_title', $useFormDesignLanguage),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],
    'linked_records' => [
        'Title' => _fdtk('linked_records', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'Special' => 'LinkedRecords',
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'causes' => [
        'Title' => _fdtk('causes', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'SectionRootCauseDetails' => [
                'controller' => src\rootcauses\controllers\RootCausesController::class,
            ],
        ],
        'Rows' => [],
    ],
    'notepad' => [
        'Title' => _fdtk('notepad', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Rows' => ['notes'],
        'Condition' => $FormType !== 'linkedDataSearch',
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'action_chains' => [
        'Title' => _fdtk('action_chains', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'getActionChains' => [
                'controller' => src\actionchains\controllers\ActionChainController::class,
            ],
        ],
        'LinkedForms' => ['action_chains' => ['module' => 'ACT']],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'pars' => [
        'Title' => _fdtk('sirs_reporting_information', $useFormDesignLanguage),
        'Rows' => [
            'inc_pars_clinical',
            'inc_user_action',
            'inc_agg_issues',
            'inc_pars_pri_type',
            'inc_pars_sec_type',
            'inc_pol_called',
            'inc_pol_call_time',
            'inc_pol_attend',
            'inc_pol_att_time',
            'inc_pol_action',
            'inc_pol_crime_no',
            'inc_pars_address',
            'inc_postcode',
            'show_assailant',
            'inc_tprop_damaged',
            'inc_pars_first_dexport',
            'inc_pars_dexport',
        ],
    ],
    'pas' => [
        'Title' => 'PAS',
        'Condition' => bYN(GetParm('SHOW_OLD_PAS', 'N')),
        'Rows' => [
            'inc_pasno1',
            'inc_pasno2',
            'inc_pasno3',
        ],
    ],
    'causal_factor_header' => [
        'Title' => _fdtk('causal_factors_for', $useFormDesignLanguage) . ' ' . _fdtk('INCNameTitle', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            'inc_causal_factors_linked',
        ],
    ],
    'causal_factor' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design' && $FormType !== 'linkedDataSearch'),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'DoCausalFactorsSection' => [
                'controller' => src\causalfactors\controllers\CausalFactorsController::class,
            ],
        ],
        'ExtraParameters' => ['causal_factor_name' => 'inc_causal_factor'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'causal_factor_design' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && $FormType !== 'linkedDataSearch'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'causal_factor',
        'NoSectionActions' => true,
        'Rows' => [
            'caf_level_1',
            'caf_level_2',
        ],
    ],
    'openness_transparency' => [
        'Title' => _fdtk('openness_transparency_title', $useFormDesignLanguage),
        'Rows' => [
            'inc_ot_q1',
            'inc_ot_q2',
            'inc_ot_q3',
            'inc_ot_q4',
            'inc_ot_q5',
            'inc_ot_q6',
            'inc_ot_q7',
            'inc_ot_q8',
            'inc_ot_q9',
            'inc_ot_q10',
            'inc_ot_q11',
            'inc_ot_q12',
            'inc_ot_q13',
            'inc_ot_q14',
            'inc_ot_q15',
            'inc_ot_q16',
            'inc_ot_q17',
            'inc_ot_q18',
            'inc_ot_q19',
            'inc_ot_q20',
            'inc_ot_q21',
            'inc_ot_q22',
        ],
    ],
    'falls' => [
        'Title' => _fdtk('falls', $useFormDesignLanguage),
        'Rows' => [
            'fall_unassisted_assisted',
            'fall_observed_by',
            'fall_physical_injury',
            'fall_patient_doing',
            'fall_patient_doing_other',
            'fall_risk_assessment',
            'fall_protocols_in_place',
            'fall_score',
            'fall_medication_risk',
            'fall_fell_from',
            'fall_post_fall_action',
            'fall_risk_assessment_score',
        ],
    ],
    'fire' => [
        'Title' => _fdtk('fire', $useFormDesignLanguage),
        'Rows' => [
            'fire_detected',
            'fire_cause_known',
            'fire_action_taken',
            'fire_department_response',
            'fire_department_time_taken',
        ],
    ],
    'blood_transfusion' => [
        'Title' => _fdtk('blood_transfusion', $useFormDesignLanguage),
        'Rows' => [
            'blood_date_transfusion_started',
            'blood_time_transfusion_started',
            'blood_time_transfusion_stopped',
            'blood_date_reaction_started',
            'blood_time_reaction_started',
            'blood_amount_transfused',
        ],
    ],
    'spsc_national_data' => [
        'Title' => _fdtk('spsc_national_data', $useFormDesignLanguage),
        'Condition' => ($showSpscFields || $showNationalDataset),
        'Rows' => [
            ['Name' => IncidentsFields::SPSC_INTERNATIONAL_GOALS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_NATIONAL_GOALS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_SURGERY_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_MORBIDITY_ANTICIPATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_REASON_FOR_LAMA, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_RECOVERY_DATE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VISIT_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_HAZARDOUS_SUBSTANCE_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_INJURY_LEVEL, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_CODE_WHITE_PERSON_INJURY, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_CODE_WHITE_GENDER, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_OCCURRED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_LOCATION_DVT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CONFIRMED_DVT, 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::SPSC_WAS_VTE_RISK_ASSESSMENT_DOCUMENTED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PATIENTS_DOCUMENTED_RISK_OF_VTE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_DOCUMENTED_RISK_OF_BLEEDING, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PHARMACOLOGICAL_MECHANICAL_PROPHYLAXIS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_PARMACOLOGICAL_ANTICOAGULANT_ADMINISTERED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHY_ANTICOAGULANT_NOT_GIVE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_MECHANICAL_PROPHYLAXIS_APPLIED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CORFIRMED_PE, 'Condition' => $showSpscFields],
            ['Name' => IncidentsFields::SPSC_VTE_OUTCOME, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_SUB_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_VTE_FEMALE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_BLOOD_TYPE_PRODUCT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_RED_CODE_ACTIVATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_FIRE_ALARM_ACTIVATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_BUILDING_EVACUATED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_PORTABLE_FIRE_EXTINGUISHERS_USED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WAS_LOCAL_FIRE_DEPARTMENT_INFORMED, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_WERE_ANY_DAMAGES_TO_PROPERTY, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_MORBIDITY_RECORD_IS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_IS_TRIGGER_RELATED_TO_IMPROPER_ASSESSMENT_OF_PATIENT, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SPSC_ADDITIONAL_MORBIDITY_TRIGGERS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::SEND_TO_SFDA, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::DIAGNOSTIC, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::PROCEDURES, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::ALLERGY_REACTION, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::ALLERGY_SEVERITY, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::ALLERGEN_TYPE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::ALLERGY_CLINICAL_STATUS, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::ONSET_DATE, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::DATE_OF_ADMISSION, 'Condition' => $showSpscFields || $showNationalDataset],
            ['Name' => IncidentsFields::RECOVERY_DATE, 'Condition' => $showSpscFields || $showNationalDataset],
        ],
    ],
    'pressure_ulcers' => [
        'Title' => _fdtk('pressure_sores', $useFormDesignLanguage),
        'NotModes' => ['Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showPressureUlcerDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'pressure_ulcers'],
        'Rows' => [],
    ],
    'pressure_ulcers_design' => [
        'Title' => _fdtk('pressure_sores', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showPressureUlcerDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'pressure_ulcers',
        'NoSectionActions' => true,
        'Rows' => [
            'hospital',
            'location',
            'nature',
            'advanced_stage',
            'admission_status',
            'admission_status_3_4',
            'skin_inspection',
            'risk_assessment',
            'risk_assessment_type',
            'increased_risk',
            'preventative_intervention_yn',
            'preventative_intervention',
            'preventative_intervention_detail',
            'device',
            'device_type',
            'device_detail',
            'secondary_morbidity',
            'risk_assessment_score',
        ],
    ],
    'skin_lesions' => [
        'Title' => _fdtk('skin_lesions', $useFormDesignLanguage),
        'NotModes' => ['Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showSkinLesionDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'skin_lesions'],
        'Rows' => [],
    ],
    'skin_lesions_design' => [
        'Title' => _fdtk('skin_lesions', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showSkinLesionDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'skin_lesions',
        'NoSectionActions' => true,
        'Rows' => [
            'site',
            'development_level',
        ],
    ],
    'sharps' => [
        'Title' => _fdtk('sharps', $useFormDesignLanguage),
        'NotModes' => ['Design'],
        'LinkedDataSection' => true,
        'Condition' => ($FormType !== 'linkedDataSearch' && ($showSpscFields || $showSharpsDataset)),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'sharps'],
        'Rows' => [],
    ],
    'sharps_design' => [
        'Title' => _fdtk('sharps', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design' && ($showSpscFields || $showSharpsDataset)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'sharps',
        'NoSectionActions' => true,
        'Rows' => [
            'nature_of_injury',
            'type_of_tool',
            'contamination',
            'contamination_type',
            'contamination_other',
        ],
    ],
    'adverse_drug_reactions' => [
        'Title' => _fdtk('adverse_drug_reactions', $useFormDesignLanguage),
        'Condition' => $showSpscFields || $showAdverseReactionDataset,
        'Rows' => [
            IncidentsFields::INC_NARANJO_TOTAL_SCORE,
            IncidentsFields::INC_ADR_PROBABILITY_SCALE,
            IncidentsFields::INC_ADR_ACTION_TAKEN,
            IncidentsFields::INC_ADR_ORGAN_SYSTEM_FIELDS,
            IncidentsFields::INC_ADR_TOTAL_SCORE,
        ],
    ],
    'history' => [
        'Title' => _fdtk('notifications', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => src\email\controllers\EmailHistoryController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'word' => [
        'Title' => _fdtk('mod_templates_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            ],
        ],
        'NotModes' => ['New', 'Search', 'Print'],
        'Rows' => [],
    ],
    'rejection' => GenericRejectionArray('INC', $inc, $useFormDesignLanguage),
    'rejection_history' => [
        'Title' => _fdtk('reasons_history_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'SectionRejectionHistory' => [
                'controller' => src\reasons\controllers\ReasonsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Condition' => bYN(GetParm('REJECT_REASON', 'Y')),
        'Rows' => [],
    ],
    'vanessas_law' => [
        'Title' => _fdtk('vanessas_law_compliance', $useFormDesignLanguage),
        'Condition' => $isVanessaLawEnabled,
        'Rows' => [
            ['Name' => IncidentsFields::VLC_DRUG_DOCUMENTED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DRUG_REACTION_EFFECT, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_MEDICAL_CONDITION, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HEALTH_RESTORED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_CONCOMITANT_USED, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_DOCUMENTED_DATE, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_EFFECT_HEALTH, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_DEVICE_CONTRIBUTION, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HOSPITAL_REP_EMAIL_ID, 'Condition' => $isVanessaLawEnabled],
            ['Name' => IncidentsFields::VLC_HEALTH_CANADA_INST_ID, 'Condition' => $isVanessaLawEnabled],
        ],
    ],
    IncidentsSections::TIME_CHAIN => [
        'Title' => _fdtk('inc_time_chain', $useFormDesignLanguage),
        'NoReadOnly' => true,
        'NoFieldAdditions' => true,
        'Condition' => $incTimeChainEnabled,
        'ControllerAction' => [
            'SectionTimeChain' => [
                'controller' => TimeChainController::class,
            ],
        ],
    ],
    IncidentsSections::PSIMS_RESPONSE => [
        FormProperties::TITLE => _fdtk('psims_response', $useFormDesignLanguage),
        FormProperties::NO_READ_ONLY => true,
        FormProperties::NO_MANDATORY => true,
        FormProperties::FIELD_CONDITION => $showPsimsFields,
        FormProperties::NO_HIDE => false,
        FormProperties::ROWS => [
            [
                FormProperties::FIELD_NAME => IncidentsFields::REFERENCE_NUMBER,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::READ_ONLY,
                FormProperties::NO_MANDATORY => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
            ],
            [
                FormProperties::FIELD_NAME => IncidentsFields::SUBMISSION_DATE_TIME,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::READ_ONLY,
                FormProperties::NO_MANDATORY => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
            ],
            [
                FormProperties::FIELD_NAME => IncidentsFields::STATUS,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::READ_ONLY,
                FormProperties::NO_MANDATORY => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
            ],
            [
                FormProperties::FIELD_NAME => IncidentsFields::WARNINGS,
                FormProperties::FIELD_CONDITION => $showPsimsFields,
                FormProperties::READ_ONLY,
                FormProperties::NO_MANDATORY => true,
                FormProperties::NO_HIDE => false,
                FormProperties::NO_READ_ONLY => true,
            ],
        ],
    ],
    IncidentsSections::TIME_FIELDS => SharedSections::getTimeFieldSection($useFormDesignLanguage),
];

$lfpseSectionKeys = IncidentsSections::getLFPSESections();
$FormArray = PsimsFieldPropertyManager::preventPsimsFieldConfiguration(
    $FormArray,
    $lfpseSectionKeys,
    $formlevel ?? $formLevel ?? $level,
);

if (bYN(GetParm('READ_ONLY_REPORTER', 'N'))) {
    $FormArray['reportedby']['ReadOnly'] = ($FormType != 'New');
}

$ContactArray = [];
// Add contact sections for each contact type.
foreach ($ModuleDefs['INC']['CONTACTTYPES'] as $ContactTypeDetails) {
    if ($FormType === 'Search') {
        // Contacts field specifically for search that use a different controller
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_INC_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'LinkedDataSection' => true,
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'generateSearchCriteria' => [
                    'controller' => src\contacts\controllers\SearchCriteriaController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $ContactTypeDetails['Type'], 'linkModule' => 'CON', 'module' => 'INC', 'sectionId' => 'contacts_type_' . $ContactTypeDetails['Type']],
            'NotModes' => ['New', 'Design', 'Print', 'Edit'],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => ($FormType == 'Search' && (CanSeeContacts('INC', $DIFPerms ?? '', $inc['rep_approved'] ?? '') || !bYN(GetParm('DIF2_HIDE_CONTACTS', 'N')))),
            'Rows' => [],
        ];
    } else {
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_INC_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'ListLinkedContacts' => [
                    'controller' => src\contacts\controllers\ContactsController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $ContactTypeDetails['Type']],
            'NotModes' => ['New', 'Search'],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => ($FormType !== 'linkedDataSearch' && (CanSeeContacts('INC', $DIFPerms ?? '', $inc['rep_approved'] ?? '') || !bYN(GetParm('DIF2_HIDE_CONTACTS', 'N')))),
            'Rows' => [],
        ];
    }
}

$ActionsArray = [];

// Decide if we are using Actions for search or display
if ($FormType === 'Search') {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'ACT', 'module' => 'INC', 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'],
        'NotModes' => ['New'],
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT']],
        'Rows' => [],
    ];
} else {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Special' => 'LinkedActions',
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT', 'carltonFormDesigns' => true]],
        'NotModes' => ['New'],
        'Condition' => $FormType !== 'linkedDataSearch',
        'Rows' => [],
    ];
}

$paymentsArray = [];

// Decide if we are using Payments for search or display
if ($FormType === 'Search') {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'PAY', 'module' => 'INC', 'sectionId' => 'payments', 'link_type' => 'payments'],
        'NotModes' => ['New'],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'Listings' => ['payments' => ['module' => 'PAY']],
    ];
} else {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => ['New'],
        'Condition' => $FormType !== 'linkedDataSearch',
        'Listings' => ['payments' => ['module' => 'PAY']],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'ControllerAction' => [
            'listPayments' => [
                'controller' => src\payments\controllers\PaymentController::class,
            ],
        ],
    ];
}

array_insert_datix($FormArray, 'causes', $ContactArray);
array_insert_datix($FormArray, 'action_chains', $ActionsArray);
array_insert_datix($FormArray, 'causes', $paymentsArray);

$FormArray = $sectionHelper->addLinkedModuleListings($FormArray);

return $FormArray;
