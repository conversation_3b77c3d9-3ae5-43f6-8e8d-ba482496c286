<?php

use app\models\generic\valueObjects\Module;
use app\services\idGenerator\RecordIdGeneratorFactory;
use src\component\field\CustomFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTableFactory;
use src\formdesign\forms\service\Loaders\FormDesignInstanceLoader;
use src\framework\session\UserSessionFactory;
use src\helpers\SqlInClauseHelper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;

/**
 * @desc This function is called when a form containing the personal property section is loaded. It
 * contstructs the table and fills it with any data present.
 *
 * @param array $data data array for current record
 * @param string $FormType Type of form (e.g. Print, ReadOnly, Search...)
 * @param string $module Current module
 * @param int $suffix Suffix of contact this section will be attached to
 * @param string $section Not currently used, but may be needed for form design options for this section
 */
function SectionPropertyDetails($data, $FormType, $module, $suffix = 0, $section = '', $LinkType = '')
{
    global $ReadOnlyFields, $HideFields, $UserLabels, $MandatoryFields;

    $language = LanguageSessionFactory::getInstance()->getLanguage();

    $RowSuffix = 1;
    $SuffixString = (is_int((int) $suffix) && $suffix > 0 ? '_' . $RowSuffix : '');


    if (!empty($section) && is_array($ReadOnlyFields) && array_key_exists($section, $ReadOnlyFields)) {
        $FormType = 'ReadOnly';
    }

    if (is_array($HideFields)) {
        if (array_key_exists('dum_description' . $SuffixString, $HideFields)) {
            $descriptionHidden = true;
        }
        if (array_key_exists('dum_property_type' . $SuffixString, $HideFields)) {
            $typeHidden = true;
        }
        if (array_key_exists('dum_value' . $SuffixString, $HideFields)) {
            $valueHidden = true;
        }
    }
    if (is_array($ReadOnlyFields)) {
        if (array_key_exists('dum_description' . $SuffixString, $ReadOnlyFields)) {
            $descriptionReadOnly = true;
        }
        if (array_key_exists('dum_property_type' . $SuffixString, $ReadOnlyFields)) {
            $typeReadOnly = true;
        }
        if (array_key_exists('dum_value' . $SuffixString, $ReadOnlyFields)) {
            $valueReadOnly = true;
        }
    }

    $Table = FormTableFactory::create($FormType, Module::INCIDENTS);
    $Table->setCurrentSection('personal_property');

    $descriptionKey = 'ipp_description' . $SuffixString;
    $typeKey = 'ipp_damage_type' . $SuffixString;
    $valueKey = 'ipp_value' . $SuffixString;

    $HTML = '
    <div class="new_windowbg2" style="overflow:auto">
';

    if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked' && !($descriptionReadOnly && $typeReadOnly && $valueReadOnly)) {
        $DescTitle = FirstNonNull([$UserLabels[$descriptionKey][$language], Labels_FormLabel::GetFormFieldLabel('ipp_description')]);
        $TypeTitle = FirstNonNull([$UserLabels[$typeKey][$language], Labels_FormLabel::GetFormFieldLabel('ipp_damage_type')]);
        $ValueTitle = FirstNonNull([$UserLabels[$valueKey][$language], Labels_FormLabel::GetFormFieldLabel('ipp_value')]);

        $HTML .= '
        <table id="property_table' . $SuffixString . '" class="bordercolor" cellspacing="1" cellpadding="5" align="left" border="0" style="min-width:570px;">
        <tr>';

        if (!$descriptionHidden) {
            $HTML .= '
            <td class="titlebg" align="left" width="250">';
            if ($MandatoryFields['dum_description' . $SuffixString] && $FormType != 'Print') {
                $HTML .= '<img src="images/Warning.gif" />';
            }
            $HTML .= $DescTitle . '
            </td>';
        }

        if (!$typeHidden) {
            $HTML .= '
            <td class="titlebg" align="left" width="250">';
            if ($MandatoryFields['dum_property_type' . $SuffixString] && $FormType != 'Print') {
                $HTML .= '<img src="images/Warning.gif" />';
            }
            $HTML .= $TypeTitle . '
            </td>';
        }

        if (!$valueHidden) {
            $HTML .= '
            <td class="titlebg" align="left" width="250">';
            if ($MandatoryFields['dum_value' . $SuffixString] && $FormType != 'Print') {
                $HTML .= '<img src="images/Warning.gif" />';
            }
            $HTML .= $ValueTitle . '
            </td>';
        }

        $HTML .= '
        <td class="titlebg" align="left" width="50">
        </td>
        </tr>
        ';
    }

    // ifthere is data

    if (!empty($data['property_table']) && is_array($data['property_table'])) {
        foreach ($data['property_table'] as $Property) {
            if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked') {
                $HTML .= MakePropertyRow([
                    'row_suffix' => $RowSuffix,
                    'person_suffix' => $suffix,
                    'formtype' => $FormType,
                    'data' => $Property,
                ]);
            } else {
                $HTML .= '<div style="padding-bottom:5px">' . code_descr(Module::INCIDENTS, 'ipp_damage_type', $Property['ipp_damage_type']);
                if ($Property['ipp_value']) {
                    $HTML .= ' (' . FormatMoneyVal($Property['ipp_value']) . ')';
                }
                $HTML .= '<div>' . $Property['ipp_description'] . '</div></div>';
            }
            ++$RowSuffix;
        }
    } elseif ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked') {
        $HTML .= MakePropertyRow([
            'row_suffix' => 1,
            'person_suffix' => $suffix,
            'formtype' => $FormType,
            // 'hidden' => array('injury' => $injuryHidden, 'body' => $bodyHidden),
            // 'readonly' => array('injury' => $injuryReadOnly, 'body' => $bodyReadOnly)
        ]);
    }

    if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked'
    && !(($descriptionReadOnly && $typeReadOnly && $valueReadOnly)
    || ($descriptionReadOnly && $valueReadOnly && $typeHidden)
    || ($typeReadOnly && $valueReadOnly && $descriptionHidden)
    || ($valueReadOnly && $typeHidden && $descriptionHidden))) {
        $HTML .= '<tr><td colspan="4" class="windowbg2"><input type="button" value="' . _fdtk('add_another_item') . '" style="text-align:right" nextSuffix="' . ($RowSuffix + 1) . '" onclick="addProperty(this, \'' . $suffix . '\', \'' . $LinkType . '\')" /></td></tr>';
    }

    if ($FormType != 'Print' && $FormType != 'ReadOnly' && $FormType != 'Locked' && !($descriptionReadOnly && $typeReadOnly && $valueReadOnly)) {
        $HTML .= '</table>';
    }

    $PropertyObj = CustomFieldFactory::create($FormType, $HTML);

    $Table->makeRow(_fdtk('personal_property') . '<br/>' . GetValidationErrors($data, 'personal_property' . ($suffix ? '_' . $suffix : '')), $PropertyObj);
    $Table->makeTable();
    echo $Table->getFormTable();
}

/**
 * @desc Collates HTML for a table row for the property table. Called on pageload.
 *
 * @param array $aParams Array of parameters
 *
 * @return string The HTML for a property table row
 */
function MakePropertyRow($aParams)
{
    $SuffixString = ($aParams['person_suffix'] ? '_' . $aParams['person_suffix'] : '') . '_' . $aParams['row_suffix'];

    return '<tr id="property_row' . $SuffixString . '" name="property_row' . $SuffixString . '" class="property_row">
            <td>' .
        implode('</td><td>', constructPropertyCells($aParams)) .
        '    </td>' .
        '</tr>';
}

/**
 * @desc Pushes HTML for each field in a property table row into an array to return.
 *
 * @param array $aParams Array of parameters
 *
 * @return array array with one entry for each table cell - contains the HTML for the control within each one
 */
function constructPropertyCells($aParams)
{
    global $MandatoryFields, $HideFields, $ReadOnlyFields, $JSFunctions, $UserLabels;

    $language = LanguageSessionFactory::getInstance()->getLanguage();

    $PropertyCells = [];

    $row_suffix = $aParams['row_suffix'];
    $person_suffix = $aParams['person_suffix'];
    $table = 'inc_personal_property';

    if ($aParams['fieldset'] > 0) {
        $table = $aParams['fieldset'] . '|' . $table;
    }

    $ShortSuffixString = ($person_suffix ? '_' . $person_suffix : '');
    $SuffixString = ($person_suffix ? '_' . $person_suffix . '_' . $row_suffix : '_' . $row_suffix);

    $DescTitle = FirstNonNull([$UserLabels['dum_description' . $ShortSuffixString][$language], Labels_FormLabel::GetFormFieldLabel('ipp_description')]);
    $TypeTitle = FirstNonNull([$UserLabels['dum_property_type' . $ShortSuffixString][$language], Labels_FormLabel::GetFormFieldLabel('ipp_damage_type')]);
    $ValueTitle = FirstNonNull([$UserLabels['dum_value' . $ShortSuffixString][$language], Labels_FormLabel::GetFormFieldLabel('ipp_value')]);

    if (!$HideFields['dum_description' . $ShortSuffixString]) {
        if (!$ReadOnlyFields['dum_description' . $ShortSuffixString]) {
            // need a different name and ID to allow collection of data from discrete fields.
            $DescObj = CustomFieldFactory::create($aParams['formtype'], '<textarea id="ipp_description' . $SuffixString . '" name="ipp_description' . $ShortSuffixString . '[]" rows="2" cols="31">' . $aParams['data']['ipp_description'] . '</textarea>');
            $DescField = $DescObj->GetField();
        }
        if ($aParams['formtype'] !== 'Print') {
            $DescField .= '
                <input type="hidden" name="show_field_ipp_description' . $SuffixString . '" id="show_field_ipp_description' . $SuffixString . '" value="1" />';
        } else {
            $DescField = $aParams['data']['ipp_description'];
        }

        if ($MandatoryFields['dum_description' . $ShortSuffixString]) {
            $DescField = $DescField . '<div class="field_error" id="erripp_description' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
            $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("ipp_description' . $SuffixString . '","' . 'property_section' . $ShortSuffixString . '","' . $DescTitle . ($person_suffix ? ' (Contact #' . $person_suffix . ')' : '') . '"))};';
        }

        $PropertyCells[] = $DescField;
    }

    if (!$HideFields['dum_property_type' . $ShortSuffixString]) {
        if (!$ReadOnlyFields['dum_property_type' . $ShortSuffixString]) {
            $TypeObj = SelectFieldFactory::createSelectField('ipp_damage_type', Module::INCIDENTS, $aParams['data']['ipp_damage_type'], $aParams['formtype'], false, $TypeTitle, 'property_section', $aParams['data']['CHANGED-ipp_damage_type'], $SuffixString);
            $TypeObj->setAltFieldName('ipp_damage_type' . $SuffixString);
            $TypeObj->setOnChangeExtra('jQuery(\'#hidden_ipp_damage_type' . $SuffixString . '\').val(jQuery(\'#ipp_damage_type' . $SuffixString . '\').val());');
            $TypeField = $TypeObj->GetField();
        }
        if ($aParams['formtype'] !== 'Print') {
            $TypeField .= '
            <input type="hidden" name="show_field_ipp_damage_type' . $SuffixString . '" id="show_field_ipp_damage_type' . $SuffixString . '" value="1" />';
        } else {
            $TypeField = code_descr(Module::INCIDENTS, 'ipp_damage_type', $aParams['data']['ipp_damage_type']);
        }
        $TypeField .= '<input type="hidden" id="' . 'hidden_ipp_damage_type' . $SuffixString . '" name="ipp_damage_type' . $ShortSuffixString . '[]" value="' . $aParams['data']['ipp_damage_type'] . '" />';

        if ($MandatoryFields['dum_property_type' . $ShortSuffixString]) {
            $TypeField = $TypeField . '<div class="field_error" id="erripp_damage_type' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
            $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("ipp_damage_type' . $SuffixString . '","' . 'property_section' . $ShortSuffixString . '","' . $TypeTitle . ($person_suffix ? ' (Contact #' . $person_suffix . ')' : '') . '"))};';
        }

        $PropertyCells[] = $TypeField;
    }

    if (!$HideFields['dum_value' . $ShortSuffixString]) {
        if (!$ReadOnlyFields['dum_value' . $ShortSuffixString]) {
            // TODO: Expand MoneyField class to cope with dynamically generated fields
            $ValueObj = CustomFieldFactory::create(
                $aParams['formtype'],
                '<input type="text" name="ipp_value' . $ShortSuffixString . '[]" id="ipp_value' . $SuffixString . '" '
                    . 'size="5" maxlength="11" value="' . $aParams['data']['ipp_value'] . '"'
                    . 'onblur="CheckMoney(jQuery(this), false);"'
                    . ' />',
            );
            $ValueField = $ValueObj->GetField();
        }
        if ($aParams['formtype'] !== 'Print') {
            $ValueField .= '
                <input type="hidden" name="show_field_ipp_valuen' . $SuffixString . '" id="show_field_ipp_value' . $SuffixString . '" value="1" />';
        } else {
            $ValueField = $aParams['data']['ipp_value'];
        }

        if ($MandatoryFields['dum_value' . $ShortSuffixString]) {
            $ValueField = $ValueField . '<div class="field_error" id="erripp_value' . $SuffixString . '" style="display:none">' . _fdtk('mandatory_err') . '</div>';
            $JSFunctions[] = 'if(mandatoryArray){mandatoryArray.push(new Array("ipp_value' . $SuffixString . '","' . 'property_section' . $ShortSuffixString . '","' . $ValueTitle . ($person_suffix ? ' (Contact #' . $person_suffix . ')' : '') . '"))};';
        }

        $PropertyCells[] = $ValueField;
    }

    $HiddenID = '<input type="hidden" name="ipp_recordid' . $ShortSuffixString . '[]" value="' . $aParams['data']['recordid'] . '" />';

    if ($row_suffix != 1 && !$aParams['readonly']['property_section']) {
        $PropertyCells[] = $HiddenID . '<input type="button" name="btnAdd" value="Delete" onclick="deletePropertyRow(' . ($person_suffix ?: '0') . ', ' . ($row_suffix ?: '0') . ')" />';
    } else {
        $PropertyCells[] = $HiddenID;
    }

    return $PropertyCells;
}

/**
 * @desc Called from httprequest.php when we need to insert a new property row via ajax. Echos the html for the new
 * row in json format.
 */
function printPropertyRow()
{
    global $ContactForms;

    $formDesignLoader = Container::get(FormDesignInstanceLoader::class);

    $aParams['row_suffix'] = Sanitize::SanitizeInt($_GET['row_suffix']);
    $aParams['person_suffix'] = Sanitize::SanitizeInt($_GET['person_suffix']);
    $aParams['linktype'] = Sanitize::SanitizeString($_GET['linktype']);

    header('Content-type: application/x-json');

    if (empty($ContactForms) && file_exists($_SESSION['LASTUSEDFORMDESIGN'])) {
        include $_SESSION['LASTUSEDFORMDESIGN'];
    }

    // bit of a hack, but we need to know whether we're looking at a dif1 or dif2
    if (mb_substr_count($_SESSION['LASTUSEDFORMDESIGN'], 'DIF2')) {
        $FormLevel = 2;
    } else {
        $FormLevel = 1;
    }

    /** @var Forms_FormDesign $formDesign */
    $formDesign = Forms_FormDesign::GetFormDesign([
        'module' => Module::CONTACTS,
        'level' => $FormLevel,
        'form_type' => '',
        'link_type' => $aParams['linktype'],
        'parent_module' => Module::INCIDENTS,
    ]);

    if ($aParams['person_suffix'] != '') {
        $formDesign->AddSuffixToFormDesign($aParams['person_suffix']);
    }

    $formDesignLoader->load($formDesign);

    $SuffixString = ($aParams['person_suffix'] ? '_' . $aParams['person_suffix'] : '') . '_' . $aParams['row_suffix'];
    foreach (['dum_description', 'dum_property_type', 'dum_value'] as $propertyField) {
        $fieldSuffixedName = $propertyField . $SuffixString;
        $fieldShortName = preg_replace('/^dum_/', 'ipp_', $propertyField);
        if ($fieldShortName === 'ipp_property_type') {
            $fieldShortName = 'ipp_damage_type';
        }
        if (is_array($GLOBALS['HideFields']) && array_key_exists($fieldSuffixedName, $GLOBALS['HideFields'])) {
            $aParams['hidden'][$fieldShortName] = true;
        }

        if (is_array($GLOBALS['ReadOnlyFields']) && array_key_exists($fieldSuffixedName, $GLOBALS['ReadOnlyFields'])) {
            $aParams['readonly'][$fieldShortName] = true;
        }

        if (is_array($GLOBALS['MandatoryFields']) && array_key_exists($propertyField, $GLOBALS['MandatoryFields'])) {
            $aParams['mandatoryFields'][$propertyField] = true;
        }
    }

    $JSONdata['cells'] = constructPropertyCells($aParams);
    $JSONdata['row_suffix'] = $aParams['row_suffix'];
    $JSONdata['person_suffix'] = $aParams['person_suffix'];
    $JSONdata['mandatoryFields'] = $aParams['mandatoryFields'];
    $JSONdata['js'] = getJSFunctions();

    // encode and return json data...
    return $JSONdata;
}

/**
 * @desc Called when contact is saved - pushes data from $_POST into the inc_personal_property table.
 */
function SaveProperty($aParams)
{
    if ($aParams['module'] == 'INC') { // no support for other modules yet.
        $SuffixString = ($aParams['suffix'] ? '_' . $aParams['suffix'] : '');
        $Listorder = 0;
        $UpdatedRecords = []; // keeps track of ids which have been re-saved. Any not in this list (i.e. not posted) will be deleted.

        if ($_POST['ipp_description' . $SuffixString] != '') {
            $loggedInUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;

            foreach ($_POST['ipp_description' . $SuffixString] as $ref => $Desc) {
                if (empty($_POST['ipp_value' . $SuffixString][$ref])) {
                    $_POST['ipp_value' . $SuffixString][$ref] = null;
                }

                if ($Desc != '') {
                    ++$Listorder;

                    $propertyValue = CheckMoneyValue($ref, $_POST['ipp_value' . $SuffixString]);

                    if ($_POST['ipp_recordid' . $SuffixString][$ref]) {
                        $sql = 'UPDATE inc_personal_property SET
                                listorder = :listorder,
                                ipp_description = :ipp_description,
                                ipp_damage_type = :ipp_damage_type,
                                ipp_value = :ipp_value,
                                updateddate = :updateddate,
                                updatedby = :updatedby
                                WHERE
                                recordid = :recordid';

                        DatixDBQuery::PDO_query(
                            $sql,
                            [
                                'listorder' => $Listorder,
                                'ipp_description' => Sanitize::SanitizeString($_POST['ipp_description' . $SuffixString][$ref]),
                                'ipp_damage_type' => Sanitize::SanitizeString($_POST['ipp_damage_type' . $SuffixString][$ref]),
                                'ipp_value' => $propertyValue,
                                'updateddate' => date('Y-m-d H:i:s.000'),
                                'updatedby' => $loggedInUserInitials,
                                'recordid' => Sanitize::SanitizeInt($_POST['ipp_recordid' . $SuffixString][$ref]),
                            ],
                        );

                        $UpdatedRecords[] = Sanitize::SanitizeInt($_POST['ipp_recordid' . $SuffixString][$ref]);
                    } else {
                        $recordIdGenerator = (new RecordIdGeneratorFactory())->create('inc_personal_property');
                        $recordid = $recordIdGenerator->generateRecordId();

                        DatixDBQuery::PDO_build_and_insert(
                            'inc_personal_property',
                            [
                                'recordid' => $recordid,
                                'inc_id' => $aParams['main_recordid'],
                                'con_id' => $aParams['recordid'],
                                'listorder' => $Listorder,
                                'ipp_description' => $_POST['ipp_description' . $SuffixString][$ref],
                                'ipp_damage_type' => $_POST['ipp_damage_type' . $SuffixString][$ref],
                                'ipp_value' => $propertyValue,
                                'updateddate' => date('Y-m-d H:i:s.000'),
                                'updatedby' => $loggedInUserInitials,

                            ],
                        );

                        $UpdatedRecords[] = $recordid;
                    }
                }
            }
        }

        $sql = 'DELETE FROM inc_personal_property
        WHERE inc_id = :inc_id
        AND con_id = :con_id';

        if (!empty($UpdatedRecords)) {
            $inClauseHelper = new SqlInClauseHelper();
            $sql .= ' AND ' . $inClauseHelper->notInClause('recordid', $UpdatedRecords);
        }

        DatixDBQuery::PDO_query($sql, ['inc_id' => $aParams['main_recordid'], 'con_id' => $aParams['recordid']]);
    }
}
