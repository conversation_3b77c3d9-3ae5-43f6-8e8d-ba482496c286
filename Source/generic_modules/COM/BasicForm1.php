<?php

use app\models\framework\config\DatixConfigFactory;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormKeys;
use Source\generic_modules\MED\MedicationFormSectionHelperFactory;
use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSections;
use src\complaints\model\OmbudsmanSubjectFields;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\medications\models\MedicationPrefixes;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();
$FormType = $basicFormHelper->getValidFormMode($FormType);

$registry ??= Container::get(Registry::class);

$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showOmbudsmanFields = $registry->getParm('OMBUDSMAN_SECTION', 'N')->isTrue();
$showNcdsFields = $registry->getParm('COM_DATA_SUBMISSION', 'N')->isTrue();
$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();
$config = (new DatixConfigFactory())->getInstance();
$showLastChildFirst = $config->showLastChildFirst();
$medicationsSectionHelper = (new MedicationFormSectionHelperFactory())->create($FormType, 1, Module::FEEDBACK);

$FormArray = [
    'Parameters' => [
        'Condition' => false,
        'ExtraContacts' => 1,
    ],
    'contacts_type_C' => [
        'Title' => _fdtk('details_of_person_providing', $useFormDesignLanguage) . ' ' . _fdtk('COMNameTitle', $useFormDesignLanguage),
        'LinkRole' => 'COMP',
        'module' => 'COM',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => ['C' => ['module' => 'CON']],
        'contacttype' => 'C',
        'suffix' => 1,
        'Rows' => [],
    ],
    'person_header' => [
        'Title' => _fdtk('com_person_affected_plural'),
        'Rows' => ['show_person'],
    ],
    'contacts_type_A' => [
        'Title' => _fdtk('details_of_person_affected_by_the', $useFormDesignLanguage) . ' ' . _fdtk('COMName', $useFormDesignLanguage),
        'LinkRole' => 'REL',
        'module' => 'COM',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => 'A',
        'suffix' => 2,
        'LinkedForms' => ['A' => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'details' => [
        'Title' => _fdtk('details_of_feedback', $useFormDesignLanguage),
        'Rows' => [
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'com_method',
            'com_type',
            'com_subtype',
            'com_dreceived',
            'com_consent',
            'com_dincident',
            'com_dopened',
            'com_dclosed',
            'com_dreopened',
            'com_detail',
            'com_purchaser',
            'com_otherref',
            'com_curstage',
            'com_outcome',
            'com_summary',
            'dum_com_grading',
            // SPSC
            ['Name' => 'requested_consultant', 'Condition' => $showSpscFields],
            ['Name' => 'priority_scale', 'Condition' => $showSpscFields],
            ['Name' => 'is_record_sensitive', 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_SPECIALTY, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::MCA_OR_NA, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_PAT_EXPECTATIONS, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_PAT_UPDATE_PREF, 'Condition' => $showSpscFields],
            // NCDS
            ['Name' => FeedbackFields::COM_OUTCOME_GRADING, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_SERIOUS_INCIDENT, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_WELSH_LANGUAGE, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_REDRESS_ESCALATED, 'Condition' => $showNcdsFields],
        ],
    ],
    FeedbackSections::OUTBREAK => [
        'Title' => _fdtk('com_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => FeedbackFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => FeedbackFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],
    'ko41' => [
        'Title' => 'KO41',
        'Rows' => [
            'com_ko41_type',
            'com_koservarea',
            'com_kosubject',
            'com_koprof',
            'com_koethnic_pat',
            'com_koethnic_staff',
        ],
    ],
    'subject_header' => [
        'Title' => _fdtk('subjects_of', $useFormDesignLanguage) . ' ' . _fdtk('COMNameTitle', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            'com_subjects_linked',
        ],
    ],
    'subject' => [
        'Title' => _fdtk('table_compl_subjects', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design'),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'DoSubjectSection' => [
                'controller' => src\generic\controllers\SubjectsController::class,
            ],
        ],
        'ExtraParameters' => ['subject_name' => 'com_subject'],
        'Rows' => [],
    ],
    'subject_design' => [
        'Title' => _fdtk('table_compl_subjects', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'NoSectionActions' => true,
        'AltSectionKey' => 'subject',
        'Rows' => [
            'csu_subject',
            'csu_subsubject',
            'csu_stafftype',
            'csu_location_id',
            'csu_service_id',
            'csu_outcome',
            'csu_notes',
            'csu_dcompleted',
            'com_service_area',
            'subject_type',
            'subject_subtype',
            FeedbackFields::COM_ISSUE_PATHWAY,
            FeedbackFields::COM_ISSUE_TYPE,
            ['Name' => 'csu_level_of_harm', 'Condition' => $showSpscFields],
        ],
    ],
    'isd' => [
        'Title' => 'ISD',
        'Rows' => [
            'com_isd_unit',
            'com_isd_locactual',
            'com_isd_consent',
            'com_isd_dconsent_req',
            'com_isd_dconsent_rec',
            'com_isd_div_sent',
            'com_isd_ref_added',
            'com_isd_iaas_involved',
            'com_isd_cas_involved',
            'com_isd_chi_no',
            'com_isd_resp_sent_20',
            'com_isd_resp_20_reason',
            'com_isd_agree_40',
            'com_isd_agree_40_date',
            'com_isd_resp_40_reason',
            'com_isd_actions',
            'com_isd_plan',
            'com_isd_dexport',
        ],
    ],
    'issue_header' => [
        'Title' => _fdtk('issues_of', $useFormDesignLanguage) . ' ' . _fdtk('COMNameTitle', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            'com_issues_linked',
        ],
    ],
    'issue' => [
        'Title' => _fdtk('table_compl_isd_issues', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design'),
        'NoFieldAdditions' => true,
        'Include' => 'Source/generic_modules/COM/ModuleFunctions.php',
        'Function' => 'DoIssueSection',
        'NoTitle' => true,
        'ExtraParameters' => ['issue_name' => 'com_issue'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'issue_design' => [
        'Title' => _fdtk('table_compl_isd_issues', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'issue',
        'NoSectionActions' => true,
        'Rows' => [
            'cisd_type',
            'cisd_category',
            'cisd_subcategory',
            'cisd_pat_adm_type',
            'cisd_service_area',
            'cisd_specialty',
            'cisd_staff_group',
            'cisd_staff_position',
        ],
    ],
    'additional_info' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_document',
            ['Name' => FeedbackFields::REFERRED_TO_OMBUDSMAN, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::EARLY_SETTLEMENT => [
        'Title' => _fdtk('early_settlement', $useFormDesignLanguage),
        'Condition' => $registry->getParm('EARLY_SETTLEMENT', 'N')->isTrue(),
        'Rows' => [
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED,
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED,
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED,
        ],
    ],
    FeedbackSections::OMBUDSMAN => [
        'Title' => _fdtk('ombudsman', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_FIRST_CONTACT, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_EVIDENCE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_EVIDENCE_SUBMITTED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_REFERENCE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_HANDLER, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_CURRENT_STAGE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::EARLY_SETTLEMENT_PROPOSAL, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_INVESTIGATION_DETAILS => [
        'Title' => _fdtk('ombudsman_investigation_details', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_INVESTIGATION_BEGAN, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_RESPONSE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DOC_INV_SUB, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_INV_SUB, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_DRAFT_REPORT => [
        'Title' => _fdtk('ombudsman_draft_report', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_DRAFT_RECEIVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DRAFT_RESPONSE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_REC_RECIEVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_ACTION_PLAN, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DRAFT_REPORT_SUB, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_FINAL_REPORT => [
        'Title' => _fdtk('ombudsman_final_report', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_REPORT_RECEIVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::FINAL_REP_TYPE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_OUTCOME, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_LEARNING, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_SUBJECTS => [
        'Title' => _fdtk('ombudsman_subject', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN],
        'LinkedDataSection' => true,
        'Condition' => $showOmbudsmanFields && ($formType !== FormTable::MODE_LINKED_DATA_SEARCH),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'com_ombudsman_subject'],
    ],
    FeedbackSections::OMBUDSMAN_SUBJECTS . '_design' => [
        'Title' => _fdtk('ombudsman_subject', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields && ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => FeedbackSections::OMBUDSMAN_SUBJECTS,
        'NoSectionActions' => true,
        'Rows' => OmbudsmanSubjectFields::getFields(),
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true),
        'NoFieldAdditions' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],
    'linked_documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_PRINT,
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'your_manager' => [
        'Title' => _fdtk('your_manager', $useFormDesignLanguage),
        'module' => 'COM',
        'Condition' => bYN(GetParm('COM_EMAIL_MGR', 'N')),
        BasicFormKeys::NO_READ_ONLY => true,
        'Rows' => [
            [
                'Type' => 'formfield',
                'Name' => 'com_mgr',
                'Title' => 'Your Manager',
                BasicFormKeys::NO_READ_ONLY => true,
                'FormField' => MakeManagerDropdownGeneric('COM', $com, $FormType, ['COM2', 'RM']),
            ],
        ],
    ],
    'contacts_type_R' => [
        'Title' => _fdtk('details_of_person_reporting_the', $useFormDesignLanguage) . ' ' . _fdtk('COMName', $useFormDesignLanguage),
        'LinkRole' => GetParm('REPORTER_ROLE', 'REP'),
        'module' => 'COM',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => ['R' => ['module' => 'CON']],
        'contacttype' => 'R',
        'suffix' => 3,
        'Rows' => [],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_COM', 'N')),
        'Rows' => [
            'com_affecting_tier_zero',
            'com_type_tier_one',
            'com_type_tier_two',
            'com_type_tier_three',
        ],
    ],
    'udf' => [
        'ExtraFields' => $UDFGroups,
        'NoFieldAdditions' => true,
    ],
];

if ($config->isFeedbackMedicationsEnabled()) {
    $FormArray['iq_medications'] = $medicationsSectionHelper->createMedicationFormSection();
    $FormArray['iq_administered_drug_design'] = $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::ADMINISTERED_FIELD_PREFIX);
    $FormArray['iq_correct_drug_design'] = $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::CORRECT_FIELD_PREFIX);
    $FormArray['iq_medications_other_design'] = $medicationsSectionHelper->createOtherFieldsFormDesignSection();
    $FormArray['medication_search'] = $medicationsSectionHelper->createMedicationSearchFormSection();
}

return $FormArray;
