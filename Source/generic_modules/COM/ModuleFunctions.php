<?php

use app\framework\DoctrineEntityManagerFactory;
use app\models\contact\entities\HoldingLetterEntity;
use app\models\generic\entities\HasUUIDEntity;
use app\models\generic\valueObjects\Module;
use app\Repository\FeedbackRepository;
use app\services\audit\FullAuditPerformerFactory;
use app\services\contact\ContactComplainantPatientRepository;
use app\services\idGenerator\RecordIdGeneratorFactory;
use Doctrine\ORM\EntityRepository;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Source\generic_modules\COM\workflowListings\FeedbackWorkflowInterface;
use Source\generic_modules\MED\MedicationFormSectionHelperFactory;
use src\complaints\controllers\ComplaintController;
use src\component\form\FormTableFactory;
use src\contacts\model\transformer\LinkContactModelTransformer;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\medications\Services\MedicationsServiceFactory;
use src\security\SecuredEncryptionService;
use src\system\container\ContainerFactory;
use src\system\container\facade\Container;

function SaveComplainantAsPatient(array $aParams): void
{
    Container::get(ContactComplainantPatientRepository::class)->saveFeedbackClaimant($aParams);
}

function DoIssueSection($data, $FormType, $module, $ExtraParams = [])
{
    // need to work out how many subjects to display, and then display them.

    if (isset($data['error'])) {
        $aIssues = getLinkedIssuesFromPost($data, $module);
    } else {
        $aIssues = getLinkedIssues(['recordid' => $data['recordid'], 'module' => $module]);
    }

    foreach ($aIssues as $id => $LinkedIssue) {
        echo getIssueSectionHTML(['module' => $module, 'data' => $LinkedIssue, 'formtype' => $FormType, 'suffix' => ((int) $id + 1), 'clearsection' => ($id == 0), 'issue_name' => $ExtraParams['issue_name']]);
    }

    $NumIssues = count($aIssues);

    if ($FormType == 'Search') { // no linked issues
        // When on the search form, the issue section on the issue form has the same section key as the issues section
        // on the main complaints form. This causes some collisions when determining whether sections should show.
        unset($GLOBALS['ShowHideSections']['issue']);
        echo getIssueSectionHTML(['module' => $module, 'data' => $data, 'formtype' => $FormType, 'clearsection' => true, 'issue_name' => $ExtraParams['issue_name']]);
        $NumIssues = 1;
    } elseif (count($aIssues) == 0) { // no linked issues
        echo getIssueSectionHTML([
            'module' => $module,
            'data' => [],
            'formtype' => $FormType == 'ReadOnly' ? $FormType : 'New',
            'suffix' => 1,
            'clearsection' => true,
            'issue_name' => $ExtraParams['issue_name'], ]);

        $NumIssues = 1;
    }

    echo '<script language="javascript">dif1_section_suffix[\'' . $ExtraParams['issue_name'] . '\'] = ' . ($NumIssues + 1) . '</script>';

    if ($FormType != 'Search' && $FormType != 'Print' && $FormType != 'ReadOnly') {
        $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';
        echo '
            <li class="new_windowbg" id="add_another_' . $ExtraParams['issue_name'] . '_button_list">
                <button id="section_' . $ExtraParams['issue_name'] . '_button" class="dtx-button dtx-button-small dtx-button-small-min-width button-tertiary" type="button" '
                . 'onclick="AddSectionToForm(\'' . $ExtraParams['issue_name'] . '\',\'\',jQuery(\'#' . $ExtraParams['issue_name'] . '_section_div_\'+(dif1_section_suffix[\'' . $ExtraParams['issue_name'] . '\']-1)),\'' . Sanitize::getModule($module) . '\',\'\', true, ' . $spellChecker . ', ' . (isset($_REQUEST['form_id']) ? Sanitize::SanitizeInt($_REQUEST['form_id']) : 'null') . ');">'
                . '<span>' . _fdtk('add_another') . '</span>'
                . '</button>'
            . '</li>';
    }
}

function getLinkedIssues($aParams)
{
    $module = $aParams['module'];
    $recordid = $aParams['recordid'];
    $issues = [];

    if ($module == 'COM' && $recordid) {
        $Rows = getIssueFieldsForSave(['module' => $module]);
        $sql = 'SELECT recordid as issue_id, ' . implode(', ', $Rows['Rows']) . ' FROM COMPL_ISD_ISSUES WHERE com_id = :recordid ORDER BY listorder';
        $issues = DatixDBQuery::PDO_fetch_all($sql, ['recordid' => $recordid]);
    }

    return $issues;
}

/**
 * Creates an array of issue records from POST data.
 *
 * Used to populate the Issues section when returning to the form after a validation error.
 *
 * @param array $data the POST data
 * @param string $module the current module
 *
 * @return array $issues
 */
function getLinkedIssuesFromPost($data, $module)
{
    $issues = [];
    $fields = getIssueFieldsForSave(['module' => $module]);

    for ($i = 1; $i < $data[\UnicodeString::strtolower($module) . '_issue_max_suffix']; ++$i) {
        $issue = [];
        foreach ($fields['Rows'] as $field) {
            $issue[$field] = $field == 'listorder' ? $data[\UnicodeString::strtolower($module) . '_issue_listorder' . '_' . $i] : $data[$field . '_' . $i];
        }
        $issues[] = $issue;
    }

    return $issues;
}

function GetIssueSectionHTML($aParams)
{
    global $JSFunctions, $formlevel;

    if ($formlevel == null) {
        $formlevel = ($_POST['form_level'] ?: 1);
    }

    $SavedFormDesignSettings = saveFormDesignSettings();
    unsetFormDesignSettings();

    $AJAX = $aParams['ajax'];

    if ($AJAX) {
        $formlevel = $aParams['level'];
    }

    $aIssueRows = getBasicIssueForm($aParams);
    $request = new Request();
    $formId = $request->getParameter('form_id');
    $issuesFormDesign = Forms_FormDesign::GetFormDesign(['id' => $formId, 'module' => $aParams['module'], 'level' => $formlevel]);
    $issuesFormDesign = ModifyFormDesignForIssues($issuesFormDesign);

    if ($aParams['suffix']) {
        $issuesFormDesign->AddSuffixToFormDesign($aParams['suffix']);
        $aParams['data'] = AddSuffixToData($aParams['data'], $aParams['suffix'], getIssueFieldsForSave(['module' => $aParams['module']]));
    }

    $oIssueTable = FormTableFactory::create($aParams['formtype'], $aParams['module'], $issuesFormDesign);
    $oIssueTable->makeForm($aIssueRows, $aParams['data'], $aParams['module'], ['dynamic_section' => true]);

    $html = '';

    if (!$AJAX && $aParams['formtype'] != 'Print') {
        $html .= '<div id="' . $aParams['issue_name'] . '_section_div_' . $aParams['suffix'] . '">';
    }

    $html .= $oIssueTable->getFormTable();
    $html .= '<input type="hidden" name="' . $aParams['issue_name'] . '_link_id_' . $aParams['suffix'] . '" id="' . $aParams['issue_name'] . '_link_id_' . $aParams['suffix'] . '" value="' . $aParams['data']['issue_id'] . '" />';


    if ($aParams['formtype'] != 'Search' && $aParams['formtype'] != 'Print' && $aParams['formtype'] != 'ReadOnly') {
        $spellChecker = $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';
        $html .= '
            <div id="copy_issue_button_div" class="button-margin-bottom">
                <button id="copy_issue_section" class="dtx-button dtx-button-small dtx-button-small-min-width button-secondary" type="button" onclick="CopySectionToForm(\'' . $aParams['issue_name'] . '\',\'\',jQuery(\'#' . $aParams['issue_name'] . '_section_div_' . $aParams['suffix'] . '\'),\'' . $aParams['module'] . '\',' . $aParams['suffix'] . ', true, ' . $spellChecker . ', ' . (is_numeric($_REQUEST['form_id']) ? Sanitize::SanitizeInt($_REQUEST['form_id']) : 'null') . ');">
                    <span>' . _fdtk('copy_issue_button') . '</span>
                </button>
            </div>';
    }

    if (!$AJAX && $aParams['formtype'] != 'Print') {
        $html .= '</div>';
    }

    loadFormDesignSettings($SavedFormDesignSettings);

    return $html;
}

function getBasicIssueForm($aParams)
{
    global $ModuleDefs;

    $aParams['issue_name'] = $ModuleDefs[$aParams['module']]['ISSUE_TYPE'];
    $RowList = GetIssueRowList($aParams);

    if ($aParams['rows']) {
        return ['Rows' => $RowList];
    }

    return [
        'Parameters' => $aParams['formtype'] != 'Search' ? ['Suffix' => $aParams['suffix']] : [],
        'issue' . ($aParams['suffix'] && $aParams['formtype'] != 'Search' ? '_' . $aParams['suffix'] : '') => [
            'Title' => 'Issue',
            'OrderField' => ['id' => $aParams['issue_name'] . '_listorder_' . $aParams['suffix'], 'value' => $aParams['data']['listorder']],
            'ClearSectionOption' => $aParams['clearsection'],
            'DeleteSectionOption' => !$aParams['clearsection'],
            'ContactSuffix' => $aParams['formtype'] != 'Search' ? $aParams['suffix'] : '',
            'DynamicSectionType' => $aParams['issue_name'],
            'Rows' => $RowList,
            'ContainedIn' => 'issue',
        ], ];
}

function GetIssueRowList($aParams)
{
    global $ModuleDefs;

    $RowList = $ModuleDefs[$aParams['module']]['LINKED_RECORDS'][$ModuleDefs[$aParams['module']]['ISSUE_TYPE']]['basic_form']['Rows'];

    return $RowList;
}

function getIssueFieldsForSave($aParams)
{
    $RowList = GetIssueRowList($aParams);
    $RowList[] = 'listorder';

    return ['Rows' => $RowList];
}

function ModifyFormDesignForIssues(Forms_FormDesign $design)
{
    if (is_array($design->ExpandSections)) {
        $NewArray = [];
        foreach ($design->ExpandSections as $FieldName => $SectionArray) {
            foreach ($SectionArray as $SectionID => $Details) {
                if ($Details['section'] != 'issue') {
                    $NewArray[$FieldName][] = $Details;
                }
            }
        }

        $design->ExpandSections = $NewArray;
    }

    // remove extra sections/extra fields
    $design->unsetUserDefinedElements();

    return $design;
}

/**
 * @desc Calculates and updates the Independent Review date fields
 *
 * @param array $com all $_POST data for the complaint
 */
function SetIndependentReviewDates($com)
{
    if (CheckCalculateIRDates(['data' => $_POST])) {
        $CalculatedDates = CalculateIRDates(['data' => $com, 'com_drequest' => ($_POST['com_drequest'] ?: GetTodaysDate()), 'complaint_recordid' => $_POST['main_recordid']]);
    }

    $DateFields = GetComplaintIRDateFields();

    $com_id = $com['recordid'];

    $UpdateSetLink = GeneratePDOSQLFromArrays(
        [
            'FieldArray' => $DateFields,
            'DataArray' => $CalculatedDates ?? null,
            'Module' => 'COM',
        ],
        $PDOParamsArray,
    );

    if ($UpdateSetLink) {
        $sql = "UPDATE compl_main
            SET {$UpdateSetLink}
            WHERE recordid = {$com_id}";

        DatixDBQuery::PDO_query($sql, $PDOParamsArray);
    }
}

/**
 * @desc Clean (first) subject fields on claim record when no subject is linked
 *
 * @param mixed array $aParams $_POST datafrom form submitted
 */
function CleanupAfterEmptySubject($aParams)
{
    /** @var FeedbackRepository $feedbackRepository */
    $feedbackRepository = Container::get(FeedbackRepository::class);
    $result = $feedbackRepository->getLinkedFeedbackSubjects($aParams['recordid']);

    if (empty($result)) {
        $feedbackRepository->setFeedbackSubjectToEmpty($aParams['recordid']);
    }
}

/**
 * @desc Check whether IR dates need to be updated
 *
 * @param mixed array $aParams $_POST datafrom form submitted
 */
function CheckCalculateIRDates($aParams)
{
    if (empty($aParams['data']['CHANGED-com_drequest'])) {
        return false;
    }

    $ComplDoneArray = ['com_dackreq'];

    foreach ($ComplDoneArray as $field) {
        if ($aParams['data'][$field] !== '' && $aParams['data'][$field] !== null) {
            return false;
        }
    }

    return true;
}

/**
 * @desc Calculates the dates for each IR due date including working days only
 *
 * @param mixed array $aParams $_POST datafrom form submitted
 *
 * @return date array $dates Array of date objects for each IR due date
 */
function CalculateIRDates($aParams)
{
    $ComplVars = [
        'com_ddueackreq' => ['default_parm' => 'IR_ACKDAYS', 'default_val' => 2],
        'com_dduelaychair' => ['default_parm' => 'IR_LAYCHAIRDAYS', 'default_val' => 4],
        'com_dduedecision' => ['default_parm' => 'IR_DECISIONDAYS', 'default_val' => 20],
        'com_dduepappt' => ['default_parm' => 'IR_PANELDAYS', 'default_val' => 20],
        'com_dduepdraft' => ['default_parm' => 'IR_DRAFTDAYS', 'default_val' => 50],
        'com_ddueppublish' => ['default_parm' => 'IR_FINALDAYS', 'default_val' => 10],
        'com_dduecereply' => ['default_parm' => 'IR_REPLYDAYS', 'default_val' => 20],
    ];

    foreach ($ComplVars as $key => $Details) {
        $days[$key] = GetParm($Details['default_parm'], $Details['default_val']);
    }

    $DateTracker = new DateTime(UserDateToSQLDate($aParams['com_drequest']));

    $dates = [];
    foreach ($days as $type => $numdays) {
        $numdays = CalculateWorkingDays($numdays, $DateTracker);
        $DateTracker->modify('+' . $numdays . ' day');
        $key = $type . ($aParams['suffix'] ? '_' . $aParams['suffix'] : '');
        $dates[$key] = $DateTracker->format('Y-m-d');
    }

    return $dates;
}

/**
 * @desc Returns a list of date fields used for the IR section
 *
 * @return string array Array of date field names
 */
function GetComplaintIRDateFields()
{
    return [
        'com_ddueackreq',
        'com_dackreq',
        'com_dstatement',
        'com_dduelaychair',
        'com_dlaychair',
        'com_dduedecision',
        'com_ddecision',
        'com_dinform',
        'com_dduepappt',
        'com_dpappt',
        'com_dduepdraft',
        'com_dpdraft',
        'com_ddueppublish',
        'com_dppublish',
        'com_dduecereply',
        'com_dcereply',
    ];
}

/**
 * @desc Check whether IR dates need to be updated
 *
 * @param mixed array $com $_POST values for comaplaints form submitted
 *
 * @return string $error Any errors message that occured
 */
function SaveHCC($com): string
{
    // $com = ParseSaveData(array('module' => 'COM', 'data' => $com));
    $com_id = $com['recordid'];

    $HCCFields = getHHCFields();

    foreach ($HCCFields as $i => $HCCFieldName) {
        if (($_POST['CHANGED-' . $HCCFieldName] ?? null) != '') {
            $FieldsToUpdate[] = $HCCFieldName;
        }
    }

    $error = '';
    if (isset($FieldsToUpdate) && is_array($FieldsToUpdate)) {
        $sql = "SELECT count(*) as check_hcc FROM rev_main WHERE link_id = {$com_id} and link_module ='COM'";

        $row = DatixDBQuery::PDO_fetch($sql);

        if (!$row) {
            $error = "An error has occurred when trying to save the notes attached to this record. Please report the following to the Datix administrator: {$sql}";
        } else {
            foreach ($FieldsToUpdate as $i => $HCC_field) {
                $PDOParamsArray[$HCC_field] = $com[$HCC_field];
            }

            if ($row['check_hcc'] > 0) {
                $UpdateSetLink = GeneratePDOSQLFromArrays(
                    [
                        'FieldArray' => $FieldsToUpdate,
                        'DataArray' => $com,
                        'Module' => 'COM',
                        'Extras' => ['updatedby' => $_SESSION['initials'], 'updateddate' => date('d-M-Y H:i:s')],
                    ],
                    $PDOParamsArray,
                );

                $sql = "UPDATE rev_main
                    SET {$UpdateSetLink}
                    WHERE link_id = {$com_id} and link_module = 'COM'";

                (new FullAuditPerformerFactory())->create()->perform(Module::FEEDBACK, 'rev_main', $com_id, $PDOParamsArray, "link_id = {$com_id} and link_module = 'COM'", false);

                DatixDBQuery::PDO_query($sql, $PDOParamsArray);
            } else {
                $recordIdGenerator = (new RecordIdGeneratorFactory())->create('rev_main');
                $rev_id = $recordIdGenerator->generateRecordId();

                $sql = 'INSERT INTO rev_main ';

                $sql .= GeneratePDOInsertSQLFromArrays(
                    [
                        'Module' => 'COM',
                        'FieldArray' => $FieldsToUpdate,
                        'DataArray' => $com,
                        'Extras' => ['recordid' => $rev_id, 'link_id' => $com_id, 'link_module' => 'COM'],
                    ],
                    $PDOParamsArray,
                );

                if (!DatixDBQuery::PDO_query($sql, $PDOParamsArray)) {
                    fatal_error('Cannot insert HCC data');
                }
            }
        }
    }

    return $error;
}

/**
 * @desc Returns a list of fields used for the HCC section
 *
 * @return string array Array of HCC field names
 */
function getHHCFields()
{
    return ['rev_drequested',
        'rev_reason',
        'rev_dclosed',
        'rev_ddecisiondue',
        'rev_ddecisiondone',
        'rev_init_handler',
        'rev_init_outcome',
        'rev_investigate_yn',
        'rev_init_comments',
        'rev_dhctermsdue',
        'rev_dhctermsdone',
        'rev_dtermsdue',
        'rev_dtermsdone',
        'rev_dcompleteddue',
        'rev_dcompleteddone',
        'rev_inv_handler',
        'rev_inv_outcome',
        'rev_panelreview_yn',
        'rev_inv_recommend',
        'rev_inv_action',
        'rev_dpanelrequested',
        'rev_dpanelcompleted',
        'rev_panel_outcome',
        'rev_ombudsman_yn',
        'rev_panel_recommend',
        'rev_panel_action',
        'rev_dombrequested',
        'rev_omb_assessment',
        'rev_omb_upheld',
        // 'rev_omb_reason',
        'rev_omb_handler',
        'rev_omb_recommend',
        'rev_omb_action', ];
}

/**
 * @desc Returns an array of data for each field in the HCC section
 *
 * @return mixed array $returnData Array of HCC field data
 */
function getHCC($recordid)
{
    $HCCFields = getHHCFields();
    $HCCFieldList = implode(',', $HCCFields);

    $sql = "SELECT {$HCCFieldList} FROM rev_main WHERE link_id = {$recordid} and link_module ='COM'";

    $returnData = DatixDBQuery::PDO_fetch($sql);

    if (is_array($returnData)) {
        return $returnData;
    }

    return [];
}

/**
 * @desc Saves any data for primary complainant dates shown on main complaints form.
 *
 * @param array $com $_POST values for complaints form submitted
 *
 * @throws DatixDBQueryException
 * @throws InvalidDataException
 */
function SaveComplaintDatesOnComplaint(array $com)
{
    $registry = Container::get(Registry::class);

    $aDateValues = CheckDatesFromArray(GetComplaintDateFields(), Sanitize::SanitizeStringArray($_POST));
    $com = array_merge($com, $aDateValues);
    $com_id = $com['recordid'];

    if ($com['recordid']) {
        $sql = "SELECT TOP 1 recordid AS link_compl_id,
                 *
                FROM link_compl
                WHERE com_id = {$com_id} AND
                 lcom_current = 'Y' AND
                 lcom_primary = 'Y'";

        $row = DatixDBQuery::PDO_fetch($sql);

        $linkComplId = $row['link_compl_id'] ?? 0;

        if ($linkComplId > 0) {
            if (CheckCalculateComplaintDates(['data' => $_POST])) {
                $CalculatedDates = CalculateComplaintDates([
                    'data' => $_POST,
                    'date_received' => $_POST['lcom_dreceived'],
                    'complaint_recordid' => $com_id,
                ]);

                $com = array_merge($com, $CalculatedDates);
            }

            $dateFields = GetComplaintDateFields();
            $dateFields[] = 'lcom_primary'; // non-date fields also stored here.
            $dateFields[] = 'lcom_iscomplpat';

            $updateData = $com;

            $reopenedDate = $_POST['lcom_dreopened'] ?? null;
            $reopen = $_POST['lcom_reopen'] ?? 'N';

            if (!$reopenedDate && $reopen === 'Y') {
                // Use today's date if we're reopening but haven't explicitly specified the reopen date
                $reopenedDate = (new DateTime())->format('Y-m-d');
            }

            // if this has been reopened, the dates being saved are no longer current.
            if ($reopenedDate) {
                $dateFields[] = 'lcom_current';
                $updateData['lcom_current'] = 'N';
                $updateData['lcom_primary'] = 'N';
                $updateData['lcom_dreopened'] = $reopenedDate;
            }

            if ($registry->getParm('SPSC_DATASET', 'N')->toBool()) {
                $dateFields[] = 'lcom_reopen';
                $dateFields[] = 'lcom_reason_for_reopen';
            }

            $globalIsSet = $registry->getParm('COM_AUTO_CLOSE_REPLIED', 'N')->isTrue();
            $recordHasClosedDate = isset($com['com_dclosed']);
            $closedDateChanged = ($com['CHANGED-com_dclosed'] ?? null) == 1;
            $recordHasRepliedDate = isset($com['lcom_dreplied']);

            if ($globalIsSet && $recordHasClosedDate && $closedDateChanged && !$recordHasRepliedDate) {
                $updateData['lcom_dreplied'] = $com['com_dclosed'];
            }

            // Hold1 fields are in a separate table and should be handled separately
            $dateFields = array_diff($dateFields, ['lcom_dhold1', 'lcom_dduehold1']);

            $updateSetLink = GeneratePDOSQLFromArrays([
                'FieldArray' => $dateFields,
                'DataArray' => $updateData,
                'Module' => Module::FEEDBACK,
            ], $pdoParamsArray);

            if ($updateSetLink) {
                $sql = "UPDATE link_compl
                    SET {$updateSetLink}
                    WHERE recordid = {$linkComplId}";

                DatixDBQuery::PDO_query($sql, $pdoParamsArray);
            }

            saveHoldingDates($updateData, $linkComplId);

            // if this has been reopened, we need to re-calculate the dates and save a new link_compl record.
            if ($reopenedDate) {
                $com['lcom_primary'] = 'Y'; // must be the case if we're here, so we ensure this value is set in case the field is hidden on the form
                $com['lcom_dreopened'] = $reopenedDate;

                // If the chain has been reopened, the record should be moved back into the main chain status flow, which means it cannot be marked as closed.
                DatixDBQuery::PDO_query(
                    '
                    UPDATE compl_main
                    SET com_dclosed = null
                    WHERE recordid = :recordid',
                    ['recordid' => $com['recordid']],
                );
                // Record audit that we removed the date closed
                $userSession = (new UserSessionFactory())->create();
                DatixDBQuery::PDO_query(
                    '
                        INSERT INTO full_audit (aud_module, aud_record, aud_login, aud_date, aud_action, aud_detail, aud_delegate_id)
                        VALUES (:aud_module, :aud_record, :aud_login, :aud_date, :aud_action, :aud_detail, :aud_delegate_id)
                    ',
                    [
                        'aud_module' => 'COM',
                        'aud_record' => $com['recordid'],
                        'aud_login' => $userSession->getCurrentUser()->initials,
                        'aud_date' => (new \DateTime())->format('Y-m-d H:i:s'),
                        'aud_action' => 'WEB:com_dclosed',
                        'aud_detail' => $com['com_dclosed'] ?? null,
                        'aud_delegate_id' => $userSession->getDelegator(),
                    ],
                );

                ReOpenComplaint([
                    'link_compl_recordid' => $row['link_compl_id'],
                    'recordid' => $row['con_id'],
                    'main_recordid' => $com['recordid'],
                    'data' => $com,
                    'module' => Module::FEEDBACK,
                ]);
            }

            saveReasonsForChange($linkComplId, $row, $com);
        }
    }
}

function saveHoldingDates(array $updateData, int $linkComplId)
{
    $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
    $repository = $entityManager->getRepository(HoldingLetterEntity::class);

    $holdingLetters = $repository->findBy(['linkComplId' => $linkComplId]);
    foreach ($holdingLetters as $holdingLetter) {
        $entityManager->remove($holdingLetter);
    }
    $entityManager->flush();

    $updatedReceivedDate = null;
    if (!empty($updateData['CHANGED-lcom_dreceived'])) {
        $sql = "SELECT TOP 1 lcom_ddueact FROM link_compl WHERE recordid = {$linkComplId}";
        $dateReceived = DatixDBQuery::PDO_fetch($sql);

        $updatedReceivedDate = $dateReceived['lcom_ddueact'];
    }

    $index = 0;
    while ($index < ComplaintController::MAX_NUMBER_HOLDING_LETTERS) {
        $holdingLetter = new HoldingLetterEntity();

        if (!empty($updateData["lcom_dduehold1_{$index}"])) {
            $date = $updateData["lcom_dduehold1_{$index}"];

            if ($updatedReceivedDate) {
                $date = CalculateHoldingDate($index, $updatedReceivedDate);
            }

            $date = ConvertHoldingDateFormat($date);
            $holdingLetter->setDue($date);
        }

        if (!empty($updateData["lcom_dhold1_{$index}"])) {
            $holdingLetter->setDone($updateData["lcom_dhold1_{$index}"]);
        }

        if (!empty($updateData["lcom_dduehold1_{$index}"]) || !empty($updateData["lcom_dhold1_{$index}"])) {
            $holdingLetter->setLinkComplId($linkComplId);
            $entityManager->persist($holdingLetter);
        }

        ++$index;
    }

    $entityManager->flush();
}

function CalculateHoldingDate(int $index, string $dreceived): string
{
    $registry = Container::get(Registry::class);
    $dateTracker = new DateTime($dreceived);

    $isWorkingDays = $registry->getParm('COM_COMPLAINT_CHAIN_TIMESCALES', 'W')->toScalar() === 'W';
    $numDays = (int) $registry->getParm('COMPL_HOLDDAYS_1', 2)->toScalar();

    $numDays *= ($index + 1);

    if ($isWorkingDays) {
        $numDays = CalculateWorkingDays($numDays, $dateTracker);
    }

    $dateTracker->modify('+' . $numDays . ' day');

    return $dateTracker->format('Y-m-d');
}

function ConvertHoldingDateFormat(string $date): string
{
    return date('Y-m-d', strtotime($date));
}

/**
 * @desc Retrieves any data for primary complainant dates shown on main complaints form.
 *
 * @param int $comId ID for complaint to retrieve primary comaplainant details for
 * @param bool $skipPrimaryContactCheck To skip the contact primary check
 */
function GetPrimaryComplLinkData(int $comId, bool $skipPrimaryContactCheck = false): array
{
    $sql = "SELECT recordid AS link_compl_id,
            lcom_primary,
            lcom_ddueack,
            lcom_ddueact,
            lcom_ddueresp,
            lcom_dduehold,
            lcom_dduerepl,
            lcom_iscomplpat,
            lcom_dreceived,
            lcom_dack,
            lcom_dactioned,
            lcom_dresponse,
            lcom_dholding,
            lcom_dreplied,
            due,
            done
            FROM link_compl
            LEFT JOIN holding_letters
            ON recordid = link_compl_id
            WHERE com_id = :comId AND lcom_current = 'Y' ";

    if (!$skipPrimaryContactCheck) {
        $sql .= "AND lcom_primary = 'Y'";
    }

    $data = DatixDBQuery::PDO_fetch_all($sql, ['comId' => $comId]);

    if (!$data) {
        return [];
    }

    return LinkContactModelTransformer::transform($data);
}

function getComplLinkData(int $conId): array
{
    $sql = 'SELECT recordid AS link_compl_id,
            lcom_primary,
            lcom_ddueack,
            lcom_ddueact,
            lcom_ddueresp,
            lcom_dduehold,
            lcom_dduerepl,
            lcom_iscomplpat,
            lcom_dreceived,
            lcom_dack,
            lcom_dactioned,
            lcom_dresponse,
            lcom_dholding,
            lcom_dreplied,
            due,
            done
            FROM link_compl
            LEFT JOIN holding_letters
            ON recordid = link_compl_id
            WHERE con_id = :conId';

    $data = DatixDBQuery::PDO_fetch_all($sql, ['conId' => $conId]);

    if (!$data) {
        return [];
    }

    return LinkContactModelTransformer::transform($data);
}

/**
 * @desc Calculates and updates the HCC date fields
 *
 * @param array $com all $_POST data for the complaint
 */
function SetHCCDates($com)
{
    if (CheckCalculateHCCDates(['data' => $_POST])) {
        $CalculatedDates = CalculateHCCDates(['data' => $com, 'rev_drequested' => ($_POST['rev_drequested'] ?: GetTodaysDate()), 'complaint_recordid' => $aParams['main_recordid'] ?? null]);
    }

    $DateFields = ['rev_ddecisiondue', 'rev_dhctermsdue', 'rev_dtermsdue', 'rev_dcompleteddue'];

    $com_id = $com['recordid'];

    $UpdateSetLink = GeneratePDOSQLFromArrays(
        [
            'FieldArray' => $DateFields,
            'DataArray' => $CalculatedDates ?? null,
            'Module' => 'COM',
        ],
        $PDOParamsArray,
    );

    if ($UpdateSetLink) {
        $sql = "UPDATE rev_main
                SET {$UpdateSetLink}
                WHERE link_id = {$com_id} and link_module = 'COM'";

        DatixDBQuery::PDO_query($sql, $PDOParamsArray);
    }
}

/**
 * @desc Check whether HCC dates need to be updated
 *
 * @param mixed array $aParams $_POST datafrom form submitted
 */
function CheckCalculateHCCDates($aParams)
{
    $ComplDoneArray = ['rev_drequested', 'rev_ddecisiondone', 'rev_dhctermsdone', 'rev_dtermsdone'];
    foreach ($ComplDoneArray as $field) {
        if (!empty($aParams['data']['CHANGED-' . $field]) && ($aParams['data'][$field] !== '' && $aParams['data'][$field] !== null)) {
            return true;
        }
    }

    return false;
}

/**
 * @desc Calculates the dates for each HCC due date including working days only
 *
 * @param mixed array $aParams $_POST datafrom form submitted
 *
 * @return date array $dates Array of date objects for each HCC due date
 */
function CalculateHCCDates($aParams)
{
    $ComplVars = [
        'rev_ddecisiondue' => ['default_parm' => 'REV_DECISION_TIME', 'default_val' => 20, 'from_date_field' => 'rev_drequested'],
        'rev_dhctermsdue' => ['default_parm' => 'REV_TERMS_TIME', 'default_val' => 10, 'from_date_field' => 'rev_ddecisiondone'],
        'rev_dtermsdue' => ['default_parm' => 'REV_COMMENTS_TIME', 'default_val' => 10, 'from_date_field' => 'rev_dhctermsdone'],
        'rev_dcompleteddue' => ['default_parm' => 'REV_INV_TIME', 'default_val' => 180, 'from_date_field' => 'rev_dtermsdone'],
    ];

    foreach ($ComplVars as $key => $Details) {
        $days[$key] = GetParm($Details['default_parm'], $Details['default_val']);
    }

    $dates = [];

    foreach ($days as $type => $numdays) {
        $from_date_field = $ComplVars[$type]['from_date_field'];
        if (isset($aParams['data'][$from_date_field])) {
            $DateTracker = new DateTime($aParams['data'][$from_date_field]);
            $numdays = CalculateWorkingDays($numdays, $DateTracker);
            $DateTracker->modify('+' . $numdays . ' day');
            $key = $type . ($aParams['suffix'] ? '_' . $aParams['suffix'] : '');
            $dates[$key] = $DateTracker->format('Y-m-d');
        }
    }

    return $dates;
}

function SaveComplaintDates($aParams)
{
    global $ModuleDefs;

    $registry = Container::get(Registry::class);
    $complaintDateFields = GetComplaintDateFields();

    $aDateValues = CheckDatesFromArray(AddSuffixToFields($complaintDateFields, $aParams['suffixstring'] ?? ''), $_POST);
    $aParams['data'] = array_merge($aParams['data'], $aDateValues);

    if ($aParams['recordid'] && $aParams['main_recordid']) {
        $sql = 'SELECT * from link_compl WHERE con_id = :recordid AND ' . $ModuleDefs[$aParams['module']]['FK'] . ' = :main_recordid AND lcom_current = :lcom_current';

        $row = DatixDBQuery::PDO_fetch($sql, ['recordid' => $aParams['recordid'], 'main_recordid' => $aParams['main_recordid'], 'lcom_current' => 'Y']);

        $link_compl_id = $row['recordid'] ?? null;

        if (!$row) {
            if (!$link_compl_id) {
                $recordIdGenerator = (new RecordIdGeneratorFactory())->create('link_compl');
                $link_compl_id = $recordIdGenerator->generateRecordId();
            }

            /** @var FeedbackRepository $feedbackRepository */
            $feedbackRepository = Container::get(FeedbackRepository::class);
            $feedbackRepository->insertIntoLinkCompl($link_compl_id, $aParams['recordid'], $aParams['main_recordid'], 'Y');
            $NewComplainant = true;
        }

        if (CheckCalculateComplaintDates(['data' => $_POST]) || !empty($NewComplainant)) {
            if ($registry->getParm('COM_AUTO_DRECEIVED_LCOM_DRECEIVED', 'N')->isTrue() && $_POST['show_field_lcom_dreceived_' . ($aParams['suffixstring'] ?? null)] == 0) {
                if (!empty($_POST['lcom_dreceived'])) {
                    $_POST['lcom_dreceived'] = $aParams['data']['com_dreceived'];
                } else {
                    $aParams['data']['lcom_dreceived' . $aParams['suffixstring']] = $aParams['data']['com_dreceived'];
                }
            }
            $CalculatedDates = CalculateComplaintDates(['data' => $aParams['data'],
                'date_received' => ($_POST['lcom_dreceived'] ?? null ?: $aParams['data']['lcom_dreceived' . $aParams['suffixstring']] ?? null),
                'complaint_recordid' => $aParams['main_recordid'], 'suffix' => $aParams['suffix'] ?? null, ]);
            $aParams['data'] = array_merge($aParams['data'], $CalculatedDates);
        }

        $DateFields = GetComplaintDateFields();
        $DateFields[] = 'lcom_primary'; // non-date fields also stored here.
        $DateFields[] = 'lcom_iscomplpat';

        $UpdateData = $aParams['data'];

        $reopenedDate = $_POST['lcom_dreopened' . ($aParams['suffixstring'] ?? null)] ?? null;
        $reopen = $_POST['lcom_reopen' . ($aParams['suffixstring'] ?? null)] ?? 'N';

        if (!$reopenedDate && $reopen == 'Y') {
            // Use today's date if we're reopening but haven't explicitly specified the reopen date
            $reopenedDate = (new DateTime())->format('Y-m-d');
        }
        saveHoldingDates($UpdateData, $link_compl_id);
        if ($reopenedDate) { // if this has been reopened, the dates being saved are no longer current.
            $DateFields[] = 'lcom_current';
            $UpdateData['lcom_current'] = 'N';
            $UpdateData['lcom_primary'] = 'N';
            $UpdateData['lcom_dreopened'] = $reopenedDate;
        }

        if ($registry->getParm('SPSC_DATASET', 'N')->toBool()) {
            $DateFields[] = 'lcom_reopen';
            $DateFields[] = 'lcom_reason_for_reopen';
        }

        $UpdateSetLink = GeneratePDOSQLFromArrays(
            [
                'FieldArray' => $DateFields,
                'DataArray' => $UpdateData,
                'Module' => 'COM',
                'Suffix' => $aParams['suffix'] ?? '',
            ],
            $PDOParamsArray,
        );

        if ($UpdateSetLink) {
            $sql = "UPDATE link_compl
                SET {$UpdateSetLink}
                WHERE recordid = {$link_compl_id}";

            DatixDBQuery::PDO_query($sql, $PDOParamsArray);

            // need to determine if this is the primary complainant in cases where this field is hidden on the form design
            $aParams['data']['lcom_primary'] = $_POST['lcom_primary'] ?? $row['lcom_primary'] ?? null;

            if ($aParams['data']['lcom_primary'] == 'Y') {
                EnsureUniquePrimary($aParams);
            }
        }

        if ($reopenedDate) { // if this has been reopened, we need to re-calculate the dates and save a new link_compl record.
            $aParams['link_compl_recordid'] = $link_compl_id;
            $aParams['data']['lcom_dreopened' . $aParams['suffixstring']] = $reopenedDate;
            // If the chain has been reopened, the record should be moved back into the main chain status flow, which means it cannot be marked as closed.
            DatixDBQuery::PDO_query('UPDATE compl_main SET com_dclosed = null WHERE recordid = :recordid', ['recordid' => $aParams['main_recordid']]);
            ReOpenComplaint($aParams);
        }

        if ($registry->getParm('COM_AUTO_DRECEIVED_LCOM_DRECEIVED', 'N')->isTrue()) {
            if (bYN($aParams['data']['lcom_primary']) && !empty($aParams['data']['CHANGED-lcom_dreceived'])) {
                DatixDBQuery::PDO_query(<<<'SQL'

                    UPDATE compl_main
                    SET
                        com_dreceived = :date_received
                    WHERE
                        com_dreceived IS NULL
                        AND
                        recordid = :recordid
                    SQL
                    , [
                        ':recordid' => $aParams['main_recordid'],
                        ':date_received' => $aParams['data']['lcom_dreceived'],
                    ]);
            }
        }

        if (empty($NewComplainant)) {
            saveReasonsForChange($link_compl_id, $row, $aParams['data']);
        }
    }
}

/**
 * @param $aParams
 *
 * @throws DatixDBQueryException
 * @throws Exception
 * @throws InvalidDataException
 *
 * Creates a new row in the link_compl table to hold the new up to date complainant link. The old row is archived and
 * all of the dates are recalculated from the reopened date.
 */
function ReOpenComplaint($aParams)
{
    // TODO: Probably all of the sql here could be contained in a single statement, but it's a bit risky, so we can wait until it is refactored.

    if ($aParams['recordid'] && $aParams['main_recordid']) {
        $dateFields = GetComplaintDateFields();

        foreach ($dateFields as $DateField) {
            if ($DateField != 'lcom_dreopened') {
                $aParams['data'][$DateField . ($aParams['suffixstring'] ?? null)] = null;
            }
        }

        $recordIdGenerator = (new RecordIdGeneratorFactory())->create('link_compl');
        $linkComplId = $recordIdGenerator->generateRecordId();

        \DatixDBQuery::PDO_build_and_insert(
            'link_compl',
            [
                'recordid' => $linkComplId,
                'con_id' => $aParams['recordid'],
                'com_id' => $aParams['main_recordid'],
                'lcom_current' => 'Y',
                'lcom_primary' => $aParams['data']['lcom_primary'],
            ],
        );

        $aParams['data']['lcom_dreceived' . $aParams['suffixstring']] = UserDateToSQLDate($aParams['data']['lcom_dreopened' . $aParams['suffixstring']]);

        $calculatedDates = CalculateComplaintDates(['data' => $aParams['data'], 'date_received' => $aParams['data']['lcom_dreceived' . $aParams['suffixstring']], 'complaint_recordid' => $aParams['main_recordid'], 'suffix' => $aParams['suffix'] ?? null]);
        $aParams['data'] = array_merge($aParams['data'], $calculatedDates);

        // New record should not have the re-opened date filled in.
        $aParams['data']['lcom_dreopened'] = null;

        // pull across lcom_iscomplpat from the linked record, since it may not be posted from the form.
        $sql = 'UPDATE link_compl
            SET
                link_compl.lcom_iscomplpat = old_record.lcom_iscomplpat,
                link_compl.lcom_dreceived = :date_received
            FROM
                (
                SELECT lcom_iscomplpat
                FROM link_compl WHERE recordid = :old_id
                ) old_record
            WHERE recordid = :new_id';

        $pdoParamsArray['old_id'] = $aParams['link_compl_recordid'];
        $pdoParamsArray['new_id'] = $linkComplId;
        $pdoParamsArray['date_received'] = $aParams['data']['lcom_dreceived' . ($aParams['suffixstring'] ?? null)];

        DatixDBQuery::PDO_query($sql, $pdoParamsArray);

        $pdoParamsArray = [];

        // Add the recalculated dates.
        $updateSetLink = GeneratePDOSQLFromArrays(
            [
                'FieldArray' => $dateFields,
                'DataArray' => $calculatedDates,
                'Module' => 'COM',
                'Suffix' => $aParams['suffix'] ?? null,
            ],
            $pdoParamsArray,
        );

        $sql = "UPDATE link_compl
                SET {$updateSetLink}
                WHERE recordid = :complaint_id";

        $pdoParamsArray['complaint_id'] = $linkComplId;

        DatixDBQuery::PDO_query($sql, $pdoParamsArray);

        if ($_POST['lcom_primary'] == 'Y') {
            EnsureUniquePrimary($aParams);
        }

        // Update the previous feedback chain with details of the reopen user/date
        $userSession = (new UserSessionFactory())->create();
        $sql = '
            UPDATE
                link_compl
            SET
                reopen_user_id = :reopen_user_id,
                reopen_delegator_id = :reopen_delegator_id,
                reopen_date = :reopen_date
            WHERE
                recordid = :recordid';

        DatixDBQuery::PDO_query($sql, [
            'reopen_user_id' => $userSession->getUserId(),
            'reopen_delegator_id' => $userSession->getDelegator(),
            'reopen_date' => date('Y-m-d H:i:s'),
            'recordid' => $aParams['link_compl_recordid'],
        ]);
    }
}

function CalculateComplaintDates($aParams): array
{
    $registry = Container::get(Registry::class);
    $complVars = [];
    $dates = [];

    if ($registry->getParm('SHOW_ACKNOWLEDGED', 'Y')->isTrue()) {
        $complVars['lcom_ddueack'] = ['default_parm' => 'COMPL_ACKDAYS', 'default_val' => 2, 'type_prefix' => 'DACK_'];
    }

    if ($registry->getParm('SHOW_ACTIONED', 'Y')->isTrue()) {
        $complVars['lcom_ddueact'] = ['default_parm' => 'COMPL_ACTDAYS', 'default_val' => 1, 'type_prefix' => 'DACT_'];
    }

    if ($registry->getParm('SHOW_RESPONSE', 'Y')->isTrue()) {
        $complVars['lcom_ddueresp'] = ['default_parm' => 'COMPL_RESPDAYS', 'default_val' => 10, 'type_prefix' => 'DRSP_'];
    }

    if ($registry->getParm('SHOW_HOLDING', 'Y')->isTrue()) {
        $complVars['lcom_dduehold'] = ['default_parm' => 'COMPL_HOLDDAYS', 'default_val' => 2, 'type_prefix' => 'DHLD_'];
    }

    if ($registry->getParm('SHOW_REPLIED', 'Y')->isTrue()) {
        $complVars['lcom_dduerepl'] = ['default_parm' => 'COMPL_REPLDAYS', 'default_val' => 5, 'type_prefix' => 'DRPL_'];
    }

    if (empty($complVars)) {
        return $dates;
    }

    $sql = 'SELECT com_type, com_subtype FROM compl_main WHERE recordid = :recordid';
    $row = DatixDBQuery::PDO_fetch($sql, ['recordid' => $aParams['complaint_recordid']]);

    $typeDaysSet = false;

    if ($row['com_type'] && $row['com_subtype']) {
        foreach ($complVars as $key => $details) {
            $global = $details['type_prefix'] . $row['com_type'] . '_' . $row['com_subtype'];
            $days[$key] = $registry->getParm($global, '-1')->toScalar();

            if ($days[$key] != -1) {
                $typeDaysSet = true;
            }
        }
    }

    if ($row['com_subtype'] && !$typeDaysSet) {
        foreach ($complVars as $key => $details) {
            $global = $details['type_prefix'] . '*_' . $row['com_subtype'];
            $days[$key] = $days[$key] = $registry->getParm($global, '-1')->toScalar();


            if ($days[$key] != -1) {
                $typeDaysSet = true;
            }
        }
    }

    if ($row['com_type'] && !$typeDaysSet) {
        foreach ($complVars as $key => $details) {
            $global = $details['type_prefix'] . $row['com_type'] . '_*';
            $days[$key] = $days[$key] = $registry->getParm($global, '-1')->toScalar();

            if ($days[$key] != -1) {
                $typeDaysSet = true;
            }
        }
    }

    if (!$typeDaysSet) {
        foreach ($complVars as $key => $details) {
            $days[$key] = $registry->getParm($details['default_parm'], $details['default_val'])->toScalar();
        }
    }

    $hasDateRecieved = $aParams['date_received'] != null
        || $aParams['data'][!empty($aParams['suffix']) ?
            'lcom_dreceived' . '_' . $aParams['suffix'] :
            'lcom_dreceived'
        ];

    if ($hasDateRecieved !== null) {
        $shouldUpdateDateRecieved = $aParams['date_received'] === null
            && $aParams['data'][!empty($aParams['suffix']) ?
                'lcom_dreceived' . '_' . $aParams['suffix'] :
                'lcom_dreceived'] !==
            null;

        if ($shouldUpdateDateRecieved) {
            $aParams['date_received'] =
                $aParams['data'][!empty($aParams['suffix']) ?
                    'lcom_dreceived' . '_' . $aParams['suffix'] :
                    'lcom_dreceived'
                ];
        }

        $dateTracker = new DateTime(UserDateToSQLDate($aParams['date_received']));

        foreach ($days as $type => $numdays) {
            if ($registry->getParm('COM_COMPLAINT_CHAIN_TIMESCALES', 'W')->toScalar() === 'W') {
                $numdays = CalculateWorkingDays($numdays, $dateTracker);
            }

            $dateTracker->modify('+' . $numdays . ' day');
            $key = $type . (!empty($aParams['suffix']) ? '_' . $aParams['suffix'] : '');
            $dates[$key] = $dateTracker->format('Y-m-d 00:00:00.000');
        }
    } else {
        foreach ($days as $type => $numdays) {
            $key = $type . ($aParams['suffix'] ? '_' . $aParams['suffix'] : '');
            $dates[$key] = null;
        }
    }

    return $dates;
}

function ShowComplaintDatesHistory($com_id, $con_id = '')
{
    if (!$con_id) { // use primary complainant
        $con_id = DatixDBQuery::PDO_fetch('SELECT con_id FROM link_compl WHERE lcom_primary = :lcom_primary AND com_id = :com_id', ['lcom_primary' => 'Y', 'com_id' => $com_id], PDO::FETCH_COLUMN);
    }

    $NumRecords = DatixDBQuery::PDO_fetch('SELECT COUNT(*) FROM link_compl WHERE con_id = :con_id AND com_id = :com_id AND lcom_current = :lcom_current', ['con_id' => $con_id, 'com_id' => $com_id, 'lcom_current' => 'N'], PDO::FETCH_COLUMN);

    return $NumRecords > 0;
}

function CheckCalculateComplaintDates($aParams)
{
    if (empty($aParams['data']['CHANGED-lcom_dreceived'])) {
        return false;
    }

    $ComplDoneArray = ['lcom_dack', 'lcom_dactioned', 'lcom_dresponse', 'lcom_dholding', 'lcom_dreplied', 'lcom_dhold1'];

    foreach ($ComplDoneArray as $field) {
        if (($aParams['data'][$field] ?? null) !== '' && $aParams['data'][$field] !== null) {
            return false;
        }
    }

    return true;
}

/**
 * Retrieves the data for the lcom_last_dreopened field.
 *
 * @param int $com_id The complaint recordid
 *
 * @return string The most recent reopened date for the primary complainaint
 */
function getLastReopenedDate($com_id)
{
    // get primary complainant id
    $con_id = DatixDBQuery::PDO_fetch('SELECT con_id FROM link_compl WHERE lcom_primary = :lcom_primary AND com_id = :com_id', ['lcom_primary' => 'Y', 'com_id' => $com_id], PDO::FETCH_COLUMN);

    // return most recent reopened date for this contact
    return DatixDBQuery::PDO_fetch('SELECT lcom_dreopened FROM link_compl WHERE con_id = :con_id AND com_id = :com_id AND lcom_current = :lcom_current ORDER BY lcom_dreopened DESC', ['con_id' => $con_id, 'com_id' => $com_id, 'lcom_current' => 'N'], PDO::FETCH_COLUMN);
}

/**
 * Used to simplify the SQL statements defined for the complaints statuses by providing a shortcut for a piece of SQL determining
 * that a particular status is part of the chain and "expected" (i.e has a due date).
 *
 * @psalm-import-type COM_STATUSES from FeedbackWorkflowInterface
 *
 * @psalm-param COM_STATUSES $COMStatuses
 *
 * @return string The SQL segment representing this logic
 */
function StatusValid(string $status, array $COMStatuses): string
{
    $registry = Container::get(Registry::class);
    $comStatus = $COMStatuses[$status];
    $isGlobal = $registry->getParm($comStatus['global'], 'Y')->isTrue();

    return 'NOT(' . ($isGlobal ? '1=2' : '1=1') . ' OR ' . $comStatus['due'] . ' IS NULL)';
}

/**
 * Used to simplify the SQL statements defined for the complaints statuses by providing a shortcut for a piece of SQL determining
 * that a particular status is not part of the chain (due to global settings) or is not "expected" (i.e has no due date).
 *
 * @param string $status The status to calculate the sql for
 *
 * @psalm-import-type COM_STATUSES from FeedbackWorkflowInterface
 *
 * @psalm-param COM_STATUSES $COMStatuses
 *
 * @return string The SQL segment representing this logic
 */
function StatusNotValid(string $status, array $COMStatuses): string
{
    $registry = Container::get(Registry::class);
    $comStatus = $COMStatuses[$status];
    $isGlobal = $registry->getParm($comStatus['global'], 'Y')->isTrue();

    return '(' . ($isGlobal ? '1=2' : '1=1') . ' OR ' . $comStatus['due'] . ' IS NULL)';
}

function autoFillCloseDateFromComplainantChainData($data)
{
    $registry = Container::get(Registry::class);
    $globalIsSet = $registry->getParm('COM_AUTO_CLOSE_REPLIED', 'N')->isTrue();
    $approvalChangeGlobal = $registry->getParm('COM_AUTO_CLOSE_APPROVAL_STATUS', '')->toScalar();
    $recordStatusIsApproved = $data['rep_approved'] === 'FA';
    $recordHasClosedDate = isset($data['com_dclosed']);
    $recordHasRepliedDate = isset($data['lcom_dreplied']);
    $repliedDateChanged = ($data['CHANGED-lcom_dreplied'] ?? null) == 1;

    if ($globalIsSet && $recordHasRepliedDate && $repliedDateChanged && !$recordHasClosedDate) {
        $data['com_dclosed'] = $data['lcom_dreplied'];
        if (!empty($approvalChangeGlobal) && $recordStatusIsApproved) {
            $data['rep_approved'] = $approvalChangeGlobal;
        }
    }

    return $data;
}

function SetFirstReceivedDateFromComplaintChainReceivedIfEmpty(array $data): array
{
    if (Container::get(Registry::class)->getParm('COM_AUTO_DRECEIVED_LCOM_DRECEIVED', 'N')->isFalse()) {
        return $data;
    }

    if (!empty($data['com_dreceived'])) {
        return $data;
    }

    if (!empty($data['lcom_dreceived'])) {
        $data['com_dreceived'] = $data['lcom_dreceived'];
    }

    return $data;
}

function SetComplaintChainDateReceivedFromFirstReceivedDateIfEmpty($data)
{
    if (Container::get(Registry::class)->getParm('COM_AUTO_DRECEIVED_LCOM_DRECEIVED', 'N')->isFalse()) {
        return;
    }

    if (empty($data['CHANGED-com_dreceived'])) {
        return;
    }

    // Update any primary contact with no lcom_dreceived
    \DatixDBQuery::PDO_query(<<<'SQL'
        UPDATE link_compl
        SET
            lcom_dreceived = :date_received
        WHERE
            lcom_primary = 'Y'
            AND
            com_id = :record_id
            AND
            lcom_dreceived IS NULL
        SQL
        , [
            ':date_received' => $data['com_dreceived'],
            ':record_id' => $data['recordid'],
        ]);
}

/**
 * Awful hard-coded function that can be replaced when we have time with something generic that
 * works for all modules.
 *
 * If configured, sends data to external urls for SPSC web portal and SPSC patient app.
 */
function SendDataToExternalURLs($data)
{
    $registry = Container::get(Registry::class);

    $logger = (new ContainerFactory())->create()['logger'];

    $complainantChainStatus = '';

    $webPortalUrl = $registry->getParm('SPSC_WEB_PORTAL_API_URL', '')->toScalar();
    $patientAppUrl = $registry->getParm('SPSC_PATIENT_APP_API_URL', '')->toScalar();

    if (!empty($webPortalUrl) || !empty($patientAppUrl)) {
        // First need to define which complainant chain status this record now sits in:
        foreach ($registry->getModuleDefs()['COM']['HARD_CODED_LISTINGS'] as $statusDetails) {
            if ($statusDetails['Where'] && !$complainantChainStatus) {
                $found = \DatixDBQuery::PDO_fetch(
                    'SELECT count(*) FROM compl_main WHERE recordid = :recordid AND (' . $statusDetails['Where'] . ')',
                    ['recordid' => $data['recordid']],
                    \PDO::FETCH_COLUMN,
                );

                if ($found > 0) {
                    $complainantChainStatus = $statusDetails['Title'];
                }
            }
        }
        $logger->info('Complainant chain status for feedback record ' . $data['recordid'] . ' calculated to be "' . $complainantChainStatus . '".');

        $credentials = \DatixDBQuery::PDO_fetch('SELECT username, [password] FROM external_basic_auth_config WHERE [type] = \'SPSC_WEB_PORTAL\'');
        $webPortalUserName = $credentials['username'];

        $webPortalSecret = \DatixDBQuery::PDO_fetch("SELECT secret FROM secrets WHERE feature = 'SPSC_WEB_PORTAL'", [], PDO::FETCH_COLUMN);

        $securedEncryptionService = (new SecuredEncryptionService());
        $webPortalPassword = $securedEncryptionService->secured_decrypt($credentials['password'], '', $webPortalSecret);

        if (!empty($webPortalUrl)) {
            try {
                $config = [
                    RequestOptions::JSON => getJson($data, $complainantChainStatus),
                ];

                if ($webPortalUserName && $webPortalPassword) {
                    $config[RequestOptions::AUTH] = [$webPortalUserName, $webPortalPassword];
                }

                $client = new Client();
                $response = $client->request('PUT', $webPortalUrl, $config);

                if ($response->getStatusCode() != '200') {
                    $logger->error('Submission to SPSC Web Portal API failed.', [
                        'status' => $response->getStatusCode(),
                    ]);
                } else {
                    $logger->info('Submission to SPSC Web Portal API for record ' . $data['recordid'] . ' successful.');
                }
            } catch (\Exception $e) {
                $logger->error('Submission to SPSC Web Portal API failed', [
                    'exception' => $e,
                ]);
            }
        }


        $credentials = \DatixDBQuery::PDO_fetch('SELECT username, [password] FROM external_basic_auth_config WHERE [type] = \'SPSC_PATIENT_APP\'');
        $patientAppUserName = $credentials['username'];

        $patientAppSecret = \DatixDBQuery::PDO_fetch("SELECT secret FROM secrets WHERE feature = 'SPSC_PATIENT_APP'", [], PDO::FETCH_COLUMN);

        $securedEncryptionService = (new SecuredEncryptionService());
        $patientAppPassword = $securedEncryptionService->secured_decrypt($credentials['password'], '', $patientAppSecret);

        if (!empty($patientAppUrl)) {
            try {
                $config = [
                    RequestOptions::JSON => getJson($data, $complainantChainStatus),
                ];

                if ($patientAppUserName && $patientAppPassword) {
                    $config[RequestOptions::AUTH] = [$patientAppUserName, $patientAppPassword];
                }

                $client = new Client();
                $response = $client->request('PUT', $patientAppUrl, $config);

                if ($response->getStatusCode() != '200') {
                    $logger->error('Submission to SPSC Web Portal API failed.', [
                        'status' => $response->getStatusCode(),
                    ]);
                } else {
                    $logger->info('Submission to SPSC Web Portal API for record ' . $data['recordid'] . ' successful.');
                }
            } catch (\Exception $e) {
                $logger->error('Submission to SPSC Patient App failed', [
                    'exception' => $e,
                ]);
            }
        }
    }
}

function getJson(array $data, ?string $complainantChainStatus): array
{
    $json = [
        'id' => $data['recordid'],
        'status' => $complainantChainStatus,
    ];

    if (isset($data['com_summary'])) {
        $json['outcome_summary'] = $data['com_summary'];
    }

    if (isset($data['com_dclosed'])) {
        $json['closed_date'] = $data['com_dclosed'];
    }

    if (isset($data['service_id'])) {
        $json['service_id'] = $data['service_id'];
    }

    if (isset($data['location_id'])) {
        $json['location_id'] = $data['location_id'];
    }

    if (isset($data['com_inv_lessons'])) {
        $json['lesson_learned'] = $data['com_inv_lessons'];
    }

    if (isset($data['com_lesson_learned_sub_category'])) {
        $json['lesson_learned_sub_category'] = explode(' ', $data['com_lesson_learned_sub_category']);
    }

    return $json;
}

/**
 * Handles the persistence of feedback chain due date reasons for change.
 */
function saveReasonsForChange(int $feedbackChainId, array $previousChainData, array $requestData): void
{
    $registry = Container::get(Registry::class);
    if ($registry->getParm('FEEDBACK_CHAIN_REASON_FOR_CHANGE', 'N')->isFalse()) {
        return;
    }

    $dueDateFields = array_filter(GetComplaintDateFields(), static function ($fieldName) {
        return substr($fieldName, 0, 9) === 'lcom_ddue';
    });

    $userSession = (new UserSessionFactory())->create();

    foreach ($dueDateFields as $dueDateField) {
        if (($requestData["CHANGED-{$dueDateField}"] ?? null) === '1') {
            DatixDBQuery::PDO_build_and_insert('feedback_chain_change_history', [
                'link_compl_id' => $feedbackChainId,
                'user_id' => $userSession->getUserId(),
                'delegator_id' => $userSession->getDelegator(),
                'updated' => date('Y-m-d H:i:s'),
                'field_name' => $dueDateField,
                'previous_value' => $previousChainData[$dueDateField],
                'reason_for_change' => $requestData["{$dueDateField}_reason_for_change"],
            ]);
        }
    }

    $index = 0;
    while ($index < ComplaintController::MAX_NUMBER_HOLDING_LETTERS) {
        if ($requestData['CHANGED-lcom_dduehold1_' . $index] === '1') {
            DatixDBQuery::PDO_build_and_insert('feedback_chain_change_history', [
                'link_compl_id' => $feedbackChainId,
                'user_id' => $userSession->getUserId(),
                'delegator_id' => $userSession->getDelegator(),
                'updated' => date('Y-m-d H:i:s'),
                'field_name' => 'lcom_dduehold1_' . $index,
                'previous_value' => $previousChainData['lcom_dduehold1'],
                'reason_for_change' => $requestData['lcom_dduehold1_' . $index . '_reason_for_change'],
            ]);
        }

        ++$index;
    }
}

function GenericSaveMedications(string $module, EntityRepository $repository, array $data)
{
    global $ModuleDefs;
    if (!$ModuleDefs[$module]['IS_MEDS_ENABLED_FOR_MODULE']) {
        return;
    }

    $record = $repository->find($data['recordid']);
    if (!$record instanceof HasUUIDEntity) {
        // No record/UUID, can't save meds without them
        return;
    }

    $formHelper = (new MedicationFormSectionHelperFactory())->create('Edit', 2, $module);
    $medicationData = $formHelper->extractMedicationData($data);

    $medicationService = MedicationsServiceFactory::create($module);
    $medicationService->process($record->getUUID(), $medicationData);
}
