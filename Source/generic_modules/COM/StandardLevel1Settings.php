<?php

use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSections;
use src\complaints\model\OmbudsmanSubjectFields;

$GLOBALS['FormTitle'][7] = _fdtk('com1_title');

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'location_id' => true,
    'service_id' => true,
    'csu_location_id' => true,
    'csu_service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
    'csu_location_id' => false,
    'csu_service_id' => false,
];


$GLOBALS['ExpandSections'] = [
    'show_person' => [
        [
            'section' => 'contacts_type_A',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'com_subjects_linked' => [
        [
            'section' => 'subject',
            'values' => [
                'Y',
            ],
        ],
    ],
    'com_issues_linked' => [
        [
            'section' => 'issue',
            'values' => [
                'Y',
            ],
        ],
    ],
    'show_document' => [
        [
            'section' => 'documents',
            'values' => [
                'Y',
            ],
        ],
    ],
    FeedbackFields::REFERRED_TO_OMBUDSMAN => [
        [
            'section' => FeedbackSections::OMBUDSMAN,
            'values' => [
                'Y',
            ],
        ],
        [
            'section' => FeedbackSections::OMBUDSMAN_INVESTIGATION_DETAILS,
            'values' => [
                'Y',
            ],
        ],
        [
            'section' => FeedbackSections::OMBUDSMAN_DRAFT_REPORT,
            'values' => [
                'Y',
            ],
        ],
        [
            'section' => FeedbackSections::OMBUDSMAN_FINAL_REPORT,
            'values' => [
                'Y',
            ],
        ],
        [
            'section' => FeedbackSections::OMBUDSMAN_SUBJECTS,
            'values' => [
                'Y',
            ],
        ],
    ],
];

$GLOBALS['HideFields'] = [
    'rep_approved' => true,
    'com_dopened' => true,
    'com_dclosed' => true,
    'com_dreopened' => true,
    'com_purchaser' => true,
    'com_otherref' => true,
    'ko41' => true,
    'dum_com_grading' => true,
    'com_consent' => true,
    'ccs2' => true,
    'subject_type' => true,
    'subject_subtype' => true,
    'com_service_area' => true,
    'csu_level_of_harm' => true,
    'requested_consultant' => true,
    'priority_scale' => true,
    'is_record_sensitive' => true,
    FeedbackFields::COM_SPECIALTY => true,
    FeedbackFields::MCA_OR_NA => true,
    FeedbackFields::COM_PAT_EXPECTATIONS => true,
    FeedbackFields::COM_PAT_UPDATE_PREF => true,
    FeedbackFields::REFERRED_TO_OMBUDSMAN => true,
    FeedbackFields::DATE_FIRST_CONTACT => true,
    FeedbackFields::DATE_EVIDENCE_DUE => true,
    FeedbackFields::DATE_EVIDENCE_SUBMITTED => true,
    FeedbackFields::OMBUDSMAN_REFERENCE => true,
    FeedbackFields::OMBUDSMAN_HANDLER => true,
    FeedbackFields::OMBUDSMAN_CURRENT_STAGE => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL => true,
    FeedbackSections::EARLY_SETTLEMENT => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED => true,
    FeedbackFields::DATE_INVESTIGATION_BEGAN => true,
    FeedbackFields::DATE_RESPONSE_DUE => true,
    FeedbackFields::DATE_DOC_INV_SUB => true,
    FeedbackFields::DATE_INV_SUB => true,
    FeedbackFields::DATE_DRAFT_RECEIVED => true,
    FeedbackFields::DATE_DRAFT_RESPONSE_DUE => true,
    FeedbackFields::DATE_REC_RECIEVED => true,
    FeedbackFields::DATE_ACTION_PLAN => true,
    FeedbackFields::DATE_DRAFT_REPORT_SUB => true,
    FeedbackFields::DATE_REPORT_RECEIVED => true,
    FeedbackFields::FINAL_REP_TYPE => true,
    FeedbackFields::OMBUDSMAN_OUTCOME => true,
    FeedbackFields::OMBUDSMAN_LEARNING => true,
    FeedbackSections::OMBUDSMAN_SUBJECTS => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBJECT => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUB_SUBJECT => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_NARRATIVE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_OUTCOME => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_RECOMMENDATION => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_DUE_DATE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBMITTED_DATE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_LOCATION_ID => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SERVICE_ID => true,
    FeedbackFields::COM_OUTCOME_GRADING => true,
    FeedbackFields::COM_SERIOUS_INCIDENT => true,
    FeedbackFields::COM_WELSH_LANGUAGE => true,
    FeedbackSections::OUTBREAK => true,
    FeedbackFields::OUTBREAK_IMPACT => true,
    FeedbackFields::OUTBREAK_TYPE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_RECOMMENDATIONS => true,
    OmbudsmanSubjectFields::OMBUDSMAN_LESSONS_LEARNED => true,
    FeedbackFields::COM_REDRESS_ESCALATED => true,
    FeedbackFields::COM_ISSUE_PATHWAY => true,
    FeedbackFields::COM_ISSUE_TYPE => true,
];

$GLOBALS['DefaultValues'] = [
    'com_dopened' => 'TODAY',
    'com_dreceived' => 'TODAY',
    'rep_approved' => 'UN',
];
