<?php

declare(strict_types=1);

namespace Source\generic_modules\COM;

class AppVarsHelper
{
    public const WORKFLOW_TWO_COMPLETED_STATUS_SQL = <<<'SQL'
        (compl_main.rep_approved = 'FAC'
        OR (
             compl_main.rep_approved IN ('UN','FA')
             AND (
                 (compl_main.com_dclosed IS NOT NULL AND compl_main.com_dclosed != '')
                 OR
                 exists (
                     SELECT 1
                     from LINK_COMPL
                     WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID
                       AND (lcom_dreplied IS NOT NULL)
                       AND LINK_COMPL.recordid =
                       (
                           SELECT top 1 LINK_COMPL.recordid
                           from LINK_COMPL
                           WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID
                             AND (LINK_COMPL.lcom_primary = 'Y' OR LINK_COMPL.LCOM_CURRENT = 'Y')
                           ORDER by lcom_primary desc, LINK_COMPL.recordid
                       )
                     )
                 )
            )
        )
        SQL;
}
