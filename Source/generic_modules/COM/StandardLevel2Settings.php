<?php

use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSections;
use src\complaints\model\OmbudsmanSubjectFields;
use src\email\AwsSesEmailLimits;
use src\medications\models\MedicationFields;
use src\medications\models\MedicationSections;

$GLOBALS['FormTitle'][7] = _fdtk('com2_title');

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'location_id' => true,
    'service_id' => true,
    'csu_location_id' => true,
    'csu_service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
    'csu_location_id' => false,
    'csu_service_id' => false,
];

$GLOBALS['ExpandSections'] = [
    'com_subjects_linked' => [
        0 => [
            'section' => 'subject',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'com_issues_linked' => [
        0 => [
            'section' => 'issue',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'rep_approved' => [
        0 => [
            'section' => 'rejection',
            'alerttext' => 'Please complete the \'Details of rejection\' section before saving this form.',
            'values' => [
                0 => 'REJECT',
            ],
        ],
    ],
    FeedbackFields::REFERRED_TO_OMBUDSMAN => [
        0 => [
            'section' => FeedbackSections::OMBUDSMAN,
            'values' => [
                0 => 'Y',
            ],
        ],
        1 => [
            'section' => FeedbackSections::OMBUDSMAN_INVESTIGATION_DETAILS,
            'values' => [
                0 => 'Y',
            ],
        ],
        2 => [
            'section' => FeedbackSections::OMBUDSMAN_DRAFT_REPORT,
            'values' => [
                0 => 'Y',
            ],
        ],
        3 => [
            'section' => FeedbackSections::OMBUDSMAN_FINAL_REPORT,
            'values' => [
                0 => 'Y',
            ],
        ],
        4 => [
            'section' => FeedbackSections::OMBUDSMAN_SUBJECTS,
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
];

$GLOBALS['DefaultValues'] = [
    'com_dopened' => 'TODAY',
    'com_dreceived' => 'TODAY',
];

$GLOBALS['HideFields'] = [
    'additional' => true,
    'ko41' => true,
    'independent_review' => true,
    'hcc' => true,
    'primary_compl_dates' => true,
    'primary_compl_dates_history' => true,
    'progress_notes' => true,
    'rejection_history' => true,
    'dum_com_grading' => true,
    'com_consent' => true,
    'lcom_last_dreopened' => true,
    'payments' => true,
    'com_dreopened' => true,
    'action_chains' => true,
    'com_inc_type' => true,
    'ccs2' => true,
    'com_last_updated' => true,
    'com_service_area' => true,
    'subject_type' => true,
    'subject_subtype' => true,
    'learning' => true,
    'csu_level_of_harm' => true,
    'requested_consultant' => true,
    'priority_scale' => true,
    'is_record_sensitive' => true,
    'flag_for_rib' => true,
    'time_taken_to_submit' => true,
    FeedbackFields::COM_LESSON_LEARNED_SUB_CATEGORY => true,
    FeedbackFields::COM_HRO_CHARACTERISTICS => true,
    FeedbackFields::COM_SPECIALTY => true,
    FeedbackFields::MCA_OR_NA => true,
    FeedbackFields::SOURCE_OF_RECORD => true,
    FeedbackFields::COM_PAT_EXPECTATIONS => true,
    FeedbackFields::COM_PAT_UPDATE_PREF => true,
    FeedbackFields::COM_GRADE_RATING => true,
    FeedbackFields::REFERRED_TO_OMBUDSMAN => true,
    FeedbackFields::DATE_FIRST_CONTACT => true,
    FeedbackFields::DATE_EVIDENCE_DUE => true,
    FeedbackFields::DATE_EVIDENCE_SUBMITTED => true,
    FeedbackFields::OMBUDSMAN_REFERENCE => true,
    FeedbackFields::OMBUDSMAN_HANDLER => true,
    FeedbackFields::OMBUDSMAN_CURRENT_STAGE => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL => true,
    FeedbackSections::EARLY_SETTLEMENT => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED => true,
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED => true,
    FeedbackFields::DATE_INVESTIGATION_BEGAN => true,
    FeedbackFields::DATE_RESPONSE_DUE => true,
    FeedbackFields::DATE_DOC_INV_SUB => true,
    FeedbackFields::DATE_INV_SUB => true,
    FeedbackFields::DATE_DRAFT_RECEIVED => true,
    FeedbackFields::DATE_DRAFT_RESPONSE_DUE => true,
    FeedbackFields::DATE_REC_RECIEVED => true,
    FeedbackFields::DATE_ACTION_PLAN => true,
    FeedbackFields::DATE_DRAFT_REPORT_SUB => true,
    FeedbackFields::DATE_REPORT_RECEIVED => true,
    FeedbackFields::FINAL_REP_TYPE => true,
    FeedbackFields::OMBUDSMAN_OUTCOME => true,
    FeedbackFields::OMBUDSMAN_LEARNING => true,
    FeedbackSections::OMBUDSMAN_SUBJECTS => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBJECT => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUB_SUBJECT => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_NARRATIVE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_OUTCOME => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_RECOMMENDATION => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_DUE_DATE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBMITTED_DATE => true,
    OmbudsmanSubjectFields::OMBUDSMAN_LOCATION_ID => true,
    OmbudsmanSubjectFields::OMBUDSMAN_SERVICE_ID => true,
    FeedbackFields::COM_OUTCOME_GRADING => true,
    FeedbackFields::COM_SERIOUS_INCIDENT => true,
    FeedbackFields::COM_WELSH_LANGUAGE => true,
    FeedbackSections::OUTBREAK => true,
    FeedbackFields::OUTBREAK_IMPACT => true,
    FeedbackFields::OUTBREAK_TYPE => true,
    'tasks' => true,
    OmbudsmanSubjectFields::OMBUDSMAN_RECOMMENDATIONS => true,
    OmbudsmanSubjectFields::OMBUDSMAN_LESSONS_LEARNED => true,
    FeedbackFields::COM_REDRESS_ESCALATED => true,
    FeedbackFields::COM_ISSUE_PATHWAY => true,
    FeedbackFields::COM_ISSUE_TYPE => true,
];

$GLOBALS['NewPanels'] = [
    'location' => true,
    'ko41' => true,
    'subject_header' => true,
    'contacts_type_C' => true,
    'feedback' => true,
    'investigation' => true,
    'notepad' => true,
    'independent_review' => true,
    'hcc' => true,
    'primary_compl_dates' => true,
    'progress_notes' => true,
    'linked_actions' => true,
    'documents' => true,
    'word' => true,
    'linked_records' => true,
    'payments' => true,
    'action_chains' => true,
    FeedbackSections::OMBUDSMAN => true,
    FeedbackSections::EARLY_SETTLEMENT => true,
];

$GLOBALS['UserExtraText'] = [
    'dum_fbk_to' => ['7' => 'Only staff and contacts with e-mail addresses are shown.'],
    'dum_fbk_gab' => ['7' => 'Only users with e-mail addresses are shown.'],
    'dum_fbk_email' => ['7' => 'Enter e-mail addresses of other recipients not listed above. You can<br />enter multiple addresses, separated by commas.'],
    'flag_for_investigation' => ['7' => 'Selecting yes will create a investigation from this record.'],
    'flag_for_rib' => ['7' => 'Selecting yes will submit this record for RIB review.'],
    'dum_fbk_attachments' => ['7' => sprintf(_fdtk('email_attachment_extra_text'), AwsSesEmailLimits::AWS_SES_MAIL_SIZE_MB_LIMIT)],
];

$GLOBALS['ExpandFields'] = [
    'learnings_to_share' => [
        0 => [
            'field' => 'key_learnings',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
        1 => [
            'field' => 'learnings_title',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
];

$GLOBALS['MandatoryFields'] = [
    'learnings_title' => 'learning',
    'key_learnings' => 'learning',
    MedicationFields::ADMINISTERED_MED_NAME => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_ROUTE => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_DOSE => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::ADMINISTERED_FORM => MedicationSections::ADMINISTERED_DRUG,
    MedicationFields::CORRECT_MED_NAME => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_ROUTE => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_DOSE => MedicationSections::CORRECT_DRUG,
    MedicationFields::CORRECT_FORM => MedicationSections::CORRECT_DRUG,
    MedicationFields::STAGE_OF_ERROR => MedicationSections::MEDICATIONS_OTHER_FIELDS,
    MedicationFields::TYPE_OF_ERROR => MedicationSections::MEDICATIONS_OTHER_FIELDS,
    MedicationFields::OTHER_IMPORTANT_FACTORS => MedicationSections::MEDICATIONS_OTHER_FIELDS,
];
