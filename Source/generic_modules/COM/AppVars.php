<?php

use app\framework\DoctrineEntityManagerFactory;
use app\models\contact\ContactTypes;
use app\models\feedback\entities\FeedbackEntity;
use app\models\framework\config\DatixConfigFactory;
use app\models\generic\valueObjects\Module;
use app\models\medication\entities\FeedbackMedicationLink;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\approvalStatus\ApprovalStatus;
use app\services\approvalStatus\ApprovalStatusAcronyms;
use app\services\approvalStatus\ApprovalStatusDescriptionRepositoryFactory;
use app\services\forms\RecordHeaderProvider;
use Source\generic_modules\COM\AppVarsHelper;
use Source\generic_modules\COM\workflowListings\AwaitingAcknowledgementWorkflowListing;
use Source\generic_modules\COM\workflowListings\AwaitingHoldingWorkflowListing;
use Source\generic_modules\COM\workflowListings\AwaitingInvestigationWorkflowListing;
use Source\generic_modules\COM\workflowListings\AwaitingReplyWorkflowListing;
use Source\generic_modules\COM\workflowListings\AwaitingResponseWorkflowListing;
use Source\generic_modules\COM\workflowListings\CompletedWorkflowListing;
use Source\generic_modules\COM\workflowListings\RejectedWorkflowListing;
use Source\generic_modules\COM\workflowListings\UnapprovedWorkflowListing;
use Source\generic_modules\COM\workflowListings\UnderInvestigationWorkflowListing;
use Source\generic_modules\ModuleDefKeys;
use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSections;
use src\complaints\model\OmbudsmanSubjectFields;
use src\framework\events\FormEventsHelper;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\tasks\models\TaskFields;
use src\tasks\models\TaskModelFactory;

require_once __DIR__ . '/ModuleFunctions.php';

$COMStatuses = [
    'ACK' => [
        'global' => 'SHOW_ACKNOWLEDGED',
        'due' => 'lcom_ddueack',
        'complete' => 'lcom_dack',
    ],
    'ACT' => [
        'global' => 'SHOW_ACTIONED',
        'due' => 'lcom_ddueact',
        'complete' => 'lcom_dactioned',
    ],

    'HD1' => [
        'global' => 'SHOW_HOLDING_1',
        'due' => 'lcom_dduehold1',
        'complete' => 'lcom_dhold1',
    ],

    'RSP' => [
        'global' => 'SHOW_RESPONSE',
        'due' => 'lcom_ddueresp',
        'complete' => 'lcom_dresponse',
    ],
    'HLD' => [
        'global' => 'SHOW_HOLDING',
        'due' => 'lcom_dduehold',
        'complete' => 'lcom_dholding',
    ],
    'REP' => [
        'global' => 'SHOW_REPLIED',
        'due' => 'lcom_dduerepl',
        'complete' => 'lcom_dreplied',
    ],
];

// Get the WHERE Clause for the last stage available.
if ($registry->getParm('SHOW_REPLIED', 'Y')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dreplied IS NOT NULL OR lcom_dduerepl IS NULL)';
} elseif ($registry->getParm('SHOW_HOLDING', 'Y')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dholding IS NOT NULL OR lcom_dduehold IS NULL)';
} elseif ($registry->getParm('SHOW_RESPONSE', 'Y')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dresponse IS NOT NULL OR lcom_ddueresp IS NULL)';
} elseif ($registry->getParm('SHOW_HOLDING_1', 'N')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dhold1 IS NOT NULL OR lcom_dduehold1 IS NULL)';
} elseif ($registry->getParm('SHOW_ACTIONED', 'Y')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dactioned IS NOT NULL OR lcom_ddueact IS NULL)';
} elseif ($registry->getParm('SHOW_ACKNOWLEDGED', 'Y')->toBool()) {
    $CompletedWHERECheck = ' AND (lcom_dack IS NOT NULL OR lcom_ddueack IS NULL)';
}

$registry ??= Container::get(Registry::class);
$spscDataset = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$captureTimeTaken = $spscDataset || $registry->getParm('TIME_TO_SUBMIT')->isTrue();
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS')->isTrue();

$approvalStatusDescriptionRepository = (new ApprovalStatusDescriptionRepositoryFactory())->create();
$feedbackCodeApprovalStatuses = $approvalStatusDescriptionRepository->GetApprovalStatusesForModule('COM');

$unapproved = new UnapprovedWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$awaitingAcknowledgement = new AwaitingAcknowledgementWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$awaitingInvestigation = new AwaitingInvestigationWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$awaitingResponse = new AwaitingResponseWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$underInvestigation = new UnderInvestigationWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$awaitingHolding = new AwaitingHoldingWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$awaitingReply = new AwaitingReplyWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$completed = new CompletedWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);
$rejected = new RejectedWorkflowListing($feedbackCodeApprovalStatuses, $COMStatuses);

$workflowListings = array_merge(
    $unapproved->getListing(),
    $awaitingAcknowledgement->getListing(),
    $awaitingInvestigation->getListing(),
    $awaitingResponse->getListing(),
    $underInvestigation->getListing(),
    $awaitingHolding->getListing(),
    $awaitingReply->getListing(),
    $completed->getListing(),
    $rejected->getListing(),
);

$ModuleDefs['COM'] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => 'COM',
    'IS_MAIN_MODULE' => true,
    'LEVEL1_PERMS' => ['COM1'],
    'AUDIT_TRAIL_PERMS' => ['COM2'],  // which permissions can see the audit trail?
    'MOD_ID' => MOD_COMPLAINTS,
    'CODE' => 'COM',
    'NAME' => _fdtk('mod_complaints_title'),
    'NAME_FIELD' => 'com_name',
    'USES_APPROVAL_STATUSES' => true,
    'TABLE' => 'compl_main',
    'HAS_DEFAULT_LISTING' => true,
    'AUDIT_TABLE' => 'aud_compl_main',
    'REC_NAME' => _fdtk('COMName'),
    'TRANSLATION_DOMAIN' => 'complaints',
    'REC_NAME_PLURAL' => _fdtk('COMNames'),
    'REC_NAME_TITLE' => _fdtk('COMNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('COMNamesTitle'),
    RecordHeaderProvider::APPVARS_KEY => [
        'recordid',
        'com_ourref',
        'com_name',
        'com_type',
        'com_subtype',
    ],
    'FK' => 'com_id',
    'PK' => 'recordid',
    'OURREF' => 'com_ourref',
    'PERM_GLOBAL' => 'COM_PERMS',
    'NO_LEVEL1_GLOBAL' => 'COM_NO_OPEN',
    'ACTION' => 'record&module=COM',
    'FIELD_NAMES' => [
        'NAME' => 'com_name',
        'HANDLER' => 'com_mgr',
        'MANAGER' => 'com_head',
        'INVESTIGATORS' => 'com_investigator',
        'REF' => 'com_ourref',
    ],
    'LOCATION_FIELD_PREFIX' => 'com_',

    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'COM1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::FEEDBACK_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'COM2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::FEEDBACK_LEVEL_2],
    ],

    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $eventParameters): array {
                        $hcc = getHCC($eventParameters['recordId']);
                        $linkData = GetPrimaryComplLinkData($eventParameters['recordId']);

                        return array_merge($hcc, $linkData);
                    },
                ],
            ],
        ],
    ],

    ModuleDefKeys::EXTRA_RECORD_DATA_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [
                    'Source/generic/Subjects.php',
                    'Source/generic_modules/COM/ModuleFunctions.php',
                ],
                FormEventsHelper::EVENT_CONDITION => static function (array $params): bool {
                    return $params['data']['link_type' . $params['suffixstring']] === ContactTypes::CONSULTANT;
                },
                FormEventsHelper::EVENT_FUNCTIONS => ['SaveComplaintDates'],
            ],
        ],
    ],
    ModuleDefKeys::EXTRA_CONTACT_LINK_DATA_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $contact): array {
                        return GetPrimaryComplLinkData($contact['com_id'], true);
                    },
                ],
            ],
        ],
    ],
    ModuleDefKeys::EXTRA_CONTACT_UNLINK_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => ['UnlinkComplLinkData'],
            ],
        ],
    ],
    ModuleDefKeys::PRE_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function ($data) {
                        $data = autoFillCloseDateFromComplainantChainData($data);

                        return SetFirstReceivedDateFromComplaintChainReceivedIfEmpty($data);
                    },
                ],
            ],
        ],
    ],
    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function ($data): void {
                        SetIndependentReviewDates($data);
                        SaveHCC($data);
                        SetHCCDates($data);
                        SaveComplaintDatesOnComplaint($data);
                        CleanupAfterEmptySubject($data);
                        SendDataToExternalURLs($data);
                        SetComplaintChainDateReceivedFromFirstReceivedDateIfEmpty($data);
                        GenericSaveMedications(
                            Module::FEEDBACK,
                            (new DoctrineEntityManagerFactory())->getInstance()->getRepository(FeedbackEntity::class),
                            $data,
                        );
                    },
                ],
            ],
        ],
    ],
    ModuleDefKeys::POST_LINK_CONTACT_SAVE_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => ['Source/generic_modules/COM/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => ['SaveComplainantAsPatient'],
            ],
        ],
    ],
    'USE_WORKFLOWS' => true,
    'WORKFLOW_GLOBAL' => 'COM_WORKFLOW',
    'DEFAULT_WORKFLOW' => 1,
    'WORKFLOWS_ALLOWING_READONLY_STATUS_EDIT' => [2],
    'STATUSES_ALLOWING_READONLY_STATUS_EDIT' => ['FIN'],
    'LOGGED_OUT_LEVEL1' => true,
    'FORMCODE1' => 'COM1',
    'FORMCODE2' => 'COM2',
    'MAIN_MENU_ICON' => 'complaints24n.gif',
    'ICON' => 'icons/icon_COM.png',
    'AGE_AT_DATE' => 'com_dreceived',
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'documents',
    'LINKED_CONTACT_LISTING_COLS' => ['recordid', 'com_name', 'com_ourref', 'com_detail', 'com_type'],
    'SEARCH_URL' => 'action=search',
    'DEFAULT_ORDER' => 'com_dreceived',
    'CAN_CREATE_EMAIL_TEMPLATES' => true,
    'LINKED_RECORDS' => [
        'com_subject' => [
            'section' => 'subject',
            'type' => 'com_subject',
            'table' => 'VW_COMPL_SUBJ_WEB',
            'real_table' => 'COMPL_SUBJECTS',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => [
                    'csu_subject', 'csu_subsubject', 'csu_stafftype', 'csu_location_id', 'csu_service_id',
                    'csu_outcome', 'csu_notes', 'csu_dcompleted', 'com_service_area', 'subject_type', 'subject_subtype',
                    FeedbackFields::COM_ISSUE_PATHWAY,
                    FeedbackFields::COM_ISSUE_TYPE,
                    'csu_level_of_harm',
                ],
            ],
            'main_recordid_label' => 'COM_ID',
            'RunAfterSave' => ['SavePrimarySubject'],
            'RunAfterSaveIncludes' => ['Source/generic/Subjects.php'],
        ],
        'com_issue' => [
            'section' => 'issue',
            'type' => 'com_issue',
            'table' => 'COMPL_ISD_ISSUES',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => [
                    'cisd_type', 'cisd_category', 'cisd_subcategory', 'cisd_pat_adm_type',
                    'cisd_service_area', 'cisd_specialty', 'cisd_staff_group', 'cisd_staff_position',
                ],
            ],
            'main_recordid_label' => 'COM_ID',
        ],
        'com_ombudsman_subject' => [
            'title' => _fdtk('ombudsman_subject'),
            'section' => FeedbackSections::OMBUDSMAN_SUBJECTS,
            'type' => 'com_ombudsman_subject',
            'table' => 'com_ombudsman_subject',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => ['Rows' => OmbudsmanSubjectFields::getFields()],
            'fieldFormatsTable' => 'COMOMS',
            'main_recordid_label' => 'COM_ID',
            'useIdentity' => true,
        ],
        'com_tasks' => [
            'title' => _fdtk('tasks'),
            'section' => 'tasks',
            'type' => 'com_tasks',
            'table' => 'tasks',
            'save_listorder' => true,
            'basic_form' => ['Rows' => TaskFields::getFields()],
            'fieldFormatsTable' => 'COMTAS',
            'main_recordid_label' => 'com_id',
            'model' => TaskModelFactory::class,
        ],
    ],
    'SUBJECT_TYPE' => 'com_subject',
    'ISSUE_TYPE' => 'com_issue',
    'PRIMARY_SUBJECT_MAPPINGS' => [
        'csu_subject' => 'com_subject1',
        'csu_subsubject' => 'com_subsubject1',
        'csu_stafftype' => 'com_stafftype1',
        'csu_location_id' => 'location_id1',
        'csu_service_id' => 'service_id1',
        'csu_outcome' => 'com_outcome1',
        'csu_notes' => 'com_notes1',
        'csu_dcompleted' => 'com_dcompleted1',
        'subject_type' => 'com_type1',
        'subject_subtype' => 'com_subtype1',
    ],
    'IDENTIFY_TABLE' => true, // needed because some complaints field names are duplicated on subforms
    'FIELD_MAPPINGS' => [
        'com_subject' => 'csu_subject',
        'com_subsubject' => 'csu_subsubject',
        'com_stafftype' => 'csu_stafftype',
        'location_id' => 'location_id',
        'service_id' => 'service_id',
        'com_outcome' => 'csu_outcome',
        'csu_notes' => 'csu_notes',
        'csu_dcompleted' => 'csu_dcompleted',
        'com_type' => 'subject_type',
        'com_subtype' => 'subject_subtype',
        'csu_level_of_harm' => 'csu_level_of_harm',
        'csu_location_id' => 'csu_location_id',
        'csu_service_id' => 'csu_service_id',
        FeedbackFields::COM_ISSUE_PATHWAY => FeedbackFields::COM_ISSUE_PATHWAY,
        FeedbackFields::COM_ISSUE_TYPE => FeedbackFields::COM_ISSUE_TYPE,
    ],
    'CONTACTTYPES' => [
        ContactTypes::CONSULTANT => [
            'Type' => ContactTypes::CONSULTANT,
            'Name' => _fdtk('com_complainant'),
            'Plural' => _fdtk('com_complainant_plural'),
            'None' => _fdtk('no_com_complainant_plural'),
            'CreateNew' => _fdtk('com_complainant_link'),
            'NoDuplicatesWith' => ['A'],
        ],
        ContactTypes::PERSON_AFFECTED => [
            'Type' => ContactTypes::PERSON_AFFECTED,
            'Name' => _fdtk('com_person_affected'),
            'Plural' => _fdtk('com_person_affected_plural'),
            'None' => _fdtk('no_com_person_affected_plural'),
            'CreateNew' => _fdtk('com_person_affected_link'),
            'NoDuplicatesWith' => ['C'],
        ],
        ContactTypes::EMPLOYEE => [
            'Type' => ContactTypes::EMPLOYEE,
            'Name' => _fdtk('com_employee'),
            'Plural' => _fdtk('com_employee_plural'),
            'None' => _fdtk('no_com_employee_plural'),
            'CreateNew' => _fdtk('com_employee_link'),
        ],
        ContactTypes::OTHER_CONTACT => [
            'Type' => ContactTypes::OTHER_CONTACT,
            'Name' => _fdtk('com_other_contact'),
            'Plural' => _fdtk('com_other_contact_plural'),
            'None' => _fdtk('no_com_other_contact_plural'),
            'CreateNew' => _fdtk('com_other_contact_link'),
        ],
    ],
    'LEVEL1_CON_OPTIONS' => [
        'C' => ['Title' => _fdtk('com_complainant'), 'DivName' => 'contacts_type_C', 'Max' => 1],
        'A' => ['Title' => _fdtk('person_affected'), 'DivName' => 'contacts_type_A'],
        'N' => ['Title' => 'Contact', 'DivName' => 'contacts_type_N'],
        'R' => ['Title' => 'Reporter', 'Role' => GetParm('REPORTER_ROLE', 'REP'), 'ActualType' => 'N', 'DivName' => 'contacts_type_R', 'Max' => 1],
    ],
    'RECORD_NAME_FROM_CONTACT' => 'A',
    'STAFF_EMPL_FILTER_MAPPINGS' => [
        'location' => 'location_id',
        'service' => 'service_id',
    ],
    'LEVEL_1_ONLY_FORM_GLOBAL' => 'COM1_DEFAULT',
    'FIELD_ARRAY' => [
        'com_issues_linked',
        'com_name', 'com_ourref', 'com_mgr', 'com_head', 'com_dreceived', 'location_id', 'service_id', 'other_location',
        'other_service', 'confirm_location_id', 'confirm_service_id', 'com_detail', 'com_type', 'com_method', 'com_summary', 'com_outcome', 'com_subtype',
        'com_dincident', 'com_consent', 'com_dopened', 'com_dreopened', 'com_curstage', 'com_otherref', 'com_purchaser',
        'com_dclosed', 'com_investigator', 'com_inv_dstart',
        'com_inv_dcomp', 'com_grade', 'com_consequence', 'com_likelihood', 'com_inv_outcome', 'com_lessons_code',
        'com_inv_lessons', 'com_action_code', 'com_inv_action',
        'rep_approved', 'show_person', 'com_subjects_linked',
        'com_ko41_type', 'com_koservarea', 'com_kosubject', 'com_koprof', 'com_koethnic_pat', 'com_koethnic_staff',
        'com_isd_unit', 'com_isd_locactual', 'com_isd_consent', 'com_isd_dconsent_req', 'com_isd_dconsent_rec', 'com_isd_div_sent',
        'com_isd_ref_added', 'com_isd_iaas_involved', 'com_isd_cas_involved', 'com_isd_chi_no', 'com_isd_resp_sent_20',
        'com_isd_resp_20_reason', 'com_isd_agree_40', 'com_isd_agree_40_date', 'com_isd_resp_40_reason', 'com_isd_actions', 'com_isd_plan', 'com_isd_dexport',
        'com_drequest', 'com_ddueackreq', 'com_dackreq', 'com_dstatement', 'com_dduelaychair', 'com_dlaychair', 'com_dduedecision', 'com_ddecision', 'com_assessor',
        'com_recir', 'com_irsynopsis', 'com_dinform', 'com_dduepappt', 'com_dpappt', 'com_dduepdraft', 'com_dpdraft', 'com_ddueppublish', 'com_dppublish', 'com_dduecereply', 'com_dcereply', 'com_ircode',
        'com_inc_type', 'com_affecting_tier_zero', 'com_type_tier_one', 'com_type_tier_two', 'com_type_tier_three',
        'com_last_updated', 'flag_for_investigation', 'flag_for_rib', 'updateddate', 'updatedby', 'learnings_to_share', 'key_learnings', 'learnings_title',
        'requested_consultant', 'priority_scale', 'is_record_sensitive', 'time_taken_to_submit',
        FeedbackFields::COM_LESSON_LEARNED_SUB_CATEGORY,
        FeedbackFields::COM_HRO_CHARACTERISTICS,
        FeedbackFields::COM_SPECIALTY,
        FeedbackFields::MCA_OR_NA,
        FeedbackFields::SOURCE_OF_RECORD,
        FeedbackFields::COM_PAT_EXPECTATIONS,
        FeedbackFields::COM_PAT_UPDATE_PREF,
        FeedbackFields::COM_GRADE_RATING,
        FeedbackFields::REFERRED_TO_OMBUDSMAN,
        FeedbackFields::DATE_FIRST_CONTACT,
        FeedbackFields::DATE_EVIDENCE_DUE,
        FeedbackFields::DATE_EVIDENCE_SUBMITTED,
        FeedbackFields::OMBUDSMAN_REFERENCE,
        FeedbackFields::OMBUDSMAN_HANDLER,
        FeedbackFields::OMBUDSMAN_CURRENT_STAGE,
        FeedbackFields::EARLY_SETTLEMENT_PROPOSAL,
        FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED,
        FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED,
        FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED,
        FeedbackFields::DATE_INVESTIGATION_BEGAN,
        FeedbackFields::DATE_RESPONSE_DUE,
        FeedbackFields::DATE_DOC_INV_SUB,
        FeedbackFields::DATE_INV_SUB,
        FeedbackFields::DATE_DRAFT_RECEIVED,
        FeedbackFields::DATE_DRAFT_RESPONSE_DUE,
        FeedbackFields::DATE_REC_RECIEVED,
        FeedbackFields::DATE_ACTION_PLAN,
        FeedbackFields::DATE_DRAFT_REPORT_SUB,
        FeedbackFields::DATE_REPORT_RECEIVED,
        FeedbackFields::FINAL_REP_TYPE,
        FeedbackFields::OMBUDSMAN_OUTCOME,
        FeedbackFields::OMBUDSMAN_LEARNING,
        FeedbackFields::COM_OUTCOME_GRADING,
        FeedbackFields::COM_SERIOUS_INCIDENT,
        FeedbackFields::COM_WELSH_LANGUAGE,
        FeedbackFields::COM_REDRESS_ESCALATED,
        FeedbackFields::OUTBREAK_IMPACT,
        FeedbackFields::OUTBREAK_TYPE,
        FeedbackFields::COM_UUID,
    ],
    'SECURITY' => [
        'LOC_FIELDS' => [
            'location_id' => [],
            'service_id' => [],
        ],
        'EMAIL_NOTIFICATION_GLOBAL' => 'COM_STA_EMAIL_LOCS',
    ],
    'SHOW_EMAIL_GLOBAL' => 'COM_SHOW_EMAIL',
    'EMAIL_REPORTER_GLOBAL' => 'COM_EMAIL_REPORTER',
    'COM_EMAIL_MGR' => 'COM_EMAIL_MGR',
    'EMAIL_HANDLER_GLOBAL' => 'EMAIL_HANDLER_GLOBAL',
    'EMAIL_USER_PARAMETER' => 'COM_STA_EMAIL_LOCS',
    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserCOM1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserCOM2Settings',
    'HARD_CODED_LISTINGS' => $workflowListings,
    'ACTION_LINK_LIST_FIELDS' => [
        ['field' => 'recordid', 'width' => '5'],
        ['field' => 'com_name', 'width' => '17'],
        ['field' => 'com_dreceived', 'width' => '10'],
        ['field' => 'com_type', 'width' => '17'],
        ['field' => 'com_detail', 'width' => '51'],
    ],
    'TRAFFICLIGHTS_FIELDS' => [
        'com_method', 'com_type', 'rep_approved',
    ],
    'HOME_SCREEN_STATUS_LIST' => true,
    'ADDITIONAL_GENERATE_LIB' => 'Source/generic_modules/INC/CopyFunctions.php',
    'ADDITIONAL_GENERATE_FUNCTION_CALLS' => [
        'copyRespondents',
    ],
    'GENERATE_ADDITIONAL_MAPPINGS' => [
        Module::INCIDENTS => [
            'com_dreceived',
            'com_method',
            FeedbackFields::COM_PAT_EXPECTATIONS,
            FeedbackFields::COM_PAT_UPDATE_PREF,
        ],
    ],
    'CAN_LINK_DOCS' => true,
    'CAN_LINK_CONTACTS' => true,
    'CAN_LINK_MODULES' => true,
    'CAN_LINK_NOTES' => true,
    'CCS2_FIELDS' => [
        'com_affecting_tier_zero',
        'com_type_tier_one',
        'com_type_tier_two',
        'com_type_tier_three',
    ],
    'COMBO_EXCLUDE' => [
        'com_unit_type',
    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Complaints/c_dx_complaints_guide.xml',

    'FIELDSET_MAPPINGS' => [
        'C' => 19,
        'A' => 18,
        'E' => 17,
        'N' => 16,
        'linked_actions' => 13,
        'payments' => 29,
    ],

    'RECORD_SUBMIT_DURATION' => $captureTimeTaken,
    'ACTION_TRIGGERS' => $spscDataset || $actionTriggersEnabled,
    'ADD_NEW_CONFIG_OPTION' => true,
    // OVERDUE
    'CAN_HAVE_OVERDUE_RECORDS' => true,
    'OVERDUE_CHECK_FIELD' => FeedbackFields::DATE_FIRST_RECEIVED,
    'OVERDUE_STATUSES' => [
        ApprovalStatus::HOLDING_AREA,
        ApprovalStatus::DRAFT,
        ApprovalStatus::BEING_REVIEWED,
        ApprovalStatus::AWAITING_FINAL,
        ApprovalStatus::BEING_APPROVED,
    ],
    'DATE_OPENED_AT' => FeedbackFields::DATE_OPENED,
];

// If the workflow for Feedback is set to 2, add the additional statuses to the listing view
if ($registry->getParm($ModuleDefs['COM']['WORKFLOW_GLOBAL'], $ModuleDefs['COM']['DEFAULT_WORKFLOW'])->toScalar() == 2) {
    // remove the rejected status so it can be added back in after the statuses for Workflow 1
    unset($workflowListings['rejected']);

    // merge the existing workflow listing with the statuses for Workflow 1
    $workflowListings = array_merge($workflowListings, [
        'completed' => [
            'Title' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['description'] ?? _fdtk('com_completed_listing_title'),
            'Link' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['description'] ?? _fdtk('com_completed_listing_title'),
            'Colour' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['cod_web_colour'] ?? '0',
            'NoOverdue' => true,
            'Where' => AppVarsHelper::WORKFLOW_TWO_COMPLETED_STATUS_SQL,
        ],
        'pending_finalisation' => [
            'Title' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['PENDING']]['description'] ?? _fdtk('com_pending_finalisation_listing_title'),
            'Link' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['PENDING']]['description'] ?? _fdtk('com_pending_finalisation_listing_title'),
            'Colour' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['PENDING']]['cod_web_colour'] ?? '0',
            'NoOverdue' => true,
            'Where' => 'compl_main.rep_approved = \'PEND\'',
        ],
        'finalised' => [
            'Title' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['FINALISED']]['description'] ?? _fdtk('com_finalised_listing_title'),
            'Link' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['FINALISED']]['description'] ?? _fdtk('com_finalised_listing_title'),
            'Colour' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['FINALISED']]['cod_web_colour'] ?? '0',
            'NoOverdue' => true,
            'Where' => 'compl_main.rep_approved = \'FIN\'',
        ],
        'rejected' => [
            'Title' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['description'] ?? _fdtk('com_rejected_listing_title'),
            'Link' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['description'] ?? _fdtk('com_rejected_listing_title'),
            'Colour' => $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['cod_web_colour'] ?? '0',
            'NoOverdue' => true,
            'Where' => 'compl_main.rep_approved = \'REJECT\'',
        ],
    ]);

    // Override the value set for the hard coded listing
    $ModuleDefs['COM']['HARD_CODED_LISTINGS'] = $workflowListings;
}

// Remove unwanted sections according to defined complaints chain globals.
// If stages are hidden, then include them in the next stage count/WHERE clause

if (!$registry->getParm('SHOW_ACKNOWLEDGED', 'Y')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitinginvestigation']['Where'] =
        '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitinginvestigation']['Where'] . ')' . ' OR (' .
        $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingacknowledgement']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingacknowledgement']);
}

if (!$registry->getParm('SHOW_ACTIONED', 'Y')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingresponse']['Where'] =
        '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingresponse']['Where'] . ')' . ' OR (' .
        $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitinginvestigation']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitinginvestigation']);
}

if (!$registry->getParm('SHOW_HOLDING_1', 'N')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['underinvestigation']['Where'] =
        '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['underinvestigation']['Where'] . ')' . ' OR (' .
        $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingresponse']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingresponse']);
}

if (!$registry->getParm('SHOW_RESPONSE', 'Y')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingholding']['Where'] =
        '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingholding']['Where'] . ')' . ' OR (' .
        $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['underinvestigation']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['underinvestigation']);
}

if (!$registry->getParm('SHOW_HOLDING', 'Y')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingreply']['Where'] =
        '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingreply']['Where'] . ')' . ' OR (' .
        $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingholding']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingholding']);
}

if (!$registry->getParm('SHOW_REPLIED', 'Y')->toBool()) {
    $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['completed']['Where'] =
       '(' . $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['completed']['Where'] . ')' . ' OR (' .
       $ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingreply']['Where'] . ')';

    unset($ModuleDefs[Module::FEEDBACK]['HARD_CODED_LISTINGS']['awaitingreply']);
}

$ModuleDefs[Module::FEEDBACK]['IS_MEDS_ENABLED_FOR_MODULE'] = (new DatixConfigFactory())->getInstance()->isFeedbackMedicationsEnabled();
$ModuleDefs[Module::FEEDBACK]['MEDICATION_LINK_ENTITY'] = FeedbackMedicationLink::class;
