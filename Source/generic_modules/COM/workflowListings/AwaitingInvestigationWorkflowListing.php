<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;

class AwaitingInvestigationWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $comStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($comStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($comStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'awaitinginvestigation';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['AWAITING_INVESTIGATION']]['description'] ??
            _fdtk('com_awaitinginvestigation_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['AWAITING_INVESTIGATION']]['description'] ??
            _fdtk('com_awaitinginvestigation_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['AWAITING_INVESTIGATION']]['cod_web_colour'] ??
            '0';
    }

    public function getWhere(array $comStatuses): string
    {
        $globalValues = $this->getGlobalParameterValues();

        $whereClause = "compl_main.rep_approved = 'FA' AND
            (compl_main.com_dclosed IS NULL) AND
                exists (SELECT 1 from LINK_COMPL WITH(NOLOCK)
                    WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                     lcom_dreplied IS NULL AND (";

        // preceding status doesn't exist or is not expected
        $whereClause .= StatusNotValid('ACK', $comStatuses);

        // OR preceding status exists and is completed
        $whereClause .= ' OR ' . StatusValid('ACK', $comStatuses) . ' AND lcom_dack IS NOT NULL' . ')';

        // AND current status is not complete AND current status is expected
        if ($globalValues['includeActionedClause']) {
            $whereClause .= ' AND lcom_dactioned IS NULL AND lcom_ddueact IS NOT NULL';
        }

        // AND no later status completed
        if ($globalValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL';
        }

        if ($globalValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL';
        }

        if ($globalValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND
                    LINK_COMPL.recordid = (SELECT top 1 LINK_COMPL.recordid from LINK_COMPL WITH(NOLOCK) WHERE
                                            LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND LINK_COMPL.LCOM_CURRENT='Y'
                                            ORDER by lcom_primary desc, LINK_COMPL.recordid))";

        return $whereClause;
    }

    public function getNoOverdue(): bool
    {
        return false;
    }

    public function getApprovalCode(): string
    {
        return ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['AWAITING_INVESTIGATION'];
    }

    public function getOverdueWhere(array $comStatuses): string
    {
        $globalValues = $this->getGlobalParameterValues();

        $whereClause =
            "compl_main.rep_approved = 'FA' AND
                (compl_main.com_dclosed IS NULL) AND
                exists (SELECT 1 from LINK_COMPL WITH(NOLOCK)
                    WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                     lcom_dreplied IS NULL AND (";

        // preceding status doesn't exist or is not expected
        $whereClause .= StatusNotValid('ACK', $comStatuses);

        // OR preceding status exists and is completed
        $whereClause .= ' OR ' . StatusValid('ACK', $comStatuses) . ' AND lcom_dack IS NOT NULL' . ')';

        // AND current status is not complete AND current status is expected
        if ($globalValues['includeActionedClause']) {
            $whereClause .= " AND lcom_dactioned IS NULL AND lcom_ddueact < '@TODAY'";
        }

        // AND no later status completed
        if ($globalValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL';
        }

        if ($globalValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL';
        }

        if ($globalValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND
                    LINK_COMPL.recordid = (SELECT top 1 LINK_COMPL.recordid from LINK_COMPL WITH(NOLOCK)
                    WHERE
                            LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND LINK_COMPL.LCOM_CURRENT='Y'
                    ORDER by lcom_primary desc, LINK_COMPL.recordid))";

        return $whereClause;
    }

    public function getOverdueDateField(): string
    {
        return 'lcom_ddueact';
    }
}
