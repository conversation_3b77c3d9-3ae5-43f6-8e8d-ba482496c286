<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

class CompletedWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $comStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($comStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($comStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'completed';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['description'] ??
            _fdtk('com_completed_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['description'] ??
            _fdtk('com_completed_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED']]['cod_web_colour'] ??
           '0';
    }

    public function getWhere(array $comStatuses): string
    {
        $completedWhereCheck = $this->getCompletedWhereCheck();

        return "compl_main.rep_approved = 'FA'
                AND
                (
                    compl_main.com_dclosed IS NOT NULL
                    OR
                    (
                        exists
                        (
                            SELECT 1
                            FROM LINK_COMPL WITH(NOLOCK), LINK_CONTACTS WITH(NOLOCK)
                            WHERE
                                LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID
                                AND
                                LINK_CONTACTS.COM_ID = LINK_COMPL.COM_ID
                                AND
                                LINK_COMPL.LCOM_CURRENT='Y'
                        )
                        AND
                        exists (
                                    SELECT 1
                                    FROM LINK_COMPL WITH(NOLOCK)
                                    WHERE
                                        LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID  {$completedWhereCheck}
                                        AND
                                        LINK_COMPL.recordid = (
                                                                SELECT top 1 LINK_COMPL.recordid
                                                                FROM LINK_COMPL WITH(NOLOCK)
                                                                WHERE
                                                                    LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID
                                                                    AND
                                                                    LINK_COMPL.LCOM_CURRENT='Y'
                                                                    ORDER BY
                                                                    lcom_primary desc, LINK_COMPL.recordid
                                                                )
                            )
                    )
                )";
    }

    public function getNoOverdue(): bool
    {
        return true;
    }

    public function getApprovalCode(): string
    {
        return ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['COMPLETED'];
    }

    public function getOverdueWhere(array $comStatuses): string
    {
        return '';
    }

    public function getOverdueDateField(): string
    {
        return '';
    }

    /**
     * Get the WHERE Clause for the last stage available.
     */
    private function getCompletedWhereCheck(): string
    {
        $registry = Container::get(Registry::class);

        if ($registry->getParm('SHOW_REPLIED', 'Y')->toBool()) {
            return ' AND (lcom_dreplied IS NOT NULL OR lcom_dduerepl IS NULL)';
        }

        if ($registry->getParm('SHOW_HOLDING', 'Y')->toBool()) {
            return ' AND (lcom_dholding IS NOT NULL OR lcom_dduehold IS NULL)';
        }

        if ($registry->getParm('SHOW_RESPONSE', 'Y')->toBool()) {
            return ' AND (lcom_dresponse IS NOT NULL OR lcom_ddueresp IS NULL)';
        }

        if ($registry->getParm('SHOW_HOLDING_1', 'N')->toBool()) {
            return ' AND (lcom_dhold1 IS NOT NULL OR lcom_dduehold1 IS NULL)';
        }

        if ($registry->getParm('SHOW_ACTIONED', 'Y')->toBool()) {
            return ' AND (lcom_dactioned IS NOT NULL OR lcom_ddueact IS NULL)';
        }

        if ($registry->getParm('SHOW_ACKNOWLEDGED', 'Y')->toBool()) {
            return ' AND (lcom_dack IS NOT NULL OR lcom_ddueack IS NULL)';
        }

        return ' ';
    }
}
