<?php

namespace Source\generic_modules\COM\workflowListings;

use src\framework\registry\Registry;
use src\system\container\facade\Container;

class FeedbackWorkflowListing
{
    protected $key;
    protected $title;
    protected $link;
    protected $colour;
    protected $where;
    protected $noOverdue;
    protected $approvalCode;
    protected $overdueWhere;
    protected $overdueDateField;

    public function getListing(): array
    {
        return [
            $this->key => [
                'Title' => $this->title,
                'Link' => $this->link,
                'Colour' => $this->colour,
                'Where' => $this->where,
                'NoOverdue' => $this->noOverdue,
                'ApprovalCode' => $this->approvalCode,
                'OverdueWhere' => $this->overdueWhere,
                'OverdueDateField' => $this->overdueDateField,
            ],
        ];
    }

    protected function getGlobalParameterValues(): array
    {
        $registry = Container::get(Registry::class);

        return [
            'includeAcknowledgedClause' => $registry->getParm('SHOW_ACKNOWLEDGED', 'Y')->isTrue(),
            'includeActionedClause' => $registry->getParm('SHOW_ACTIONED', 'Y')->isTrue(),
            'includeResponseClause' => $registry->getParm('SHOW_RESPONSE', 'Y')->isTrue(),
            'includeHoldingClause' => $registry->getParm('SHOW_HOLDING', 'Y')->isTrue(),
            'includeHolding1Clause' => $registry->getParm('SHOW_HOLDING_1', 'N')->isTrue(),
            'includeRepliedClause' => $registry->getParm('SHOW_REPLIED', 'Y')->isTrue(),
        ];
    }
}
