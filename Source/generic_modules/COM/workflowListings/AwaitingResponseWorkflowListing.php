<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;

class AwaitingResponseWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $COMStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($COMStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($COMStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'awaitingresponse';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['HOLDING_1']]['description'] ??
            _fdtk('com_awaitingholding_1_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['HOLDING_1']]['description'] ??
            _fdtk('com_awaitingholding_1_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['HOLDING_1']]['cod_web_colour'] ??
            '0';
    }

    public function getWhere(array $COMStatuses): string
    {
        $globalValues = $this->getGlobalParameterValues();

        $whereClause = "compl_main.rep_approved = 'FA' AND
            (compl_main.com_dclosed IS NULL) AND
                exists (SELECT 1 from LINK_COMPL WITH(NOLOCK)
                    WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                     lcom_dreplied IS NULL AND (";

        // preceding status doesn't exist or is not expected
        $whereClause .= StatusNotValid('ACT', $COMStatuses) . ' AND ' .
            StatusNotValid('ACK', $COMStatuses);

        // OR preceding status exists and is completed
        $whereClause .= ' OR ' .
            StatusValid('ACT', $COMStatuses) . ' AND lcom_dactioned IS NOT NULL' .
            ' OR ' .
            StatusNotValid('ACT', $COMStatuses) . ' AND ' .
            StatusValid('ACK', $COMStatuses) . ' AND lcom_dack IS NOT NULL' .
            ')';

        // AND current status is not complete AND current status is expected
        if ($globalValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL AND lcom_dduehold1 IS NOT NULL';
        }

        // AND no later status completed
        if ($globalValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL AND lcom_ddueresp IS NOT NULL';
        }

        if ($globalValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND
                    LINK_COMPL.recordid = (SELECT top 1 LINK_COMPL.recordid from LINK_COMPL WITH(NOLOCK) WHERE
                                            LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND LINK_COMPL.LCOM_CURRENT='Y'
                                            ORDER by lcom_primary desc, LINK_COMPL.recordid))";

        return $whereClause;
    }

    public function getNoOverdue(): bool
    {
        return false;
    }

    public function getApprovalCode(): string
    {
        return ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['HOLDING_1'];
    }

    public function getOverdueWhere(array $COMStatuses): string
    {
        $globalValues = $this->getGlobalParameterValues();

        $whereClause = "compl_main.rep_approved = 'FA' AND
            (compl_main.com_dclosed IS NULL) AND
                exists (SELECT 1 from LINK_COMPL WITH(NOLOCK)
                    WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                     lcom_dreplied IS NULL AND (";

        // preceding status doesn't exist or is not expected
        $whereClause .= StatusNotValid('ACT', $COMStatuses) . ' AND ' .
            StatusNotValid('ACK', $COMStatuses);

        // OR preceding status exists and is completed
        $whereClause .= ' OR ' .
            StatusValid('ACT', $COMStatuses) . ' AND lcom_dactioned IS NOT NULL' .
            ' OR ' .
            StatusNotValid('ACT', $COMStatuses) . ' AND ' .
            StatusValid('ACK', $COMStatuses) . ' AND lcom_dack IS NOT NULL' .
            ')';

        // AND current status is not complete AND current status is expected
        if ($globalValues['includeHolding1Clause']) {
            $whereClause .= " AND lcom_dhold1 IS NULL AND lcom_dduehold1.value('(/dates/date[1]/due)[1]', 'datetime') < '@TODAY'";
        }

        // AND no later status completed
        if ($globalValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL AND lcom_ddueresp IS NOT NULL';
        }

        if ($globalValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND
                    LINK_COMPL.recordid = (SELECT top 1 LINK_COMPL.recordid from LINK_COMPL WITH(NOLOCK) WHERE
                                            LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND LINK_COMPL.LCOM_CURRENT='Y'
                                            ORDER by lcom_primary desc, LINK_COMPL.recordid))";

        return $whereClause;
    }

    public function getOverdueDateField(): string
    {
        return 'lcom_dduehold1';
    }
}
