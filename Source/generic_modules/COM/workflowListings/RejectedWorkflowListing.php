<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;

class RejectedWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $COMStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($COMStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($COMStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'rejected';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['description'] ??
            _fdtk('com_rejected_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['description'] ??
            _fdtk('com_rejected_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT']]['cod_web_colour'] ??
            '0';
    }

    public function getWhere(array $COMStatuses): string
    {
        return "compl_main.rep_approved = 'REJECT'";
    }

    public function getNoOverdue(): bool
    {
        return true;
    }

    public function getApprovalCode(): string
    {
        return ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['REJECT'];
    }

    public function getOverdueWhere(array $COMStatuses): string
    {
        return '';
    }

    public function getOverdueDateField(): string
    {
        return '';
    }
}
