<?php

namespace Source\generic_modules\COM\workflowListings;

/**
 * @psalm-type COM_STATUSES @param array{string, array{global: string, due: string, complete: string}}
 */
interface FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $comStatuses);

    public function getKey(): string;

    public function getTitle(array $feedbackCodeApprovalStatuses): string;

    public function getLink(array $feedbackCodeApprovalStatuses): string;

    public function getColour(array $feedbackCodeApprovalStatuses): string;

    /**
     * @psalm-param COM_STATUSES $comStatuses
     */
    public function getWhere(array $comStatuses): string;

    public function getNoOverdue(): bool;

    public function getApprovalCode(): string;

    public function getOverdueWhere(array $comStatuses): string;

    public function getOverdueDateField(): string;
}
