<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;

class AwaitingAcknowledgementWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $comStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($comStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($comStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'awaitingacknowledgement';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['ACKNOWLEDGE']]['description'] ??
            _fdtk('com_awaitingacknowledgement_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['ACKNOWLEDGE']]['description'] ??
            _fdtk('com_awaitingacknowledgement_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['ACKNOWLEDGE']]['cod_web_colour'] ??
            '0';
    }

    public function getWhere(array $comStatuses): string
    {
        $globalParameterValues = $this->getGlobalParameterValues();

        $whereClause =
            "compl_main.rep_approved = 'FA' AND
                (compl_main.com_dclosed IS NULL OR compl_main.com_dclosed = '') AND
                        (exists (SELECT 1 from LINK_COMPL WITH(NOLOCK) WHERE (LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID
                        AND lcom_dreplied IS NULL ";


        if ($globalParameterValues['includeAcknowledgedClause']) {
            $whereClause .= ' AND lcom_dack IS NULL AND lcom_ddueack IS NOT NULL';
        }

        if ($globalParameterValues['includeActionedClause']) {
            $whereClause .= ' AND lcom_dactioned IS NULL';
        }

        if ($globalParameterValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL';
        }

        if ($globalParameterValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalParameterValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL';
        }

        if ($globalParameterValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND LINK_COMPL.LCOM_CURRENT= 'Y')
                OR ( LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND (LCOM_DRECEIVED IS NULL ";

        if ($globalParameterValues['includeAcknowledgedClause']) {
            $whereClause .= ' AND lcom_dack IS NULL';
        }

        if ($globalParameterValues['includeActionedClause']) {
            $whereClause .= ' AND lcom_dactioned IS NULL';
        }

        if ($globalParameterValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL';
        }

        if ($globalParameterValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL';
        }

        if ($globalParameterValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalParameterValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= ")
            )) OR compl_main.recordid not in
                (SELECT LINK_COMPL.com_id
                from LINK_COMPL WITH(NOLOCK)
                WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                 LINK_COMPL.LCOM_CURRENT='Y')) ";

        return $whereClause;
    }

    public function getNoOverdue(): bool
    {
        return false;
    }

    public function getApprovalCode(): string
    {
        return ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['ACKNOWLEDGE'];
    }

    public function getOverdueDateField(): string
    {
        return 'lcom_ddueack';
    }

    public function getOverdueWhere(array $comStatuse): string
    {
        $globalParameterValues = $this->getGlobalParameterValues();

        $whereClause =
            "compl_main.rep_approved = 'FA' AND
                (compl_main.com_dclosed IS NULL OR compl_main.com_dclosed = '') AND
                    (exists (SELECT 1 from LINK_COMPL WITH(NOLOCK) WHERE LINK_COMPL.COM_ID = COMPL_MAIN.RECORDID AND
                     lcom_dreplied IS NULL ";

        if ($globalParameterValues['includeAcknowledgedClause']) {
            $whereClause .= " AND lcom_dack IS NULL AND lcom_ddueack < '@TODAY'";
        }

        if ($globalParameterValues['includeActionedClause']) {
            $whereClause .= ' AND lcom_dactioned IS NULL';
        }

        if ($globalParameterValues['includeHolding1Clause']) {
            $whereClause .= ' AND lcom_dhold1 IS NULL';
        }

        if ($globalParameterValues['includeResponseClause']) {
            $whereClause .= ' AND lcom_dresponse IS NULL';
        }

        if ($globalParameterValues['includeHoldingClause']) {
            $whereClause .= ' AND lcom_dholding IS NULL';
        }

        if ($globalParameterValues['includeRepliedClause']) {
            $whereClause .= ' AND lcom_dreplied IS NULL';
        }

        $whereClause .= " AND LINK_COMPL.LCOM_CURRENT='Y'))";

        return $whereClause;
    }
}
