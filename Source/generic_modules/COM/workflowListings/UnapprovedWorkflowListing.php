<?php

namespace Source\generic_modules\COM\workflowListings;

use app\services\approvalStatus\ApprovalStatusAcronyms;

class UnapprovedWorkflowListing extends FeedbackWorkflowListing implements FeedbackWorkflowInterface
{
    public function __construct(array $feedbackCodeApprovalStatuses, array $comStatuses)
    {
        $this->key = $this->getKey();
        $this->title = $this->getTitle($feedbackCodeApprovalStatuses);
        $this->link = $this->getLink($feedbackCodeApprovalStatuses);
        $this->colour = $this->getColour($feedbackCodeApprovalStatuses);
        $this->where = $this->getWhere($comStatuses);
        $this->noOverdue = $this->getNoOverdue();
        $this->approvalCode = $this->getApprovalCode();
        $this->overdueWhere = $this->getOverdueWhere($comStatuses);
        $this->overdueDateField = $this->getOverdueDateField();
    }

    public function getKey(): string
    {
        return 'unapproved';
    }

    public function getTitle(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['UNAPPROVED']]['description'] ??
            _fdtk('com_unapproved_listing_title');
    }

    public function getLink(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['UNAPPROVED']]['description'] ??
            _fdtk('com_unapproved_listing_title');
    }

    public function getColour(array $feedbackCodeApprovalStatuses): string
    {
        return
            $feedbackCodeApprovalStatuses[ApprovalStatusAcronyms::FEEDBACK_APPROVAL_STATUSES['UNAPPROVED']]['cod_web_colour'] ??
            '0';
    }

    public function getWhere(array $comStatuses): string
    {
        return "compl_main.rep_approved = 'UN' OR compl_main.rep_approved IS NULL OR compl_main.rep_approved = ''";
    }

    public function getNoOverdue(): bool
    {
        return true;
    }

    public function getApprovalCode(): string
    {
        return '';
    }

    public function getOverdueWhere(array $comStatuses): string
    {
        return '';
    }

    public function getOverdueDateField(): string
    {
        return '';
    }
}
