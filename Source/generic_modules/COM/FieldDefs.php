<?php

use app\models\framework\modules\ModuleRepository;
use app\models\generic\Tables;
use Source\generic_modules\FieldDefKeys;
use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSubjectsFields;
use src\complaints\model\OmbudsmanSubjectFields;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\tasks\models\TaskFields;

$moduleRepository = Container::get(ModuleRepository::class);
$investigationsIsLicensed = $moduleRepository->getModuleByCode('INV')->isEnabled();

try {
    $ribIsLicensed = $moduleRepository->getModuleByCode('RIB')->isEnabled();
} catch (Throwable $e) {
    $ribIsLicensed = false;
}

$FieldDefs['COM'] = [
    'recordid' => [
        'Type' => 'number',
        'Title' => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
        'Table' => 'compl_main',
    ],
    'location_id' => [
        'Type' => 'tree',
        'Title' => 'Location admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'compl_main',
    ],
    'location_id_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Location admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'location_id',
    ],
    'service_id' => [
        'Type' => 'tree',
        'Title' => 'Service admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'compl_main',
    ],
    'service_id_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Service admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'service_id',
    ],
    'other_location' => [
        'Type' => 'tree',
        'Title' => 'Other location',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'compl_main',
    ],
    'other_location_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Other location tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'other location',
    ],
    'confirm_location_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Location?',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'confirm_service_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Service?',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'other_service' => [
        'Type' => 'tree',
        'Title' => 'Other service',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'compl_main',
    ],
    'other_service_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Other service tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'other_service',
    ],
    'com_mgr' => [
        'Type' => 'ff_select',
        'Title' => 'Handler',
        'Table' => 'compl_main',
        'StaffField' => true,
    ],
    'com_head' => [
        'Type' => 'ff_select',
        'Title' => 'Manager',
        'Table' => 'compl_main',
        'StaffField' => true,
    ],
    'com_dreceived' => [
        'Type' => 'date',
        'Title' => 'First received',
        'NotFuture' => true,
        'Table' => 'compl_main',
    ],
    'com_dincident' => [
        'Type' => 'date',
        'Title' => 'Incident Date',
        'NotFuture' => true,
        'Table' => 'compl_main',
    ],
    'com_detail' => [
        'Type' => 'textarea',
        'Title' => _fdtk('com_detail'),
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_method' => [
        'Type' => 'ff_select',
        'Title' => _fdtk('com_method'),
        'Table' => 'compl_main',
    ],
    'com_type' => [
        'Type' => 'ff_select',
        'Title' => 'Type',
        'Table' => 'compl_main',
    ],
    'com_subtype' => [
        'Type' => 'ff_select',
        'Title' => 'Subtype',
        'Table' => 'compl_main',
    ],
    'com_curstage' => [
        'Type' => 'ff_select',
        'Title' => 'Current Stage',
        'Table' => 'compl_main',
    ],
    'com_outcome' => [
        'Type' => 'ff_select',
        'Title' => 'Outcome code',
        'Table' => 'compl_main',
    ],
    'com_summary' => [
        'Type' => 'textarea',
        'Title' => 'Outcome',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_purchaser' => [
        'Title' => 'Commissioner',
        'Type' => 'ff_select',
        'Table' => 'compl_main',
    ],
    'com_otherref' => [
        'Title' => 'Other ref',
        'Type' => 'string',
        'Width' => 32,
        'MaxLength' => 32,
        'Table' => 'compl_main',
    ],
    'csu_notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'NoListCol' => true,
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'compl_subjects',
    ],
    'subject_subtype' => [
        'Type' => 'ff_select',
        'Title' => 'Subtype',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_subtype',
        'oldCodes' => 'true',
        'Table' => 'compl_subjects',
    ],
    'subject_type' => [
        'Type' => 'ff_select',
        'Title' => 'Type',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_type',
        'oldCodes' => 'true',
        'Table' => 'compl_subjects',
    ],
    'csu_dcompleted' => [
        'Type' => 'date',
        'Title' => 'Completed date',
        'NoListCol_Override' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_level_of_harm' => [
        'Type' => 'ff_select',
        'Title' => 'Level of harm',
        'Table' => 'compl_subjects',
    ],
    'learnings_to_share' => [
        'Type' => 'yesno',
        'Title' => 'Any Learnings or Outcomes to share?',
        'Table' => 'compl_main',
    ],
    'learnings_title' => [
        'Type' => 'string',
        'Width' => 30,
        'MaxLength' => 64,
        'Title' => 'Title',
        'Table' => 'compl_main',
    ],
    'key_learnings' => [
        'Type' => 'textarea',
        'Title' => 'Key Learnings and Outcomes',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'compl_main',
        'RecordLinkBtn' => [
            'moduleTo' => 'LEA',
        ],
    ],
    'com_name' => [
        'Type' => 'string',
        'Title' => 'Name',
        'Width' => 70,
        'MaxLength' => 32,
        'Table' => 'compl_main',
    ],
    'com_ourref' => [
        'Type' => 'string',
        'Title' => 'Our Ref',
        'Width' => 50,
        'MaxLength' => 32,
        'Table' => 'compl_main',
    ],
    'com_inc_type' => [
        'Type' => 'ff_select',
        'Title' => 'Incident Type',
        'Table' => 'compl_main',
    ],
    'link_patrelation' => [
        'Type' => 'ff_select',
        'NoListCol' => true,
        'Title' => 'Relationship',
        'Table' => 'compl_main',
    ],
    'com_investigator' => [
        'Type' => 'multilistbox',
        'Title' => 'Investigator',
        'MaxLength' => 252,
        'Table' => 'compl_main',
        'StaffField' => true,
    ],
    'com_inv_outcome' => [
        'Type' => 'ff_select',
        'Title' => 'Outcome of investigation',
        'Table' => 'compl_main',
    ],
    'com_inv_dstart' => [
        'Type' => 'date',
        'NotFuture' => true,
        'Title' => 'Date investigation started',
        'Table' => 'compl_main',
    ],
    'com_inv_dcomp' => [
        'Type' => 'date',
        'NotFuture' => true,
        'NotEarlierThan' => ['com_inv_dstart'],
        'Title' => 'Date investigation completed',
        'Table' => 'compl_main',
    ],
    'com_inv_lessons' => [
        'Type' => 'textarea',
        'Title' => 'Lessons learned',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_inv_action' => [
        'Type' => 'textarea',
        'Title' => 'Action taken',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_action_code' => [
        'Type' => 'multilistbox',
        'Title' => 'Action taken codes',
        'NoListCol' => true,
        'MaxLength' => 128,
        'Table' => 'compl_main',
    ],
    'com_lessons_code' => [
        'Type' => 'multilistbox',
        'Title' => 'Lessons learned codes',
        'NoListCol' => true,
        'MaxLength' => 128,
        'Table' => 'compl_main',
    ],
    'csu_subject' => [
        'Type' => 'ff_select',
        'Title' => 'Subject',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_subject',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_subsubject' => [
        'Type' => 'ff_select',
        'Title' => 'Sub-Subject',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_subsubject',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_stafftype' => [
        'Type' => 'ff_select',
        'Title' => 'Staff Type',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_stafftype',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_directorate' => [
        'Type' => 'ff_select',
        'Title' => 'Directorate',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_directorate',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_specialty' => [
        'Type' => 'ff_select',
        'Title' => 'Specialty',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_specialty',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_location' => [
        'Type' => 'ff_select',
        'Title' => 'Location',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_location',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_outcome' => [
        'Type' => 'ff_select',
        'Title' => 'Outcome',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_outcome',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_clingroup' => [
        'Type' => 'ff_select',
        'Title' => 'Clinical Group',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_clingroup',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_organisation' => [
        'Type' => 'ff_select',
        'Title' => 'Organisation',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_organisation',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_loctype' => [
        'Type' => 'ff_select',
        'Title' => 'Location Type',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_loctype',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_unit' => [
        'Type' => 'ff_select',
        'Title' => 'Unit',
        'NoListCol' => true,
        'FieldFormatsName' => 'com_unit',
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'csu_location_id' => [
        'Type' => 'tree',
        'Title' => 'Location (Subjects)',
        'NoListCol' => true,
        'requireMinChars' => true,
        'Table' => 'compl_subjects',
        'mapperType' => 'location',
    ],
    'csu_service_id' => [
        'Type' => 'tree',
        'Title' => 'Service (Subjects)',
        'NoListCol' => true,
        'requireMinChars' => true,
        'Table' => 'compl_subjects',
        'mapperType' => 'service',
    ],
    'com_service_area' => [
        'Type' => 'ff_select',
        'Title' => 'Service area (Subjects)',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_subjects',
    ],
    'lcom_iscomplpat' => [
        'Type' => 'yesno',
        'Title' => _fdtk('link_complpat'),
        'NoListCol' => true,
        'Table' => 'link_compl',
    ],
    'lcom_ddueack' => ['Title' => 'Acknowledged due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_ddueact' => ['Title' => 'Actioned due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dduehold1' => ['Title' => 'Holding 1 due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_ddueresp' => ['Title' => 'Response due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dduehold' => ['Title' => 'Holding due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dduerepl' => ['Title' => 'Replied due (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],

    'lcom_dreceived' => [
        'Title' => 'Date Received',
        'Type' => 'date',
        'NoListCol' => true,
        'NotFuture' => true,
        'Table' => 'link_compl',
    ],
    'lcom_primary' => [
        'Title' => 'Primary Person Providing Feedback',
        'Type' => 'yesno',
        'NoListCol' => true,
        'Table' => 'link_compl',
    ],
    'lcom_dack' => ['Title' => 'Acknowledged (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dactioned' => ['Title' => 'Actioned done (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dhold1' => ['Title' => 'Holding 1 done (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dresponse' => ['Title' => 'Response done (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dholding' => ['Title' => 'Holding done (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'lcom_dreplied' => ['Title' => 'Replied done (Person Providing Feedback)', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'link_compl', 'NotEarlierThan' => ['lcom_dreceived']],
    'rep_approved' => [
        'Type' => 'ff_select',
        'Title' => 'Approval status',
        'Width' => 32,
        'Table' => 'compl_main',
    ],
    'show_document' => [
        'Type' => 'checkbox',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'show_person' => [
        'Type' => 'yesno',
        'Title' => 'Are there any additional people affected by this feedback?',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'com_subjects_linked' => [
        'Type' => 'yesno',
        'Title' => 'Do you want to add any subjects to this record?',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'com_issues_linked' => [
        'Type' => 'yesno',
        'Title' => 'Do you want to add any issues to this record?',
        'NoListCol' => true,
        'Table' => 'compl_main',
    ],
    'notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'notepad',
    ],
    'flag_for_investigation' => [
        'Type' => 'ff_select',
        'Title' => 'Flag for investigation?',
        'Table' => 'compl_main',
        'NoListCol' => !$investigationsIsLicensed,
        'BlockFromReports' => !$investigationsIsLicensed,
    ],
    'flag_for_rib' => [
        'Type' => 'ff_select',
        'Title' => 'Flag for RIB?',
        'Table' => 'compl_main',
        'NoListCol' => !$ribIsLicensed,
        'BlockFromReports' => !$ribIsLicensed,
    ],
    // ko41 fields
    'com_ko41_type' => ['Title' => 'KO41 Type', 'Type' => 'string', 'MaxLength' => 6, 'Table' => 'compl_main'],
    'com_kosubject' => ['Title' => 'Subject', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_koservarea' => ['Title' => 'Service area', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_koprof' => ['Title' => 'Profession', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_koethnic_pat' => ['Title' => 'Patient Ethnicity', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_koethnic_staff' => ['Title' => 'Staff Ethnicity', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_isd_locactual' => ['Title' => 'Location (ISD)', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_likelihood' => ['Title' => 'Likelihood of recurrence', 'Type' => 'ff_select'],
    'com_unit1' => ['Title' => 'Unit (primary)', 'Type' => 'ff_select'],
    'com_organisation1' => ['Title' => 'Trust (primary)', 'Type' => 'ff_select'],
    'com_loctype1' => ['Title' => 'Location type (primary)', 'Type' => 'ff_select'],
    'com_location1' => ['Title' => 'Location exact (primary)', 'Type' => 'ff_select'],
    'com_recomm_code' => ['Title' => 'Recommendations codes', 'Type' => 'ff_select', 'NoListCol' => true],
    'com_isd_unit' => ['Title' => 'Health sector', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_isd_iaas_involved' => ['Title' => 'IASS involved?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_isd_agree_40_date' => ['Title' => 'If yes - date of letter detailing this extension', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_isd_resp_20_reason' => ['Title' => 'If greater than 20 days, identify reason', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_subsubject1' => ['Title' => 'Sub-subject (primary)', 'Type' => 'ff_select'],
    'com_dopened' => ['Title' => 'Opened date', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_drequest' => ['Title' => 'Request received', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_isd_resp_sent_20' => ['Title' => 'Response sent within 20 working days?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_isd_ref_added' => ['Title' => 'Ref. number added?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_root_causes' => ['Title' => 'Remediable causes', 'Type' => 'ff_select'],
    'com_dreopened' => ['Title' => 'Reopened', 'Type' => 'date', 'NotEarlierThan' => ['com_dopened']],
    'com_specialty1' => ['Title' => 'Specialty (primary)', 'Type' => 'ff_select'],
    'com_outcome1' => ['Title' => 'Outcome (primary)', 'Type' => 'ff_select'],
    'com_subject1' => ['Title' => 'Subject (primary)', 'Type' => 'ff_select'],
    'com_stafftype1' => ['Title' => 'Staff type (primary)', 'Type' => 'ff_select'],
    'com_unit_type' => ['Title' => 'Unit type', 'Type' => 'ff_select'],
    'com_dinform' => ['Title' => 'Person Providing Feedback informed', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dclosed' => ['Title' => 'Closed date', 'Type' => 'date', 'NotFuture' => true, 'NotEarlierThan' => ['com_dopened'], 'Table' => 'compl_main'],
    'com_clingroup1' => ['Title' => 'Clin. group (primary)', 'Type' => 'ff_select'],
    'com_isd_consent' => ['Title' => 'Consent required?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_consequence' => ['Title' => 'Consequence', 'Type' => 'ff_select'],
    'com_isd_dconsent_rec' => ['Title' => 'Date consent obtained', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_isd_dconsent_req' => ['Title' => 'Date consent requested', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_directorate1' => ['Title' => 'Directorate (primary)', 'Type' => 'ff_select'],
    'com_isd_agree_40' => ['Title' => 'Person Providing Feedback agreed to timescale greater than 40 working days?', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_isd_div_sent' => ['Title' => 'Diversity form sent?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_isd_cas_involved' => ['Title' => 'CAS involved?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_isd_actions' => ['Title' => 'Actions taken', 'Type' => 'multilistbox', 'MaxLength' => 254, 'Table' => 'compl_main'],
    'com_isd_dexport' => ['Title' => 'Last exported', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_isd_plan' => [
        'Type' => 'textarea',
        'Title' => 'Service improvement/long-term plan',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_isd_chi_no' => [
        'Type' => 'string',
        'Title' => 'CHI number',
        'Width' => 50,
        'MaxLength' => 64,
        'Table' => 'compl_main',
    ],
    'com_isd_resp_40_reason' => [
        'Type' => 'textarea',
        'Title' => 'Reasons for response taking longer than 40 working days',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_ddueackreq' => ['Title' => 'Request acknowledged (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dackreq' => ['Title' => 'Request acknowledged (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_dstatement' => ['Title' => 'Statement received', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dduelaychair' => ['Title' => 'Lay chair appointed (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dlaychair' => ['Title' => 'Lay chair appointed (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_dduedecision' => ['Title' => 'Decision made (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_ddecision' => ['Title' => 'Decision made (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_assessor' => ['Title' => 'Clinical assessor needed?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_recir' => ['Title' => 'I.R. recommended?', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_irsynopsis' => [
        'Type' => 'textarea',
        'Title' => 'Synopsis',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'compl_main',
    ],
    'com_dduepappt' => ['Title' => 'Panel appointed (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dpappt' => ['Title' => 'Panel appointed (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_dduepdraft' => ['Title' => 'Draft report published (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dpdraft' => ['Title' => 'Draft report published (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_ddueppublish' => ['Title' => 'Final report published (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dppublish' => ['Title' => 'Final report published (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_dduecereply' => ['Title' => 'C.E. replied to Person Providing Feedback (due)', 'Type' => 'date', 'Table' => 'compl_main'],
    'com_dcereply' => ['Title' => 'C.E. replied to Person Providing Feedback (done)', 'Type' => 'date', 'NotFuture' => true, 'Table' => 'compl_main'],
    'com_ircode' => ['Title' => 'IR Outcome Code', 'Type' => 'ff_select', 'Table' => 'compl_main'],
    'com_notes1' => ['Title' => 'Subject notes (primary)'],
    'com_dcompleted1' => ['Title' => 'Completed date (primary)'],
    'rea_code' => [
        'Type' => 'ff_select',
        'Title' => 'Reason for rejection',
        'NoListCol' => true,
        'Table' => 'aud_reasons',
    ],
    'rev_drequested' => ['Title' => 'Date of request', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_reason' => ['Title' => 'Reason for request', 'Type' => 'multilistbox', 'NoListCol' => true, 'MaxLength' => 254, 'Table' => 'rev_main'],
    'rev_dclosed' => ['Title' => 'Closed date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_ddecisiondue' => ['Title' => 'Initial review decision due date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_ddecisiondone' => ['Title' => 'Initial review decision date', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'rev_main'],
    'rev_init_handler' => ['Title' => 'Initial review handler', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_init_outcome' => ['Title' => 'Initial review outcome', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_investigate_yn' => ['Title' => 'Investigation required?', 'Type' => 'yesno', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_init_comments' => [
        'Title' => 'Initial review Comments',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_dhctermsdue' => ['Title' => 'HC terms due date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_dhctermsdone' => ['Title' => 'HC terms sent date', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'rev_main'],
    'rev_dtermsdue' => ['Title' => 'Terms of reference comments due date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_dtermsdone' => ['Title' => 'Terms of reference comments date', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'rev_main'],
    'rev_dcompleteddue' => ['Title' => 'Investigation completion due date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_dcompleteddone' => ['Title' => 'Investigation completion date', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'rev_main'],
    'rev_inv_handler' => ['Title' => 'Investigation review handler', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_inv_outcome' => ['Title' => 'Investigation outcome', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_panelreview_yn' => ['Title' => 'Panel review requested?', 'Type' => 'yesno', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_inv_recommend' => [
        'Title' => 'Investigation recommendations',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_inv_action' => [
        'Title' => 'Investigation action taken',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_dpanelrequested' => ['Title' => 'Panel review requested date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_dpanelcompleted' => ['Title' => 'Panel review completion date', 'Type' => 'date', 'NoListCol' => true, 'NotFuture' => true, 'Table' => 'rev_main'],
    'rev_panel_outcome' => ['Title' => 'Panel review outcome', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_ombudsman_yn' => ['Title' => 'HSO requested?', 'Type' => 'yesno', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_panel_recommend' => [
        'Title' => 'Panel review recommendations',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_panel_action' => [
        'Title' => 'Panel review action taken',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_dombrequested' => ['Title' => 'HSO requested date', 'Type' => 'date', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_omb_assessment' => ['Title' => 'Assessment', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_omb_upheld' => ['Title' => 'Complaint upheld', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_omb_reason' => ['Title' => 'HSO reason for not considering feedback', 'NoListCol' => true],
    'rev_omb_handler' => ['Title' => 'HSO handler', 'Type' => 'ff_select', 'NoListCol' => true, 'Table' => 'rev_main'],
    'rev_omb_recommend' => [
        'Title' => 'HSO recommendations',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'rev_omb_action' => [
        'Title' => 'HSO action taken',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'rev_main',
    ],
    'com_ch8_poc' => ['Title' => 'Programme of Care (Subjects)(CH8)', 'NoListCol' => true],
    'com_subsubject' => ['Title' => 'Sub-subject', 'NoListCol' => true, 'Type' => 'ff_select'],
    'com_grade' => ['Title' => 'Grade', 'Type' => 'ff_select'],
    FeedbackFields::COM_GRADE_RATING => [
        'Type' => FieldInterface::NUMBER_DB,
        'Title' => 'Risk Rating',
        'Table' => 'compl_main',
    ],
    'com_pasno3' => ['Title' => 'PAS No. 3'],
    'lcom_details' => ['Title' => 'Notes (Person Providing Feedback)', 'NoListCol' => true],
    'com_ch8_subject' => ['Title' => 'Subjects (Subjects)(CH8)', 'NoListCol' => true],
    'com_pasno2' => ['Title' => 'PAS No. 2'],
    'com_subject' => ['Title' => 'Subjects', 'NoListCol' => true, 'Type' => 'ff_select'],
    'com_cla_count' => ['Title' => 'No. of Linked Claims', 'NoListCol' => true],
    'com_inc_count' => ['Title' => 'No. of Linked Incidents', 'NoListCol' => true],
    'com_compl_count' => ['Title' => 'No. of Linked Persons Providing Feedback', 'NoListCol' => true],
    'com_consent' => ['Title' => 'Consent obtained', 'Type' => 'yesno', 'Table' => 'compl_main'],
    'com_stafftype' => ['Title' => 'Staff types', 'NoListCol' => true, 'Type' => 'ff_select'],
    'com_location' => ['Title' => 'Location exact (Subjects)', 'NoListCol' => true, 'Type' => 'ff_select'],
    'com_notes' => [
        'Title' => 'Subject notes (Subjects)',
        'NoListCol' => true,
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
    ],
    'com_dcompleted' => ['Title' => 'Subject date completed (Subjects)', 'NoListCol' => true, 'Type' => 'date'],
    'com_pasno1' => ['Title' => 'PAS No. 1'],
    'lcom_dreopened' => ['Title' => 'Re-opened (Person Providing Feedback)', 'NoListCol' => true, 'Type' => 'date', 'NotFuture' => true],
    'lcom_last_dreopened' => ['Title' => 'Person Providing Feedback Re-opened date', 'NoListCol' => true, 'Type' => 'date', 'ReadOnly' => true],
    'com_recommend' => ['Title' => 'Recommendations'],

    // Issues
    'cisd_type' => [
        'Type' => 'ff_select',
        'Title' => 'Issue type (ISD Issues)',
        'FieldFormatsName' => 'cisd_type',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_category' => [
        'Type' => 'ff_select',
        'Title' => 'Issue category (ISD Issues)',
        'FieldFormatsName' => 'cisd_category',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_subcategory' => [
        'Type' => 'ff_select',
        'Title' => 'Issue sub-category (ISD Issues)',
        'FieldFormatsName' => 'cisd_subcategory',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_pat_adm_type' => [
        'Type' => 'ff_select',
        'Title' => 'Patient admission type (ISD Issues)',
        'FieldFormatsName' => 'cisd_pat_adm_type',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_service_area' => [
        'Type' => 'ff_select',
        'Title' => 'Service area (ISD Issues)',
        'FieldFormatsName' => 'cisd_service_area',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_specialty' => [
        'Type' => 'ff_select',
        'Title' => 'Specialty (ISD Issues)',
        'FieldFormatsName' => 'cisd_specialty',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_staff_group' => [
        'Type' => 'ff_select',
        'Title' => 'Staff group (ISD Issues)',
        'FieldFormatsName' => 'cisd_staff_group',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    'cisd_staff_position' => [
        'Type' => 'ff_select',
        'Title' => 'Staff position (ISD Issues)',
        'FieldFormatsName' => 'cisd_staff_position',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'compl_isd_issues',
    ],
    // risk matrix
    'dum_com_grading' => [
        'Type' => 'riskregister',
        'Title' => _fdtk('risk_grading'),
        'RiskRow' => 'grading', // Replaces Paramaters
        'NoListCol' => true,
        'MandatoryField' => 'com_grade',
    ],
    // CCS2
    'com_affecting_tier_zero' => [
        'Type' => 'ff_select',
        'Title' => 'Incident affecting',
    ],
    'com_type_tier_one' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 1',
    ],
    'com_type_tier_two' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 2',
    ],
    'com_type_tier_three' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 3',
    ],
    'COM_SAVED_QUERIES' => [
        'Type' => 'multilistbox',
        'MaxLength' => 70,
    ],

    // contacts fields to be blocked from reports in this modules
    'link_injuries' => ['BlockFromReports' => true, 'Title' => 'Injuries (Persons)', 'NoListCol' => true],
    'link_injury1' => ['Title' => 'Injury (primary)', 'BlockFromReports' => true],
    'link_bodypart1' => ['Title' => 'Body part (primary)', 'BlockFromReports' => true],
    'com_last_updated' => [
        'Type' => 'string',
        'Title' => 'Last updated',
        'Width' => 32,
        'Computed' => true,
        'NoSearch' => true,
    ],
    'pno_type' => [
        'Type' => 'ff_select',
        'Title' => _fdtk('progress_notes_type_title'),
        'Table' => 'progress_notes',
    ],
    // SPSC
    'requested_consultant' => [
        'Type' => 'ff_select',
        'Title' => 'Requested consultant',
        'Table' => 'compl_main',
    ],
    'priority_scale' => [
        'Type' => 'ff_select',
        'Title' => 'Priority scale',
        'Table' => 'compl_main',
    ],
    'is_record_sensitive' => [
        'Type' => 'yesno',
        'Title' => 'Is this record sensitive?',
        'Table' => 'compl_main',
    ],
    'time_taken_to_submit' => [
        'Type' => 'duration',
        'Title' => 'Time Taken To Submit',
        'ReadOnly' => true,
        'Table' => 'incidents_main',
    ],
    FeedbackFields::COM_LESSON_LEARNED_SUB_CATEGORY => [
        'Type' => 'multilistbox',
        'Title' => 'Lesson learned sub category',
        'MaxLength' => 128,
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_HRO_CHARACTERISTICS => [
        'Type' => 'multilistbox',
        'Title' => 'HRO characteristics',
        'MaxLength' => 128,
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_SPECIALTY => [
        'Type' => 'ff_select',
        'Title' => 'Specialty',
        'Table' => 'compl_main',
    ],
    FeedbackFields::MCA_OR_NA => [
        'Type' => 'ff_select',
        'Title' => 'MCA or NA',
        'Table' => 'compl_main',
    ],
    FeedbackFields::SOURCE_OF_RECORD => [
        'Type' => 'ff_select',
        'Title' => 'Source of record',
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_PAT_EXPECTATIONS => [
        'Type' => 'string',
        'Title' => 'Patient Expectations',
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_PAT_UPDATE_PREF => [
        'Type' => 'ff_select',
        'Title' => 'Patient Update Preference',
        'Table' => 'compl_main',
    ],

    FeedbackFields::REFERRED_TO_OMBUDSMAN => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Was this complaint referred to the ombudsman?',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_FIRST_CONTACT => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date of first contact',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_EVIDENCE_DUE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date evidence/information due',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_EVIDENCE_SUBMITTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date evidence/information submitted',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OMBUDSMAN_REFERENCE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Ombudsman reference',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OMBUDSMAN_HANDLER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Ombudsman handler',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OMBUDSMAN_CURRENT_STAGE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Ombudsman current stage',
        'Table' => 'compl_main',
    ],
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Has the Ombudsman sent an early settlement proposal?',
        'Table' => 'compl_main',
    ],
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date of proposal received regarding early resolution',
        'Table' => 'compl_main',
    ],
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date proposal response requested',
        'Table' => 'compl_main',
    ],
    FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date proposal response submitted',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_INVESTIGATION_BEGAN => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date ombudsman investigation began',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_RESPONSE_DUE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date investigation documentation/response due',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_DOC_INV_SUB => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date investigation response documents submitted',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_INV_SUB => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date investigation response submitted',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_DRAFT_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date draft report received',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_DRAFT_RESPONSE_DUE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date draft report response due',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_REC_RECIEVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date draft recommendations reviewed',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_ACTION_PLAN => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date draft recommendation action plan agreed',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_DRAFT_REPORT_SUB => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date draft report response submitted',
        'Table' => 'compl_main',
    ],
    FeedbackFields::DATE_REPORT_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date final report received',
        'Table' => 'compl_main',
    ],
    FeedbackFields::FINAL_REP_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Ombudsman final report type',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OMBUDSMAN_OUTCOME => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Ombudsman outcome',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OMBUDSMAN_LEARNING => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Ombudsman learning',
        'Table' => 'compl_main',
        'Rows' => 10,
        'Columns' => 70,
    ],
    FeedbackFields::COM_OUTCOME_GRADING => [
        'Type' => 'ff_select',
        'Title' => 'Complaint grading at outcome',
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_SERIOUS_INCIDENT => [
        'Type' => 'yesno',
        'Title' => 'Is this linked to a serious incident?',
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_WELSH_LANGUAGE => [
        'Type' => 'yesno',
        'Title' => 'Has the complaint been made by, or on behalf of, someone to wishes to communicate through the Welsh Language?',
        'Table' => 'compl_main',
    ],
    FeedbackFields::COM_REDRESS_ESCALATED => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Has this complaint been escalated to a Redress case?',
        'Table' => 'compl_main',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBJECT => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Ombudsman subject',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUB_SUBJECT => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Ombudsman sub-subject',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_NARRATIVE => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Narrative',
        'Table' => 'com_ombudsman_subject',
        'Rows' => 10,
        'Columns' => 70,
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_OUTCOME => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Outcome',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_RECOMMENDATION => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Recommendation',
        'Table' => 'com_ombudsman_subject',
        'Rows' => 10,
        'Columns' => 70,
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_DUE_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Recommendation due date',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SUBJECT_SUBMITTED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Recommendation submitted date',
        'Table' => 'com_ombudsman_subject',
    ],

    FeedbackFields::OUTBREAK_IMPACT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Outbreak Impact',
        'Table' => 'compl_main',
    ],
    FeedbackFields::OUTBREAK_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Outbreak type',
        'Table' => 'compl_main',
    ],
    TaskFields::CREATED => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Created',
        'Table' => 'tasks',
        'ReadOnly' => true,
        'NoReadOnly' => true,
        'formatAsDateTime' => true,
    ],
    TaskFields::DUE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Due',
        'Table' => 'tasks',
    ],
    TaskFields::DESCRIPTION => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Description',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'tasks',
    ],
    TaskFields::COMPLETE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Complete',
        'Table' => 'tasks',
    ],
    TaskFields::COMPLETED => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'completed',
        'Table' => 'tasks',
        'ReadOnly' => true,
        'NoReadOnly' => true,
        'formatAsDateTime' => true,
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_LOCATION_ID => [
        'Type' => 'tree',
        'Title' => 'Location (Ombudsman)',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_SERVICE_ID => [
        'Type' => 'tree',
        'Title' => 'Service (Ombudsman)',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_RECOMMENDATIONS => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'Recommendations (Ombudsman)',
        'MaxLength' => 128,
        'Table' => 'com_ombudsman_subject',
    ],
    OmbudsmanSubjectFields::OMBUDSMAN_LESSONS_LEARNED => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'Lessons learned (Ombudsman)',
        'MaxLength' => 128,
        'Table' => 'com_ombudsman_subject',
    ],
    FeedbackFields::COM_ISSUE_PATHWAY => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Issue Pathway',
        'Table' => 'compl_subjects',
    ],
    FeedbackFields::COM_ISSUE_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Issue Type',
        'Table' => 'compl_subjects',
    ],
    FeedbackSubjectsFields::LIST_ORDER => [
        'Type' => FieldInterface::NUMBER_DB,
        'Title' => 'Order',
        'Table' => Tables::FEEDBACK_SUBJECTS,
    ],
];
