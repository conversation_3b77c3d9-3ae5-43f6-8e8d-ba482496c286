<?php

use app\models\framework\config\DatixConfig;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormSectionHelperFactory;
use Source\generic_modules\MED\MedicationFormSectionHelperFactory;
use src\complaints\model\FeedbackFields;
use src\complaints\model\FeedbackSections;
use src\complaints\model\OmbudsmanSubjectFields;
use src\component\field\DateFieldFactory;
use src\component\form\FormTable;
use src\contacts\controllers\ContactsController;
use src\contacts\controllers\SearchCriteriaController;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\medications\models\MedicationPrefixes;
use src\system\container\facade\Container;
use src\tasks\models\TaskFields;

require_once 'Source/generic_modules/COM/ModuleFunctions.php';

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$FormType = $basicFormHelper->getValidFormMode($FormType);

$sectionHelper = (new BasicFormSectionHelperFactory())->create($FormType);

$registry ??= Container::get(Registry::class);
$moduleRepository ??= Container::get(ModuleRepository::class);
$lastDReopened = DateFieldFactory::create('ReadOnly', 'lcom_last_dreopened', getLastReopenedDate($data['com_id']));
$investigationsIsLicensed = $moduleRepository->getModuleByCode('INV')->isEnabled();

$config ??= Container::get(DatixConfig::class);
$showLastChildFirst = $config->showLastChildFirst();

try {
    $ribIsLicensed = $moduleRepository->getModuleByCode('RIB')->isEnabled();
} catch (\Exception $e) {
    $ribIsLicensed = false;
}

$useFormDesignLanguage = $FormType == 'Design';
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showTimeToSubmit = $showSpscFields || $registry->getParm('TIME_TO_SUBMIT', 'N')->isTrue();
$showOmbudsmanFields = $registry->getParm('OMBUDSMAN_SECTION', 'N')->isTrue();
$showNcdsFields = $registry->getParm('COM_DATA_SUBMISSION', 'N')->isTrue();
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();
$showTasks = $registry->getParm('TASKS_ENABLED', 'N')->isTrue();
$medicationsSectionHelper = (new MedicationFormSectionHelperFactory())->create($FormType, 2, Module::FEEDBACK);

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
        'LinkType' => ($FormType == 'linkedDataSearch') ? 'COM' : '',
    ],
    'header' => [
        'Title' => _fdtk('name_ref_section', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'recordid',
                'Condition' => ($data['recordid'] || $FormType == 'Design' || $FormType == 'Search' || $FormType == 'linkedDataSearch'),
            ],
            [
                'Name' => FeedbackFields::SOURCE_OF_RECORD,
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'ReadOnly' => true,
            ],
            'com_name',
            'com_ourref',
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => 'COM',
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => 'COM',
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'com_mgr',
            'com_head',
            'com_dopened',
            'com_dclosed',
            'com_dreopened',
            [
                'Type' => 'formfield',
                'Name' => 'lcom_last_dreopened',
                'FormField' => $lastDReopened,
                'NoReadOnly' => true,
            ],
            ['Name' => 'time_taken_to_submit', 'Condition' => $showTimeToSubmit && $FormType !== 'New'],
        ],
    ],
    'details' => [
        'Title' => _fdtk('details_of', $useFormDesignLanguage) . ' ' . _fdtk('COMNameTitle', $useFormDesignLanguage),
        'Rows' => [
            ['Name' => 'com_last_updated', 'ReadOnly' => true],
            'com_method',
            'com_type',
            'com_subtype',
            'com_inc_type',
            'com_dreceived',
            'com_consent',
            'com_dincident',
            'com_detail',
            'com_purchaser',
            'com_otherref',
            'com_curstage',
            'com_outcome',
            'com_summary',
            [
                'Name' => 'flag_for_investigation',
                'ReadOnly' => $data['flag_for_investigation'] === 'Y',
                'Condition' => $investigationsIsLicensed,
            ],
            [
                'Name' => 'flag_for_rib',
                'ReadOnly' => $data['flag_for_rib'] === 'Y',
                'Condition' => $ribIsLicensed,
            ],
            ['Name' => 'requested_consultant', 'Condition' => $showSpscFields],
            ['Name' => 'priority_scale', 'Condition' => $showSpscFields],
            ['Name' => 'is_record_sensitive', 'Condition' => $showSpscFields],
            // SPSC
            ['Name' => FeedbackFields::COM_HRO_CHARACTERISTICS, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_SPECIALTY, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::MCA_OR_NA, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_PAT_EXPECTATIONS, 'Condition' => $showSpscFields],
            ['Name' => FeedbackFields::COM_PAT_UPDATE_PREF, 'Condition' => $showSpscFields],
            // NCDS
            ['Name' => FeedbackFields::COM_OUTCOME_GRADING, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_SERIOUS_INCIDENT, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_WELSH_LANGUAGE, 'Condition' => $showNcdsFields],
            ['Name' => FeedbackFields::COM_REDRESS_ESCALATED, 'Condition' => $showNcdsFields],
        ],
    ],
    FeedbackSections::OUTBREAK => [
        'Title' => _fdtk('com_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => FeedbackFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => FeedbackFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],
    'additional' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_person',
            ['Name' => FeedbackFields::REFERRED_TO_OMBUDSMAN, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::EARLY_SETTLEMENT => [
        'Title' => _fdtk('early_settlement', $useFormDesignLanguage),
        'Condition' => $registry->getParm('EARLY_SETTLEMENT', 'N')->isTrue(),
        'Rows' => [
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED,
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED,
            FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED,
        ],
    ],
    FeedbackSections::OMBUDSMAN => [
        'Title' => _fdtk('ombudsman', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_FIRST_CONTACT, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_EVIDENCE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_EVIDENCE_SUBMITTED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_REFERENCE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_HANDLER, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_CURRENT_STAGE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::EARLY_SETTLEMENT_PROPOSAL, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_INVESTIGATION_DETAILS => [
        'Title' => _fdtk('ombudsman_investigation_details', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_INVESTIGATION_BEGAN, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_RESPONSE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DOC_INV_SUB, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_INV_SUB, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_DRAFT_REPORT => [
        'Title' => _fdtk('ombudsman_draft_report', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_DRAFT_RECEIVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DRAFT_RESPONSE_DUE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_REC_RECIEVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_ACTION_PLAN, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::DATE_DRAFT_REPORT_SUB, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_FINAL_REPORT => [
        'Title' => _fdtk('ombudsman_final_report', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields,
        'Rows' => [
            ['Name' => FeedbackFields::DATE_REPORT_RECEIVED, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::FINAL_REP_TYPE, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_OUTCOME, 'Condition' => $showOmbudsmanFields],
            ['Name' => FeedbackFields::OMBUDSMAN_LEARNING, 'Condition' => $showOmbudsmanFields],
        ],
    ],
    FeedbackSections::OMBUDSMAN_SUBJECTS => [
        'Title' => _fdtk('ombudsman_subject', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN],
        'LinkedDataSection' => true,
        'Condition' => $showOmbudsmanFields && ($FormType !== FormTable::MODE_LINKED_DATA_SEARCH),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'com_ombudsman_subject'],
    ],
    FeedbackSections::OMBUDSMAN_SUBJECTS . '_design' => [
        'Title' => _fdtk('ombudsman_subject', $useFormDesignLanguage),
        'Condition' => $showOmbudsmanFields && ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => FeedbackSections::OMBUDSMAN_SUBJECTS,
        'NoSectionActions' => true,
        'Rows' => OmbudsmanSubjectFields::getFields(),
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'ko41' => [
        'Title' => 'KO41',
        'Rows' => [
            'com_ko41_type',
            'com_koservarea',
            'com_kosubject',
            'com_koprof',
            'com_koethnic_pat',
            'com_koethnic_staff',
        ],
    ],
    'subject_header' => [
        'Title' => _fdtk('subjects_section_title'),
        'LinkedDataSection' => true,
        'Rows' => [
            'com_subjects_linked',
        ],
        'Condition' => ($FormType !== 'linkedDataSearch'),
    ],
    'subject' => [
        'Title' => _fdtk('table_compl_subjects', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => (($FormType != 'Design' && $FormType != 'Print' && $FormType !== 'linkedDataSearch') || ($FormType == 'Print' && $data['com_subjects_linked'] != 'N')),
        'NoFieldAdditions' => true,
        'Invisible' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'DoSubjectSection' => [
                'controller' => src\generic\controllers\SubjectsController::class,
            ],
        ],
        'ExtraParameters' => ['subject_name' => 'com_subject'],
        'Rows' => [],
    ],
    'subject_design' => [
        'Title' => _fdtk('table_compl_subjects', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'NoSectionActions' => true,
        'AltSectionKey' => 'subject',
        'Rows' => [
            'csu_subject',
            'csu_subsubject',
            'csu_stafftype',
            'csu_location_id',
            'csu_service_id',
            'csu_outcome',
            'csu_notes',
            'csu_dcompleted',
            'com_service_area',
            'subject_type',
            'subject_subtype',
            FeedbackFields::COM_ISSUE_PATHWAY,
            FeedbackFields::COM_ISSUE_TYPE,
            ['Name' => 'csu_level_of_harm', 'Condition' => $showSpscFields],
        ],
    ],
    'isd' => [
        'Title' => 'ISD',
        'LinkedDataSection' => true,
        'Rows' => [
            'com_isd_unit',
            'com_isd_locactual',
            'com_isd_consent',
            'com_isd_dconsent_req',
            'com_isd_dconsent_rec',
            'com_isd_div_sent',
            'com_isd_ref_added',
            'com_isd_iaas_involved',
            'com_isd_cas_involved',
            'com_isd_chi_no',
            'com_isd_resp_sent_20',
            'com_isd_resp_20_reason',
            'com_isd_agree_40',
            'com_isd_agree_40_date',
            'com_isd_resp_40_reason',
            'com_isd_actions',
            'com_isd_plan',
            'com_isd_dexport',
        ],
    ],
    'issue_header' => [
        'LinkedDataSection' => true,
        'Title' => _fdtk('issues_of', $useFormDesignLanguage) . ' ' . _fdtk('COMNameTitle', $useFormDesignLanguage),
        'Rows' => [
            'com_issues_linked',
        ],
        'Condition' => $FormType != 'linkedDataSearch',
    ],
    'issue' => [
        'Title' => _fdtk('table_compl_isd_issues', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => (($FormType != 'Design' && $FormType != 'Print' && $FormType != 'linkedDataSearch') || ($FormType == 'Print' && $data['com_issues_linked'] != 'N')),
        'NoFieldAdditions' => true,
        'MandatorySection' => 'issue',
        'Include' => 'Source/generic_modules/COM/ModuleFunctions.php',
        'Function' => 'DoIssueSection',
        'NoTitle' => true,
        'ExtraParameters' => ['issue_name' => 'com_issue'],
        'Rows' => [],
    ],
    'issue_design' => [
        'Title' => _fdtk('table_compl_isd_issues', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'issue',
        'NoSectionActions' => true,
        'Rows' => [
            'cisd_type',
            'cisd_category',
            'cisd_subcategory',
            'cisd_pat_adm_type',
            'cisd_service_area',
            'cisd_specialty',
            'cisd_staff_group',
            'cisd_staff_position',
        ],
    ],
    'primary_compl_dates' => [
        'Title' => _fdtk('primary_complainant_chain', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => ($FormType == 'Search' || $FormType == 'Design' || HasPrimaryComplLinkData($data['recordid'])),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ControllerAction' => [
            'complaintDates' => [
                'controller' => src\complaints\controllers\ComplaintController::class,
            ],
        ],
        'ExtraParameters' => ['fieldset' => 74],
        'Rows' => [],
    ],
    'primary_compl_dates_history' => [
        'Title' => _fdtk('primary_complainant_chain_history', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => ($FormType != 'Search' && $FormType != 'linkedDataSearch' && ($FormType == 'Design' || (HasPrimaryComplLinkData($data['recordid']) && ShowComplaintDatesHistory($data['recordid'])))),
        'NotModes' => ['Search'],
        'ControllerAction' => [
            'complaintDatesHistory' => [
                'controller' => src\complaints\controllers\ComplaintController::class,
            ],
        ],
        'Rows' => [],
    ],
    'learning' => [
        'Title' => _fdtk('learnings', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            [
                'Name' => 'learnings_to_share',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $data['learnings_to_share'] === 'Y',

            ],
            [
                'Name' => 'learnings_title',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $data['learnings_to_share'] === 'Y',
                'NoMandatory' => true,
            ],
            [
                'Name' => 'key_learnings',
                'NoHide' => true,
                'NoOrder' => true,
                'ReadOnly' => $data['learnings_to_share'] === 'Y',
                'NoMandatory' => true,
            ],
        ],
    ],
    'feedback' => [
        'Title' => _fdtk('feedback_title', $useFormDesignLanguage),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],
    'investigation' => [
        'Title' => _fdtk('details_of_review', $useFormDesignLanguage),
        'Rows' => [
            'com_investigator',
            'com_inv_dstart',
            'com_inv_dcomp',
            'dum_com_grading',
            'com_inv_outcome',
            'com_lessons_code',
            ['Name' => FeedbackFields::COM_LESSON_LEARNED_SUB_CATEGORY, 'Condition' => $showSpscFields],
            'com_inv_lessons',
            'com_action_code',
            'com_inv_action',
        ],
    ],
    'notepad' => [
        'Title' => _fdtk('notepad', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NotModes' => ['New'],
        'Condition' => $FormType != 'linkedDataSearch',
        'Rows' => [
            'notes',
        ],
    ],
    'independent_review' => [
        'Title' => _fdtk('independant_review', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'Rows' => [
            'com_drequest',
            'com_ddueackreq',
            'com_dackreq',
            'com_dstatement',
            'com_dduelaychair',
            'com_dlaychair',
            'com_dduedecision',
            'com_ddecision',
            'com_assessor',
            'com_recir',
            'com_irsynopsis',
            'com_dinform',
            'com_dduepappt',
            'com_dpappt',
            'com_dduepdraft',
            'com_dpdraft',
            'com_ddueppublish',
            'com_dppublish',
            'com_dduecereply',
            'com_dcereply',
            'com_ircode',
        ],
    ],
    'hcc' => [
        'Title' => _fdtk('table_rev_main', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'NoFieldRemoval' => true,
        'Rows' => [
            'rev_drequested',
            'rev_reason',
            'rev_dclosed',
            'rev_ddecisiondue',
            'rev_ddecisiondone',
            'rev_init_handler',
            'rev_init_outcome',
            'rev_investigate_yn',
            'rev_init_comments',
            'rev_dhctermsdue',
            'rev_dhctermsdone',
            'rev_dtermsdue',
            'rev_dtermsdone',
            'rev_dcompleteddue',
            'rev_dcompleteddone',
            'rev_inv_handler',
            'rev_inv_outcome',
            'rev_panelreview_yn',
            'rev_inv_recommend',
            'rev_inv_action',
            'rev_dpanelrequested',
            'rev_dpanelcompleted',
            'rev_panel_outcome',
            'rev_ombudsman_yn',
            'rev_panel_recommend',
            'rev_panel_action',
            'rev_dombrequested',
            'rev_omb_assessment',
            'rev_omb_upheld',
            // 'rev_omb_reason',
            'rev_omb_handler',
            'rev_omb_recommend',
            'rev_omb_action',
        ],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'action_chains' => [
        'Title' => _fdtk('action_chains', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'getActionChains' => [
                'controller' => src\actionchains\controllers\ActionChainController::class,
            ],
        ],
        'LinkedForms' => ['action_chains' => ['module' => 'ACT']],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'tasks' => [
        'Title' => _fdtk('tasks', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH],
        'LinkedDataSection' => true,
        'Condition' => $showTasks && $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'com_tasks'],
    ],
    'tasks_design' => [
        'Title' => _fdtk('tasks', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'tasks',
        'NoSectionActions' => true,
        'Rows' => TaskFields::getFields(),
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'history' => [
        'Title' => _fdtk('notifications', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => src\email\controllers\EmailHistoryController::class,
            ],
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [],
    ],
    'word' => [
        'Title' => _fdtk('mod_templates_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            ],
        ],
        'NotModes' => ['New', 'Search', 'Print'],
        'Rows' => [],
    ],
    'linked_records' => [
        'Title' => _fdtk('linked_records', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'Special' => 'LinkedRecords',
        'Rows' => [],
    ],
    'rejection' => GenericRejectionArray('COM', $data, $useFormDesignLanguage),
    'rejection_history' => [
        'Title' => _fdtk('reasons_history_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'SectionRejectionHistory' => [
                'controller' => src\reasons\controllers\ReasonsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Condition' => bYN(GetParm('REJECT_REASON', 'Y')),
        'Rows' => [],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_COM', 'N')),
        'Rows' => [
            'com_affecting_tier_zero',
            'com_type_tier_one',
            'com_type_tier_two',
            'com_type_tier_three',
        ],
    ],
];

$ContactArray = [];
// add contact sections for each contact type.
foreach ($ModuleDefs['COM']['CONTACTTYPES'] as $ContactTypeDetails) {
    if ($FormType === FormTable::MODE_SEARCH) {
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_COM_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'LinkedDataSection' => true,
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ],
            'ExtraParameters' => [
                'link_type' => $ContactTypeDetails['Type'],
                'linkModule' => 'CON',
                'module' => 'COM',
                'sectionId' => 'contacts_type_' . $ContactTypeDetails['Type'],
            ],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => (
                CanSeeContacts('COM', $DIFPerms ?? null, $inc['rep_approved'] ?? null)
                || !$registry->getParm('DIF2_HIDE_CONTACTS', 'N')->toBool()
            ),
            'Rows' => [],
        ];
    } else {
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_COM_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'ListLinkedContacts' => [
                    'controller' => ContactsController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $ContactTypeDetails['Type']],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH
                && (
                    CanSeeContacts('COM', $DIFPerms ?? null, $inc['rep_approved'] ?? null)
                    || (
                        !$registry->getParm('DIF2_HIDE_CONTACTS', 'N')->toBool()
                        && $FormType !== FormTable::MODE_LINKED_DATA_SEARCH
                    )
                ),
            'Rows' => [],
        ];
    }
}

$ActionsArray = [];

// Decide if we are using Actions for search or display
if ($FormType === 'Search') {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'ACT', 'module' => 'COM', 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'],
        'NotModes' => ['New'],
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT']],
        'Rows' => [],
    ];
} else {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Special' => 'LinkedActions',
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT', 'carltonFormDesigns' => true]],
        'NotModes' => ['New'],
        'Condition' => ($FormType !== 'linkedDataSearch'),
        'Rows' => [],
    ];
}

$paymentsArray = [];

// Decide if we are using Payments for search or display
if ($FormType === 'Search') {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'PAY', 'module' => 'COM', 'sectionId' => 'payments'],
        'NotModes' => ['New'],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'Listings' => ['payments' => ['module' => 'PAY']],
    ];
} else {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => ['New'],
        'Condition' => $FormType !== 'linkedDataSearch',
        'Listings' => ['payments' => ['module' => 'PAY']],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'ControllerAction' => [
            'listPayments' => [
                'controller' => src\payments\controllers\PaymentController::class,
            ],
        ],
    ];
}

array_insert_datix($FormArray, 'feedback', $ContactArray);
array_insert_datix($FormArray, 'action_chains', $ActionsArray);
array_insert_datix($FormArray, 'rejection', $paymentsArray);

$FormArray = $sectionHelper->addLinkedModuleListings($FormArray);

if ($config->isFeedbackMedicationsEnabled()) {
    $FormArray['iq_medications'] = $medicationsSectionHelper->createMedicationFormSection();
    $FormArray['iq_administered_drug_design'] = $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::ADMINISTERED_FIELD_PREFIX);
    $FormArray['iq_correct_drug_design'] = $medicationsSectionHelper->createDrugFormDesignSection(MedicationPrefixes::CORRECT_FIELD_PREFIX);
    $FormArray['iq_medications_other_design'] = $medicationsSectionHelper->createOtherFieldsFormDesignSection();
    $FormArray['medication_search'] = $medicationsSectionHelper->createMedicationSearchFormSection();
}

return $FormArray;
