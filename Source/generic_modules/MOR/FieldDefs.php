<?php

use app\models\framework\modules\ModuleRepository;
use Source\generic_modules\FieldDefKeys;
use src\mortality\model\MortalityFields;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;

$investigationsIsLicensed = Container::get(ModuleRepository::class)->getModuleByCode('INV')->isEnabled();

$FieldDefs['MOR'] = [
    'recordid' => [
        'Type' => 'number',
        'Table' => 'mortality_main',
        'ReadOnly' => true,
        'Title' => 'ID',
        'Width' => 5, ],
    'dadmission' => [
        'Type' => 'date',
        'Table' => 'mortality_main',
        'Title' => 'Date of admission',
        'NotFuture' => true, ],
    'deceased_date_of_death' => [
        'Type' => 'date',
        'Title' => 'Date of death',
        'Table' => 'mortality_main',
        'ReadOnly' => true,
        'CalculatedField' => true,
        'Computed' => true, ],
    'incident_date' => [
        'Table' => 'mortality_main',
        'Type' => 'date',
        'Title' => 'Incident date',
        'NotFuture' => true, ],
    'summary' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Summary', ],
    'dreview' => [
        'Table' => 'mortality_main',
        'Type' => 'date',
        'Title' => 'Date of review', ],
    'death_expected' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Was the death expected?	', ],
    'death_avoidable' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Was the death avoidable?', ],
    'admission_method' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Admission method', ],
    'admitting_diagnosis' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Admitting diagnosis', ],
    'cause_death_a' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Cause of death I(a)', ],
    'cause_death_b' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Cause of death I(b)', ],
    'cause_death_c' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Cause of death I(c)', ],
    'cause_death_d' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Cause of death I(d)', ],
    'cause_death_e' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Cause of death II', ],
    'co_morbidities' => [
        'Table' => 'mortality_main',
        'Type' => 'multilistbox',
        'Title' => 'Co-morbidities',
        'MaxLength' => 252, ],
    'dmeeting' => [
        'Table' => 'mortality_main',
        'Type' => 'date',
        'Title' => 'Date of meeting', ],
    'situation' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Situation', ],
    'background' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Background', ],
    'assessment' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Assessment', ],
    'recommendation' => [
        'Table' => 'mortality_main',
        'Type' => 'textarea',
        'Rows' => 7,
        'Columns' => 70,
        'Title' => 'Recommendation', ],
    'ourref' => [
        'Table' => 'mortality_main',
        'Type' => 'string',
        'Title' => 'Ref',
        'Width' => 32,
        'MaxLength' => 32, ],
    'name' => [
        'Table' => 'mortality_main',
        'Type' => 'string',
        'Title' => 'Name',
        'Width' => 70,
        'MaxLength' => 128,
        'UpperCase' => true, ],
    'reviewer' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Reviewer',
        'StaffField' => true,
    ],
    'admission_location_id' => [
        'Type' => 'tree',
        'Title' => 'Location admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'mortality_main',
    ],
    'admission_location_id_tag' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Location admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'admission_location_id',
    ],
    'admission_service_id' => [
        'Type' => 'tree',
        'Title' => 'Service admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'mortality_main',
    ],
    'admission_service_id_tag' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Service admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'admission_service_id',
    ],
    'other_location' => [
        'Type' => 'tree',
        'Title' => 'Other location',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'mortality_main',
    ],
    'other_location_tag' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Other location tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'other location',
    ],
    'confirm_location_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Location?',
        'NoListCol' => true,
        'Table' => 'mortality_main',
    ],
    'confirm_service_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Service?',
        'NoListCol' => true,
        'Table' => 'mortality_main',
    ],
    'other_service' => [
        'Type' => 'tree',
        'Title' => 'Other service',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'mortality_main',
    ],
    'other_service_tag' => [
        'Table' => 'mortality_main',
        'Type' => 'ff_select',
        'Title' => 'Other service tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'other_service',
    ],
    'inquest_held' => [
        'Type' => 'yesno',
        'Title' => 'Inquest held?',
        'NoListCol' => true,
        'Table' => 'mortality_main',
    ],
    'considerations' => [
        'Type' => 'multilistbox',
        'Title' => 'Did the mortality involve any of the following?',
        'Table' => 'mortality_main',
        'MaxLength' => 254,
    ],
    'score' => [
        'Type' => 'ff_select',
        'Title' => 'Score',
        'Table' => 'mortality_main',
    ],
    'mandatory_review' => [
        'Type' => 'yesno',
        'Title' => 'Does this require a mandatory review?',
        'Table' => 'mortality_main',
    ],
    'closed_date' => [
        'Table' => 'mortality_main',
        'Title' => 'Closed date',
        'Type' => 'date',
        'NotFuture' => true,
        'NotEarlierThan' => [
            'com_dopened', ],
    ],
    'inquest_start_date' => [
        'Type' => 'date',
        'Title' => 'Inquest start date',
        'Table' => 'mortality_main',
    ],
    'inquest_end_date' => [
        'Type' => 'date',
        'Title' => 'Inquest end date',
        'Table' => 'mortality_main',
    ],
    'expected_verdict' => [
        'Type' => 'ff_select',
        'Title' => 'Expected verdict',
        'Table' => 'mortality_main',
    ],
    'actual_verdict' => [
        'Type' => 'ff_select',
        'Title' => 'Actual verdict',
        'Table' => 'mortality_main',
    ],
    'jury_present' => [
        'Type' => 'yesno',
        'Title' => 'Was a jury present?',
        'Table' => 'mortality_main',
    ],
    'pofd_report_date' => [
        'Type' => 'date',
        'Title' => 'Prevention of future deaths report date?',
        'Table' => 'mortality_main',
    ],
    'pm_recieved_date' => [
        'Type' => 'date',
        'Title' => 'Post-mortem report received date?',
        'Table' => 'mortality_main',
    ],
    'last_updated' => [
        'Table' => 'mortality_main',
        'Type' => 'string',
        'Title' => 'Last updated',
        'Width' => 32,
        'Computed' => true,
        'NoSearch' => true,
    ],
    'rep_approved' => ['Type' => 'ff_select',
        'Table' => 'mortality_main',
        'Title' => 'Approval status',
        'Width' => 32,
    ],
    'notes' => [
        'Type' => 'textarea',
        'Table' => 'notepad',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
    ],
    'flag_for_investigation' => [
        'Type' => 'ff_select',
        'Title' => 'Flag for investigation?',
        'Table' => 'mortality_main',
        'NoListCol' => !$investigationsIsLicensed,
        'BlockFromReports' => !$investigationsIsLicensed,
    ],
    'show_document' => [
        'Type' => 'checkbox',
        'NoListCol' => true,
        'Table' => 'mortality_main',
    ],
    // stage 1 questions
    'stage1_death_cert_wording' => [
        'Type' => 'ff_select',
        'Title' => 'Are you satisfied that the wording on the Death Certificate adequately reflects the cause of death?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage1_coroner_uncertain' => [
        'Type' => 'ff_select',
        'Title' => 'Has the death been referred to the Coroner because of uncertainty about its cause, or the circumstances leading up to it?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage1_death_anticipated' => [
        'Type' => 'ff_select',
        'Title' => 'Was this death anticipated, given the clinical presentation of the patient at admission?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage1_evidence' => [
        'Type' => 'multilistbox',
        'Title' => 'Is there evidence of the following?',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage1_evidence_other_details' => [
        'Type' => 'string',
        'Title' => 'Other details',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage1_concern_care' => [
        'Type' => 'ff_select',
        'Title' => 'Is there any indication of concerns from family or carers about the care this patient received?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage1_concerns_raised' => [
        'Type' => 'ff_select',
        'Title' => "Is there any evidence of documented concerns about this patient's care raised by other health professionals?",
        'Width' => 32,
        'Table' => 'mortality_main',
    ],

    // stage 2
    'stage2_unpl_prior' => [
        'Type' => 'ff_select',
        'Title' => 'Unplanned prior admission within 30 days of the admission as a result of any healthcare management',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_hospital_incu' => [
        'Type' => 'ff_select',
        'Title' => 'Hospital-incurred patient accident or injury',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_hospital_incu_a' => [
        'Type' => 'multilistbox',
        'Title' => 'Hospital-incurred patient accident/injury: Yes, specify',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_hospital_incu_a_o_d' => [
        'Type' => 'string',
        'Title' => 'Hospital-incurred patient accident/injury: Other details',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_adverse_drug' => [
        'Type' => 'ff_select',
        'Title' => 'Adverse drug reaction, side effect or drug error',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_unpl_trans_a' => [
        'Type' => 'multilistbox',
        'Title' => 'Were there any unplanned transfers',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_unpl_trans_a_o_d' => [
        'Type' => 'string',
        'Title' => 'Were there any unplanned transfers: Other details',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_unpl_return' => [
        'Type' => 'ff_select',
        'Title' => 'Was there an unplanned return to the theatre on this admission?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_unpl_visit' => [
        'Type' => 'ff_select',
        'Title' => 'Was there an unplanned visit to the operating theatre on this admission?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_unpl_removal' => [
        'Type' => 'ff_select',
        'Title' => 'Was there an unplanned removal, injury or repair of organ or structure during surgery, invasive procedure or vaginal delivery?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_patient_compl' => [
        'Type' => 'ff_select',
        'Title' => 'Other patient complications to include MI, DVT, CVA',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_patient_compl_a' => [
        'Type' => 'multilistbox',
        'Title' => 'MI/DVT/CVA: Yes, specify',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_patient_compl_a_o_d' => [
        'Type' => 'string',
        'Title' => 'MI/DVT/CVA: Other details',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_neuro_defect' => [
        'Type' => 'ff_select',
        'Title' => 'Development of neurological defect not present on admission',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_un_death_dis' => [
        'Type' => 'ff_select',
        'Title' => 'Unexpected death (i.e. not an expected outcome of the disease during hospitalisation), or referral to Coroner',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_cardiac_resp' => [
        'Type' => 'ff_select',
        'Title' => 'Cardiac/respiratory arrest',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_hcare_inf' => [
        'Type' => 'ff_select',
        'Title' => 'Was there a Healthcare Associated Infection?',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_hcare_inf_a' => [
        'Type' => 'multilistbox',
        'Title' => 'HAI: Yes, specify',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_hcare_inf_a_o_d' => [
        'Type' => 'string',
        'Title' => 'HAI: Other details',
        'MaxLength' => 252,
        'Table' => 'mortality_main',
    ],
    'stage2_family_diss' => [
        'Type' => 'ff_select',
        'Title' => 'Patients family dissatisfied with care received documented in the medical record and/or evidence of complaint',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_doc_lit' => [
        'Type' => 'ff_select',
        'Title' => 'Documentation or correspondence indicating litigation, either contemplated or actual, for example solicitors letter',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_delay_treatment' => [
        'Type' => 'ff_select',
        'Title' => 'Delays in, or cancellation of treatment',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_evidence_in_dec' => [
        'Type' => 'ff_select',
        'Title' => 'Evidence of inappropriate decision making regarding the treatment or intervention the patient received',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_comm_prob' => [
        'Type' => 'ff_select',
        'Title' => 'Problems relating to communication, either failure to communicate directly with the patient or regarding the patient',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_doc_prob' => [
        'Type' => 'ff_select',
        'Title' => 'Problems with documentation such as insufficient record, discrepancies between notes, and missing documentation resulting in problems in care',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_incorrect_diag' => [
        'Type' => 'ff_select',
        'Title' => 'Missed or incorrect diagnosis',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_o_undes_out' => [
        'Type' => 'ff_select',
        'Title' => 'Any other undesirable outcomes (not covered by other criteria)',
        'Width' => 32,
        'Table' => 'mortality_main',
    ],

    'stage2_o_undes_out_p_d' => [
        'Type' => 'textarea',
        'Title' => 'Other undesirable outcomes: Yes, specify',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'mortality_main',
    ],

    // stage 2 - causes of death
    'stage2_death_caused_prob' => [
        'Type' => 'ff_select',
        'Title' => "In your view, was the patient\'s death caused by a problem or problems in healthcare?",
        'Width' => 32,
        'Table' => 'mortality_main',
    ],
    'stage2_death_contr_prob' => [
        'Type' => 'ff_select',
        'Title' => "Did a problem or problems in care contribute to the patient\'s death?",
        'Width' => 32,
        'Table' => 'mortality_main',
    ],

    'stage2_death_caused_prob_p_d' => [
        'Type' => 'textarea',
        'Title' => "Patient's death caused by problem in healthcare: Yes, specify",
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'mortality_main',
    ],
    'stage2_death_contr_prob_p_d' => [
        'Type' => 'textarea',
        'Title' => "Problem in care contributed to patient's death: Yes, specify",
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'mortality_main',
    ],
    'lessons_learned' => [
        'Type' => 'textarea',
        'Title' => 'lessons_learned',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'mortality_main',
    ],
    'outcomes' => [
        'Type' => 'textarea',
        'Title' => 'outcomes',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'mortality_main',
    ],
    'MOR_SAVED_QUERIES' => [
        'Type' => 'multilistbox',
        'MaxLength' => 70,
    ],
    'pno_type' => [
        'Type' => 'ff_select',
        'Title' => _fdtk('progress_notes_type_title'),
        'Table' => 'progress_notes',
    ],
    // SPSC
    MortalityFields::MAM_MORTALITY_LOCATION_DIFFERENT_TO_EVENT_LOCATION => [
        'Type' => 'ff_select',
        'Title' => 'M&M - Is the Mortality Location different than the Event Location?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MAM_WERE_INTERVENTIONS_PERFORMED_NECESSARY => [
        'Type' => 'ff_select',
        'Title' => 'M&M - If No CPR case or Terminal Case, were all the interventions performed on the patient necessary?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MAM_DISCUSS_CASE_AT_MAM => [
        'Type' => 'ff_select',
        'Title' => 'M&M - Does this case need to be discussed at the Departmental M&M committee?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::CATEGORY => [
        'Type' => 'ff_select',
        'Title' => 'Category',
        'Table' => 'mortality_main',
    ],
    MortalityFields::SUB_CATEGORY => [
        'Type' => 'ff_select',
        'Title' => 'Sub-Category',
        'Table' => 'mortality_main',
    ],
    MortalityFields::CAUSES_OF_DEATH => [
        'Type' => 'multilistbox',
        'Title' => 'Causes of Death',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::SUB_CAUSES => [
        'Type' => 'multilistbox',
        'Title' => 'Sub Causes',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::COMPLICATIONS_CAUSES => [
        'Type' => 'multilistbox',
        'Title' => 'Complications causes',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::SUB_COMPLICATIONS => [
        'Type' => 'multilistbox',
        'Title' => 'Sub Complications',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::NON_CARDIOVASCULAR_CAUSES => [
        'Type' => 'multilistbox',
        'Title' => 'Non Cardiovascular Causes',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::SURGICAL_COMPLICATIONS => [
        'Type' => 'multilistbox',
        'Title' => 'Surgical Complications',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_WITHIN_48_HOURS_OF_SURGICAL_OR_INVASIVE_PROCEDURE => [
        'Type' => 'ff_select',
        'Title' => 'Death within 48 hours of a surgical or invasive procedure?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DIAGNOSTIC_WORKUP_ADEQUATE => [
        'Type' => 'ff_select',
        'Title' => 'Was the diagnostic workup adequate?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_WITHIN_48_HOURS_OF_ADMISSION => [
        'Type' => 'ff_select',
        'Title' => 'Death within 48 hours of admission?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::TERMINAL_ILLNESS_KNOWN_ON_ADMISSION => [
        'Type' => 'ff_select',
        'Title' => 'Did patient have a known terminal illness upon admission?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ABNORMAL_TEST_RESULTS_ADDRESSED => [
        'Type' => 'ff_select',
        'Title' => 'Were abnormal labs, x-ray, other test results or physical findings addressed?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::PATIENT_UNDER_50 => [
        'Type' => 'ff_select',
        'Title' => 'Patient under 50 years of age?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_ASSOCIATED_WITH_DRUG_REACTION => [
        'Type' => 'ff_select',
        'Title' => 'Death associated with drug reaction?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_ASSOCIATED_WITH_ADVERSE_EVENT => [
        'Type' => 'ff_select',
        'Title' => 'Death associated with adverse event?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_PREVENTABLE_UNDER_OPTIMAL_CONDITIONS => [
        'Type' => 'ff_select',
        'Title' => 'Under optimal conditions would this death have been preventable?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::TERMINAL_EVENTS_ANTICIPATED => [
        'Type' => 'ff_select',
        'Title' => 'Were terminal events/morbidity anticipated?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DIAGNOSTIC_WORKUP_BEFORE_AND_AFTER_EVENT_ADEQUATE_TIMELY => [
        'Type' => 'ff_select',
        'Title' => 'Was the diagnostic workup before and after the event adequate & timely?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DOCUMENTATION_DEFICIENCY => [
        'Type' => 'ff_select',
        'Title' => 'Documentation deficiency?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MEDICATION_ERROR => [
        'Type' => 'ff_select',
        'Title' => 'Medication error?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::HOSPITAL_ACQUIRED_INFECTION => [
        'Type' => 'ff_select',
        'Title' => 'Hospital acquired infection?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::EXTENDED_LENGTH_OF_STAY => [
        'Type' => 'ff_select',
        'Title' => 'Extended length of stay',
        'Table' => 'mortality_main',
    ],
    MortalityFields::UNPLANNED_RETURN_TO_OR => [
        'Type' => 'ff_select',
        'Title' => 'Unplanned Return to OR',
        'Table' => 'mortality_main',
    ],
    MortalityFields::UNPLANNED_RETURN_TO_ICU => [
        'Type' => 'ff_select',
        'Title' => 'Unplanned return/admission to ICU',
        'Table' => 'mortality_main',
    ],
    MortalityFields::FAMILY_PATIENT_COMPLAINT => [
        'Type' => 'ff_select',
        'Title' => 'Family/patient complaint',
        'Table' => 'mortality_main',
    ],
    MortalityFields::OPPORTUNITY_REPORT_INITIATED => [
        'Type' => 'ff_select',
        'Title' => 'An opportunity report initiated?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::PREVENTATIVE_MEASURES_ADEQUATE => [
        'Type' => 'ff_select',
        'Title' => 'Were preventive measures adequate & timely',
        'Table' => 'mortality_main',
    ],
    MortalityFields::TREATMENT_ADEQUATE => [
        'Type' => 'ff_select',
        'Title' => 'Was treatment / response / intervention adequate & timely?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::EOL_PREPARATION_MET => [
        'Type' => 'ff_select',
        'Title' => 'In expected death and palliative cases were end of life preparation met and adequate?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::FUTURE_CORRECTIVE_MEASURES_REQUIRED => [
        'Type' => 'ff_select',
        'Title' => 'Are future corrective / preventive measures required?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ABNORMAL_INVESTIGATIONS_RESULT_OR_PHYSICAL_ADMISSION => [
        'Type' => 'ff_select',
        'Title' => 'Were abnormal investigations result or physical admission?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::QUALITY_OF_CARE_ISSUE_IDENTIFIED => [
        'Type' => 'ff_select',
        'Title' => 'Were any quality of care issue identified?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEATH_DUE_TO_SURGERY => [
        'Type' => 'ff_select',
        'Title' => 'Was a death due to a surgery?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::RRT_CALLED => [
        'Type' => 'ff_select',
        'Title' => 'Was the rapid response team (RRT) called?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::PROGNOSIS_DISCUSSED_WITH_FAMILY => [
        'Type' => 'ff_select',
        'Title' => 'Was Prognosis clearly discussed with family based on documentation?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DEVIATION_FROM_STANDARD_CARE => [
        'Type' => 'ff_select',
        'Title' => 'Any deviation from standards of care',
        'Table' => 'mortality_main',
    ],
    MortalityFields::EQUIPMENT_FAILURE_IDENTIFIED => [
        'Type' => 'ff_select',
        'Title' => 'Equipment failure identified',
        'Table' => 'mortality_main',
    ],
    MortalityFields::IATROGENIC_EVENT => [
        'Type' => 'ff_select',
        'Title' => 'Iatrogenic event',
        'Table' => 'mortality_main',
    ],
    MortalityFields::CAUSE_OF_DEATH_IDENTIFIED => [
        'Type' => 'ff_select',
        'Title' => 'Cause of Death identified',
        'Table' => 'mortality_main',
    ],
    MortalityFields::WHAT_INTERNAL_FACTORS_CONTRIBUTED_TO_DEATH => [
        'Type' => 'ff_select',
        'Title' => 'What internal factors contributed to the death or morbidity?',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_LESSONS => [
        'Type' => 'multilistbox',
        'Title' => 'Lessons Code',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_LESSONS_SUBCAT => [
        'Type' => 'multilistbox',
        'Title' => 'Lessons Learned Sub Category',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_HRO_CHARACTERISTICS => [
        'Type' => 'multilistbox',
        'Title' => 'HRO Characteristics',
        'MaxLength' => 128,
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_SPECIALTY => [
        'Type' => 'ff_select',
        'Title' => 'Specialty',
        'Table' => 'mortality_main',
    ],
    MortalityFields::SOURCE_OF_RECORD => [
        'Type' => 'ff_select',
        'Title' => 'Source of record',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ADMITTING_PHYSICIAN => [
        'Type' => 'string',
        'Title' => 'Admitting Physician',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ATTENDING_PHYSICIAN => [
        'Type' => 'string',
        'Title' => 'Attending Physician',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ROOM_NO => [
        'Type' => 'string',
        'Title' => 'Room No.',
        'Table' => 'mortality_main',
    ],
    MortalityFields::BED_NO => [
        'Type' => 'string',
        'Title' => 'Bed No.',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ADMITTING_SERVICE => [
        'Type' => 'string',
        'Title' => 'Admitting Service',
        'Table' => 'mortality_main',
    ],
    MortalityFields::ATTENDING_SERVICE => [
        'Type' => 'string',
        'Title' => 'Attending Service',
        'Table' => 'mortality_main',
    ],
    MortalityFields::DIAGNOSTIC => [
        'Type' => 'ff_select',
        'Title' => 'Diagnostic',
        'Table' => 'mortality_main',
    ],
    MortalityFields::PROCEDURES => [
        'Type' => 'ff_select',
        'Title' => 'Procedures',
        'Table' => 'mortality_main',
    ],
    MortalityFields::OUTBREAK_IMPACT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Outbreak impact',
        'Table' => 'mortality_main',
    ],
    MortalityFields::OUTBREAK_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Outbreak type',
        'Table' => 'mortality_main',
    ],
    // CCS2
    MortalityFields::MOR_AFFECTING_TIER_ZERO => [
        'Type' => 'ff_select',
        'Title' => 'Incident affecting',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_TYPE_TIER_ONE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 1',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_TYPE_TIER_TWO => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 2',
        'Table' => 'mortality_main',
    ],
    MortalityFields::MOR_TYPE_TIER_THREE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 3',
        'Table' => 'mortality_main',
    ],
];
