<?php

use src\mortality\model\MortalityFields;
use src\mortality\model\MortalitySections;

$GLOBALS['FormTitle'][7] = _fdtk('mor2_title');

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'admission_location_id' => true,
    'admission_service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'admission_location_id' => false,
    'admission_service_id' => false,
    'other_location' => false,
    'other_service' => false,
];

$GLOBALS['ExpandSections'] = [
    'inquest_held' => [
        0 => [
            'section' => 'inquest',
            'alerttext' => '',
            'values' => [0 => 'Y'],
        ],
    ],
    'rep_approved' => [
        0 => [
            'section' => 'rejection',
            'alerttext' => 'Please complete the \'Details of rejection\' section before saving this form.',
            'values' => [0 => 'REJECT'],
        ],
    ],
];

$GLOBALS['ExpandFields'] = [
    'stage1_death_anticipated' => [
        0 => [
            'field' => 'stage1_evidence',
            'values' => [0 => 'Y'],
        ],
    ],
    'stage1_evidence' => [
        0 => [
            'field' => 'stage1_evidence_other_details',
            'values' => [0 => 'ST1314'],
        ],
    ],

    // stage 2
    'stage2_hospital_incu' => [
        0 => [
            'field' => 'stage2_hospital_incu_a',
            'values' => [0 => 'Y'],
        ],
    ],
    'stage2_patient_compl' => [
        0 => [
            'field' => 'stage2_patient_compl_a',
            'values' => [0 => 'Y'],
        ],
    ],
    'stage2_hcare_inf' => [
        0 => [
            'field' => 'stage2_hcare_inf_a',
            'values' => [0 => 'Y'],
        ],
    ],
    'stage2_o_undes_out' => [
        0 => [
            'field' => 'stage2_o_undes_out_p_d',
            'values' => [0 => 'Y'],
        ],
    ],

    'stage2_hospital_incu_a' => [
        0 => [
            'field' => 'stage2_hospital_incu_a_o_d',
            'values' => [0 => 'ST2HI4'],
        ],
    ],
    'stage2_unpl_trans_a' => [
        0 => [
            'field' => 'stage2_unpl_trans_a_o_d',
            'values' => [0 => 'ST2UT3'],
        ],
    ],
    'stage2_patient_compl_a' => [
        0 => [
            'field' => 'stage2_patient_compl_a_o_d',
            'values' => [0 => 'ST2PC4'],
        ],
    ],
    'stage2_hcare_inf_a' => [
        0 => [
            'field' => 'stage2_hcare_inf_a_o_d',
            'values' => [0 => 'ST2HI5'],
        ],
    ],

    // stage 2 - cause of death
    'stage2_death_caused_prob' => [
        0 => [
            'field' => 'stage2_death_caused_prob_p_d',
            'values' => [0 => 'Y'],
        ],
    ],
    'stage2_death_contr_prob' => [
        0 => [
            'field' => 'stage2_death_contr_prob_p_d',
            'values' => [0 => 'Y'],
        ],
    ],
];

$GLOBALS['UserExtraText'] = [
    'dum_fbk_to' => ['7' => 'Only staff and contacts with e-mail addresses are shown.'],
    'dum_fbk_gab' => ['7' => 'Only users with e-mail addresses are shown.'],
    'dum_fbk_email' => ['7' => 'Enter e-mail addresses of other recipients not listed above. You can<br />enter multiple addresses, separated by commas.'],
    'flag_for_investigation' => ['7' => 'Selecting yes will submit this record for investigation review.'],
];

$GLOBALS['HideFields'] = [
    MortalityFields::MAM_MORTALITY_LOCATION_DIFFERENT_TO_EVENT_LOCATION => true,
    MortalityFields::MAM_WERE_INTERVENTIONS_PERFORMED_NECESSARY => true,
    MortalityFields::MAM_DISCUSS_CASE_AT_MAM => true,
    MortalityFields::CATEGORY => true,
    MortalityFields::SUB_CATEGORY => true,
    MortalityFields::CAUSES_OF_DEATH => true,
    MortalityFields::SUB_CAUSES => true,
    MortalityFields::COMPLICATIONS_CAUSES => true,
    MortalityFields::SUB_COMPLICATIONS => true,
    MortalityFields::NON_CARDIOVASCULAR_CAUSES => true,
    MortalityFields::SURGICAL_COMPLICATIONS => true,
    MortalityFields::DEATH_WITHIN_48_HOURS_OF_SURGICAL_OR_INVASIVE_PROCEDURE => true,
    MortalityFields::DIAGNOSTIC_WORKUP_ADEQUATE => true,
    MortalityFields::DEATH_WITHIN_48_HOURS_OF_ADMISSION => true,
    MortalityFields::TERMINAL_ILLNESS_KNOWN_ON_ADMISSION => true,
    MortalityFields::ABNORMAL_TEST_RESULTS_ADDRESSED => true,
    MortalityFields::PATIENT_UNDER_50 => true,
    MortalityFields::DEATH_ASSOCIATED_WITH_DRUG_REACTION => true,
    MortalityFields::DEATH_ASSOCIATED_WITH_ADVERSE_EVENT => true,
    MortalityFields::DEATH_PREVENTABLE_UNDER_OPTIMAL_CONDITIONS => true,
    MortalityFields::TERMINAL_EVENTS_ANTICIPATED => true,
    MortalityFields::DIAGNOSTIC_WORKUP_BEFORE_AND_AFTER_EVENT_ADEQUATE_TIMELY => true,
    MortalityFields::DOCUMENTATION_DEFICIENCY => true,
    MortalityFields::MEDICATION_ERROR => true,
    MortalityFields::HOSPITAL_ACQUIRED_INFECTION => true,
    MortalityFields::EXTENDED_LENGTH_OF_STAY => true,
    MortalityFields::UNPLANNED_RETURN_TO_OR => true,
    MortalityFields::UNPLANNED_RETURN_TO_ICU => true,
    MortalityFields::FAMILY_PATIENT_COMPLAINT => true,
    MortalityFields::OPPORTUNITY_REPORT_INITIATED => true,
    MortalityFields::PREVENTATIVE_MEASURES_ADEQUATE => true,
    MortalityFields::TREATMENT_ADEQUATE => true,
    MortalityFields::EOL_PREPARATION_MET => true,
    MortalityFields::FUTURE_CORRECTIVE_MEASURES_REQUIRED => true,
    MortalityFields::ABNORMAL_INVESTIGATIONS_RESULT_OR_PHYSICAL_ADMISSION => true,
    MortalityFields::QUALITY_OF_CARE_ISSUE_IDENTIFIED => true,
    MortalityFields::DEATH_DUE_TO_SURGERY => true,
    MortalityFields::RRT_CALLED => true,
    MortalityFields::PROGNOSIS_DISCUSSED_WITH_FAMILY => true,
    MortalityFields::DEVIATION_FROM_STANDARD_CARE => true,
    MortalityFields::EQUIPMENT_FAILURE_IDENTIFIED => true,
    MortalityFields::IATROGENIC_EVENT => true,
    MortalityFields::CAUSE_OF_DEATH_IDENTIFIED => true,
    MortalityFields::WHAT_INTERNAL_FACTORS_CONTRIBUTED_TO_DEATH => true,
    MortalityFields::MOR_LESSONS => true,
    MortalityFields::MOR_LESSONS_SUBCAT => true,
    MortalityFields::MOR_HRO_CHARACTERISTICS => true,
    MortalityFields::MOR_SPECIALTY => true,
    MortalityFields::SOURCE_OF_RECORD => true,
    MortalityFields::ADMITTING_PHYSICIAN => true,
    MortalityFields::ATTENDING_PHYSICIAN => true,
    MortalityFields::ROOM_NO => true,
    MortalityFields::BED_NO => true,
    MortalityFields::ADMITTING_SERVICE => true,
    MortalityFields::ATTENDING_SERVICE => true,
    MortalityFields::DIAGNOSTIC => true,
    MortalityFields::PROCEDURES => true,
    MortalitySections::OUTBREAK => true,
    MortalityFields::OUTBREAK_IMPACT => true,
    MortalityFields::OUTBREAK_TYPE => true,
    'ccs2' => true,
];
