<?php

use app\models\contact\ContactTypes;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\forms\RecordHeaderProvider;
use Source\generic_modules\ModuleDefKeys;
use src\framework\registry\Registry;
use src\mortality\model\MortalityFields;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$spscDataset = $registry->getParm('SPSC_DATASET')->isTrue();
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS')->isTrue();

$ModuleDefs['MOR'] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => 'MOR',
    'IS_MAIN_MODULE' => true,
    'AGE_AT_DATE' => 'deceased_date_of_death',
    ModuleDefKeys::LOCATION_FIELD => MortalityFields::LOCATION_ID,
    ModuleDefKeys::SERVICE_FIELD => MortalityFields::SERVICE_ID,
    'MOD_ID' => MOD_MORTALITY,
    'CODE' => 'MOR',
    'STAFF_EMPL_FILTER_MAPPINGS' => [
        'location' => 'admission_location_id',
        'service' => 'admission_service_id',
    ],
    'NAME_FIELD' => 'name',
    'APPROVAL_LEVELS' => true,
    'USES_APPROVAL_STATUSES' => true,
    'CAN_CREATE_EMAIL_TEMPLATES' => true,
    'NAME' => _fdtk('mod_mortality_title'),
    'TABLE' => 'mortality_main',
    'HAS_DEFAULT_LISTING' => true,
    RecordHeaderProvider::APPVARS_KEY => [
        'recordid',
        'ourref',
        'name',
        'deceased_date_of_death',
    ],
    'REC_NAME' => _fdtk('MORName'),
    'REC_NAME_PLURAL' => _fdtk('MORNames'),
    'RECORD_NAME_FROM_CONTACT' => ContactTypes::POLICE_OFFICER,
    'FK' => 'mor_id',
    'ACTION' => 'record&module=MOR',
    'SEARCH_URL' => 'action=search&module=MOR',
    'SHOW_EMAIL_GLOBAL' => 'MOR_SHOW_EMAIL',
    'EMAIL_REPORTER_GLOBAL' => 'MOR_EMAIL_REPORTER',
    'MOR_EMAIL_HDLR' => 'MOR_EMAIL_HDLR',
    'EMAIL_HANDLER_GLOBAL' => 'EMAIL_HANDLER_GLOBAL',
    'PERM_GLOBAL' => 'MOR_PERMS',
    'NO_LEVEL1_GLOBAL' => 'MOR_NO_OPEN',
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'documents',
    'ADD_NEW_RECORD_LEVELS' => ['MOR2', 'MOR1'],
    'LOGGED_OUT_LEVEL1' => true,
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'MOR1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::MORTALITY_REVIEW_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'MOR2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::MORTALITY_REVIEW_LEVEL_2],
    ],
    'ICON' => 'icons/icon_MOR.png',
    'FIELD_ARRAY' => [
        'dadmission',
        'incident_date',
        'summary',
        'dreview',
        'death_expected',
        'death_avoidable',
        'admission_method',
        'admitting_diagnosis',
        'cause_death_a',
        'cause_death_b',
        'cause_death_c',
        'cause_death_d',
        'cause_death_e',
        'co_morbidities',
        'dmeeting',
        'situation',
        'background',
        'assessment',
        'recommendation',
        'ourref',
        'name',
        'rep_approved',
        'deceased_date_of_death',
        'reviewer',
        'admission_location_id',
        'admission_service_id',
        'other_location',
        'other_service',
        'confirm_location_id',
        'confirm_service_id',
        'inquest_held',
        'inquest_start_date',
        'inquest_end_date',
        'expected_verdict',
        'actual_verdict',
        'jury_present',
        'pofd_report_date',
        'pm_recieved_date',
        'last_updated',
        'flag_for_investigation',
        'show_document',
        'stage1_death_cert_wording',
        'stage1_coroner_uncertain',
        'stage1_death_anticipated',
        'stage1_evidence',
        'stage1_evidence_other_details',
        'stage1_concern_care',
        'stage1_concerns_raised',
        'closed_date',

        // stage 2
        'stage2_unpl_prior',
        'stage2_hospital_incu',
        'stage2_adverse_drug',
        'stage2_unpl_return',
        'stage2_unpl_visit',
        'stage2_unpl_removal',
        'stage2_patient_compl',
        'stage2_neuro_defect',
        'stage2_un_death_dis',
        'stage2_cardiac_resp',
        'stage2_hcare_inf',
        'stage2_family_diss',
        'stage2_doc_lit',
        'stage2_delay_treatment',
        'stage2_evidence_in_dec',
        'stage2_comm_prob',
        'stage2_doc_prob',
        'stage2_incorrect_diag',
        'stage2_o_undes_out',

        'stage2_hospital_incu_a',
        'stage2_unpl_trans_a',
        'stage2_patient_compl_a',
        'stage2_hcare_inf_a',

        'stage2_hospital_incu_a_o_d',
        'stage2_unpl_trans_a_o_d',
        'stage2_patient_compl_a_o_d',
        'stage2_hcare_inf_a_o_d',

        'stage2_o_undes_out_p_d',

        // stage 2 - cause of death
        'stage2_death_caused_prob',
        'stage2_death_contr_prob',

        'stage2_death_caused_prob_p_d',
        'stage2_death_contr_prob_p_d',

        'lessons_learned',
        'outcomes',
        'considerations',
        'score',
        'mandatory_review',

        'updateid',
        'updateddate',
        'updatedby',
        // SPSC
        MortalityFields::MAM_MORTALITY_LOCATION_DIFFERENT_TO_EVENT_LOCATION,
        MortalityFields::MAM_WERE_INTERVENTIONS_PERFORMED_NECESSARY,
        MortalityFields::MAM_DISCUSS_CASE_AT_MAM,
        MortalityFields::CATEGORY,
        MortalityFields::SUB_CATEGORY,
        MortalityFields::CAUSES_OF_DEATH,
        MortalityFields::SUB_CAUSES,
        MortalityFields::COMPLICATIONS_CAUSES,
        MortalityFields::SUB_COMPLICATIONS,
        MortalityFields::NON_CARDIOVASCULAR_CAUSES,
        MortalityFields::SURGICAL_COMPLICATIONS,
        MortalityFields::DEATH_WITHIN_48_HOURS_OF_SURGICAL_OR_INVASIVE_PROCEDURE,
        MortalityFields::DIAGNOSTIC_WORKUP_ADEQUATE,
        MortalityFields::DEATH_WITHIN_48_HOURS_OF_ADMISSION,
        MortalityFields::TERMINAL_ILLNESS_KNOWN_ON_ADMISSION,
        MortalityFields::ABNORMAL_TEST_RESULTS_ADDRESSED,
        MortalityFields::PATIENT_UNDER_50,
        MortalityFields::DEATH_ASSOCIATED_WITH_DRUG_REACTION,
        MortalityFields::DEATH_ASSOCIATED_WITH_ADVERSE_EVENT,
        MortalityFields::DEATH_PREVENTABLE_UNDER_OPTIMAL_CONDITIONS,
        MortalityFields::TERMINAL_EVENTS_ANTICIPATED,
        MortalityFields::DIAGNOSTIC_WORKUP_BEFORE_AND_AFTER_EVENT_ADEQUATE_TIMELY,
        MortalityFields::DOCUMENTATION_DEFICIENCY,
        MortalityFields::MEDICATION_ERROR,
        MortalityFields::HOSPITAL_ACQUIRED_INFECTION,
        MortalityFields::EXTENDED_LENGTH_OF_STAY,
        MortalityFields::UNPLANNED_RETURN_TO_OR,
        MortalityFields::UNPLANNED_RETURN_TO_ICU,
        MortalityFields::FAMILY_PATIENT_COMPLAINT,
        MortalityFields::OPPORTUNITY_REPORT_INITIATED,
        MortalityFields::PREVENTATIVE_MEASURES_ADEQUATE,
        MortalityFields::TREATMENT_ADEQUATE,
        MortalityFields::EOL_PREPARATION_MET,
        MortalityFields::FUTURE_CORRECTIVE_MEASURES_REQUIRED,
        MortalityFields::ABNORMAL_INVESTIGATIONS_RESULT_OR_PHYSICAL_ADMISSION,
        MortalityFields::QUALITY_OF_CARE_ISSUE_IDENTIFIED,
        MortalityFields::DEATH_DUE_TO_SURGERY,
        MortalityFields::RRT_CALLED,
        MortalityFields::PROGNOSIS_DISCUSSED_WITH_FAMILY,
        MortalityFields::DEVIATION_FROM_STANDARD_CARE,
        MortalityFields::EQUIPMENT_FAILURE_IDENTIFIED,
        MortalityFields::IATROGENIC_EVENT,
        MortalityFields::CAUSE_OF_DEATH_IDENTIFIED,
        MortalityFields::WHAT_INTERNAL_FACTORS_CONTRIBUTED_TO_DEATH,
        MortalityFields::MOR_LESSONS,
        MortalityFields::MOR_LESSONS_SUBCAT,
        MortalityFields::MOR_HRO_CHARACTERISTICS,
        MortalityFields::MOR_SPECIALTY,
        MortalityFields::SOURCE_OF_RECORD,
        MortalityFields::ADMITTING_PHYSICIAN,
        MortalityFields::ATTENDING_PHYSICIAN,
        MortalityFields::ROOM_NO,
        MortalityFields::BED_NO,
        MortalityFields::ADMITTING_SERVICE,
        MortalityFields::ATTENDING_SERVICE,
        MortalityFields::DIAGNOSTIC,
        MortalityFields::PROCEDURES,
        MortalityFields::OUTBREAK_IMPACT,
        MortalityFields::OUTBREAK_TYPE,

        // CCS2
        MortalityFields::MOR_AFFECTING_TIER_ZERO,
        MortalityFields::MOR_TYPE_TIER_ONE,
        MortalityFields::MOR_TYPE_TIER_TWO,
        MortalityFields::MOR_TYPE_TIER_THREE,
    ],
    'ACTION_LINK_LIST_FIELDS' => [
        ['field' => 'recordid', 'width' => '5'],
        ['field' => 'name', 'width' => '17'],
        ['field' => 'dreview', 'width' => '10'],
        ['field' => 'handler', 'width' => '17'],
        ['field' => 'summary', 'width' => '51'],
    ],
    'LEVEL1_CON_OPTIONS' => [
        'P' => ['Title' => _fdtk('deceased'), 'DivName' => 'contacts_type_P', 'Max' => 1],
        'C' => ['Title' => _fdtk('consultant'), 'DivName' => 'contacts_type_C'],
        'N' => ['Title' => _fdtk('other_contact'), 'DivName' => 'contacts_type_N'],
        'R' => ['Title' => 'Reporter', 'Role' => $registry->getParm('REPORTER_ROLE', 'REP'), 'ActualType' => 'N', 'DivName' => 'contacts_type_R', 'Max' => 1],
    ],

    'CONTACTTYPES' => [
        ContactTypes::POLICE_OFFICER => [
            'Type' => ContactTypes::POLICE_OFFICER,
            'Name' => _fdtk('deceased'),
            'Plural' => _fdtk('deceased'),
            'None' => _fdtk('no_deceased'),
            'CreateNew' => _fdtk('deceased_link'),
            'Max' => 1,
        ],
        ContactTypes::CONSULTANT => [
            'Type' => ContactTypes::CONSULTANT,
            'Name' => _fdtk('consultant'),
            'Plural' => _fdtk('consultant_plural'),
            'None' => _fdtk('no_consultant_plural'),
            'CreateNew' => _fdtk('consultant_link'),
        ],
        ContactTypes::OTHER_CONTACT => [
            'Type' => ContactTypes::OTHER_CONTACT,
            'Name' => _fdtk('other_contact'),
            'Plural' => _fdtk('other_contact_plural'),
            'None' => _fdtk('no_other_contact_plural'),
            'CreateNew' => _fdtk('other_contact_link'),
        ],
    ],

    'USE_WORKFLOWS' => true,
    'WORKFLOW_GLOBAL' => 'MOR_WORKFLOW',
    'DEFAULT_WORKFLOW' => 0,

    'LIBPATH' => 'Source/generic_modules/MOR',

    'FIELD_NAMES' => [
        'NAME' => 'name',
        'HANDLER' => 'reviewer',
        'REF' => 'ourref',
    ],

    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserMOR1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserMOR2Settings',
    'HOME_SCREEN_STATUS_LIST' => true,
    'LINKED_CONTACTS' => true,
    'BASIC_FORM_FILES' => [
        1 => 'Source/generic_modules/MOR/BasicForm1.php',
        2 => 'Source/generic_modules/MOR/BasicForm2.php',
    ],
    'NOTEPAD' => true,
    'ACTION_TRIGGERS' => $spscDataset || $actionTriggersEnabled,
    'CAN_LINK_MODULES' => true,
    'LEVEL1_PERMS' => 'CODE_MOR1_INPUT_ONLY',
    // OVERDUE
    'CAN_HAVE_OVERDUE_RECORDS' => true,
    'OVERDUE_CHECK_FIELD' => MortalityFields::DECEASED_DATE_OF_DEATH,
    'OVERDUE_DAY_GLOBAL' => 'MOR_OVERDUE_DAYS',
    'OVERDUE_TYPE_GLOBAL' => 'MOR_OVERDUE_TYPE',
    'CCS2_FIELDS' => [
        MortalityFields::MOR_AFFECTING_TIER_ZERO,
        MortalityFields::MOR_TYPE_TIER_ONE,
        MortalityFields::MOR_TYPE_TIER_TWO,
        MortalityFields::MOR_TYPE_TIER_THREE,
    ],
    'DATE_OPENED_AT' => MortalityFields::DATE_ADMISSION,
    'CAN_LINK_CONTACTS' => true,

    ModuleDefKeys::FIELDSET_MAPPINGS => [
        ContactTypes::DECEASED => 106,
        ContactTypes::CONSULTANT => 107,
        ContactTypes::OTHER_CONTACT => 108,
    ],
];
