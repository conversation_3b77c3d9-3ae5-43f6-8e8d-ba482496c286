<?php

use app\models\framework\config\DatixConfigFactory;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\mortality\model\MortalityFields;
use src\mortality\model\MortalitySections;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);

$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();

$config = (new DatixConfigFactory())->getInstance();
$showLastChildFirst = $config->showLastChildFirst();

$useFormDesignLanguage = $FormType == 'Design';

$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

$FormArray = [
    'Parameters' => [
        'Panels' => false,
        'Condition' => false,
    ],

    'details' => [
        'Title' => _fdtk('mortality_review_details', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            'ourref',
            'name',
            'reviewer',
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'dadmission',
            'incident_date',
            'summary',
            'dreview',
            'death_expected',
            'death_avoidable',
            'admission_method',
            'admitting_diagnosis',
            'cause_death_a',
            'cause_death_b',
            'cause_death_c',
            'cause_death_d',
            'cause_death_e',
            'co_morbidities',
            'dmeeting',
            'situation',
            'background',
            'assessment',
            'recommendation',
            'inquest_held',
            'considerations',
            'score',
            'mandatory_review',
            'closed_date',
            ['Name' => MortalityFields::MAM_MORTALITY_LOCATION_DIFFERENT_TO_EVENT_LOCATION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MAM_WERE_INTERVENTIONS_PERFORMED_NECESSARY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MAM_DISCUSS_CASE_AT_MAM, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CATEGORY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_CATEGORY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CAUSES_OF_DEATH, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::COMPLICATIONS_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_COMPLICATIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::NON_CARDIOVASCULAR_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SURGICAL_COMPLICATIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_WITHIN_48_HOURS_OF_SURGICAL_OR_INVASIVE_PROCEDURE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DIAGNOSTIC_WORKUP_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_WITHIN_48_HOURS_OF_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TERMINAL_ILLNESS_KNOWN_ON_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::ABNORMAL_TEST_RESULTS_ADDRESSED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PATIENT_UNDER_50, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_ASSOCIATED_WITH_DRUG_REACTION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_ASSOCIATED_WITH_ADVERSE_EVENT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_PREVENTABLE_UNDER_OPTIMAL_CONDITIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TERMINAL_EVENTS_ANTICIPATED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DIAGNOSTIC_WORKUP_BEFORE_AND_AFTER_EVENT_ADEQUATE_TIMELY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DOCUMENTATION_DEFICIENCY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MEDICATION_ERROR, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::HOSPITAL_ACQUIRED_INFECTION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EXTENDED_LENGTH_OF_STAY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::UNPLANNED_RETURN_TO_OR, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::UNPLANNED_RETURN_TO_ICU, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::FAMILY_PATIENT_COMPLAINT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::OPPORTUNITY_REPORT_INITIATED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PREVENTATIVE_MEASURES_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TREATMENT_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EOL_PREPARATION_MET, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::FUTURE_CORRECTIVE_MEASURES_REQUIRED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::ABNORMAL_INVESTIGATIONS_RESULT_OR_PHYSICAL_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::QUALITY_OF_CARE_ISSUE_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_DUE_TO_SURGERY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::RRT_CALLED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PROGNOSIS_DISCUSSED_WITH_FAMILY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEVIATION_FROM_STANDARD_CARE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EQUIPMENT_FAILURE_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::IATROGENIC_EVENT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CAUSE_OF_DEATH_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::WHAT_INTERNAL_FACTORS_CONTRIBUTED_TO_DEATH, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_LESSONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_LESSONS_SUBCAT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_HRO_CHARACTERISTICS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_SPECIALTY, 'Condition' => $showSpscFields],
        ],
    ],

    MortalitySections::OUTBREAK => [
        'Title' => _fdtk('mor_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => MortalityFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => MortalityFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],

    'location_admitted' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'admission_location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    'service_admitted' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'admission_service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'inquest' => [
        'Title' => _fdtk('inquest', $useFormDesignLanguage),
        'Rows' => [
            'inquest_start_date',
            'inquest_end_date',
            'expected_verdict',
            'actual_verdict',
            'jury_present',
            'pofd_report_date',
            'pm_recieved_date',
        ],
    ],

    'contacts_type_P' => [
        'Title' => _fdtk('deceased', $useFormDesignLanguage),
        'LinkRole' => 'PAT',
        'module' => 'MOR',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => ['P' => ['module' => 'CON']],
        'contacttype' => 'P',
        'suffix' => 1,
        'Rows' => [],
    ],

    'contacts_type_C' => [
        'Title' => _fdtk('consultant_plural', $useFormDesignLanguage),
        'module' => 'MOR',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => ['C' => ['module' => 'CON']],
        'contacttype' => 'C',
        'suffix' => 2,
        'Rows' => [],
    ],

    'contacts_type_N' => [
        'Title' => _fdtk('other_contact_plural', $useFormDesignLanguage),
        'module' => 'MOR',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => ['N' => ['module' => 'CON']],
        'contacttype' => 'N',
        'suffix' => 3,
        'Rows' => [],
    ],

    'contacts_type_R' => [
        'Title' => _fdtk('details_of_person_reporting_the', $useFormDesignLanguage) . ' ' . _fdtk('MORName', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => 'MOR',
        'Special' => 'DynamicContact',
        'contacttype' => 'R',
        'Role' => $registry->getParm('REPORTER_ROLE', 'REP'),
        'LinkedForms' => ['R' => ['module' => 'CON']],
        'suffix' => 4,
        'Rows' => [],
    ],
    'additional' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_document',
        ],
    ],

    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true),
        'NoReadOnly' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],

    'linked_documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_PRINT,
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],

    'stage1' => [
        'Title' => _fdtk('stage1', $useFormDesignLanguage),
        'Rows' => [
            'stage1_death_cert_wording',
            'stage1_coroner_uncertain',
            'stage1_death_anticipated',
            'stage1_evidence',
            'stage1_evidence_other_details',
            'stage1_concern_care',
            'stage1_concerns_raised',
        ],
    ],

    'stage2' => [
        'Title' => _fdtk('stage2', $useFormDesignLanguage),
        'Rows' => [
            'stage2_unpl_prior',
            'stage2_hospital_incu',
            'stage2_hospital_incu_a',
            'stage2_hospital_incu_a_o_d',
            'stage2_adverse_drug',
            'stage2_unpl_trans_a',
            'stage2_unpl_trans_a_o_d',
            'stage2_unpl_return',
            'stage2_unpl_visit',
            'stage2_unpl_removal',
            'stage2_patient_compl',
            'stage2_patient_compl_a',
            'stage2_patient_compl_a_o_d',
            'stage2_neuro_defect',
            'stage2_un_death_dis',
            'stage2_cardiac_resp',
            'stage2_hcare_inf',
            'stage2_hcare_inf_a',
            'stage2_hcare_inf_a_o_d',
            'stage2_family_diss',
            'stage2_doc_lit',
            'stage2_delay_treatment',
            'stage2_evidence_in_dec',
            'stage2_comm_prob',
            'stage2_doc_prob',
            'stage2_incorrect_diag',
            'stage2_o_undes_out',
            'stage2_o_undes_out_p_d',
            'lessons_learned',
            'outcomes',
        ],
    ],

    'stage2_cause_of_death' => [
        'Title' => _fdtk('stage2_cause_of_death', $useFormDesignLanguage),
        'Rows' => [
            'stage2_death_caused_prob',
            'stage2_death_caused_prob_p_d',
            'stage2_death_contr_prob',
            'stage2_death_contr_prob_p_d',
        ],
    ],

    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            MortalityFields::MOR_AFFECTING_TIER_ZERO,
            MortalityFields::MOR_TYPE_TIER_ONE,
            MortalityFields::MOR_TYPE_TIER_TWO,
            MortalityFields::MOR_TYPE_TIER_THREE,
        ],
    ],
];

return $FormArray;
