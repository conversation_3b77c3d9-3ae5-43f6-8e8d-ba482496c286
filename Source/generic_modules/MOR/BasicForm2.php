<?php

use app\models\framework\config\DatixConfig;
use app\models\framework\modules\ModuleRepository;
use Source\generic_modules\BasicFormSectionHelperFactory;
use src\component\form\FormTable;
use src\contacts\controllers\ContactsController;
use src\contacts\controllers\SearchCriteriaController;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\mortality\model\MortalityFields;
use src\mortality\model\MortalitySections;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$FormType = $basicFormHelper->getValidFormMode($FormType);

$sectionHelper = (new BasicFormSectionHelperFactory())->create($FormType);

$registry ??= Container::get(Registry::class);

$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showOutbreakFields = $registry->getParm('OUTBREAK_FIELDS_ENABLED', 'N')->isTrue();

$investigationsIsLicensed = Container::get(ModuleRepository::class)->getModuleByCode('INV')->isEnabled();
$useFormDesignLanguage = $FormType == 'Design';

$showLastChildFirst = Container::get(DatixConfig::class)->showLastChildFirst();

$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

$FormArray = [

    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],

    'details' => [
        'Title' => _fdtk('mortality_review_details', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            [
                'Name' => MortalityFields::SOURCE_OF_RECORD,
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'ReadOnly' => true,
            ],
            'ourref',
            'name',
            'reviewer',
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => 'MOR',
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => 'MOR',
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            ['Name' => 'last_updated', 'ReadOnly' => true],
            'dadmission',
            'deceased_date_of_death',
            'incident_date',
            'summary',
            'dreview',
            'death_expected',
            'death_avoidable',
            'admission_method',
            'admitting_diagnosis',
            'cause_death_a',
            'cause_death_b',
            'cause_death_c',
            'cause_death_d',
            'cause_death_e',
            'co_morbidities',
            'dmeeting',
            'situation',
            'background',
            'assessment',
            'recommendation',
            'inquest_held',
            'considerations',
            'score',
            'mandatory_review',
            'closed_date',
            [
                'Name' => 'flag_for_investigation',
                'ReadOnly' => $data['flag_for_investigation'] === 'Y',
                'Condition' => $investigationsIsLicensed,
            ],
            ['Name' => MortalityFields::MAM_MORTALITY_LOCATION_DIFFERENT_TO_EVENT_LOCATION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MAM_WERE_INTERVENTIONS_PERFORMED_NECESSARY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MAM_DISCUSS_CASE_AT_MAM, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CATEGORY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_CATEGORY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CAUSES_OF_DEATH, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::COMPLICATIONS_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SUB_COMPLICATIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::NON_CARDIOVASCULAR_CAUSES, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::SURGICAL_COMPLICATIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_WITHIN_48_HOURS_OF_SURGICAL_OR_INVASIVE_PROCEDURE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DIAGNOSTIC_WORKUP_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_WITHIN_48_HOURS_OF_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TERMINAL_ILLNESS_KNOWN_ON_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::ABNORMAL_TEST_RESULTS_ADDRESSED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PATIENT_UNDER_50, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_ASSOCIATED_WITH_DRUG_REACTION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_ASSOCIATED_WITH_ADVERSE_EVENT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_PREVENTABLE_UNDER_OPTIMAL_CONDITIONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TERMINAL_EVENTS_ANTICIPATED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DIAGNOSTIC_WORKUP_BEFORE_AND_AFTER_EVENT_ADEQUATE_TIMELY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DOCUMENTATION_DEFICIENCY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MEDICATION_ERROR, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::HOSPITAL_ACQUIRED_INFECTION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EXTENDED_LENGTH_OF_STAY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::UNPLANNED_RETURN_TO_OR, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::UNPLANNED_RETURN_TO_ICU, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::FAMILY_PATIENT_COMPLAINT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::OPPORTUNITY_REPORT_INITIATED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PREVENTATIVE_MEASURES_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::TREATMENT_ADEQUATE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EOL_PREPARATION_MET, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::FUTURE_CORRECTIVE_MEASURES_REQUIRED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::ABNORMAL_INVESTIGATIONS_RESULT_OR_PHYSICAL_ADMISSION, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::QUALITY_OF_CARE_ISSUE_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEATH_DUE_TO_SURGERY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::RRT_CALLED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::PROGNOSIS_DISCUSSED_WITH_FAMILY, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::DEVIATION_FROM_STANDARD_CARE, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::EQUIPMENT_FAILURE_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::IATROGENIC_EVENT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::CAUSE_OF_DEATH_IDENTIFIED, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::WHAT_INTERNAL_FACTORS_CONTRIBUTED_TO_DEATH, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_LESSONS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_LESSONS_SUBCAT, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_HRO_CHARACTERISTICS, 'Condition' => $showSpscFields],
            ['Name' => MortalityFields::MOR_SPECIALTY, 'Condition' => $showSpscFields],
        ],
    ],

    MortalitySections::OUTBREAK => [
        'Title' => _fdtk('mor_outbreak_title', $useFormDesignLanguage),
        'Condition' => $showOutbreakFields,
        'Rows' => [
            ['Name' => MortalityFields::OUTBREAK_IMPACT, 'Condition' => $showOutbreakFields],
            ['Name' => MortalityFields::OUTBREAK_TYPE, 'Condition' => $showOutbreakFields],
        ],
    ],

    'location_admitted' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'admission_location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    'service_admitted' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'admission_service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    'spsc_national_data' => [
        'Title' => _fdtk('spsc_national_data', $useFormDesignLanguage),
        'Condition' => $showSpscFields,
        'Rows' => [
            MortalityFields::ADMITTING_PHYSICIAN,
            MortalityFields::ATTENDING_PHYSICIAN,
            MortalityFields::ROOM_NO,
            MortalityFields::BED_NO,
            MortalityFields::ADMITTING_SERVICE,
            MortalityFields::ATTENDING_SERVICE,
            MortalityFields::DIAGNOSTIC,
            MortalityFields::PROCEDURES,
        ],
    ],

    'inquest' => [
        'Title' => _fdtk('inquest', $useFormDesignLanguage),
        'Rows' => [
            'inquest_start_date',
            'inquest_end_date',
            'expected_verdict',
            'actual_verdict',
            'jury_present',
            'pofd_report_date',
            'pm_recieved_date',
        ],
    ],

    'notepad' => [
        'Title' => _fdtk('notepad', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [
            'notes',
        ],
    ],

    'feedback' => [
        'Title' => _fdtk('feedback_title', $useFormDesignLanguage),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],
    'linked_records' => [
        'Title' => _fdtk('linked_records', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'Special' => 'LinkedRecords',
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'action_chains' => [
        'Title' => _fdtk('action_chains', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'getActionChains' => [
                'controller' => src\actionchains\controllers\ActionChainController::class,
            ],
        ],
        'LinkedForms' => ['action_chains' => ['module' => 'ACT']],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'additional' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_document',
        ],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'history' => [
        'Title' => _fdtk('notifications', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => src\email\controllers\EmailHistoryController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'word' => [
        'Title' => _fdtk('mod_templates_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class, ], ],
        'NotModes' => ['New', 'Search', 'Print'],
        'Rows' => [],
    ],

    'rejection' => GenericRejectionArray('MOR', $data, $useFormDesignLanguage),
    'rejection_history' => [
        'Title' => _fdtk('reasons_history_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'SectionRejectionHistory' => [
                'controller' => src\reasons\controllers\ReasonsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Condition' => $registry->getParm('REJECT_REASON', 'Y')->isTrue(),
        'Rows' => [],
    ],

    'stage1' => [
        'Title' => _fdtk('stage1', $useFormDesignLanguage),
        'Rows' => [
            'stage1_death_cert_wording',
            'stage1_coroner_uncertain',
            'stage1_death_anticipated',
            'stage1_evidence',
            'stage1_evidence_other_details',
            'stage1_concern_care',
            'stage1_concerns_raised',
        ],
    ],

    'stage2' => [
        'Title' => _fdtk('stage2', $useFormDesignLanguage),
        'Rows' => [
            'stage2_unpl_prior',
            'stage2_hospital_incu',
            'stage2_hospital_incu_a',
            'stage2_hospital_incu_a_o_d',
            'stage2_adverse_drug',
            'stage2_unpl_trans_a',
            'stage2_unpl_trans_a_o_d',
            'stage2_unpl_return',
            'stage2_unpl_visit',
            'stage2_unpl_removal',
            'stage2_patient_compl',
            'stage2_patient_compl_a',
            'stage2_patient_compl_a_o_d',
            'stage2_neuro_defect',
            'stage2_un_death_dis',
            'stage2_cardiac_resp',
            'stage2_hcare_inf',
            'stage2_hcare_inf_a',
            'stage2_hcare_inf_a_o_d',
            'stage2_family_diss',
            'stage2_doc_lit',
            'stage2_delay_treatment',
            'stage2_evidence_in_dec',
            'stage2_comm_prob',
            'stage2_doc_prob',
            'stage2_incorrect_diag',
            'stage2_o_undes_out',
            'stage2_o_undes_out_p_d',
            'lessons_learned',
            'outcomes',
        ],
    ],

    'stage2_cause_of_death' => [
        'Title' => _fdtk('stage2_cause_of_death', $useFormDesignLanguage),
        'Rows' => [
            'stage2_death_caused_prob',
            'stage2_death_caused_prob_p_d',
            'stage2_death_contr_prob',
            'stage2_death_contr_prob_p_d',
        ],
    ],

    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            MortalityFields::MOR_AFFECTING_TIER_ZERO,
            MortalityFields::MOR_TYPE_TIER_ONE,
            MortalityFields::MOR_TYPE_TIER_TWO,
            MortalityFields::MOR_TYPE_TIER_THREE,
        ],
    ],
];

$actionsArray = [];

// Decide if we are using Actions for search or display
if ($FormType === 'Search') {
    $actionsArray['linked_actions'] = [
        'Title' => 'Actions',
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'ACT', 'module' => 'MOR', 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'],
        'NotModes' => ['New'],
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT']],
        'Rows' => [],
    ];
} else {
    $actionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Special' => 'LinkedActions',
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT', 'carltonFormDesigns' => true]],
        'NotModes' => ['New'],
        'Condition' => $FormType !== 'linkedDataSearch',
        'Rows' => [],
    ];
}

$contactArray = [];
// Add contact sections for each contact type.
foreach ($registry->getModuleDefs()['MOR']['CONTACTTYPES'] as $contactTypeDetails) {
    if ($FormType === FormTable::MODE_SEARCH) {
        // Contacts field specifically for search that use a different controller
        $contactArray['contacts_type_' . $contactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_MOR_' . $contactTypeDetails['Type'], $useFormDesignLanguage),
            'LinkedDataSection' => true,
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ],
            'ExtraParameters' => [
                'link_type' => $contactTypeDetails['Type'],
                'linkModule' => 'CON',
                'module' => 'MOR',
                'sectionId' => 'contacts_type_' . $contactTypeDetails['Type'],
            ],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
            'Listings' => ['contacts_type_' . $contactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$contactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => $FormType === FormTable::MODE_SEARCH && CanSeeContacts('MOR', $Perms, $data['rep_approved']),
            'Rows' => [],
        ];
    } else {
        $contactArray['contacts_type_' . $contactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_MOR_' . $contactTypeDetails['Type'], $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'ListLinkedContacts' => [
                    'controller' => ContactsController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $contactTypeDetails['Type']],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'Listings' => ['contacts_type_' . $contactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$contactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH && CanSeeContacts('MOR', $Perms, $data['rep_approved']),
            'Rows' => [],
        ];
    }
}

array_insert_datix($FormArray, 'location_admitted', $contactArray);
array_insert_datix($FormArray, 'service_admitted', $contactArray);
array_insert_datix($FormArray, 'action_chains', $actionsArray);

$FormArray = $sectionHelper->addLinkedModuleListings($FormArray);

return $FormArray;
