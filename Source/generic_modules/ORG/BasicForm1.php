<?php

use src\framework\registry\Registry;
use src\organisations\model\OrganisationFields;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);

// variables for globals table values
$showUsGeneralFields = $registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue();
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();

$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Condition' => false,
        'Suffix' => $suffix,
    ],
    'link_details' . ($suffix ? '_' . $suffix : '') => [
        'Title' => _fdtk('link_details', $useFormDesignLanguage),
        'ContactSuffix' => $suffix,
        'Condition' => $ShowLinkDetails,
        'LinkFields' => true,
        'NotModes' => ['Search'],
        'Rows' => [
            'link_notes',
            'link_resp',
            'link_role',
        ],
        'ContactsLinkTable' => 'link_respondents',
    ],
    'details' . ($suffix ? '_' . $suffix : '') => [
        'Title' => _fdtk('details', $useFormDesignLanguage),
        'ContactSuffix' => $suffix,
        'Rows' => [
            'recordid',
            'org_name',
            'org_reference',
            'org_address',
            [
                'Name' => 'org_city',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'org_state',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'org_county',
                'Condition' => $showUsGeneralFields,
            ],
            'org_postcode',
            'org_tel1',
            'org_tel2',
            'org_email',
            'org_notes',
            [
                'Name' => 'tax_id',
                'Condition' => $showUsClaimsFields,
            ],
            OrganisationFields::TYPE,
            OrganisationFields::SUBTYPE,
        ],
    ],
    'locations' . ($suffix ? '_' . $suffix : '') => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'ContactSuffix' => $suffix,
        'Rows' => [
            'location_id',
        ],
    ],
    'services' . ($suffix ? '_' . $suffix : '') => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'ContactSuffix' => $suffix,
        'Rows' => [
            'service_id',
        ],
    ],
];


return $FormArray;
