<?php

use app\models\generic\valueObjects\Module;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\organisations\controllers\OrganisationsController;
use src\organisations\model\OrganisationFields;
use src\organisations\model\OrganisationSections;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);

// variables for globals table values
$showUsGeneralFields = $registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue();
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
        'LinkType' => 'G',
    ],
    'link_details' => [
        'Title' => _fdtk('link_details', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'Condition' => $ShowLinkDetails,
        'LinkFields' => true,
        'Rows' => [
            'link_notes',
            'link_resp',
            'link_role',
            'indemnity_reserve_assigned',
            'expenses_reserve_assigned',
            'fin_medical_reserve_assigned',
            'fin_legal_reserve_assigned',
            'fin_temporary_indemnity_reserve_assigned',
            'fin_permanent_indemnity_reserve_assigned',
            'remaining_indemnity_reserve_assigned',
            'remaining_expenses_reserve_assigned',
            'fin_remaining_medical_reserve_assigned',
            'fin_remaining_legal_reserve_assigned',
            'fin_remaining_temporary_indemnity_reserve_assigned',
            'fin_remaining_permanent_indemnity_reserve_assigned',
            'resp_total_paid',
        ],
        'ContactsLinkTable' => 'link_respondents',
    ],
    'details' => [
        'Title' => _fdtk('details', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            'org_name',
            'org_reference',
            'org_address',
            [
                'Name' => 'org_city',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'org_state',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'org_county',
                'Condition' => $showUsGeneralFields,
            ],
            'org_postcode',
            'org_tel1',
            'org_tel2',
            'org_email',
            'org_notes',
            [
                'Name' => 'tax_id',
                'Condition' => $showUsClaimsFields,
            ],
            OrganisationFields::TYPE,
            OrganisationFields::SUBTYPE,
        ],
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            'location_id',
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            'service_id',
        ],
    ],
    'policies' => [
        'Title' => _fdtk('mod_policies_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'listpoliciesforrespondent' => [
                'controller' => src\policies\controllers\PoliciesController::class,
            ],
        ],
        'Condition' => (GetParm('POL_PERMS') != '' && ($linkRecordId != '' || $FormMode == 'Design')),
        'NotModes' => ['New', 'Search', 'Print'],
        'Listings' => ['policies' => ['module' => 'POL']],
        'Rows' => [],
    ],
    'claims' => [
        'Title' => _fdtk('table_claims_main', $useFormDesignLanguage),
        'Condition' => $_SESSION['licensedModules'][MOD_CLAIMS],
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'ControllerAction' => [
            'listlinkedclaims' => [
                'controller' => src\organisations\controllers\OrganisationsController::class,
            ],
        ],
        'Rows' => [],
    ],
    OrganisationSections::CONTACTS => [
        'Title' => _fdtk('table_contacts_main', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listLinkedContacts' => [
                'controller' => OrganisationsController::class,
            ],
        ],
        'Rows' => [],
    ],
];

if ($module === Module::REDRESS) {
    $FormArray['link_details']['Rows'] = ['link_role'];
}

return $FormArray;
