<?php

use src\organisations\model\OrganisationFields;
use src\system\database\FieldInterface;

$FieldDefs['ORG'] = [
    'recordid' => [
        'Type' => 'number',
        'ReadOnly' => true,
        'Title' => 'ID',
        'Width' => 5,
        'IdentityCol' => true,
        'Table' => 'organisations_main',
    ],
    'org_name' => [
        'Type' => 'string',
        'Width' => 100,
        'MaxLength' => 120,
        'Title' => 'Name',
        'Table' => 'organisations_main',
    ],
    'org_reference' => [
        'Type' => 'string',
        'MaxLength' => 120,
        'Title' => 'Reference',
        'Table' => 'organisations_main',
    ],
    'org_address' => [
        'Type' => 'textarea',
        'MaxLength' => 250,
        'Rows' => 5,
        'Columns' => 50,
        'Title' => 'Address',
        'NoSpellcheck' => true,
        'Table' => 'organisations_main',
    ],
    'org_postcode' => [
        'Type' => 'string',
        'MaxLength' => 100,
        'Title' => 'Postcode',
        'Table' => 'organisations_main',
    ],
    'org_tel1' => [
        'Type' => 'string',
        'MaxLength' => 100,
        'Title' => 'Telephone no.1',
        'isTelNumber' => true,
        'Table' => 'organisations_main',
    ],
    'org_tel2' => [
        'Type' => 'string',
        'MaxLength' => 100,
        'Title' => 'Telephone no.2',
        'isTelNumber' => true,
        'Table' => 'organisations_main',
    ],
    'org_email' => [
        'Type' => 'email',
        'MaxLength' => 128,
        'Title' => 'E-mail address',
        'Table' => 'organisations_main',
    ],
    'org_issues_linked' => [
        'Type' => 'yesno',
        'Title' => 'Do you want to add any issues to this record?',
        'NoListCol' => true,
    ],
    'org_notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'organisations_main',
    ],
    'tax_id' => [
        'Type' => 'string',
        'Title' => 'Tax ID',
        'MaxLength' => 9,
        'Table' => 'organisations_main',
    ],
    'org_city' => [
        'Type' => 'string',
        'Title' => 'City',
        'Width' => 254,
        'MaxLength' => 254,
        'Table' => 'organisations_main',
    ],
    'org_state' => [
        'Type' => 'ff_select',
        'Title' => 'State',
        'Width' => 2,
        'MaxLength' => 2,
        'UpperCase' => true,
        'Table' => 'organisations_main',
    ],

    'org_county' => [
        'Type' => 'string',
        'Title' => 'County',
        'MaxLength' => 254,
        'Table' => 'organisations_main',
    ],

    // link fields
    'link_notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'link_respondents',
    ],
    'link_resp' => ['Type' => 'number',
        'Title' => 'Responsibility %',
        'Width' => 5,
        'MaxLength' => 10,
        'MaxValue' => 100,
        'MinValue' => 0,
        'NoListCol' => true,
        'Table' => 'link_respondents',
    ],
    'link_role' => [
        'Type' => 'ff_select',
        'Title' => 'Role',
        'NoListCol' => true,
        'Table' => 'link_respondents',
    ],
    'indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Indemnity incurred',
        'ReadOnly' => true,
        'Table' => 'link_respondents',
    ],
    'expenses_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Expenses incurred',
        'ReadOnly' => true,
        'Table' => 'link_respondents',
    ],
    'fin_medical_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Medical incurred',
        'ReadOnly' => true,
    ],
    'fin_legal_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Legal incurred',
        'ReadOnly' => true,
    ],
    'fin_temporary_indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Temporary indemnity incurred',
        'ReadOnly' => true,
    ],
    'fin_permanent_indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Permanent indemnity incurred',
        'ReadOnly' => true,
    ],
    'remaining_expenses_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Indemnity reserve assigned',
        'ReadOnly' => true,
        'Table' => 'link_respondents',
    ],
    'remaining_indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Expenses reserve assigned',
        'ReadOnly' => true,
        'Table' => 'link_respondents',
    ],
    'fin_remaining_medical_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Medical reserve assigned',
        'ReadOnly' => true,
    ],
    'fin_remaining_legal_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Legal reserve assigned',
        'ReadOnly' => true,
    ],
    'fin_remaining_temporary_indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Temporary indemnity reserve assigned',
        'ReadOnly' => true,
    ],
    'fin_remaining_permanent_indemnity_reserve_assigned' => [
        'Type' => 'money',
        'Title' => 'Permanent indemnity reserve assigned',
        'ReadOnly' => true,
    ],
    'resp_total_paid' => [
        'Type' => 'money',
        'Title' => 'Total paid',
        'ReadOnly' => true,
        'Table' => 'link_respondents',
    ],
    'ORG_PAS_CHK_FIELDS' => [
        'Type' => 'multilistbox',
        'MaxLength' => 91,
    ],
    'location_id' => [
        'Type' => 'tree',
        'Title' => 'Location',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => 'organisations_main',
    ],
    'service_id' => [
        'Type' => 'tree',
        'Title' => 'Service',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => 'organisations_main',
    ],
    OrganisationFields::TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Type',
        'Table' => 'organisations_main',
    ],
    OrganisationFields::SUBTYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Subtype',
        'Table' => 'organisations_main',
    ],
];
