<?php

use app\models\modules\ModuleDisplayAcronyms;
use src\organisations\model\OrganisationFields;

$ModuleDefs['ORG'] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => 'ORG',
    'MOD_ID' => MOD_ORGANISATIONS,
    'CODE' => 'ORG',
    'APPROVAL_LEVELS' => false,
    'USES_APPROVAL_STATUSES' => false,
    'NAME' => _fdtk('ORGNamesTitle'),
    'ICON' => 'icons/icon_ORG.png',
    'TABLE' => 'organisations_main',
    'HAS_DEFAULT_LISTING' => true,
    'AUDIT_TABLE' => 'aud_compl_main',
    'REC_NAME' => _fdtk('ORGName'),
    'REC_NAME_PLURAL' => _fdtk('ORGNames'),
    'REC_NAME_TITLE' => _fdtk('ORGNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('ORGNamesTitle'),
    'FK' => 'org_id',
    'ACTION' => 'record&module=ORG',
    'SEARCH_URL' => 'action=search',
    'PERM_GLOBAL' => 'ORG_PERMS',
    'NO_REP_APPROVED' => true,
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'ORG1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::ORGANISATIONS_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'ORG2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::ORGANISATIONS_LEVEL_2],
    ],
    'FIELD_ARRAY' => [
        'org_name',
        'org_reference',
        'org_address',
        'org_county',
        'org_postcode',
        'org_tel1',
        'org_tel2',
        'org_email',
        'org_notes',
        'location_id',
        'service_id',
        'tax_id',
        'org_city',
        'org_state',
        OrganisationFields::TYPE,
        OrganisationFields::SUBTYPE,
    ],
    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserORG1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserORG2Settings',
    'LINKED_CONTACTS' => false,
    'NOTEPAD' => false,

    'LINKED_MODULE' => [
        // 'parent_ids' => array('CLA' => 'main_recordid'),
        // 'link_recordid' => 'recordid',
        'panel' => 'respondents',
    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Organisations/c_dx_organisations_guide.xml',
    'FIELDSET_MAPPINGS' => [
        'respondents_organisation' => 65,
        'respondents_individual' => 64,
    ],
];
