<?php

use app\services\carlton\i18n\I18nServiceFactory;
use Source\generic_modules\FieldDefKeys;
use src\equipment\models\EquipmentFields;
use src\system\database\FieldInterface;
use src\system\language\LanguageSessionFactory;

$i18nService = (new I18nServiceFactory())->create();
$locale = LanguageSessionFactory::getInstance()->getLocale();

$searchCategories = [
    'brand' => ['label' => 'DEVICES.V2.FORM.LABEL.BRAND_NAME', 'fieldName' => 'brandName'],
    'supplyCategory' => ['label' => 'DEVICES.V2.FORM.LABEL.SUPPLY_CATEGORY', 'fieldName' => 'supplyCategory'],
    'supplyClassification' => ['label' => 'DEVICES.V2.FORM.LABEL.SUPPLY_CLASSIFICATION', 'fieldName' => 'supplyClassification'],
    'supplier' => ['label' => 'DEVICES.V2.FORM.LABEL.SUPPLIER', 'fieldName' => 'supplier'],
    'model' => ['label' => 'DEVICES.V2.DISPLAY.LABEL.MODEL', 'fieldName' => 'model'],
    'manufacturer' => ['label' => 'DEVICES.V2.DISPLAY.LABEL.MANUFACTURER', 'fieldName' => 'manufacturer'],
    'type' => ['label' => 'DEVICES.V2.FORM.LABEL.TYPE', 'fieldName' => 'type'],
    'subType' => ['label' => 'DEVICES.V2.FORM.BUTTON.SUB_TYPE', 'fieldName' => 'subType'],
    'genericName' => ['label' => 'DEVICES.V2.MESSAGE.SEARCH_TABLE.GENERIC_NAME', 'fieldName' => 'productName'],
];

$availableSearchCategories = array_flip(
    array_map('src\equipment\models\EquipmentFields::convertForGraphQl', EquipmentFields::getFields()),
);

$searchCategoryCodes = [];
foreach ($searchCategories as $code => $searchCategory) {
    $fieldName = $searchCategory['fieldName'];
    if (isset($availableSearchCategories[$fieldName])) {
        $searchCategoryCodes[$code] = $searchCategory['label'];
    }
}

$deviceTypes = [
    'Administration and giving sets' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ADMINISTRATION_AND_GIVING_SETS',
    'Anaesthetic machines and monitors' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ANAESTHETIC_MACHINES_AND_MONITORS',
    'Anaesthetic and breathing masks' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ANAESTHETIC_AND_BREATHING_MASKS',
    'Autoclaves' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.AUTOCLAVES',
    'Bath aids' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BATH_AIDS',
    'Beds and mattresses' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BEDS_AND_MATTRESSES',
    'Blood pressure measurement' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BLOOD_PRESSURE_MEASUREMENT',
    'Commodes' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.COMMODES',
    'Contact lenses and care products' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.CONTACT_LENSES_AND_CARE_PRODUCTS',
    'CT systems' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.CT_SYSTEMS',
    'Dental appliances' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DENTAL_APPLIANCES',
    'Dental materials' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DENTAL_MATERIALS',
    'Dialysis equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DIALYSIS_EQUIPMENT',
    'Diathermy equipment and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DIATHERMY_EQUIPMENT_AND_ACCESSORIES',
    'Dressings' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DRESSINGS',
    'Endoscopes and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ENDOSCOPES_AND_ACCESSORIES',
    'Endotracheal tubes and airways' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ENDOTRACHEAL_TUBES_AND_AIRWAYS',
    'External defibrillators' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.EXTERNAL_DEFIBRILLATORS',
    'External pacemakers' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.EXTERNAL_PACEMAKERS',
    'Feeding systems - enteral' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.FEEDING_SYSTEMS_ENTERAL',
    'Feeding tubes' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.FEEDING_TUBES',
    'Gloves' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.GLOVES',
    'Guidewires' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.GUIDEWIRES',
    'Hearing aids' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HEARING_AIDS',
    'Heart lung bypass machine' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HEART_LUNG_BYPASS_MACHINE',
    'Hypodermic syringes and needles' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HYPODERMIC_SYRINGES_AND_NEEDLES',
    'Implants  –  active (general)' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_ACTIVE_GENERAL',
    'Implants  –  breast' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_BREAST',
    'Implants  –  cardiovascular' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_CARDIOVASCULAR',
    'Implants  –  hip and knee' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_HIP_AND_KNEE',
    'Implants  –  non-active' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_NON_ACTIVE',
    'Implants  –  pacemakers, defibrillators and leads' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_PACEMAKERS_DEFIBRILLATORS_AND_LEADS',
    'Implant materials' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANT_MATERIALS',
    'In vitro medical devices' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IN_VITRO_MEDICAL_DEVICES',
    'Infant incubators' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INFANT_INCUBATORS',
    'Infusion pumps, syringe drivers' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INFUSION_PUMPS_SYRINGE_DRIVERS',
    'Insulin syringes' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INSULIN_SYRINGES',
    'Intravenous catheters and cannulae' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INTRAVENOUS_CATHETERS_AND_CANNULAE',
    'Laryngoscopes' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.LARYNGOSCOPES',
    'Lasers and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.LASERS_AND_ACCESSORIES',
    'Magnetic resonance equipment and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MAGNETIC_RESONANCE_EQUIPMENT_AND_ACCESSORIES',
    'Mobile X-ray systems' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILE_X_RAY_SYSTEMS',
    'Mobility devices- wheeled, seating aids and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILITY_DEVICES_WHEELED_SEATING_AIDS_AND_ACCESSORIES',
    'Mobility devices - non-wheeled' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILITY_DEVICES_NON_WHEELED',
    'Monitors and electrodes' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MONITORS_AND_ELECTRODES',
    'Ophthalmic equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.OPHTHALMIC_EQUIPMENT',
    'Orthotics' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ORTHOTICS',
    'Patient hoists' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PATIENT_HOISTS',
    'Patient monitoring equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PATIENT_MONITORING_EQUIPMENT',
    'Physiotherapy equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PHYSIOTHERAPY_EQUIPMENT',
    'Prostheses  –  external limb' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PROSTHESES_EXTERNAL_LIMB',
    'Radiotherapy equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RADIOTHERAPY_EQUIPMENT',
    'Radionuclide equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RADIONUCLIDE_EQUIPMENT',
    'Resuscitators' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RESUSCITATORS',
    'Staples and staple guns' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.STAPLES_AND_STAPLE_GUNS',
    'Stretchers' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.STRETCHERS',
    'Surgical instruments' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SURGICAL_INSTRUMENTS',
    'Surgical power tools' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SURGICAL_POWER_TOOLS',
    'Sutures' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SUTURES',
    'Thermometers' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.THERMOMETERS',
    'Ultrasound equipment' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ULTRASOUND_EQUIPMENT',
    'Urinary catheters' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.URINARY_CATHETERS',
    'Ventilators' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.VENTILATORS',
    'Walking sticks / frames' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.WALKING_STICKS_FRAMES',
    'Wound drains' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.WOUND_DRAINS',
    'X-ray equipment, systems and accessories' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.X_RAY_EQUIPMENT_SYSTEMS_AND_ACCESSORIES',
    'Other' => 'DEVICES.V2.FORM.OPTION.DEVICE_TYPE.OTHER',
];

$operators = [
    'Healthcare professional' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.HEALTHCARE_PROFESSIONAL',
    'Patient' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.PATIENT',
    'Other Caregiver' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.OTHER_CAREGIVER',
    'None' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.NONE',
];

$usages = [
    'Initial Use' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.INITIAL_USE',
    'Reuse of Single Use Device' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.REUSE_OF_SINGLE_USE_DEVICE',
    'Reuse of Reusable Device' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.REUSE_OF_REUSABLE_DEVICE',
    'Re-serviced/Refurbished' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.RE_SERVICED_REFURBISHED',
    'Other' => 'DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.OTHER',
];

$searchCategoryCodes = $i18nService->getTranslations($searchCategoryCodes, 'devices', $locale);
$deviceTypes = $i18nService->getTranslations($deviceTypes, 'devices', $locale);
$operators = $i18nService->getTranslations($operators, 'devices', $locale);
$usages = $i18nService->getTranslations($usages, 'devices', $locale);

$FieldDefs['EQU'] = [
    EquipmentFields::SEARCH => [
        'Type' => FieldInterface::COMBINED_LOOKUP_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.FORM.LABEL.SEARCH', 'devices', $locale),
        'NoHide' => true,
        'NoReadOnly' => true,
        'AlwaysMandatory' => true,
        'LookupFunction' => 'lookupEquipment',
        'Field1' => [
            'Name' => EquipmentFields::SEARCH_TERM,
            'Type' => FieldInterface::STRING_DB,
        ],
        'Field2' => [
            'Name' => EquipmentFields::SEARCH_CATEGORY,
            'Type' => FieldInterface::CODE_DB,
            'CustomCodes' => $searchCategoryCodes,
        ],
    ],
    EquipmentFields::BRAND => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.BRAND_NAME', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::TYPE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.TYPE', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::SUB_TYPE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.SUB_TYPE', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::GENERIC_NAME => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.PRODUCT_NAME', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::MANUFACTURER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.MANUFACTURER', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::MODEL => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.MODEL', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::SUPPLIER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.SUPPLIER', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::SUPPLY_CATEGORY => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.SUPPLY_CATEGORY', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::SUPPLY_CLASSIFICATION => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.SUPPLY_CLASSIFICATION', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::SOURCE_ID => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.DEVICE_ID', 'devices', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    EquipmentFields::DEVICE_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.DEVICE_TYPE', 'devices', $locale),
        'CustomCodes' => $deviceTypes,
    ],
    EquipmentFields::QUANTITY_USED => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.QUANTITY_USED', 'devices', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    EquipmentFields::BATCH_NUMBER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.BATCH_NUMBER', 'devices', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    EquipmentFields::EXPIRY_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.EXPIRY_DATE', 'devices', $locale),
    ],
    EquipmentFields::MANUFACTURED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.DATE_MANUFACTURED', 'devices', $locale),
    ],
    EquipmentFields::CATELOGUE_NUMBER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.CATALOGUE_NUMBER', 'devices', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    EquipmentFields::SERIAL_NUMBER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.SERIAL_NUMBER', 'devices', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    EquipmentFields::OPERATOR => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.DEVICE_OPERATOR', 'devices', $locale),
        'CustomCodes' => $operators,
    ],
    EquipmentFields::USAGE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.DEVICE_USAGE', 'devices', $locale),
        'CustomCodes' => $usages,
    ],
    EquipmentFields::CURRENT_LOCATION => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('DEVICES.V2.DISPLAY.LABEL.CURRENT_DEVICE_LOCATION', 'devices', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
];
