<?php

declare(strict_types=1);

namespace Source\generic_modules\EQU;

use app\models\framework\config\DatixConfigFactory;
use src\equipment\Services\EquipmentFieldsService;
use src\framework\session\UserSessionFactory;
use src\helpers\GenericBasicFormHelper;

class EquipmentFormSectionHelperFactory
{
    public function create(?string $formMode, int $level): EquipmentFormSectionHelper
    {
        return new EquipmentFormSectionHelper(
            $formMode,
            $level,
            (new GenericBasicFormHelper())->useFormDesignLanguage($formMode),
            (new DatixConfigFactory())->getInstance()->usePhpEquipForm(),
            (new UserSessionFactory())->create(),
            new EquipmentFieldsService(),
        );
    }
}
