<?php

declare(strict_types=1);

namespace Source\generic_modules\EQU;

use app\models\equipment\entities\EquipmentEntity;
use Ramsey\Uuid\Uuid;
use src\component\form\FormTable;
use src\equipment\controllers\EquipmentController;
use src\equipment\models\EquipmentFields;
use src\equipment\Services\EquipmentFieldsService;
use src\framework\session\UserSession;
use src\system\database\FieldInterface;

use function in_array;

class EquipmentFormSectionHelper
{
    private ?string $formMode;
    private int $formLevel;
    private bool $useFormDesignLanguage;
    private bool $usePhpForm;
    private UserSession $userSession;
    private EquipmentFieldsService $equipmentFieldsService;

    public function __construct(
        ?string $formMode,
        int $formLevel,
        bool $useFormDesignLanguage,
        bool $usePhpForm,
        UserSession $userSession,
        EquipmentFieldsService $equipmentFieldsService
    ) {
        $this->formMode = $formMode;
        $this->formLevel = $formLevel;
        $this->useFormDesignLanguage = $useFormDesignLanguage;
        $this->usePhpForm = $usePhpForm;
        $this->userSession = $userSession;
        $this->equipmentFieldsService = $equipmentFieldsService;
    }

    public function createEquipmentFormSection(): array
    {
        $condition = $this->formLevel === 1 ?
            getenv('MEDICATIONLOCATIONFILTERING_ENABLED') !== '1' || $this->userSession->isLoggedIn() :
            $this->formMode !== FormTable::MODE_SEARCH;

        $section = [
            'Condition' => $condition,
            'Title' => _fdtk('equipment', $this->useFormDesignLanguage),
            'Rows' => [],
        ];

        if ($this->usePhpForm) {
            $section['NoFieldAdditions'] = true;
            $section['NoFieldRemoval'] = true;
            $section['ControllerAction'] = [
                'displayEquipment' => [
                    'controller' => EquipmentController::class,
                ],
            ];
            $section['Rows'] = $this->getEquipmentFields();
            $section['ExtraParameters'] = ['level' => $this->formLevel];
        }

        return $section;
    }

    public function createEquipmentForm(string $suffix): array
    {
        return [
            'Parameters' => ['Suffix' => $suffix],
            "iq_equipment_{$suffix}" => [
                'NoTitle' => true,
                'ContactSuffix' => $suffix,
                'Rows' => $this->getEquipmentFields(),
                'AlwaysShow' => true,
            ],
        ];
    }

    public function createEquipmentSearchFormSection(): array
    {
        $equipmentSearchTitle = _fdtk('equipment', $this->useFormDesignLanguage);
        if ($this->formMode === FormTable::MODE_DESIGN) {
            $equipmentSearchTitle = sprintf("{$equipmentSearchTitle} (%s)", _fdtk('form_search', true));
        }

        return [
            'Title' => $equipmentSearchTitle,
            'Condition' => in_array($this->formMode, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH]),
            'NoFieldAdditions' => true,
            'NoReadOnly' => true,
            'ControllerAction' => [
                'equipmentSearchForm' => [
                    'controller' => EquipmentController::class,
                ],
            ],
            'Rows' => [],
        ];
    }

    public function extractEquipmentData(array $formData): array
    {
        $maxSuffix = (int) ($formData['equipment_max_suffix'] ?? 1);
        $equipment = [];
        for ($i = 1; $i <= $maxSuffix; ++$i) {
            if (!isset($formData["equipment_record_id_{$i}"])) {
                continue;
            }

            $equipmentData = [
                'id' => $formData["equipment_record_id_{$i}"] ?: Uuid::uuid4()->toString(),
                'record_id' => $formData['uuid'],
                'record_type' => 'incident',
                'answers' => [],
                'equipment_id' => $formData["equipment_reference_{$i}"],
            ];
            foreach ($this->equipmentFieldsService->getFields() as $fieldName) {
                $fieldData = $formData["{$fieldName}_{$i}"] ?? null;
                if (isset($fieldData)) {
                    $equipmentData['answers'][] = [
                        'id' => $equipmentData['id'],
                        'question_id' => EquipmentFields::convertForGraphQl($fieldName),
                        'answer' => $fieldData,
                    ];
                }
            }
            $equipment[] = $equipmentData;
        }

        return $equipment;
    }

    private function getEquipmentFields(): array
    {
        $fields = $this->equipmentFieldsService->getFields();

        // Reference is output as a hidden field, so remove from the field list
        unset($fields[array_search('equipment_reference', $fields)]);

        // We need to also remove the individual fields within "combined lookup" fields
        $fields = array_diff($fields, [EquipmentFields::SEARCH_TERM, EquipmentFields::SEARCH_CATEGORY]);

        // We need to include all equipment record fields, regardless of whether they're available on the form,
        // so that we can persist the equipment record locally within Capture in cases where it may not previously
        // have been synced from the medications service. Fields not visible on the form will be added as hidden.
        if ($this->formMode !== FormTable::MODE_DESIGN) {
            $allEquipmentFields = array_map(static function ($field): string {
                return "equipment_{$field}";
            }, EquipmentEntity::getFields());

            unset($allEquipmentFields[array_search('equipment_equipment_reference', $allEquipmentFields)]);

            foreach (array_diff($allEquipmentFields, $fields) as $unavailableField) {
                $fields[] = [
                    'Name' => $unavailableField,
                    'Type' => FieldInterface::HIDDEN_DB,
                ];
            }
        }

        return $fields;
    }
}
