<?php

use app\models\accessLevels\AccessLevels;
use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormKeys;
use Source\generic_modules\BasicFormSectionHelperFactory;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\respondents\controllers\RespondentsController;
use src\safeguarding\models\SafeguardingFields;
use src\safeguarding\models\SafeguardingForm;
use src\system\container\facade\Container;
use src\component\form\FormProperties;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$formMode = $basicFormHelper->getValidFormMode($FormType);

$basicFormSectionHelper = (new BasicFormSectionHelperFactory())->create($formMode);
$config ??= Container::get(DatixConfig::class);
$showLastChildFirst = $config->showLastChildFirst();

$registry ??= Container::get(Registry::class);
$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

$localAuthorityEnabled = $config->isSfgLocalAuthorityEnabled();
$useFormDesignLanguage = $formMode === FormTable::MODE_DESIGN;

$FormArray = [
    'Parameters' => [
        'Panels' => false,
        'Condition' => false,
    ],
    // DETAILS OF REPORT
    SafeguardingForm::SECTION_DETAILS_OF_REPORT => [
        'Title' => _fdtk('safeguarding_details_of_report', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::DATE_REPORTED,
            SafeguardingFields::DATE_OF_EVENT,
            SafeguardingFields::REPORT_CHILD_CONNECTED,
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => Module::SAFEGUARDING,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $basicFormHelper->useFormDesignLanguage($formMode),
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => Module::SAFEGUARDING,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $basicFormHelper->useFormDesignLanguage($formMode),
            ]),
        ],
    ],
    // DETAILS OF PERSON REPORTING - REPORTER
    SafeguardingForm::SECTION_REPORTER => $basicFormSectionHelper->createLevel1ContactSection(Module::SAFEGUARDING, ContactTypes::REPORTER),

    // DETAILS OF INDIVIDUAL - PERSON AFFECTED
    SafeguardingForm::SECTION_PERSON_AFFECTED => $basicFormSectionHelper->createLevel1ContactSection(Module::SAFEGUARDING, ContactTypes::PERSON_AFFECTED),

    // FURTHER INFORMATION
    SafeguardingForm::SECTION_FURTHER_INFORMATION => [
        'Title' => _fdtk('safeguarding_further_information', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::ADULTS_CHILDREN_AT_PROPERTY,
            SafeguardingFields::AT_RISK,
            SafeguardingFields::WHAT_IS_RISK,
        ],
    ],
    // ASSOCIATED PERSONS - OTHER CONTACTS
    SafeguardingForm::SECTION_OTHER_CONTACTS => $basicFormSectionHelper->createLevel1ContactSection(Module::SAFEGUARDING, ContactTypes::OTHER_CONTACT),

    // KEY AGENCY - ORGANISATIONS
    SafeguardingForm::SECTION_ORGANISATIONS => [
        'Title' => _fdtk('safeguarding_key_agency', $basicFormHelper->useFormDesignLanguage($formMode)),
        'NoFieldAdditions' => true,
        'LinkedForms' => [ContactTypes::RESPONDENT => ['module' => Module::ORGANISATIONS]],
        'ControllerAction' => [
            'MakeDynamicOrganisationSection' => [
                'controller' => RespondentsController::class,
            ],
        ],
        'Rows' => [],
    ],
    // REASON FOR REPORT
    SafeguardingForm::SECTION_REASON_FOR_REPORT => [
        'Title' => _fdtk('safeguarding_reason_for_report', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::ABUSE_TYPES,
            SafeguardingFields::PROFESSIONAL_CONCERN_INVOLVED,
            SafeguardingFields::DESCRIPTION,
            SafeguardingFields::DISCUSSED_VIEWS_WISHES,
            SafeguardingFields::NOT_DISCUSSED_REASON,
            SafeguardingFields::EXPECTED_OUTCOME,
            SafeguardingFields::WHERE_ABUSE_OCCURRED,
            SafeguardingFields::WHERE_ABUSE_OCCURRED_OTHER,
            SafeguardingFields::AAR_NEEDS_ADVOCATE,
            SafeguardingFields::AAR_NO_ADVOCATE_REASON,
            SafeguardingFields::AAR_ADVOCATE_DETAILS,
            SafeguardingFields::AAR_LEGISLATIVE_POWERS,
            SafeguardingFields::AAR_LEGISLATIVE_POWERS_REASON,
            SafeguardingFields::AAR_AWARE_OF_REPORT,
            SafeguardingFields::AAR_NOT_AWARE_REASON,
            SafeguardingFields::AAR_CANT_CONSENT_EVIDENCE,
            SafeguardingFields::AAR_SHARE_INFO_CONSENT,
            SafeguardingFields::SHARE_CONSENT_OVERRIDE,
            SafeguardingFields::CONSENT_OVERRIDE_REASON,
            SafeguardingFields::AAR_INFORMED_CONSENT_OVERRIDE,
            SafeguardingFields::PARENTAL_CONSENT_OBTAINED,
            SafeguardingFields::PARENT_NAME_RELATIONSHIP,
            SafeguardingFields::PARENT_REFERRAL_VIEWS,
            SafeguardingFields::CHILD_YP_REFERRAL_VIEWS,
        ],
    ],
    // EMPLOYEES
    SafeguardingForm::SECTION_EMPLOYEES => $basicFormSectionHelper->createLevel1ContactSection(Module::SAFEGUARDING, ContactTypes::EMPLOYEE),

    // ADDITIONAL INFORMATION
    SafeguardingForm::SECTION_ADDITIONAL_INFORMATION => [
        'Title' => _fdtk('safeguarding_additional_information', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::SHOW_WITNESSES,
            SafeguardingFields::SHOW_DOCUMENTS,
            SafeguardingFields::ANON_REPORTING,
        ],
    ],
    // WITNESSES
    SafeguardingForm::SECTION_WITNESSES => $basicFormSectionHelper->createLevel1ContactSection(Module::SAFEGUARDING, ContactTypes::WITNESS),

    // DOCUMENTS
    SafeguardingForm::SECTION_DOCUMENTS => [
        'Title' => _fdtk('documents', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Condition' => $basicFormHelper->notReadOnlyMode($formMode),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],
    'linked_documents' => [
        'Title' => _fdtk('documents', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Condition' => $basicFormHelper->PrintMode($formMode),
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    // LOCATIONS AND SERVICES
    SafeguardingForm::SECTION_LOCATIONS => [
        'Title' => _fdtk('mod_locations_title', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            [
                'Name' => SafeguardingFields::LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::CONFIRM_LOCATION_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::OTHER_LOCATION,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            SafeguardingFields::EXACT_LOCATION,
        ],
    ],
    SafeguardingForm::SECTION_SERVICES => [
        'Title' => _fdtk('mod_services_title', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            [
                'Name' => SafeguardingFields::SERVICE_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::CONFIRM_SERVICE_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::OTHER_SERVICE,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    // NOTES
    SafeguardingForm::SECTION_NOTES => [
        'Title' => _fdtk('notepad', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::NOTES,
        ],
    ],

    // CCS2
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            SafeguardingFields::SFG_AFFECTING_TIER_ZERO,
            SafeguardingFields::SFG_TYPE_TIER_ONE,
            SafeguardingFields::SFG_TYPE_TIER_TWO,
            SafeguardingFields::SFG_TYPE_TIER_THREE,
        ],
    ],
    // SAFEGUARDING REFERRAL
    SafeguardingForm::SECTION_SFG_REFERRAL => [
        'Title' => _fdtk('safeguarding_referral', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Condition' => $localAuthorityEnabled,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            [
                'Name' => SafeguardingFields::LOCAL_AUTHORITY,
                FormProperties::NO_FIELD_ACTIONS => true,
            ],
        ],
    ],
    SafeguardingForm::SECTION_YOUR_MANAGER => [
        BasicFormKeys::TITLE => _fdtk(SafeguardingForm::SECTION_YOUR_MANAGER, $useFormDesignLanguage),
        BasicFormKeys::MODULE => Module::SAFEGUARDING,
        BasicFormKeys::NO_READ_ONLY => true,
        BasicFormKeys::ROWS => [
            [
                FormProperties::TYPE => FormProperties::TYPE_FORM_FIELD,
                FormProperties::FIELD_NAME => SafeguardingFields::HANDLER,
                BasicFormKeys::NO_READ_ONLY => true,
                FormProperties::TITLE => _fdtk(SafeguardingForm::SECTION_YOUR_MANAGER, $useFormDesignLanguage),
                FormProperties::FORM_FIELD => MakeManagerDropdownGeneric(
                    Module::SAFEGUARDING,
                    $data,
                    $FormType,
                    [
                        AccessLevels::CODE_SFG2_FULL_ACCESS,
                        AccessLevels::CODE_SFG2_FULL_ACCESS_NO_CLOSE,
                    ],
                ),
            ],
        ],
    ],
];
