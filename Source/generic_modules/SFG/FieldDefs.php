<?php

use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\FieldDefKeys;
use src\progressnotes\models\ProgressNotesFields;
use src\safeguarding\models\SafeguardingFields;
use src\system\database\FieldInterface;

$FieldDefs[Module::SAFEGUARDING] = [
    'recordid' => [
        'Type' => 'number',
        'Title' => 'ID',
        'IdentityCol' => true,
        'ReadOnly' => true,
        'Width' => 5,
        'Table' => Tables::SAFEGUARDING_MAIN,

    ],
    SafeguardingFields::DATE_REPORTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date of Report',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::REPORT_CHILD_CONNECTED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the report in connection with a child',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::ADULTS_CHILDREN_AT_PROPERTY => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Are there adults or children at the property?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AT_RISK => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Are they also considered at risk?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::WHAT_IS_RISK => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'If yes - what is the risk?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::ABUSE_TYPES => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'Type of alleged abuse:',
        'MaxLength' => 128,
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::PROFESSIONAL_CONCERN_INVOLVED => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Does this involve a professional concern?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::DESCRIPTION => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Description',
        'Table' => Tables::SAFEGUARDING_MAIN,
        'Rows' => 1,
        'Columns' => 50,
    ],
    SafeguardingFields::DISCUSSED_VIEWS_WISHES => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Did you discuss the views and wishes?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::NOT_DISCUSSED_REASON => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'If not, why not?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::EXPECTED_OUTCOME => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'What would they like the outcome to be?',
        'Table' => Tables::SAFEGUARDING_MAIN,
        'Rows' => 1,
        'Columns' => 50,
    ],
    SafeguardingFields::WHERE_ABUSE_OCCURRED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Where did the alleged abuse occur?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::WHERE_ABUSE_OCCURRED_OTHER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Other - Please State:',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_NEEDS_ADVOCATE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Does the adult at risk have/need an advocate?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_NO_ADVOCATE_REASON => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Adult at risk does not need advocate because:',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_ADVOCATE_DETAILS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Adult at risk advocate details',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_LEGISLATIVE_POWERS => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is the adult at risk subject to legislative powers, such as DoLS, MHA or Power of Attorney?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_LEGISLATIVE_POWERS_REASON => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'If yes, please specify which?',
        'MaxLength' => 128,
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_AWARE_OF_REPORT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is the adult at risk aware of the report?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_NOT_AWARE_REASON => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'If not, please explain why:',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_CANT_CONSENT_EVIDENCE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is there any evidence to suggest that the adult at risk lacks mental capacity to consent to this report?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_SHARE_INFO_CONSENT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'If the adult at risk has capacity, do they consent to their information being shared with other agencies?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SHARE_CONSENT_OVERRIDE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is there an overriding reason to share this concern without consent?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::CONSENT_OVERRIDE_REASON => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'If yes, please explain why:',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::AAR_INFORMED_CONSENT_OVERRIDE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Has the adult at risk been informed that their information will be shared without consent, where necessary?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::PARENTAL_CONSENT_OBTAINED => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Has consent for referral been obtained from the person with parental responsibility?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::PARENT_NAME_RELATIONSHIP => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Name of person with parental responsibility giving consent and relationship to child',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::PARENT_REFERRAL_VIEWS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Views of the person with parental responsibility about making this referral',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::CHILD_YP_REFERRAL_VIEWS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Views of the Child / Young Person about making this referral:',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SHOW_WITNESSES => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Were there any witnesses to the incident?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SHOW_DOCUMENTS => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Are there any documents to be attached to this record?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::LOCATION_ID => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Location',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::EXACT_LOCATION => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Exact location',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::OTHER_LOCATION => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Other location',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::CONFIRM_LOCATION_ID => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Location?',
        'NoListCol' => true,
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::CONFIRM_SERVICE_ID => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Service?',
        'NoListCol' => true,
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SERVICE_ID => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Service admitted',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::OTHER_SERVICE => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Other service',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    'notes' => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'notepad',
    ],
    SafeguardingFields::DATE_OPENED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date opened',
        'ReadOnly' => true,
        'NoReadOnly' => true,
        'NoDefault' => true,
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::DATE_CLOSED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date closed',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::HANDLER => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Handler',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::MANAGER => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Manager',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::REFERENCE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Reference',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::ANON_REPORTING => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Report this Safeguarding Referral anonymously',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::APPROVAL_STATUS => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Approval status',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::LEARNINGS_TO_SHARE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Any learnings or outcomes to share?',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::LEARNINGS_TITLE => [
        'Type' => 'string',
        'Width' => 30,
        'MaxLength' => 64,
        'Title' => 'Title',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::KEY_LEARNINGS => [
        'Type' => 'textarea',
        'Title' => 'Key Learnings and Outcomes',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => Tables::SAFEGUARDING_MAIN,
        'RecordLinkBtn' => [
            'moduleTo' => 'LEA',
        ],
    ],
    SafeguardingFields::NAME => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Record name',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    // Config fields
    'SFG_SAVED_QUERIES' => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => _fdtk('choose_saved_queries'),
        'MaxLength' => 70,
    ],
    // COPY/GENERATE FUNCTION
    SafeguardingFields::SOURCE_OF_RECORD => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Source of record',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    // PROGRESS NOTES
    ProgressNotesFields::PROGRESS_NOTE_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => _fdtk('progress_notes_type_title'),
        'Table' => Tables::PROGRESS_NOTES,
    ],
    // CCS2
    SafeguardingFields::SFG_AFFECTING_TIER_ZERO => [
        'Type' => 'ff_select',
        'Title' => 'Incident affecting',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SFG_TYPE_TIER_ONE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 1',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SFG_TYPE_TIER_TWO => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 2',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::SFG_TYPE_TIER_THREE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 3',
        'Table' => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::DATE_OF_EVENT => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Date of event',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_MAIN,
    ],
    SafeguardingFields::LOCAL_AUTHORITY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('local_authority'),
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_MAIN,
        FieldDefKeys::NO_LIST_COL => true,
        FieldDefKeys::NO_ORDER => true,
        FieldDefKeys::RESEND_LOCAL_AUTH_EMAIL => true,
    ],
    SafeguardingFields::DISPATCH_DATE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Dispatch Date',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
    SafeguardingFields::DISPATCH_TIME => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Dispatch Time',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
    SafeguardingFields::STATUS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Status',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
    SafeguardingFields::ERROR_MESSAGE => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'Error Message',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
        FieldDefKeys::ROWS => 3,
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::MAX_LENGTH => 12000,
    ],
    SafeguardingFields::LOCAL_AUTHORITY_ID => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Local Authority Id',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
    SafeguardingFields::LOCAL_AUTHORITY_NAME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Local Authority Name',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
    SafeguardingFields::EMAIL_ADDRESS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Email Address',
        FieldDefKeys::TABLE => Tables::SAFEGUARDING_REFERRAL_HISTORY,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::NO_MANDATORY => true,
    ],
];
