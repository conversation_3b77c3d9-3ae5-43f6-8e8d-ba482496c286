<?php

declare(strict_types=1);

namespace Source\generic_modules\SFG;

use app\models\contact\ContactTypes;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormSectionHelper;
use src\component\form\FormTable;
use src\contacts\controllers\SearchCriteriaController;
use src\respondents\controllers\RespondentsController;

class SafeguardingFormSectionHelper extends BasicFormSectionHelper
{
    public function createKeyAgencySection(): array
    {
        $section = [
            'Title' => _fdtk('safeguarding_key_agency', $this->useFormDesignLanguage),
            'contacttype' => ContactTypes::RESPONDENT,
            'NoFieldAdditions' => true,
            'Rows' => [],
        ];

        if ($this->formMode === FormTable::MODE_SEARCH) {
            $section['ControllerAction'] = [
                'generateMultipleSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ];
            $section['NotModes'] = [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT];
            $section['ExtraParameters'] = [
                'linkedSections' => [
                    ContactTypes::RESPONDENT => [
                        'module' => 'ORG',
                        'label' => _fdtk('safeguarding_key_agency', $this->useFormDesignLanguage),
                        'sectionId' => 'key_agencies',
                    ],
                ],
                'module' => Module::SAFEGUARDING,
            ];
        } else {
            $section['LinkedForms'] = [
                ContactTypes::RESPONDENT => ['module' => Module::ORGANISATIONS, 'Label' => _fdtk('label_organisation_respondent_form_design', $this->useFormDesignLanguage)],
            ];
            $section['ControllerAction'] = [
                'ListLinkedRespondents' => [
                    'controller' => RespondentsController::class,
                ],
            ];
            $section['NotModes'] = [FormTable::MODE_NEW, FormTable::MODE_SEARCH];
        }

        return $section;
    }
}
