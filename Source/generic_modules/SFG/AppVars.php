<?php

use app\models\accessLevels\AccessLevels;
use app\models\contact\ContactTypes;
use app\models\generic\LinkTypes;
use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\approvalStatus\ApprovalStatus;
use Source\generic_modules\ModuleDefKeys;
use src\framework\events\FormEventsHelper;
use src\framework\registry\Registry;
use src\generic\services\DataHelper;
use src\safeguarding\models\SafeguardingFields;
use src\system\container\facade\Container;

const LVL1_CODE = Module::SAFEGUARDING . '1';
const LVL2_CODE = Module::SAFEGUARDING . '2';

$registry ??= Container::get(Registry::class);
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS')->isTrue();

$ModuleDefs[Module::SAFEGUARDING] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => Module::SAFEGUARDING,
    'IS_MAIN_MODULE' => true,
    'MOD_ID' => MOD_SAFEGUARDING,
    'CODE' => Module::SAFEGUARDING,
    'LEVEL1_PERMS' => [AccessLevels::CODE_SFG2_READ_ONLY],
    'APPROVAL_LEVELS' => true,
    'USES_APPROVAL_STATUSES' => true,
    'NAME' => _fdtk('mod_safeguarding_title'),
    'TABLE' => Tables::SAFEGUARDING_MAIN,
    'FK' => 'sfg_id',
    'NAME_FIELD' => SafeguardingFields::NAME,
    'ACTION' => 'record&module=SFG',
    'SEARCH_URL' => 'action=search&module=' . Module::SAFEGUARDING,
    'PERM_GLOBAL' => 'SFG_PERMS',
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'documents',
    'ADD_NEW_RECORD_LEVELS' => [AccessLevels::CODE_SFG2_FULL_ACCESS, AccessLevels::CODE_SFG2_FULL_ACCESS_NO_CLOSE],
    'LOGGED_OUT_LEVEL1' => true,
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => LVL1_CODE, 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::SAFEGUARDING_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => LVL2_CODE, 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::SAFEGUARDING_LEVEL_2],
    ],
    'FIELD_ARRAY' => SafeguardingFields::getFields(),
    'CONTACTTYPES' => [
        ContactTypes::REPORTER => [
            'Type' => ContactTypes::REPORTER,
            'Name' => _fdtk('sfg_reporter'),
            'Plural' => _fdtk('sfg_reporter_plural'),
            'None' => _fdtk('no_sfg_reporter_plural'),
            'CreateNew' => _fdtk('sfg_reporter_link'),
        ],
        ContactTypes::PERSON_AFFECTED => [
            'Type' => ContactTypes::PERSON_AFFECTED,
            'Name' => _fdtk('sfg_person'),
            'Plural' => _fdtk('sfg_person_plural'),
            'None' => _fdtk('no_sfg_person_plural'),
            'CreateNew' => _fdtk('sfg_person_link'),
        ],
        ContactTypes::OTHER_CONTACT => [
            'Type' => ContactTypes::OTHER_CONTACT,
            'Name' => _fdtk('sfg_other_contact'),
            'Plural' => _fdtk('sfg_other_contact_plural'),
            'None' => _fdtk('no_sfg_other_contact_plural'),
            'CreateNew' => _fdtk('sfg_other_contact_link'),
        ],
        ContactTypes::EMPLOYEE => [
            'Type' => ContactTypes::EMPLOYEE,
            'Name' => _fdtk('sfg_employee'),
            'Plural' => _fdtk('sfg_employee_plural'),
            'None' => _fdtk('no_sfg_employee_plural'),
            'CreateNew' => _fdtk('sfg_employee_link'),
        ],
        ContactTypes::WITNESS => [
            'Type' => ContactTypes::WITNESS,
            'Name' => _fdtk('sfg_witness'),
            'Plural' => _fdtk('sfg_witness_plural'),
            'None' => _fdtk('no_sfg_witness_plural'),
            'CreateNew' => _fdtk('sfg_witness_link'),
        ],
    ],
    'LEVEL1_CON_OPTIONS' => [
        ContactTypes::REPORTER => [
            'Title' => _fdtk('sfg_reporter'),
            'Role' => $registry->getParm('REPORTER_ROLE', 'REP'),
            'DivName' => 'contacts_type_R',
            'Max' => 1,
        ],
        ContactTypes::PERSON_AFFECTED => ['Title' => _fdtk('sfg_person'), 'DivName' => 'contacts_type_A'],
        ContactTypes::OTHER_CONTACT => ['Title' => _fdtk('sfg_contact'), 'DivName' => 'contacts_type_N'],
        ContactTypes::EMPLOYEE => ['Title' => _fdtk('sfg_employee_plural'), 'DivName' => 'contacts_type_E'],
        ContactTypes::WITNESS => ['Title' => _fdtk('witnesses'), 'DivName' => 'contacts_type_W'],
    ],
    'LIBPATH' => 'Source/generic_modules/SFG',
    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserSFG1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserSFG2Settings',
    'LINKED_CONTACTS' => true,
    'LINKED_ORGANISATIONS' => true,
    'SHOW_ORGANISATIONS_IN_FBK_TO_FIELD' => true,
    'ORGANISATIONS_TERMINOLOGY' => _fdtk('sfg_organisation_respondent_title'),
    'BASIC_FORM_FILES' => [
        1 => 'Source/generic_modules/SFG/BasicForm1.php',
        2 => 'Source/generic_modules/SFG/BasicForm2.php',
    ],
    'NOTEPAD' => true,
    'ACTION_TRIGGERS' => $actionTriggersEnabled,

    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $eventParameters): array {
                        return Container::get(DataHelper::class)
                            ->populateOpenedDate(
                                SafeguardingFields::DATE_OPENED,
                                Tables::SAFEGUARDING_MAIN,
                                $eventParameters['recordId'],
                                $eventParameters['data'],
                            );
                    },
                ],
            ],
        ],
    ],
    'HOME_SCREEN_STATUS_LIST' => true,
    'AGE_AT_DATE' => SafeguardingFields::DATE_OF_EVENT,
    'FIELD_NAMES' => [
        'NAME' => SafeguardingFields::NAME,
        'REF' => SafeguardingFields::REFERENCE,
        'HANDLER' => SafeguardingFields::HANDLER,
        'MANAGER' => SafeguardingFields::MANAGER,
    ],
    'DATA_VALIDATION_INCLUDES' => [
        'Source/generic_modules/CLA/ModuleFunctions.php',
    ],
    'DATA_VALIDATION_FUNCTIONS' => [
        'validateLinkedOrganisations',
    ],
    'ALLOWS_ANONYMOUS_REPORTING' => true,
    'AUDIT_TRAIL_PERMS' => [AccessLevels::CODE_SFG2_FULL_ACCESS, AccessLevels::CODE_SFG2_FULL_ACCESS_NO_CLOSE],
    'CONTACT_LINK_TABLE_ID' => ['link_respondents' => 'main_recordid'],
    'ADD_NEW_CONFIG_OPTION' => true,
    'RECORD_NAME_FROM_CONTACT' => ContactTypes::PERSON_AFFECTED,
    'REC_NAME' => _fdtk('sfgname'),
    'REC_NAME_PLURAL' => _fdtk('sfgnames'),
    // Batch Delete config
    'CAN_LINK_DOCS' => true,
    'CAN_LINK_CONTACTS' => true,
    'CAN_LINK_MODULES' => true,
    'CAN_LINK_NOTES' => true,
    // Listings
    'HAS_DEFAULT_LISTING' => true,
    // New Approval Status Workflow edit feature
    'CAN_DISABLE_APPROVAL_STATUSES' => true,
    'USE_WORKFLOWS' => true,
    'WORKFLOW_GLOBAL' => 'SFG_WORKFLOW',
    'DEFAULT_WORKFLOW' => 0,
    // Copy Safeguard records
    'ADDITIONAL_COPY_LIB' => 'Source/generic_modules/SFG/CopyFunctions.php',
    'ADDITIONAL_COPY_CHECK_OPTIONS' => [
        'copy_respondents' => true,
    ],
    'ADDITIONAL_COPY_FUNCTION_CALLS' => [
        'copyRespondents',
    ],
    'COPY_EXCLUDE_FIELDS' => [
        'reference',
    ],
    'ADDITIONAL_GENERATE_LIB' => 'Source/generic_modules/SFG/GenerateFunctions.php',
    'ADDITIONAL_GENERATE_FUNCTION_CALLS' => [
        'copyRespondents',
    ],
    // OVERDUE
    'CAN_HAVE_OVERDUE_RECORDS' => true,
    'OVERDUE_STATUSES' => [
        ApprovalStatus::SCREENING_COMPLETE,
        ApprovalStatus::INITIAL_EVALUATION,
        ApprovalStatus::UNDER_INVESTIGATION,
        ApprovalStatus::FEEDBACK_RECEIVED,
    ],
    'OVERDUE_CHECK_FIELD' => SafeguardingFields::DATE_REPORTED,
    'OVERDUE_DAY_GLOBAL' => 'SFG_OVERDUE_DAYS',
    'OVERDUE_TYPE_GLOBAL' => 'SFG_OVERDUE_TYPE',
    // FIELDSETS
    'FIELDSET_MAPPINGS' => [
        ContactTypes::REPORTER => 149,
        ContactTypes::PERSON_AFFECTED => 141,
        ContactTypes::RESPONDENT => 143,
        ContactTypes::OTHER_CONTACT => 142,
        ContactTypes::EMPLOYEE => 144,
        ContactTypes::WITNESS => 145,
        LinkTypes::ACTIONS => 147,
    ],
    // EMAILING
    'EMAIL_REPORTER_GLOBAL' => 'SFG_EMAIL_REPORTER',
    'CAN_CREATE_EMAIL_TEMPLATES' => true,
    // CCS2
    'CCS2_FIELDS' => [
        SafeguardingFields::SFG_AFFECTING_TIER_ZERO,
        SafeguardingFields::SFG_TYPE_TIER_ONE,
        SafeguardingFields::SFG_TYPE_TIER_TWO,
        SafeguardingFields::SFG_TYPE_TIER_THREE,
    ],
    'DATE_OPENED_AT' => SafeguardingFields::DATE_OPENED,
    'LOCAL_AUTHORITY' => SafeguardingFields::LOCAL_AUTHORITY,
    'LOCAL_AUTHORITY_LINK_FORM' => 'showlocalauthorityform',
];
