<?php

namespace Source\generic_modules\SFG;

use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\system\container\facade\Container;

class SafeguardingFormSectionHelperFactory
{
    public function create(string $formMode): SafeguardingFormSectionHelper
    {
        return new SafeguardingFormSectionHelper(
            $formMode,
            (new GenericBasicFormHelper())->useFormDesignLanguage($formMode),
            Container::get(Registry::class),
        );
    }
}
