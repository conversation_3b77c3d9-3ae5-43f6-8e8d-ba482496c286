<?php
/**
 * @return bool
 */
function copyRespondents(string $sourceModule, string $destModule, int $recordId, int $newRecordId, CopyOptions $options)
{
    if ($options->copy_respondents !== 'Y') {
        return false;
    }

    $sql = '
        INSERT INTO link_respondents (main_recordid, link_role, updateid, updatedby, updateddate, link_type, con_id, org_id, link_notes, link_resp, main_module) (
            SELECT :newRecordId, link_role, updateid, updatedby, updateddate, link_type, con_id, org_id, link_notes, link_resp, :destModule
            FROM link_respondents lr
            WHERE lr.main_module = :sourceModule
              AND lr.main_recordid = :sourceRecordId
        );
    ';

    $params = [
        'sourceRecordId' => $recordId,
        'sourceModule' => $sourceModule,
        'newRecordId' => $newRecordId,
        'destModule' => $destModule,
    ];

    $db = new DatixDBQuery();
    $db->setSQL($sql);
    $db->prepareAndExecute($params);
}
