<?php

use app\services\approvalStatus\ApprovalStatus;
use src\safeguarding\models\SafeguardingFields;
use src\safeguarding\models\SafeguardingForm;

$GLOBALS['FormTitle'][7] = _fdtk('sfg1_title');

$GLOBALS['ExpandSections'] = [
    SafeguardingFields::SHOW_WITNESSES => [
        0 => [
            'section' => SafeguardingForm::SECTION_WITNESSES,
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    SafeguardingFields::SHOW_DOCUMENTS => [
        0 => [
            'section' => SafeguardingForm::SECTION_DOCUMENTS,
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    // Hide the reporter section if Report anonymously is checked (or show when not checked)
    SafeguardingFields::ANON_REPORTING => [
        0 => [
            'section' => SafeguardingForm::SECTION_REPORTER,
            'alerttext' => '',
            'values' => [
                0 => 'N',
            ],
        ],
    ],
];

$GLOBALS['MandatoryFields'] = [
    SafeguardingFields::DATE_REPORTED => SafeguardingForm::SECTION_DETAILS_OF_REPORT,
    SafeguardingFields::ADULTS_CHILDREN_AT_PROPERTY => SafeguardingForm::SECTION_FURTHER_INFORMATION,
    SafeguardingFields::AT_RISK => SafeguardingForm::SECTION_FURTHER_INFORMATION,
    SafeguardingFields::ABUSE_TYPES => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::PROFESSIONAL_CONCERN_INVOLVED => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::DESCRIPTION => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::DISCUSSED_VIEWS_WISHES => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::WHERE_ABUSE_OCCURRED => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::AAR_NEEDS_ADVOCATE => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::AAR_LEGISLATIVE_POWERS => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::AAR_CANT_CONSENT_EVIDENCE => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::SHARE_CONSENT_OVERRIDE => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::AAR_INFORMED_CONSENT_OVERRIDE => SafeguardingForm::SECTION_REASON_FOR_REPORT,
    SafeguardingFields::PARENTAL_CONSENT_OBTAINED => SafeguardingForm::SECTION_REASON_FOR_REPORT,
];

$GLOBALS['UserExtraText'] = [
    SafeguardingForm::SECTION_EMPLOYEES => [
        '7' => 'For concerns of allegations of professional abuse',
    ],
];

$GLOBALS['DefaultValues'] = [
    SafeguardingFields::APPROVAL_STATUS => ApprovalStatus::NEW_REFERRAL,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
];

$GLOBALS['HideFields'] = [
    'ccs2' => true,
    SafeguardingFields::HANDLER => true,
    SafeguardingForm::SECTION_YOUR_MANAGER => true,
];
