<?php

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormKeys;
use Source\generic_modules\SFG\SafeguardingFormSectionHelperFactory;
use Source\generic_modules\SpecialSectionTypes;
use src\component\form\FormProperties;
use src\component\form\FormTable;
use src\email\controllers\EmailHistoryController;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\safeguarding\models\SafeguardingFields;
use src\safeguarding\models\SafeguardingForm;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$formMode = $basicFormHelper->getValidFormMode($FormType);

$safeguardingFormSectionHelper = (new SafeguardingFormSectionHelperFactory())->create($formMode);
$config = Container::get(DatixConfig::class);
$showLastChildFirst = $config->showLastChildFirst();

$registry = Container::get(Registry::class);
$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

$localAuthorityEnabled = $config->isSfgLocalAuthorityEnabled();

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],

    // DETAILS OF REPORT
    SafeguardingForm::SECTION_DETAILS_OF_REPORT => [
        'Title' => _fdtk('safeguarding_details_of_report', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            'recordid',
            SafeguardingFields::REFERENCE,
            SafeguardingFields::NAME,
            SafeguardingFields::DATE_REPORTED,
            SafeguardingFields::DATE_OF_EVENT,
            SafeguardingFields::DATE_OPENED,
            SafeguardingFields::DATE_CLOSED,
            SafeguardingFields::REPORT_CHILD_CONNECTED,
            SafeguardingFields::HANDLER,
            SafeguardingFields::MANAGER,
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => Module::SAFEGUARDING,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $basicFormHelper->useFormDesignLanguage($formMode),
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => Module::SAFEGUARDING,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $basicFormHelper->useFormDesignLanguage($formMode),
            ]),
        ],
    ],

    // DETAILS OF PERSON REPORTING - REPORTER
    SafeguardingForm::SECTION_REPORTER => $safeguardingFormSectionHelper->createContactSection(Module::SAFEGUARDING, ContactTypes::REPORTER),

    // DETAILS OF INDIVIDUAL - PERSON AFFECTED
    SafeguardingForm::SECTION_PERSON_AFFECTED => $safeguardingFormSectionHelper->createContactSection(Module::SAFEGUARDING, ContactTypes::PERSON_AFFECTED),


    // FURTHER INFORMATION
    SafeguardingForm::SECTION_FURTHER_INFORMATION => [
        'Title' => _fdtk('safeguarding_further_information', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::ADULTS_CHILDREN_AT_PROPERTY,
            SafeguardingFields::AT_RISK,
            SafeguardingFields::WHAT_IS_RISK,
        ],
    ],

    // ASSOCIATED PERSONS - OTHER CONTACTS
    SafeguardingForm::SECTION_OTHER_CONTACTS => $safeguardingFormSectionHelper->createContactSection(Module::SAFEGUARDING, ContactTypes::OTHER_CONTACT),

    // KEY AGENCY - ORGANISATIONS
    SafeguardingForm::SECTION_ORGANISATIONS => $safeguardingFormSectionHelper->createKeyAgencySection(),

    // REASON FOR REPORT
    SafeguardingForm::SECTION_REASON_FOR_REPORT => [
        'Title' => _fdtk('safeguarding_reason_for_report', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            SafeguardingFields::ABUSE_TYPES,
            SafeguardingFields::PROFESSIONAL_CONCERN_INVOLVED,
            SafeguardingFields::DESCRIPTION,
            SafeguardingFields::DISCUSSED_VIEWS_WISHES,
            SafeguardingFields::NOT_DISCUSSED_REASON,
            SafeguardingFields::EXPECTED_OUTCOME,
            SafeguardingFields::WHERE_ABUSE_OCCURRED,
            SafeguardingFields::WHERE_ABUSE_OCCURRED_OTHER,
            SafeguardingFields::AAR_NEEDS_ADVOCATE,
            SafeguardingFields::AAR_NO_ADVOCATE_REASON,
            SafeguardingFields::AAR_ADVOCATE_DETAILS,
            SafeguardingFields::AAR_LEGISLATIVE_POWERS,
            SafeguardingFields::AAR_LEGISLATIVE_POWERS_REASON,
            SafeguardingFields::AAR_AWARE_OF_REPORT,
            SafeguardingFields::AAR_NOT_AWARE_REASON,
            SafeguardingFields::AAR_CANT_CONSENT_EVIDENCE,
            SafeguardingFields::AAR_SHARE_INFO_CONSENT,
            SafeguardingFields::SHARE_CONSENT_OVERRIDE,
            SafeguardingFields::CONSENT_OVERRIDE_REASON,
            SafeguardingFields::AAR_INFORMED_CONSENT_OVERRIDE,
            SafeguardingFields::PARENTAL_CONSENT_OBTAINED,
            SafeguardingFields::PARENT_NAME_RELATIONSHIP,
            SafeguardingFields::PARENT_REFERRAL_VIEWS,
            SafeguardingFields::CHILD_YP_REFERRAL_VIEWS,
        ],
    ],

    // EMPLOYEES
    SafeguardingForm::SECTION_EMPLOYEES => $safeguardingFormSectionHelper->createContactSection(Module::SAFEGUARDING, ContactTypes::EMPLOYEE),

    // ADDITIONAL INFORMATION
    SafeguardingForm::SECTION_ADDITIONAL_INFORMATION => [
        'Title' => _fdtk('safeguarding_additional_information', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            [
                'Name' => SafeguardingFields::ANON_REPORTING,
                'ReadOnly' => true,
            ],
        ],
    ],

    // WITNESSES
    SafeguardingForm::SECTION_WITNESSES => $safeguardingFormSectionHelper->createContactSection(Module::SAFEGUARDING, ContactTypes::WITNESS),

    // DOCUMENTS
    SafeguardingForm::SECTION_DOCUMENTS => [
        'Title' => _fdtk('documents', $basicFormHelper->useFormDesignLanguage($formMode)),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [], ],

    // LOCATIONS AND SERVICES
    SafeguardingForm::SECTION_LOCATIONS => [
        'Title' => _fdtk('mod_locations_title', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            [
                'Name' => SafeguardingFields::LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::CONFIRM_LOCATION_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::OTHER_LOCATION,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            SafeguardingFields::EXACT_LOCATION,
        ],
    ],
    SafeguardingForm::SECTION_SERVICES => [
        'Title' => _fdtk('mod_services_title', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Rows' => [
            [
                'Name' => SafeguardingFields::SERVICE_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::CONFIRM_SERVICE_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => SafeguardingFields::OTHER_SERVICE,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    // NOTES
    SafeguardingForm::SECTION_NOTES => [
        'Title' => _fdtk('notepad', $basicFormHelper->useFormDesignLanguage($formMode)),
        'LinkedDataSection' => true,
        'Condition' => $basicFormHelper->notLinkedDataSearchMode($formMode),
        'Rows' => [
            SafeguardingFields::NOTES,
        ],
    ],

    // LEARNINGS
    SafeguardingForm::SECTION_LEARNINGS => $safeguardingFormSectionHelper->createLearningSection($data),

    // COMMUNICATION AND FEEDBACK
    SafeguardingForm::SECTION_COMM_AND_FEEDBACK => [
        'Title' => _fdtk('feedback_title', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $basicFormHelper->useFormDesignLanguage($formMode)),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],

    // LINKED RECORDS
    SafeguardingForm::SECTION_LINKED_RECORDS => [
        'Title' => _fdtk('linked_records'),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Condition' => $basicFormHelper->notLinkedDataSearchMode($formMode),
        'Special' => 'LinkedRecords',
        'Rows' => [],
    ],

    // PROGRESS NOTES
    SafeguardingForm::SECTION_PROGRESS_NOTES => $safeguardingFormSectionHelper->createProgressNotesSection(),

    // ACTIONS
    SafeguardingForm::SECTION_ACTIONS => $safeguardingFormSectionHelper->createActionsSection(Module::SAFEGUARDING),

    // ACTION PLANS
    SafeguardingForm::SECTION_ACTION_PLANS => $safeguardingFormSectionHelper->createActionPlanSection(),

    // NOTIFICATIONS
    SafeguardingForm::SECTION_NOTIFICATIONS => [
        'Title' => _fdtk('notifications', $basicFormHelper->useFormDesignLanguage($formMode)),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => EmailHistoryController::class,
            ],
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [],
    ],

    // DOCUMENT TEMPLATES
    SafeguardingForm::SECTION_TEMPLATES => $safeguardingFormSectionHelper->createDocumentTemplatesSection(),

    // REASONS FOR REJECTION
    SafeguardingForm::SECTION_REASONS_FOR_REJECTION => $safeguardingFormSectionHelper->createReasonsForRejectionSection(
        Module::SAFEGUARDING,
        (bool) $data['ReasonSectionEditable'] ?? false,
    ),
    SafeguardingForm::SECTION_REASONS_FOR_REJECTION_HISTORY => $safeguardingFormSectionHelper->createReasonsForRejectionHistorySection(),

    // CCS2
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $basicFormHelper->useFormDesignLanguage($formMode)),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            SafeguardingFields::SFG_AFFECTING_TIER_ZERO,
            SafeguardingFields::SFG_TYPE_TIER_ONE,
            SafeguardingFields::SFG_TYPE_TIER_TWO,
            SafeguardingFields::SFG_TYPE_TIER_THREE,
        ],
    ],
    // SAFEGUARDING REFERRAL
    SafeguardingForm::SECTION_SFG_REFERRAL => [
        BasicFormKeys::TITLE => _fdtk('safeguarding_referral', $basicFormHelper->useFormDesignLanguage($formMode)),
        BasicFormKeys::CONDITION => $localAuthorityEnabled,
        BasicFormKeys::SPECIAL => SpecialSectionTypes::TABULAR,
        BasicFormKeys::NO_FIELD_ADDITIONS => true,
        BasicFormKeys::NO_FIELD_REMOVALS => true,
        BasicFormKeys::SHOW_HISTORY => true,
        BasicFormKeys::ROWS => [
            [
                'Name' => SafeguardingFields::LOCAL_AUTHORITY,
                FormProperties::NO_FIELD_ACTIONS => true,
            ],
        ],
        BasicFormKeys::TABULAR_ROWS => [
            SafeguardingFields::DISPATCH_DATE,
            SafeguardingFields::DISPATCH_TIME,
            SafeguardingFields::LOCAL_AUTHORITY_ID,
            SafeguardingFields::LOCAL_AUTHORITY_NAME,
            SafeguardingFields::EMAIL_ADDRESS,
            SafeguardingFields::STATUS,
            SafeguardingFields::ERROR_MESSAGE,
        ],
        BasicFormKeys::TABLE_SECTION_HEADER => 'notification_history',
    ],
];

$FormArray = $safeguardingFormSectionHelper->addLinkedModuleListings($FormArray);

return $FormArray;
