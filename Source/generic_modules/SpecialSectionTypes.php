<?php

declare(strict_types=1);

namespace Source\generic_modules;

use Exception;
use RuntimeException;

final class SpecialSectionTypes
{
    public const TABULAR = 'tabular';
    public const LINKED_CONTACTS = 'LinkedContacts';
    public const LINKED_ACTIONS = 'LinkedActions';
    public const FEEDBACK = 'Feedback';
    public const LINKED_RECORDS = 'LinkedRecords';
    public const INJURY_DETAILS = 'SectionInjuryDetails';
    public const PROPERTY_DETAILS = 'SectionPropertyDetails';
    public const DYNAMIC_CONTACT = 'DynamicContact';
    public const DYNAMIC_DOCUMENT = 'DynamicDocument';
    public const PROGRESS_NOTES = 'ProgressNotes';
    public const NARRATIVE_NOTES = 'NarrativeNotes';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        throw new RuntimeException("Can't get an instance of SpecialSectionTypes");
    }
}
