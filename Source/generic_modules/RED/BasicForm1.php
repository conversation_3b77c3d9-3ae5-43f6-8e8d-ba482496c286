<?php

use app\models\accessLevels\AccessLevels;
use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormKeys;
use src\addanother\controllers\GenericAddAnotherController;
use src\component\form\FormProperties;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\redress\models\RedressFields;
use src\redress\models\RedressLferFields;
use src\redress\models\RedressRequestForInfoFields;
use src\redress\models\RedressSections;
use src\respondents\controllers\RespondentsController;
use src\system\container\facade\Container;

$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$registry ??= Container::get(Registry::class);
$config ??= Container::get(DatixConfig::class);
$showLastChildFirst = $config->showLastChildFirst();
$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

$FormArray = [
    'Parameters' => [
        'Panels' => false,
        'Condition' => false,
    ],
    'redress_summary' => [
        'Title' => _fdtk('redress_summary', $useFormDesignLanguage),
        'Rows' => [
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => Module::REDRESS,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => Module::REDRESS,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RedressFields::TYPE,
            RedressFields::SUB_TYPE,
            RedressFields::HEALTH_BODY_REF,
            RedressFields::WRP_REF,
            RedressFields::LEGAL_AND_RISK_REF,
            RedressFields::LINKED_TO_SI,
            RedressFields::SI_REF,
            RedressFields::PATIENT_NAME,
            RedressFields::DESCRIPTION,
            RedressFields::NHS_HEALTH_BODY,
            RedressFields::DATE_OF_INDEX_INCIDENT,
            RedressFields::DATE_CONCERN_RECEIVED,
            RedressFields::EST_FIN_YEAR_COMPLETE,
            RedressFields::PTR_GRADING,
            RedressFields::LEVEL_OF_HARM,
            RedressFields::CLOSED_DATE,
        ],
    ],

    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => RedressFields::LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => RedressFields::CONFIRM_LOCATION_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => RedressFields::SECONDARY_LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            RedressFields::LOCATION_EXACT,
        ],
    ],

    'specialties' => [
        'Title' => _fdtk('redress_specialties', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => RedressFields::PRINCIPLE_CLINICAL_SPECIALTY,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => RedressFields::CONFIRM_SERVICE_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => RedressFields::SECONDARY_CLINICAL_SPECIALTY,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    'contacts_type_A' => [
        'Title' => _fdtk('redress_person_affected', $useFormDesignLanguage),
        'module' => Module::REDRESS,
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::PERSON_AFFECTED,
        'suffix' => 2,
        'LinkedForms' => [ContactTypes::PERSON_AFFECTED => ['module' => Module::CONTACTS]],
        'NoReadOnly' => true,
        'Rows' => [],
    ],

    'contacts_type_A_additional' => [
        'Title' => _fdtk('contacts_type_A_additional', $useFormDesignLanguage),
        'NoTitle' => true,
        'Rows' => [
            RedressFields::IS_CLAIMANT_LITIGANT_IN_PERSON,
            RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT,
        ],
    ],

    'contacts_type_M' => [
        'Title' => _fdtk('redress_claimant', $useFormDesignLanguage),
        'module' => Module::REDRESS,
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => [ContactTypes::CLAIMANT => ['module' => Module::CONTACTS]],
        'contacttype' => ContactTypes::CLAIMANT,
        'suffix' => 1,
        'Rows' => [],
    ],

    'respondents_organisation' => [
        'Title' => _fdtk('redress_details_of_representative', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'LinkedForms' => [ContactTypes::RESPONDENT => ['module' => Module::ORGANISATIONS]],
        'ControllerAction' => [
            'MakeDynamicOrganisationSection' => [
                'controller' => RespondentsController::class,
            ],
        ],
        'Rows' => [],
    ],

    'additional' => [
        'Title' => _fdtk('redress_additional', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::SHOW_OTHER_CONTACTS,
            RedressFields::SHOW_DOCUMENT,
        ],
    ],

    'contacts_type_N' => [
        'Title' => _fdtk('contacts', $useFormDesignLanguage),
        'module' => Module::REDRESS,
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::OTHER_CONTACT,
        'suffix' => 4,
        'LinkedForms' => [ContactTypes::OTHER_CONTACT => ['module' => Module::CONTACTS]],
        'NoReadOnly' => true,
        'Rows' => [],
    ],

    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true),
        'NoReadOnly' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],
    'linked_documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_PRINT,
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],

    'contacts_type_R' => [
        'Title' => _fdtk('redress_reporter', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => Module::REDRESS,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::REPORTER,
        'Role' => $registry->getParm('REPORTER_ROLE', 'REP')->toScalar(),
        'LinkedForms' => [ContactTypes::REPORTER => ['module' => Module::CONTACTS]],
        'suffix' => 3,
        'Rows' => [],
    ],

    RedressSections::CASE_MANAGEMENT_REPORT => [
        'Title' => _fdtk('redress_case_management_report', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::CASE_MANAGEMENT_TYPE,
            RedressFields::CASE_DEFERRED_DATE,
            RedressFields::CASE_SUBMITTED_FOR_REIMBURSEMENT,
        ],
    ],

    RedressSections::WRP_REQUESTS_FOR_INFORMATION => [
        'Title' => _fdtk('table_red_wrp_requests_for_information', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'module' => Module::REDRESS,
        'LinkedDataSection' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => GenericAddAnotherController::class,
            ],
        ],
        'Rows' => RedressRequestForInfoFields::getFields(),
        'ExtraParameters' => ['linkedRecordSpecificationKey' => RedressSections::WRP_REQUESTS_FOR_INFORMATION],
    ],

    RedressSections::LFER => [
        'Title' => 'Learning from events extension requests',
        'NoLazyLoad' => true,
        'module' => Module::REDRESS,
        'LinkedDataSection' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => GenericAddAnotherController::class,
            ],
        ],
        'Rows' => RedressLferFields::getFields(),
        'ExtraParameters' => ['linkedRecordSpecificationKey' => RedressSections::LFER],
    ],

    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            RedressFields::RED_AFFECTING_TIER_ZERO,
            RedressFields::RED_TYPE_TIER_ONE,
            RedressFields::RED_TYPE_TIER_TWO,
            RedressFields::RED_TYPE_TIER_THREE,
        ],
    ],

    RedressSections::YOUR_MANAGER => [
        BasicFormKeys::TITLE => _fdtk(RedressSections::YOUR_MANAGER, $useFormDesignLanguage),
        BasicFormKeys::MODULE => Module::REDRESS,
        BasicFormKeys::NO_READ_ONLY => true,
        BasicFormKeys::ROWS => [
            [
                FormProperties::TYPE => FormProperties::TYPE_FORM_FIELD,
                FormProperties::FIELD_NAME => RedressFields::CASE_MANAGER,
                BasicFormKeys::NO_READ_ONLY => true,
                FormProperties::TITLE => _fdtk(RedressSections::YOUR_MANAGER, $useFormDesignLanguage),
                FormProperties::FORM_FIELD => MakeManagerDropdownGeneric(
                    Module::REDRESS,
                    $data,
                    $FormType,
                    [
                        AccessLevels::CODE_FINAL_APPROVAL_RED2,
                    ],
                ),
            ],
        ],
    ],
];

return $FormArray;
