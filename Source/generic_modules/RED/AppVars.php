<?php

use app\models\accessLevels\AccessLevels;
use app\models\contact\ContactTypes;
use app\models\generic\valueObjects\Module;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\forms\RecordHeaderProvider;
use Source\generic_modules\ModuleDefKeys;
use src\framework\events\FormEventsHelper;
use src\framework\registry\Registry;
use src\generic\services\DataHelper;
use src\redress\emails\RedressEmailTypes;
use src\redress\helpers\RedressDataHelper;
use src\redress\models\CRUFields;
use src\redress\models\ExpertReviewFields;
use src\redress\models\LegalRiskServicesFields;
use src\redress\models\RedressFields;
use src\redress\models\RedressLferFields;
use src\redress\models\RedressRequestForInfoFields;
use src\redress\models\RedressSections;
use src\redress\models\RedressTrackerStageModelFactory;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS')->isTrue();
$newCaseManagerEmailEnabled = $registry->getParm(RedressEmailTypes::NEW_CASE_MANAGER_GLOBAL)->isTrue();
$newEscalationManagerEmailEnabled = $registry->getParm(RedressEmailTypes::NEW_ESCALATION_MANAGER_GLOBAL)->isTrue();

$newStaffEmails = [];

if ($newCaseManagerEmailEnabled) {
    $newStaffEmails[RedressFields::CASE_MANAGER] = RedressEmailTypes::NEW_CASE_MANAGER;
}

if ($newEscalationManagerEmailEnabled) {
    $newStaffEmails[RedressFields::ESCALATION_MANAGER] = RedressEmailTypes::NEW_ESCALATION_MANAGER;
}

$ModuleDefs[Module::REDRESS] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => Module::REDRESS,
    'LEVEL1_PERMS' => [AccessLevels::CODE_RED1_INPUT_ONLY],
    'AUDIT_TRAIL_PERMS' => [AccessLevels::CODE_FINAL_APPROVAL_RED2],
    'IS_MAIN_MODULE' => true,
    'MOD_ID' => MOD_REDRESS,
    'CODE' => Module::REDRESS,
    'NAME_FIELD' => RedressFields::PATIENT_NAME,
    'APPROVAL_LEVELS' => true,
    'USES_APPROVAL_STATUSES' => true,
    'ORG_PARENT_SECTION' => 'respondents_organisation',
    RecordHeaderProvider::APPVARS_KEY => array_merge(['recordid'], RedressFields::getFields()),
    'CAN_CREATE_EMAIL_TEMPLATES' => true,
    'NAME' => _fdtk('mod_redress_title'),
    'TABLE' => 'redress_main',
    'HAS_DEFAULT_LISTING' => true,
    'REC_NAME' => 'redress',
    'REC_NAME_PLURAL' => 'redresses',
    'RECORD_NAME_FROM_CONTACT' => ContactTypes::PERSON_AFFECTED,
    'FK' => 'red_id',
    'ACTION' => 'record&module=RED',
    'SEARCH_URL' => 'action=search&module=RED',
    ModuleDefKeys::SERVICE_FIELD => RedressFields::PRINCIPLE_CLINICAL_SPECIALTY,
    'COPY_EXCLUDE_FIELDS' => [
        'last_updated',
        'non_reimbursable_payments',
        'reimbursable_payments',
        'total_payments',
    ],
    'SHOW_EMAIL_GLOBAL' => 'RED_SHOW_EMAIL',
    'EMAIL_REPORTER_GLOBAL' => 'RED_EMAIL_REPORTER',
    'RED_EMAIL_HDLR' => 'RED_EMAIL_HDLR',
    'EMAIL_HANDLER_GLOBAL' => 'EMAIL_HANDLER_GLOBAL',
    'PERM_GLOBAL' => 'RED_PERMS',
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'documents',
    'ADD_NEW_RECORD_LEVELS' => [AccessLevels::CODE_FINAL_APPROVAL_RED2, AccessLevels::CODE_RED1_INPUT_ONLY],
    'LOGGED_OUT_LEVEL1' => false,
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'RED1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::REDRESS_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'RED2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::REDRESS_LEVEL_2],
    ],
    'FIELD_ARRAY' => RedressFields::getFields(),
    'ADDITIONAL_GENERATE_LIB' => 'Source/generic_modules/INC/CopyFunctions.php',
    'ADDITIONAL_GENERATE_FUNCTION_CALLS' => [
        'copyRespondents',
    ],
    'CONTACTTYPES' => [
        ContactTypes::CLAIMANT => [
            'Type' => ContactTypes::CLAIMANT,
            'Name' => _fdtk('red_claimant'),
            'Plural' => _fdtk('red_claimant_plural'),
            'None' => _fdtk('no_red_claimant_plural'),
            'CreateNew' => _fdtk('red_claimant_link'),
        ],
        ContactTypes::PERSON_AFFECTED => [
            'Type' => ContactTypes::PERSON_AFFECTED,
            'Name' => _fdtk('red_person'),
            'Plural' => _fdtk('red_person_plural'),
            'None' => _fdtk('no_red_person_plural'),
            'CreateNew' => _fdtk('red_person_link'),
        ],
        ContactTypes::EMPLOYEE => [
            'Type' => ContactTypes::EMPLOYEE,
            'Name' => _fdtk('red_employee'),
            'Plural' => _fdtk('red_employee_plural'),
            'None' => _fdtk('no_red_employee_plural'),
            'CreateNew' => _fdtk('red_employee_link'),
        ],
        ContactTypes::OTHER_CONTACT => [
            'Type' => ContactTypes::OTHER_CONTACT,
            'Name' => _fdtk('red_other_contact'),
            'Plural' => _fdtk('red_other_contact_plural'),
            'None' => _fdtk('no_red_other_contact_plural'),
            'CreateNew' => _fdtk('red_other_contact_link'),
        ],
        ContactTypes::INDIVIDUAL_RESPONDENT => [
            'Type' => ContactTypes::INDIVIDUAL_RESPONDENT,
            'Name' => _fdtk('red_individual_respondent_title'),
            'Plural' => _fdtk('red_individual_respondent_plural'),
            'None' => _fdtk('no_red_individual_respondent_plural'),
            'CreateNew' => _fdtk('red_individual_respondent_link'),
        ],
    ],

    'LEVEL1_CON_OPTIONS' => [
        ContactTypes::INDIVIDUAL_RESPONDENT => ['Title' => _fdtk('red_individual_respondent_title'), 'DivName' => 'contacts_type_O'],
        ContactTypes::CLAIMANT => ['Title' => _fdtk('red_claimant'), 'DivName' => 'contacts_type_M'],
        ContactTypes::PERSON_AFFECTED => ['Title' => _fdtk('red_person'), 'DivName' => 'contacts_type_A'],
        ContactTypes::OTHER_CONTACT => ['Title' => _fdtk('red_contact'), 'DivName' => 'contacts_type_N'],
        ContactTypes::EMPLOYEE => ['Title' => _fdtk('employee'), 'DivName' => 'contacts_type_E'],
        ContactTypes::REPORTER => [
            'Title' => _fdtk('red_reporter'),
            'Role' => $registry->getParm('REPORTER_ROLE', 'REP'),
            'ActualType' => ContactTypes::OTHER_CONTACT,
            'DivName' => 'contacts_type_R',
            'Max' => 1,
        ],
    ],

    'LIBPATH' => 'Source/generic_modules/RED',

    'FIELD_NAMES' => [
        'NAME' => 'patient_name',
        'HANDLER' => RedressFields::CASE_MANAGER,
    ],

    'NEW_STAFF_EMAILS' => $newStaffEmails,

    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserRED1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserRED2Settings',
    'HOME_SCREEN_STATUS_LIST' => true,
    'LINKED_CONTACTS' => false, // until we've build the contact form sections
    'LINKED_ORGANISATIONS' => true,
    'BASIC_FORM_FILES' => [
        1 => 'Source/generic_modules/RED/BasicForm1.php',
        2 => 'Source/generic_modules/RED/BasicForm2.php',
    ],
    'NOTEPAD' => false,
    'ACTION_TRIGGERS' => $actionTriggersEnabled,

    'CAUSAL_FACTOR_TYPE' => 'red_causal_factor',

    'LINKED_RECORDS' => [
        'red_causal_factor' => [
            'section' => 'causal_factor',
            'type' => 'red_causal_factor',
            'table' => 'causal_factors',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => ['Rows' => ['caf_level_1', 'caf_level_2']],
            'main_recordid_label' => 'RED_ID',
            'useIdentity' => true,
        ],
        'redress_expert_review' => [
            'title' => _fdtk('Expert review report'),
            'section' => 'expert_review_report',
            'type' => 'redress_expert_review',
            'table' => 'redress_expert_review',
            'recordid_field' => 'recordid',
            'save_listorder' => false,
            'basic_form' => ['Rows' => ExpertReviewFields::getFields()],
            'main_recordid_label' => 'RED_ID',
            'fieldFormatsTable' => 'REDEXP',
            'useIdentity' => true,
        ],
        'red_compensation_recovery' => [
            'title' => _fdtk('compensation_recovery'),
            'section' => 'red_compensation_recovery',
            'type' => 'red_compensation_recovery',
            'table' => 'red_compensation_recovery',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => ['Rows' => CRUFields::getFields()],
            'main_recordid_label' => 'RED_ID',
            'fieldFormatsTable' => 'REDCRU',
            'useIdentity' => true,
        ],
        'red_legal_and_risk_services' => [
            'title' => _fdtk('red_legal_and_risk_services'),
            'section' => 'red_legal_and_risk_services',
            'type' => 'red_legal_and_risk_services',
            'table' => 'red_legal_and_risk_services',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => ['Rows' => LegalRiskServicesFields::getFields()],
            'main_recordid_label' => 'RED_ID',
            'fieldFormatsTable' => 'REDLRS',
            'useIdentity' => true,
        ],
        RedressSections::WRP_REQUESTS_FOR_INFORMATION => [
            'title' => 'Request for information',
            'section' => RedressSections::WRP_REQUESTS_FOR_INFORMATION,
            'type' => RedressSections::WRP_REQUESTS_FOR_INFORMATION,
            'table' => 'red_wrp_requests_for_information',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => RedressRequestForInfoFields::getFields(),
            ],
            'main_recordid_label' => 'RED_ID',
            'fieldFormatsTable' => 'REDRFI',
            'useIdentity' => true,
        ],
        RedressSections::LFER => [
            'title' => 'Learning from events extension requests',
            'section' => RedressSections::LFER,
            'type' => RedressSections::LFER,
            'table' => 'red_learning_from_events_requests',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => ['Rows' => RedressLferFields::getFields()],
            'main_recordid_label' => 'RED_ID',
            'fieldFormatsTable' => 'REDLFER',
            'useIdentity' => true,
        ],
    ],

    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $data): void {
                        (new RedressTrackerStageModelFactory())
                            ->getMapper()
                            ->saveStages($data);

                        Container::get(RedressDataHelper::class)
                            ->copyPersonAffectedToComplainant($data);
                    },
                ],
            ],
        ],
    ],

    // Pre-display hook for level 2 linked contacts
    ModuleDefKeys::EXTRA_CONTACT_LINK_DATA_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function ($data) {
                        return Container::get(RedressDataHelper::class)
                            ->getIsPersonAffectedAlsoClaimant(['data' => $data ?? []]);
                    },
                ],
            ],
        ],
    ],

    // Post-save hook for level 2 linked contacts
    ModuleDefKeys::EXTRA_RECORD_DATA_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $parameters): void {
                        $recordid = $parameters['main_recordid'] ?? null;
                        $isPersonAlsoClaimant = $parameters['data'][RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT] ?? null;

                        $data = [
                            'recordid' => $recordid,
                            RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT => $isPersonAlsoClaimant,
                        ];

                        Container::get(RedressDataHelper::class)
                            ->copyPersonAffectedToComplainant($data);
                    },
                ],
            ],
        ],
    ],

    'DATA_VALIDATION_INCLUDES' => [
        'Source/generic_modules/CLA/ModuleFunctions.php',
    ],
    'DATA_VALIDATION_FUNCTIONS' => [
        'validateLinkedOrganisations',
    ],

    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $eventParameters): array {
                        return Container::get(DataHelper::class)
                            ->populateOpenedDate(
                                RedressFields::DATE_RECORD_OPENED,
                                'redress_main',
                                (int)
                                $eventParameters['recordId'],
                                $eventParameters['data'],
                            );
                    },
                ],
            ],
        ],
    ],

    'CONTACT_LINK_TABLE_ID' => ['link_respondents' => 'main_recordid'],

    'AGE_AT_DATE' => RedressFields::DATE_OF_INDEX_INCIDENT,

    'FIELDSET_MAPPINGS' => [
        ContactTypes::INDIVIDUAL_RESPONDENT => 129,
        ContactTypes::RESPONDENT => 130,
        ContactTypes::CLAIMANT => 125,
        ContactTypes::PERSON_AFFECTED => 126,
        ContactTypes::EMPLOYEE => 127,
        ContactTypes::OTHER_CONTACT => 128,
        'linked_actions' => 136,
        'payments' => 135,
    ],
    'CAN_LINK_MODULES' => true,

    'CCS2_FIELDS' => [
        RedressFields::RED_AFFECTING_TIER_ZERO,
        RedressFields::RED_TYPE_TIER_ONE,
        RedressFields::RED_TYPE_TIER_TWO,
        RedressFields::RED_TYPE_TIER_THREE,
    ],
    'DATE_OPENED_AT' => RedressFields::DATE_RECORD_OPENED,
];
