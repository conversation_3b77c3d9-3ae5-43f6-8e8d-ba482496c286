<?php

use app\models\generic\valueObjects\Module;
use Source\generic_modules\FieldDefKeys;
use src\progressnotes\models\ProgressNotesFields;
use src\redress\models\CRUFields;
use src\redress\models\ExpertReviewFields;
use src\redress\models\LegalRiskServicesFields;
use src\redress\models\RedressFields;
use src\redress\models\RedressLferFields;
use src\redress\models\RedressRequestForInfoFields;
use src\redress\models\RedressTrackerFields;
use src\system\database\FieldInterface;

$FieldDefs[Module::REDRESS] = [
    'recordid' => [
        'Type' => FieldInterface::NUMBER_DB,
        'Title' => 'ID',
        'IdentityCol' => true,
        'ReadOnly' => true,
        'Width' => 5,
        'Table' => 'redress_main',
    ],
    RedressFields::TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Redress record type',
        'Table' => 'redress_main',
    ],
    RedressFields::SUB_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Redress record sub-type',
        'Table' => 'redress_main',
    ],
    RedressFields::HEALTH_BODY_REF => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Health body redress record reference',
        'Table' => 'redress_main',
    ],
    RedressFields::WRP_REF => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Welsh Risk Pool reference',
        'Table' => 'redress_main',
    ],
    RedressFields::LEGAL_AND_RISK_REF => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Legal & Risk Services reference',
        'Table' => 'redress_main',
    ],
    RedressFields::LINKED_TO_SI => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Linked to SI notified to WG?',
        'Table' => 'redress_main',
    ],
    RedressFields::SI_REF => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'SI reference',
        'Table' => 'redress_main',
    ],
    RedressFields::PATIENT_NAME => [
        'Type' => FieldInterface::STRING_DB,
        'Width' => 254,
        'Title' => 'Patient name',
        'Table' => 'redress_main',
    ],
    RedressFields::DESCRIPTION => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Description',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'redress_main',
    ],
    RedressFields::NHS_HEALTH_BODY => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'NHS Wales health body',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_OF_INDEX_INCIDENT => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date of index incident',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_CONCERN_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date concern received by health body',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_RECORD_OPENED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date record opened',
        'ReadOnly' => true,
        'NoReadOnly' => true,
        'NoDefault' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::EST_FIN_YEAR_COMPLETE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Estimated financial year for case to complete',
        'Table' => 'redress_main',
    ],
    RedressFields::PTR_GRADING => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'PTR grading',
        'Table' => 'redress_main',
    ],
    RedressFields::LEVEL_OF_HARM => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Level of harm',
        'Table' => 'redress_main',
    ],
    RedressFields::LOCATION_ID => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Location',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => 'redress_main',
    ],
    RedressFields::SECONDARY_LOCATION_ID => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Secondary location',
        'requireMinChars' => true,
        'mapperType' => 'location',
        'Table' => 'redress_main',
    ],
    RedressFields::LOCATION_EXACT => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Exact location',
        'Table' => 'redress_main',
    ],
    RedressFields::PRINCIPLE_CLINICAL_SPECIALTY => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Principal clinical specialty',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => 'redress_main',
    ],
    RedressFields::SECONDARY_CLINICAL_SPECIALTY => [
        'Type' => FieldInterface::TREE_DB,
        'Title' => 'Secondary clinical specialty',
        'requireMinChars' => true,
        'mapperType' => 'service',
        'Table' => 'redress_main',
    ],
    RedressFields::CONFIRM_LOCATION_ID => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Location?',
        'NoListCol' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::CONFIRM_SERVICE_ID => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Service?',
        'NoListCol' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::IS_CLAIMANT_LITIGANT_IN_PERSON => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is the claimant a litigant in person?',
        'Table' => 'redress_main',
    ],
    RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Is the person affected also the claimant?',
        'Table' => 'redress_main',
    ],
    RedressFields::SHOW_OTHER_CONTACTS => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Are there any other contacts involved?',
        'Table' => 'redress_main',
    ],
    RedressFields::SHOW_DOCUMENT => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Are there any documents to attach?',
        'Table' => 'redress_main',
    ],
    RedressFields::CONSENT_RECEIVED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Consent received',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_CONSENT_REQUESTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date consent requested from patient / NOK',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_CONSENT_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date consent received from patient / NOK',
        'Table' => 'redress_main',
    ],
    RedressFields::CASE_MANAGER => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Redress case manager',
        'Table' => 'redress_main',
    ],
    RedressFields::ESCALATION_MANAGER => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Escalation manager',
        'Table' => 'redress_main',
    ],
    RedressFields::REG_RESPONSE_SENT => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Reg response sent (HB)',
        'Table' => 'redress_main',
    ],
    RedressFields::HEALTH_BODY_CASE_MANAGER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Health body redress case manager',
        'Table' => 'redress_main',
    ],
    RedressFields::HEALTH_BODY_CASE_MANAGER_TEL => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Health body redress case manager telephone number',
        'Table' => 'redress_main',
    ],
    RedressFields::HEALTH_BODY_CASE_MANAGER_EMAIL => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Health body case manager email',
        'Table' => 'redress_main',
    ],
    RedressFields::POSITION_ON_BREACH_OF_DUTY => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Position on breach of duty?',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_BREACH_CONFIRMED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date breach of duty confirmed',
        'Table' => 'redress_main',
    ],
    RedressFields::BREACHES_OF_DUTY_IDENTIFIED => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Breaches of duty identified',
        'Table' => 'redress_main',
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::ROWS => 10,
    ],
    RedressFields::POSITION_ON_CAUSATION => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Position on causation?',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_LIABILITY_ADMITTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date qualifying liability admitted to complainant',
        'Table' => 'redress_main',
    ],
    RedressFields::INJURY_MATTERS_IDENTIFIED => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Injury / Illness / Causation matters identified',
        'Table' => 'redress_main',
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::ROWS => 10,
    ],
    RedressFields::SUMMARY_OF_ACTIONS => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Summary of actions taken to address issues identified',
        'Table' => 'redress_main',
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::ROWS => 10,
    ],
    RedressFields::DATE_LIABILITY_CONFIRMED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date qualifying liability confirmed',
        'Table' => 'redress_main',
    ],
    RedressFields::CAUSAL_FACTORS_LINKED => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Were there any causal factors?',
        'Table' => 'redress_main',
    ],
    RedressFields::LAST_UPDATED => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Last Updated',
        'Width' => 32,
        'Computed' => true,
        'NoSearch' => true,
        'ReadOnly' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::REF_LOSSES_SPECIAL_PAYMENTS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Losses and Special Payments Reference (LASPAR)',
        'Table' => 'redress_main',
    ],
    RedressFields::LFER_DUE_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Learning from events due',
        'Table' => 'redress_main',
    ],
    RedressFields::LFER_SENT_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Learning from events sent to WRP',
        'Table' => 'redress_main',
    ],
    RedressLferFields::EXTENSION_REQUESTED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Extension to LFER submission requested?',
        'Table' => 'red_learning_from_events_requests',
    ],
    RedressLferFields::EXTENSION_REQUESTED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date extension to LFER submission requested',
        'Table' => 'red_learning_from_events_requests',
    ],
    RedressLferFields::EXTENSION_GRANTED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Extension to LFER submission granted?',
        'Table' => 'red_learning_from_events_requests',
    ],
    RedressLferFields::EXTENSION_GRANTED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date extension to LFER granted?',
        'Table' => 'red_learning_from_events_requests',
    ],
    RedressLferFields::EXTENSION_REASON => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Reason for LFER extension',
        'Table' => 'red_learning_from_events_requests',
    ],
    RedressFields::CASE_REPORT_DUE_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date case management report (CRM) due',
        'Table' => 'redress_main',
    ],
    ProgressNotesFields::PROGRESS_NOTE_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Progress Notes',
        'Table' => 'progress_notes',
    ],
    RedressFields::DATE_CMR_SENT_TO_WRP => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Case management report (CMR) sent to WRP',
        'Table' => 'redress_main',
    ],
    RedressFields::DATE_APPROVE_LEARNING => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date WRP approve learning',
        'Table' => 'redress_main',
    ],

    ExpertReviewFields::EXPERT_NAME => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Name of expert',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::EXPERT_SPECIALTY => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Expert specialty',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::DATE_EXPERT_INSTRUCTED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date expert instructed',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::DATE_EXPERT_REPORT_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date expert report received',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::EXPERT_COST => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'Expert cost',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::EXPERT_REPORT_OUTCOME => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Outcome of expert report',
        'Table' => 'redress_expert_review',
    ],
    ExpertReviewFields::EXPERT_ADDITIONAL_FINDINGS => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Issue/additional findings of the expert',
        'Table' => 'redress_expert_review',
        'Rows' => 10,
        'Columns' => 70,
    ],

    CRUFields::REQUEST_SENT => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'CRU request sent',
        'Table' => 'red_compensation_recovery',
        FieldDefKeys::NOT_FUTURE => true,
    ],
    CRUFields::REQUEST_RECEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'CRU requested received',
        'Table' => 'red_compensation_recovery',
    ],
    CRUFields::EXPIRY_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date CRU expires',
        'Table' => 'red_compensation_recovery',
    ],
    CRUFields::AMOUNT => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'CRU amount',
        'Table' => 'red_compensation_recovery',
    ],
    CRUFields::NHS_CHARGE_AMOUNT => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'NHS charge amount',
        'Table' => 'red_compensation_recovery',
    ],
    CRUFields::RETURNED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'CRU returned',
        'Table' => 'red_compensation_recovery',
        FieldDefKeys::NOT_FUTURE => true,
    ],
    CRUFields::ADDITIONAL_INFORMATION => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Additional CRU information',
        'Table' => 'red_compensation_recovery',
    ],

    LegalRiskServicesFields::LRD_REFERENCE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'L&RD reference',
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::LR_ADVICE_REQUESTED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'L&R advice requested?',
        'Table' => 'redress_main',
        FieldDefKeys::NOT_FUTURE => true,
    ],
    LegalRiskServicesFields::ADVICE_TYPE => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'Type of advice',
        'MaxLength' => 128,
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::SOLICITOR_NAME => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Names of solicitor at L&R ',
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::DATE_SENT_TO_LR => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date sent to L&R',
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::DATE_ADVICE_REVEIVED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date advice received',
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::LR_COST => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'L&R cost',
        'Table' => 'redress_main',
    ],
    LegalRiskServicesFields::REIMBURSABLE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Reimbursable?',
        'Table' => 'redress_main',
    ],
    RedressFields::APPROVAL_STATUS => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Approval status',
        'Table' => 'redress_main',
    ],

    // Causal factors
    'caf_level_1' => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Level 1',
        'FieldFormatsName' => 'caf_level_1',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'causal_factors',
    ],
    'caf_level_2' => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Level 2',
        'FieldFormatsName' => 'caf_level_2',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'causal_factors',
    ],

    // Profile fields
    'RED_SAVED_QUERIES' => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => _fdtk('choose_saved_queries'),
        'MaxLength' => 70,
    ],

    // Redress tracker
    RedressTrackerFields::STAGE_NAME => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Stage name',
        'NoMandatory' => true,
        'NoHide' => true,
        'Table' => 'redress_tracker_stage',
    ],
    RedressTrackerFields::STAGE_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Stage date',
        'Table' => 'redress_tracker_stage',
        FieldDefKeys::NOT_FUTURE => true,
    ],
    RedressTrackerFields::NARRATIVE => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => 'Narrative',
        'NoTimestamp' => true,
        'Rows' => 5,
        'Columns' => 70,
        'Table' => 'redress_tracker_stage',
    ],
    RedressTrackerFields::OFFER_TYPE => [
        'Type' => FieldInterface::MULTI_SELECT_DB,
        'Title' => 'Offer type',
        'MaxLength' => 8000,
        'Table' => 'redress_tracker_stage',
    ],
    RedressTrackerFields::OFFER_AMOUNT => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'Offer amount',
        'Table' => 'redress_tracker_stage',
    ],
    RedressTrackerFields::AUTHOR => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Author',
        'StaffField' => true,
        'Table' => 'redress_tracker_stage',
    ],
    RedressFields::NON_REIMBURSABLE_PAYMENTS => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'Non-reimbursable',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::REIMBURSABLE_PAYMENTS => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'Reimbursable',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::TOTAL_PAYMENTS => [
        'Type' => FieldInterface::MONEY_DB,
        'Title' => 'Total',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::SOURCE_OF_RECORD => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Source Of Record',
        'NoReadOnly' => true,
        'NoMandatory' => true,
        'ReadOnly' => true,
        'Table' => 'redress_main',
    ],
    RedressFields::CLOSED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Closed date',
        'Table' => 'redress_main',
    ],
    RedressFields::CASE_MANAGEMENT_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Type of case management deferred',
        'Table' => 'redress_main',
    ],
    RedressFields::CASE_DEFERRED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date case deferred',
        'Table' => 'redress_main',
    ],
    RedressFields::CASE_SUBMITTED_FOR_REIMBURSEMENT => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date case submitted for reimbursement',
        'Table' => 'redress_main',
    ],
    RedressRequestForInfoFields::IS_INFO_REQUESTED => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Information requested by WRP',
        'Table' => 'red_wrp_requests_for_information',
    ],
    RedressRequestForInfoFields::INFO_REQUESTED_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date further information requested',
        'Table' => 'red_wrp_requests_for_information',
    ],
    RedressRequestForInfoFields::INFO_DUE_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date further information due',
        'Table' => 'red_wrp_requests_for_information',
    ],
    RedressRequestForInfoFields::INFO_SENT_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => 'Date further information sent',
        'Table' => 'red_wrp_requests_for_information',
    ],
    RedressRequestForInfoFields::INFO_TYPE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Type of further information requested?',
        'Table' => 'red_wrp_requests_for_information',
    ],
    RedressRequestForInfoFields::INFO_DETAILS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'What further information requested?',
        'Table' => 'red_wrp_requests_for_information',
    ],
    // CCS2
    RedressFields::RED_AFFECTING_TIER_ZERO => [
        'Type' => 'ff_select',
        'Title' => 'Incident affecting',
        'Table' => 'redress_main',
    ],
    RedressFields::RED_TYPE_TIER_ONE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 1',
        'Table' => 'redress_main',
    ],
    RedressFields::RED_TYPE_TIER_TWO => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 2',
        'Table' => 'redress_main',
    ],
    RedressFields::RED_TYPE_TIER_THREE => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 3',
        'Table' => 'redress_main',
    ],
];
