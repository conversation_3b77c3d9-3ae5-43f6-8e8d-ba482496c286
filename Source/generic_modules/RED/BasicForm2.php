<?php

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormSectionHelperFactory;
use src\actionchains\controllers\ActionChainController;
use src\addanother\controllers\GenericAddAnotherController;
use src\component\form\FormTable;
use src\contacts\controllers\SearchCriteriaController;
use src\email\controllers\EmailHistoryController;
use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\payments\controllers\PaymentController;
use src\reasons\controllers\ReasonsController;
use src\redress\controllers\RedressTrackerController;
use src\redress\models\CRUFields;
use src\redress\models\ExpertReviewFields;
use src\redress\models\LegalRiskServicesFields;
use src\redress\models\RedressFields;
use src\redress\models\RedressLferFields;
use src\redress\models\RedressRequestForInfoFields;
use src\redress\models\RedressSections;
use src\redress\models\RedressTrackerFields;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$FormType = $basicFormHelper->getValidFormMode($FormType);

$sectionHelper = (new BasicFormSectionHelperFactory())->create($FormType);

$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$registry ??= Container::get(Registry::class);
$config ??= Container::get(DatixConfig::class);
$showLastChildFirst = $config->showLastChildFirst();
$showCCS2Fields = $registry->getParm('CCS2_INC', 'N')->isTrue();

// Gotta go all WordPress here since BasicForm files are required several times in some instances..
if (!function_exists('createRespondentsSection')) {
    function createRespondentsSection(string $formType): array
    {
        $useFormDesignLanguage = $formType === FormTable::MODE_DESIGN;

        $section = [
            'Title' => _fdtk('redress_claimant_representative', $useFormDesignLanguage),
            'contacttype' => ContactTypes::INDIVIDUAL_RESPONDENT,
            'NoFieldAdditions' => true,
            'Rows' => [],
        ];

        if ($formType === FormTable::MODE_SEARCH) {
            $section['ControllerAction'] = [
                'generateMultipleSearchCriteria' => [
                    'controller' => src\contacts\controllers\SearchCriteriaController::class,
                ],
            ];
            $section['NotModes'] = [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT];
            $section['ExtraParameters'] = [
                'linkedSections' => [
                    ContactTypes::INDIVIDUAL_RESPONDENT => [
                        'module' => 'CON',
                        'label' => _fdtk('red_individual_respondent_title', $useFormDesignLanguage),
                        'sectionId' => 'respondents_individual',
                    ],
                    ContactTypes::RESPONDENT => [
                        'module' => 'ORG',
                        'label' => _fdtk('red_organisation_respondent_title', $useFormDesignLanguage),
                        'sectionId' => 'respondents_organisation',
                    ],
                ],
                'module' => Module::REDRESS,
            ];
        } else {
            $section['LinkedForms'] = [
                ContactTypes::INDIVIDUAL_RESPONDENT => ['module' => Module::CONTACTS, 'Label' => _fdtk('label_individual_respondent_form_design', $useFormDesignLanguage)],
                ContactTypes::RESPONDENT => ['module' => Module::ORGANISATIONS, 'Label' => _fdtk('label_organisation_respondent_form_design', $useFormDesignLanguage)],
            ];
            $section['ControllerAction'] = [
                'ListLinkedRespondents' => [
                    'controller' => src\respondents\controllers\RespondentsController::class,
                ],
            ];
            $section['NotModes'] = [FormTable::MODE_NEW, FormTable::MODE_SEARCH];
        }
        if (Container::get(Registry::class)->getParm('CUSTOMIZABLE_RESPONDENT', 'N', true)->toScalar() == 'Y') {
            $section['Listings'] = [ContactTypes::RESPONDENT => ['module' => Module::CONTACTS]];
        }

        return $section;
    }
}

if (!function_exists('createPaymentsSection')) {
    function createPaymentsSection(string $formType): array
    {
        $useFormDesignLanguage = $formType === FormTable::MODE_DESIGN;

        $section = [
            'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'NotModes' => [FormTable::MODE_NEW],
            'LinkedForms' => ['payments' => ['module' => Module::PAYMENTS]],
            'Listings' => ['payments' => ['module' => Module::PAYMENTS]],
        ];

        if ($formType === FormTable::MODE_SEARCH) {
            $section['LinkedDataSection'] = true;
            $section['ControllerAction'] = [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ];
            $section['ExtraParameters'] = [
                'linkModule' => Module::PAYMENTS,
                'module' => Module::REDRESS,
                'sectionId' => 'payments',
                'link_type' => 'payments',
            ];
        } else {
            $section['NotModes'] = [FormTable::MODE_NEW];
            $section['Condition'] = $formType !== FormTable::MODE_LINKED_DATA_SEARCH;
            $section['ControllerAction'] = [
                'listPayments' => [
                    'controller' => PaymentController::class,
                ],
            ];
        }

        return $section;
    }
}

if (!function_exists('createActionsSection')) {
    function createActionsSection(string $formType): array
    {
        $useFormDesignLanguage = $formType === FormTable::MODE_DESIGN;

        $section = [
            'Title' => _fdtk('actions', $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'Rows' => [],
        ];

        if ($formType === FormTable::MODE_SEARCH) {
            $section['LinkedDataSection'] = true;
            $section['ControllerAction'] = [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ];
            $section['ExtraParameters'] = ['linkModule' => Module::ACTIONS, 'module' => Module::REDRESS, 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'];
            $section['NotModes'] = [FormTable::MODE_NEW];
            $section['LinkedForms'] = ['linked_actions' => ['module' => Module::ACTIONS]];
        } else {
            $section['Special'] = 'LinkedActions';
            $section['LinkedForms'] = ['linked_actions' => ['module' => Module::ACTIONS, 'carltonFormDesigns' => true]];
            $section['NotModes'] = [FormTable::MODE_NEW];
            $section['Condition'] = $formType !== FormTable::MODE_LINKED_DATA_SEARCH;
        }

        return $section;
    }
}

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],

    'record_and_event_summary' => [
        'Title' => _fdtk('redress_record_and_event_summary', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            [
                'Name' => RedressFields::SOURCE_OF_RECORD,
                'ReadOnly' => true,
            ],
            RedressFields::DATE_CONCERN_RECEIVED,
            RedressFields::DATE_RECORD_OPENED,
            RedressFields::HEALTH_BODY_REF,
            RedressFields::PATIENT_NAME,
            RedressFields::CONSENT_RECEIVED,
            RedressFields::DATE_CONSENT_REQUESTED,
            RedressFields::DATE_CONSENT_RECEIVED,
            RedressFields::CASE_MANAGER,
            RedressFields::ESCALATION_MANAGER,
            RedressFields::TYPE,
            RedressFields::SUB_TYPE,
            RedressFields::REG_RESPONSE_SENT,
            RedressFields::LINKED_TO_SI,
            RedressFields::SI_REF,
            RedressFields::CLOSED_DATE,
        ],
    ],

    'health_body_details' => [
        'Title' => _fdtk('redress_health_body_details', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::NHS_HEALTH_BODY,
            RedressFields::HEALTH_BODY_CASE_MANAGER,
            RedressFields::HEALTH_BODY_CASE_MANAGER_TEL,
            RedressFields::HEALTH_BODY_CASE_MANAGER_EMAIL,
        ],
    ],

    'event_details' => [
        'Title' => _fdtk('redress_event_details', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::DATE_OF_INDEX_INCIDENT,
            RedressFields::PTR_GRADING,
            RedressFields::LEVEL_OF_HARM,
        ],
    ],

    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => RedressFields::LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => RedressFields::CONFIRM_LOCATION_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => RedressFields::SECONDARY_LOCATION_ID,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            RedressFields::LOCATION_EXACT,
        ],
    ],

    'specialties' => [
        'Title' => _fdtk('redress_specialties', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => RedressFields::PRINCIPLE_CLINICAL_SPECIALTY,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => RedressFields::CONFIRM_SERVICE_ID,
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => RedressFields::SECONDARY_CLINICAL_SPECIALTY,
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],

    'status' => [
        'Title' => _fdtk('redress_status', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::LAST_UPDATED,
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => Module::REDRESS,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => Module::REDRESS,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
        ],
    ],

    'rejection' => GenericRejectionArray(Module::REDRESS, $data, $useFormDesignLanguage),
    'rejection_history' => [
        'Title' => _fdtk('reasons_history_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'SectionRejectionHistory' => [
                'controller' => ReasonsController::class,
            ],
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [],
    ],

    'case_details' => [
        'Title' => _fdtk('redress_case_details', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::DESCRIPTION,
            RedressFields::POSITION_ON_BREACH_OF_DUTY,
            RedressFields::DATE_BREACH_CONFIRMED,
            RedressFields::BREACHES_OF_DUTY_IDENTIFIED,
            RedressFields::POSITION_ON_CAUSATION,
            RedressFields::DATE_LIABILITY_ADMITTED,
            RedressFields::INJURY_MATTERS_IDENTIFIED,
            RedressFields::SUMMARY_OF_ACTIONS,
            RedressFields::DATE_LIABILITY_CONFIRMED,
        ],
    ],

    'redress_tracker_form' => [
        'Title' => _fdtk('redress_tracker', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN],
        'ControllerAction' => [
            'displayRedressTracker' => [
                'controller' => RedressTrackerController::class,
            ],
        ],
        'Rows' => [],
    ],

    'redress_tracker_design' => [
        'Title' => _fdtk('redress_tracker', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_DESIGN,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'redress_tracker_form',
        'NoSectionActions' => true,
        'Rows' => RedressTrackerFields::getFields(),
    ],

    'welsh_risk_pool' => [
        'Title' => _fdtk('redress_welsh_risk_pool', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::WRP_REF,
            RedressFields::EST_FIN_YEAR_COMPLETE,
            RedressFields::CASE_REPORT_DUE_DATE,
            RedressFields::DATE_CMR_SENT_TO_WRP,
            RedressFields::DATE_APPROVE_LEARNING,
        ],
    ],

    RedressSections::CASE_MANAGEMENT_REPORT => [
        'Title' => _fdtk('redress_case_management_report', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::CASE_MANAGEMENT_TYPE,
            RedressFields::CASE_DEFERRED_DATE,
            RedressFields::CASE_SUBMITTED_FOR_REIMBURSEMENT,
        ],
    ],
    'rf1_checklist' => [
        'Title' => _fdtk('redress_rf1_checklist', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Special' => 'NarrativeNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'legal_and_risk_services' => [
        'Title' => _fdtk('red_legal_and_risk_services', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN],
        'LinkedDataSection' => true,
        'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => [
            'linkedRecordSpecificationKey' => 'red_legal_and_risk_services',
        ],
    ],
    'legal_and_risk_services_design' => [
        'Title' => _fdtk('red_legal_and_risk_services', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'legal_and_risk_services',
        'NoSectionActions' => true,
        'Rows' => LegalRiskServicesFields::getFields(),
    ],

    'persons_connected' => [
        'Title' => _fdtk('redress_persons_connected', $useFormDesignLanguage),
        'Rows' => [],
    ],

    'contacts_type_M' => $sectionHelper->createContactSection(Module::REDRESS, ContactTypes::CLAIMANT),

    'respondents' => createRespondentsSection($FormType),

    'contacts_type_A' => $sectionHelper->createContactSection(Module::REDRESS, ContactTypes::PERSON_AFFECTED),
    'contacts_type_E' => $sectionHelper->createContactSection(Module::REDRESS, ContactTypes::EMPLOYEE),
    'contacts_type_N' => $sectionHelper->createContactSection(Module::REDRESS, ContactTypes::OTHER_CONTACT),

    'contacts_type_R' => [
        'Title' => _fdtk('redress_details_of_person_reporting', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => Module::REDRESS,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::REPORTER,
        'Role' => $registry->getParm('REPORTER_ROLE', 'REP')->toScalar(),
        'LinkedForms' => [ContactTypes::REPORTER => ['module' => Module::CONTACTS]],
        'suffix' => 3,
        'Rows' => [],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
    ],

    'expert_report_review' => [
        'Title' => _fdtk('redress_expert_reports', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN],
        'LinkedDataSection' => true,
        'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => ['linkedRecordSpecificationKey' => 'redress_expert_review'],
    ],
    'expert_report_design' => [
        'Title' => _fdtk('redress_expert_reports', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'expert_report_review',
        'NoSectionActions' => true,
        'Rows' => ExpertReviewFields::getFields(),
    ],

    'causal_factor_header' => [
        'Title' => _fdtk('causal_factors_for_redress', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NewPanel' => true,
        'Rows' => [
            'causal_factors_linked',
        ],
    ],

    'causal_factor' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType !== FormTable::MODE_DESIGN),
        'LinkedDataSection' => true,
        'NoTitle' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'DoCausalFactorsSection' => [
                'controller' => src\causalfactors\controllers\CausalFactorsController::class,
            ],
        ],
        'ExtraParameters' => ['causal_factor_name' => 'red_causal_factor'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],

    'causal_factor_design' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'causal_factor',
        'NoSectionActions' => true,
        'Rows' => [
            'caf_level_1',
            'caf_level_2',
        ],
    ],

    'quantum_costs_and_cru' => [
        'Title' => _fdtk('redress_quantum_costs_and_cru', $useFormDesignLanguage),
        'Rows' => [RedressFields::REF_LOSSES_SPECIAL_PAYMENTS],
    ],

    'payments_summary' => [
        'Title' => _fdtk('redress_payments_summary', $useFormDesignLanguage),
        'Rows' => [
            RedressFields::NON_REIMBURSABLE_PAYMENTS,
            RedressFields::REIMBURSABLE_PAYMENTS,
            RedressFields::TOTAL_PAYMENTS,
        ],
    ],

    'compensation_recovery' => [
        'Title' => _fdtk('compensation_recovery', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_DESIGN],
        'LinkedDataSection' => true,
        'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => src\addanother\controllers\GenericAddAnotherController::class,
            ],
        ],
        'ExtraParameters' => [
            'linkedRecordSpecificationKey' => 'red_compensation_recovery',
        ],
    ],
    'compensation_recovery_design' => [
        'Title' => _fdtk('compensation_recovery', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'compensation_recovery',
        'NoSectionActions' => true,
        'Rows' => CRUFields::getFields(),
    ],

    'payments' => createPaymentsSection($FormType),

    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],

    'word' => [
        'Title' => _fdtk('mod_templates_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            ],
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH, FormTable::MODE_PRINT],
        'Rows' => [],
    ],

    'linked_records' => [
        'Title' => _fdtk('linked_records'),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Condition' => $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'Special' => 'LinkedRecords',
        'Rows' => [],
    ],

    'history' => [
        'Title' => _fdtk('notifications', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => EmailHistoryController::class,
            ],
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [],
    ],

    'feedback' => [
        'Title' => _fdtk('feedback_title', $useFormDesignLanguage),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],

    'linked_actions' => createActionsSection($FormType),

    'action_chains' => [
        'Title' => _fdtk('action_chains', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'getActionChains' => [
                'controller' => ActionChainController::class,
            ],
        ],
        'LinkedForms' => ['action_chains' => ['module' => Module::ACTIONS]],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Rows' => [],
    ],

    RedressSections::WRP_REQUESTS_FOR_INFORMATION => [
        'Title' => _fdtk('table_red_wrp_requests_for_information', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'module' => Module::REDRESS,
        'LinkedDataSection' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => GenericAddAnotherController::class,
            ],
        ],
        'Rows' => RedressRequestForInfoFields::getFields(),
        'ExtraParameters' => ['linkedRecordSpecificationKey' => RedressSections::WRP_REQUESTS_FOR_INFORMATION],
    ],

    RedressSections::LFER => [
        'Title' => 'Learning from events extension requests',
        'NoLazyLoad' => true,
        'module' => Module::REDRESS,
        'LinkedDataSection' => true,
        'ControllerAction' => [
            'doGenericAddAnotherSection' => [
                'controller' => GenericAddAnotherController::class,
            ],
        ],
        'Rows' => RedressLferFields::getFields(),
        'ExtraParameters' => ['linkedRecordSpecificationKey' => RedressSections::LFER],
    ],

    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => $showCCS2Fields,
        'Rows' => [
            RedressFields::RED_AFFECTING_TIER_ZERO,
            RedressFields::RED_TYPE_TIER_ONE,
            RedressFields::RED_TYPE_TIER_TWO,
            RedressFields::RED_TYPE_TIER_THREE,
        ],
    ],
];

$FormArray = $sectionHelper->addLinkedModuleListings($FormArray);

return $FormArray;
