<?php

use src\redress\models\RedressFields;
use src\redress\models\RedressSections;

$GLOBALS['FormTitle'][7] = _fdtk('red1_title');

$GLOBALS['ExpandSections'] = [
    'is_claimant_litigant_in_person' => [
        0 => [
            'section' => 'respondents_organisation',
            'alerttext' => '',
            'values' => [
                0 => 'N',
            ],
        ],
    ],
    'show_other_contacts' => [
        0 => [
            'section' => 'contacts_type_N',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'show_document' => [
        0 => [
            'section' => 'documents',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
];

$GLOBALS['DefaultValues'] = [
    'rep_approved' => 'UN',
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'principle_service_id' => false,
    'secondary_location_id' => false,
    'secondary_service_id' => false,
];

$GLOBALS['HideFields'] = [
    RedressFields::CASE_MANAGER => true,
    RedressSections::YOUR_MANAGER => true,
    'ccs2' => true,
];
