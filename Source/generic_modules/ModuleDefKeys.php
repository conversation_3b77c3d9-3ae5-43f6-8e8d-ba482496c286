<?php

namespace Source\generic_modules;

use Exception;
use RuntimeException;

final class ModuleDefKeys
{
    public const LOCATION_FIELD = 'LOCATION_FIELD'; // Denotes the name of the main location field
    public const SERVICE_FIELD = 'SERVICE_FIELD'; // Denotes the name of the main service field
    public const CODE = 'CODE';
    public const ID = 'MOD_ID';
    public const VIEW = 'VIEW';
    public const TABLE = 'TABLE';
    public const ICON = 'ICON';
    public const USES_APPROVAL_STATUSES = 'USES_APPROVAL_STATUSES';
    public const NON_GRAPHICAL_FIELDSETS = 'NON_GRAPHICAL_FIELDSETS';
    public const POST_SAVE_HOOK = 'POST_SAVE_HOOK';
    public const POST_LINK_CONTACT_SAVE_EVENT = 'POST_LINK_CONTACT_SAVE';
    public const EXTRA_RECORD_DATA_HOOK = 'EXTRA_RECORD_DATA_HOOK';
    public const EXTRA_RECORD_DATA_EVENT = 'EXTRA_RECORD_DATA_EVENT';
    public const EXTRA_CONTACT_LINK_DATA_EVENT = 'EXTRA_CONTACT_LINK_DATA';
    public const EXTRA_CONTACT_UNLINK_EVENT = 'EXTRA_CONTACT_UNLINK';
    public const PRE_SAVE_HOOK = 'PRE_SAVE_HOOK';
    public const HOME_SCREEN_STATUS_LIST = 'HOME_SCREEN_STATUS_LIST';
    public const APPROVAL_LEVELS = 'APPROVAL_LEVELS';
    public const FIELDSET_MAPPINGS = 'FIELDSET_MAPPINGS';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        throw new RuntimeException("Can't get an instance of ModuleDefKeys");
    }
}
