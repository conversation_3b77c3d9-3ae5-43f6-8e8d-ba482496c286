<?php

use app\models\generic\valueObjects\Module;
use app\models\modules\ModuleDisplayAcronyms;
use Source\generic_modules\ModuleDefKeys;
use src\framework\events\FormEventsHelper;
use src\payments\model\PaymentFields;

$ModuleDefs['PAY'] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => 'PAY',
    'AUDIT_TRAIL_PERMS' => ['PAY2'],  // which permissions can see the audit trail?

    'MOD_ID' => MOD_PAYMENTS,
    'CODE' => 'PAY',
    'APPROVAL_LEVELS' => false,
    'NAME' => _fdtk('mod_payments_title'),
    'USES_APPROVAL_STATUSES' => false,
    'VIEW' => 'vw_payments',
    'TABLE' => 'payments',
    'HAS_DEFAULT_LISTING' => true,
    'PERM_GLOBAL' => 'PAY_PERMS',
    'REC_NAME' => _fdtk('PAYName'),
    'REC_NAME_PLURAL' => _fdtk('PAYNames'),
    'REC_NAME_TITLE' => _fdtk('PAYNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('PAYNamesTitle'),
    // 'FK' => 'cla_id',
    'ACTION' => 'record&module=PAY',
    'LINK_ACTION' => 'record&module=PAY',
    'FORMS' => [
        2 => ['LEVEL' => 2, 'CODE' => 'PAY2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::PAYMENTS_LEVEL_2],
    ],
    'FORMCODE2' => 'PAY2',
    'ICON' => 'icons/icon_PAY.png',
    'ADD_NEW_RECORD_LEVELS' => ['CLA2', 'CLA1'],
    'NO_REP_APPROVED' => true,
    'HARD_CODED_LISTINGS' => [
        'all' => [
            'Title' => _fdtk('pay_all_listing_title'),
            'Link' => _fdtk('pay_all_listing_link'),
            'Where' => '1=1',
        ],
    ],

    'SEARCH_URL' => 'action=search',
    'DEFAULT_ORDER' => 'pay_date',

    'FIELD_ARRAY' => [
        'pay_date', 'pay_type', 'pay_subtype', 'pay_amount', 'pay_vat_rate', 'pay_total', 'pay_notes',
        'pay_calc_vat_amount', 'pay_calc_total', 'resp_id', 'pay_recipient', 'updateddate', 'updatedby', 'payee',
        PaymentFields::REIMBURSABLE, PaymentFields::PAY_TYPE_REDRESS,
    ],

    'VIEW_FIELD_ARRAY' => [
        'pay_date', 'pay_type', 'pay_subtype', 'pay_amount', 'pay_vat_rate', 'pay_total', 'pay_notes', 'payee',
        'pay_calc_vat_amount', 'pay_calc_total', 'pay_module', 'pay_cas_id', 'pay_link_title', 'resp_id', 'pay_recipient',
        PaymentFields::REIMBURSABLE, PaymentFields::PAY_TYPE_REDRESS,
    ],

    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserPAY2Settings',

    'DATA_VALIDATION_INCLUDES' => [
        'Source/generic_modules/PAY/ModuleFunctions.php',
    ],
    'DATA_VALIDATION_FUNCTIONS' => [
        'validateVAT',
    ],

    'LINKED_CONTACTS' => false,
    'NOTEPAD' => false,

    'LINKED_MODULE' => [
        'parent_ids' => [Module::CLAIMS => 'cla_id', Module::INCIDENTS => 'inc_id', Module::FEEDBACK => 'com_id', Module::REDRESS => 'red_id'],
        'link_recordid' => 'recordid',
        'panel' => 'payments',
    ],

    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function ($data): void {
                        updateRCFields($data);
                    },
                ],
            ],
        ],
    ],

    ModuleDefKeys::PRE_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_CONDITION => null,
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $data): array {
                        $data = formatRespondentID($data);

                        return CheckZeroReserves($data);
                    },
                ],
            ],
        ],
    ],

    'CUSTOM_DATA_TRANSFORM' => ['file' => 'Source/generic_modules/PAY/ModuleFunctions.php', 'function' => 'transformPaymentData'],

    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $eventParameters): array {
                        return ParentRecordAccessCheck($eventParameters['recordId']);
                    },
                ],
            ],
        ],
    ],

    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Payments/c_dx_payments_guide.xml',
];
