<?php

use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

function validateVAT()
{
    $Error = [];

    if ($_POST['pay_vat_rate'] < 0) {
        $Error['pay_vat_rate'] = 'VAT cannot be less than 0%';
    }

    return $Error;
}

/**
 * Updates the Rich Client payments fields on save to maintain interoperability between the two apps.
 *
 * @param array $data the submitted record data
 */
function updateRCFields($data)
{
    DatixDBQuery::PDO_query('UPDATE payments SET pay_vat = pay_calc_vat_amount, pay_total = pay_calc_total WHERE recordid = :recordid', ['recordid' => $data['recordid']]);
    if ($data['pay_type'] == 'RECE') {
        // RC stores these as negavtive values, so...
        DatixDBQuery::PDO_query('UPDATE payments SET pay_amount = 0 - pay_amount, pay_vat = 0 - pay_vat, pay_total = 0 - pay_total WHERE recordid = :recordid', ['recordid' => $data['recordid']]);
    }
}

/**
 * Inverts negative values stored for receipts before form view is rendered.
 *
 * @param array $data
 *
 * @return array $data
 */
function transformPaymentData($data)
{
    if ($data['pay_amount'] && $data['pay_type'] == 'RECE') {
        $data['pay_amount'] = abs($data['pay_amount']);
    }

    if ($data['pay_calc_total'] && $data['pay_type'] == 'RECE') {
        $data['pay_calc_total'] = abs($data['pay_calc_total']);
    }

    if ($data['pay_calc_vat_amount'] && $data['pay_type'] == 'RECE') {
        $data['pay_calc_vat_amount'] = abs($data['pay_calc_vat_amount']);
    }

    return $data;
}
/**
 * This checks whether a claim indemnity or expenses reserve needs to be adjusted after this payment has been added to
 * it. This happens if the relevant reserve is closed; this payment is assigned to that reserve and the pay date for this
 * payment; and the date for this payment is blank or before the close date.
 *
 * @param array $data takes the data posted in saving a claim and manipulates it before saving
 *
 * @return $data the return of the manipulated data
 */
function CheckZeroReserves(array $data): array
{
    require_once __DIR__ . '/../CLA/ModuleFunctions.php';

    return zeroReservesOnDate($data);
}

function formatRespondentID(array $data): array
{
    if ($data['resp_id'] === '') { // stored as an INT in the database, but for DW purposes is a coded field. This writes it to the database in the correct format.
        $data['resp_id'] = null;
    }

    return $data;
}

/**
 * Check whether the user has access to the parent record for this payment.
 */
function ParentRecordAccessCheck($recordid)
{
    $ModuleDefs = Container::get(ModuleDefs::class);
    $parentModule = strtoupper(DatixDBQuery::PDO_fetch('SELECT pay_module FROM vw_payments WHERE recordid= :recordid', ['recordid' => $recordid], PDO::FETCH_COLUMN));

    if ($ModuleDefs[$parentModule]['FK']) {
        $parentRecordid = DatixDBQuery::PDO_fetch('SELECT ' . $ModuleDefs[$parentModule]['FK'] . ' FROM vw_payments WHERE recordid = :recordid', ['recordid' => $recordid], PDO::FETCH_COLUMN);

        $whereClause = MakeSecurityWhereClause('recordid = ' . $parentRecordid, $parentModule);

        $accessToParent = DatixDBQuery::PDO_fetch(
            'SELECT count(*) FROM ' . $ModuleDefs[$parentModule]['TABLE'] . ' WHERE ' . $whereClause,
            [],
            PDO::FETCH_COLUMN,
        );
    }

    return ['permissions_to_linked_record' => ($accessToParent == '1')];
}
