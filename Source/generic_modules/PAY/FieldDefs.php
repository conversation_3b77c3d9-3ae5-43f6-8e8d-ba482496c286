<?php

use src\payments\model\PaymentFields;
use src\system\database\FieldInterface;

$FieldDefs['PAY'] = [
    'recordid' => [
        'Type' => 'number',
        'Title' => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
        'Table' => 'vw_payments',
    ],
    'pay_date' => [
        'Type' => 'date',
        'Title' => 'Payment date',
        'Table' => 'vw_payments',
    ],
    'pay_type' => [
        'Type' => 'ff_select',
        'Title' => 'Type',
        'Table' => 'vw_payments',
    ],
    'pay_subtype' => [
        'Type' => 'ff_select',
        'Title' => 'Subtype',
        'Table' => 'vw_payments',
    ],
    'pay_amount' => [
        'Type' => 'money',
        'Title' => 'Amount',
        'Table' => 'vw_payments',
    ],
    'pay_vat_rate' => [
        'Type' => 'decimal',
        'Title' => 'VAT rate (%)',
        'MaxValue' => '999.99',
        'MinValue' => '-999.99',
        'Table' => 'vw_payments',
    ],
    'pay_calc_vat_amount' => [
        'Type' => 'money',
        'ReadOnly' => true,
        'Computed' => true,
        'Title' => 'VAT Amount',
        'Table' => 'vw_payments',
    ],
    'pay_calc_total' => [
        'Type' => 'money',
        'ReadOnly' => true,
        'Computed' => true,
        'Title' => 'Total',
        'Table' => 'vw_payments',
    ],
    'pay_notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'Table' => 'vw_payments',
    ],
    'pay_cas_id' => [
        'Type' => 'number',
        'Title' => 'Linked record ID',
        'ReadOnly' => true,
        'Table' => 'vw_payments',
    ],
    'pay_module' => [
        'Type' => 'ff_select',
        'Title' => 'Module',
        'ReadOnly' => true,
        'CustomCodes' => [
            'INC' => _fdtk('mod_incidents_title'),
            'CLA' => _fdtk('mod_claims_title'),
            'COM' => _fdtk('mod_complaints_title'),
        ],
    ],
    'pay_link_title' => [
        'Type' => 'string',
        'Title' => 'Name',
        'ReadOnly' => true,
    ],
    'resp_id' => [
        'Type' => 'ff_select',
        'Title' => 'Respondents',
        'Width' => 5,
        'SelectFunction' => [
            'getRespondentsForPayment', [
                'cla_id' => '',
            ],
        ],
    ],
    'pay_recipient' => [
        'Type' => 'ff_select',
        'Title' => _fdtk('pay_recipient'),
        'Table' => 'vw_payments',
        'data' => [
            'can-add' => 'true',
        ],
    ],
    'payee' => [
        'Type' => 'ff_select',
        'Title' => 'Payee',
        'Width' => 5,
        'Table' => 'payments',
        'SelectFunction' => [
            'getPayeesForPayment', [
                'cla_id' => '',
            ],
        ],
        'RecordLinkBtn' => [
            'checkField' => 'payee',
            'linkFields' => [
                'pay_module' => 'module',
                'pay_cas_id' => 'main_recordid',
                'recordid' => 'payments_id',
            ],
            'params' => [
                'action' => 'linkcontactgeneral',
                'module' => 'CON',
            ],
        ],
    ],
    PaymentFields::REIMBURSABLE => [
        'Type' => FieldInterface::YESNO_DB,
        'Title' => 'Reimbursable?',
        'NoMandatory' => true,
        'Table' => 'vw_payments',
    ],
    PaymentFields::PAY_TYPE_REDRESS => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Type',
        'Table' => 'vw_payments',
    ],
];
