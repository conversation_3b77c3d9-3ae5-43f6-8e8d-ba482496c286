<?php

use app\models\generic\valueObjects\Module;
use src\component\field\MultiSelectField;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\framework\query\Query;
use src\payments\model\payeefield\PayeeField;
use src\payments\model\PaymentFields;
use src\payments\model\respondentfield\RespondentField;

if ($FormType === FormTable::MODE_LINKED_DATA_SEARCH) {
    $mainModule = $Module;
    $FormType = FormTable::MODE_SEARCH;
} else {
    $mainModule = $linkModule;
}

$readOnlyFields = array_keys($FormDesign->ReadOnlyFields);

$respIdReadOnly = in_array('resp_id', $readOnlyFields);
$respondentMode = (!empty($data['payee']) || $respIdReadOnly) ? 'ReadOnly' : $FormType;
$RespondentField = SelectFieldFactory::createSelectField('resp_id', 'PAY', $data['resp_id'], $respondentMode, false, 'Respondent Select', 'Payment Details', $data['CHANGED-resp_id'], '', 'vw_payments');
$RespondentField->setSelectFunction('getRespondentsForPayment', ['main_recordid' => '"' . $mainRecordid . '"', 'main_module' => '"' . $mainModule . '"']);

if ($RespondentField instanceof MultiSelectField) {
    // Prevent the form builder whinging about the field metadata missing a "max length" when in search mode
    $RespondentField->setIgnoreMaxLength();
}

$codes = (new RespondentField())->getCodes((new Query())->where(['resp_id' => $data['resp_id']]));
if (($code = $codes[$data['resp_id']]) !== null) {
    $RespondentField->setDescription($code->description);
}

$payeeReadOnly = in_array('payee', $readOnlyFields);
$payeeMode = (!empty($data['resp_id']) || $payeeReadOnly) ? 'ReadOnly' : $FormType;
$payeeField = SelectFieldFactory::createSelectField('payee', 'PAY', $data['payee'], $payeeMode, false, 'Payee', 'Payment Details', $data['CHANGED-payee'], '', 'vw_payments');
$payeeField->setSelectFunction('getPayeesForPayment', ['main_id' => '"' . $mainRecordid . '"', 'form_type' => '"' . $FormType . '"', 'main_module' => '"' . $mainModule . '"']);

$codes = (new PayeeField())->getCodes(new Query());
if (($code = $codes[$data['payee']]) !== null) {
    $payeeField->setDescription($code->description);
}

$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
        'LinkType' => ($FormType == 'linkedDataSearch') ? 'payments' : '',
    ],
    'details' => [
        'Title' => _fdtk('payment_details', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            ['Name' => 'pay_module', 'Condition' => $FormType !== 'linkedDataSearch'],
            'pay_cas_id',
            ['Name' => 'pay_link_title', 'Condition' => ((CanSeeModule(strtoupper($data['pay_module'])) && $data['permissions_to_linked_record'] === true) || $FormType == 'Design')],
            ['Name' => 'pay_recipient', 'Condition' => in_array($mainModule, [Module::CLAIMS, Module::REDRESS]) || $FormType === FormTable::MODE_DESIGN],
            'pay_date',
            [
                'Name' => 'pay_type',
                'Condition' => $mainModule !== Module::REDRESS || $FormType === FormTable::MODE_DESIGN,
            ],
            'pay_subtype',
            'pay_amount',
            'pay_vat_rate',
            'pay_calc_vat_amount',
            'pay_calc_total',
            'pay_notes',
            [
                'Type' => 'formfield',
                'Name' => 'resp_id',
                'FormField' => $RespondentField,
                'Condition' => (in_array($mainModule, [Module::CLAIMS, Module::REDRESS]) || $FormType == 'Design'),
            ],
            [
                'Type' => 'formfield',
                'Name' => 'payee',
                'FormField' => $payeeField,
                'Condition' => ($FormType != 'linkedDataSearch'),
            ],
            [
                'Name' => PaymentFields::REIMBURSABLE,
                'Condition' => $mainModule === Module::REDRESS || $FormType === FormTable::MODE_DESIGN,
            ],
            [
                'Name' => PaymentFields::PAY_TYPE_REDRESS,
                'Condition' => $mainModule === Module::REDRESS || $FormType === FormTable::MODE_DESIGN,
            ],
        ],
    ],
];

return $FormArray;
