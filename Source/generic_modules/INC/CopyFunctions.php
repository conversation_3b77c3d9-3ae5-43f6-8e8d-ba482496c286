<?php

use app\models\generic\valueObjects\Module;

/**
 * Copies incident Causal factors data.
 *
 * @param string $module module short name
 * @param string $destModule module short name to copy to
 * @param int $sourceID recordid of source incident/claim
 * @param int $destID recordid of new incident/claim record
 * @param stdClass $aAdditionalParams array containing additional options
 *
 * @return bool true if copy is success, false otherwise
 */
function CopyCausalFactors($module, $destModule, $sourceID, $destID, $aAdditionalParams)
{
    if (!in_array($destModule, [Module::CLAIMS, Module::REDRESS], true)) {
        return true;
    }

    if (!bYN($aAdditionalParams->copy_causal_factors)) {
        return true;
    }

    $fields = [
        'listorder',
        'caf_level_1',
        'caf_level_2',
    ];

    $sourceColumnName = strtolower($module) . '_id';
    $destinationColumnName = strtolower($destModule) . '_id';

    $fields[] = $destinationColumnName;

    $sql = '
        SELECT [' . implode('], [', $fields) . ']
        FROM causal_factors
        WHERE
    ';

    $sql .= "[{$sourceColumnName}] = '{$sourceID}'";

    $result = DatixDBQuery::PDO_fetch_all($sql);

    foreach ($result as $data) {
        $data[$destinationColumnName] = $destID;

        $InsertSql = GeneratePDOInsertSQLFromArrays([
            'FieldArray' => $fields,
            'DataArray' => $data,
            'Module' => $module,
        ], $PDOParams);

        $sql = "INSERT INTO causal_factors {$InsertSql}";

        if (!DatixDBQuery::PDO_query($sql, $PDOParams)) {
            fatal_error('Cannot copy causal factors data');

            return false;
        }
    }

    return true;
}

/**
 * @return bool
 */
function copyRespondents(string $sourceModule, string $destModule, int $recordId, int $newRecordId, stdClass $options)
{
    if (bYN($options->copy_respondents) !== true) {
        return false;
    }

    $sql = '
        INSERT INTO link_respondents (main_recordid, link_role, updateid, updatedby, updateddate, link_type, con_id, org_id, link_notes, link_resp, main_module) (
            SELECT :newRecordId, link_role, updateid, updatedby, updateddate, link_type, con_id, org_id, link_notes, link_resp, :destModule
            FROM link_respondents lr
            WHERE lr.main_module = :sourceModule
              AND lr.main_recordid = :sourceRecordId
        );
    ';

    $params = [
        'sourceRecordId' => $recordId,
        'sourceModule' => $sourceModule,
        'newRecordId' => $newRecordId,
        'destModule' => $destModule,
    ];

    $db = new DatixDBQuery();
    $db->setSQL($sql);
    $db->prepareAndExecute($params);
}
