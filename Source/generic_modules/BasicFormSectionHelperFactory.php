<?php

declare(strict_types=1);

namespace Source\generic_modules;

use src\framework\registry\Registry;
use src\helpers\GenericBasicFormHelper;
use src\system\container\facade\Container;

class BasicFormSectionHelperFactory
{
    public function create(string $formMode): BasicFormSectionHelper
    {
        return new BasicFormSectionHelper(
            $formMode,
            (new GenericBasicFormHelper())->useFormDesignLanguage($formMode),
            Container::get(Registry::class),
        );
    }
}
