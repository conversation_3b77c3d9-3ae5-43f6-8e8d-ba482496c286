<?php

use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * Checks that the 'Coverage end date' date has not been set to earlier than the 'Coverage start date' date on the policy form.
 *
 * @return string $error the error message
 */
function validateCoverageDates()
{
    $error = [];
    $fieldLabels = Container::get(Registry::class)->getFieldLabels();

    if ($_POST['start_date'] != '' && $_POST['end_date'] != '') {
        if (strtotime(UserDateToSQLDate($_POST['end_date'])) < strtotime(UserDateToSQLDate($_POST['start_date']))) {
            $error['end_date']['message'] = $fieldLabels->getLabel('policies', 'end_date') . ' cannot be earlier than ' . $fieldLabels->getLabel('policies', 'start_date');
        }
    }

    return $error;
}
