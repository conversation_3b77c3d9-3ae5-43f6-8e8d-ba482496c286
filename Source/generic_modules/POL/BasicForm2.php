<?php

use src\component\form\FormTable;

$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],
    'policydetails' => [
        'Title' => _fdtk('insurance_policy_details', $useFormDesignLanguage),
        'Rows' => [
            ['Name' => 'recordid', 'Condition' => ($data['recordid'] || $FormType == 'Design' || $FormType == 'Search')],
            'title',
            'reference',
            'description',
            'insurer',
            'coverage_type',
            'policy_basis',
            'policy_type',
            'costs_covered',
        ],
    ],
    'coveragedetails' => [
        'Title' => _fdtk('coverage_details', $useFormDesignLanguage),
        'Rows' => [
            'start_date',
            'end_date',
            'attachment_point',
            'event_limit',
            'aggregate_limit',
        ],
    ],
    'respondents' => [
        'Title' => _fdtk('respondents_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'Condition' => $_GET['action'] == 'record' || $FormMode == 'Design',
        'ControllerAction' => [
            'listrespondentsforpolicy' => [
                'controller' => src\respondents\controllers\RespondentsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'claims' => [
        'Title' => _fdtk('CLANamesTitle', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'Condition' => $_GET['action'] == 'record' || $FormMode == 'Design',
        'ControllerAction' => [
            'listclaimsforpolicy' => [
                'controller' => src\policies\controllers\PoliciesController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Listings' => ['claims' => ['module' => 'CLA']],
        'Rows' => [],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
];

return $FormArray;
