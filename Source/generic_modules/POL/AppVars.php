<?php

use app\models\modules\ModuleDisplayAcronyms;

$ModuleDefs['POL'] = [
    'GENERIC' => true,
    'GENERIC_FOLDER' => 'POL',
    'MOD_ID' => MOD_POLICIES,
    'CODE' => 'POL',
    'NAME' => _fdtk('mod_policies_title'),
    'TABLE' => 'policies',
    'HAS_DEFAULT_LISTING' => true,
    'REC_NAME' => _fdtk('POLName'),
    'REC_NAME_PLURAL' => _fdtk('POLNames'),
    'REC_NAME_TITLE' => _fdtk('POLNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('POLNamesTitle'),
    'FK' => 'pol_id',
    'ACTION' => 'record&module=POL',
    'SEARCH_URL' => 'action=search&module=POL',
    'PERM_GLOBAL' => 'POL_PERMS',
    'FORMS' => [
        2 => ['LEVEL' => 2, 'CODE' => 'POL2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::INSURANCE_POLICIES_LEVEL_2],
    ],
    'ICON' => 'icons/icon_POL.png',
    'FIELD_ARRAY' => ['title', 'reference', 'description', 'aggregate_limit', 'event_limit', 'start_date', 'end_date', 'coverage_type', 'policy_basis', 'insurer', 'policy_type', 'costs_covered', 'attachment_point'],
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserPOL2Settings',
    'LINKED_CONTACTS' => false,
    'LINKED_DOCUMENTS' => true,
    'NOTEPAD' => false,
    'NO_REP_APPROVED' => true,
    'ADD_NEW_RECORD_LEVELS' => ['POL_FULL'],
    'USES_APPROVAL_STATUSES' => false,

    'DATA_VALIDATION_INCLUDES' => [
        'Source/generic_modules/POL/ModuleFunctions.php',
    ],
    'DATA_VALIDATION_FUNCTIONS' => [
        'validateCoverageDates',
    ],
    'HARD_CODED_LISTINGS' => [
        'all' => [
            'Title' => _fdtk('pol_listing'),
            'Link' => _fdtk('list_all_policy'),
            'Where' => '(1=1)',
        ],
    ],
    'EXTRA_FORM_ACTIONS' => [
        'generateInstances' => [
            'title' => 'unlink_policy',
            'condition' => ($_GET['link_recordid'] ?? null) != '',
            'js' => "SendTo('index.php?action=editpolicylink&link_action=delete" .
                               '&policy_id=' . ((int) ($_GET['recordid'] ?? 0)) .
                               '&type=' . (in_array($_GET['type'] ?? null, ['CON', 'ORG']) ? $_GET['type'] : '') .
                               '&link_type=' . ($_GET['link_type'] ?? '') .
                               '&main_recordid=' . ((int) ($_GET['main_recordid'] ?? 0)) .
                               '&link_recordid=' . ((int) ($_GET['link_recordid'] ?? 0)) .
                               '&org_id=' . ((int) ($_GET['org_id'] ?? null)) . "');",
            'action' => 'DELETE',
            'class' => 'red-gradient button-margin-right',
        ],
    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Policies/c_dx_policies_guide.xml',
    'CONTACT_LINK_TABLE_ID' => ['link_respondents' => 'main_recordid'],
];
