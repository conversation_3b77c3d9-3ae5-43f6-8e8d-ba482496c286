<?php

$FieldDefs['POL'] = [
    'recordid' => [
        'Type' => 'number',
        'ReadOnly' => true,
        'Title' => 'ID',
        'Width' => 5,
        'IdentityCol' => true, ],
    'title' => [
        'Type' => 'string',
        'Title' => 'Title',
        'MaxLength' => 100,
        'Width' => 70,
    ],
    'reference' => [
        'Type' => 'string',
        'Title' => 'Reference',
        'MaxLength' => 100,
    ],
    'description' => [
        'Type' => 'textarea',
        'Title' => 'Description',
        'Rows' => 7,
        'Columns' => 70, ],
    'insurer' => [
        'Type' => 'ff_select',
        'Title' => 'Insurer', ],
    'coverage_type' => [
        'Type' => 'ff_select',
        'Title' => 'Coverage type', ],
    'policy_basis' => [
        'Type' => 'ff_select',
        'Title' => 'Insurance Policy basis', ],
    'policy_type' => [
        'Type' => 'ff_select',
        'Title' => 'Insurance Policy type', ],
    'costs_covered' => [
        'Type' => 'multilistbox',
        'Title' => 'Costs covered',
        'MaxLength' => 254, ],
    'start_date' => [
        'Type' => 'date',
        'Title' => 'Coverage start date', ],
    'end_date' => [
        'Type' => 'date',
        'Title' => 'Coverage end date', ],
    'attachment_point' => [
        'Type' => 'money',
        'Title' => 'Attachment point', ],
    'event_limit' => [
        'Type' => 'money',
        'Title' => 'Single-event limit', ],
    'aggregate_limit' => [
        'Type' => 'money',
        'Title' => 'Aggregate limit', ],
];
