<?php

declare(strict_types=1);

namespace Source\generic_modules\MED;

use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use app\models\medication\entities\MedicationEntity;
use Exception;
use ReflectionException;
use src\component\field\HiddenField;
use src\component\form\FormTable;
use src\framework\session\UserSession;
use src\medications\controllers\MedicationController;
use src\medications\helpers\MedicationFieldsHelper;
use src\medications\helpers\MedicationSearchFieldsHelper;
use src\medications\models\MedicationPrefixes;
use src\medications\models\MedicationFields;
use src\medications\Services\MedicationsService;
use src\system\database\FieldInterface;

use function in_array;

class MedicationFormSectionHelper
{
    private ?string $formMode;
    private int $formLevel;
    private bool $useFormDesignLanguage;
    private bool $usePhpForm;
    private UserSession $userSession;
    private MedicationsService $medicationsService;
    private MedicationFieldsHelper $medicationFieldsHelper;
    private MedicationSearchFieldsHelper $medicationSearchFieldsHelper;

    public function __construct(
        ?string $formMode,
        int $formLevel,
        bool $useFormDesignLanguage,
        bool $usePhpForm,
        UserSession $userSession,
        MedicationsService $medicationsService,
        MedicationFieldsHelper $medicationFieldsHelper,
        MedicationSearchFieldsHelper $medicationSearchFieldsHelper
    ) {
        $this->formMode = $formMode;
        $this->formLevel = $formLevel;
        $this->useFormDesignLanguage = $useFormDesignLanguage;
        $this->usePhpForm = $usePhpForm;
        $this->userSession = $userSession;
        $this->medicationsService = $medicationsService;
        $this->medicationFieldsHelper = $medicationFieldsHelper;
        $this->medicationSearchFieldsHelper = $medicationSearchFieldsHelper;
    }

    public function createMedicationFormSection(): array
    {
        $section = [
            'Condition' => $this->showMedicationFormSection(),
            'Title' => _fdtk('mod_medications_title', $this->useFormDesignLanguage),
            'Rows' => [],
        ];

        if ($this->usePhpForm) {
            $section['NoFieldAdditions'] = true;
            $section['NoReadOnly'] = true;
            $section['ControllerAction'] = [
                'displayMedications' => [
                    'controller' => MedicationController::class,
                ],
            ];
            $section['ExtraParameters'] = [
                'level' => $this->formLevel,
                'medicationsService' => $this->medicationsService,
                'medicationFormSectionHelper' => $this,
            ];
        }

        return $section;
    }

    /**
     * @throws Exception
     */
    public function createDrugFormSection(string $type, ?string $suffix, array $formData): array
    {
        $sectionKey = "{$type}_drug_form" . ($suffix ? "_{$suffix}" : '');

        return [
            'Parameters' => ['Suffix' => $suffix],
            $sectionKey => [
                'NoTitle' => true,
                'ContactSuffix' => $suffix,
                'Rows' => $this->getDrugFormFields($type, $formData),
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function createDrugFormDesignSection(string $type): array
    {
        return [
            'Title' => _fdtk("medications_drug_{$type}", $this->useFormDesignLanguage),
            'Condition' => $this->usePhpForm && $this->formMode === FormTable::MODE_DESIGN,
            'NoFieldAdditions' => true,
            'NoFieldRemoval' => true,
            'NoNewPanel' => true,
            'NoOrder' => true,
            'AltSectionKey' => "{$type}_drug_form",
            'Rows' => $this->getDrugFormFields($type, []),
        ];
    }

    /**
     * @throws ReflectionException
     */
    public function createOtherFieldsFormSection(?string $suffix): array
    {
        $sectionKey = 'medications_other_fields_form' . ($suffix ? "_{$suffix}" : '');

        return [
            'Parameters' => ['Suffix' => $suffix],
            $sectionKey => [
                'NoTitle' => true,
                'ContactSuffix' => $suffix,
                'Rows' => MedicationFields::getOtherFields(),
            ],
        ];
    }

    /**
     * @throws ReflectionException
     */
    public function createOtherFieldsFormDesignSection(): array
    {
        $fields = MedicationFields::getOtherFields();

        // It's possible that there are no "other fields" depending on system config, so
        // if that's the case then no need to display this design section
        $display = !empty($fields);

        return [
            'Title' => _fdtk('medications_other', $this->useFormDesignLanguage),
            'Condition' => $this->usePhpForm && $display && $this->formMode === FormTable::MODE_DESIGN,
            'NoFieldAdditions' => true,
            'NoFieldRemoval' => true,
            'NoNewPanel' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'AltSectionKey' => 'medications_other_fields_form',
            'Rows' => MedicationFields::getOtherFields(),
        ];
    }

    // We could potentially also do away with the separate search form when the PHP meds form is in use
    // (it was only required because the original medications form was built in javascript)
    public function createMedicationSearchFormSection(): array
    {
        $title = $this->formMode === FormTable::MODE_DESIGN ?
            _fdtk('medications_search', $this->useFormDesignLanguage) :
            _fdtk('mod_medications_title', $this->useFormDesignLanguage);

        return [
            'Title' => $title,
            'Condition' => $this->showSearchSection(),
            'NoFieldAdditions' => true,
            'NoReadOnly' => true,
            'ControllerAction' => [
                'medicationSearchForm' => [
                    'controller' => MedicationController::class,
                ],
            ],
            'Rows' => [],
        ];
    }

    /**
     * Helper function to extract the medication form fields from the medication form
     * results are keyed by the drug ID.
     */
    public function extractMedicationData(array $formData): array
    {
        $maxSuffix = (int) ($formData['medication_max_suffix'] ?? 1);
        $medications = [];
        for ($i = 1; $i <= $maxSuffix; ++$i) {
            if (!isset($formData["drug_answer_id_{$i}"])) {
                continue;
            }

            $drugData = [
                // The ID should either be an integer or null, this is a quick hack to avoid empty strings
                // being flagged as invalid
                'id' => $formData["drug_answer_id_{$i}"] ?: null,
                'administered_drug' => [],
                'correct_drug' => [],
            ];
            foreach (MedicationFields::getFields() as $fieldName) {
                $fieldData = $formData["{$fieldName}_{$i}"] ?? null;
                $fieldNameParts = explode('_', $fieldName);
                if (in_array($fieldNameParts[0], MedicationPrefixes::DRUG_FIELD_PREFIXES)) {
                    $drugType = array_shift($fieldNameParts);
                    $drugData["{$drugType}_drug"][implode('_', $fieldNameParts)] = $fieldData;
                } else {
                    $drugData[$fieldName] = $fieldData;
                }
            }
            $medications[] = $drugData;
        }

        return $medications;
    }

    public static function getSearchDrugDisplayFieldsGroupedByType(): array
    {
        $groupedList = [];
        foreach (MedicationSearchFieldsHelper::SEARCH_DRUG_DISPLAY_FIELDS as $fieldName) {
            $fieldNameParts = explode('_', $fieldName);
            if (!isset($groupedList[$fieldNameParts[0]])) {
                $groupedList[$fieldNameParts[0]] = [];
            }
            $groupedList[$fieldNameParts[0]][] = $fieldName;
        }

        return $groupedList;
    }

    /**
     * @throws Exception
     */
    private function getDrugFormFields(string $type, array $formData): array
    {
        if (!in_array($type, MedicationPrefixes::DRUG_FIELD_PREFIXES)) {
            throw new Exception("Invalid drug form type '{$type}'");
        }

        $fieldGetter = "get{$type}DrugFields";
        $fields = MedicationFields::$fieldGetter();

        // Reference is output as a hidden field, so remove from the field list
        unset($fields[array_search("{$type}_med_reference", $fields)]);

        // We need to also remove the individual fields within "combined" fields
        $fields = array_diff($fields, ["{$type}_quantity", "{$type}_unit"]);

        // We need to include all medication record fields, regardless of whether they're available on the form,
        // so that we can persist the medication record locally within Capture in cases where it may not previously
        // have been synced from the medications service. Fields not visible on the form will be added as hidden.
        if ($this->formMode !== FormTable::MODE_DESIGN) {
            $allMedsFields = array_map(static function ($field) use ($type): string {
                return "{$type}_{$field}";
            }, MedicationEntity::getFields());

            unset($allMedsFields[array_search("{$type}_med_reference", $allMedsFields)]);

            foreach (array_diff($allMedsFields, $fields) as $unavailableField) {
                $fieldWrapper = [
                    'Name' => $unavailableField,
                    'Type' => FieldInterface::HIDDEN_DB,
                ];
                if ($this->formMode === FormTable::MODE_SEARCH) {
                    $fieldWrapper['FormField'] = new HiddenField(Tables::COMBINED_LINKED_MEDICATIONS_VIEW . '|' . $unavailableField, '');
                }
                $fields[] = $fieldWrapper;
            }
        }

        if ($this->formMode === FormTable::MODE_SEARCH) {
            $table = Tables::COMBINED_LINKED_MEDICATIONS_VIEW;

            foreach ($fields as &$field) { // by ref because we reassign the field if it's a search field
                if (is_array($field)) {
                    // Field is a hidden field, skip it
                    continue;
                }
                $isPseudoCodeField = in_array($field, MedicationSearchFieldsHelper::SEARCH_DRUG_PSEUDO_CODE_FIELDS, true);
                $isCombinedField = in_array($field, MedicationSearchFieldsHelper::SEARCH_DRUG_COMBINED_FIELDS, true);
                $isCustomCodeField = isset(MedicationFieldsHelper::CUSTOM_CODE_FIELDS[$field]);

                if (!$isPseudoCodeField && !$isCombinedField && !$isCustomCodeField) {
                    continue;
                }

                $fieldLabel = _fdtk($field);

                if ($isPseudoCodeField) {
                    $formField = $this->medicationSearchFieldsHelper->buildMedsSearchDropdownField($field, $fieldLabel, $table, $formData[$field] ?? '');
                } elseif ($isCombinedField) {
                    $formField = $this->medicationSearchFieldsHelper->buildMedsSearchComboField($field, $table, $formData);
                } else {
                    $componentField = [
                        'Name' => $field,
                        'Module' => Module::MEDICATIONS,
                        'Table' => $table,
                        'Type' => FieldInterface::CODE_DB,
                        'CustomCodes' => $this->medicationFieldsHelper->getMedFieldCustomCodes($field),
                    ];

                    $formField = $this->medicationSearchFieldsHelper->buildMedsSearchComponentField($componentField, $table, $formData);
                }

                $field = [
                    'Type' => 'formfield',
                    'Name' => $field,
                    'Title' => $fieldLabel,
                    'FormField' => $formField,
                ];
            }
        }

        return $fields;
    }

    private function showMedicationFormSection(): bool
    {
        if ($this->formLevel === 1) {
            // User is logged in or location filtering is turned off(can't find meds if the user is logged out and the env is on)
            return $this->userSession->isLoggedIn()
                || getenv('MEDICATIONLOCATIONFILTERING_ENABLED') !== '1';
        }

        if ($this->formMode !== FormTable::MODE_SEARCH) {
            // For non-search level 2 we always display the form
            return true;
        }

        // if level 2 search then PHP_MEDS_FORM uses the normal form for search
        return $this->usePhpForm;
    }

    private function showSearchSection(): bool
    {
        if ($this->usePhpForm) {
            // We're using the normal form for search if PHP Meds Form
            return false;
        }

        // Otherwise the search form should only ever show if design(to show/hide the search)
        // or search(because obviously then we render the search)
        return in_array($this->formMode, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH]);
    }
}
