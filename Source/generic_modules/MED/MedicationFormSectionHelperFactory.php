<?php

declare(strict_types=1);

namespace Source\generic_modules\MED;

use app\models\framework\config\DatixConfigFactory;
use src\framework\session\UserSessionFactory;
use src\helpers\GenericBasicFormHelper;
use src\medications\Adaptors\MedicationsInterface;
use src\medications\helpers\MedicationFieldsHelper;
use src\medications\helpers\MedicationSearchFieldsHelper;
use src\medications\Services\MedicationsServiceFactory;
use src\system\container\facade\Container;

class MedicationFormSectionHelperFactory
{
    public function create(?string $formMode, int $formLevel, string $module, ?MedicationsInterface $medicationsInterface = null): MedicationFormSectionHelper
    {
        return new MedicationFormSectionHelper(
            $formMode,
            $formLevel,
            (new GenericBasicFormHelper())->useFormDesignLanguage($formMode),
            (new DatixConfigFactory())->getInstance()->usePhpMedsForm(),
            (new UserSessionFactory())->create(),
            MedicationsServiceFactory::create($module, $medicationsInterface),
            Container::get(MedicationFieldsHelper::class),
            Container::get(MedicationSearchFieldsHelper::class),
        );
    }
}
