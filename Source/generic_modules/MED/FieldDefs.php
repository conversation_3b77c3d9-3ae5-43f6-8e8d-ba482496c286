<?php

use app\services\carlton\i18n\I18nService;
use Source\generic_modules\FieldDefKeys;
use src\medications\helpers\MedicationFieldsHelper;
use src\medications\models\MedicationFields;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\system\language\LanguageSession;

$i18nService = Container::get(I18nService::class);
$locale = Container::get(LanguageSession::class)->getLocale();
$medicationFieldsHelper = Container::get(MedicationFieldsHelper::class);

$FieldDefs['MED'] = [
    MedicationFields::ADMINISTERED_MED_SEARCH => [
        FieldDefKeys::TYPE => FieldInterface::LOOKUP_DB,
        FieldDefKeys::TITLE => $i18nService->getTranslation('MEDICATIONS.V2.FORM.LABEL.SEARCH_DRUG_ADMINISTERED', 'medications', $locale),
        FieldDefKeys::NO_HIDE => true,
        FieldDefKeys::NO_READ_ONLY => true,
        FieldDefKeys::ALWAYS_MANDATORY => true,
        FieldDefKeys::LOOKUP_FUNCTION => 'getMedicationSearchResults',
    ],
    MedicationFields::ADMINISTERED_MED_NAME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED', 'medications', $locale),
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::ALWAYS_MANDATORY => true,
        FieldDefKeys::READ_ONLY_AND_MANDATORY => true,
        FieldDefKeys::CUSTOM_ERROR_MESSAGE => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.ERROR.MEDICATION_REQUIRED', 'medications', $locale),
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_BRAND => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_BRAND', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_MANUFACTURER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_MANUFACTURER', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_CLASS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_CLASS', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_STRENGTH => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_STRENGTH', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_SUPPLIER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_SUPPLIER', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_TYPE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_TYPE', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_MED_SOURCE_ID => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_SOURCE_ID', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::ADMINISTERED_ROUTE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_ADMINISTERED_ROUTE', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_ROUTE),
    ],
    MedicationFields::ADMINISTERED_DOSE => [
        'Type' => FieldInterface::COMBINED_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.FORM.LABEL.DRUG_ADMINISTERED_DOSAGE', 'medications', $locale),
        'Field1' => [
            'Name' => MedicationFields::ADMINISTERED_QUANTITY,
            'Type' => FieldInterface::NUMBER_DB,
            'alwaysShowBox' => true,
        ],
        'Field2' => [
            'Name' => MedicationFields::ADMINISTERED_UNIT,
            'Type' => FieldInterface::CODE_DB,
            'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_DOSE_UNIT),
        ],
    ],
    MedicationFields::ADMINISTERED_FORM => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.FORM_ADMINISTERED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_FORM),
    ],
    MedicationFields::ADMINISTERED_BNF_CLASSIFICATION => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Drug administered or omitted BNF classification',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_BNF_CLASSIFICATION),
    ],
    MedicationFields::ADMINISTERED_BATCH_NUMBER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Drug administered or omitted batch number',
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::ADMINISTERED_ETHICS_COMMITTEE_NAME_REFERENCE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Drug administered or omitted ethics committee name and reference',
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::ADMINISTERED_CLINICAL_TRIAL => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug administered or omitted under clinical trial?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_CLINICAL_TRIAL),
    ],
    MedicationFields::ADMINISTERED_MANUFACTURED_SPECIAL => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug administered or omitted a manufactured special?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_MANUFACTURED_SPECIAL),
    ],
    MedicationFields::ADMINISTERED_PARALLEL_IMPORT => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug administered or omitted a parallel import?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADMINISTERED_PARALLEL_IMPORT),
    ],
    MedicationFields::CORRECT_MED_SEARCH => [
        FieldDefKeys::TYPE => FieldInterface::LOOKUP_DB,
        FieldDefKeys::TITLE => $i18nService->getTranslation('MEDICATIONS.V2.FORM.LABEL.SEARCH_DRUG_INTENDED', 'medications', $locale),
        FieldDefKeys::NO_HIDE => true,
        FieldDefKeys::ALWAYS_MANDATORY => true,
        FieldDefKeys::NO_READ_ONLY => true,
        FieldDefKeys::LOOKUP_FUNCTION => 'getMedicationSearchResults',
    ],
    MedicationFields::CORRECT_MED_NAME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED', 'medications', $locale),
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::ALWAYS_MANDATORY => true,
        FieldDefKeys::READ_ONLY_AND_MANDATORY => true,
        FieldDefKeys::CUSTOM_ERROR_MESSAGE => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.ERROR.MEDICATION_REQUIRED', 'medications', $locale),
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_BRAND => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_BRAND', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_MANUFACTURER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_MANUFACTURER', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_CLASS => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_CLASS', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_STRENGTH => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_STRENGTH', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_SUPPLIER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_SUPPLIER', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_TYPE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_TYPE', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_MED_SOURCE_ID => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_SOURCE_ID', 'medications', $locale),
        'ReadOnly' => true,
        FieldDefKeys::ALWAYS_INCLUDE_ON_FORM => true,
    ],
    MedicationFields::CORRECT_ROUTE => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_ROUTE', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_ROUTE),
    ],
    MedicationFields::CORRECT_DOSE => [
        'Type' => FieldInterface::COMBINED_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DRUG_INTENDED_DOSAGE', 'medications', $locale),
        'Field1' => [
            'Name' => MedicationFields::CORRECT_QUANTITY,
            'Type' => FieldInterface::NUMBER_DB,
            'alwaysShowBox' => true,
        ],
        'Field2' => [
            'Name' => MedicationFields::CORRECT_UNIT,
            'Type' => FieldInterface::CODE_DB,
            'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_DOSE_UNIT),
        ],
    ],
    MedicationFields::CORRECT_FORM => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.FORM_INTENDED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_FORM),
    ],
    MedicationFields::CORRECT_BNF_CLASSIFICATION => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Drug intended BNF classification',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_BNF_CLASSIFICATION),
    ],
    MedicationFields::CORRECT_BATCH_NUMBER => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Intended drug batch number',
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::CORRECT_ETHICS_COMMITTEE_NAME_REFERENCE => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => 'Drug intended ethics committee name and reference',
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::CORRECT_CLINICAL_TRIAL => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug intended under clinical trial?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_CLINICAL_TRIAL),
    ],
    MedicationFields::CORRECT_MANUFACTURED_SPECIAL => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug intended a manufactured special?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_MANUFACTURED_SPECIAL),
    ],
    MedicationFields::CORRECT_PARALLEL_IMPORT => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => 'Is the drug intended a parallel import?',
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::CORRECT_PARALLEL_IMPORT),
    ],
    MedicationFields::REPORTED_TO_MANUFACTURER => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.REPORTED_TO_MANUFACTURER', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::REPORTED_TO_MANUFACTURER),
    ],
    MedicationFields::DATE_REPORTED_TO_MANUFACTURER => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DATE_REPORTED_TO_MANUFACTURER', 'medications', $locale),
    ],
    MedicationFields::MANUFACTURER_REF => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.MANUFACTURER_REF', 'medications', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::IS_STILL_ADMINISTERED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.STILL_ADMINISTERED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::IS_STILL_ADMINISTERED),
    ],
    MedicationFields::FREQUENCY => [
        'Type' => FieldInterface::STRING_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.FREQUENCY', 'medications', $locale),
        FieldDefKeys::MAX_LENGTH => 255,
    ],
    MedicationFields::PRODUCT_EXPIRY_DATE => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.PRODUCT_EXPIRY_DATE', 'medications', $locale),
    ],
    MedicationFields::ACTION_TAKEN => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.ACTION_TAKEN', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ACTION_TAKEN),
    ],
    MedicationFields::ADR_LESSENED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.ADR_LESSENED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADR_LESSENED),
    ],
    MedicationFields::ADR_REAPPEARED => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.ADR_REAPPEARED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::ADR_REAPPEARED),
    ],
    MedicationFields::STAGE_OF_ERROR => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.STAGE_ERROR_OCCURED', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::STAGE_OF_ERROR),
    ],
    MedicationFields::TYPE_OF_ERROR => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.ERROR_TYPE', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::TYPE_OF_ERROR),
    ],
    MedicationFields::NOTES => [
        'Type' => FieldInterface::TEXT_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.NOTES', 'medications', $locale),
        'Rows' => 7,
        'Columns' => 70,
    ],
    MedicationFields::OTHER_IMPORTANT_FACTORS => [
        'Type' => FieldInterface::CODE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.OTHER_IMPORTANT_FACTORS', 'medications', $locale),
        'CustomCodes' => $medicationFieldsHelper->getMedFieldCustomCodes(MedicationFields::OTHER_IMPORTANT_FACTORS),
    ],
    MedicationFields::DATE_FIRST_USED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DATE_FIRST_USED', 'medications', $locale),
    ],
    MedicationFields::DATE_LAST_USED => [
        'Type' => FieldInterface::DATE_DB,
        'Title' => $i18nService->getTranslation('MEDICATIONS.V2.DISPLAY.LABEL.DATE_LAST_USED', 'medications', $locale),
    ],
];
