<?php

declare(strict_types=1);

namespace Source\generic_modules\MED;

use app\models\framework\config\DatixConfig;

class FieldDefsHelper
{
    private DatixConfig $config;

    public function __construct(DatixConfig $config)
    {
        $this->config = $config;
    }

    public function fetchOptionsDependingOnVenessaLawEnabled(array $disabledOptions, array $enabledOptions): array
    {
        return $this->config->getVanessaLawEnabled() ? $enabledOptions : $disabledOptions;
    }
}
