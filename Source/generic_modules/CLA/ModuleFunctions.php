<?php

use app\services\contact\ContactComplainantPatientRepository;
use app\services\idGenerator\RecordIdGeneratorFactory;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\organisations\model\OrganisationModelFactory;
use src\system\container\facade\Container;

/**
 * POST save function for linked contacts in order to link a claimant as a Patient if the link_plapat field is set to 'Y'
 * 'Is claimant the person affected?' is a Y/N field.
 * - If 'N' is selected, upon save the system checks if a link exists to the 'Person affected' section and, if one does, that link is deleted.
 * - If 'Y' is selected, upon save the 'Person affected' section will automatically link to the same contact record.
 */
function SavePatient(array $aParams): void
{
    Container::get(ContactComplainantPatientRepository::class)->saveClaimsClaimant($aParams);
}

function validatePartyPercentages()
{
    $Error = [];

    if (array_sum([$_POST['fin_ourshare'], $_POST['fin_thirdshare'], $_POST['fin_thirdshare2']]) > 100) {
        $Error['fin_ourshare'] = 'Total of share fields cannot come to more than 100%';
        $Error['fin_thirdshare'] = 'Total of share fields cannot come to more than 100%';
        $Error['fin_thirdshare2'] = 'Total of share fields cannot come to more than 100%';
    }

    return $Error;
}

function validateQuickRefId()
{
    $Error = [];

    if ($_POST['cla_generated_reference_id'] === ''
        && $_POST['cla_dclaim'] === ''
        && Container::get(Registry::class)->getParm('CLAIMS_REFERENCE_ID_FIELDS', '')->toScalar()) {
        $Error['cla_dclaim'] = 'Date of claim required to generate reference field';
    }

    return $Error;
}

/**
 * If a new stage has been added, adds it to stage history table.
 */
function AddLatestStage($Data)
{
    if ($Data['cla_curstage']) {
        $LastStage = DatixDBQuery::PDO_fetch('SELECT TOP 1 EventType from events_history WHERE cas_id = :cas_id AND CHAINNAME = :chainname ORDER BY EVENTDATE DESC, recordid DESC', ['cas_id' => $Data['recordid'], 'chainname' => 'CSTG'], PDO::FETCH_COLUMN);

        if ($LastStage != $Data['cla_curstage']) {
            $recordIdGenerator = (new RecordIdGeneratorFactory())->create('events_history');
            $StageID = $recordIdGenerator->generateRecordId();

            DatixDBQuery::PDO_build_and_insert(
                'events_history',
                ['recordid' => $StageID, 'CHAINNAME' => 'CSTG', 'cas_id' => $Data['recordid'], 'EventType' => $Data['cla_curstage'], 'EVENTDATE' => GetTodaysDate()],
            );
        }
    }
}

/**
 * Validate organisation attached to level 1.
 *
 * @return array
 */
function validateLinkedOrganisations()
{
    $maxLinkedOrganisations = $_POST['organisation_max_suffix'];

    $error = [];

    if ($maxLinkedOrganisations == '') {
        return [];
    }

    $organisationNumber = 100;

    while ($organisationNumber <= $maxLinkedOrganisations) {
        if (!OrganisationNotFilledIn($_POST, $organisationNumber)) {
            $organisationData = (new OrganisationModelFactory())->getMapper()->findOrganisationByID($_POST['org_id_' . $organisationNumber]);

            if (count($organisationData) == 0 || $organisationData == '') {
                $error['org_name_' . $organisationNumber] = _fdtk('invalid_organisation_error');
            }
        }

        ++$organisationNumber;
    }

    return $error;
}

function OrganisationNotFilledIn($Data, $Suffix)
{
    return $Data["org_name_{$Suffix}"] == ''
        && $Data["org_reference_{$Suffix}"] == ''
        && $Data["org_address_{$Suffix}"] == ''
        && $Data["org_postcode_{$Suffix}"] == ''
        && $Data["org_tel1_{$Suffix}"] == ''
        && $Data["org_tel2_{$Suffix}"] == ''
        && $Data["org_email_{$Suffix}"] == ''
        && $Data["location_id_{$Suffix}"] == ''
        && $Data["service_id_{$Suffix}"] == ''
        && $Data["org_notes_{$Suffix}"] == '';
}
/**
 * Depending on the global CLAIM_CLOSED_DATES closes either the claim close dates, indemnity & expenses reserveAuditTable dates or
 * all of the above. This happens when a claim is set to closed.
 *
 * @global          CLAIM_CLOSED_DATES can be NONE for no date changes, CLAIM to only close the claim date, RES to only
 *                  close the reserveAuditTable dates or BOTH for all three dates
 *
 * @param array $data takes the data posted in saving a claim and manipulates it before saving
 *
 * @return $data the return of the manipulated data
 */
function calculateClaimCloseDates(array $data): array
{
    // make sure the claim is being closed (and not previously closed) and the global is set to change dates
    if ($data['rep_approved'] == 'CLOSED' && $data['rep_approved_old'] != 'CLOSED' && getParm('CLAIM_CLOSED_DATES', 'NONE') != 'NONE') {
        $dateFields = ['cla_dclosed', 'indem_dclosed', 'expen_dclosed', 'fin_medical_dclosed', 'fin_legal_dclosed',
            'fin_temporary_indemnity_dclosed', 'fin_permanent_indemnity_dclosed', ];

        // make sure we have the relevant dates
        $sql = 'SELECT ' . implode(',', $dateFields) . ' FROM claims_main WHERE recordid = :recordid';
        $dates = \DatixDBQuery::PDO_fetch($sql, ['recordid' => $data['recordid']]);

        // get the string for todays date
        $now = (new DateTime())->format('Y-m-d H:i:s');

        // loop through each relevant date

        foreach ($dateFields as $field) {
            // if the relevant date is empty...
            if ($data[$field] == null && $dates[$field] == null) {
                // check the global covers each date
                if (($field == 'cla_dclosed' && (getParm('CLAIM_CLOSED_DATES', 'NONE') == 'CLAIM' || getParm('CLAIM_CLOSED_DATES', 'NONE') == 'BOTH'))
                    || ($field != 'cla_dclosed' && (getParm('CLAIM_CLOSED_DATES', 'NONE') == 'RES' || getParm('CLAIM_CLOSED_DATES', 'NONE') == 'BOTH'))) {
                    // if the date is empty and the global covers its closure, close it.
                    $data[$field] = $now;
                    $data['CHANGED-' . $field] = 1;
                }
            } elseif ($data[$field] == null && $dates[$field] != null) {
                // if the data provided does not hold the relevant date, but it is available on the database, correct here.
                $data[$field] = $dates[$field];
            }
        }
    }

    return $data;
}
/**
 * Zeroes the indemnity or expenses remaining reserveAuditTable if the reserveAuditTable close date has been set. Zeroing takes place on the
 * date in question, and is achieved by setting the reserveAuditTable level to the equivalent of total payments against that reserveAuditTable.
 * Payments added after the date of closure do not count and so it is possible for the reserveAuditTable to not actually be 0.
 * Additionally, payments made on the exact date of closure will be included, and payments with no date will always be
 * included. This method can also be called by a linked payment through a module function. This all happens pre-save on
 * a claim, or payment linked to a claim.
 *
 * Note that this function has been revamped several times, and is now officially a monster. I have put in as many
 * comments as seem reasonable to explain it, but it is essentially a piece of procedural hell.
 *
 * @param $data takes the data posted in saving a claim and manipulates it before saving
 *
 * @return $data the return of the manipulated data
 */
function zeroReservesOnDate(array $data): array
{
    $mainModule = $data['module'];
    $isClaimsPayment = ($mainModule == 'PAY' && strtoupper($data['pay_module']) == 'CLA');
    $claimId = ($mainModule == 'CLA') ? $data['recordid'] : $data['main_recordid'];
    $currentDateTime = (new DateTime())->format('Y-m-d H:i:s.000');

    $costTypeFieldInformation = [
        'indemnity' => [
            'closedDate' => 'indem_dclosed',
            'costTypeMappingId' => 1,
            'incurredField' => 'fin_indemnity_reserve',
            'auditTable' => 'fin_indemnity_reserve_audit',

        ],
        'expenses' => [
            'closedDate' => 'expen_dclosed',
            'costTypeMappingId' => 2,
            'incurredField' => 'fin_expenses_reserve',
            'auditTable' => 'fin_expenses_reserve_audit',
        ],
        'medical' => [
            'closedDate' => 'fin_medical_dclosed',
            'costTypeMappingId' => 3,
            'incurredField' => 'fin_medical_incurred',
            'auditTable' => 'fin_medical_reserve_audit',

        ],
        'legal' => [
            'closedDate' => 'fin_legal_dclosed',
            'costTypeMappingId' => 4,
            'incurredField' => 'fin_legal_incurred',
            'auditTable' => 'fin_legal_reserve_audit',

        ],
        'temporary_indemnity' => [
            'closedDate' => 'fin_temporary_indemnity_dclosed',
            'costTypeMappingId' => 5,
            'incurredField' => 'fin_temporary_indemnity_incurred',
            'auditTable' => 'fin_temporary_indemnity_reserve_audit',

        ],
        'permanent_indemnity' => [
            'closedDate' => 'fin_permanent_indemnity_dclosed',
            'costTypeMappingId' => 6,
            'incurredField' => 'fin_permanent_indemnity_incurred',
            'auditTable' => 'fin_permanent_indemnity_reserve_audit',
        ], ];

    // check we have all the relevant data
    if ($mainModule == 'PAY') {
        $data = getClaimData($data, $costTypeFieldInformation);
    }

    if ($mainModule == 'CLA' || $data['link_module'] == 'CLA' || $isClaimsPayment) {
        if (count(array_intersect(
            array_column($costTypeFieldInformation, 'closedDate'),
            array_keys($data),
        ))) {
            $sql = 'SELECT recordid, pay_date, pay_type, pay_calc_total FROM payments WHERE cla_id = :cla_id';
            $payments = \DatixDBQuery::PDO_fetch_all($sql, ['cla_id' => $claimId]);

            if ($mainModule == 'PAY') {
                // pay_calc_total is a calculated field and has yet to be calculated, so we will calculate it manually
                $data['pay_calc_total'] = $data['pay_amount'];
                if (!empty($data['pay_vat_rate'])) {
                    $data['pay_calc_total'] += ($data['pay_vat_rate'] / 100) * $data['pay_amount'];
                }
                // If the payment is found, it needs to be updated with the values (from $_POST) which have not yet been saved
                $paymentFound = false;
                foreach ($payments as $code => $payment) {
                    if ($payment['recordid'] == $data['recordid']) {
                        $paymentFound = true;
                        $payments[$code]['pay_date'] = $data['pay_date'];
                        $payments[$code]['pay_type'] = $data['pay_type'];
                        $payments[$code]['pay_calc_total'] = $data['pay_calc_total'];
                    }
                }
                // If the payment is not found (i.e. it's a new payment) we should add it in
                if (!$paymentFound) {
                    $payments[] = [
                        'pay_date' => $data['pay_date'],
                        'pay_type' => $data['pay_type'],
                        'pay_calc_total' => $data['pay_calc_total'],
                    ];
                }
            }

            foreach ($costTypeFieldInformation as $reserve) {
                if ($data[$reserve['closedDate']] !== null) {
                    $expectedReserve = calculateExpectedReserve($reserve, $data, $payments);

                    if (
                        bccomp($expectedReserve, $data[$reserve['incurredField']], 2) != 0
                        || !hasLossAdjustmentHistory($reserve['auditTable'], $claimId)
                    ) {
                        updateFullAudit($claimId, $currentDateTime, $reserve, $data);
                        $data = updateCostTypeAudit($claimId, $currentDateTime, $reserve, $data, $expectedReserve);
                    }
                }
            }
        }

        if ($mainModule == 'PAY') {
            updateIncurredFields($data, $costTypeFieldInformation);
        }
    }

    return $data;
}

/**
 * Updates the incurred field for each reserve in the $costTypeFieldInformation array.
 *
 * @param $data array Form data from claims form
 * @param $costTypeFieldInformation array Contains details of fields for reserve
 */
function updateIncurredFields($data, $costTypeFieldInformation)
{
    $fieldString = '';
    foreach ($costTypeFieldInformation as $costTypeFieldInfo) {
        $fieldString .= $costTypeFieldInfo['incurredField'] . '= :' . $costTypeFieldInfo['incurredField'] . ', ';
    }
    $fieldString = substr($fieldString, 0, -2);

    $sql = 'UPDATE CLAIMS_MAIN
            SET ' . $fieldString .
            ' WHERE recordid=:recordid';

    $insertarray = ['recordid' => $data['main_recordid']];

    foreach ($costTypeFieldInformation as $fieldInformation) {
        $insertarray[$fieldInformation['incurredField']] = $data[$fieldInformation['incurredField']];
    }

    \DatixDBQuery::PDO_query($sql, $insertarray);
}

/**
 * Updates the reserve audit for reserve passed in $reserve.
 *
 * @param $claimId integer Id for the Claim being updated
 * @param $currentDateTime datetime Current datetime
 * @param $reserve array Data for the reserve to update
 * @param $data array Form data from Claim form
 * @param $expectedReserve float calculated value of the reserve
 *
 * @return mixed
 */
function updateCostTypeAudit($claimId, $currentDateTime, $reserve, $data, $expectedReserve)
{
    $sql = 'INSERT INTO ' . $reserve['auditTable'] .
        ' (cla_id, field_value, change_value, changed_by, datetime_from, reason_for_change)
            VALUES
          (:cla_id, :field_value, :change_value, :changed_by, :datetime_from, :reason_for_change)';

    $change_value = bcsub($expectedReserve, $data[$reserve['incurredField']]);
    $data[$reserve['incurredField']] = $expectedReserve;

    \DatixDBQuery::PDO_query($sql, [
        'cla_id' => $claimId,
        'field_value' => $data[$reserve['incurredField']],
        'change_value' => $change_value,
        'changed_by' => $_SESSION['initials'],
        'datetime_from' => $currentDateTime,
        'reason_for_change' => 'Reserves have been automatically adjusted because the record has been closed or a payment has been updated',
    ]);

    return $data;
}

/**
 * Update the full audit with details of the changes to reserve values.
 *
 * @param $claimId integer Id of the claim being updated
 * @param $currentDateTime datetime Current datetime
 * @param $reserve array Data for the reserve to update
 * @param $data array Form data from Claim form
 */
function updateFullAudit($claimId, $currentDateTime, $reserve, $data)
{
    $userSession = (new UserSessionFactory())->create();

    $sql = 'INSERT INTO full_audit
            (aud_module, aud_record, aud_login, aud_delegate_id, aud_date, aud_action, aud_detail)
            VALUES
            (:aud_module, :aud_record, :aud_login, :aud_delegate_id, :aud_date, :aud_action, :aud_detail)';

    \DatixDBQuery::PDO_query($sql, [
        'aud_module' => 'CLA',
        'aud_record' => $claimId,
        'aud_login' => $userSession->getInitials(),
        'aud_delegate_id' => $userSession->getDelegator(),
        'aud_date' => $currentDateTime,
        'aud_action' => 'WEB:' . $reserve['incurredField'],
        'aud_detail' => (float) $data[$reserve['incurredField']],
    ]);
}

/**
 * Calculates the value of the expected reserve based on payments made for the reserver.
 *
 * @param $reserve array Data for the reserve to use
 * @param $data array Form data from Claim form
 * @param $payments array Data of payments against the current reserve
 *
 * @return float
 */
function calculateExpectedReserve($reserve, $data, $payments)
{
    $payTypes = getPaymentCodesForMappedReserves($reserve);

    $formattedDate = new DateTime($data[$reserve['closedDate']]);

    $expectedReserve = 0;
    foreach ($payments as $payment) {
        if (in_array($payment['pay_type'], $payTypes)) {
            if ($payment['pay_date'] != null) {
                $payDate = new DateTime($payment['pay_date']);

                if ($payDate->diff($formattedDate)->invert == 0) {
                    $expectedReserve += $payment['pay_calc_total'];
                }
            } else {
                $expectedReserve += $payment['pay_calc_total'];
            }
        }
    }

    return $expectedReserve;
}

/**
 * Return the codes for payments made against the reserver passed in $reserve.
 *
 * @param $reserve array Data for the reserve to use
 *
 * @return array
 */
function getPaymentCodesForMappedReserves($reserve)
{
    // find out which payment types relate to which reserveAuditTable
    $sql = 'SELECT CODE, cod_reserve FROM CODE_FIN_TYPE';
    $payTypeMappings = \DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_KEY_PAIR);

    $payTypes = [];
    foreach ($payTypeMappings as $code => $reserveId) {
        if ($reserveId == $reserve['costTypeMappingId']) {
            $payTypes[] = $code;
        }
    }

    return $payTypes;
}

/**
 * Returns data from claims_main about the current reserve data saved against a record.
 *
 * @param $data array Form data from Claim form
 * @param $costTypeFieldInformation
 *
 * @return array array Contains details of fields for reserve
 */
function getClaimData($data, $costTypeFieldInformation)
{
    $fieldString = '';
    foreach ($costTypeFieldInformation as $costTypeFieldInfo) {
        $fieldString .= $costTypeFieldInfo['closedDate'] . ', ' . $costTypeFieldInfo['incurredField'] . ', ';
    }
    $fieldString = substr($fieldString, 0, -2);

    $sql = 'SELECT ' . $fieldString . '
                FROM claims_main
                WHERE recordid = :cla_id';

    $claimData = \DatixDBQuery::PDO_fetch($sql, ['cla_id' => $data['main_recordid']]);

    // We can only merge the two arrays if the second one is an array otherwise we will lose the $data array completely.
    if (is_array($claimData)) {
        $data = array_merge($data, $claimData);
    }

    return $data;
}

/**
 * This deals with a very specific case for 'incurred' fields which once they have a value, cannot return to null.
 * The reason for this is that we must accurately track the loss adjustment history, and going from some value to
 * null is not the same as some value to zero (the former not being numerically measurable). If someone then clears
 * a value from one of these incurred fields we will manually interpret null as zero.
 *
 * @param array $data takes the data posted in saving a claim and manipulates it before saving
 *
 * @return $data the return of the manipulated data
 */
function checkIncurredAreNotNull(array $data): array
{
    $reserveAuditTables = [
        'fin_indemnity_reserve' => 'fin_indemnity_reserve_audit',
        'fin_expenses_reserve' => 'fin_expenses_reserve_audit',
        'fin_medical_incurred' => 'fin_medical_reserve_audit',
        'fin_legal_incurred' => 'fin_legal_reserve_audit',
        'fin_temporary_indemnity_incurred' => 'fin_temporary_indemnity_reserve_audit',
        'fin_permanent_indemnity_incurred' => 'fin_permanent_indemnity_reserve_audit',
    ];

    foreach ($reserveAuditTables as $reserve => $auditTable) {
        if (empty($data[$reserve]) && hasLossAdjustmentHistory($auditTable, $data['recordid'])) {
            $data[$reserve] = 0;
        }
    }

    return $data;
}

/**
 * Checks to see whether a claim record has any values set in the loss adjustment history for a particular reserveAuditTable.
 *
 * @param $reserveAuditTable The reserveAuditTable to be checked (should be either 'indemnity' or 'expenses')
 *
 * @return bool True if there are eny reserveAuditTable audit entries, else false
 */
function hasLossAdjustmentHistory($reserveAuditTable, $claimId)
{
    $sql = 'SELECT TOP 1 datetime_from FROM ' . $reserveAuditTable . '
            WHERE cla_id = :cla_id
            ORDER BY datetime_from DESC';
    $result = \DatixDBQuery::PDO_fetch($sql, ['cla_id' => $claimId]);

    return $result !== false;
}

/**
 * When moving a claim record from non-closed status to closed status, we need to update the closed date.
 */
function setClosedDate(array $data): array
{
    if (Container::get(Registry::class)->getParm('CLA_SET_CLOSED_DATE_IN_CLOSED', 'N')->isFalse()) {
        return $data;
    }

    if (
        $data['rep_approved'] != 'CLOSED'
        && $data['rep_approved_old'] == 'CLOSED'
    ) {
        $data['cla_dclosed'] = '';
    }

    if (
        $data['rep_approved'] == 'CLOSED'
        && $data['rep_approved_old'] != 'CLOSED'
    ) {
        $data['cla_dclosed'] = GetTodaysDate();
    }

    return $data;
}
