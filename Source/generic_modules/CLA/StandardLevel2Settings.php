<?php

use src\claims\models\ClaimsFields;

$GLOBALS['FormTitle'][7] = _fdtk('cla2_title');

$GLOBALS['DefaultValues'] = [
    'rep_approved' => 'UN',
    'cla_dopened' => 'TODAY',
];

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'location_id' => true,
    'service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
];

$GLOBALS['HideFields'] = [
    'cla_itype' => true,
    'cla_outcome' => true,
    'cla_otherref' => true,
    'cla_cspecialty' => true,
    'cla_cost' => true,
    'cla_head' => true,
    'causal_factor_header' => true,
    'causal_factor' => true,
    'rejection_history' => true,
    'causes' => true,
    'word' => true,
    'history' => true,
    'orphanedudfs' => true,
    'ccs2' => true,
    'action_chains' => true,
    'cla_level_intervention' => true,
    'cla_level_harm' => true,
    'reserve_audit' => true,
    'indem_dclosed' => true,
    'expen_dclosed' => true,
    'cla_last_updated' => true,
    'respondents' => true,
    'fin_medical_reserve' => true,
    'fin_legal_reserve' => true,
    'fin_temporary_indemnity_reserve' => true,
    'fin_permanent_indemnity_reserve' => true,
    'fin_medical_incurred' => true,
    'fin_legal_incurred' => true,
    'fin_temporary_indemnity_incurred' => true,
    'fin_permanent_indemnity_incurred' => true,
    'fin_medical_dclosed' => true,
    'fin_legal_dclosed' => true,
    'fin_temporary_indemnity_dclosed' => true,
    'fin_permanent_indemnity_dclosed' => true,
    'workers_comp' => true,
    'osha' => true,
    'cla_time' => true,
    'edi' => true,
    'cla_inc_ourref' => true,
    'cla_inc_time' => true,
    'rre_id' => true,
    'cla_generated_reference_id' => true,
    'incident_occurred_on_employer_premises' => true,
    'edi_froi_maintenance_type' => true,
    'agreement_to_compensate' => true,
    'late_reason_code' => true,
    'change_element_segment' => true,
    'change_reason' => true,
    'cancel_reason' => true,
    'number_managed_care_orgs' => true,
    'number_changed_data_elements' => true,
    'number_cancel_elements' => true,
    'safeguard_provided' => true,
    'safeguard_used' => true,
    'osha_registered_establishment' => true,
    'osha_recordable' => true,
    'osha_privacy_case' => true,
    'osha_case_classification' => true,
    'osha_treated_in_er' => true,
    'osha_hospitalized_overnight' => true,
    'osha_before_incident_description' => true,
    'osha_direct_harm_cause' => true,
    'edi_jurisdiction' => true,
    'edi_date_extracted' => true,
    'edi_date_processed' => true,
    'edi_agency_claim_number' => true,
    'edi_r1_froi_status' => true,
    'edi_cause_code' => true,
    'edi_drug_screen_summary' => true,
    'edi_froi_correction_date' => true,
    'edi_claim_status' => true,
    'has_orm' => true,
    'orm_termination_date' => true,
    'full_denial_effective_date' => true,
    'full_denial_reason_code' => true,
    'denial_rescission_date' => true,
    'edi_iaiabc_sroi_filing_status' => true,
    'contacts_type_R' => true,
    // SPSC
    ClaimsFields::CLA_INV_LESSONS_SUB_CATEGORY => true,
    ClaimsFields::CLA_HRO_CHARACTERISTICS => true,
    ClaimsFields::CLA_SPECIALTY => true,
    ClaimsFields::SOURCE_OF_RECORD => true,
    ClaimsFields::CLA_GRADE_RATING => true,
];

$GLOBALS['ReadOnlyFields'] = [
    'cla_inc_ourref' => true,
    'cla_inc_time' => true,
    'edi_date_extracted' => true,
    'edi_date_processed' => true,
    'edi_agency_claim_number' => true,
];

$GLOBALS['NewPanels'] = [
    'location' => true,
    'progress_notes' => true,
    'contacts_type_M' => true,
    'finance' => true,
    'respondents' => true,
    'feedback' => true,
    'investigation' => true,
    'linked_actions' => true,
    'action_chains' => true,
    'documents' => true,
    'notepad' => true,
    'linked_records' => true,
    'history' => true,
    'rejection' => true,
    'orphanedudfs' => true,
];

$GLOBALS['ExpandSections'] = [
    'rep_approved' => [
        0 => [
            'section' => 'rejection',
            'alerttext' => 'Please complete the \'Details of rejection\' section before saving this form.',
            'values' => [
                0 => 'REJECT',
            ],
        ],
    ],
    'cla_causal_factors_linked' => [
        0 => [
            'section' => 'causal_factor',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
];

$GLOBALS['MandatoryFields'] = [
    'cla_name' => 'details',
];

$GLOBALS['UserExtraText'] = [
    'dum_fbk_to' => ['7' => 'Only staff and contacts with e-mail addresses are shown.'],
    'dum_fbk_gab' => ['7' => 'Only users with e-mail addresses are shown.'],
    'dum_fbk_email' => ['7' => 'Enter e-mail addresses of other recipients not listed above. You can<br />enter multiple addresses, separated by commas.'],
    'flag_for_investigation' => ['7' => 'Selecting yes will submit this record for investigation review.'],
];
