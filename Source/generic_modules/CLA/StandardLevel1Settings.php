<?php

use src\claims\models\ClaimsFields;

$GLOBALS['FormTitle'][7] = _fdtk('cla1_title');

$GLOBALS['taggedFields'] = [
    'other_location' => true,
    'other_service' => true,
    'location_id' => true,
    'service_id' => true,
];

$GLOBALS['lastChildFirstFields'] = [
    'location_id' => false,
    'service_id' => false,
    'other_location' => false,
    'other_service' => false,
];

$GLOBALS['ExpandSections'] = [
    'show_employee' => [
        0 => [
            'section' => 'contacts_type_E',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'show_other_contacts' => [
        0 => [
            'section' => 'contacts_type_N',
            'alerttext' => '',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'cla_causal_factors_linked' => [
        0 => [
            'section' => 'causal_factor',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
    'show_document' => [
        0 => [
            'section' => 'documents',
            'values' => [
                0 => 'Y',
            ],
        ],
    ],
];

$GLOBALS['HideFields'] = [
    'cla_itype' => true,
    'cla_dopened' => true,
    'cla_probability' => true,
    'cla_estset' => true,
    'cla_dsettled' => true,
    'cla_dclosed' => true,
    'cla_outcome' => true,
    'cla_otherref' => true,
    'cla_cspecialty' => true,
    'causal_factor_header' => true,
    'causal_factor' => true,
    'ccs2' => true,
    'cla_level_intervention' => true,
    'cla_level_harm' => true,
    'contacts_type_O' => true,
    'respondents_organisation' => true,
    'workers_comp' => true,
    'incident_occurred_on_employer_premises' => true,
    'safeguard_provided' => true,
    'safeguard_used' => true,
    'has_orm' => true,
    'orm_termination_date' => true,
    'contacts_type_R' => true,
    // SPSPC
    ClaimsFields::CLA_SPECIALTY => true,
];

$GLOBALS['DefaultValues'] = [
    'rep_approved' => 'UN',
    'cla_dopened' => 'TODAY',
];

$GLOBALS['MandatoryFields'] = [
    'cla_name' => 'details',
];

$GLOBALS['FormDesigns'] = [
    'contacts_type_A' => [
        'A' => '0',
    ],
    'contacts_type_C' => [
        'E' => '0',
    ],
];
