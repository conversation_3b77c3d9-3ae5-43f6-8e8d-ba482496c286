<?php

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\valueObjects\Module;
use Source\generic_modules\BasicFormSectionHelperFactory;
use src\claims\models\ClaimsFields;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\framework\session\UserSession;
use src\helpers\GenericBasicFormHelper;
use src\system\container\facade\Container;

$basicFormHelper = new GenericBasicFormHelper();

// Initial $FormType declaration comes from outside of this file as it is added using an include in the main form builder process
$FormType = $basicFormHelper->getValidFormMode($FormType);

$sectionHelper = (new BasicFormSectionHelperFactory())->create($FormType);

$registry ??= Container::get(Registry::class);
$investigationsIsLicensed = Container::get(ModuleRepository::class)->getModuleByCode('INV')->isEnabled();
$currentUserCanSeeORG = Container::get(UserSession::class)->getCurrentUser()->canSeeModule('ORG');

// variables for globals table values
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showOshaFields = $registry->getParm('SHOW_OSHA_FIELDS', 'N')->isTrue();
$showEdiFields = $registry->getParm('SHOW_EDI_FIELDS', 'N')->isTrue();
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;

$showLastChildFirst = Container::get(DatixConfig::class)->showLastChildFirst();

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
    ],
    'details' => [
        'Title' => _fdtk('claim_details', $useFormDesignLanguage),
        'Rows' => [
            'recordid',
            [
                'Name' => ClaimsFields::SOURCE_OF_RECORD,
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'ReadOnly' => true,
            ],
            'cla_ourref',
            'cla_generated_reference_id',
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => 'CLA',
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => 'CLA',
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'cla_mgr',
            'cla_name',
            'cla_type',
            'cla_subtype',
            'cla_synopsis',
            'cla_dopened',
            'cla_inc_ourref',
            'cla_time',
            'cla_dincident',
            'cla_inc_time',
            'cla_dclaim',
            'cla_dinsurer',
            'cla_insurer',
            'cla_insurer_ref',
            'cla_probability',
            'cla_estset',
            'cla_dsettled',
            'cla_dclosed',
            'cla_itype',
            'cla_head',
            'cla_outcome',
            'cla_otherref',
            'cla_cspecialty',
            ['Name' => 'cla_level_intervention', 'Condition' => bYN(GetParm('CCS2_CLA', 'N'))],
            ['Name' => 'cla_level_harm', 'Condition' => bYN(GetParm('CCS2_CLA', 'N'))],
            ['Name' => 'cla_last_updated', 'ReadOnly' => true],
            [
                'Name' => 'flag_for_investigation',
                'ReadOnly' => $data['flag_for_investigation'] === 'Y',
                'Condition' => $investigationsIsLicensed,
            ],
            ['Name' => 'rre_id', 'Condition' => $showUsClaimsFields],
            ['Name' => 'incident_occurred_on_employer_premises', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_provided', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_used', 'Condition' => $showUsClaimsFields],
            // SPSC
            ['Name' => ClaimsFields::CLA_HRO_CHARACTERISTICS, 'Condition' => $showSpscFields],
            ['Name' => ClaimsFields::CLA_SPECIALTY, 'Condition' => $showSpscFields],
        ],
    ],
    'contacts_type_R' => [
        'Title' => _fdtk('details_of_person_reporting_the', $useFormDesignLanguage) . ' ' . _fdtk('CLAName', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => 'CLA',
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::REPORTER,
        'Role' => $registry->getParm('REPORTER_ROLE', 'REP')->__toString(),
        'LinkedForms' => [ContactTypes::REPORTER => ['module' => 'CON']],
        'suffix' => 3,
        'Rows' => [],
        'NotModes' => ['Print', 'Edit'],
    ],
    'ccs' => [
        'Title' => _fdtk('datix_ccs', $useFormDesignLanguage),
        'Rows' => [
            'cla_carestage',
            'cla_clin_detail',
            'cla_clintype',
        ],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_CLA', 'N')),
        'Rows' => [
            'cla_affecting_tier_zero',
            'cla_type_tier_one',
            'cla_type_tier_two',
            'cla_type_tier_three',
        ],
    ],
    'curstage' => [
        'Title' => _fdtk('current_stage', $useFormDesignLanguage),
        'Rows' => ['cla_curstage'],
    ],
    'stagehistory' => [
        'Title' => _fdtk('stage_history', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'stageHistory' => [
                'controller' => src\claims\controllers\StageHistoryController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
    ],
    'additional' => [
        'Title' => _fdtk('additional_details', $useFormDesignLanguage),
        'Rows' => [
            'show_employee',
            'show_other_contacts',
            'show_document',
        ],
    ],
    'rejection' => GenericRejectionArray('CLA', $data, $useFormDesignLanguage),
    'rejection_history' => [
        'Title' => _fdtk('reasons_history_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'SectionRejectionHistory' => [
                'controller' => src\reasons\controllers\ReasonsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Condition' => bYN(GetParm('REJECT_REASON', 'Y')),
        'Rows' => [],
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'workers_comp' => [
        'Title' => _fdtk('workers_comp', $useFormDesignLanguage),
        'Condition' => $showOshaFields,
        'Rows' => [
            'has_orm',
            'orm_termination_date',
        ],
    ],
    'osha' => [
        'Title' => 'OSHA',
        'Condition' => $showOshaFields,
        'Rows' => [
            'osha_registered_establishment',
            'osha_recordable',
            'osha_privacy_case',
            'osha_case_classification',
            'osha_treated_in_er',
            'osha_hospitalized_overnight',
            'osha_before_incident_description',
            'osha_direct_harm_cause',
        ],
    ],
    'edi' => [
        'Title' => 'EDI',
        'Condition' => $showEdiFields,
        'Rows' => [
            'edi_jurisdiction',
            'edi_date_extracted',
            'edi_date_processed',
            'edi_agency_claim_number',
            'edi_r1_froi_status',
            'edi_froi_correction_date',
            'edi_claim_status',
            'edi_cause_code',
            'full_denial_effective_date',
            'full_denial_reason_code',
            'denial_rescission_date',
            'edi_iaiabc_sroi_filing_status',
            'edi_froi_maintenance_type',
            'agreement_to_compensate',
            'late_reason_code',
            'change_element_segment',
            'change_reason',
            'cancel_reason',
            'number_managed_care_orgs',
            'number_changed_data_elements',
            'number_cancel_elements',
        ],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => ['New', 'Search'],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    // This section will be populated below
    'respondents' => [],
    'finance' => [],
    'financeTable' => [],
    'reserve_audit' => [
        'Title' => _fdtk('loss_adjustment_history', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'NotModes' => ['New', 'Search'],
        'ControllerAction' => [
            'ReserveAudit' => [
                'controller' => src\claims\controllers\FinanceController::class,
            ],
        ],
        'Rows' => [],
    ],
    'payments_summary' => [
        'Title' => _fdtk('payments_summary', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NotModes' => ['New'],
        'ControllerAction' => [
            'paymentsSummary' => [
                'controller' => src\claims\controllers\FinanceController::class,
            ],
        ],
    ],
    'feedback' => [
        'Title' => _fdtk('feedback_title', $useFormDesignLanguage),
        'Special' => 'Feedback',
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'NoFieldRemoval' => true,
        'NoReadOnly' => true,
        'Rows' => [
            [
                'Name' => 'dum_fbk_to',
                'Title' => 'Staff and contacts attached to this record',
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_gab',
                'Title' => _fdtk('all_users'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_email',
                'Title' => _fdtk('additional_recipients', $useFormDesignLanguage),
                'NoReadOnly' => true,
                'NoMandatory' => true,
            ],
            [
                'Name' => 'dum_fbk_subject',
                'Title' => _fdtk('subject'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_body',
                'Title' => _fdtk('body_of_message_header'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoHide' => true,
                'NoOrder' => true,
            ],
            [
                'Name' => 'dum_fbk_attachments',
                'Title' => _fdtk('attachments'),
                'NoReadOnly' => true,
                'NoMandatory' => true,
                'NoOrder' => true,
                'NoHide' => false,
            ],
        ],
    ],
    'investigation' => [
        'Title' => _fdtk('review', $useFormDesignLanguage),
        'Rows' => [
            'cla_investigator',
            'cla_inv_dstart',
            'cla_inv_dcomp',
            'cla_cost',
            'dum_cla_grading',
            'cla_inv_outcome',
            'cla_lessons_code',
            ['Name' => ClaimsFields::CLA_INV_LESSONS_SUB_CATEGORY, 'Condition' => $showSpscFields],
            'cla_inv_lessons',
            'cla_inquiry',
            'cla_action_code',
            'cla_inv_action',
        ],
    ],
    'causes' => [
        'Title' => _fdtk('causes', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'SectionRootCauseDetails' => [
                'controller' => src\rootcauses\controllers\RootCausesController::class,
            ],
        ],
        'Rows' => [],
    ],
    'causal_factor_header' => [
        'Title' => _fdtk('causal_factors_for', $useFormDesignLanguage) . ' ' . _fdtk('CLAName', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NewPanel' => true,
        'Rows' => [
            'cla_causal_factors_linked',
        ],
    ],
    'causal_factor' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design'),
        'LinkedDataSection' => true,
        'NoTitle' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'DoCausalFactorsSection' => [
                'controller' => src\causalfactors\controllers\CausalFactorsController::class,
            ],
        ],
        'ExtraParameters' => ['causal_factor_name' => 'cla_causal_factor'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'causal_factor_design' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'causal_factor',
        'NoSectionActions' => true,
        'Rows' => [
            'caf_level_1',
            'caf_level_2',
        ],
    ],
    'action_chains' => [
        'Title' => _fdtk('action_chains', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'getActionChains' => [
                'controller' => src\actionchains\controllers\ActionChainController::class,
            ],
        ],
        'LinkedForms' => ['action_chains' => ['module' => 'ACT']],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'word' => [
        'Title' => _fdtk('mod_templates_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            ],
        ],
        'NotModes' => ['New', 'Search', 'Print'],
        'Rows' => [],
    ],
    'notepad' => [
        'Title' => _fdtk('notepad', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NotModes' => ['New'],
        'Condition' => $FormType != 'linkedDataSearch',
        'Rows' => [
            'notes',
        ],
    ],
    'linked_records' => [
        'Title' => _fdtk('linked_records', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NotModes' => ['New', 'Search'],
        'Condition' => $FormType != 'linkedDataSearch',
        'Special' => 'LinkedRecords',
        'Rows' => [],
    ],
    'history' => [
        'Title' => _fdtk('notifications', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'MakeEmailHistoryPanel' => [
                'controller' => src\email\controllers\EmailHistoryController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ],
];

if ($FormType == 'Search') {
    $FormArray['respondents'] = [
        'Title' => _fdtk('respondents_title', $useFormDesignLanguage),
        'contacttype' => 'O',
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateMultipleSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'NotModes' => ['New', 'Design', 'Print', 'Edit'],
        'Rows' => [],
        'ExtraParameters' => [
            'linkedSections' => [
                'O' => [
                    'module' => 'CON',
                    'label' => _fdtk('cla_individual_respondent_title', $useFormDesignLanguage),
                    'sectionId' => 'respondents_individual',
                ],
                'G' => [
                    'module' => 'ORG',
                    'label' => _fdtk('cla_organisation_respondent_title', $useFormDesignLanguage),
                    'sectionId' => 'respondents_organisation',
                ],
            ],
            'module' => 'CLA',
        ],
    ];
} else {
    $FormArray['respondents'] = [
        'Title' => _fdtk('respondents_title', $useFormDesignLanguage),
        'contacttype' => 'O',
        'LinkedForms' => [
            'O' => ['module' => 'CON', 'Label' => _fdtk('label_individual_respondent_form_design', $useFormDesignLanguage)],
            'G' => ['module' => 'ORG', 'Label' => _fdtk('label_organisation_respondent_form_design', $useFormDesignLanguage)],
        ],
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'ListLinkedRespondents' => [
                'controller' => src\respondents\controllers\RespondentsController::class,
            ],
        ],
        'NotModes' => ['New', 'Search'],
        'Rows' => [],
    ];
    if ($registry->getParm('CUSTOMIZABLE_RESPONDENT', 'N', true)->toScalar() == 'Y') {
        $FormArray['respondents']['Listings'] = [ContactTypes::RESPONDENT => ['module' => Module::CONTACTS]];
    }
}

$FormArray['finance'] = [
    'Title' => _fdtk('finance', $useFormDesignLanguage),
    'Rows' => [
        'fin_estdamages',
        'fin_estpcosts',
        'fin_estdefence',
        'fin_calc_total',
        'fin_ourshare',
        'fin_calc_ourshare',
        'fin_otherparty',
        'fin_thirdshare',
        'fin_otherparty2',
        'fin_thirdshare2',
        'cla_insurer_excess',
        'fin_calc_liability',
        'fin_calc_reimbursement',
    ],
];

$FormArray['financeTable'] = [
    'Title' => _fdtk('reserves', $useFormDesignLanguage),
    'NoFieldRemoval' => true,
    'NoFieldAdditions' => true,
    'ControllerAction' => [
        'financeTable' => [
            'controller' => src\claims\controllers\FinanceController::class,
        ],
    ],
    'Rows' => [
        [
            'Name' => 'fin_indemnity_reserve',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
        ],
        [
            'Name' => 'fin_expenses_reserve',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
        ],
        [
            'Name' => 'fin_medical_incurred',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_legal_incurred',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_temporary_indemnity_incurred',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_permanent_indemnity_incurred',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'indem_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
        ],
        [
            'Name' => 'expen_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
        ],
        [
            'Name' => 'fin_medical_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_legal_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_temporary_indemnity_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
        [
            'Name' => 'fin_permanent_indemnity_dclosed',
            'NoMandatory' => true,
            'NoHide' => true,
            'NoOrder' => true,
            'NoAdvanced' => true,
            'Condition' => $registry->getParm('SHOW_US_CLAIMS_FIELDS')->isTrue(),
        ],
    ],
];

$ContactArray = [];
// add contact sections for each contact type.
foreach ($ModuleDefs['CLA']['CONTACTTYPES'] as $ContactTypeDetails) {
    // skip respondents
    if ($ContactTypeDetails['Type'] == 'O') {
        continue;
    }

    if ($FormType === 'Search') {
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_CLA_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'LinkedDataSection' => true,
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'generateSearchCriteria' => [
                    'controller' => src\contacts\controllers\SearchCriteriaController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $ContactTypeDetails['Type'], 'linkModule' => 'CON', 'module' => 'CLA', 'sectionId' => 'contacts_type_' . $ContactTypeDetails['Type']],
            'NotModes' => ['New', 'Design', 'Print', 'Edit'],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => $FormType != 'linkedDataSearch' && (CanSeeContacts('CLA', $DIFPerms, $inc['rep_approved']) || !bYN(GetParm('DIF2_HIDE_CONTACTS', 'N'))),
            'Rows' => [],
        ];
    } else {
        $ContactArray['contacts_type_' . $ContactTypeDetails['Type']] = [
            'Title' => _fdtk('table_link_contacts_CLA_' . $ContactTypeDetails['Type'], $useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'ListLinkedContacts' => [
                    'controller' => src\contacts\controllers\ContactsController::class,
                ],
            ],
            'ExtraParameters' => ['link_type' => $ContactTypeDetails['Type']],
            'NotModes' => ['New', 'Search'],
            'Listings' => ['contacts_type_' . $ContactTypeDetails['Type'] => ['module' => 'CON']],
            'LinkedForms' => [$ContactTypeDetails['Type'] => ['module' => 'CON']],
            'Condition' => (
                CanSeeContacts('COM', $DIFPerms ?? null, $inc['rep_approved'] ?? null)
                || !bYN(GetParm('DIF2_HIDE_CONTACTS', 'N'))
            ),
            'Rows' => [],
        ];
    }
}

$ActionsArray = [];

// Decide if we are using Actions for search or display
if ($FormType === 'Search') {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'ACT', 'module' => 'CLA', 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'],
        'NotModes' => ['New'],
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT']],
        'Rows' => [],
    ];
} else {
    $ActionsArray['linked_actions'] = [
        'Title' => _fdtk('actions', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Special' => 'LinkedActions',
        'LinkedForms' => ['linked_actions' => ['module' => 'ACT', 'carltonFormDesigns' => true]],
        'NotModes' => ['New'],
        'Condition' => $FormType != 'linkedDataSearch',
        'Rows' => [],
    ];
}

$paymentsArray = [];

// Decide if we are using Payments for search or display
if ($FormType === 'Search') {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => ['linkModule' => 'PAY', 'module' => 'CLA', 'sectionId' => 'payments', 'link_type' => 'payments'],
        'NotModes' => ['New'],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'Listings' => ['payments' => ['module' => 'PAY']],
    ];
} else {
    $paymentsArray['payments'] = [
        'Title' => _fdtk('mod_payments_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => ['New'],
        'Condition' => $FormType != 'linkedDataSearch',
        'Listings' => ['payments' => ['module' => 'PAY']],
        'LinkedForms' => ['payments' => ['module' => 'PAY']],
        'ControllerAction' => [
            'listPaymentsForClaim' => [
                'controller' => src\payments\controllers\PaymentController::class,
            ],
        ],
    ];
}

array_insert_datix($FormArray, 'finance', $ContactArray);
array_insert_datix($FormArray, 'action_chains', $ActionsArray);
array_insert_datix($FormArray, 'feedback', $paymentsArray);

$FormArray = $sectionHelper->addLinkedModuleListings($FormArray);

return $FormArray;
