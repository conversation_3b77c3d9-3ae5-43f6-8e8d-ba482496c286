<?php

use app\models\contact\ContactTypes;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\forms\RecordHeaderProvider;
use Source\generic_modules\ModuleDefKeys;
use src\claims\models\ClaimsFields;
use src\framework\events\FormEventsHelper;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$spscDataset = $registry->getParm('SPSC_DATASET')->isTrue();
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS', 'N')->isTrue();

$ModuleDefs['CLA'] = [
    'GENERIC' => true,
    'IS_MAIN_MODULE' => true,
    'GENERIC_FOLDER' => 'CLA',
    'LEVEL1_PERMS' => ['CLA1'],
    'AUDIT_TRAIL_PERMS' => ['CLA2'],  // which permissions can see the audit trail?
    'NAME_FIELD' => 'cla_name',
    'MOD_ID' => MOD_CLAIMS,
    'CODE' => 'CLA',
    'APPROVAL_LEVELS' => true,
    'NAME' => _fdtk('mod_claims_title'),
    'USES_APPROVAL_STATUSES' => true,
    'TABLE' => 'claims_main',
    'HAS_DEFAULT_LISTING' => true,
    'REC_NAME' => _fdtk('CLAName'),
    'REC_NAME_PLURAL' => _fdtk('CLANames'),
    'REC_NAME_TITLE' => _fdtk('CLANameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('CLANamesTitle'),
    'FK' => 'cla_id',
    'PK' => 'recordid',
    'OURREF' => 'cla_ourref',
    RecordHeaderProvider::APPVARS_KEY => [
        'recordid',
        'cla_ourref',
        'cla_name',
        'cla_type',
        'cla_subtype',
        'cla_curstage',
    ],
    'PERM_GLOBAL' => 'CLA_PERMS',
    'NO_LEVEL1_GLOBAL' => 'CLA_NO_OPEN',
    'ACTION' => 'record&module=CLA',
    'FIELD_NAMES' => [
        'NAME' => 'cla_name',
        'HANDLER' => 'cla_mgr',
        'MANAGER' => 'cla_head',
        'REF' => 'cla_ourref',
        'INVESTIGATORS' => 'cla_investigator',
        'INCIDENT_DATE' => 'cla_dincident',
    ],
    'DATE_OPENED_AT' => ClaimsFields::CLAIM_OPENED,
    'LOCATION_FIELD_PREFIX' => 'cla_',
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'CLAIM1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::CLAIMS_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'CLAIM2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::CLAIMS_LEVEL_2],
    ],
    ModuleDefKeys::POST_LINK_CONTACT_SAVE_EVENT => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => ['Source/generic_modules/CLA/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => ['SavePatient'],
            ],
        ],
    ],
    'LINKED_MODULES' => [
        'CON' => ['action' => 'linkcontactgeneral'],
    ],
    'CAUSAL_FACTOR_TYPE' => 'cla_causal_factor',
    'LOGGED_OUT_LEVEL1' => true,
    'FORMCODE1' => 'CLAIM1',
    'FORMCODE2' => 'CLAIM2',
    'ICON' => 'icons/icon_CLA.png',
    'AGE_AT_DATE' => 'cla_dincident',
    'ADD_NEW_RECORD_LEVELS' => ['CLA2', 'CLA1'],

    'HARD_CODED_LISTINGS' => [
        'UN' => [
            'Title' => _fdtk('potential_claims'),
            'Link' => _fdtk('potential_claims'),
            'UseApprovalStatusDescription' => 'UN',
            'Where' => 'claims_main.rep_approved = \'UN\' OR claims_main.rep_approved IS NULL OR claims_main.rep_approved = \'\'',
            'Condition' => false, // so the menu item doesn't appear in the options menu
            'NoOverdue' => true,
            'workflows' => ['0'],
        ],

        'ACT' => [
            'Title' => _fdtk('confirmed_claims'),
            'Link' => _fdtk('confirmed_claims'),
            'UseApprovalStatusDescription' => 'ACT',
            'Where' => 'claims_main.rep_approved = \'ACT\'',
            'Condition' => false,
            'NoOverdue' => true,
            'workflows' => ['0', '1'],
        ],

        'CLOSED' => [
            'Title' => _fdtk('closed_claims'),
            'Link' => _fdtk('closed_claims'),
            'UseApprovalStatusDescription' => 'CLOSED',
            'Where' => 'claims_main.rep_approved = \'CLOSED\'',
            'Condition' => false,
            'NoOverdue' => true,
            'workflows' => ['0', '1'],
        ],

        'REJECT' => [
            'Title' => _fdtk('rejected_claims'),
            'Link' => _fdtk('rejected_claims'),
            'UseApprovalStatusDescription' => 'REJECT',
            'Where' => 'claims_main.rep_approved = \'REJECT\'',
            'Condition' => false,
            'NoOverdue' => true,
            'workflows' => ['0', '1'],
        ],
    ],
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'documents',

    'LINKED_CONTACT_LISTING_COLS' => ['recordid', 'cla_name', 'cla_ourref', 'cla_dopened', 'cla_type'],
    'LINKED_ORGANISATIONS' => true,

    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function ($data): void {
                        AddLatestStage($data);
                    },
                ],
            ],
        ],
    ],

    'SEARCH_URL' => 'action=search',
    'DEFAULT_ORDER' => 'cla_dopened',
    'OVERDUE_CHECK_FIELD' => 'cla_dopened',
    'OVERDUE_STATUSES' => ['ACT'],

    'CONTACTTYPES' => [
        ContactTypes::CLAIMANT => [
            'Type' => ContactTypes::CLAIMANT,
            'Name' => _fdtk('cla_claimant'),
            'Plural' => _fdtk('cla_claimant_plural'),
            'None' => _fdtk('no_cla_claimant_plural'),
            'CreateNew' => _fdtk('cla_claimant_link'),
        ],
        ContactTypes::PERSON_AFFECTED => [
            'Type' => ContactTypes::PERSON_AFFECTED,
            'Name' => _fdtk('cla_person'),
            'Plural' => _fdtk('cla_person_plural'),
            'None' => _fdtk('no_cla_person_plural'),
            'CreateNew' => _fdtk('cla_person_link'),
        ],
        ContactTypes::EMPLOYEE => [
            'Type' => ContactTypes::EMPLOYEE,
            'Name' => _fdtk('cla_employee'),
            'Plural' => _fdtk('cla_employee_plural'),
            'None' => _fdtk('no_cla_employee_plural'),
            'CreateNew' => _fdtk('cla_employee_link'),
        ],
        ContactTypes::OTHER_CONTACT => [
            'Type' => ContactTypes::OTHER_CONTACT,
            'Name' => _fdtk('cla_other_contact'),
            'Plural' => _fdtk('cla_other_contact_plural'),
            'None' => _fdtk('no_cla_other_contact_plural'),
            'CreateNew' => _fdtk('cla_other_contact_link'),
        ],
        ContactTypes::INDIVIDUAL_RESPONDENT => [
            'Type' => ContactTypes::INDIVIDUAL_RESPONDENT,
            'Name' => _fdtk('cla_individual_respondent_title'),
            'Plural' => _fdtk('cla_individual_respondent_plural'),
            'None' => _fdtk('no_cla_individual_respondent_plural'),
            'CreateNew' => _fdtk('cla_individual_respondent_link'),
        ],
    ],

    'LEVEL1_CON_OPTIONS' => [
        ContactTypes::INDIVIDUAL_RESPONDENT => ['Title' => _fdtk('cla_individual_respondent_title'), 'DivName' => 'contacts_type_O'],
        ContactTypes::CLAIMANT => ['Title' => _fdtk('cla_claimant'), 'DivName' => 'contacts_type_M'],
        ContactTypes::PERSON_AFFECTED => ['Title' => _fdtk('cla_person'), 'DivName' => 'contacts_type_A'],
        ContactTypes::OTHER_CONTACT => ['Title' => _fdtk('cla_contact'), 'DivName' => 'contacts_type_N'],
        ContactTypes::EMPLOYEE => ['Title' => _fdtk('employee'), 'DivName' => 'contacts_type_E'],
        ContactTypes::REPORTER => [
            'Title' => _fdtk('cla_reporter'),
            'Role' => $registry->getParm('REPORTER_ROLE', 'REP'),
            'ActualType' => ContactTypes::OTHER_CONTACT,
            'DivName' => 'contacts_type_R',
            'Max' => 1,
        ],
    ],

    'STAFF_EMPL_FILTER_MAPPINGS' => [
        'location' => 'location_id',
        'service' => 'service_id',
    ],

    'FIELD_ARRAY' => [
        'cla_name', 'cla_carestage', 'cla_clin_detail', 'cla_clintype',
        'cla_synopsis', 'cla_mgr', 'cla_head', 'cla_ourref', 'cla_otherref', 'cla_dincident',
        'cla_dopened', 'cla_dclaim', 'cla_dclosed', 'cla_dsettled', 'cla_type', 'cla_subtype',
        'cla_itype', 'cla_outcome', 'location_id', 'service_id', 'other_location',
        'other_service', 'confirm_location_id', 'confirm_service_id', 'cla_cspecialty', 'cla_estset',
        'cla_probability', 'fin_maxliability', 'fin_estdamages', 'fin_estpcosts',
        'fin_estdefence', 'fin_totalclaim', 'fin_cnstliability', 'fin_ourexcess', 'fin_ourshare',
        'fin_thisyear', 'fin_nextyear', 'fin_future', 'cla_insurer', 'cla_insurer_ref',
        'cla_investigator', 'cla_inv_dstart', 'cla_inv_dcomp', 'cla_root_causes', 'cla_inv_outcome',
        'cla_lessons_code', 'cla_inv_lessons', 'cla_action_code', 'cla_inv_action', 'cla_inquiry',
        'cla_cost', 'cla_likelihood', 'cla_consequence', 'cla_grade', 'cla_dinsurer',
        'show_employee', 'show_other_contacts', 'show_document', 'fin_calc_total', 'fin_calc_ourshare',
        'cla_curstage', 'fin_otherparty', 'fin_thirdshare', 'fin_otherparty2', 'fin_thirdshare2',
        'cla_insurer_excess', 'fin_calc_liability', 'fin_calc_reimbursement', 'rep_approved',
        'cla_affecting_tier_zero', 'cla_type_tier_one', 'cla_type_tier_two', 'cla_type_tier_three', 'fin_indemnity_reserve', 'fin_expenses_reserve',
        'cla_level_intervention', 'cla_level_harm', 'fin_calc_reserve_1', 'fin_calc_reserve_2',
        'fin_calc_total_incurred', 'fin_calc_total_paid', 'fin_calc_total_collected', 'fin_calc_total_reserved', 'fin_calc_total_closed_date',
        'cla_last_updated', 'indem_dclosed', 'expen_dclosed', 'cla_causal_factors_linked', 'flag_for_investigation', 'osha_registered_establishment',
        'fin_medical_reserve', 'fin_legal_reserve', 'fin_temporary_indemnity_reserve', 'fin_permanent_indemnity_reserve',
        'fin_medical_incurred', 'fin_legal_incurred', 'fin_temporary_indemnity_incurred', 'fin_permanent_indemnity_incurred',
        'fin_calc_indemnity_paid', 'fin_calc_expenses_paid', 'fin_calc_medical_paid', 'fin_calc_legal_paid',
        'fin_calc_temporary_indemnity_paid', 'fin_calc_permanent_indemnity_paid',
        'fin_calc_indemnity_collected', 'fin_calc_expenses_collected', 'fin_calc_medical_collected', 'fin_calc_legal_collected',
        'fin_calc_temporary_indemnity_collected', 'fin_calc_permanent_indemnity_collected',
        'fin_medical_dclosed', 'fin_legal_dclosed', 'fin_temporary_indemnity_dclosed', 'fin_permanent_indemnity_dclosed',
        'has_orm', 'orm_termination_date', 'osha_recordable', 'osha_privacy_case', 'osha_case_classification',
        'osha_treated_in_er', 'osha_hospitalized_overnight', 'osha_before_incident_description', 'osha_direct_harm_cause',
        'cla_time', 'edi_jurisdiction', 'cla_inc_ourref', 'cla_inc_time', 'rre_id', 'cla_generated_reference_id', 'updateddate', 'updatedby',
        'incident_occurred_on_employer_premises', 'safeguard_provided', 'safeguard_used',
        'edi_date_extracted', 'edi_date_processed', 'edi_agency_claim_number', 'edi_r1_froi_status',
        'edi_froi_correction_date', 'edi_claim_status', 'edi_cause_code', 'full_denial_effective_date', 'full_denial_reason_code',
        'denial_rescission_date', 'edi_iaiabc_sroi_filing_status', 'edi_froi_maintenance_type', 'agreement_to_compensate',
        'late_reason_code', 'change_element_segment', 'change_reason', 'cancel_reason', 'number_managed_care_orgs',
        'number_changed_data_elements', 'number_cancel_elements',
        ClaimsFields::CLA_INV_LESSONS_SUB_CATEGORY,
        ClaimsFields::CLA_HRO_CHARACTERISTICS,
        ClaimsFields::CLA_SPECIALTY,
        ClaimsFields::SOURCE_OF_RECORD,
        ClaimsFields::CLA_GRADE_RATING,
    ],
    'LINKED_RECORDS' => [
        'cla_causal_factor' => [
            'section' => 'causal_factor',
            'type' => 'cla_causal_factor', 'table' => 'causal_factors', 'recordid_field' => 'recordid', 'save_listorder' => true,
            'basic_form' => ['Rows' => ['caf_level_1', 'caf_level_2']],
            'main_recordid_label' => 'CLA_ID',
            'useIdentity' => true,
        ],
    ],
    ModuleDefKeys::PRE_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_INCLUDES => [__DIR__ . '/ModuleFunctions.php'],
                FormEventsHelper::EVENT_CONDITION => null,
                FormEventsHelper::EVENT_FUNCTIONS => [
                    function (array $data): array {
                        $data = checkIncurredAreNotNull($data);
                        $data = calculateClaimCloseDates($data);
                        $data = zeroReservesOnDate($data);

                        return setClosedDate($data);
                    },
                ],
            ],
        ],
    ],
    'USE_WORKFLOWS' => true,
    'WORKFLOW_GLOBAL' => 'CLA_WORKFLOW',
    'DEFAULT_WORKFLOW' => 0,
    'SECURITY' => [
        'LOC_FIELDS' => [
            'location_id' => [],
            'service_id' => [],
        ],
        'EMAIL_NOTIFICATION_GLOBAL' => 'CLA_STA_EMAIL_LOCS',
    ],
    'SHOW_EMAIL_GLOBAL' => 'CLA_SHOW_EMAIL',
    'EMAIL_REPORTER_GLOBAL' => 'CLA_EMAIL_REPORTER',
    'CLA_EMAIL_MGR' => 'CLA_EMAIL_MGR',
    'EMAIL_HANDLER_GLOBAL' => 'EMAIL_HANDLER_GLOBAL',
    'EMAIL_USER_PARAMETER' => 'CLA_STA_EMAIL_LOCS',

    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserCLA1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserCLA2Settings',

    'ACTION_LINK_LIST_FIELDS' => [
        ['field' => 'recordid', 'width' => '5'],
        ['field' => 'cla_mgr', 'width' => '10'],
        ['field' => 'cla_name', 'width' => '10'],
        ['field' => 'cla_dopened', 'width' => '6'],
        ['field' => 'cla_synopsis', 'width' => '30'],
    ],

    'TRAFFICLIGHTS_FIELDS' => [
        'cla_type',
    ],

    'HOME_SCREEN_STATUS_LIST' => true,

    'DATA_VALIDATION_INCLUDES' => [
        'Source/generic_modules/CLA/ModuleFunctions.php',
    ],
    'DATA_VALIDATION_FUNCTIONS' => [
        'validatePartyPercentages',
        'validateLinkedOrganisations',
        'validateQuickRefId',
    ],
    'CAN_LINK_DOCS' => true,
    'CAN_LINK_CONTACTS' => true,
    'CAN_LINK_MODULES' => true,
    'CAN_LINK_NOTES' => true,
    'CCS2_FIELDS' => [
        'cla_affecting_tier_zero',
        'cla_type_tier_one',
        'cla_type_tier_two',
        'cla_type_tier_three',
        'cla_level_intervention',
        'cla_level_harm',
    ],
    'COMBO_EXCLUDE' => ['secgroup', 'cla_unit_type'],
    // A list of all possible organisations fields that a user can search on from a CLA1 form.
    // Exact fields used in a specific installation will be set in the ORG_PAS_CHK_FIELDS global
    'CLA1_ORG_SEARCHING_FIELDS' => [
        'org_name',
        'org_reference',
        'org_address',
        'org_postcode',
        'org_tel1',
        'org_tel2',
        'org_email',
        'location_id',
        'service_id',
        'org_notes',
        'org_city',
        'org_county',
        'org_state',

    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Claims/c_dx_claims_guide.xml',
    'CONTACT_LINK_TABLE_ID' => ['link_respondents' => 'main_recordid', 'link_contacts' => 'CLA_ID'],
    'FIELDSET_MAPPINGS' => [
        'O' => 64,
        'G' => 65,
        'M' => 31,
        'A' => 32,
        'E' => 33,
        'N' => 34,
        'linked_actions' => 28,
        'payments' => 26,
    ],
    'CAN_CREATE_EMAIL_TEMPLATES' => true,

    'ACTION_TRIGGERS' => $spscDataset || $actionTriggersEnabled,

];

if ($registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue()) {
    $usClaimsFieldsToAdd = [
        'tax_id',
    ];

    $ModuleDefs['CLA']['CLA1_ORG_SEARCHING_FIELDS'] = array_merge($ModuleDefs['CLA']['CLA1_ORG_SEARCHING_FIELDS'], $usClaimsFieldsToAdd);
}

if ($registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue()) {
    $usGeneralFieldsToAdd = [
        'org_state',
    ];

    $ModuleDefs['CLA']['CLA1_ORG_SEARCHING_FIELDS'] = array_merge($ModuleDefs['CLA']['CLA1_ORG_SEARCHING_FIELDS'], $usGeneralFieldsToAdd);
}
