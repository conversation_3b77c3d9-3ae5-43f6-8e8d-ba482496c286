<?php

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfigFactory;
use src\claims\models\ClaimsFields;
use src\component\form\FormTable;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$config = (new DatixConfigFactory())->getInstance();

// variables for globals table values
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$showLastChildFirst = $config->showLastChildFirst();

$FormArray = [
    'Parameters' => [
        'Panels' => false,
        'Condition' => false,
        'ExtraContacts' => 1,
    ],
    'details' => [
        'Title' => _fdtk('claim_details', $useFormDesignLanguage),
        'Rows' => [
            'cla_ourref',
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'currentapproveobj' => $CurrentApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $data,
                'module' => $module ?: $Module,
                'perms' => $Perms,
                'approveobj' => $ApproveObj,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'cla_name',
            'cla_type',
            'cla_subtype',
            'cla_synopsis',
            'cla_dincident',
            'cla_dclaim',
            'cla_dinsurer',
            'cla_insurer',
            'cla_insurer_ref',
            'cla_itype',
            'cla_dopened',
            'cla_probability',
            'cla_estset',
            'cla_dsettled',
            'cla_dclosed',
            'cla_outcome',
            'cla_otherref',
            'cla_cspecialty',
            ['Name' => 'cla_level_intervention', 'Condition' => bYN(GetParm('CCS2_CLA', 'N'))],
            ['Name' => 'cla_level_harm', 'Condition' => bYN(GetParm('CCS2_CLA', 'N'))],
            ['Name' => 'incident_occurred_on_employer_premises', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_provided', 'Condition' => $showUsClaimsFields],
            ['Name' => 'safeguard_used', 'Condition' => $showUsClaimsFields],
            // SPSC
            ['Name' => ClaimsFields::CLA_SPECIALTY, 'Condition' => $showSpscFields],
        ],
    ],
    'locations' => [
        'Title' => _fdtk('mod_locations_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'location_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_location_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_location',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'services' => [
        'Title' => _fdtk('mod_services_title', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'service_id',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
            [
                'Name' => 'confirm_service_id',
                'NoReadOnly' => true,
                'Condition' => $showLastChildFirst,
            ],
            [
                'Name' => 'other_service',
                'NoLastChildFirst' => !$showLastChildFirst,
            ],
        ],
    ],
    'workers_comp' => [
        'Title' => _fdtk('workers_comp', $useFormDesignLanguage),
        'Condition' => $registry->getParm('SHOW_OSHA_FIELDS', 'N')->isTrue(),
        'Rows' => [
            'has_orm',
            'orm_termination_date',
        ],
    ],
    'ccs' => [
        'Title' => _fdtk('datix_ccs', $useFormDesignLanguage),
        'Rows' => [
            'cla_carestage',
            'cla_clin_detail',
            'cla_clintype',
        ],
    ],
    'ccs2' => [
        'Title' => _fdtk('datix_ccs2', $useFormDesignLanguage),
        'Condition' => bYN(GetParm('CCS2_CLA', 'N')),
        'Rows' => [
            'cla_affecting_tier_zero',
            'cla_type_tier_one',
            'cla_type_tier_two',
            'cla_type_tier_three',
        ],
    ],
    'contacts_type_R' => [
        'Title' => _fdtk('details_of_person_reporting_the', $useFormDesignLanguage) . ' ' . _fdtk('CLAName', $useFormDesignLanguage),
        'NoLazyLoad' => true,
        'NoFieldAdditions' => true,
        'module' => 'CLA',
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::REPORTER,
        'Role' => $registry->getParm('REPORTER_ROLE', 'REP')->toScalar(),
        'LinkedForms' => [ContactTypes::REPORTER => ['module' => 'CON']],
        'suffix' => 3,
        'Rows' => [],
    ],
    'contacts_type_M' => [
        'Title' => _fdtk('details_of', $useFormDesignLanguage) . ' ' . _fdtk('cla_claimant', $useFormDesignLanguage),
        'module' => 'CLA',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => [ContactTypes::CLAIMANT => ['module' => 'CON']],
        'contacttype' => ContactTypes::CLAIMANT,
        'suffix' => 1,
        'Rows' => [],
    ],
    'contacts_type_O' => [
        'Title' => _fdtk('details_of', $useFormDesignLanguage) . ' ' . _fdtk('cla_individual_respondent', $useFormDesignLanguage),
        'module' => 'CLA',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'LinkedForms' => [
            ContactTypes::INDIVIDUAL_RESPONDENT => [
                'module' => 'CON',
                'Label' => _fdtk('label_individual_respondent_form_design', $useFormDesignLanguage),
            ],
        ],
        'contacttype' => ContactTypes::INDIVIDUAL_RESPONDENT,
        'suffix' => 5,
        'Rows' => [],
    ],
    'respondents_organisation' => [
        'Title' => _fdtk('details_of_organisation_respondent', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Rows' => [],
        'LinkedForms' => ['G' => ['module' => 'ORG']],
        'ControllerAction' => [
            'MakeDynamicOrganisationSection' => [
                'controller' => src\respondents\controllers\RespondentsController::class,
            ],
        ],
    ],
    'contacts_type_A' => [
        'Title' => _fdtk('details_of_person_affected_by_the', $useFormDesignLanguage) . ' ' . _fdtk('CLAName', $useFormDesignLanguage),
        'module' => 'CLA',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::PERSON_AFFECTED,
        'suffix' => 2,
        'LinkedForms' => [ContactTypes::PERSON_AFFECTED => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'additional' => [
        'Title' => _fdtk('additional_details', $useFormDesignLanguage),
        'Rows' => [
            'show_employee',
            'show_other_contacts',
            'show_document',
        ],
    ],
    'contacts_type_E' => [
        'Title' => _fdtk('employee_plural', $useFormDesignLanguage),
        'module' => 'CLA',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::EMPLOYEE,
        'suffix' => 7,
        'LinkedForms' => [ContactTypes::EMPLOYEE => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'contacts_type_N' => [
        'Title' => _fdtk('contacts', $useFormDesignLanguage),
        'module' => 'CLA',
        'NoFieldAdditions' => true,
        'Special' => 'DynamicContact',
        'contacttype' => ContactTypes::OTHER_CONTACT,
        'suffix' => 4,
        'LinkedForms' => [ContactTypes::OTHER_CONTACT => ['module' => 'CON']],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'Condition' => !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY], true),
        'NoReadOnly' => true,
        'Special' => 'DynamicDocument',
        'Rows' => [],
    ],
    'linked_documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_PRINT,
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'causal_factor_header' => [
        'Title' => _fdtk('causal_factors_for', $useFormDesignLanguage) . ' ' . _fdtk('CLANameTitle', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            'cla_causal_factors_linked',
        ],
    ],
    'causal_factor' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType != 'Design'),
        'NoFieldAdditions' => true,
        'NoTitle' => true,
        'ControllerAction' => [
            'DoCausalFactorsSection' => [
                'controller' => src\causalfactors\controllers\CausalFactorsController::class,
            ],
        ],
        'ExtraParameters' => ['causal_factor_name' => 'cla_causal_factor'],
        'NoReadOnly' => true,
        'Rows' => [],
    ],
    'causal_factor_design' => [
        'Title' => _fdtk('causal_factors', $useFormDesignLanguage),
        'Condition' => ($FormType == 'Design'),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'AltSectionKey' => 'causal_factor',
        'NoSectionActions' => true,
        'Rows' => [
            'caf_level_1',
            'caf_level_2',
        ],
    ],
];

return $FormArray;
