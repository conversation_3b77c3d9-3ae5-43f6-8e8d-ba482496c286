<?php

use app\models\framework\modules\ModuleRepository;
use Source\generic_modules\FieldDefKeys;
use src\claims\models\ClaimsFields;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;

$investigationsIsLicensed = Container::get(ModuleRepository::class)->getModuleByCode('INV')->isEnabled();

$FieldDefs['CLA'] = [
    'recordid' => [
        'Type' => 'number',
        'Title' => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
        'Table' => 'claims_main',
    ],
    'cla_name' => [
        'Type' => 'string',
        'Title' => 'Claim name',
        'Width' => 70,
        'MaxLength' => 128,
        'data' => ['duplicate-check' => 'true'],
        'Table' => 'claims_main',
    ],
    'cla_carestage' => [
        'Type' => 'ff_select',
        'Title' => 'Stage of care',
        'Child' => 'cla_clin_detail',
        'OldCodes' => true,
        'Table' => 'claims_main',
    ],
    'cla_clin_detail' => [
        'Type' => 'ff_select',
        'Title' => 'Detail',
        'Parent' => 'cla_carestage',
        'Child' => 'cla_clintype',
        'Table' => 'claims_main',
    ],
    'cla_clintype' => [
        'Type' => 'ff_select',
        'Title' => 'Adverse event',
        'Parent' => 'cla_clin_detail',
        'Table' => 'claims_main',
    ],
    'cla_synopsis' => [
        'Type' => 'textarea',
        'Title' => 'Description of claim',
        'TitleExtra' => _fdtk('user_extra_text_inc_notes'),
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'claims_main',
    ],
    'cla_mgr' => [
        'Type' => 'ff_select',
        'Title' => 'Handler',
        'Table' => 'claims_main',
        'StaffField' => true,
    ],
    'cla_head' => [
        'Type' => 'ff_select',
        'Title' => 'Manager',
        'Table' => 'claims_main',
        'StaffField' => true,
    ],
    'cla_ourref' => [
        'Type' => 'string',
        'Title' => 'Claim form reference',
        'Width' => 32,
        'MaxLength' => 32,
        'Table' => 'claims_main',
    ],
    'cla_otherref' => [
        'Type' => 'string',
        'Title' => 'Other reference',
        'Width' => 32,
        'MaxLength' => 32,
        'Table' => 'claims_main',
    ],
    'cla_dincident' => [
        'Type' => 'date',
        'Title' => 'Incident date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_dopened' => [
        'Type' => 'date',
        'Title' => 'Opened date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_dclaim' => [
        'Type' => 'date',
        'Title' => 'Date of claim',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_dclosed' => [
        'Type' => 'date',
        'Title' => 'Closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_dsettled' => [
        'Type' => 'date',
        'Title' => 'Settled date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_dinsurer' => [
        'Type' => 'date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'cla_type' => [
        'Type' => 'ff_select',
        'Title' => 'Type',
        'Table' => 'claims_main',
    ],
    'cla_subtype' => [
        'Type' => 'ff_select',
        'Title' => 'Subtype',
        'Table' => 'claims_main',
    ],
    'cla_insurer' => [
        'Type' => 'ff_select',
        'Title' => 'Insurer',
        'BlockFromReports' => false,
        'Table' => 'claims_main',
    ],
    'cla_insurer_ref' => [
        'Type' => 'string',
        'MaxLength' => 32,
        'Title' => 'Insurer reference',
        'Table' => 'claims_main',
    ],
    'cla_itype' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type',
        'Table' => 'claims_main',
    ],
    'cla_outcome' => [
        'Type' => 'ff_select',
        'Title' => 'Outcome',
        'Table' => 'claims_main',
    ],
    'location_id' => [
        'Type' => 'tree',
        'Title' => 'Location admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'claims_main',
    ],
    'location_id_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Location admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'location_id',
    ],
    'service_id' => [
        'Type' => 'tree',
        'Title' => 'Service admitted',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'claims_main',
    ],
    'service_id_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Service admitted tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'service_id',
    ],
    'other_location' => [
        'Type' => 'tree',
        'Title' => 'Other location',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        'Table' => 'claims_main',
    ],
    'other_location_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Other location tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'other location',
    ],
    'confirm_location_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Location?',
        'NoListCol' => true,
        'Table' => 'claims_main',
    ],
    'confirm_service_id' => [
        'Type' => FieldInterface::CHECKBOX_DB,
        'Title' => 'Confirm Exact Service?',
        'NoListCol' => true,
        'Table' => 'claims_main',
    ],
    'other_service' => [
        'Type' => 'tree',
        'Title' => 'Other service',
        'requireMinChars' => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        'Table' => 'claims_main',
    ],
    'other_service_tag' => [
        'Type' => 'ff_select',
        'Title' => 'Other service tag',
        'requireMinChars' => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'other_service',
    ],
    'cla_cspecialty' => [
        'Type' => 'ff_select',
        'Title' => 'Case specialty',
        'Table' => 'claims_main',
    ],
    'cla_estset' => [
        'Type' => 'ff_select',
        'Title' => 'Estimated settlement',
        'Table' => 'claims_main',
    ],
    'cla_probability' => [
        'Type' => 'ff_select',
        'Title' => 'Probability',
        'Table' => 'claims_main',
    ],

    'cla_consequence' => [
        'Type' => 'ff_select',
        'Title' => 'Consequence',
    ],
    'cla_likelihood' => [
        'Type' => 'ff_select',
        'Title' => 'Likelihood of recurrence',
    ],
    'cla_grade' => [
        'Type' => 'ff_select',
        'Title' => 'Grade',
    ],
    ClaimsFields::CLA_GRADE_RATING => [
        'Type' => FieldInterface::NUMBER_DB,
        'Title' => 'Risk Rating',
        'Table' => 'claims_main',
    ],
    'notes' => [
        'Type' => 'textarea',
        'Title' => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        'Table' => 'notepad',
    ],
    'fin_maxliability' => [
        'Type' => 'money',
        'Title' => 'Maximum potential',
        'BlockFromReports' => true,
        'Table' => 'claims_main',
    ],
    'fin_estdamages' => [
        'Type' => 'money',
        'Title' => 'Estimated damages',
        'Table' => 'claims_main',
    ],
    'fin_estpcosts' => [
        'Type' => 'money',
        'Title' => "Estimated claimant's costs",
        'Table' => 'claims_main',
    ],
    'fin_estdefence' => [
        'Type' => 'money',
        'Title' => 'Estimated defence costs',
        'Table' => 'claims_main',
    ],
    'fin_totalclaim' => [
        'Type' => 'money',
        'Title' => 'Total',
        'Table' => 'claims_main',
    ],
    'fin_cnstliability' => [
        'Type' => 'money',
        'Title' => 'Insurance reimbursement',
        'Table' => 'claims_main',
    ],
    'fin_ourexcess' => [
        'Type' => 'number',
        'Title' => 'Our liability',
        'Table' => 'claims_main',
    ],
    'fin_ourshare' => [
        'Type' => 'decimal',
        'Title' => 'Our share %',
        'Table' => 'claims_main',
    ],
    'fin_otherparty' => [
        'Type' => 'string',
        'Title' => 'Third party (1)',
        'Table' => 'claims_main',
    ],
    'fin_thirdshare' => [
        'Type' => 'decimal',
        'Title' => 'Third party share % (1)',
        'Table' => 'claims_main',
    ],
    'fin_otherparty2' => [
        'Type' => 'string',
        'Title' => 'Third party (2)',
        'Table' => 'claims_main',
    ],
    'fin_thirdshare2' => [
        'Type' => 'decimal',
        'Title' => 'Third party share % (2)',
        'Table' => 'claims_main',
    ],
    'fin_thisyear' => [
        'Type' => 'money',
        'Title' => 'This year',
        'Table' => 'claims_main',
    ],
    'fin_nextyear' => [
        'Type' => 'money',
        'Title' => 'Next year',
        'Table' => 'claims_main',
    ],
    'fin_future' => [
        'Type' => 'money',
        'Title' => 'Future',
        'Table' => 'claims_main',
    ],
    'fin_calc_total' => [
        'Type' => 'money',
        'Title' => 'Total',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_ourshare' => [
        'Type' => 'money',
        'Title' => 'Our share',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'cla_insurer_excess' => [
        'Type' => 'money',
        'Title' => 'Insurer excess',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_liability' => [
        'Type' => 'money',
        'Title' => 'Expected cost to organisation',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_reimbursement' => [
        'Type' => 'money',
        'Title' => 'Insurer reimbursement',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_indemnity_reserve' => [
        'Type' => 'money',
        'Title' => 'Indemnity incurred',
        'Table' => 'claims_main',
    ],
    'fin_expenses_reserve' => [
        'Type' => 'money',
        'Title' => 'Expenses incurred',
        'Table' => 'claims_main',
    ],
    'indem_dclosed' => [
        'Type' => 'date',
        'Title' => 'Indemnity reserve closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'expen_dclosed' => [
        'Type' => 'date',
        'Title' => 'Expenses reserve closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'fin_medical_dclosed' => [
        'Type' => 'date',
        'Title' => 'Medical reserve closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'fin_legal_dclosed' => [
        'Type' => 'date',
        'Title' => 'Legal reserve closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'fin_temporary_indemnity_dclosed' => [
        'Type' => 'date',
        'Title' => 'Temporary indemnity closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'fin_permanent_indemnity_dclosed' => [
        'Type' => 'date',
        'Title' => 'Permanent indemnity closed date',
        'NotFuture' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_reserve_1' => [
        'Type' => 'money',
        'Title' => 'Indemnity reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_reserve_2' => [
        'Type' => 'money',
        'Title' => 'Expenses reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_medical_reserve' => [
        'Type' => 'money',
        'Title' => 'Medical reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_legal_reserve' => [
        'Type' => 'money',
        'Title' => 'Legal reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_temporary_indemnity_reserve' => [
        'Type' => 'money',
        'Title' => 'Temporary indemnity reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_permanent_indemnity_reserve' => [
        'Type' => 'money',
        'Title' => 'Permanent indemnity reserve',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_medical_incurred' => [
        'Type' => 'money',
        'Title' => 'Medical incurred',
    ],
    'fin_legal_incurred' => [
        'Type' => 'money',
        'Title' => 'Legal incurred',
    ],
    'fin_temporary_indemnity_incurred' => [
        'Type' => 'money',
        'Title' => 'Temporary indemnity incurred',
    ],
    'fin_permanent_indemnity_incurred' => [
        'Type' => 'money',
        'Title' => 'Permanent indemnity incurred',
    ],
    'fin_calc_indemnity_paid' => [
        'Type' => 'money',
        'Title' => 'Indemnity paid',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_expenses_paid' => [
        'Type' => 'money',
        'Title' => 'Expenses paid',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_medical_paid' => [
        'Type' => 'money',
        'Title' => 'Indemnity paid',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_legal_paid' => [
        'Type' => 'money',
        'Title' => 'Expenses paid',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'fin_calc_temporary_indemnity_paid' => [
        'Type' => 'money',
        'Title' => 'Indemnity paid',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_permanent_indemnity_paid' => [
        'Type' => 'money',
        'Title' => 'Expenses paid',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_indemnity_collected' => [
        'Type' => 'money',
        'Title' => 'Indemnity collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_expenses_collected' => [
        'Type' => 'money',
        'Title' => 'Expenses collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_medical_collected' => [
        'Type' => 'money',
        'Title' => 'Indemnity collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_legal_collected' => [
        'Type' => 'money',
        'Title' => 'Expenses collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_temporary_indemnity_collected' => [
        'Type' => 'money',
        'Title' => 'Indemnity collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'fin_calc_permanent_indemnity_collected' => [
        'Type' => 'money',
        'Title' => 'Expenses collected',
        'ReadOnly' => true,
        'Computed' => true,
        'Table' => 'claims_main',
    ],
    'pay_type' => [
        'Type' => 'ff_select',
        'Title' => 'Type',
        'NoListCol' => true,
        'Child' => 'pay_subtype',
    ],
    'pay_subtype' => [
        'Type' => 'ff_select',
        'Title' => 'Subtype',
        'NoListCol' => true,
        'Parent' => 'pay_type',
    ],
    'link_ndependents' => [
        'Type' => 'number',
        'Title' => 'Number of dependants',
        'Width' => 3,
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'link_agedependents' => [
        'Type' => 'string',
        'Title' => 'Age of dependants',
        'Width' => 10,
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'link_marriage' => [
        'Type' => 'ff_select',
        'Title' => 'Marital status',
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'link_plapat' => [
        'Type' => 'yesno',
        'Title' => 'Is claimant the person affected?',
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'link_legalaid' => [
        'Type' => 'yesno',
        'Title' => 'Legal aid?',
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'link_resp' => [
        'Type' => 'decimal',
        'Title' => 'Responsibility %',
        'Width' => 5,
        'MaxLength' => 10,
        'MaxValue' => 100,
        'MinValue' => 0,
        'NoListCol' => true,
        'Table' => 'link_contacts',
    ],
    'link_injuries' => [
        'Type' => 'ff_select',
        'BlockFromReports' => true,
        'Title' => 'Injuries',
        'Table' => 'link_contacts',
        'NoListCol' => true,
    ],
    'cla_causal_factors_linked' => [
        'Type' => 'yesno',
        'Title' => 'Were there any Causal factors?',
        'Table' => 'claims_main',
        'NoListCol' => true,
    ],
    // Causal factors
    'caf_level_1' => [
        'Type' => 'ff_select',
        'Title' => 'Level 1',
        'FieldFormatsName' => 'caf_level_1',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'causal_factors',
    ],
    'caf_level_2' => [
        'Type' => 'ff_select',
        'Title' => 'Level 2',
        'FieldFormatsName' => 'caf_level_2',
        'NoListCol' => true,
        'OldCodes' => true,
        'Table' => 'causal_factors',
    ],
    'cla_curstage' => [
        'Type' => 'ff_select',
        'Title' => 'Current Stage',
        'Table' => 'claims_main',
    ],

    'cla_investigator' => [
        'Type' => 'multilistbox',
        'Title' => 'Investigator',
        'Table' => 'claims_main',
        'MaxLength' => 252,
        'StaffField' => true,
    ],
    'cla_inv_outcome' => [
        'Type' => 'ff_select',
        'Title' => 'Outcome of investigation',
        'Table' => 'claims_main',
    ],
    'cla_inv_dstart' => [
        'Type' => 'date',
        'NotFuture' => true,
        'Title' => 'Date investigation started',
        'Table' => 'claims_main',
    ],
    'cla_inv_dcomp' => [
        'Type' => 'date',
        'NotFuture' => true,
        'NotEarlierThan' => ['cla_inv_dstart'],
        'Title' => 'Date investigation completed',
        'Table' => 'claims_main',
    ],
    'cla_inv_lessons' => [
        'Type' => 'textarea',
        'Title' => 'Lessons learned',
        'Table' => 'claims_main',
        'Rows' => 7,
        'Columns' => 70,
    ],
    'cla_inv_action' => [
        'Type' => 'textarea',
        'Title' => 'Action taken',
        'Rows' => 7,
        'Columns' => 70,
        'Table' => 'claims_main',
    ],
    'cla_action_code' => [
        'Type' => 'multilistbox',
        'Title' => 'Action taken codes',
        'NoListCol' => true,
        'MaxLength' => 128,
        'Table' => 'claims_main',
    ],
    'cla_lessons_code' => [
        'Type' => 'multilistbox',
        'Title' => 'Lessons learned codes',
        'NoListCol' => true,
        'MaxLength' => 128,
        'Table' => 'claims_main',
    ],
    'cla_root_causes' => [
        'Type' => 'multilistbox',
        'Title' => 'Causes',
        'NoListCol' => true,
        'MaxLength' => 248,
        'Table' => 'claims_main',
    ],
    'cla_cost' => [
        'Type' => 'money',
        'Title' => 'Cost',
        'Width' => 15,
    ],
    'cla_inquiry' => [
        'Type' => 'ff_select',
        'Title' => 'Further inquiry?',
        'Table' => 'claims_main',
    ],
    'dum_cla_grading' => [
        'Type' => 'riskregister',
        'Title' => _fdtk('risk_grading'),
        'RiskRow' => 'grading', // / Replaces Paramaters
        'NoListCol' => true,
        'MandatoryField' => 'cla_grade',
    ],
    'show_document' => [
        'Type' => 'checkbox',
        'NoListCol' => true,
        'Table' => 'claims_main',
    ],
    'show_employee' => [
        'Type' => 'checkbox',
        'Title' => _fdtk('show_employee_title'),
        'NoListCol' => true,
    ],
    'show_other_contacts' => [
        'Type' => 'checkbox',
        'Title' => _fdtk('cla_contacts_involved'),
        'NoListCol' => true,
        'Table' => 'claims_main',
    ],
    'rep_approved' => [
        'Type' => 'ff_select',
        'Title' => 'Approval status',
        'Width' => 32,
        'Table' => 'claims_main',
    ],
    'fin_calc_total_incurred' => [
        'Type' => 'money',
        'Title' => 'Total incurred',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'fin_calc_total_paid' => [
        'Type' => 'money',
        'Title' => 'Total paid',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'fin_calc_total_collected' => [
        'Type' => 'money',
        'Title' => 'Total collected',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'fin_calc_total_reserved' => [
        'Type' => 'money',
        'Title' => 'Total reserved',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'fin_calc_total_closed_date' => [
        'Type' => 'date',
        'Title' => 'Total closed date',
        'ReadOnly' => true,
        'Computed' => true,
    ],
    'osha_registered_establishment' => [
        'Type' => 'ff_select',
        'Title' => 'OSHA registered establishment',
        'Table' => 'claims_main',
    ],

    // CCS2
    'cla_affecting_tier_zero' => [
        'Type' => 'ff_select',
        'Title' => 'Incident affecting',
        'Table' => 'claims_main',
    ],
    'cla_type_tier_one' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 1',
        'Table' => 'claims_main',
    ],
    'cla_type_tier_two' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 2',
        'Table' => 'claims_main',
    ],
    'cla_type_tier_three' => [
        'Type' => 'ff_select',
        'Title' => 'Incident type tier 3',
        'Table' => 'claims_main',
    ],
    'cla_level_intervention' => [
        'Type' => 'ff_select',
        'Title' => 'Level of intervention',
        'Table' => 'claims_main',
    ],
    'cla_level_harm' => [
        'Type' => 'ff_select',
        'Title' => 'Level of harm',
        'Table' => 'claims_main',
    ],
    'initial_current' => [
        'BlockFromReports' => true,
    ],
    'cla_last_updated' => [
        'Type' => 'string',
        'Title' => 'Last updated',
        'Width' => 32,
        'Table' => 'claims_main',
        'Computed' => true,
        'NoSearch' => true,
    ],
    'flag_for_investigation' => [
        'Type' => 'ff_select',
        'Title' => 'Flag for investigation?',
        'Table' => 'claims_main',
        'NoListCol' => !$investigationsIsLicensed,
        'BlockFromReports' => !$investigationsIsLicensed,
    ],
    // ORM
    'has_orm' => [
        'Type' => 'yesno',
        'Title' => 'Ongoing responsibility for medicals (ORM)',
        'Table' => 'claims_main',
        'allowUnknownValue' => true,
    ],
    'orm_termination_date' => [
        'Type' => 'date',
        'Title' => 'ORM termination date',
        'Table' => 'claims_main',
    ],
    // OSHA
    'osha_recordable' => [
        'Type' => 'checkbox',
        'Title' => 'OSHA recordable',
        'Table' => 'claims_main',
    ],
    'osha_privacy_case' => [
        'Type' => 'checkbox',
        'Title' => 'Privacy case',
        'Table' => 'claims_main',
    ],
    'osha_case_classification' => [
        'Type' => 'ff_select',
        'Title' => 'Case classification',
        'Table' => 'claims_main',
    ],
    'osha_treated_in_er' => [
        'Type' => 'yesno',
        'Title' => 'Was employee treated in the emergency room?',
        'Table' => 'claims_main',
    ],
    'osha_hospitalized_overnight' => [
        'Type' => 'yesno',
        'Title' => 'Was the employee hospitalized overnight as an in-patient?',
        'Table' => 'claims_main',
    ],
    'osha_before_incident_description' => [
        'Type' => 'textarea',
        'Title' => 'What was the employee doing just before the incident occurred?',
        'Table' => 'claims_main',
        'Rows' => 7,
        'Columns' => 70,
    ],
    'osha_direct_harm_cause' => [
        'Type' => 'textarea',
        'Title' => 'What object or substance directly harmed the employee?',
        'Table' => 'claims_main',
        'Rows' => 7,
        'Columns' => 70,
    ],
    // EDI
    'cla_time' => [
        'Type' => 'time',
        'Title' => 'Time of claim',
        'Table' => 'claims_main',
    ],
    'edi_jurisdiction' => [
        'Type' => 'ff_select',
        'Title' => 'Jurisdiction',
        'Table' => 'claims_main',
    ],
    'edi_date_extracted' => [
        'Type' => 'date',
        'Title' => 'Date Extracted',
        'Table' => 'claims_main',
    ],
    'edi_date_processed' => [
        'Type' => 'date',
        'Title' => 'Date Processed',
        'Table' => 'claims_main',
    ],
    'edi_agency_claim_number' => [
        'Type' => 'string',
        'Title' => 'Agency Claim number',
        'Table' => 'claims_main',
    ],
    'edi_r1_froi_status' => [
        'Type' => 'ff_select',
        'Title' => 'IAIABC FROI Filing Status',
        'Table' => 'claims_main',
    ],
    'edi_froi_correction_date' => [
        'Type' => 'date',
        'Title' => 'FROI correction date',
        'Table' => 'claims_main',
    ],
    'edi_claim_status' => [
        'Type' => 'ff_select',
        'Title' => 'Claim status',
        'Table' => 'claims_main',
    ],
    'edi_cause_code' => [
        'Type' => 'ff_select',
        'Title' => 'Cause code',
        'Table' => 'claims_main',
    ],
    'edi_froi_maintenance_type' => [
        'Type' => 'ff_select',
        'Title' => 'FROI maintenance type code',
        'Table' => 'claims_main',
    ],
    'agreement_to_compensate' => [
        'Type' => 'ff_select',
        'Title' => 'Agreement to compensate code',
        'Table' => 'claims_main',
    ],
    'late_reason_code' => [
        'Type' => 'ff_select',
        'Title' => 'Late reason code',
        'Table' => 'claims_main',
    ],
    'change_element_segment' => [
        'Type' => 'ff_select',
        'Title' => 'Change data element/segment number',
        'Table' => 'claims_main',
    ],
    'change_reason' => [
        'Type' => 'ff_select',
        'Title' => 'Change reason',
        'Table' => 'claims_main',
    ],
    'cancel_reason' => [
        'Type' => 'ff_select',
        'Title' => 'Cancel reason',
        'Table' => 'claims_main',
    ],
    'number_managed_care_orgs' => [
        'Type' => 'number',
        'Title' => 'Number of managed care organisations',
        'Table' => 'claims_main',
    ],
    'number_changed_data_elements' => [
        'Type' => 'number',
        'Title' => 'Number of change data elements',
        'Table' => 'claims_main',
    ],
    'number_cancel_elements' => [
        'Type' => 'number',
        'Title' => 'Number of cancel elements',
        'Table' => 'claims_main',
    ],

    'cla_inc_ourref' => [
        'Type' => 'string',
        'Title' => 'Event reference',
        'Width' => 50,
        'MaxLength' => 32,
        'Table' => 'claims_main',
    ],
    'cla_inc_time' => [
        'Type' => 'time',
        'Title' => 'Event time',
        'Table' => 'claims_main',
    ],
    'rre_id' => [
        'Type' => 'ff_select',
        'Title' => 'Responsible Reporting Entity (RRE) ID',
        'Table' => 'claims_main',
    ],
    'cla_generated_reference_id' => [
        'Type' => 'string',
        'Title' => 'Quick reference ID',
        'Table' => 'claims_main',
    ],
    // FROI
    'incident_occurred_on_employer_premises' => [
        'Type' => 'yesno',
        'Title' => 'Did injury/illness/exposure occur on employer\'s premises?',
        'Table' => 'claims_main',
    ],
    'safeguard_provided' => [
        'Type' => 'yesno',
        'Title' => 'Safeguards provided?',
        'Table' => 'claims_main',
    ],
    'safeguard_used' => [
        'Type' => 'yesno',
        'Title' => 'Safeguards used?',
        'Table' => 'claims_main',
    ],
    // SROI
    'full_denial_effective_date' => [
        'Type' => 'date',
        'Title' => 'Full Denial Effective Date',
        'Table' => 'claims_main',
    ],
    'full_denial_reason_code' => [
        'Type' => 'multilistbox',
        'Title' => 'Full Denial Reason Code',
        'Table' => 'claims_main',
        'MaxLength' => 128,
    ],
    'denial_rescission_date' => [
        'Type' => 'date',
        'Title' => 'Denial Rescission Date',
        'Table' => 'claims_main',
    ],
    'edi_iaiabc_sroi_filing_status' => [
        'Type' => 'ff_select',
        'Title' => 'EDI IAIABC SROI Filing Status',
        'Table' => 'claims_main',
    ],
    'CLA_SAVED_QUERIES' => [
        'Type' => 'multilistbox',
        'MaxLength' => 70,
    ],
    'cla_recomm_code' => ['Title' => 'Recommendations codes', 'Type' => 'multilistbox', 'MaxLength' => 128],
    'cla_cnstref' => ['Title' => 'CNST ref.'],
    'cla_frs12_llimit' => ['Title' => 'FRS12 Lower limit'],
    'cla_frs12_lprob' => ['Title' => 'FRS12 Lower probability'],
    'cla_frs12_mlimit' => ['Title' => 'FRS12 Middle limit'],
    'cla_frs12_mprob' => ['Title' => 'FRS12 Middle probability'],
    'cla_frs12_ulimit' => ['Title' => 'FRS12 Upper limit'],
    'cla_frs12_uprob' => ['Title' => 'FRS12 Upper probability'],
    'cla_inc_category' => ['Title' => 'Category', 'BlockFromReports' => false],
    'cla_inc_subcat' => ['Title' => 'Sub category', 'BlockFromReports' => false],
    'cla_iscnst' => ['Title' => 'Insurer', 'BlockFromReports' => true],
    'cla_pasno1' => ['Title' => 'PAS No. 1'],
    'cla_pasno2' => ['Title' => 'PAS No. 2'],
    'cla_pasno3' => ['Title' => 'PAS No. 3'],
    'cla_recommend' => ['Title' => 'Recommendations'],
    'cla_unit_type' => ['Title' => 'Unit type'],
    'fin_damages' => ['Title' => 'Damages', 'BlockFromReports' => true],
    'fin_defence' => ['Title' => 'Defence costs', 'BlockFromReports' => true],
    'fin_excessband' => ['Title' => 'Excess limit'],
    'fin_plaintiff' => ['Title' => 'Claimant\'s costs', 'BlockFromReports' => true, 'Type' => 'money'],
    'fin_receipts' => ['Title' => 'Receipts'],
    'fin_totalcosts' => ['Title' => 'Total payments'],
    'fin_totalpayments' => ['Title' => 'Payments/Balance'],
    'fin_totalreserves' => ['Title' => 'Total reserves'],

    '((cla_frs12_llimit*cla_frs12_lprob+cla_frs12_mlimit*cla_frs12_mprob+cla_frs12_ulimit*cla_frs12_uprob)/100)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    '(cla_frs12_llimit+cla_frs12_mlimit+cla_frs12_ulimit)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    '(cla_frs12_llimit*cla_frs12_lprob/100)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    '(cla_frs12_lprob+cla_frs12_mprob+cla_frs12_uprob)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    '(cla_frs12_mlimit*cla_frs12_mprob/100)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    '(cla_frs12_ulimit*cla_frs12_uprob/100)' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    'secgroup' => ['BlockFromReports' => true, 'NoListCol_Override' => true],
    'seclevel' => ['BlockFromReports' => true, 'NoListCol_Override' => true],

    // contacts fields to be blocked from reports in this modules
    'link_injury1' => ['Title' => 'Injury (primary)', 'BlockFromReports' => true, 'Type' => 'ff_select'],
    'link_bodypart1' => ['Title' => 'Body part (primary)', 'BlockFromReports' => true, 'Type' => 'ff_select'],
    'death_result_injury' => ['Title' => 'Death Result of Injury Code', 'BlockFromReports' => true, 'Type' => 'yesno'],
    'permanent_impairment_percentage' => ['Title' => 'Permanent Impairment Percentage', 'BlockFromReports' => true, 'Type' => 'number'],
    'pno_type' => [
        'Type' => 'ff_select',
        'Title' => _fdtk('progress_notes_type_title'),
        'Table' => 'progress_notes',
    ],
    'link_riddor' => [
        'Type' => 'ff_select',
        'Title' => 'RIDDOR injury type',
        'NoListCol' => true,
        'Table' => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    // SPSC
    ClaimsFields::CLA_INV_LESSONS_SUB_CATEGORY => [
        'Type' => 'multilistbox',
        'Title' => 'Lesson learned sub category',
        'MaxLength' => 128,
        'Table' => 'claims_main',
    ],
    ClaimsFields::CLA_HRO_CHARACTERISTICS => [
        'Type' => 'multilistbox',
        'Title' => 'HRO characteristics',
        'MaxLength' => 128,
        'Table' => 'claims_main',
    ],
    ClaimsFields::CLA_SPECIALTY => [
        'Type' => 'ff_select',
        'Title' => 'Specialty',
        'Table' => 'claims_main',
    ],
    ClaimsFields::SOURCE_OF_RECORD => [
        'Type' => 'ff_select',
        'Title' => 'Source of record',
        'Table' => 'claims_main',
    ],
];

$PaymentCodes = DatixDBQuery::PDO_fetch_all('SELECT code, description from code_fin_type');
foreach ($PaymentCodes as $PaymentCode) {
    $FieldDefs['CLA']['pay_type_' . $PaymentCode['code']] = ['Title' => $PaymentCode['description'] . ' (Payment summary)', 'Type' => 'money'];
}
$FieldDefs['CLA']['pay_type_PAYTOTAL'] = ['Title' => 'Total (Payment summary)', 'Type' => 'money'];
