<?php

namespace Source\generic_modules;

use Exception;
use RuntimeException;

final class FieldDefKeys
{
    public const NOT_FUTURE = 'NotFuture'; // Set to true on date fields to stop dates in the future from being selected
    public const ROWS = 'Rows'; // int to determine the size of text fields. Standard 10
    public const COLUMNS = 'Columns'; // int to determine the size of text fields. Standard 70
    public const IS_TAG_FIELD = 'isTagField'; // bool to identify field is a location or service tag field
    public const MAPPER_TYPE = 'mapperType'; // used to identify where tree style codes should be retrieved from, i.e. location | service
    public const RELATED_TREE_FIELD = 'relatedTreeField'; // The main tree field associated with this tree tag field
    public const MAX_LENGTH = 'MaxLength'; // The maximum character length of input allowed in a text field
    public const TYPE = 'Type'; // The type of field, based off of FieldInterface
    public const TITLE = 'Title'; // Fieldname
    public const TABLE = 'Table'; // The table on which the field sits in the DB
    public const TREAT_EMPTY_AS_N_IN_TRIGGERS = 'TreatEmptyAsNInTriggers'; // When matching against a trigger should empty match to `N` for dropdown behaviours?
    public const ALWAYS_INCLUDE_ON_FORM = 'AlwaysIncludeOnForm'; // We always want this field to be included on the form, even if it's been hidden by form design
    public const COMPUTED = 'Computed';
    public const NOT_EARLIER_THAN = 'NotEarlierThan';
    public const READ_ONLY = 'ReadOnly';
    public const ALWAYS_MANDATORY = 'AlwaysMandatory';
    public const READ_ONLY_AND_MANDATORY = 'ReadOnlyAndMandatory';
    public const CUSTOM_ERROR_MESSAGE = 'CustomErrorMessage';
    public const NO_HIDE = 'NoHide';
    public const NO_READ_ONLY = 'NoReadOnly';
    public const LOOKUP_FUNCTION = 'LookupFunction';
    public const NO_DEFAULT = 'NoDefault';
    public const REQUIRE_MIN_CHARS = 'requireMinChars';
    public const ONE_TO_MANY_TABLE = 'oneToManyTable';
    public const NO_SEARCH = 'NoSearch';
    public const NO_REPORT = 'BlockFromReports';
    public const COLUMNS_DEFAULT = 70;
    public const MAX_LENGTH_DEFAULT = 64;
    public const NO_MANDATORY = 'NoMandatory';
    public const NO_LIST_COL = 'NoListCol';
    public const NO_ORDER = 'NoOrder';
    public const RESEND_LOCAL_AUTH_EMAIL = 'ResendReferral';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        throw new RuntimeException("Can't get an instance of FieldDefKeys");
    }
}
