<?php

declare(strict_types=1);

namespace Source\generic_modules;

use Exception;
use RuntimeException;

final class BasicFormKeys
{
    public const TITLE = 'Title';
    public const CONDITION = 'Condition';
    public const SPECIAL = 'Special';
    public const NO_FIELD_ADDITIONS = 'NoFieldAdditions';
    public const NO_FIELD_REMOVALS = 'NoFieldRemoval';
    public const ROWS = 'Rows';
    public const SHOW_HISTORY = 'ShowHistory';
    public const SHOW_HISTORY_SEARCH = 'ShowHistorySearch';
    public const TABULAR_ROWS = 'TabularRows';
    public const TABLE_SECTION_HEADER = 'TableSectionHeader';
    public const MODULE = 'module';
    public const NO_READ_ONLY = 'NoReadOnly';

    /**
     * @throws Exception
     */
    private function __construct()
    {
        throw new RuntimeException("Can't get an instance of BasicFormKeys");
    }
}
