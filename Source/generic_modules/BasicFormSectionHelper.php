<?php

declare(strict_types=1);

namespace Source\generic_modules;

use app\models\contact\ContactTypes;
use app\models\generic\valueObjects\Module;
use src\actionchains\controllers\ActionChainController;
use src\component\form\FormTable;
use src\contacts\controllers\ContactsController;
use src\contacts\controllers\SearchCriteriaController;
use src\framework\registry\Registry;
use src\generic\fields\LearningsFields;
use src\reasons\controllers\ReasonsController;
use src\wordmergetemplate\controllers\WordMergeTemplateController;

class BasicFormSectionHelper
{
    /** @var string */
    protected $formMode;

    /** @var bool */
    protected $useFormDesignLanguage;

    /** @var Registry */
    protected $registry;

    public function __construct(string $formMode, bool $useFormDesignLanguage, Registry $registry)
    {
        $this->formMode = $formMode;
        $this->useFormDesignLanguage = $useFormDesignLanguage;
        $this->registry = $registry;
    }

    public function createContactSection(string $module, string $type): array
    {
        $section = [
            'Title' => _fdtk("table_link_contacts_{$module}_{$type}", $this->useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'Listings' => ["contacts_type_{$type}" => ['module' => Module::CONTACTS]],
            'LinkedForms' => [$type => ['module' => Module::CONTACTS]],
            'Rows' => [],
        ];

        if ($this->formMode === FormTable::MODE_SEARCH) {
            $section['LinkedDataSection'] = true;
            $section['ControllerAction'] = [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ];
            $section['ExtraParameters'] = [
                'link_type' => $type,
                'linkModule' => MODULE::CONTACTS,
                'module' => $module,
                'sectionId' => "contacts_type_{$type}",
            ];
        } else {
            $section['ControllerAction'] = [
                'ListLinkedContacts' => [
                    'controller' => ContactsController::class,
                ],
            ];
            $section['ExtraParameters'] = ['link_type' => $type];
            $section['NotModes'] = [FormTable::MODE_NEW, FormTable::MODE_SEARCH, FormTable::MODE_LINKED_DATA_SEARCH];
        }

        return $section;
    }

    public function createLevel1ContactSection(string $module, string $type): array
    {
        $suffixMapping = [
            ContactTypes::REPORTER => 3,
            ContactTypes::PERSON_AFFECTED => 6,
            ContactTypes::OTHER_CONTACT => 4,
            ContactTypes::EMPLOYEE => 7,
            ContactTypes::WITNESS => 8,
        ];

        return [
            'Title' => _fdtk("table_link_contacts_{$module}_{$type}", $this->useFormDesignLanguage),
            'module' => $module,
            'NoFieldAdditions' => true,
            'Special' => 'DynamicContact',
            'contacttype' => $type,
            'suffix' => $suffixMapping[$type],
            'LinkedForms' => [
                $type => [
                    'module' => Module::CONTACTS,
                ],
            ],
            'NoReadOnly' => true,
            'Rows' => [],
        ];
    }

    public function createActionsSection(string $module): array
    {
        // BASE SECTION SETUP
        $section = [
            'Title' => _fdtk('actions', $this->useFormDesignLanguage),
            'NoFiledAdditions' => true,
            'Rows' => [],
            'NotModes' => [FormTable::MODE_NEW],
        ];

        // SECTION SETUP FOR SEARCH
        if ($this->formMode === FormTable::MODE_SEARCH) {
            $section['LinkedDataSection'] = true;
            $section['ControllerAction'] = [
                'generateSearchCriteria' => [
                    'controller' => SearchCriteriaController::class,
                ],
            ];
            $section['ExtraParameters'] = ['linkModule' => Module::ACTIONS, 'module' => $module, 'sectionId' => 'linked_actions', 'link_type' => 'linked_actions'];
            $section['LinkedForms'] = ['linked_actions' => ['module' => Module::ACTIONS]];
        // SECTION SETUP FOR INPUT
        } else {
            $section['Special'] = 'LinkedActions';
            $section['LinkedForms'] = ['linked_actions' => ['module' => Module::ACTIONS, 'carltonFormDesigns' => true]];
            $section['Condition'] = $this->formMode !== FormTable::MODE_LINKED_DATA_SEARCH;
        }

        return $section;
    }

    public function createProgressNotesSection(): array
    {
        return [
            'Title' => _fdtk('table_progress_notes', $this->useFormDesignLanguage),
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'Special' => 'ProgressNotes',
            'NoFieldAdditions' => true,
            'Rows' => [],
        ];
    }

    public function createDocumentTemplatesSection(): array
    {
        return [
            'Title' => _fdtk('mod_templates_title', $this->useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'NoReadOnly' => true,
            'ControllerAction' => [
                'wordmergesection' => [
                    'controller' => WordMergeTemplateController::class,
                ],
            ],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH, FormTable::MODE_PRINT],
            'Rows' => [],
        ];
    }

    public function createActionPlanSection(): array
    {
        return [
            'Title' => _fdtk('action_chains', $this->useFormDesignLanguage),
            'LinkedDataSection' => true,
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'getActionChains' => [
                    'controller' => ActionChainController::class,
                ],
            ],
            'LinkedForms' => ['action_chains' => ['module' => Module::ACTIONS]],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'Rows' => [],
        ];
    }

    public function createLearningSection(?array $data): array
    {
        return [
            'Title' => _fdtk('learnings', $this->useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'NoFieldRemoval' => true,
            'Rows' => [
                [
                    'Name' => LearningsFields::LEARNINGS_TO_SHARE,
                    'NoHide' => true,
                    'NoOrder' => true,
                    'ReadOnly' => $data['learnings_to_share'] === 'Y',

                ],
                [
                    'Name' => LearningsFields::LEARNINGS_TITLE,
                    'NoHide' => true,
                    'NoOrder' => true,
                    'ReadOnly' => $data['learnings_to_share'] === 'Y',
                    'NoMandatory' => true,
                ],
                [
                    'Name' => LearningsFields::KEY_LEARNINGS,
                    'NoHide' => true,
                    'NoOrder' => true,
                    'ReadOnly' => $data['learnings_to_share'] === 'Y',
                    'NoMandatory' => true,
                ],
            ],
        ];
    }

    public function createReasonsForRejectionSection(string $module, bool $sectionEditable): array
    {
        return [
            'Condition' => $this->registry->getParm('REJECT_REASON', 'Y')->toBool(),
            'Title' => _fdtk('details_rejection', $this->useFormDesignLanguage),
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'NoReadOnly' => true,
            'NoFieldRemoval' => true,
            'Rows' => [
                [
                    'Name' => 'rea_dlogged',
                    'Title' => _fdtk('rea_dlogged', $this->useFormDesignLanguage),
                    'ReadOnly' => true,
                    'Type' => 'date',
                    'Module' => $module,
                ],
                [
                    'Name' => 'rea_con_name',
                    'Title' => _fdtk('rea_con_name', $this->useFormDesignLanguage),
                    'ReadOnly' => true,
                    'Type' => 'string',
                    'Module' => $module,
                ],
                [
                    'Name' => 'rea_code',
                    'Title' => _fdtk('rea_code', $this->useFormDesignLanguage),
                    'EditableWhenReadonly' => $sectionEditable,
                    'Type' => 'ff_select',
                    'NoReadOnly' => true,
                    'Module' => $module,
                ],
                [
                    'Name' => 'rea_text',
                    'Title' => _fdtk('rea_text', $this->useFormDesignLanguage),
                    'EditableWhenReadonly' => $sectionEditable,
                    'Type' => 'textarea',
                    'Rows' => 10,
                    'Columns' => 70,
                    'NoReadOnly' => true,
                    'Module' => $module,
                ],
            ],
        ];
    }

    public function createReasonsForRejectionHistorySection(): array
    {
        return [
            'Title' => _fdtk('reasons_history_title', $this->useFormDesignLanguage),
            'NoFieldAdditions' => true,
            'ControllerAction' => [
                'SectionRejectionHistory' => [
                    'controller' => ReasonsController::class,
                ],
            ],
            'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
            'Rows' => [],
        ];
    }

    public function addLinkedModuleListings(array $formArray): array
    {
        $moduleDefs = $this->registry->getModuleDefs();

        foreach ($moduleDefs->getModulesThatCanLinkRecords() as $linkModule => $name) {
            $linkModuleDef = $moduleDefs->getModuleData($linkModule);
            $formArray['linked_records']['Listings'][$linkModule] = [
                'module' => $linkModule,
                'Label' => 'Select a listing to use for the list of ' . $linkModuleDef['REC_NAME_PLURAL'],
            ];
        }

        return $formArray;
    }
}
