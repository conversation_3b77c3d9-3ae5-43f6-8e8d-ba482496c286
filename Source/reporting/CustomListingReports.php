<?php

use app\models\framework\config\DatixConfig;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use src\security\Escaper;
use src\security\CompatEscaper;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

function report1($where)
{
    global $dtxtitle;

    LoggedIn();

    $addMinExtension = Container::get(DatixConfig::class)->isMinifierOn() ? '.min' : '';

    $WhereClause = $where;

    $sql = 'SELECT code, description, cause_group
        FROM code_inc_cause
        ORDER BY cause_group, code';
    $request = db_query($sql);

    while ($row = db_fetch_array($request)) {
        $root_causes[$row['cause_group']][$row['code']] = $row['description'];
    }

    $root_cause_groups = [
        '1' => [
            'descr' => 'Identification of higher risk',
            'notes' => 'Use this section to identify reasons why a higher risk patient or procedure was not identified or treated as such.',
        ],
        '2' => [
            'descr' => 'Management of higher risks',
            'notes' => 'Use this section to identify any shortcomings there may have been in the process of matching a higher risk patient or procedure to an appropriately skilled clinician.',
        ],
        '3' => [
            'descr' => 'Guidelines or protocols',
            'notes' => 'Use this section to identify any shortcomings in the use of guidelines or protocols that may have been implicated in the ' . _fdtk('INCName') . '.',
        ],
        '4' => [
            'descr' => 'Credentialling: skills deficit not identified in the course of',
            'notes' => 'Use this section where staff did not exercise skills required of their grade and the skills deficit was not identified/remedied during the process which has most recently been applied',
        ],
        '5' => [
            'descr' => 'Team factors',
            'notes' => 'Use this section to identify any shortcomings in teamwork that may have been implicated in the ' . _fdtk('INCName') . '.',
        ],
        '6' => [
            'descr' => 'Work environment/resources',
            'notes' => 'Use this section to identify any shortcomings in the work environment or available resources that may have been implicated in the ' . _fdtk('INCName'),
        ],
    ];

    $sql = 'SELECT code, description
        FROM code_inc_severity';

    $request = db_query($sql);

    while ($sev = db_fetch_array($request)) {
        $code_severity[$sev['code']] = $sev['description'];
    }

    $sql = 'SELECT recordid, inc_name, inc_notes, inc_severity,
    inc_root_causes
    FROM incidents_main';

    if ($WhereClause != '') {
        $sql .= " WHERE ({$WhereClause})";
    }

    $sql .= '
    ORDER BY inc_name ASC';

    $request = db_query($sql);

    $dtxtitle = 'Datix Listing with Description and Correctable Cause Analysis';

    ob_start('output_handler_listingreport'); ?>
<table class="bordercolor" cellspacing="1" cellpadding="4" width="100%" align="center" bgcolor="#6394bd" border="0" id="listingtable">
    <tr>
        <td style="text-align: center; font-size: 20px; font-weight: bold;">
            <?php if ($_POST['title'] != null) {
                echo Escaper::escapeForHTML($_POST['title']);
            } else {
                echo $dtxtitle;
            } ?>
        </td>
    </tr>
<?php
            while ($inc = db_fetch_array($request)) {
                $causes = explode(' ', $inc['inc_root_causes']);
                sort($causes, SORT_NUMERIC); ?>
<tr>
    <td class="titlebg" width="100%">
    ID: <?php echo $inc['recordid'] . ' ' . $inc['inc_name']; ?>
    </td>
</tr>
<tr>
    <td class="windowbg2" width="100%">
    Severity: <?php echo $code_severity[$inc['inc_severity']]; ?>
    <br />
    <br />
    Description:
    <br />
    <br />
    <?php echo CompatEscaper::encodeCharacters($inc['inc_notes']); ?>
    <br />
    <br />
    Correctable Causes:
    <br />
    <br />
<?php
                // Print out the root causes, grouped by root_cause_groups.
                $last_group = '';
                if (is_array($causes)) {
                    foreach ($causes as $cause) {
                        $rc = explode('.', $cause);
                        $current_group = $rc[0];
                        if ($root_cause_groups[$current_group] && $current_group != $last_group) {
                            echo "<br /><i>{$current_group} "
                            . $root_cause_groups[$current_group]['descr']
                            . '</i><br /><br />';
                        }
                        if ($cause) {
                            echo $root_causes[$current_group][$cause] . '<br />';
                        }
                        $last_group = $current_group;
                    }
                } ?>
    </td>
</tr>

<?php
            }
            ob_end_flush(); ?>
    <script language="JavaScript" type="text/javascript" src="js_functions/FloatingWindowClass<?php echo $addMinExtension; ?>.js"></script>
    <script language="JavaScript" type="text/javascript">
    function ListingHTML2pdf() {
        document.listing.action = '<?php echo "{$scripturl}?action=exportswitch"; ?>';
        document.listing.target = '_self';
        document.listing.submit();
    }
    </script>
    <tr>
        <td class="windowbg2" align="center">
            <form method="post" name="listing" action="<?php echo "{$scripturl}?action=reportdesigner&module=INC"; ?>">
            <input type="hidden" value="INC" name="module" />
            <input type="hidden" id="orientation_post" name="orientation_post" value="P" />
            <input type="hidden" id="papersize_post" name="papersize_post" value="a4" />
            <input type="hidden" id="exportmode" name="exportmode" value="listing" />
            <input type="hidden" id="reportoutputformat" name="reportoutputformat" value="pdf" />
            <input type="hidden" id="saved_query" name="saved_query" value="<?php echo Sanitize::SanitizeInt($_REQUEST['saved_query']); ?>" />
            <input type="hidden" id="crosstabtype" name="crosstabtype" value="listing" />
            <input type="hidden" id="report" name="report" value="report1" />
            <?php
                    if (!Container::get(Registry::class)->getDeviceDetector()->isTablet()) {
                        $ExportButtonLink = "{$scripturl}?action=httprequest&type=exporttopdfoptions&noexcel=1&nocsv=1"; ?>
                <input type="button" onclick="
                    var buttons = new Array();
                    buttons[0]={'value':'<?php echo _fdtk('btn_export'); ?>','onclick':'if(setReturns(1)){GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();ListingHTML2pdf();}'};
                    buttons[1]={'value':'<?php echo _fdtk('btn_cancel'); ?>','onclick':'GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();'};

                    PopupDivFromURL('exporttopdfoptions', 'Export', '<?php echo $ExportButtonLink; ?>', '', buttons, '');" value="Export"/>
            <?php
                    } ?>
            </form>
        </td>
    </tr>
    <?php
    echo '</table>';
}

function report3($where)
{
    global $dtxtitle;

    LoggedIn();

    $addMinExtension = Container::get(DatixConfig::class)->isMinifierOn() ? '.min' : '';

    $WhereClause = $where;

    // Get severity codes and descriptions

    $sql = 'SELECT code, description, listorder
        FROM code_inc_severity
        ORDER BY listorder ASC';

    $request = db_query($sql);

    while ($sev = db_fetch_array($request)) {
        $code_severity[$sev['code']] = $sev['description'];
    }


    $sql = 'SELECT code, description, cause_group
        FROM code_inc_cause
        ORDER BY cause_group, code';
    $request = db_query($sql);

    while ($row = db_fetch_array($request)) {
        $root_causes[$row['cause_group']][$row['code']] = $row['description'];
    }

    $root_cause_groups = [
        '1' => [
            'descr' => 'Identification of higher risk',
            'notes' => 'Use this section to identify reasons why a higher risk patient or procedure was not identified or treated as such.',
        ],
        '2' => [
            'descr' => 'Management of higher risks',
            'notes' => 'Use this section to identify any shortcomings there may have been in the process of matching a higher risk patient or procedure to an appropriately skilled clinician.',
        ],
        '3' => [
            'descr' => 'Guidelines or protocols',
            'notes' => 'Use this section to identify any shortcomings in the use of guidelines or protocols that may have been implicated in the ' . _fdtk('INCName') . '.',
        ],
        '4' => [
            'descr' => 'Credentialling: skills deficit not identified in the course of',
            'notes' => 'Use this section where staff did not exercise skills required of their grade and the skills deficit was not identified/remedied during the process which has most recently been applied',
        ],
        '5' => [
            'descr' => 'Team factors',
            'notes' => 'Use this section to identify any shortcomings in teamwork that may have been implicated in the ' . _fdtk('INCName') . '.',
        ],
        '6' => [
            'descr' => 'Work environment/resources',
            'notes' => 'Use this section to identify any shortcomings in the work environment or available resources that may have been implicated in the ' . _fdtk('INCName'),
        ],
    ];

    // Get incidents

    $sql = 'SELECT recordid, inc_notes, inc_name, inc_severity,
        inc_root_causes
        FROM
        incidents_main';

    if ($WhereClause) {
        $sql .= " WHERE ({$WhereClause})";
    }

    $request = db_query($sql);

    // Loop through the incidents, ordering first by directorate, then by root causes

    while ($inc = db_fetch_array($request)) {
        $causes = explode(' ', $inc['inc_root_causes']);

        if (is_array($causes)) {
            sort($causes, SORT_STRING); // root causes can be chars

            foreach ($causes as $cause) {
                if ($cause != '' && $inc['inc_severity'] != '') {
                    $rc = explode('.', $cause);
                    $incident_list[$rc[0]][$cause][$inc['inc_severity']] = $inc;
                }
            }
        }
    }

    // Now output incidents by directorate.
    ob_start('output_handler_listingreport'); ?>
<table class="bordercolor" cellspacing="1" cellpadding="4" width="100%" align="center" border="0" id="listingtable">
    <tr>
        <td style="text-align: center; font-size: 20px; font-weight: bold;">
            <?php if ($_POST['title'] != null) {
                echo Escaper::escapeForHTML($_POST['title']);
            } else {
                echo 'Incidents grouped by correctable cause and severity';
            } ?>
        </td>
    </tr>
<?php
            if (is_array($incident_list)) {
                foreach ($incident_list as $cause_group => $inc_cause) {
                    ?>
<tr>
    <td class="titlebg" width="100%">
    <?php echo $cause_group . '. ' . $root_cause_groups[$cause_group]['descr']; ?>
    </td>
</tr>
<tr>
    <td class="windowbg2">
    <table class="bordercolor" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
<tr>
    <td class="windowbg2" width="100%">
<?php
                    if (is_array($inc_cause)) {
                        foreach ($inc_cause as $cause_code => $inc_sev) {
                            ?>
<tr>
    <td class="titlebg">
    Correctable cause: <?php echo $root_causes[$cause_group][$cause_code]; ?>
    </td>
</tr>
<?php
                            if (is_array($inc_sev)) {
                                foreach ($inc_sev as $incident) {
                                    ?>
<tr>
<td class="windowbg2">
Severity: <?php echo $code_severity[$incident['inc_severity']]; ?>.  <?php echo $incident['inc_name']; ?> ID: <?php echo $incident['recordid']; ?>
<br />
<br />
<?php echo $incident['inc_notes']; ?>
<br />
<br />
</td></tr>
<?php
                                }
                            }
                        }
                    }

                    echo '</td> </tr> </table>'; // End directorate
                    echo '</td></tr>';
                }
            }
            ob_end_flush(); ?>
    <script language="JavaScript" type="text/javascript" src="js_functions/FloatingWindowClass<?php echo $addMinExtension; ?>.js"></script>
    <script language="JavaScript" type="text/javascript">
    function ListingHTML2pdf() {
        document.listing.action = '<?php echo "{$scripturl}?action=exportswitch"; ?>';
        document.listing.target = '_self';
        document.listing.submit();
    }

    function backToReportMenu() {
        document.listing.action = '<?php echo "{$scripturl}?action=reportdesigner&module=INC"; ?>';
        document.listing.target = '_self';
        document.listing.submit();
    }
    </script>
    <tr>
        <td class="windowbg2" align="center">
            <form method="post" name="listing" action="<?php echo "{$scripturl}?action=reportdesigner&module=INC"; ?>">
            <input type="hidden" value="INC" name="module" />
            <input type="hidden" id="orientation_post" name="orientation_post" value="P" />
            <input type="hidden" id="papersize_post" name="papersize_post" value="a4" />
            <input type="hidden" id="exportmode" name="exportmode" value="listing" />
            <input type="hidden" id="reportoutputformat" name="reportoutputformat" value="pdf" />
            <input type="hidden" id="saved_query" name="saved_query" value="<?php echo Sanitize::SanitizeInt($_REQUEST['saved_query']); ?>" />
            <input type="hidden" id="crosstabtype" name="crosstabtype" value="listing" />
            <input type="hidden" id="report" name="report" value="report3" />
            <?php
                    if (!Container::get(Registry::class)->getDeviceDetector()->isTablet()) {
                        $ExportButtonLink = "{$scripturl}?action=httprequest&type=exporttopdfoptions&noexcel=1&nocsv=1"; ?>
            <input type="button" onclick="
                    var buttons = new Array();
                    buttons[0]={'value':'<?php echo _fdtk('btn_export'); ?>','onclick':'if(setReturns(1)){GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();ListingHTML2pdf();}'};
                    buttons[1]={'value':'<?php echo _fdtk('btn_cancel'); ?>','onclick':'GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();'};

                    PopupDivFromURL('exporttopdfoptions', 'Export', '<?php echo $ExportButtonLink; ?>', '', buttons, '');" value="Export"/>
            <?php
                    } ?>
            </form>
        </td>
    </tr>
</table>
<?php
}

/**
 * Custom claims listing report.
 */
function report7($where)
{
    global $ModuleDefs, $dtxtitle;

    $_SESSION['listing']['whereclause'] = $where;

    $sql = '
    SELECT claims_main.recordid as cla_id, claims_main.cla_name, claims_main.cla_synopsis, claims_main.cla_dopened, claims_main.cla_dclosed, claims_main.cla_ourref,
(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN \'CLA\'
    WHEN inc_id is not null and inc_id != 0 THEN \'INC\'
    WHEN com_id is not null and com_id != 0 THEN \'COM\'
    ELSE null END) as module,

(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN cla_id2
    WHEN inc_id is not null and inc_id != 0 THEN inc_id
    WHEN com_id is not null and com_id != 0 THEN com_id
    ELSE null END) as record_id,

(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN claims_link.cla_name
    WHEN inc_id is not null and inc_id != 0 THEN inc_name
    WHEN com_id is not null and com_id != 0 THEN com_name
    ELSE null END) as record_name,

(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN claims_link.cla_ourref
    WHEN inc_id is not null and inc_id != 0 THEN inc_ourref
    WHEN com_id is not null and com_id != 0 THEN com_ourref
    ELSE null END) as record_ourref,

(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN claims_link.cla_dopened
    WHEN inc_id is not null and inc_id != 0 THEN inc_dopened
    WHEN com_id is not null and com_id != 0 THEN com_dopened
    ELSE null END) as record_dopened,

(CASE
    WHEN cla_id2 is not null and cla_id2 != 0 THEN claims_link.cla_dclosed
    WHEN inc_id is not null and inc_id != 0 THEN inc_dsched
    WHEN com_id is not null and com_id != 0 THEN com_dclosed
    ELSE null END) as record_dclosed


  FROM (
     SELECT cla_id, NULL as cla_id2, inc_id, com_id FROM link_modules
     UNION ALL
         (
    SELECT lnk_id2 as cla_id2, lnk_id1 as cla_id, NULL as inc_id, NULL as com_id FROM links JOIN claims_main c2 ON lnk_id2 = c2.recordid WHERE lnk_mod1 = \'CLA\' AND lnk_mod2 = \'CLA\'
    UNION ALL
    SELECT lnk_id1 as cla_id2, lnk_id2 as cla_id, NULL as inc_id, NULL as com_id FROM links JOIN claims_main c2 ON lnk_id1 = c2.recordid WHERE lnk_mod1 = \'CLA\' AND lnk_mod2 = \'CLA\'
    )
    ) link_mod


        LEFT JOIN claims_main ON claims_main.recordid = link_mod.cla_id

        LEFT JOIN claims_main claims_link ON link_mod.cla_id2 = claims_link.recordid

        LEFT JOIN incidents_main ON incidents_main.recordid = link_mod.inc_id
        LEFT JOIN compl_main ON compl_main.recordid = link_mod.com_id
        WHERE
        link_mod.cla_id IS NOT NULL AND link_mod.cla_id != 0 and
        (
        link_mod.inc_id is not null and link_mod.inc_id != 0 or
        link_mod.com_id is not null and link_mod.com_id != 0 or
        link_mod.cla_id2 is not null and link_mod.cla_id2 != 0
        )' .
        ($where ? ' AND (' . $where . ')' : '') . '
        ORDER BY cla_id, module';

    $Records = DatixDBQuery::PDO_fetch_all($sql);

    $CurrentClaimID = 0;
    $CurrentLinkedModule = '';
    $OpenTable = false;

    // Output the column headers
    ini_set('include_path', ini_get('include_path') . ';../Classes/');
    $objPHPExcel = new Spreadsheet();

    $objPHPExcel->getProperties()->setTitle('DatixWeb Excel export');
    $objPHPExcel->getProperties()->setSubject('DatixWeb Excel export');
    $objPHPExcel->getProperties()->setDescription('DatixWeb Excel export');

    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setTitle('Analysis of claims links');

    $i = 2;
    $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(70);
    $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(10);
    $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(12);
    $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(12);

    $objPHPExcel->getActiveSheet()->getCell('A1')->setValue('Name');
    $objPHPExcel->getActiveSheet()->getStyle('A1')->getFont()->setBold(true);

    $objPHPExcel->getActiveSheet()->getCell('B1')->setValue('Our ref');
    $objPHPExcel->getActiveSheet()->getStyle('B1')->getFont()->setBold(true);

    $objPHPExcel->getActiveSheet()->getCell('C1')->setValue('Opened');
    $objPHPExcel->getActiveSheet()->getStyle('C1')->getFont()->setBold(true);

    $objPHPExcel->getActiveSheet()->getCell('D1')->setValue('Closed');
    $objPHPExcel->getActiveSheet()->getStyle('D1')->getFont()->setBold(true);

    $HTML = '<table class="bordercolor" cellspacing="1" cellpadding="4" width="100%" align="center" bgcolor="#6394bd" border="0" id="listingtable">';

    if ($_POST['title'] == null || $_POST['title'] == '') {
        $HTML .= '<tr>
                    <td style="text-align: center; font-size: 20px; font-weight: bold;" colspan = "4">
                        Claims - Analysis of claims links report
                    </td>
                </tr>';
    } else {
        $HTML .= '<tr>
                    <td style="text-align: center; font-size: 20px; font-weight: bold;" colspan = "4">  '
                        . Escaper::escapeForHTML($_POST['title']) . '
                    </td>
                </tr>';
    }


    $HTML .= '<tr><th>Name</th><th>Our ref</th><th>Opened</th><th>Closed</th></tr>';

    foreach ($Records as $Record) {
        if ($Record['cla_id'] != $CurrentClaimID) {
            $claim_url = getRecordURL(['module' => 'CLA', 'recordid' => $Record['cla_id']]) . '&fromlisting=report7';
            $HTML .= '<tr style="background-color:#C7CCE0">
                <td><a href="' . $claim_url . '">' . $Record['cla_name'] . '</a></td>
                <td><a href="' . $claim_url . '">' . $Record['cla_ourref'] . '</a></td>
                <td><a href="' . $claim_url . '">' . formatDateForDisplay($Record['cla_dopened']) . '</a></td>
                <td><a href="' . $claim_url . '">' . formatDateForDisplay($Record['cla_dclosed']) . '</a></td></tr>';
            $HTML .= '<tr><td colspan="4"><a href="' . $claim_url . '">' . $Record['cla_synopsis'] . '</a></td></tr>';

            $CurrentClaimID = $Record['cla_id'];
            $CurrentLinkedModule = '';

            $objPHPExcel->getActiveSheet()->getCell("A{$i}")->setValue($Record['cla_name']);
            $objPHPExcel->getActiveSheet()->getCell("B{$i}")->setValue($Record['cla_ourref']);
            $objPHPExcel->getActiveSheet()->getCell("C{$i}")->setValue(formatDateForDisplay($Record['cla_dopened']));
            $objPHPExcel->getActiveSheet()->getCell("D{$i}")->setValue(formatDateForDisplay($Record['cla_dclosed']));

            $styleArray = [
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'C7CCE0'],
                ],
                'alignment' => [
                    'wrapText' => true,
                ],
            ];

            $objPHPExcel->getActiveSheet()->getStyle("A{$i}")->applyFromArray($styleArray);
            $objPHPExcel->getActiveSheet()->getStyle("B{$i}")->applyFromArray($styleArray);
            $objPHPExcel->getActiveSheet()->getStyle("C{$i}")->applyFromArray($styleArray);
            $objPHPExcel->getActiveSheet()->getStyle("D{$i}")->applyFromArray($styleArray);
            ++$i;

            $styleArray = [
                'alignment' => [
                    'wrapText' => true,
                ],
            ];

            $objPHPExcel->getActiveSheet()->getCell("A{$i}")->setValue($Record['cla_synopsis']);
            $objPHPExcel->getActiveSheet()->getStyle("A{$i}")->applyFromArray($styleArray);
            $objPHPExcel->getActiveSheet()->mergeCells("A{$i}:D{$i}");
            ++$i;
        }

        if ($Record['module'] != $CurrentLinkedModule) {
            $HTML .= '<tr><td>' . $ModuleDefs[$Record['module']]['NAME'] . '</td><td></td><td></td><td></td></tr>';

            $CurrentLinkedModule = $Record['module'];

            $objPHPExcel->getActiveSheet()->getCell("A{$i}")->setValue($ModuleDefs[$Record['module']]['NAME']);
            $objPHPExcel->getActiveSheet()->getStyle("A{$i}")->getFont()->setBold(true);
            ++$i;
        }

        $record_url = getRecordURL(['module' => $Record['module'], 'recordid' => $Record['record_id']]) . '&fromlisting=report7';
        $HTML .= '<tr>
            <td><a href="' . $record_url . '">' . $Record['record_name'] . '</a></td>
            <td><a href="' . $record_url . '">' . $Record['record_ourref'] . '</a></td>
            <td><a href="' . $record_url . '">' . formatDateForDisplay($Record['record_dopened']) . '</a></td>
            <td><a href="' . $record_url . '">' . formatDateForDisplay($Record['record_dclosed']) . '</a></td></tr>';

        $objPHPExcel->getActiveSheet()->getCell("A{$i}")->setValue($Record['record_name']);
        $objPHPExcel->getActiveSheet()->getCell("B{$i}")->setValue($Record['record_ourref']);
        $objPHPExcel->getActiveSheet()->getCell("C{$i}")->setValue(formatDateForDisplay($Record['record_dopened']));
        $objPHPExcel->getActiveSheet()->getCell("D{$i}")->setValue(formatDateForDisplay($Record['record_dclosed']));
        ++$i;
    }

    $HTML .= '</table>';

    $objPHPExcel->getActiveSheet()->getStyle('A1:D' . --$i)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

    // Store PHPExcel object in session
    $_SESSION['crosstabtablePHPExcel'] = serialize($objPHPExcel);
    $objPHPExcel = '';



    ob_start('output_handler_listingreport');

    echo $HTML;

    ob_end_flush(); ?>

    <script language="JavaScript" type="text/javascript">
        function ListingHTML2pdf() {
            document.listing.action = '<?php echo "{$scripturl}?action=exportswitch"; ?>';
            document.listing.target = '_self';
            document.listing.submit();
        }

        function backToReportMenu() {
            document.listing.action = '<?php echo "{$scripturl}?action=reportdesigner&module=CLA"; ?>';
            document.listing.target = '_self';
            document.listing.submit();
        }
    </script>
    <div class="button_wrapper">
            <form method="post" name="listing" action="<?php echo "{$scripturl}?action=reportdesigner&module=CLA"; ?>">
            <input type="hidden" value="CLA" name="module" />
            <input type="hidden" id="orientation_post" name="orientation_post" value="P" />
            <input type="hidden" id="papersize_post" name="papersize_post" value="a4" />
            <input type="hidden" id="exportmode" name="exportmode" value="listing" />
            <input type="hidden" id="reportoutputformat" name="reportoutputformat" value="pdf" />
            <input type="hidden" id="saved_query" name="saved_query" value="<?php echo Sanitize::SanitizeInt($_REQUEST['saved_query']); ?>" />
            <input type="hidden" id="crosstabtype" name="crosstabtype" value="listing" />
            <input type="hidden" id="report" name="report" value="report7" />
            <?php
            if (!Container::get(Registry::class)->getDeviceDetector()->isTablet()) {
                $ExportButtonLink = "{$scripturl}?action=httprequest&type=exporttopdfoptions&nocsv=1"; ?>
            <input type="button" onclick="
                var buttons = new Array();
                buttons[0]={'value':'<?php echo _fdtk('btn_export'); ?>','onclick':'if(setReturns(1)){GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();ListingHTML2pdf();}'};
                buttons[1]={'value':'<?php echo _fdtk('btn_cancel'); ?>','onclick':'GetFloatingDiv(\'exporttopdfoptions\').CloseFloatingControl();'};

                PopupDivFromURL('exporttopdfoptions', 'Export', '<?php echo $ExportButtonLink; ?>', '', buttons, '');" value="Export"/>
            <?php
            } ?>

    </div>

<?php
}
