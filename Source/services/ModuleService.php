<?php

namespace Source\services;

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\framework\session\UserSession;

class ModuleService
{
    private Registry $registry;
    private UserSession $userSession;

    public function __construct(Registry $registry, UserSession $userSession)
    {
        $this->registry = $registry;
        $this->userSession = $userSession;
    }

    public function canSeeModule(string $module): bool
    {
        $moduleDefs = $this->registry->getModuleDefs();
        $permGlobal = $this->registry->getParm($moduleDefs[$module]['PERM_GLOBAL'])->toScalar();

        switch ($module) {
            case Module::ACTIONS:
                return $permGlobal != '' && $permGlobal != 'ACT_INPUT_ONLY';
            case Module::MEDICATIONS:
                return $permGlobal != '' && $this->registry->getParm('MULTI_MEDICATIONS', 'N')->isTrue();
            case Module::ADMIN:
                // Don't show admin if not permitted or on a tablet device
                return $this->userSession->isSubAdmin();
            default:
                return $permGlobal != '';
        }
    }
}
