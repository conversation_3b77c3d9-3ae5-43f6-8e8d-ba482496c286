<?php

use app\services\systemConfiguration\SystemConfigurationServiceFactory;
use PhpOffice\PhpSpreadsheet\IOFactory;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\reports\exceptions\ReportException;
use src\reports\model\packagedreport\PackagedReportModelFactory;
use src\system\configuration\DTO\SystemConfigurationDTO;
use src\system\container\facade\Container;

function ExportToPDFOptions()
{
    $suggestCSV = false;
    $showTooManyColumnsWarning = false;

    if ($_GET['report_type'] == 'listing') {
        // fetch the total row count for the report, used to judge the recommended export option
        $factory = new PackagedReportModelFactory();

        try {
            $packagedReport = $factory->getEntityFactory()->createCurrentPackagedReportFromRequest(new Request());
            $packagedReport->getReport()->setReturnAllRecords(true);

            $totalRows = $packagedReport->getRowCount();
            $totalCols = $packagedReport->getColCount();
            $suggestCSV = $totalRows >= 3000;
            $showTooManyColumnsWarning = $totalCols >= 50;
        } catch (ReportException $e) {
            Log::error('Unable to determine total number of rows of listing report', [
                'exception' => $e,
            ]);
        }
    }

    $isJpGraph = Container::get(Registry::class)->getParm('GRAPH_ENGINE', 'FUSIONCHARTS')->is('JPGRAPH');
    $hasExtraOptions = (!isset($_GET['exportmode']) || $_GET['exportmode'] != 'graph') || $isJpGraph;

    echo '<script language="JavaScript" type="text/javascript">
function getRadioValue(radioGroupName)
{
        radios = document.getElementsByName(radioGroupName);
        for (i = 0; i < radios.length; i++) {
            if (radios[i].checked) return radios[i].value;
        }
}
function setReturns(form_key)
{
    if(form_key == undefined)
    {
        form_key = 0;
    }
    ' . ($hasExtraOptions ? '
    jQuery("#orientation_post").val(getRadioValue(\'orientation\'));
    jQuery("#papersize_post").val(document.getElementById(\'papersize\').value);' : '') . '
    jQuery("#reportoutputformat").val(getRadioValue(\'reportoutputformat\'));

    return true;
}

function returnReturns()
{
    var rArray = new Array();' . ($hasExtraOptions ? '
    rArray[\'orientation_post\'] = getRadioValue(\'orientation\');
    rArray[\'papersize_post\'] = document.getElementById(\'papersize\').value;' : '') . '
    rArray[\'reportoutputformat\'] = getRadioValue(\'reportoutputformat\');

    return rArray;
}';

    if ($suggestCSV) {
        echo '
jQuery(function(){
    suggestCsvExport();
})';
    }

    if ($showTooManyColumnsWarning) {
        echo '
jQuery(function(){
showToManyWarningsColsExport();
})';
    }

    echo '
</script>';

    echo '
    Options: <br/><br/>
    <table style=\'
    border-width: 1px;
    border-spacing: 0px;
    border-style: none none none none;
    border-color: white white white white;
    border-collapse: collapse;
    \' >

    <tr>
    <td style=\'
    border-width: 1px;
    padding: 3px;
    border-style: inset inset inset inset;
    border-color: white white white white;
    width: 200px;
    \'>

        <input type=\'radio\' id="output-pdf" name=\'reportoutputformat\' value=\'pdf\'' . (!$suggestCSV ? 'checked="checked"' : '') . ($hasExtraOptions ? 'onclick=document.getElementById(\'orientation1\').disabled=false;document.getElementById(\'orientation2\').disabled=false;document.getElementById(\'papersize\').disabled=false;' : '') . '> <label for="output-pdf">';

    if ($showTooManyColumnsWarning) {
        echo '<span id="pdf_warning_label" title="' . _fdtk('export_pdf_recommend_csv') . '">PDF</span><br />';
    } else {
        echo 'PDF<br/>';
    }

    echo ' </label><br>';

    if (empty($_GET['noexcel']) || $_GET['noexcel'] != '1') {
        echo "<input type='radio' id=\"output-excel\" name='reportoutputformat' value='excel' " . (!$suggestCSV ? ' checked="checked"' : '') . ($hasExtraOptions ? "onclick=document.getElementById('orientation1').disabled=true;document.getElementById('orientation2').disabled=true;document.getElementById('papersize').disabled=false;" : '') . '> <label for="output-excel">Excel</label><br>';
        if (empty($_GET['nocsv']) || $_GET['nocsv'] != '1') {
            echo "<input type='radio' id=\"output-csv\" name='reportoutputformat' value='csv' " . ($suggestCSV ? ' checked="checked"' : '') . ($hasExtraOptions ? "onclick=document.getElementById('orientation1').disabled=true;document.getElementById('orientation2').disabled=true;document.getElementById('papersize').disabled=true;" : '') . '> <label for="output-csv">';

            if ($suggestCSV) {
                echo '<span id="csv_label" title="' . _fdtk('export_to_csv_recommend') .
                    '">Raw data (CSV)</span><br />';
            } else {
                echo 'Raw data (CSV)<br />';
            }

            echo '</label>';
        }
    }

    echo '
    </td></tr>' .
        ($hasExtraOptions ? '
    <tr>
    <td style=\'
    border-width: 1px;
    padding: 3px;
    border-style: inset inset inset inset;
    border-color: white white white white;
    width: 200px;
    \'>
            <input type=\'radio\' id=\'orientation1\' name=\'orientation\' value=\'portrait\' ' . (GetParm('DEFAULT_PAPER_ORIENTATION', 'P') == 'P' ? 'checked' : '') . '> <label for="orientation1">Portrait</label><br>
            <input type=\'radio\' id=\'orientation2\' name=\'orientation\' value=\'landscape\' ' . (GetParm('DEFAULT_PAPER_ORIENTATION', 'P') == 'L' ? 'checked' : '') . '> <label for="orientation2">Landscape</label><br>
    </td></tr>
    <tr>
    <td style=\'
    border-width: 1px;
    padding: 3px;
    border-style: inset inset inset inset;
    border-color: white white white white;
    width: 200px;
    \'>
    &nbsp Paper size: &nbsp<select id=\'papersize\' name=\'papersize\'>
        <option value=\'a4\' ' . (GetParm('DEFAULT_PAPER_SIZE', 'A4') == 'A4' ? 'selected' : '') . '>A4</option>
        <option value=\'a3\' ' . (GetParm('DEFAULT_PAPER_SIZE', 'A4') == 'A3' ? 'selected' : '') . '>A3</option>
        <option value=\'letter\' ' . (GetParm('DEFAULT_PAPER_SIZE', 'A4') == 'LETTER' ? 'selected' : '') . '>Letter</option>
    </select>
    </td></tr>' : '') . '
    </table>';
}

function ExportSwitch()
{
    if ($_POST['reportoutputformat']) {
        $output_format = $_POST['reportoutputformat'];
    } else {
        $output_format = 'pdf';
    }

    if ($_POST['exportmode'] == 'listing') {
        $html = $_SESSION['listingreportstream'];
        ExportReport($html, 'listing', $output_format);
    } elseif ($_POST['exportmode'] == 'crosstab') {
        $html = $_SESSION['CurrentReport']->GetPDFExportHTML();
        ExportReport($html, 'crosstab', $output_format);
    } elseif ($_POST['exportmode'] == 'graph') {
        if ($output_format == 'pdf') {
            $tmpfile = tempnam('/tmp', 'pn2');
            file_put_contents($tmpfile, $_SESSION['pngimgstream']);
            ExportReport('<h1>Header</h1>', 'graph', $output_format, $tmpfile);
            unlink($tmpfile);
        }
    }
}

function ExportReport($html, $mode = 'graph', $output_format = 'pdf', $pngimg = '')
{
    if ($_REQUEST['widget_id']) {
        $widget_id = $_REQUEST['widget_id'];
    } else {
        $widget_id = 0;
    }

    $systemConfiguration = SystemConfigurationServiceFactory::create();
    $footerEnabled = (int) $systemConfiguration->getSystemConfigurationFromCache(SystemConfigurationDTO::FOOTER_ENABLE);
    $footerText = $footerEnabled === 1 ?
        $systemConfiguration->getSystemConfigurationFromCache(SystemConfigurationDTO::FOOTER_TEXT) :
        '';

    if ($output_format == 'pdf') {
        if (in_array($mode, ['crosstable', 'listing'], true)) {
            define('FPDF_FONTPATH', 'font/');
            require_once __DIR__ . '/../export/PDFTableWrapper.php';

            $pdf = new PDFTableWrapper($_REQUEST['orientation_post'], 'mm', $_REQUEST['papersize_post'], $footerText);
            $pdf->SetHeaderTitle($_REQUEST['rep_name']);
            $pdf->AliasNbPages();
            $pdf->SetFont('Helvetica', '', 7);
            $pdf->SetMargins(20, 20, 20);
            $html = ParseHTMLTableForExport($html, $output_format, $pdf);
            $pdf = OutputPDFTable($html, $mode, $_REQUEST['orientation_post'], $_REQUEST['papersize_post'], $pdf);

            if (\UnicodeString::strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE 6.') !== false) {
                // fix "file not found" issue when opening file directly from IE6
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            }

            $pdf->Output('DatixWebReport.pdf', 'D');
            obExit();
        }
    } elseif ($output_format == 'excel') {
        if (\UnicodeString::strpos($_SERVER['HTTP_USER_AGENT'], 'MSIE')) {
            ob_end_clean();
            header('Pragma: public');
            header('Cache-Control: max-age=0');
        }

        if ($mode == 'crosstab' || $mode == 'listing') {
            ini_set('include_path', ini_get('include_path') . ';../Classes/');
            // Note that the PHPExcel object already exists in the session with
            // all the required data. This is being built in Crosstab->GetData()

            if ($mode == 'crosstab') { // we can generate the excel object at this point
                if (is_array($_SESSION['SerializedReports']) && $widget_id > 0) {
                    $Crosstab = unserialize($_SESSION['SerializedReports'][$widget_id]);
                } else {
                    $Crosstab = $_SESSION['CurrentReport'];
                }

                $objPHPExcel = $Crosstab->GetExcelExportObject();
            } else {
                if (is_array($_SESSION['crosstabtablePHPExcel']) && $widget_id > 0) {
                    $objPHPExcel = unserialize($_SESSION['crosstabtablePHPExcel'][$widget_id]);
                } else {
                    $objPHPExcel = unserialize($_SESSION['crosstabtablePHPExcel']);
                }
            }

            header('Pragma: public');
            header('Cache-Control: max-age=0');
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment; filename="DatixWebReport.xlsx"');
            header('Content-Encoding: none');
            $objWriter = IOFactory::createWriter($objPHPExcel, IOFactory::READER_XLSX);
            $objWriter->save('php://output');

            exit;
        }
    }
}

/*
 * This function is used by listing reports to store the report HTML in the session to export to PDF.
 *
 * Note: Careful deleting it. We need to refactor the way we export listing reports to PDF before doing it.
 */
function output_handler_listingreport($html)
{
    echo '</table>';
    $_SESSION['listingreportstream'] = ob_get_contents();
    $_SESSION['listingreportstream'] = $_SESSION['listing_title'] . '<br/><br/>' . $_SESSION['listingreportstream'];

    return $html;
}
