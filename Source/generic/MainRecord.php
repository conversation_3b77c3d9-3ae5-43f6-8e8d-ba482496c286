<?php

use app\models\document\valueObjects\LinkedRecord;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use app\services\audit\FullAuditRetrieverFactory;
use app\services\document\DocumentListServiceFactory;
use app\services\forms\PageTitleProvider;
use app\services\forms\RecordHeaderProviderFactory;
use app\services\licence\LicensedModuleManager;
use app\services\records\RecordsFactory;
use app\services\securityGroups\SecurityGroupService;
use Source\generic_modules\MED\MedicationFormSectionHelper;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\contacts\helpers\ContactsSearchByIdHelper;
use src\formdesign\forms\service\FormDesignAccessibilityEvaluator;
use src\framework\controller\AssetManager;
use src\framework\registry\Registry;
use src\framework\session\UserSession;
use src\generic\model\MainRecordMapper;
use src\medications\helpers\MedicationSearchFieldsHelper;
use src\medications\models\MedicationFields;
use src\payments\model\PaymentFields;
use src\payments\services\PaymentContactAuditService;
use src\reasons\controllers\ReasonsController;
use src\safeguarding\controllers\ReferralHistoryController;
use src\safeguarding\specifications\LocalAuthorityEnabledSpecification;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

/**
 * @return never
 */
function NewRecord(): void
{
    $accessibilityEvaluator = Container::get(FormDesignAccessibilityEvaluator::class);

    $Level = ($_GET['level'] ? Sanitize::SanitizeInt($_GET['level']) : 1);

    if ($Level == 2) {
        LoggedIn();
    } else {
        if (isset($_REQUEST['form_id'])) {
            if (FormIDExists($_REQUEST['form_id'], $_GET['module'])) {
                $_SESSION['form_id'][$_GET['module']][$Level] = Sanitize::SanitizeInt($_REQUEST['form_id']);
            } else {
                $IncorrectFormID = true;
            }
        }
    }

    if ($IncorrectFormID) {
        fatal_error('There is no form with the specified ID.');
    } else {
        $FormDesign = Forms_FormDesign::GetFormDesign([
            'id' => Sanitize::SanitizeInt($_REQUEST['form_id']),
            'module' => $_GET['module'],
            'level' => 1,
        ]);

        if (!$accessibilityEvaluator->isAccessible($FormDesign)) {
            fatal_error(_fdtk('log_in_to_access_form'));
        }

        $ReporterArray = SetUpDefaultReporter();
        if (!empty($ReporterArray)) {
            $data['con']['R'][] = $ReporterArray;
        }
        ShowForm($data, '', $Level);
    }

    obExit();
}

/**
 * @return never
 */
function ShowForm($data = [], $form_action = 'edit', $level = 2, $sideMenuModule = '', $formId = null): void
{
    global $dtxtitle, $scripturl, $dtxdebug, $dtxtitle;
    global $Show_all_section, $FormType;
    global $formlevel, $FormTitle, $FormTitleDescr, $ModuleDefs, $JSFunctions;

    $registry = Container::get(Registry::class);
    $moduleDefs = $registry->getModuleDefs();

    $datixConfig = Container::get(DatixConfig::class);
    $addMinExtension = $datixConfig->isMinifierOn() ? '.min' : '';

    $Module = Sanitize::getModule($_GET['module']);
    $ModuleTable = $ModuleDefs[$Module]['TABLE'];

    $formlevel = $level;
    $_SESSION[$Module]['FORMLEVEL'] = $formlevel;

    /** @var string $permissionGlobalName */
    $permissionGlobalName = $moduleDefs[$Module]['PERM_GLOBAL'] ?? '';

    if ($form_action == 'search') {
        $Perms = $registry->getParm($permissionGlobalName);
    } else {
        $Perms = Container::get(SecurityGroupService::class)->getPerm($Module, $data['recordid'] ? (int) $data['recordid'] : null);
    }

    $LoggedIn = isset($_SESSION['logged_in']);

    if (isset($moduleDefs[$Module]['CUSTOM_DATA_TRANSFORM'])) {
        require_once $moduleDefs[$Module]['CUSTOM_DATA_TRANSFORM']['file'];
        $data = $moduleDefs[$Module]['CUSTOM_DATA_TRANSFORM']['function']($data);
    }

    SetUpFormTypeAndApproval($Module, $data, $FormType, $form_action, $Perms);

    CheckForRecordLocks($Module, $data, $FormType, $sLockMessage);

    $recordsService = (new RecordsFactory())->create();
    $requiresEditableReasonForRejection = $data['rep_approved'] == 'REJECT' || $recordsService->allowsReadonlyApprovalStatusEditing($Module, $data['rep_approved']);

    if ($requiresEditableReasonForRejection && $registry->getParm('REJECT_REASON', 'Y')->toBool()) {
        $data = ReasonsController::GetReasonsData($Module, $data['recordid'], $data, $FormType);
    }

    if (LocalAuthorityEnabledSpecification::isSatisfied($Module) && $data['recordid']) {
        $data['referral_history'] = ReferralHistoryController::getData($data['recordid']);
    }

    $mainRecordid = null;
    $linkModule = null;
    $linkType = null;
    // Use this if you don't want to specify the link as a LINKED_MODULE
    if (isset($_REQUEST['parent_module']) && $_REQUEST['parent_module'] != '' && isset($_REQUEST['linktype']) && $_REQUEST['linktype'] != '') {
        $linkModule = Sanitize::getModule($_REQUEST['parent_module']);
        $linkType = Sanitize::SanitizeString($_REQUEST['linktype']);
    } elseif (!empty($moduleDefs[$Module]['LINKED_MODULE']['parent_ids'])) {
        [$linkModule, $mainRecordid] = getParentRecordInfo($Module);
        $linkType = $moduleDefs[$Module]['TABLE'];
    }

    // Load form settings
    $formSettings = ['module' => $Module, 'level' => $level, 'form_type' => $FormType, 'link_type' => $linkType, 'parent_module' => $linkModule];
    if ($formId || $formId === '0') {
        $formSettings['id'] = $formId;
    }

    $FormDesign = Forms_FormDesign::GetFormDesign($formSettings);
    // $FormDesign->LoadFormDesignIntoGlobals();

    // These fields have special functionality, they are effectively computed fields when the related date is set.
    if ($Module == 'CLA') {
        $closedDateFields = [
            'indem_dclosed' => 'fin_indemnity_reserve',
            'expen_dclosed' => 'fin_expenses_reserve',
            'fin_medical_dclosed' => 'fin_medical_incurred',
            'fin_legal_dclosed' => 'fin_legal_incurred',
            'fin_temporary_indemnity_dclosed' => 'fin_temporary_indemnity_incurred',
            'fin_permanent_indemnity_dclosed' => 'fin_permanent_indemnity_incurred',
        ];

        foreach ($closedDateFields as $dateField => $incurredField) {
            if ($data[$dateField] !== null) {
                $FormDesign->ReadOnlyFields[$incurredField] = true;
            }
        }
    }

    if ($LoggedIn && $_GET['action'] != 'addnew') {
        $FormDesign->UnsetDefaultValues();
    }

    SetUpApprovalArrays($Module, $data, $CurrentApproveObj, $ApproveObj, $FormType, $FormDesign, '', $Perms);
    $taggedFields = $FormDesign->taggedFields;
    $lastChildFirstFields = $FormDesign->lastChildFirstFields;
    $FormFile = GetBasicFormFileName($Module, $level);
    include $FormFile;
    /**
     * From the include we get the form array leaking into this scope.
     *
     * @var array<string,array> $FormArray
     */
    $assetManager = new AssetManager();
    $Table = FormTableFactory::create($FormType, $Module, $FormDesign, $assetManager);
    $spscFieldset = $registry->getParm('SPSC_DATASET')->isTrue();
    $captureTimeTaken = !empty($moduleDefs[$Module]['RECORD_SUBMIT_DURATION']) && $FormType === FormTable::MODE_NEW;

    $assetManager->addJs([
        ['file' => 'vendor/tinymce/tinymce/tinymce.min.js'],
        ['file' => 'js_functions/datixTinyMce.js'],
        [
            'file' => 'src/generic/js/panelData.js',
            'conditions' => (
                $registry->getParm('SHOW_PANEL_INDICATORS', 'N')->toBool()
                && !empty($data['recordid'])
                && !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_SEARCH])
                && empty($_GET['print'])
            ),
        ],
        [
            'file' => 'src/generic/js/checkExisting.js',
            'conditions' => in_array($FormType, [FormTable::MODE_NEW, FormTable::MODE_EDIT]),
        ],
        [
            'file' => 'src/generic/js/toggleTable.js',
            'conditions' => LocalAuthorityEnabledSpecification::isSatisfied($Module),
        ],
        [
            'file' => 'src/generic/js/showReserveChanges.js',
            'conditions' => (
                $FormType !== FormTable::MODE_SEARCH
                && $Module === Module::CLAIMS
                && $formlevel == 2
            ),
        ],
        [
            'file' => 'src/generic/js/print.js',
            'conditions' => $FormType == FormTable::MODE_PRINT,
        ],
        [
            'file' => 'src/generic/js/checkPayeeRespondent.js',
            'conditions' => (
                $FormType !== FormTable::MODE_SEARCH
                && $formlevel == 2
                && $Module === Module::PAYMENTS
            ),
        ],
        [
            'file' => 'js_functions/timeTakenToSubmit.js',
            'conditions' => $captureTimeTaken,
        ],
        ['file' => 'node_modules/select2/dist/js/select2.js'],
        ['file' => 'js_functions/select2Js/select2.js'],
        ['file' => 'js_functions/scrollTo.js'],
        [
            'file' => 'src/contacts/js/forceSearchByIdNumber.js',
            'conditions' => Container::get(ContactsSearchByIdHelper::class)->isForceSearchByIdEnabled(),
        ],
    ]);

    $assetManager->sendVariablesToJs([
        'submitClicked' => false,
        'module' => $Module,
    ], false);

    if (!in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_SEARCH, FormTable::MODE_READONLY])) {
        $assetManager->sendVariablesToJs([
            'AlertAfterChange' => true,
        ], false);
    }

    if ($FormType === FormTable::MODE_NEW && isset($data['time_taken_to_submit'])) {
        // If we've returned to the form after failed server-side validation
        // then we need to retain the original submission time
        $assetManager->sendVariablesToJs([
            'timeTakenToSubmit' => (int) $data['time_taken_to_submit'],
        ]);
    }

    $assetManager->sendTextToJs([
        'print_options' => _fdtk('print_options'),
        'print' => _fdtk('print'),
        'close' => _fdtk('btn_close'),
        'select_form_sections' => _fdtk('select_form_sections'),
        'print_count_selected_for_print' => _fdtk('print_count_selected_for_print'),
        'print_count_separator' => _fdtk('print_count_separator'),
        'search' => _fdtk('form_search'),
        'no_matches' => _fdtk('no_matches'),
        'mandatory_message' => _fdtk('mandatory_err'),
        'reason_for_change' => _fdtk('reason_for_change'),
    ]);

    // needed because some complaints field names are duplicated on subforms
    if (!empty($moduleDefs[$Module]['IDENTIFY_TABLE'])) {
        $Table->ffTable = $moduleDefs[$Module]['TABLE'];
    }

    $Table->makeForm($FormArray, $data, $Module);

    $_REQUEST['form_design_id'] = $Table->getFormDesign()->getId();

    $userSession = Container::get(UserSession::class);

    $assetManager->sendVariablesToJs([
        'FormType' => $FormType,
        'printSections' => $Table->printSections,
        'showReserveReasonForChange' => $registry->getParm('RESERVE_REASON_CLA', 'N')->toScalar(),
        'spscFieldset' => $spscFieldset,
        'loggedIn' => $userSession->isLoggedIn(),
    ]);

    if (!empty($moduleDefs[$Module]['IS_MEDS_ENABLED_FOR_MODULE'])) {
        $medicationSearchFieldsHelper = Container::get(MedicationSearchFieldsHelper::class);
        $assetManager->sendVariablesToJs([
            'nrlsEnabled' => $registry->getParm('NRLS_ENABLED')->isTrue(),
            'recordUuid' => $data['uuid'] ?? '',
            'isNewRecord' => $FormType !== 'edit',
            'FormType' => $FormType,
            'medicationsLocationFilteringEnabled' => $datixConfig->getMedicationLocationFilteringEnabled(),
            'equipmentV2Enabled' => $datixConfig->getEquipmentsV2Enabled(),
            'medicationsBaseURL' => $datixConfig->getMedicationsBaseUrl(),
            'equipmentsBaseURL' => $datixConfig->getMedicationsBaseUrl(),
            'nswhFieldset' => $registry->getParm('NSWH_DATASET')->isTrue(),
            'spscFieldset' => $spscFieldset,
            'vanessasLawEnabled' => $datixConfig->getVanessaLawEnabled(),
            'usePhpMedsForm' => $datixConfig->usePhpMedsForm(),
            'usePhpEquipForm' => $datixConfig->usePhpEquipForm(),
            'administered_fields' => $medicationSearchFieldsHelper->includeMappedVersionOfFieldNames(MedicationFields::getAdministeredDrugFields()),
            'correct_fields' => $medicationSearchFieldsHelper->includeMappedVersionOfFieldNames(MedicationFields::getCorrectDrugFields()),
            'medsDrugDisplayFields' => MedicationFormSectionHelper::getSearchDrugDisplayFieldsGroupedByType(),
            'delete_medication_confirm_message' => _fdtk('delete_medication_confirm_message'),
            'delete_medication_error_message' => _fdtk('delete_medication_error_message'),
        ]);

        $assetManager->addJs(['src/medications/js/medications.js']);

        $assetManager->addJs([
            ['file' => 'vendor/tinymce/tinymce/tinymce.min.js'],
            ['file' => 'js_functions/datixTinyMce.js'],
            ['file' => 'js_functions/polyfills/promise.min.js'],
            ['file' => 'dist/irm-initialisation-config.js'],
            ['file' => 'js_functions/timeTakenToSubmit.js', 'conditions' => $captureTimeTaken],
        ]);

        $assetManager->addCss(['src/medications/css/medications.css']);
    }

    $dtxtitle = $FormDesign->FormTitle;

    $FormTitle = $FormDesign->FormTitle . GetRejectedSuffix($data, $Module, $moduleDefs[$Module]['FIELD_NAMES']['HANDLER'] ?? null, $data['rea_con_name'] ?? null);

    $title_suffix = (new RecordHeaderProviderFactory())->create()->getRecordHeader($Module, $data);

    if ($FormType === FormTable::MODE_SEARCH) {
        $FormTitle .= _fdtk('search_for_records');
    }

    $subtitle = $FormDesign->FormTitleDescr ?? '';

    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            $FormDesign->FormType ?? '',
            $Module,
            $FormTitle,
            $subtitle,
            $title_suffix,
        );

    $ButtonGroup = new ButtonGroup();

    $haystack = strtolower($_SERVER['HTTP_REFERER'] ?? '');
    $needles = [
        'action=incident&module=inc',
        'action=record&module=cla',
        'action=record&module=pay',
    ];
    $strposa = function ($haystack, $needles) {
        foreach ($needles as $needle) {
            if (str_contains($haystack, $needle)) {
                return true;
            }
        }

        return false;
    };
    if ($strposa($haystack, $needles)) {
        $parsedUrl = parse_url($haystack);
        parse_str($parsedUrl['query'], $query);
        $ButtonGroup->PopulateWithStandardFormButtons([
            'module' => $Module,
            'formtype' => $FormType,
            'data' => $data,
            'form_id' => $Module . 'form',
            'level' => $level,
            'link_module' => $linkModule,
            'main_recordid' => $mainRecordid,
            'cancel' => ['query_string' => [
                'action' => $query['action'],
                'module' => strtoupper($query['module']),
                'fromsearch' => $query['fromsearch'],
                'recordid' => $query['recordid'],
            ]],
        ]);
    } else {
        $ButtonGroup->PopulateWithStandardFormButtons([
            'module' => $Module,
            'formtype' => $FormType,
            'data' => $data,
            'form_id' => $Module . 'form',
            'level' => $level,
            'link_module' => $linkModule,
            'main_recordid' => $mainRecordid,
        ]);
    }

    if (
        !in_array($FormType, [FormTable::MODE_SEARCH, FormTable::MODE_PRINT])
        && !empty($_SESSION[$Module]['RECORDLIST'])
    ) {
        $CurrentIndex = $_SESSION[$Module]['RECORDLIST']->getRecordIndex(['recordid' => $data['recordid']]);

        if ($CurrentIndex !== false) {
            $ButtonGroup->AddNavigationButtons($_SESSION[$Module]['RECORDLIST'], $data['recordid'], $Module);
        }
    }

    if (
        !in_array($FormType, [FormTable::MODE_SEARCH, FormTable::MODE_PRINT, FormTable::MODE_READONLY])
        && !empty($moduleDefs[$Module]['EXTRA_FORM_ACTIONS'])
    ) {
        // prevent the custom actions from being displayed unless the form is editable. This may need to be customisable in the future
        foreach ($moduleDefs[$Module]['EXTRA_FORM_ACTIONS'] as $btn) {
            if (!empty($btn['condition'])) {
                $ButtonGroup->AddButton([
                    'id' => $btn['title'] . '_btn',
                    'name' => $btn['title'],
                    'label' => _fdtk($btn['title']),
                    'onclick' => $btn['js'],
                    'action' => $btn['action'],
                ]);
            }
        }
    }

    $ErrorField = empty($data['error']['Validation'])
        ? null
        : array_keys($data['error']['Validation'])[0];

    $userLabels = [];
    if ($FormDesign->UserLabels) {
        $userLabels = $FormDesign->UserLabels;
    }

    // If we are in "Print" mode, we don't want to display the menu
    if ($FormType != FormTable::MODE_PRINT && $LoggedIn) {
        GetSideMenuHTML([
            'module' => $Module,
            'menumodule' => $sideMenuModule,
            'table' => $Table,
            'buttons' => $ButtonGroup,
            'error_field' => $ErrorField,
            'userLabels' => $userLabels,
        ]);

        $template = null;

        template_header($template, null, $assetManager);
    } else {
        $template = new Template(['noMenu' => true]);

        template_header($template, null, $assetManager);
    }

    // include any custom javascript libraries
    if (!empty($moduleDefs[$Module]['CUSTOM_JS'])) {
        echo $moduleDefs[$Module]['CUSTOM_JS'];
    }

    if (!in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_SEARCH])) {
        $multiPerms = $userSession->isLoggedIn() ? $userSession->getCurrentUser()->getAccessLevels($Module) : [];

        $StatusesNoMandatory = GetStatusesNoMandatory(
            [
                'module' => $Module,
                'level' => $multiPerms,
                'from' => $data['rep_approved'],
            ],
            (int) $level,
        );

        if (!empty($StatusesNoMandatory)) {
            $JSFunctions[] = 'StatusesNoMandatory = [\'' . implode("','", $StatusesNoMandatory) . '\'];';
        }

        $JSFunctions[] = MakeJavaScriptValidation($Module, $FormDesign);
    }

    $newFormAction = $FormType === FormTable::MODE_SEARCH
        ? ($Module === Module::INCIDENTS ? 'incidentsdoselection' : 'doselection')
        : 'saverecord';

    echo '
<form method="post" id="' . $Module . 'form" name="' . $Module . 'form" class="dtx-form" enctype="multipart/form-data" action="'
        . $scripturl . '?action=' . $newFormAction
        . (!empty($_GET['from_parent_record']) ? '&from_parent_record=1' : '')
        . '&module=' . $Module . '" onsubmit="return(submitClicked';

    if (!in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_SEARCH, FormTable::MODE_READONLY])) {
        echo ' && validateOnSubmit()';
    }

    echo ')">
<input type="hidden" name="module" id="module" value="' . $Module . '" />
<input type="hidden" name="recordid" id="recordid" value="' . ($data['recordid'] ?? '') . '" />
<input type="hidden" id="form-panel" name="panel" value="' . ($data['panel'] ?? '') . '" />
<input type="hidden" name="form_id" value="' . $FormDesign->getId() . '" />
<input type="hidden" name="updateid" value="' . ($data['updateid'] ?? '') . '" />
<input type="hidden" name="submit_stage" value="' . ($data['submit_stage'] ?? '') . '" />
<input type="hidden" name="rep_approved_old" value="' . ($data['rep_approved_old'] ?? $data['rep_approved'] ?? 'NEW') . '" />
<input type="hidden" name="fromlisting" value="' . Escaper::escapeForHTMLParameter($_GET['fromlisting'] ?? '') . '" />
<input type="hidden" name="qbe_recordid" value="' . Escaper::escapeForHTMLParameter($_GET['qbe_recordid'] ?? '') . '" />
<input type="hidden" name="qbe_return_module" value="' . Escaper::escapeForHTMLParameter($_GET['qbe_return_module'] ?? '') . '" />
<input type="hidden" name="fromsearch" value="' . Escaper::escapeForHTMLParameter($_REQUEST['fromsearch'] ?? '') . '" />
<input type="hidden" name="from_report" value="' . Escaper::escapeForHTMLParameter($_GET['from_report'] ?? '') . '" />
<input type="hidden" name="drilllevel" value="' . Escaper::escapeForHTMLParameter($_GET['drilllevel'] ?? '') . '" />
<input type="hidden" name="overdue" value="' . Escaper::escapeForHTMLParameter($_GET['overdue'] ?? '') . '" />
<input type="hidden" name="fromparent" value="' . (isset($_REQUEST['link_module']) && isset($_REQUEST['main_recordid']) ? '1' : '0') . '" />
<input type="hidden" name="form_mode" id="form_mode" value="' . $FormType . '" />
';

    echo '
    <input type="hidden" name="formlevel" id="formlevel" value="' . $formlevel . '" />';

    if ($formlevel == 1) {
        echo '
    <input type="hidden" name="holding_form" value="1" />';
    }

    if (!empty($moduleDefs[$Module]['LINKED_MODULE'])) {
        echo '
    <input type="hidden" name="link_module" id="link_module" value="' . Sanitize::SanitizeString($linkModule) . '" />
    <input type="hidden" name="main_recordid" id="main_recordid" value="' . Sanitize::SanitizeInt($mainRecordid) . '" />
    ';
    }

    if (!empty($data['error'])) {
        echo '<div class="error_div">Some of the information you entered is missing or incorrect.  Please correct the fields marked below and try again.</div>';
    }

    if (!empty($data['error']['message'])) {
        echo '<div class="error_div">' . $data['error']['message'] . '</div>';
    }

    echo GetSessionMessages();

    if ($sLockMessage) {
        echo '
        <div class="lock_message">
            <div id="LockMessage">' . $sLockMessage . '</div>
            <div id="userLockMessage"></div>
        </div>';
    }

    // Display the sections
    $Table->makeTable();
    echo $Table->getFormTable();

    echo '
        <input type="hidden" id="printAfterSubmit" name="printAfterSubmit" value="0" />
        <input type="hidden" id="rbWhat" name="rbWhat" value="Save">';

    if ($captureTimeTaken) {
        echo '
        <input type="hidden" name="time_taken_to_submit" id="time_taken_to_submit" />
        <input type="hidden" name="show_field_time_taken_to_submit" id="show_field_time_taken_to_submit" value="1" />
        ';
    }

    echo $ButtonGroup->getHTML();

    echo '
</form>';

    if ($FormType != FormTable::MODE_PRINT) {
        echo JavascriptPanelSelect($Show_all_section, Sanitize::SanitizeString($_GET['panel'] ?? ''), $Table, $ErrorField);
    }

    footer($template);

    obExit();
}

function ListContacts($data, $con, $ReadOnly, $FormType)
{
    ListContactsSection([
        'data' => $data,
        'module' => Sanitize::getModule($_GET['module']),
        'formtype' => $FormType,
    ]);
}

function ShowTempLevel1Record()
{
    $moduleDefs = Container::get(ModuleDefs::class);
    $Module = Sanitize::getModule($_GET['module']);
    $moduleData = $moduleDefs->getModuleData($Module);
    $recordid = Sanitize::SanitizeInt($_GET['recordid']);

    $sql = 'SELECT recordid, ' . implode(', ', $moduleData->getFieldArray()) . ',
        updateid, updatedby FROM ' . $moduleDefs[$Module]['TABLE'] . ' WHERE recordid = :recordid';

    $data = DatixDBQuery::PDO_fetch($sql, ['recordid' => $recordid]);

    $extra_data = GetExtraData($Module, $recordid);

    $data = array_merge($data, $extra_data);

    $data['con'] = getLinkedContacts(['recordid' => $recordid, 'module' => $Module, 'formlevel' => 1], true);

    $data['temp_record'] = true;

    // we can remove this once additional information fields become real fields for all modules.
    if ($Module != 'INC') {
        if (is_array($data['con'])) {
            foreach ($data['con'] as $type => $links) {
                if ($moduleDefs[$Module]['LEVEL1_CON_OPTIONS'][$type]['TriggerField']) {
                    $data[$moduleDefs[$Module]['LEVEL1_CON_OPTIONS'][$type]['TriggerField']] = true;
                }
            }
        }
    }

    if ($moduleData->hasNotepad()) {
        $data['notes'] = GetNotepad($recordid, $Module);
    }

    $linkedRecord = new LinkedRecord($Module, $recordid);

    $docListService = DocumentListServiceFactory::create();
    $list = $docListService->getListOfDocuments($linkedRecord);

    $data['show_document'] = count($list) > 0 ? 'Y' : 'N';

    ShowForm($data, null, 1);
}

/**
 * Displays a record from $ModuleTable.
 *
 * @return never
 */
function ShowMainRecord($recordid, $message = '', $sideMenuModule = ''): void
{
    // Shows a record from the main table ($ModuleTable) and displays it using the PAL2 form
    global $FormType;

    $moduleDefs = Container::get(ModuleDefs::class);

    if (
        !empty($_SESSION['logged_in'])
        || empty($_GET['submitandprint'])
        || !HashesMatch($_GET['module'], $_GET['recordid'])
    ) {
        LoggedIn();
    } else {
        ShowTempLevel1Record();
        obExit();
    }

    $Module = Sanitize::getModule($_GET['module']) ?? '';
    $Perms = Container::get(SecurityGroupService::class)->getPerm($Module, (int) $recordid);
    if (
        !Container::get(LicensedModuleManager::class)->isModuleLicenced($Module)
        || !$Perms
    ) {
        CheckRecordNotFound(['module' => $Module, 'recordid' => $recordid]);
    }

    $recordData = Container::get(MainRecordMapper::class)->setModule($Module)->find((int) $recordid);
    if ($recordData === false) {
        CheckRecordNotFound(['module' => $Module, 'recordid' => $recordid]);
    }

    $AccessFlag = GetAccessFlag($Module, $Perms, $recordData['rep_approved']);
    if ($AccessFlag == '' && $recordData['rep_approved'] != '') {
        CheckRecordNotFound(['module' => $Module, 'recordid' => $recordid]);
    }

    $form_action = $AccessFlag === 'R' ? FormTable::MODE_READONLY : FormTable::MODE_EDIT;

    $recordData[$moduleDefs[$Module]['FK']] = $recordData['recordid'];

    if ($FormType != FormTable::MODE_SEARCH) {
        $extra_data = GetExtraData($Module, $recordid, $recordData);
    }

    // fetch generic linked records
    if (!empty($moduleDefs[$Module]['LINKED_RECORDS'])) {
        foreach ($moduleDefs[$Module]['LINKED_RECORDS'] as $link) {
            if (!empty($link['useGenericGet'])) {
                $extra_data = array_merge($extra_data, getLinkedRecords($recordid, $link));
            }
        }
    }

    $recordData = array_merge($recordData, $extra_data);

    if (
        !isset($moduleDefs[$Module]['LINKED_CONTACTS'])
        || $moduleDefs[$Module]['LINKED_CONTACTS'] !== false
    ) {
        $LinkedContactFunction = ($moduleDefs[$Module]['GET_LINKED_CONTACTS_FUNCTION'] ?? null ?: 'GetLinkedContacts');
        $recordData['con'] = $LinkedContactFunction(
            [
                'recordid' => $recordid,
                'module' => $Module,
                'formlevel' => $FormLevel ?? null,
            ],
            isset($FormLevel) && $FormLevel == 1,
        );
    }

    if (
        !isset($moduleDefs[$Module]['NOTEPAD'])
        || $moduleDefs[$Module]['NOTEPAD'] !== false
    ) {
        $recordData['notes'] = GetNotepad($recordid, $Module);
    }

    // Check if we need to show full audit trail
    if ($recordData && !empty($_GET['full_audit'])) {
        $recordid = (int) $recordid;
        $FullAudit = (new FullAuditRetrieverFactory())->create()->retrieve($Module, $recordid);

        if ($FullAudit) {
            $recordData['full_audit'] = $FullAudit;
        }
    }

    if (!empty($recordData[PaymentFields::PAYEE])) {
        $paymentAuditService = Container::get(PaymentContactAuditService::class);
        $paymentAuditService->auditContactsByPayeeContactIDs([$recordData[PaymentFields::PAYEE]]);
    }

    if (!empty($recordData['rep_approved'])) {
        $FormLevel = GetFormLevel($Module, $Perms, $recordData['rep_approved']);
    } else {
        $FormLevel = 2;
    }

    $_SESSION[$Module]['FORMLEVEL'] = $FormLevel;

    if ($FormLevel == 1) {
        // need to populate show_xxx fields
        $recordData = PopulateLevel1FormFields(['module' => $Module, 'data' => $recordData]);
    }

    AuditOpenRecord($Module, $recordid, '');

    ShowForm($recordData, $form_action, $FormLevel, $sideMenuModule);

    obExit();
}

/**
 * Generic function for retrieving linked record data.
 *
 * @param int $recordid the ID of the main record
 *
 * @return array $data                          the linked record data
 */
function getLinkedRecords($recordid, $link)
{
    $suffix = 1;
    $data = [];
    $orderBy = $link['save_listorder'] ? 'listorder' : $link['recordid_field'];

    $sql = 'SELECT ' . implode(', ', $link['basic_form']['Rows']) . '
            FROM ' . $link['table'] . '
            WHERE ' . $link['main_recordid_label'] . ' = :recordid
            ORDER BY ' . $orderBy;

    $LinkedRecords = DatixDBQuery::PDO_fetch_all($sql, ['recordid' => $recordid]);

    foreach ($LinkedRecords as $row) {
        foreach ($link['basic_form']['Rows'] as $field) {
            $data[$field . '_' . $suffix] = $row[$field];
        }
        ++$suffix;
    }

    if ($suffix > 1) {
        $data[$link['type'] . '_max_suffix'] = $suffix;
    }

    return $data;
}

/**
 * Retrieves information (module and id) about the parent record of linked records. Need to use $_REQUEST, because we might be returning to the page after an error with the info in the $_POST rather than the $_GET.
 *
 * @global array  $ModuleDefs
 *
 * @param string $module the link module code
 *
 * @return array $values     an array where the first element is the parent module and the second element is the parent record id
 */
function getParentRecordInfo($module)
{
    global $ModuleDefs;

    $values = [null, null];
    if (isset($_REQUEST['link_module'], $_REQUEST['main_recordid'])) {
        // link info has been passed on the query string
        $values = [$_REQUEST['link_module'], $_REQUEST['main_recordid']];
    } elseif (isset($_REQUEST['recordid'])) {
        // need to fetch link info from DB
        $sql = 'SELECT
               ' . implode(',', $ModuleDefs[$module]['LINKED_MODULE']['parent_ids']) . '
                FROM
               ' . $ModuleDefs[$module]['TABLE'] . '
                WHERE
                    recordid = :recordid';
        $PDOParams = ['recordid' => $_REQUEST['recordid']];
        $mainIds = DatixDBQuery::PDO_fetch($sql, $PDOParams);

        foreach ($mainIds as $id => $value) {
            if ($value > 0) {
                $values = [array_search($id, $ModuleDefs[$module]['LINKED_MODULE']['parent_ids']), $value];

                break;
            }
        }
    }

    return $values;
}
