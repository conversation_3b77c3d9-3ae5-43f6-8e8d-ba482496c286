<?php

use app\framework\DoctrineEntityManagerFactory;
use app\models\user\entities\UserEntity;
use Doctrine\ORM\EntityManager;
use src\component\form\FormTableFactory;
use src\framework\controller\Request;
use src\framework\query\QueryFactory;
use src\framework\query\SqlWriter;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;

function SavePrimarySubject($aParams)
{
    global $ModuleDefs;

    $sql = 'SELECT TOP 1 ' . implode(', ', $aParams['basic_form']['Rows']) . ' from ' . $aParams['table'] . ' WHERE ' . $aParams['main_recordid_label'] . ' = ' . $aParams['main_recordid'] . ' ORDER BY listorder ASC, ' . $aParams['recordid_field'] . ' ASC';

    $row = DatixDBQuery::PDO_fetch($sql);

    if (is_array($ModuleDefs[$aParams['module']]['PRIMARY_SUBJECT_MAPPINGS'])) {
        foreach ($aParams['basic_form']['Rows'] as $Field) {
            if (!empty($ModuleDefs[$aParams['module']]['PRIMARY_SUBJECT_MAPPINGS'][$Field])) {
                $NewFields[] = $ModuleDefs[$aParams['module']]['PRIMARY_SUBJECT_MAPPINGS'][$Field];
                $NewValues[$ModuleDefs[$aParams['module']]['PRIMARY_SUBJECT_MAPPINGS'][$Field]] = $row[$Field];
            }
        }
    }

    $generatedFields = GenerateSQLFromArrays([
        'FieldArray' => $NewFields,
        'DataArray' => $NewValues,
        'Module' => $aParams['module'],
    ]);

    if (!empty($generatedFields)) {
        $dbQuery = new \DatixDBQuery();
        $dbQuery->beginTransaction();

        $queryFactory = new QueryFactory();
        $query = $queryFactory->getQuery();
        $query->update($ModuleDefs[$aParams['module']]['TABLE']);
        $query->set($generatedFields);
        $query->where(['recordid' => $aParams['main_recordid']]);

        $writer = Container::get(SqlWriter::class);
        [$querySql, $parameters] = $writer->writeStatement($query);

        $dbQuery->setSQL($querySql);
        $dbQuery->prepareAndExecute($parameters);
        $dbQuery->commit();
    }
}

/**
 * @desc Generates an update-style SQL string (key=value, key2=value2...) from arrays of fields and data.
 *
 * @param array $aParams array of parameters
 *
 * @return string string containing key/value pairs formated for a SQL update statement
 */
function GenerateSQLFromArrays($aParams)
{
    global $FieldDefs;
    $rowFields = [];
    $Module = $aParams['Module'];

    $SuffixString = '';
    if (!empty($aParams['Suffix'])) {
        $SuffixString = '_' . $aParams['Suffix'];
    }

    if (is_array($aParams['FieldArray']) && is_array($aParams['DataArray'])) {
        foreach ($aParams['FieldArray'] as $FieldKey) {
            if (array_key_exists($FieldKey . $SuffixString, $aParams['DataArray'])) {
                $Value = $aParams['DataArray'][$FieldKey . $SuffixString];
                $fieldType = $FieldDefs[$Module][$FieldKey]['Type'] ?? null;
                $isFieldNullZeros = !empty($FieldDefs[$Module][$FieldKey]['NullZeros']);

                if (
                    $Value == ''
                    && (
                        in_array($fieldType, ['number', 'money', 'date'])
                        || $isFieldNullZeros
                    )
                ) {
                    $Value = 'NULL';
                }



                if (
                    in_array($fieldType, ['number', 'money'])
                    || (
                        $Value === 'NULL'
                        && (
                            $fieldType === 'date'
                            || $isFieldNullZeros
                        )
                    )
                ) {
                    if ($Value !== '') {
                        $rowFields[$FieldKey] = $Value;
                    }
                } else {
                    $rowFields[$FieldKey] = $Value;
                }
            }
        }
    }

    return $rowFields;
}

function GetSubjectSectionHTML($aParams)
{
    global $JSFunctions, $formlevel;

    if ($formlevel == null) {
        $formlevel = ($_POST['form_level'] ?? 1);
    }
    $SavedFormDesignSettings = saveFormDesignSettings();
    unsetFormDesignSettings();

    $AJAX = $aParams['ajax'] ?? false;

    if ($aParams['formtype'] == 'Search') {
        $aParams['suffix'] = null;
    }

    if ($AJAX) {
        $formlevel = $aParams['level'];
    }

    $aSubjectRows = getBasicSubjectForm($aParams);
    $request = new Request();
    $formId = $request->getParameter('form_id');
    $subjectsFormDesign = Forms_FormDesign::GetFormDesign(['id' => $formId, 'module' => $aParams['module'], 'level' => $formlevel, 'form_type' => $aParams['formtype']]);
    $subjectsFormDesign = ModifyFormDesignForSubjects($subjectsFormDesign);

    if ($aParams['suffix']) {
        $subjectsFormDesign->AddSuffixToFormDesign($aParams['suffix']);
        $aParams['data'] = AddSuffixToData($aParams['data'], $aParams['suffix'], getSubjectFieldsForSave(['module' => $aParams['module']]));
    }

    $oSubjectTable = FormTableFactory::create($aParams['formtype'], $aParams['module'], $subjectsFormDesign);
    $oSubjectTable->ffTable = 'COMSUB';
    $oSubjectTable->makeForm($aSubjectRows, $aParams['data'], $aParams['module'], ['dynamic_section' => true]);

    $html = '';

    if (!$AJAX && $aParams['formtype'] != 'Print') {
        $html .= '<div id="' . $aParams['subject_name'] . '_section_div_' . $aParams['suffix'] . '">';
    }

    $html .= $oSubjectTable->getFormTable();
    $html .= '<input type="hidden" name="' . $aParams['subject_name'] . '_link_id_' . $aParams['suffix'] . '" id="' . $aParams['subject_name'] . '_link_id_' . $aParams['suffix'] . '" value="' . ($aParams['data']['subject_id'] ?? '') . '" />';

    if ($aParams['formtype'] != 'Print' && $aParams['formtype'] != 'ReadOnly' && $aParams['formtype'] != 'Search') {
        $buttonText = _fdtk('copy_subject');
        $spellChecker = isset($_SESSION['Globals']['WEB_SPELLCHECKER']) && $_SESSION['Globals']['WEB_SPELLCHECKER'] == 'Y' ? 'true' : 'false';
        $html .= '
            <div class="button-margin-bottom">
                <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-secondary" id="copy_subject_section" onclick="CopySectionToForm(\'' . $aParams['subject_name'] . '\',\'\',jQuery(\'#' . $aParams['subject_name'] . '_section_div_' . $aParams['suffix'] . '\'),\'' . $aParams['module'] . '\',' . $aParams['suffix'] . ', true, ' . $spellChecker . ', ' . (isset($_REQUEST['form_id']) && is_numeric($_REQUEST['form_id']) ? Sanitize::SanitizeInt($_REQUEST['form_id']) : 'null') . ');">
                    <span>' . $buttonText . '</span>
                </button>
            </div>';
    }

    if (!$AJAX && $aParams['formtype'] != 'Print') {
        $html .= '</div>';
    }

    loadFormDesignSettings($SavedFormDesignSettings);

    return $html;
}

function getSubjectFieldsForSave($aParams)
{
    $RowList = GetSubjectRowList($aParams);
    $RowList[] = 'listorder';

    return ['Rows' => $RowList];
}

function GetSubjectRowList($aParams)
{
    global $ModuleDefs;

    $RowList = $ModuleDefs[$aParams['module']]['LINKED_RECORDS'][$ModuleDefs[$aParams['module']]['SUBJECT_TYPE']]['basic_form']['Rows'];

    return $RowList;
}

function getBasicSubjectForm($aParams)
{
    global $ModuleDefs;

    $aParams['subject_name'] = $ModuleDefs[$aParams['module']]['SUBJECT_TYPE'];
    $RowList = GetSubjectRowList($aParams);

    if (!empty($aParams['rows'])) {
        return ['Rows' => $RowList];
    }

    return [
        'Parameters' => ['Suffix' => $aParams['suffix']],
        'subject' . ($aParams['suffix'] ? '_' . $aParams['suffix'] : '') => [
            'Title' => 'Subject',
            'OrderField' => ['id' => $aParams['subject_name'] . '_listorder_' . $aParams['suffix'], 'value' => $aParams['data']['listorder'] ?? ''],
            'ClearSectionOption' => $aParams['clearsection'],
            'DeleteSectionOption' => !$aParams['clearsection'],
            'MandatorySection' => 'subject',
            'ContactSuffix' => $aParams['suffix'],
            'DynamicSectionType' => $aParams['subject_name'],
            'Rows' => $RowList,
            'ContainedIn' => 'subject',
            'FieldFormatsTable' => $aParams['module'] . 'SUB',
        ], ];
}

function ModifyFormDesignForSubjects(Forms_FormDesign $design)
{
    if (is_array($design->ExpandSections)) {
        $NewArray = [];
        foreach ($design->ExpandSections as $FieldName => $SectionArray) {
            foreach ($SectionArray as $SectionID => $Details) {
                if ($Details['section'] != 'subject') {
                    $NewArray[$FieldName][] = $Details;
                }
            }
        }

        $design->ExpandSections = $NewArray;
    }

    // remove extra sections/extra fields
    $design->unsetUserDefinedElements();

    $spscDataSet = Container::get(Registry::class)->getParm('SPSC_DATASET', 'N')->isTrue();

    if ($spscDataSet) {
        $design = populateLocationFromUser($design);
    }

    return $design;
}

/**
 * @return Forms_FormDesign
 *
 * Populate the subjects location field based upon the user's location
 * Uses default values to only apply if no value has already been saved or provided
 */
function populateLocationFromUser(Forms_FormDesign $design)
{
    if (empty($design->DefaultValues['csu_location_id']) && !isset($design->HideFields['csu_location_id'])) {
        /** @var EntityManager $entityManager */
        $entityManager = (new DoctrineEntityManagerFactory())->getInstance();
        $userRepository = $entityManager->getRepository(UserEntity::class);
        $user = (new UserSessionFactory())->create()->getCurrentUser();

        if (!isset($user)) {
            return $design;
        }

        $userID = $user->getVars()['recordid'];

        /** @var UserEntity $user */
        $userEntity = $userRepository->find($userID);
        $locations = $userEntity->getLocations();

        // If multiple locations have been set, do not populate as it is not possible to determine which location to use
        if ($locations->count() == 1) {
            $design->DefaultValues['csu_location_id'] = $locations[0]->getId();
        }
    }

    return $design;
}
