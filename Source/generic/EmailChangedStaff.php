<?php

use app\services\approvalStatus\ApprovalStatus;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use src\email\EmailSenderFactory;
use src\framework\session\UserSessionFactory;
use src\logger\Facade\Log;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;
use src\users\model\User;
use src\users\model\UserModelFactory;
use src\users\specifications\EmailableUserSpecification;

/**
 * Checks investigators to be emailed prior to data persistence.
 *
 * @return false|string[]|void
 */
function investigatorsToEmail(array $data, string $module)
{
    global $ModuleDefs;

    if (!isset($ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS'])) {
        return;
    }

    // Check for staff to e-mail if the Investigators field has been edited
    // and is not empty.
    if ($data['CHANGED-' . $ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']] && $data[$ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']] != '') {
        if ($data[$ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']]) {
            $Investigators = explode(' ', $data[$ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']]);
        }

        // Get the value of inc_investigator already in the database, i.e.
        // the old value.
        $OriginalInvestigator = DatixDBQuery::PDO_fetch(
            'SELECT ' . $ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS'] .
            ' FROM ' . $ModuleDefs[$module]['TABLE'] . ' WHERE recordid = :recordid',
            ['recordid' => $data['recordid']],
            PDO::FETCH_COLUMN,
        );

        if ($OriginalInvestigator) {
            $OriginalInvestigators = explode(' ', $OriginalInvestigator);
        }

        // If the field was empty, e-mail everyone in the new list of
        // investigators
        if (empty($OriginalInvestigators)) {
            $EmailInvestigators = $Investigators;
        } else {
            // Loop through the list of investigators, adding only those
            // which were not in the old list to the array of staff to be
            // e-mailed.
            foreach ($Investigators as $Inv) {
                if (!in_array($Inv, $OriginalInvestigators)) {
                    $EmailInvestigators[] = $Inv;
                }
            }
        }

        return $EmailInvestigators;
    }
}

/**
 * @desc  Function EmailNewInvestigators() is called when a record is saved, but
 * before the record is saved to the incidents_main table. It checks to see
 * if the inc_investigator field has been edited and, if it has, sends an
 * e-mail of type NewInvestigator to each new member of staff that has been
 * added to this field.
 *
 * @param $EmailInvestigators
 */
function EmailNewInvestigators(array $data, string $module, $EmailInvestigators): void
{
    if ($data['rep_approved'] === ApprovalStatus::DRAFT) {
        return;
    }

    if (!empty($EmailInvestigators)) {
        $Factory = new UserModelFactory();

        // Loop through the array, sending e-mails to all those members
        // who have an e-mail address. Also, add all the User entity
        // properties to the $data array so things like fullname
        // can be used in the e-mail template if required.
        $emailSender = EmailSenderFactory::createEmailSender($module, 'NewInvestigator');

        $emailableUserSpecification = new EmailableUserSpecification();
        $loggedInUser = (new UserSessionFactory())->create()->getCurrentUser();

        foreach ($EmailInvestigators as $Initials) {
            /** @var User $StaffDetails */
            $StaffDetails = $Factory->getMapper()->findByInitials($Initials);

            if (!$StaffDetails instanceof User) {
                Log::info('Unable to find investigator with the initials: ' . $Initials);

                return;
            }

            if ($emailableUserSpecification->isSatisfiedBy($StaffDetails, $loggedInUser)) {
                Log::info('E-mail new Investigator added to record ' . $data['recordid'] . ' in module ' . $module . '.');
                $emailSender->addRecipient($StaffDetails);
            }
        }
        $emailSender->sendEmails(array_merge($StaffDetails->getVars(), $data));
    }
}

/**
 * Fires a notification e-mail to the new staff member if the staff field has changed.
 *
 * @throws InvalidParameterException
 * @throws MapperException
 */
function emailNewStaff(array $data, string $module, string $staffField, string $emailType): void
{
    if ($data['rep_approved'] === ApprovalStatus::DRAFT) {
        return;
    }

    // Don't bother sending an e-mail if the field is empty
    if ($data['CHANGED-' . $staffField] && $data[$staffField] != '') {
        $Factory = new UserModelFactory();
        $StaffDetails = $Factory->getMapper()->findByInitials(
            $data[$staffField],
            true,
        );

        $emailableUserSpecification = new EmailableUserSpecification();
        $loggedInUser = (new UserSessionFactory())->create()->getCurrentUser();

        if ($emailableUserSpecification->isSatisfiedBy($StaffDetails, $loggedInUser)) {
            Log::info('Emailing ' . $emailType . ' on record ' . $data['recordid'] . ' in module ' . $module . '.');

            $emailSender = EmailSenderFactory::createEmailSender($module, $emailType);
            $emailSender->addRecipient($StaffDetails);
            $emailSender->sendEmails(array_merge($StaffDetails->getVars(), $data));
        }
    }
}

/**
 * @desc This is called when any record is saved, but
 * before the record is saved to the incidents_main table. It checks to see
 * if the inc_mgr (normally called Handler) field has been edited and, if it
 * has, sends an e-mail of type NewHandler to the new member of staff that has
 * been added to this field.
 *
 * @param $data
 * @param $module
 *
 * @throws InvalidParameterException
 * @throws MapperException
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 */
function EmailNewHandler($data, $module): void
{
    if (savedToNonMailableStatus($data, $module)) {
        return;
    }

    $moduleDefs = Container::get(ModuleDefs::class);

    $handlerField = $moduleDefs[$module]['FIELD_NAMES']['HANDLER'] ?? null;
    if ($handlerField) {
        emailNewStaff($data, $module, $handlerField, 'NewHandler');
    }
}

function savedToNonMailableStatus(array $data, string $module): bool
{
    $statusBeingSavedTo = $data['rep_approved'] ?? null;

    if ($statusBeingSavedTo === null) {
        return true;
    }

    if (!in_array($statusBeingSavedTo, [ApprovalStatus::DRAFT, ApprovalStatus::REJECTED], true)) {
        return false;
    }

    $recordId = $data['recordid'] ?? '';
    Log::info('E-mail new handler email aborted for record ' .
        $recordId .
        ' in module ' .
        $module .
        ' due to the record being saved in a non mailable status.');

    return true;
}
