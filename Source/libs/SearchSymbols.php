<?php

use app\models\treeFields\TreeFieldQueryBuilder;
use app\models\treeFields\TreeFieldQueryBuilderFactory;

function GetLastWeekDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $nWeekStartDay = GetParm('WEEK_START_DAY', '2') - 1;
    $nWeekCurrentDay = $dtToday['wday'];

    if ($nWeekCurrentDay >= $nWeekStartDay) {
        $dtEnd = mktime(0, 0, 0, $dtToday['mon'], $dtToday['mday'] - ($nWeekCurrentDay - $nWeekStartDay) - 1, $dtToday['year']);
    } else {
        $dtEnd = mktime(0, 0, 0, $dtToday['mon'], $dtToday['mday'] - ($nWeekStartDay - $nWeekCurrentDay) - 2, $dtToday['year']);
    }
    $dtStartDate = getdate($dtEnd);
    $dtStart = mktime(0, 0, 0, $dtStartDate['mon'], $dtStartDate['mday'] - 6, $dtStartDate['year']);
}

function TranslateDateRangeCom($sWhere, $sCommand, $dtStart, $dtEnd)
{
    $StartDateTime = date('Y-m-d 00:00:00', $dtStart);
    $EndDateTime = date('Y-m-d 23:59:59', $dtEnd);

    preg_match_all("/(CAST\( FLOOR\( CAST\([ ])([^ ()]*)([ ]AS FLOAT \) \) AS DATETIME\))[ ]?[><=]*[ ]?[\'\"]+({$sCommand})[\'\"]+/iu", $sWhere, $Matches);

    foreach ($Matches[0] as $id => $Match) {
        $sFieldName = $Matches[1][$id] . $Matches[2][$id] . $Matches[3][$id];

        $sWhere = str_replace($Match, $sFieldName . " >= '{$StartDateTime}' AND " . $sFieldName . " <= '{$EndDateTime}'", $sWhere);
    }

    preg_match_all("/([^ ()]*)[ ]?[><=]*[ ]?[\'\"]+({$sCommand})[\'\"]+/iu", $sWhere, $Matches);

    foreach ($Matches[0] as $id => $Match) {
        $sFieldName = $Matches[1][$id];

        if ($sFieldName != 'like') { // need to deal with cases where this has been put into a non-date field.
            $sWhere = str_replace($Match, $sFieldName . " >= '{$StartDateTime}' AND " . $sFieldName . " <= '{$EndDateTime}'", $sWhere);
        }
    }

    return $sWhere;
}

function GetLastMonthDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $dtEnd = mktime(0, 0, 0, $dtToday['mon'], 0, $dtToday['year']);
    $dtStart = mktime(0, 0, 0, $dtToday['mon'] - 1, 1, $dtToday['year']);
}

function GetLastQuarterDateRange($dtToday, &$dtStart, &$dtEnd)
{
    if ($dtToday['mon'] >= 10) {
        $QtrStartMonth = 7;
        $QtrYear = $dtToday['year'];
    } elseif ($dtToday['mon'] >= 7) {
        $QtrStartMonth = 4;
        $QtrYear = $dtToday['year'];
    } elseif ($dtToday['mon'] >= 4) {
        $QtrStartMonth = 1;
        $QtrYear = $dtToday['year'];
    } elseif ($dtToday['mon'] >= 1) {
        $QtrStartMonth = 10;
        $QtrYear = $dtToday['year'] - 1;
    }

    $dtStart = mktime(0, 0, 0, $QtrStartMonth, 1, $QtrYear);
    $dtEnd = mktime(0, 0, 0, $QtrStartMonth + 3, 0, $QtrYear);
}

function GetLastYearDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $dtStart = mktime(0, 0, 0, 1, 1, $dtToday['year'] - 1);
    $dtEnd = mktime(0, 0, 0, 12, 31, $dtToday['year'] - 1);
}

function GetWeekDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $nWeekStartDay = GetParm('WEEK_START_DAY', '2') - 1; // we count sunday as "1", where php counts it as "0"
    $nWeekCurrentDay = $dtToday['wday'];

    if ($nWeekCurrentDay >= $nWeekStartDay) {
        $dtStart = mktime(0, 0, 0, $dtToday['mon'], $dtToday['mday'] - ($nWeekCurrentDay - $nWeekStartDay), $dtToday['year']);
    } else {
        $dtStart = mktime(0, 0, 0, $dtToday['mon'], $dtToday['mday'] - (7 - $nWeekStartDay + $nWeekCurrentDay), $dtToday['year']);
    }
    $dtEndDate = getdate($dtStart);
    $dtEnd = mktime(0, 0, 0, $dtEndDate['mon'], $dtEndDate['mday'] + 6, $dtEndDate['year']);
}

function GetMonthDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $dtStart = mktime(0, 0, 0, $dtToday['mon'], 1, $dtToday['year']);
    $dtEnd = mktime(0, 0, 0, $dtToday['mon'] + 1, 0, $dtToday['year']);
}

function GetQuarterDateRange($dtToday, &$dtStart, &$dtEnd)
{
    if ($dtToday['mon'] >= 10) {
        $QtrStartMonth = 10;
    } elseif ($dtToday['mon'] >= 7) {
        $QtrStartMonth = 7;
    } elseif ($dtToday['mon'] >= 4) {
        $QtrStartMonth = 4;
    } elseif ($dtToday['mon'] >= 1) {
        $QtrStartMonth = 1;
    }

    $dtStart = mktime(0, 0, 0, $QtrStartMonth, 1, $dtToday['year']);
    $dtEnd = mktime(0, 0, 0, $QtrStartMonth + 3, 0, $dtToday['year']);
}

function GetYearDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $dtStart = mktime(0, 0, 0, 1, 1, $dtToday['year']);
    $dtEnd = mktime(0, 0, 0, 12, 31, $dtToday['year']);
}

/**
 * Get date range for the financial year.
 *
 * @param array $dtToday the date used to determine which financial year to return (not necessarily today)
 * @param int $dtStart start date, passed by reference and set within this function
 * @param int $dtEnd end date, passed by reference and set within this function
 */
function GetFinYearDateRange($dtToday, &$dtStart, &$dtEnd)
{
    $nFinMonthStart = GetParm('FINYEAR_START_MONTH', '4');

    if ($dtToday['mon'] < $nFinMonthStart) {
        // The current financial year started last calendar year
        --$dtToday['year'];
    }

    $dtStart = mktime(0, 0, 0, $nFinMonthStart, 1, $dtToday['year']);
    $dtEnd = mktime(0, 0, 0, $nFinMonthStart + 12, 0, $dtToday['year']);
}

function TranslateConCode($where, $initials, $module = '')
{
    global $FieldDefsExtra;

    $currentSymbol = '';
    $sSymbol = '@USER_';
    $nTagPos = \UnicodeString::stripos($where, $sSymbol);

    while ($nTagPos !== false) {
        $CurrentCharacter = $where[$nTagPos];

        while (!empty($CurrentCharacter)) {
            if (preg_match("/^(\s)|(\))|(\')|(,)$/u", $CurrentCharacter)) {
                break;
            }
            $currentSymbol .= $CurrentCharacter;
            $nTagPos = $nTagPos + 1;
            $CurrentCharacter = $where[$nTagPos];
        }

        $treeFieldSearchCodes = array_keys(TreeFieldQueryBuilder::ENTITIES);
        $currentSymbol = \UnicodeString::strtoupper($currentSymbol);

        if (in_array($currentSymbol, $treeFieldSearchCodes)) {
            $treeFieldQueryBuilder = (new TreeFieldQueryBuilderFactory())->create();

            $where = $treeFieldQueryBuilder->generateWhere($where, $currentSymbol, $initials);
        } else {
            $CurrentField = \UnicodeString::strtolower(\UnicodeString::str_ireplace('@USER_', '', $currentSymbol));

            if (!array_key_exists($CurrentField, $FieldDefsExtra['USE'])) {
                $CurrentData["{$CurrentField}"] = '[INVALID USER SYMBOL/COLUMN: ' . $CurrentField . ']';
            } elseif ($initials != null) {
                $sql = "SELECT CAST({$CurrentField} AS nvarchar(max)) AS {$CurrentField}
                    FROM users_main
                    WHERE initials = '{$initials}'";
                $result = db_query($sql);
                $CurrentData = db_fetch_array($result);
            } else {
                // if initials are blank, we are validating @user_con codes and so this gives us the right data
                $CurrentData = [];
            }

            if ($CurrentData["{$CurrentField}"] == '') {
                // preg_match("/([A-Za-z_.]*)[\s]*(like|LIKE|=)[\s]*('" . $currentSymbol . "'|" . $currentSymbol . ")/i",$where,$Field);
                $where = preg_replace("/([A-Za-z_.]*)[\s]*(like|not like|=)[\s]*('" . $currentSymbol . "'|" . $currentSymbol . ')/iu', '(1=2)', $where);
            } elseif (isset($FieldDefsExtra['CON'][$CurrentField]['Type'], $CurrentData[$CurrentField]) &&
                $FieldDefsExtra['CON'][$CurrentField]['Type'] == 'multilistbox' &&
                $CurrentData[$CurrentField] != '')
            {
                $InList = [];
                $FieldParts = explode(' ', $CurrentData["{$CurrentField}"]);
                // Try and get the field name of the source field in order to determine whether it is a multicode
                preg_match("/(?P<leftfield>\w+)[\s]*(like|LIKE|=)[\s]*'" . $currentSymbol . "'/iu", $where, $matches);

                if (\UnicodeString::strtolower($matches['leftfield']) == 'udv_string') {
                    preg_match('/field_id = ([0-9]+) AND/iu', $where, $udfmatches);
                    $udf = new Fields_ExtraField($udfmatches[1]);
                    if ($udf->getFieldType() == 'T') {
                        $udfmulticode = true;
                    }
                }

                if ($FieldDefsExtra[$module][\UnicodeString::strtolower($matches['leftfield'])]['Type'] == 'multilistbox' || $udfmulticode) {
                    if (count($FieldParts) > 0) {
                        foreach ($FieldParts as $code) {
                            $codewhere = \UnicodeString::str_ireplace($currentSymbol, $code, $where);
                            $InList[] = '(' . $codewhere . ')';
                        }
                    }
                    $where = implode(' OR ', $InList);
                } else {
                    if (count($FieldParts) > 0) {
                        foreach ($FieldParts as $code) {
                            $InList[] = "'" . $code . "'";
                        }
                    }

                    $CurrentData["{$CurrentField}"] = implode(',', $InList);
                    $CurrentDataAutoSQL = ' IN (' . implode(',', $InList) . ')';
                    $CurrentSymbolAutoSQL[] = "/[\s]*(like|LIKE|=)[\s]*'" . $currentSymbol . "'/iu";
                    $where = preg_replace($CurrentSymbolAutoSQL, $CurrentDataAutoSQL, $where, -1, $count);

                    if ($count == 0) {
                        throw new \Exception('@user_con search and replace failed.');
                    }

                    $currentSymbol = "'" . $currentSymbol . "'";
                }
            }

            $where = \UnicodeString::str_ireplace($currentSymbol, $CurrentData["{$CurrentField}"], $where);
        }

        $nTagPos = \UnicodeString::stripos($where, $sSymbol);
        $currentSymbol = '';
    }

    return $where;
}
