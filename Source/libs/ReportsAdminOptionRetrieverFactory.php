<?php

namespace Source\libs;

use app\models\framework\config\DatixConfigFactory;
use app\services\module\ModuleResolver;
use src\component\field\FieldLabelRepositoryFactory;
use src\system\container\facade\Container;
use src\system\language\LanguageServiceFactory;

/**
 * @deprecated use container instead
 *
 * @see Container::get()
 *
 * @codeCoverageIgnore
 */
class ReportsAdminOptionRetrieverFactory
{
    public function create(): ReportsAdminOptionRetriever
    {
        return new ReportsAdminOptionRetriever(
            (new LanguageServiceFactory())->create(),
            (new FieldLabelRepositoryFactory())->create(),
            (new DatixConfigFactory())->getInstance(),
            Container::get(ModuleResolver::class),
        );
    }
}
