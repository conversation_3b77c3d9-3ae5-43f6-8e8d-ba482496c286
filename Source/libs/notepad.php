<?php

use app\services\idGenerator\UpdateIdGenerator;
use src\framework\session\UserSessionFactory;

/**
 * @desc Gets the contents of the notepad field for a given record.
 *
 * @param int $recordid ID of current record
 * @param string $module Current module
 *
 * @return string contents of notepad field
 */
function GetNotepad($recordid, $module)
{
    global $ModuleDefs;

    $sql = 'SELECT notes FROM notepad WHERE ' . $ModuleDefs[$module]['FK'] . " = {$recordid}";
    $request = db_query($sql);

    if ($row = db_fetch_array($request)) {
        return $row['notes'];
    }
}

function SaveNotes($aParams)
{
    $userSession = (new UserSessionFactory())->create();
    $link_field = $aParams['id_field'];
    $module = explode('_', $link_field);
    $module = \UnicodeString::strtoupper($module[0]);
    $recordid = $aParams['id'];
    $notes = $aParams['notes'];

    if (isset($notes) && $_POST['CHANGED-notes'] != '') {
        $PDOParamsArray['recordid'] = $recordid;
        $sql = "SELECT {$link_field}, notes, updateid FROM notepad WHERE {$link_field} = :recordid";

        $Query = new DatixDBQuery($sql);
        $request = $Query->prepareAndExecute($PDOParamsArray);
        $row = $Query->fetch();

        if (!$request) {
            $error = "An error has occurred when trying to save the notes attached to this record. Please report the following to the Datix administrator: {$sql}";
        } else {
            $currentUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;


            if ($row) {
                $oldNotes = $row['notes'];  // for audit table

                $sql = "UPDATE notepad SET
                updatedby = :updatedby,
                updateddate = :updateddate,
                updateid = :updateid,
                notes = :notes
                WHERE {$link_field} = :recordid";

                $PDOParamsArray['updatedby'] = $currentUserInitials;
                $PDOParamsArray['updateid'] = (new UpdateIdGenerator())->generateUpdateId($row['updateid'] ?? '');
                $PDOParamsArray['updateddate'] = date('d-M-Y H:i:s');
                $PDOParamsArray['notes'] = $notes;
            } elseif ($notes != '') {
                $sql = "INSERT INTO notepad ({$link_field}, notes, updateddate, updatedby, updateid)
                VALUES (:recordid, :notes, :updateddate, :updatedby, :updateid)";

                $PDOParamsArray['updatedby'] = $currentUserInitials;
                $PDOParamsArray['updateid'] = (new UpdateIdGenerator())->generateUpdateId('');
                $PDOParamsArray['updateddate'] = date('d-M-Y H:i:s');
                $PDOParamsArray['notes'] = $notes;
            }

            $request = DatixDBQuery::PDO_query($sql, $PDOParamsArray);

            if (!$request) {
                $error = "An error has occurred when trying to save the notes attached to this record. Please report the following to the Datix administrator: {$sql}";
            } elseif (!$aParams['new']) {
                $delegateId = $userSession->getDelegator();

                $PDOAuditParams['module'] = $module;
                $PDOAuditParams['recordid'] = $recordid;
                $PDOAuditParams['updatedby'] = $PDOParamsArray['updatedby'];
                $PDOAuditParams['updateddate'] = $PDOParamsArray['updateddate'];
                $PDOAuditParams['aud_detail'] = $oldNotes;
                $PDOAuditParams['aud_action'] = 'WEB:notes';
                $PDOAuditParams['aud_delegate_id'] = $delegateId;

                $sql = 'INSERT INTO full_audit
                              (aud_module, aud_record,
                               aud_login, aud_date, aud_action,
                               aud_detail, aud_delegate_id)
                          VALUES
                              (:module, :recordid, :updatedby, :updateddate, :aud_action, :aud_detail, :aud_delegate_id)';

                $request = DatixDBQuery::PDO_query($sql, $PDOAuditParams);
            }
        }
    }

    return $error;
}
