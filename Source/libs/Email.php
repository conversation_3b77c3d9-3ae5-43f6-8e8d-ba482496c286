<?php

use app\models\accessLevels\AccessLevelResolver;
use app\models\treeFields\TreeFieldQueryBuilder;
use app\services\approvalStatus\ApprovalStatus;
use app\services\securityGroups\SecurityGroupServiceFactory;
use Ramsey\Uuid\Uuid;
use src\contacts\model\ContactModelFactory;
use src\email\EmailSender;
use src\email\EmailSenderFactory;
use src\email\EmailTypes;
use src\email\service\EmailNotificationSenderFactory;
use src\framework\query\Query;
use src\framework\query\SqlWriter;
use src\framework\registry\Registry;
use src\helpers\SqlInClauseHelper;
use src\logger\DatixLogger;
use src\system\container\ContainerFactory;
use src\system\container\facade\Container;
use src\system\database\field\TreeField;
use src\system\database\FieldInterface;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\Module;
use src\system\moduledefs\ModuleDefs;
use src\users\model\UserModelFactory;

/**
 * @desc Returns all users who have emailing enabled and an email address but are not in a security group.
 *
 * @param array $aParams Array of parameters
 *
 * @return array Array of users to email
 */
function GetUsersToEmail($aParams)
{
    $module = $aParams['module'];
    $values = $aParams['values'];
    $where = $aParams['where'];

    $contacts = [];

    $email_param = [
        'INC' => 'DIF_STA_EMAIL_LOCS',
        'COM' => 'COM_STA_EMAIL_LOCS',
        'CLA' => 'CLA_STA_EMAIL_LOCS',
    ];

    if (!array_key_exists($module, $email_param)) {
        return null;
    }

    // Only select contacts/users who are not also member of any security group of any type
    $sql = '
        SELECT
            distinct users_main.recordid AS use_id' . ($values ? ", {$values}" : '') . "
        FROM
            users_main
        JOIN user_parms p ON users_main.login = p.login
        WHERE
            use_email != '' AND use_email IS NOT NULL
            AND users_main.active = 1
            AND p.parameter = '{$email_param[$module]}'
            AND p.parmvalue = 'Y'
            AND NOT EXISTS (
                SELECT * FROM sec_staff_group
                JOIN sec_groups ON sec_staff_group.grp_id = sec_groups.recordid
                WHERE sec_staff_group.use_id = users_main.recordid
            )
            AND
            (
                users_main.sta_profile IS NULL
                OR users_main.sta_profile = ''
                OR NOT EXISTS (
                    SELECT * FROM link_profile_group
                    WHERE lpg_profile = users_main.sta_profile
                )
            )
    ";

    if ($where) {
        $sql .= " AND ({$where})";
    }

    $contactList = DatixDBQuery::PDO_fetch_all($sql, []);

    foreach ($contactList as $contact) {
        $contacts = $contacts + [$contact['use_id'] => $contact];
    }

    return $contacts;
}

/**
 * @desc Returns all contacts who have emailing enabled and an email address but are not in a security group.
 *
 * @param array $aParams Array of parameters
 *
 * @return array Array of groups to email
 */
function GetGroupsToEmail($aParams)
{
    $group_type = $aParams['group_type'] ?: GRP_TYPE_EMAIL;

    $sql = 'SELECT recordid from sec_groups
            where sec_groups.grp_type = (grp_type | ' . $group_type . ')';

    return DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_COLUMN);
}

/**
 * @param array $aParams Array of parameters
 *
 * @return array|string HTML output to show to the user
 */
function SendEmails($aParams)
{
    $registry = Container::get(Registry::class);
    $moduleDefs = Container::get(ModuleDefs::class);

    /** @var string $output */
    $output = '';

    if ($aParams['to'] === ApprovalStatus::DRAFT) {
        return $output;
    }

    $sentList = $aParams['notList'] ?? [];

    if (empty($aParams['source'])) {
        $aParams['source'] = EmailSender::SRC_INLINE;
    }
    /** @var DatixLogger $logger */
    $logger = (new ContainerFactory())->create()['logger'];
    $logger->info('SendEmails() has been called with parameters', ['aParams' => $aParams]);

    $sql = '
        SELECT
            apac_email_reporter,
            apac_email_handler,
            access_level
        FROM
            approval_action
        WHERE
            apac_from = :apac_from
            AND apac_to = :apac_to
            AND module = :module
            AND apac_workflow = :apac_workflow';

    $result = DatixDBQuery::PDO_fetch_all(
        $sql,
        [
            'apac_from' => $aParams['from'],
            'apac_to' => $aParams['to'],
            'module' => $aParams['module'],
            'apac_workflow' => GetWorkflowID($aParams['module']),
        ],
    );

    $userAccessLevels = [$aParams['perms']];

    if (
        !empty($_SESSION['CurrentUser'])
        && $registry->getParm('USE_ADVANCED_ACCESS_LEVELS', 'N')->isTrue()
    ) {
        $userAccessLevels = $_SESSION['CurrentUser']->getAccessLevels($aParams['module']);
    }

    $shouldEmailReporter = false;
    $shouldEmailHandler = false;

    foreach ($result as $row) {
        // does any of the workflow access levels match the
        // current user access levels?
        if (in_array($row['access_level'], $userAccessLevels, true)) {
            if (bYN($row['apac_email_reporter'])) {
                $shouldEmailReporter = true;
            }

            if (bYN($row['apac_email_handler'])) {
                $shouldEmailHandler = true;
            }
        }
    }

    $errors = [];
    $successfulEmails = [];

    $notificationUsersToEmail = getNotificationEmailsRecipientList($aParams);

    $shouldEmailLevel1Reporter =
        $shouldEmailReporter
        && (
            $aParams['level'] == 1
            && $registry->getParm($moduleDefs[$aParams['module']]['EMAIL_REPORTER_GLOBAL'], '', true)->isTrue()
        );

    $shouldEmailLevel2Reporter =
        $shouldEmailReporter
        && $aParams['level'] == 2
        && $registry->getParm('REP_EMAIL_' . $aParams['module'] . '_2')->isTrue();

    if (($shouldEmailLevel1Reporter || $shouldEmailLevel2Reporter) && $aParams['falseSend'] !== true) {
        SendEmailToReporter($aParams, $errors, $successfulEmails);
    }

    $whoEmailGlobal = $registry->getParm('WHO_EMAIL_' . $aParams['module'] . '_2')->toArray();
    $shouldEmailLevel1Handler = $shouldEmailHandler && ($aParams['level'] == 1
            && $registry->getParm($moduleDefs[$aParams['module']]['EMAIL_HANDLER_GLOBAL'])->isTrue());
    $shouldEmailLevel2Handler = $aParams['level'] == 2
        && in_array('HNDLR', $whoEmailGlobal, true);

    if (($shouldEmailLevel1Handler || $shouldEmailLevel2Handler) && $aParams['falseSend'] !== true) {
        $handlerSent = SendEmailToHandler($aParams, $errors, $successfulEmails, $notificationUsersToEmail);

        if ($handlerSent) {
            $sentList = array_merge($sentList, $handlerSent);
        }
    }

    // assume handler and manager get emailed at the same stages
    if (
        (bYN($row['apac_email_handler']) && ($aParams['level'] == 2 && in_array('MNGR', $whoEmailGlobal, true)))
        && $aParams['falseSend'] !== true
    ) {
        $managerSent = SendEmailToManager($aParams, $errors, $successfulEmails, $notificationUsersToEmail);

        if ($managerSent) {
            $sentList = array_merge($sentList, $managerSent);
        }
    }

    $emailNotificationSender = (new EmailNotificationSenderFactory())->create();

    $repApproved = $aParams['data']['rep_approved'];
    $isRejectTemplateIdSet = $registry->getParm('EMT_' . $aParams['module'] . '_REJ')->isNotEmpty();
    $ncEnabled = $registry->getParm('NOTIFICATIONS_CENTRE', 'N')->isTrue();

    if ($shouldEmailReporter && $repApproved == 'REJECT' && ($isRejectTemplateIdSet || $ncEnabled)) {
        $emailNotificationSender->sendRejectionEmailToReporter($aParams);
    }

    SendNotificationEmails($notificationUsersToEmail, $emailSender, $aParams, $errors, $successfulEmails, $sentList, $aParams['emailType']);

    if (!empty($successfulEmails)) {
        $output = '
        <div class="padded_wrapper">' . _fdtk('email_sent_message') . '</div>
        <div class="padded_wrapper"><div>' . implode('</div><div>', array_unique($successfulEmails)) . '</div></div>';
    }

    if (!empty($errors)) {
        $output .= '
        <div class="padded_wrapper form_error"><div>' . implode('</div><div>', $errors) . '</div></div>';
    }

    return !$aParams['falseSend'] ? $output : $successfulEmails;
}

/**
 * @desc Takes a list of groups and decides wheich of their contacts should be emailed, returning this list as an array
 *
 * @param array $aParams Array of parameters
 *
 * @return array Contacts to email
 */
function GetUsersToEmailFromGroups(array $aParams): array
{
    $moduleDefs = Container::get(ModuleDefs::class);
    $inClauseHelper = new SqlInClauseHelper();

    $userIdsToEmail = [];

    $groups = $aParams['groups'];
    $module = $aParams['module'];
    $moduleName = $moduleDefs[$module]['REC_NAME'] ?? '';
    $data = $aParams['data'];
    if (!isset($data['recordid']) && isset($aParams['recordid'])) {
        $data['recordid'] = $aParams['recordid'];
    }

    $Table = $moduleDefs[$module]['TABLE'];

    /** @var DatixLogger $logger */
    $logger = (new ContainerFactory())->create()['logger'];
    $context = [
        'task-id' => Uuid::uuid4()->toString(),
    ];
    $start = microtime(true);

    if (is_array($groups) && !empty($groups)) {
        // Loop through the groups, checking the WHERE clauses match the incident.
        foreach ($groups as $grp_id) {
            $groupStart = microtime(true);
            $context['group-id'] = $grp_id;
            $logger->info('Processing group ' . $grp_id . ' to decide whether members should be emailed for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);

            // We need to reset this where clause here otherwise other security groups will use previously values
            $joinGroupWhere = '';

            $securityGroupService = (new SecurityGroupServiceFactory())->create();
            $group_where = $securityGroupService->makeGroupSecurityWhereClause($module, $grp_id, '', '', true);

            if ($group_where == '') {
                $logger->info('Group ' . $grp_id . ' does not have a where clause, so no emails are being sent for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);

                continue; // the group does not have a where clause - no point in continuing.
            }

            // We can check the group clause at this point unless it has @USER codes, in which case it will need to
            // be checked on a user level.
            $RequireUserCheck = (mb_substr_count($group_where, '@USER') > 0 || mb_substr_count($group_where, '@user') > 0);

            if (!$RequireUserCheck) {
                $group_where = TranslateWhereCom($group_where);

                $sql = "SELECT recordid FROM {$Table}
                WHERE recordid = {$data['recordid']} AND {$group_where}";

                try {
                    $resultArray = DatixDBQuery::PDO_fetch_all($sql);
                } catch (Exception $e) {
                    throw new Exception('Invalid security group (id ' . $grp_id . ')', 0, $e);
                }

                if (count($resultArray) == 0) {
                    $logger->info('Group ' . $grp_id . ' does not match the record that was submitted, so no emails are being sent for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);

                    continue;  // the group does not match this record - no point in continuing.
                }
            }

            // We need to process every contact, in case there are contact-specific options set
            $userIdsToEmailFromThisGroup = GetSecurityGroupUsers($grp_id);

            if (empty($userIdsToEmailFromThisGroup)) {
                $logger->info('Group ' . $grp_id . ' contains no contacts, so no emails are being sent for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);

                continue; // the group contains no users - no point in continuing
            }

            $usersRestrictedByOwnOnly = [];
            if (!bYN(GetParm('EMAIL_IGNORE_OWN_ONLY', 'Y'))) {
                $usersRestrictedByOwnOnly = getUsersRestrictedByOwnOnly($userIdsToEmailFromThisGroup, $module);
            }

            $query = new Query();

            if ($RequireUserCheck) {
                $joinGroupWhere = TranslateConCodeToJoin($group_where, $module, (int) $data['recordid'], $query);
            }

            if (!empty($usersRestrictedByOwnOnly)) {
                if ($joinGroupWhere) {
                    $joinGroupWhere .= ' AND ';
                } else {
                    $joinGroupWhere = '';
                }

                $joinGroupWhere .= <<<SQL
                    (
                        (
                            {$inClauseHelper->inClause('users_main.recordid', $usersRestrictedByOwnOnly)}
                            AND
                            (
                                {$moduleDefs[$module]['FIELD_NAMES']['HANDLER']} = users_main.initials
                                OR {$moduleDefs[$module]['FIELD_NAMES']['HANDLER']} IS NULL
                                OR {$moduleDefs[$module]['FIELD_NAMES']['HANDLER']} = ''
                            )
                        )
                        OR
                        {$inClauseHelper->notInClause('users_main.recordid', $usersRestrictedByOwnOnly)}
                    )
                    SQL;
            }

            if ($joinGroupWhere) {
                $query->select(['users_main.recordid']);
                $query->join($moduleDefs[$module]['TABLE'], null, 'RIGHT', $joinGroupWhere);
                $query->setWhereStrings([$inClauseHelper->inClause('users_main.recordid', $userIdsToEmailFromThisGroup) . ' AND ' . $moduleDefs[$module]['TABLE'] . '.recordid = ' . $data['recordid']]);
                $query->orderBy(['users_main.recordid']);
                $query->overrideSecurity();

                [$statement, $parameters] = Container::get(SqlWriter::class)->writeStatement($query);

                try {
                    $userIdsToEmailFromThisGroup = DatixDBQuery::PDO_fetch_all($statement, $parameters, PDO::FETCH_COLUMN);
                } catch (DatixDBQueryException $e) {
                    $userIdsToEmailFromThisGroup = [];
                    $logger->error('SQL error when processing email security group, no emails sent', $context);
                }
            }

            if (empty($userIdsToEmailFromThisGroup)) {
                $logger->info('No users will be emailed due to membership of group ' . $grp_id . ' for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);
            } else {
                $context['users'] = $userIdsToEmailFromThisGroup;
                $logger->info('User will be emailed due to membership of group ' . $grp_id . ' for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context);
            }

            // array_merge won't cut this as we don't want duplicate IDs.
            foreach ($userIdsToEmailFromThisGroup as $userId) {
                $userIdsToEmail[$userId] = $userId;
            }

            $elapsedSeconds = microtime(true) - $groupStart;
            $logger->info('Finished processing group ' . $grp_id . ' for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context + ['elapsed-seconds' => $elapsedSeconds]);
        }

        unset($context['group-id']);
        $elapsedSeconds = microtime(true) - $start;
        $logger->info('Finished processing all groups  for ' . $moduleName . ' with ID ' . $data['recordid'] . '.', $context + ['total-elapsed-seconds' => $elapsedSeconds]);
    }

    return $userIdsToEmail;
}

function getUsersRestrictedByOwnOnly($sourceList, $module)
{
    $moduleDefs = Container::get(ModuleDefs::class);

    // we need to add the logic around OWN_ONLY that happens in MakeToplevelSecurityWhereClause(), but fit it in with the join.
    // First get all contacts with OWN_ONLY set.
    $ownOnlyContacts = GetAllContactsWithOwnOnlySet($module);

    $noOwnOnlyAccessLevels = [
        'INC' => ['RM', 'DIF1'],
        'COM' => ['COM2', 'COM1'],
        'ACT' => [],
    ];

    $usersToCheck = [];

    $accessLevelResolver = Container::get(AccessLevelResolver::class);
    // Select set of users for which we need to check that they meet the own-only restriction.
    foreach ($sourceList as $userRecordid) {
        if (in_array($userRecordid, $ownOnlyContacts)) {
            $userAccessLevels = $accessLevelResolver->getUserAccessLevels($userRecordid);

            if ($userAccessLevels[$moduleDefs[$module]['PERM_GLOBAL']] && !in_array($userAccessLevels[$moduleDefs[$module]['PERM_GLOBAL']], $noOwnOnlyAccessLevels)) {
                $usersToCheck[] = $userRecordid;
            }
        }
    }

    return $usersToCheck;
}

/**
 * Converts @user codes into usable query conditions.
 */
function TranslateConCodeToJoin(string $where, string $module, int $recordId, Query $query): string
{
    $where = translateStaticAtCodes($where);

    $registry = Container::get(Registry::class);
    $fieldDefs = $registry->getFieldDefs();
    $moduleDef = $registry->getModuleDefs()[$module];

    $logger = (new ContainerFactory())->create()['logger'];

    preg_match_all('/\'% @user_([^ \']+)\'/ui', $where, $matches);

    if (!empty($matches)) {
        foreach ($matches[0] as $key => $match) {
            $where = UnicodeString::str_ireplace($match, '\'% \' + users_main.' . $matches[1][$key], $where);
        }
    }

    preg_match_all('/\'@user_([^ \']+) %\'/ui', $where, $matches);

    if (!empty($matches)) {
        foreach ($matches[0] as $key => $match) {
            $where = UnicodeString::str_ireplace($match, 'users_main.' . $matches[1][$key] . ' + \' %\'', $where);
        }
    }

    preg_match_all('/\'% @user_([^ \']+) %\'/ui', $where, $matches);

    if (!empty($matches)) {
        foreach ($matches[0] as $key => $match) {
            $where = UnicodeString::str_ireplace($match, '\'% \' + users_main.' . $matches[1][$key] . ' + \' %\'', $where);
        }
    }

    // See if there are any '@user codes' in the query
    preg_match_all('/\'@user_([^ \']+)\'/ui', $where, $matches);
    // If there are any @user codes in the query, then replace them with their corresponding field name
    if (!empty($matches)) {
        $treeFieldSearchCodes = array_keys(TreeFieldQueryBuilder::ENTITIES);

        foreach ($matches[0] as $key => $match) {
            $code = strtoupper(substr($match, 1, -1));
            if (in_array($code, $treeFieldSearchCodes)) {
                $where = buildTreeFieldQuery($moduleDef, $recordId, $query, $where, $matches, $key, $code);

                continue;
            }

            if ($fieldDefs['users_main.' . $matches[1][$key]] instanceof FieldInterface) {
                // If the field name in the $fieldDefs object says that the field is of type MULTICODE, then try to generate a
                // more complicated replacement pattern, otherwise just use the @user codes and replace it with it's corresponding field name
                if (in_array($fieldDefs['users_main.' . $matches[1][$key]]->getType(), [FieldInterface::MULTICODE, FieldInterface::TREE])) {
                    // Attempt to extract any 'multicodes' from the where clause, so we can formulate a more complex replacement string
                    preg_match("/([\.A-Za-z_]+)[\s]*(not like|like|!=|=)[\s]*'@user_" . $matches[1][$key] . "'/iu", $where, $multicodeMatches);

                    if (!empty($multicodeMatches)) {
                        // If either of the fields are empty, a join should never be created.
                        $replacement = '(
                        users_main.' . $matches[1][$key] . ' != \'\'
                        AND users_main.' . $matches[1][$key] . ' IS NOT NULL
                        AND ' . $multicodeMatches[1] . ' != \'\'
                        AND ' . $multicodeMatches[1] . ' IS NOT NULL
                        AND
                        (';

                        if (in_array($multicodeMatches[2], ['not like', 'NOT LIKE', '!='])) {
                            // When either the contacts or the record have blank values, there should be no match.
                            $replacement .= '
                        users_main.' . $matches[1][$key] . ' NOT LIKE \'% \' + CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) + \' %\' AND
                        users_main.' . $matches[1][$key] . ' NOT LIKE CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) + \' %\' AND
                        users_main.' . $matches[1][$key] . ' NOT LIKE \'% \' + CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) AND
                        users_main.' . $matches[1][$key] . ' != CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252))';
                        } else {
                            $replacement .= '
                        users_main.' . $matches[1][$key] . ' LIKE \'% \' + CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) + \' %\' OR
                        users_main.' . $matches[1][$key] . ' LIKE CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) + \' %\' OR
                        users_main.' . $matches[1][$key] . ' LIKE \'% \' + CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252)) OR
                        users_main.' . $matches[1][$key] . ' = CAST(' . $multicodeMatches[1] . ' AS VARCHAR(252))';
                        }

                        $replacement .= '))';

                        $where = UnicodeString::str_ireplace($multicodeMatches[0], $replacement, $where);
                    }
                } else {
                    $where = UnicodeString::str_ireplace($match, 'users_main.' . $matches[1][$key], $where);
                }
            } else {
                $logger->error('Unable to interpret code when processing where clause.', [
                    'match' => $match,
                ]);
            }
        }
    }

    return $where;
}

/**
 * @param $module
 *
 * @return array
 *
 * Returns all contacts where a GetParm check of XXX_OWN_ONLY would return Y
 */
function GetAllContactsWithOwnOnlySet($module)
{
    $globalArray =
        [
            'INC' => 'DIF_OWN_ONLY',
            'COM' => 'COM_OWN_ONLY',
            'ACT' => 'ACT_OWN_ONLY',
        ];

    return DatixDBQuery::PDO_fetch_all('SELECT recordid FROM GetContactsByParameterValue(:parameter, :value)', ['parameter' => $globalArray[$module], 'value' => 'Y'], PDO::FETCH_COLUMN);
}

/**
 * @desc Appends the "Top Level" security where clause to an existing contact where clause.
 *
 * @param string $module current module
 * @param string $conId id of current contact
 * @param string $contactWhere Current contact where clause
 *
 * @return string The full contact where clause
 */
function AddTopLevelSecClause($module, $conId, $contactWhere): string
{
    $toplevelWhere = (new SecurityGroupServiceFactory())->create()->makeToplevelSecurityWhereClause($module, $conId, true, null);

    if (!$toplevelWhere) {
        return $contactWhere;
    }

    if ($contactWhere) {
        return '(' . implode('AND', [$contactWhere, $toplevelWhere]) . ')';
    }

    return $toplevelWhere;
}

/**
 * @desc Gets the handler's email address and sends an email notifying them of the submission of a record.
 *
 * @param array $aParams Array of parameters
 */
function GetContactsToEmailFromUsers($aParams)
{
    $moduleDefs = Container::get(ModuleDefs::class);

    $module = $aParams['module'] ?? '';
    $recordId = $aParams['data']['recordid'] ?? $aParams['recordid'];
    $contacts = [];

    $aParams['values'] = 'initials, use_email, use_jobtitle, fullname, users_main.login as login';

    // Get all contacts whose email option is switched on
    $contacts_tmp = GetUsersToEmail($aParams);

    if (is_array($contacts_tmp) && !empty($contacts_tmp)) {
        // For each user with e-mailing enabled, check if their security WHERE clause matches the incident.
        foreach ($contacts_tmp as $con_id => $values) {
            if (array_key_exists($con_id, $contacts)) {
                continue;
            }

            $con_id = (int) $con_id;

            $contact_where = MakeUserSecurityWhereClause($module, $con_id, '', '');
            $contact_where = AddTopLevelSecClause($module, $con_id, $contact_where);

            $sql = 'SELECT recordid AS con_id
                    FROM ' . $moduleDefs[$module]['TABLE'] . '
                    WHERE recordid = :recordid';
            $sqlParams['recordid'] = $recordId;

            if ($contact_where) {
                $sql .= " AND {$contact_where}";
            }

            if (DatixDBQuery::PDO_fetch($sql, $sqlParams)) {
                $contacts[$con_id] = $con_id;
            }
        }
    }

    return $contacts;
}

function getNotificationEmailsRecipientList($aParams): array
{
    // Get all groups with email on
    $aParams['groups'] = GetGroupsToEmail($aParams);
    $moduleDefs = Container::get(ModuleDefs::class);
    $moduleName = $moduleDefs[$aParams['module']]['REC_NAME'] ?? '';
    $recordId = $aParams['data']['recordid'] ?? $aParams['recordid'];

    $usersFromGroups = GetUsersToEmailFromGroups($aParams);

    $logger = (new ContainerFactory())->create()['logger'];

    $isUsersFromGroups = count($usersFromGroups) > 0;
    $loggerMessage = 'Calculating who to email for ' . $moduleName . ' with ID ' . $recordId . '. ';
    $loggerMessage .= 'Based on groups, sending emails to ' . ($isUsersFromGroups ? 'User IDs ' . implode(', ', $usersFromGroups) : 'no-one');
    $logger->info($loggerMessage);

    if ($isUsersFromGroups) {
        $usersFromGroups = getUsersFromGroupsNotifyRecipientList($aParams, $usersFromGroups, $logger);
    }

    $contactsFromUsers = GetContactsToEmailFromUsers($aParams);
    $loggerMessage = 'Calculating who to email for ' . $moduleName . ' with ID ' . $recordId . '. ';
    $loggerMessage .= 'Based on user settings, sending emails to' .
    (count($contactsFromUsers) > 0 ?
        ': user ' . implode(', ', array_map(function ($userDetails) {
            return $userDetails['use_email'] .
            '(' . $userDetails['fullname'] . ')' . '(' . $userDetails['use_id'] . ')';
        }, $contactsFromUsers))
        : ' no-one');
    $logger->info($loggerMessage);

    // Both arrays should be keyed by user ID so may be combined using the + operator.
    return $usersFromGroups + $contactsFromUsers;
}

function getUsersFromGroupsNotifyRecipientList(array $aParams, array $usersFromGroups, DatixLogger $logger)
{
    $denyParms = $aParams;

    $denyParms['group_type'] = GRP_TYPE_DENY_ACCESS;
    $denyParms['groups'] = GetGroupsToEmail($denyParms);

    $module = $aParams['module'] ?? '';
    $recordId = $aParams['data']['recordid'] ?? $aParams['recordid'];

    $userIdsFromDenyGroups = GetUsersToEmailFromGroups($denyParms);

    if (empty($userIdsFromDenyGroups)) {
        return $usersFromGroups;
    }

    $usersFromGroupsMinusDeniedContacts = array_diff($usersFromGroups, $userIdsFromDenyGroups);

    foreach ($userIdsFromDenyGroups as $deniedContactId) {
        if (in_array($deniedContactId, $usersFromGroupsMinusDeniedContacts)) {
            $loggerMessage = 'Calculating who to email for a module ' . $module . ' and record id ' . $recordId . '. ';
            $loggerMessage .= 'Based on being in a deny group, preventing email being sent to user with id ' . $deniedContactId;
            $logger->info($loggerMessage);
        }
    }

    return $usersFromGroupsMinusDeniedContacts;
}

/**
 * @desc Gets the handler's email address and sends an email notifying them of the submission of a record.
 *
 * @param array $aParams Array of parameters
 * @param array $Errors Reference to variable tracking current errors. NOT CURRENTLY USED IN THIS FUNCTION
 * @param array $SuccessfulEmails reference to variable tracking emails sent
 * @param array $ExcludeList list of people to ignore because they have already been sent emails
 * @param array $emailType the type of email to send: 'UPDATE' or 'NEW'
 */
function SendNotificationEmails($contactsToEmail, &$emailSender, $aParams, &$Errors, &$SuccessfulEmails, $ExcludeList = [], $emailType = 'NEW')
{
    $logger = (new ContainerFactory())->create()['logger'];
    $type = $emailType == 'UPDATE' ? 'UpdatedRecord' : 'Notify';
    $emailSender = EmailSenderFactory::createEmailSender($aParams['module'], $type);
    $inClauseHelper = new SqlInClauseHelper();

    if ($aParams['source'] != null) {
        $emailSender->setSource($aParams['source']);
    }

    $contactsToEmailMinusExcluded = array_diff($contactsToEmail, $ExcludeList);
    $contactsToStillBeExcluded = array_intersect($contactsToEmail, $ExcludeList);

    $registry = Container::get(Registry::class);
    $migrateDateString = $registry->getParm('RECORD_UPDATE_EMAIL_DATETIME')->toScalar();
    $migrateDateIsValid = DateTime::createFromFormat('Y-m-d H:i:s.u', $migrateDateString) !== false;

    $insertIntoRecordUpdateEmailLog = $registry->getParm('RECORD_UPDATE_EMAIL_' . $aParams['module'], 'N')->isTrue()
        && $migrateDateIsValid;

    if (count($contactsToEmailMinusExcluded)) {
        $module = $aParams['module'] ?? '';
        $recordId = $aParams['data']['recordid'] ?? $aParams['recordid'];
        $logger->info(
            'Sending notification e-mail to contacts when saving for a module ' . $module . ' with record id ' . $recordId,
            ['id' => $contactsToEmailMinusExcluded],
        );

        if ($aParams['falseSend'] !== true) {
            $query = (new ContactModelFactory())->getQueryFactory()->getQuery();
            $query->setWhereStrings([
                $inClauseHelper->inClause('staff.recordid', $contactsToEmailMinusExcluded),
                'staff.active = 1',
            ]);
            $query->orderBy(['staff.recordid']);

            $recipientCollection = (new UserModelFactory())->getCollection();
            $recipientCollection->setQuery($query);


            $emailSender->addRecipients($recipientCollection);
            $emailSender->sendEmails($aParams['data'], $ExcludeList);

            $emailRecipients = $emailSender->getRecipients();
            foreach ($emailRecipients as $emailRecipient) {
                if (in_array((int) $emailRecipient->recordid, $emailSender->getSuccessfulRecipients())) {
                    $SuccessfulEmails[$emailRecipient->recordid] = $emailRecipient->use_jobtitle . ' ' . $emailRecipient->fullname;
                }
            }

            if ($insertIntoRecordUpdateEmailLog) {
                insertIntoRecordUpdateEmailLog(array_merge($contactsToEmailMinusExcluded, $contactsToStillBeExcluded), $aParams['data']['recordid']);
            }
        } else {
            $SuccessfulEmails = $contactsToEmailMinusExcluded;
        }
    } elseif ($insertIntoRecordUpdateEmailLog) {
        insertIntoRecordUpdateEmailLog($contactsToStillBeExcluded, $aParams['data']['recordid']);
    }
}

/**
 * @desc Gets the handler's email address and sends an email notifying them of the submission of a record.
 *
 * @param array $aParams Array of parameters
 * @param array $Errors Reference to variable tracking current errors. NOT CURRENTLY USED IN THIS FUNCTION
 * @param array $SuccessfulEmails reference to variable tracking emails sent
 * @param array $ignoreDelegationsTo a list of notification email recipients that will be sent later and that we dont want to delegate an email to
 *
 * @see sendEmailToPerson()
 */
function SendEmailToHandler(array $aParams, array &$Errors, array &$SuccessfulEmails, array $ignoreDelegationsTo): array
{
    return sendEmailToPerson(
        $aParams,
        $Errors,
        $SuccessfulEmails,
        $ignoreDelegationsTo,
        'HANDLER',
        EmailSenderFactory::createEmailSender($aParams['module'], EmailTypes::NEW_HANDLER),
    );
}

/**
 * @desc Gets the manager's email address and sends an email notifying them of the submission of a record.
 *
 * @param array $aParams Array of parameters
 * @param array $Errors Reference to variable tracking current errors. NOT CURRENTLY USED IN THIS FUNCTION
 * @param array $SuccessfulEmails reference to variable tracking emails sent
 * @param array $ignoreDelegationsTo a list of notification email recipients that will be sent later and that we dont want to delegate an email to
 *
 * @see sendEmailToPerson()
 */
function SendEmailToManager(array $aParams, array &$Errors, array &$SuccessfulEmails, array $ignoreDelegationsTo): array
{
    return sendEmailToPerson(
        $aParams,
        $Errors,
        $SuccessfulEmails,
        $ignoreDelegationsTo,
        'MANAGER',
        EmailSenderFactory::createEmailSender($aParams['module'], 'Notify'),
    );
}

/**
 * @desc Wrapper function for SendEmailToHandler() and SendEmailToManager().
 *
 * @param array $aParams Array of parameters. Expected values include:
 *                       $aParams['data'] - Data array describing the current record.
 *                       $aParams['module'] - Current module.
 * @param array $Errors Reference to variable tracking current errors. NOT CURRENTLY USED IN THIS FUNCTION
 * @param array $SuccessfulEmails reference to variable tracking emails sent
 * @param array $ignoreDelegationsTo a list of notification email recipients that will be sent later and that we dont want to delegate an email to
 * @param string $fieldName Field name being processed. Expected values are 'MANAGER' and 'HANDLER'
 */
function sendEmailToPerson(
    array $aParams,
    array &$Errors,
    array &$SuccessfulEmails,
    array $ignoreDelegationsTo,
    string $fieldName,
    EmailSender $emailSender
): array {
    $moduleDefs = Container::get(ModuleDefs::class);

    // in future, we'll need to work out which template to use here.

    $initials = $aParams['data'][$moduleDefs[$aParams['module']]['FIELD_NAMES'][$fieldName]];

    $emailSent = false;

    if ($initials) {
        $sql = <<<'SQL'
            SELECT recordid as use_id, use_email, use_jobtitle, fullname, login
            FROM users_main
            WHERE initials = :initials
            AND use_email != '' AND use_email is not null
            AND active = 1
            SQL;

        $row = DatixDBQuery::PDO_fetch($sql, ['initials' => $initials]);

        $userModelFactory = new UserModelFactory();

        if (!empty($row)) {
            $logger = (new ContainerFactory())->create()['logger'];
            $recordId = $aParams['data']['recordid'] ?? $aParams['recordid'];
            $logger->info(strtr(
                'Sending notification e-mail to !recipient: !fullname (!use_email) when saving !record_name with ID !recordid',
                [
                    '!recipient' => strtolower($fieldName),
                    '!fullname' => $row['fullname'],
                    '!use_email' => $row['use_email'],
                    '!record_name' => $moduleDefs[$aParams['module']]['REC_NAME'],
                    '!recordid' => $recordId,
                ],
            ));

            $recipient = $userModelFactory->getMapper()->findById($row['use_id'], true);

            if ($aParams['source'] !== null) {
                $emailSender->setSource($aParams['source']);
            }

            if ($recipient && $recipient->isActive()) {
                $emailSender->addRecipient($recipient);
                $emailSent = $emailSender->sendEmails($aParams['data'], $ignoreDelegationsTo);
            }

            if ($emailSent) {
                $SuccessfulEmails[$row['use_id']] = $row['use_jobtitle'] . ' ' . $row['fullname'];
            }
        }
    }

    return ($emailSent) ? $emailSender->getSuccessfulRecipients() : [];
}

function SendProgressEmailToReporters(string $module, array $recordData): void
{
    $registry = Container::get(Registry::class);
    $moduleDefs = Container::get(ModuleDefs::class);
    $emailTemplate = $registry->getParm("EMT_{$module}_PRO")->toScalar();
    $emailSender = EmailSenderFactory::createEmailSender($module, 'REPPROGRESS', $emailTemplate);
    $logger = (new ContainerFactory())->create()['logger'];
    $reportersToEmail = (new ContactModelFactory())->getMapper()->getProgressEmailRecipients($module, $recordData['recordid']);

    foreach ($reportersToEmail as $reporter) {
        $logger->info('Sending progress update notification e-mail to reporter: ' . $reporter->con_forenames . ' ' .
            $reporter->con_surname . ' (' . $reporter->con_email . ')' . ' when saving ' . $moduleDefs[$module]['REC_NAME'] .
            ' with ID ' . $recordData['recordid'], );
        $emailSender->addRecipient($reporter);
    }
    $emailSender->sendEmails($recordData);
}

/**
 * @desc Gets the reporters email address (from the posted data or from the DB) and sends an email acknowledging submission of a record.
 *
 * @param array $aParams Array of parameters
 * @param array $Errors Reference to variable tracking current errors. NOT CURRENTLY USED IN THIS FUNCTION
 * @param array $SuccessfulEmails Reference to variable tracking emails sent. NOT CURRENTLY USED IN THIS FUNCTION
 */
function SendEmailToReporter($aParams, &$Errors, &$SuccessfulEmails)
{
    $registry = Container::get(Registry::class);
    $moduleDefs = Container::get(ModuleDefs::class);

    $logger = (new ContainerFactory())->create()['logger'];

    $reporter = null;
    $user = null;

    // in future, we'll need to work out which template to use here.

    $LinkedContactFunction = ($moduleDefs[$aParams['module']]['GET_LINKED_CONTACTS_FUNCTION'] ?: 'GetLinkedContacts');
    $contacts = $LinkedContactFunction(['recordid' => $aParams['data']['recordid'], 'module' => $aParams['module'], 'formlevel' => 1], true);

    if (is_array($contacts['R'])) {
        foreach ($contacts['R'] as $OtherContact) {
            if ($OtherContact['link_role'] === $registry->getParm('REPORTER_ROLE', 'REP')->toScalar()) {
                $reporter = (new ContactModelFactory())->getMapper()->find($OtherContact['recordid'], true);
                $user = (new UserModelFactory())->getMapper()->findByContactId($OtherContact['recordid']);
            }
        }
    }

    // Get the session language for reporter e-mails, since it may have been chosen with the language select field
    $language = LanguageSessionFactory::getInstance()->getLanguage();
    if ($aParams['emailType'] === 'UPDATE') {
        $emailSender = EmailSenderFactory::createEmailSender($aParams['module'], 'UpdatedRecord', '', '', $language);
    } else {
        $emailSender = EmailSenderFactory::createEmailSender($aParams['module'], 'Acknowledge', '', '', $language);
    }

    if ($aParams['source'] !== null) {
        $emailSender->setSource($aParams['source']);
    }

    if (!$reporter && !$user) {
        if ($aParams['data']['inc_rep_email']) {
            $logger->info(strtr(
                'Sending acknowledgement e-mail to reporter: !reporter (!email) when saving !rec_name with ID !recordid',
                [
                    '!reporter' => $aParams['data']['inc_repname'],
                    '!email' => $aParams['data']['inc_rep_email'],
                    '!rec_name' => $moduleDefs[$aParams['module']]['REC_NAME'],
                    '!recordid' => $aParams['data']['recordid'],
                ],
            ));

            $emailSender->addRecipientEmail($aParams['data']['inc_rep_email']);
        }
    } elseif ($reporter && (!$user || ($user && $user->isActive()))) {
        $logger->info(strtr(
            'Sending acknowledgement e-mail to reporter: !reporter (!email) when saving !rec_name with ID !recordid',
            [
                '!reporter' => $reporter->fullname,
                '!email' => $reporter->con_email,
                '!rec_name' => $moduleDefs[$aParams['module']]['REC_NAME'],
                '!recordid' => $aParams['data']['recordid'],
            ],
        ));

        $emailSender->addRecipient($reporter);
    }

    $emailSender->sendEmails($aParams['data']);
}

/**
 * @desc Called via AJAX. Takes a module and a recordid and sends appropriate emails as though that record had just been logged.
 *
 * @throws InvalidParameterException
 */
function EmailAlertsService()
{
    $moduleDefs = Container::get(ModuleDefs::class);

    $module = Sanitize::getModule($_POST['module']);
    $moduleName = $moduleDefs[$module]['REC_NAME'] ?? '';
    $recordId = Sanitize::SanitizeInt($_POST['recordid']);
    $permissions = GetParm($moduleDefs[$module]['PERM_GLOBAL']);

    $old_approval = ($_POST['old_approval'] ? Sanitize::SanitizeString($_POST['old_approval']) : 'NEW');
    $new_approval = Sanitize::SanitizeString($_POST['approval']);
    $perms = ($permissions ?: 'NONE');

    $data = GetRecordData(['module' => $module, 'recordid' => $recordId]);

    $usersAlreadyEmailed = [];

    if ($old_approval != 'NEW') {
        $usersAlreadyEmailed = (new UserModelFactory())->getMapper()->getNotificationEmailRecipients($recordId);
        if (count($usersAlreadyEmailed) > 0) {
            $logger = (new ContainerFactory())->create()['logger'];
            $logger->info('Email already sent to User IDs ' . implode(', ', $usersAlreadyEmailed) . ' for ' . $moduleName . ' with ID ' . $recordId);
        }
    }

    SendEmails([
        'module' => $module,
        'data' => $data,
        'from' => $old_approval,
        'to' => $new_approval,
        'perms' => $perms,
        'level' => Sanitize::SanitizeInt($_POST['formlevel']),
        'notList' => $usersAlreadyEmailed,
        'emailType' => $old_approval === 'NEW' ? 'NEW' : 'UPDATE',
    ]);
}

function insertIntoRecordUpdateEmailLog(array $contactsToExclude, $inc_id)
{
    $sql = 'DELETE FROM contact_email_history WHERE inc_id = :inc_id';
    \DatixDBQuery::PDO_query($sql, ['inc_id' => $inc_id]);

    if (count($contactsToExclude) > 0) {
        $sql = "INSERT INTO contact_email_history (inc_id, con_id)\n";
        foreach ($contactsToExclude as $key => $con_id) {
            $selects[] = 'SELECT ' . $inc_id . ', ' . $con_id . " \n";
        }

        $sql .= implode("UNION ALL \n", $selects);
        \DatixDBQuery::PDO_insert($sql);
    }
}

/**
 * Builds a set of conditions to query users on the basis of their "tree field" values,
 * i.e. the location(s)/service(s) they're attached to.  Users are matched if they're
 * attached to the same value as that on the record being saved, or any of its direct
 * ancestors up the tree hierarchy.
 */
function buildTreeFieldQuery(Module $moduleDef, int $recordId, Query $query, string $where, array $matches, string $key, string $code): string
{
    preg_match("/([\.A-Za-z_]+)[\s]*(not like|like|!=|=)[\s]*'@user_" . $matches[1][$key] . "'/iu", $where, $fieldMatches);

    $mainTableFieldFqn = $fieldMatches[1];
    $mainTableField = explode('.', $mainTableFieldFqn)[1];

    // Is main table a tree field
    $registry = Container::get(Registry::class);
    $fieldDefs = $registry->getFieldDefs();
    $mainTableFieldObject = $fieldDefs[$mainTableFieldFqn];

    // If the main table field isn't a tree field, the @user code should be removed so that it doesn't interfere with
    // other code conversions needed for the query to work
    if (!$mainTableFieldObject instanceof TreeField) {
        $container = (new ContainerFactory())->create();

        /** @var DatixLogger $logger */
        $logger = $container['logger'];
        $moduleName = $moduleDef['REC_NAME'] ?? '';
        $logger->info('Non Tree Field used with @user code in Security group when trying to send emails to ' . $moduleName . ' with ID ' . $recordId);

        // Blank out the @user code for the non tree field as it's not a valid clause
        return preg_replace("/=\s+'?{$code}'?/i", "= ''", $where, 1);
    }

    $treeFieldSpec = TreeFieldQueryBuilder::ENTITIES[$code];
    $treeFieldTable = $treeFieldSpec['table'];
    $treeFieldLinkTable = $treeFieldSpec['link_table'];
    $treeFieldFk = $treeFieldSpec['link_table_fk'];
    $mainRecordTable = $moduleDef->getDbReadObj();
    $cte = "{$mainTableField}_cte";

    // Get list of existing CTEs on the query
    $cteList = $query->getCtes();

    // If no CTE exists for the main table field, add one to the query
    if (!isset($cteList[$mainTableField])) {
        // Define a recursive CTE which will list the IDs of all the ancestors up the tree hierarchy.
        // The Adjacency List rather than Nested Set approach is used here for performance reasons.
        // See: https://explainextended.com/2009/09/25/adjacency-list-vs-nested-sets-sql-server

        // Sometimes an intermediate join is required if $mainTableFieldFqn is in a table
        // linked to $mainRecordTable instead of directly on $mainRecordTable.
        // Use the magic of Query/SqlWriter to figure out the correct join to use.
        $extraJoinQuery = (new Query())
            ->select(["{$mainRecordTable}.recordid", $mainTableFieldFqn])
            ->from($mainRecordTable);
        $extraJoinWriter = Container::get(SqlWriter::class);
        $extraJoinSql = $extraJoinWriter->combineSqlAndParameters(...$extraJoinWriter->writeJoinClause($extraJoinQuery));

        $query->addCte($cte, "
            SELECT {$treeFieldTable}.id, {$treeFieldTable}.parent_id
            FROM {$mainRecordTable}
            {$extraJoinSql}
            INNER JOIN {$treeFieldTable} ON {$mainTableFieldFqn} = {$treeFieldTable}.id
            WHERE {$mainRecordTable}.recordid = {$recordId}
            UNION ALL
            SELECT ancestors.id, ancestors.parent_id
            FROM {$cte}
            INNER JOIN {$treeFieldTable} ancestors
            ON ancestors.id = {$cte}.parent_id
        ");
    }

    // Filter e-mail recipients based on the hierarchy path provided by the CTE
    $sql = 'IS NOT NULL AND users_main.recordid IN (' .
        'SELECT DISTINCT users_id ' .
        "FROM {$treeFieldLinkTable} " .
        "WHERE {$treeFieldFk} IN (SELECT id FROM {$cte})" .
        ')';

    // Replace the reference to the @user code in the original where clause
    return preg_replace("/=\s+'?{$code}'?/i", $sql, $where, 1);
}
