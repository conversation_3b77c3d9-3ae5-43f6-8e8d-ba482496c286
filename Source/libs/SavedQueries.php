<?php

use app\models\location\services\TreeFieldService;
use app\models\reporting\AtCodes;
use app\services\forms\PageTitleProvider;
use src\actiontriggers\model\ActionTriggerModelFactory;
use src\admin\services\CapturePermissionService;
use src\admin\services\CapturePermissionServiceFactory;
use src\component\field\DateFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\component\form\FormTableMakeUdfService;
use src\formdesign\forms\service\Loaders\FormDesignInstanceLoader;
use src\framework\controller\Loader;
use src\framework\controller\Request;
use src\framework\controller\Response;
use src\framework\model\Entity;
use src\framework\query\Query;
use src\framework\query\Where;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\framework\session\WhereClauseSessionFactory;
use src\generic\services\QueryValidationService;
use src\logger\Facade\Log;
use src\savedqueries\model\SavedQuery;
use src\savedqueries\model\SavedQueryModelFactory;
use src\search\atprompt\AtPromptSessionFactory;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\database\extrafield\ExtraField;
use src\system\database\FieldInterface;
use src\system\moduledefs\ModuleDefs;
use src\users\model\UserModelFactory;

/**
 * @return never
 */
function NewSavedQuery(): void
{
    $FormAction = Sanitize::SanitizeString($_GET['form_action']);

    $loader = new Loader();
    $controller = $loader->getController(
        ['controller' => src\savedqueries\controllers\ShowSavedQueriesTemplateController::class],
    );
    $controller->setRequestParameter('formAction', $FormAction);
    echo $controller->doAction('showSavedQueryForm');
    obExit();
}

/**
 * @return never
 */
function SaveQuery(): void
{
    global $scripturl;

    if ($_POST['rbWhat'] == 'cancel') {
        if ($_POST['form_action'] == 'new') {
            if ($_POST['module'] == 'CON') {
                $location = "{$scripturl}?action=listcontacts&module=CON";
            } else {
                $location = "{$scripturl}?action=list&module=" . $_POST['module'] . '&listtype=search';
            }
        } else {
            $location = "{$scripturl}?action=savedqueries&module=" . $_POST['module'];
        }

        Container::get(Response::class)->redirect($location);
    } elseif ($_POST['rbWhat'] == 'delete') {
        DeleteQuery();
    } else {
        SaveQueryToMain();
    }
}

/**
 * @return never
 */
function SaveQueryToMain(): void
{
    global $scripturl, $ModuleDefs;

    LoggedIn();

    $whereClauseSession = (new WhereClauseSessionFactory())->create();
    $qry_id = Sanitize::SanitizeInt($_POST['qry_recordid']);
    $sq_name = $_POST['sq_name'];

    $ErrorMark = '<span style="color: red; font-size: 10px;">*</span>';

    if ($_POST['sq_name'] == '') {
        AddValidationMessage('sq_name', _fdtk('validation_message_empty_query_name'));
        $error = true;
    } else {
        if ($_POST['qry_recordid']) {
            $sql = 'SELECT recordid as qry_recordid, sq_name, sq_where, sq_tables, sq_module, sq_user_id
                  FROM queries
                  WHERE recordid = :recordid';
            $qry_original = DatixDBQuery::PDO_fetch($sql, ['recordid' => $_POST['qry_recordid']]);
        }

        if ($qry_original['sq_name'] != $sq_name) {
            $sql = 'select sq_name from queries where sq_name = :sq_name';
            $qrycheckexists = DatixDBQuery::PDO_fetch($sql, ['sq_name' => $sq_name]);

            if ($qrycheckexists) {
                AddValidationMessage('sq_name', 'The name provided already exists. Please use another name.');
                $error = true;
            }
        }
    }

    $qry = $_POST;

    $registry = Container::get(Registry::class);
    if ($registry->getParm('BLOCK_SQL_EDITING')->isTrue()) {
        $qry['sq_where'] = $whereClauseSession->getSavedQueryWhereClause();
        $whereClauseSession->unsetSavedQueryWhereClause();
    }

    $validator = new QueryValidationService();

    if (!$validator->validateWhereClause($_POST['sq_where'], Sanitize::getModule($qry['module']))) {
        AddValidationMessage('sq_where', 'The \'WHERE\' statement provided contains a syntax error.');
        $error = true;
    }

    if ($_POST['qry_type'] == '' || $_POST['qry_type'] == 'ALL') {
        $user_id = null;
    } else {
        $user_id = $_SESSION['contact_login_id'];
    }

    if ($error) {
        AddSessionMessage('ERROR', _fdtk('form_errors'));
        $loader = new Loader();
        $controller = $loader->getController(
            ['controller' => src\savedqueries\controllers\ShowSavedQueriesTemplateController::class],
        );
        $controller->setRequestParameter('error', $error);
        echo $controller->doAction('showSavedQueryForm');
        obExit();
    }

    if ($qry['table'] == 'holding') {
        $qry_type = 'WH';
    } else {
        $qry_type = 'W';
    }

    $qrycheck = '';

    if ($qry_id) {
        $sql = 'select sq_name, sq_where from queries where recordid = :recordid';
        $qrycheck = DatixDBQuery::PDO_fetch($sql, ['recordid' => $qry_id]);
    }

    $PDOParams = [
        'sq_name_w' => $sq_name,
        'user_id' => $user_id,
        'module' => $qry['module'],
        'sq_tables' => $qry['sq_tables'],
        'sq_where' => $qry['sq_where'],
        'qry_type' => $qry_type,
    ];

    if (!$qrycheck) {
        $sql = 'INSERT INTO queries (
           sq_name, sq_user_id, sq_module, sq_tables, sq_where, sq_type)
           VALUES (
           :sq_name_w, :user_id, :module, :sq_tables, :sq_where, :qry_type)';

        $query_id = DatixDBQuery::PDO_insert($sql, $PDOParams);
    } else {
        $sql = 'UPDATE queries SET
               sq_name = :sq_name_w,
               sq_user_id = :user_id,
               sq_module = :module,
               sq_tables = :sq_tables,
		       sq_where = :sq_where,
               sq_type = :qry_type
               WHERE
               recordid = :recordid';

        $PDOParams['recordid'] = $qry['qry_recordid'];

        DatixDBQuery::pdo_query($sql, $PDOParams);
    }

    if ($_POST['qry_recordid'] == '') {
        $factory = new SavedQueryModelFactory();

        if ($_POST['CHANGED-sq_where'] == '') {
            // The where clause has not been altered, so we can attempt to persist a SavedQuery properly.
            if ($_SESSION[$_POST['module']]['DRILL_QUERY'] instanceof SavedQuery) {
                // The current search has been procuced by drilling into a report.
                $savedQuery = $_SESSION[$_POST['module']]['DRILL_QUERY'];

                if ($savedQuery->where_string != '') {
                    // The original saved query used by the report was an "old" query, and so will comprise of a where_string
                    // representing the original query and a Where object representing the drill-in criteria.  In this situation we have to
                    // save the criteria as a string, so we overwrite the where_string with the posted value, which is the string representation of
                    // the old query plus the additional drill-in criteria (the Where object is removed in this process).
                    $savedQuery->where_string = $_POST['sq_where'];
                }

                $savedQuery->name = $_POST['sq_name'];
                $savedQuery->user_id = $user_id;
                $savedQuery->old_query_id = $query_id;
            } elseif ($_SESSION[$_POST['module']]['NEW_WHERE'] instanceof Where) {
                $queryData = [
                    'module' => $_POST['module'],
                    'name' => $_POST['sq_name'],
                    'user_id' => $user_id,
                    'where' => $_SESSION[$_POST['module']]['NEW_WHERE'],
                    'old_query_id' => $query_id,
                ];

                $savedQuery = $factory->getEntityFactory()->createObject($queryData);
            } else {
                saveWhereAsString($user_id, $query_id, $factory);
            }
        } else {
            saveWhereAsString($user_id, $query_id, $factory);
        }

        if (isset($savedQuery)) {
            $factory->getMapper()->save($savedQuery);
        }
    } else {
        // Attempt to find a "new" SavedQuery linked to the "old" one.
        $factory = new SavedQueryModelFactory();
        $savedQuery = $factory->getMapper()->findByOldQueryID($_POST['qry_recordid']);

        if ($savedQuery !== null) {
            // Update basic properties.
            $savedQuery->name = $_POST['sq_name'];
            $savedQuery->user_id = $user_id;

            if ($qrycheck['sq_where'] != $_POST['sq_where'] && $registry->getParm('BLOCK_SQL_EDITING')->isFalse()) {
                // The where clause has been manually edited, so we have to save it as a string.
                $savedQuery->where_string = $_POST['sq_where'];
            }

            $factory->getMapper()->save($savedQuery);
        }
    }

    AddSessionMessage('INFO', _fdtk('query_save_message') . Escaper::escapeForHTML($sq_name));

    if ($_POST['form_action'] == 'new') {
        if ($qry['module'] == 'CON') {
            $location = "{$scripturl}?action=listcontacts&module=CON&fromsearch=1";
        } else {
            $location = "{$scripturl}?action=list&module=" . $qry['module'] . '&table=' . $qry['table'] . '&fromsearch=1&listtype=search';
        }
    } else {
        $location = "{$scripturl}?action=savedqueries&module=" . $_POST['module'];
    }

    Container::get(Response::class)->redirect($location);
}

/**
 * @return never
 */
function DeleteQuery(): void
{
    global $scripturl;

    LoggedIn();

    $query_recordid = Sanitize::SanitizeInt($_POST['qry_recordid']);
    $AdminUser = $_SESSION['AdminUser'];
    $module = Sanitize::SanitizeString($_POST['module']);

    $Factory = new UserModelFactory();
    $loggedInUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;
    $User = $Factory->getMapper()->findByInitials($loggedInUserInitials);

    $hasPermissionToDelete = (new CapturePermissionServiceFactory())
        ->create()
        ->hasFullAccessToModule($module, CapturePermissionService::SAVED_QUERIES);

    $Perms = ParsePermString($User->permission);

    $allowSetup = ($Perms[$module]['disallow_setup'] === false);

    if ($query_recordid) {
        // Is this user the owner of the query?
        $sql = 'SELECT 1 FROM QUERIES WHERE recordid = :recordid AND sq_user_id = (SELECT recordid FROM staff WHERE initials = :initials)';

        $isOwner = (\DatixDBQuery::PDO_fetch($sql, [
            'recordid' => Sanitize::SanitizeInt($_POST['qry_recordid']),
            'initials' => $User->initials,
        ], \PDO::FETCH_COLUMN) == '1');

        if ($hasPermissionToDelete || $AdminUser || $allowSetup || $isOwner) {
            DatixDBQuery::static_beginTransaction();

            $result = removeActionTriggers($query_recordid);
            if (!$result) {
                handleDeleteFailure($query_recordid, 'Error deleting Action Triggers');
            }

            $sql = 'DELETE FROM queries
                   WHERE
                   recordid = :recordid';

            $result = \DatixDBQuery::PDO_query($sql, ['recordid' => $query_recordid]);
            if (!$result) {
                handleDeleteFailure($query_recordid, 'Error deleting queries record');
            }

            $factory = new SavedQueryModelFactory();
            $mapper = $factory->getMapper();
            $savedQuery = $mapper->findByOldQueryID($query_recordid);

            if ($savedQuery !== null) {
                $result = $factory->getMapper()->delete($savedQuery);
                if (!$result) {
                    handleDeleteFailure($query_recordid, 'Error deleting SavedQuery entity');
                }
            }

            $module = Sanitize::SanitizeString($_POST['module']);
            $pinnedGlobal = GetParm($module . '_SAVED_QUERIES', false);

            if ($pinnedGlobal != '') {
                $pinnedQueries = explode(' ', $pinnedGlobal);

                foreach ($pinnedQueries as $id => $queryId) {
                    if ($queryId == $query_recordid) {
                        unset($pinnedQueries[$id]);
                    }
                }

                $pinnedQueries = implode(' ', Sanitize::SanitizeStringArray($pinnedQueries));

                SetGlobal($module . '_SAVED_QUERIES', $pinnedQueries, true);
            }

            DatixDBQuery::static_commit();
        }
    }

    AddSessionMessage('INFO', _fdtk('query_delete_message'));

    $location = "{$scripturl}?action=savedqueries&module=" . Sanitize::getModule($_POST['module']);
    Container::get(Response::class)->redirect($location);
}

function handleDeleteFailure($queryId, $message)
{
    Log::error("Unable to delete Saved Query ID {$queryId}: {$message}");
    DatixDBQuery::static_rollBack();
    fatal_error(_fdtk('query_delete_error'));
}

function SavedQueriesSection($qry, $FormAction)
{
    $saved_queries = get_saved_queries(Sanitize::getModule($_GET['module']));

    if (!$saved_queries) {
        $saved_queries = [];
    }

    $CTable = FormTableFactory::create();

    if ($error) {
        $CTable->Contents .= '<span style="color: red;">' . $error['message'] . '</span><br /><br />';
    }

    $field = SelectFieldFactory::createSelectField('qry_recordid', $_GET['module'], $qry['qry_recordid'], '');
    $field->setCustomCodes($saved_queries);
    $CTable->makeRow('<label for="qry_recordid_title">' . _fdtk('query') . '</label>' . GetValidationErrors($qry, 'qry_recordid'), $field);

    $CTable->makeTable();

    echo '
        <tr>
            <td>' . $CTable->getFormTable() . '</td>
        </tr>
    ';
}

function CheckForPrompt($Query, $Module)
{
    if ($Query) {
        // make sure carriage returns are removed from where clause as these can interfere with the pattern matching
        $Query = preg_replace("/[\n\r]/u", ' ', $Query);

        // Match all fields using @prompt
        preg_match_all("/((CAST\( FLOOR\( CAST\([ ])([^ ()]*)([ ]AS FLOAT \) \) AS DATETIME\))|udf_values WHERE field_id = (\d+) AND group_id = (\d+) AND mod_id = (\d+) AND( | CAST\( FLOOR\( CAST\( | \()(udv_string|udv_date)( | AS FLOAT \) \) AS DATETIME\) )|([^ ()]*))[ ]?([><=]*|like)[ ]?[\'\"]+(@PROMPT|@prompt)[\'\"]+/iu", $Query, $matches);

        $TmpMatchArray = ProcessPromptMatch($Module, $matches, $Query);
        $DupCheckList = [];

        $MatchArray = [];
        foreach ($TmpMatchArray as $MatchFieldData) {
            if (!in_array($MatchFieldData['field'], $DupCheckList)) {
                $MatchArray[] = $MatchFieldData;
                $DupCheckList[] = $MatchFieldData['field'];
            }
        }

        return $MatchArray;
    }

    return false;
}

function ProcessPromptMatch($Module, $Matches, $fullQuery = '')
{
    $moduleDefs = Container::get(ModuleDefs::class);
    $fieldDefs = Container::get(Registry::class)->getFieldDefs();

    $MatchArray = [];

    $typeMap = [
        FieldInterface::DATE => 'date',
        FieldInterface::CODE => 'ff_select',
        FieldInterface::MULTICODE => 'multilistbox',
        FieldInterface::YESNO => 'yesno',
    ];

    if (!empty($Matches)) {
        foreach ($Matches[3] as $id => $Field) {
            $TempArray = [];
            $isUDF = false;

            if ($Field == '') {
                if ($Matches[5][$id] != '') {
                    $Field = $Matches[9][$id];
                    $isUDF = true;
                } elseif (preg_match('/^udf_\d+\.udv_string$/', $Matches[11][$id]) === 1) {
                    // "new" style non-date UDFs are captured in a different regex section
                    $isUDF = true;
                    $Field = $Matches[11][$id];
                    $Matches = extractUdfProperties($Field, $Matches, $id, $fullQuery);
                } else {
                    $Field = $Matches[1][$id];
                    $Matches[3][$id] = $Matches[1][$id];
                    $isUDF = false;
                }
            }

            if (preg_match('/^udf_\d+\.udv_date$/', $Field) === 1) {
                // "new" style UDF date queries match this section of the regex, so handle here
                $isUDF = true;
                $Matches = extractUdfProperties($Field, $Matches, $id, $fullQuery);
            }

            if ($isUDF) {
                // build UDF field name (need to look up group (any group for this field) and type)
                // UDF_<Type>_<GroupID>_<FieldID>
                $FieldID = $Matches[5][$id];
                $GroupID = $Matches[6][$id];

                if ($GroupID == 0) {
                    $sql = "SELECT fld_type, 0 as group_id FROM udf_fields WHERE recordid = {$FieldID}";
                } else {
                    $sql = "SELECT uf.fld_type, ul.group_id
                            FROM udf_fields uf
                            INNER JOIN udf_links ul ON uf.recordid = ul.field_id
                            WHERE uf.recordid = '{$FieldID}'";
                }

                $result = db_query($sql);

                if ($row = db_fetch_array($result)) {
                    if (in_array($row['fld_type'], ['D', 'C', 'T', 'Y'])) {
                        $table = $Matches[14][$id] ?: $moduleDefs[$Module]->getDbReadObj();

                        $TempArray['field'] = $table . '.' . 'UDF_' . $row['fld_type'] . '_' . $GroupID . '_' . $FieldID;
                        $TempArray['type'] = $row['fld_type'];
                        $TempArray['matchstring'] = $Matches[0][$id];

                        // Keep the original match string to be used later to replace in the where string the @prompt codes
                        $TempArray['originalmatchstring'] = $Matches[0][$id];

                        if ($TempArray['type'] == 'T') {
                            if (UnicodeString::substr_count($TempArray['matchstring'], 'prompt')) {
                                $promptReplace = '@prompt';
                            } else {
                                $promptReplace = '@PROMPT';
                            }

                            $TempArray['matchstring'] .= " OR {$Field} like '{$promptReplace} %' OR {$Field} like '% {$promptReplace}' OR {$Field} like '% {$promptReplace} %'";
                        }

                        if (isset($Matches[15][$id])) {
                            // this is a "new" style query, so we need to keep track of the alias used to join on udf_values
                            $TempArray['udf_alias'] = $Matches[15][$id];
                        }

                        $MatchArray[] = $TempArray;
                    }
                }
            } else {
                if (null !== ($fieldObj = $fieldDefs[$Field])
                    && in_array($fieldObj->getType(), AtCodes::PROMPT_VALID_TYPES)
                ) {
                    $TempArray['field'] = $Field;
                    $TempArray['type'] = $typeMap[$fieldObj->getType()];
                    $TempArray['matchstring'] = $Matches[0][$id];

                    // Keep the original match string to be used later to replace in the where string the @prompt codes
                    $TempArray['originalmatchstring'] = $Matches[0][$id];

                    if ($TempArray['type'] == 'multilistbox') {
                        if (UnicodeString::substr_count($TempArray['matchstring'], 'prompt')) {
                            $promptReplace = '@prompt';
                        } else {
                            $promptReplace = '@PROMPT';
                        }

                        $TempArray['matchstring'] .= " OR {$Field} like '{$promptReplace} %' OR {$Field} like '% {$promptReplace}' OR {$Field} like '% {$promptReplace} %'";
                    }

                    $MatchArray[] = $TempArray;
                }
            }
        }
    }

    return $MatchArray;
}

/**
 * Determines the extra field identity info from a string-based query
 * for "new" style (i.e. generated with the Query class) extra field searches.
 *
 * @param string $field
 * @param int $id
 * @param string $query
 *
 * @return array
 */
function extractUdfProperties($field, array $matches, $id, $query)
{
    $tableAlias = explode('.', $field)[0];
    $udfProperties = [];

    $matchFound = preg_match("/(\w+)\.recordid = {$tableAlias}\.cas_id AND {$tableAlias}\.mod_id = '(\d+)' AND {$tableAlias}\.field_id = '(\d+)' AND {$tableAlias}\.group_id = '(\d+)/i", $query, $udfProperties);

    if ($matchFound === 1) {
        // field_id
        $matches[5][$id] = $udfProperties[3];

        // group_id
        $matches[6][$id] = $udfProperties[4];

        // mod_id
        $matches[7][$id] = $udfProperties[2];

        // table prefix
        $matches[14][$id] = $udfProperties[1];

        // udf join alias
        $matches[15][$id] = $tableAlias;
    }

    return $matches;
}

/**
 * @deprecated
 *
 * TODO ensure this function is superfluous and remove
 *
 * @return never
 */
function GetPromptData($aParams = []): void
{
    global $dtxtitle, $scripturl;

    $params = [];

    foreach ($_GET as $key => $value) {
        $params[$key] = Sanitize::SanitizeString($value);
    }

    $ListUrlString = http_build_query($params);

    $qry_recordid = is_numeric($params['query']) ? (int) $params['query'] : '';

    if ($qry_recordid) {
        $sql = 'SELECT sq_name FROM queries
                WHERE recordid = ' . $qry_recordid;
        $request = db_query($sql);
        $row = db_fetch_array($request);
        $dtxtitle = $row['sq_name'];
    } else {
        $dtxtitle = 'Selection criteria';
    }

    if (!$_GET['module']) {
        $location = "{$scripturl}?" . $ListUrlString;
        Container::get(Response::class)->redirect($location);
    }

    $module = $_GET['module'] ?? '';
    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            $module,
            $dtxtitle,
        );


    GetSideMenuHTML(['module' => $_GET['module']]);

    template_header();

    echo '
    <form enctype="multipart/form-data" method="post" name="frmDocument" id="frmDocument" action="'
        . $scripturl . '?' . $ListUrlString . '">
        <input type="hidden" id="qry_recordid" name="qry_recordid" value="' . $qry_recordid . '" />';

    $Query = $_SESSION[$_GET['module']]['PROMPT']['ORIGINAL_WHERE'];

    $MatchArray = CheckForPrompt($Query, $aParams['module']);

    foreach ($MatchArray as $Match) {
        $MatchedFields[] = RemoveTableFromFieldName($Match['field']);
    }

    foreach (Sanitize::SanitizeStringArray($_POST) as $key => $val) {
        if (!in_array($key, $MatchedFields) && !in_array(str_replace('_start', '', $key), $MatchedFields) && !in_array(str_replace('_end', '', $key), $MatchedFields)) {
            echo '<input type="hidden" id="' . Escape::EscapeEntities($key) . '" name="' . Escape::EscapeEntities($key) . '" value="' . Escape::EscapeEntities($val) . '" />';
        }
    }

    if (empty($MatchArray)) {
        echo '
            <div>
                Redirecting...
            </div>
            <script language="Javascript">jQuery("#frmDocument").submit()</script>';
    } else {
        echo '
                <div class="external_border">
                    <div class="internal_border">
                        <a name="datix-content" id="datix-content"></a>
                        <ol>
                            <li name="prompt_title_row" id="prompt_title_row" class="section_title_row">
                                <h2>
                                    <div class="section_title_group">
                                        <div class="section_title">In order to proceed with your request, you must provide further information.  Please complete the fields below and press Continue:</div>
                                    </div>
                                </h2>
                            </li>';

        foreach ($MatchedFields as $Field) {
            echo getPromptFields($Field);
        }

        echo '
                        </ol>';

        echo '
                        <div class="button_wrapper">
                            <input type="hidden" name="promptsubmitted" value="1" />
                            <input type="button" name="confirmDates" value="Continue" onClick="document.forms[0].submit()"/>';

        if ($_GET['action'] == 'reports2') {
            $predefined = ($_GET['form'] == 'predefined');
            echo '              <input type="button" name="cancelRecordButton" value="' . _fdtk('btn_cancel') . '" onClick="SendTo(\'' . $scripturl . '?action=listmyreports&module=' . Sanitize::getModule($_REQUEST['module']) . ($predefined ? '&form=predefined' : '') . '\');" />';
        } else {
            echo '              <input type="button" name="cancelRecordButton" value="' . _fdtk('btn_cancel') . '" onClick="SendTo(\'' . $scripturl . '?module=' . Sanitize::getModule($_GET['module']) . '\');" />';
        }

        echo '
                        </div>
                    </div>
    </form>';
    }

    footer();
    obExit();
}

/**
 * @deprecated
 *
 * TODO ensure this function is superfluous and remove
 */
function getPromptFields($Field)
{
    global $FieldDefsExtra;

    $formDesignLoader = Container::get(FormDesignInstanceLoader::class);

    $Module = Sanitize::getModule($_GET['module']);

    if (\UnicodeString::substr($Field, 0, 3) == 'udf') {
        $RealFieldName = \UnicodeString::strtoupper($Field);
        $Type = 'UDF';
        $FormDesign = Forms_FormDesign::GetFormDesign(['module' => $Module, 'level' => 2, 'form_type' => 'Search']);
    } else {
        $RealFieldName = RemoveTableFromFieldName($Field);
        $Type = $FieldDefsExtra[$Module][$RealFieldName]['Type'];
        $FormDesign = Forms_FormDesign::GetFormDesign(['module' => $Module, 'level' => 2, 'form_type' => 'Search']);
    }

    $formDesignLoader->load($FormDesign);

    $label = Labels_FormLabel::GetFormFieldLabel($RealFieldName, '', $Module);

    if ($label == '') {
        // need a better way to accurately determine which module each of these fields is in
        $label = Labels_FormLabel::GetFormFieldLabel($RealFieldName);
    }

    switch ($Type) {
        case 'date':
            $StartDate = DateFieldFactory::create('Search', $RealFieldName . '_start', Sanitize::SanitizeString($_POST[$RealFieldName . '_start']));

            $EndDate = DateFieldFactory::create('Search', $RealFieldName . '_end', Sanitize::SanitizeString($_POST[$RealFieldName . '_end']));

            $HTML = '<li class="field_div" name="_row" id="_row"  style="">
                         <div class="field_label_div" style="width:25%">' . $label . '</div>
                         <div class="field_input_div">&nbsp; from: ' . $StartDate->Field . '&nbsp; to: ' . $EndDate->Field . '</div>
                     </li>';

            break;
        case 'ff_select':
        case 'yesno':
        case 'multilistbox':
            if ($Field == 'rep_approved') {
                unset($GLOBALS['HideFields']['rep_approved']); // this will affect the display of the approval status field, otherwise.
                SetUpApprovalArrays($Module, $_POST, $CurrentApproveObj, $DropdownBox, 'Search', $FormDesign);
            } else {
                $DropdownBox = SelectFieldFactory::createSelectField($RealFieldName, Sanitize::SanitizeString($_GET['module']), Sanitize::SanitizeString($_POST[$RealFieldName]), FormTable::MODE_SEARCH);
            }

            $HTML = '<li class="field_div" name="_row" id="_row"  style="">
                         <div class="field_label_div" style="width:25%">' . $label . '</div>
                         <div class="field_input_div">' . $DropdownBox->getField() . '</div>
                     </li>';

            break;
        case 'UDF':
            $UDFArray = explode('_', $RealFieldName);
            $sql = "SELECT fld_name FROM udf_fields WHERE recordid = {$UDFArray[3]}";
            $row = db_fetch_array(db_query($sql));

            if ($UDFArray[1] == 'D') {
                $StartDate = DateFieldFactory::create('Search', $RealFieldName . '_start', Sanitize::SanitizeString($_POST[$RealFieldName . '_start']));

                $EndDate = DateFieldFactory::create('Search', $RealFieldName . '_end', Sanitize::SanitizeString($_POST[$RealFieldName . '_end']));

                $HTML = '<li class="field_div" name="_row" id="_row"  style="">
                            <div class="field_label_div" style="width:25%">' . $label . '</div>
                            <div class="field_input_div">&nbsp; from: ' . $StartDate->Field . '&nbsp; to: ' . $EndDate->Field . '</div>
                         </li>';
            } else {
                $formTableMakeUdfService = new FormTableMakeUdfService(new Request(), FormTable::MODE_SEARCH);
                $Form = FormTableFactory::create();
                $Form->FormMode = FormTable::MODE_SEARCH;
                $UDFField = $formTableMakeUdfService->makeUDFField($RealFieldName, $UDFArray[3], $UDFArray[1], '', $label);
                $Form->makeRow($label, $UDFField);

                $HTML = $Form->Contents;
            }

            break;
    }

    return $HTML;
}

/**
 * @deprecated
 *
 * TODO ensure this function is superfluous and remove
 */
function DoPromptSection($aParams)
{
    global $scripturl, $ModuleDefs;

    $sModule = $aParams['module'];

    $MatchArray = CheckForPrompt($_SESSION[$sModule]['PROMPT']['ORIGINAL_WHERE'], $sModule);

    if (!empty($MatchArray)) {
        // because the "original where" value gets changed too often, we need another variable to track the where clause containing @prompt
        $_SESSION[$sModule]['PROMPT']['CURRENT_QUERY_ORIGINAL_WHERE'] = $_SESSION[$sModule]['PROMPT']['ORIGINAL_WHERE'];

        $_GET['module'] = $sModule;

        if (is_array($_SESSION[$sModule]['PROMPT']['VALUES']) && !empty($_SESSION[$sModule]['PROMPT']['VALUES'])) {
            // if at least one value is set, we can use the previous values.
            foreach ($_SESSION[$sModule]['PROMPT']['VALUES'] as $Value) {
                if (is_array($Value)) {
                    // dates will have a "start" and "end" entry
                    foreach ($Value as $ValDate) {
                        if ($ValDate != '' && $ValDate != 'NULL') {
                            $ValueSet = true;
                        }
                    }
                } elseif ($Value != '') {
                    $ValueSet = true;
                }
            }
        }

        if (!isset($_POST['promptsubmitted']) && !$ValueSet) {
            $PromptComplete = false;
        } else {
            $PromptComplete = true;
        }

        if ($PromptComplete) {
            $_SESSION[$sModule]['PROMPT']['NEW_WHERE'] = replaceAtPrompts($_SESSION[$sModule]['PROMPT']['ORIGINAL_WHERE'], $sModule, $MatchArray);
        }

        if (!$PromptComplete) {
            $_GET['module'] = $sModule;
            GetPromptData($aParams);
        }
    } else {
        $_SESSION[$sModule]['PROMPT']['NEW_WHERE'] = '';
    }
}

/**
 * Replaces occurances of @prompt within a where string.
 *
 * @param string $whereString the where clause
 * @param string $module the module context
 * @param array $MatchArray an array of @prompt matches
 *
 * @return string $whereString the where clause with the @prompts replaced
 */
function replaceAtPrompts($whereString, $module, array $MatchArray = [])
{
    $registry = Container::get(Registry::class);
    $ModuleDefs = $registry->getModuleDefs();
    $fieldDefs = $registry->getFieldDefs();
    $table = $ModuleDefs[$module]->getDbReadObj();
    $treeFieldService = new TreeFieldService();

    if (empty($MatchArray)) {
        $MatchArray = CheckForPrompt($whereString, $module);
    }

    if (!empty($MatchArray)) {
        $atPromptValues = (new AtPromptSessionFactory())->create()->get($module);

        foreach ($MatchArray as $Match) {
            $Field = $Match['field'];

            if (null === ($fieldDef = $fieldDefs[$Field])) {
                continue;
            }

            $isUDF = $fieldDef instanceof ExtraField;

            $valueKey = $fieldDef->getTable() . '.' . $fieldDef->getName();
            if ($fieldDef->getFieldset() > 0) {
                $valueKey = $fieldDef->getFieldset() . '.' . $valueKey;
            }

            $atPromptValue = $atPromptValues[$valueKey];

            if ($isUDF) {
                // we need to convert the field name to its udf_values equivalent
                $Field = ($fieldDef->getType() == FieldInterface::DATE) ? 'udv_date' : 'udv_string';

                if (isset($Match['udf_alias'])) {
                    // prepend the udf join alias for "new" style queries
                    $Field = $Match['udf_alias'] . '.' . $Field;
                }
            }

            switch ($fieldDef->getType()) {
                case FieldInterface::DATE:
                    if (UnicodeString::substr_count($atPromptValue['start'], '@') > 0 || UnicodeString::substr_count($atPromptValue['end'], '@') > 0) {
                        $PromptComplete = false;
                        AddSessionMessage('ERROR', 'Error with \'' . $fieldDef->getLabel() . '\' field : You may not use \'@\' codes in @PROMPT fields');
                    } else {
                        $start = UserDateToSQLDate(Sanitize::SanitizeString($atPromptValue['start']));
                        $end = UserDateToSQLDate(Sanitize::SanitizeString($atPromptValue['end']));

                        if ($start == '@DATE_ERROR') {
                            $PromptComplete = false;
                            AddMangledSearchError($fieldDef->getName(), Sanitize::SanitizeString($atPromptValue['start']));

                            break;
                        }

                        if ($end == '@DATE_ERROR') {
                            $PromptComplete = false;
                            AddMangledSearchError($fieldDef->getName(), Sanitize::SanitizeString($atPromptValue['end']));

                            break;
                        }

                        $FieldWithCasting = 'CAST( FLOOR( CAST( ' . $Field . ' AS FLOAT ) ) AS DATETIME)';

                        $NewWhere[] = '1=1';

                        if ($start !== null) {
                            $NewWhere[] = '(' . $FieldWithCasting . ' >= \'' . $start . '\')';
                        }

                        if ($end !== null) {
                            $NewWhere[] = '(' . $FieldWithCasting . ' <= \'' . $end . '\')';
                        }

                        if ($isUDF) {
                            $replacement = implode(' AND ', $NewWhere);
                            if ($replacement != '1=1') {
                                // we need to retain the beginning portion of the matched string
                                // which contains the udf_values field_id and mod_id
                                $ReplaceWith = preg_replace('/CAST.*/u', $replacement, $Match['matchstring']);
                            } else {
                                $ReplaceWith = '1=1';
                                $aux = str_replace("'", "\'", $Match['matchstring']);
                                $aux = str_replace('(', "\(", $aux);
                                $aux = str_replace(')', "\)", $aux);

                                $pattern = '/\(' . $table . '\.recordid IN \(SELECT cas_id FROM ' . $aux . '\){2}/iu';

                                if (preg_match($pattern, $whereString, $result) != false) {
                                    $Match['matchstring'] = $result[0];
                                }
                            }
                        } else {
                            $ReplaceWith = implode(' AND ', $NewWhere);
                        }

                        unset($NewWhere);
                        $whereString = UnicodeString::str_ireplace($Match['matchstring'], $ReplaceWith, $whereString);
                    }

                    break;
                case FieldInterface::MULTICODE:
                    if (UnicodeString::substr_count($atPromptValue, '@') > 0) {
                        $PromptComplete = false;
                        AddSessionMessage('ERROR', 'Error with \'' . $fieldDef->getLabel() . '\' field : You may not use \'@\' codes in @PROMPT fields');
                    } else {
                        if ($atPromptValue) {
                            $searchArray = explode('|', Sanitize::SanitizeString($atPromptValue));
                            $replacement = '';

                            foreach ($searchArray as $value) {
                                $replacement .= "{$Field} = '{$value}' OR {$Field} like '{$value} %' OR {$Field} like '% {$value}' OR {$Field} like '% {$value} %' OR ";
                            }

                            $replacement = \UnicodeString::substr_replace($replacement, '', -4);
                        } else {
                            $replacement = '1=1';
                        }

                        if ($isUDF) {
                            if ($replacement != '1=1') {
                                $ReplaceWith = preg_replace("/{$Field}.*/iu", $replacement, $Match['matchstring']);
                            } else {
                                $ReplaceWith = '1=1';
                                $aux = str_replace("'", "\'", $Match['matchstring']);
                                $aux = str_replace('(', "\(", $aux);

                                $pattern = '/\(' . $table . '\.recordid IN \(SELECT cas_id FROM ' . $aux . '\){3}/iu';

                                if (preg_match($pattern, $whereString, $result) != false) {
                                    $Match['matchstring'] = $result[0];
                                }

                                // This use case is not compatible with the originalmatchstring code, I guess probably
                                // because this is earlier code and is solving the same problem in a different way.
                                $Match['originalmatchstring'] = null;
                            }
                        } else {
                            $ReplaceWith = $replacement;
                        }

                        if (stripos($whereString, $Match['matchstring']) === false && isset($Match['originalmatchstring']) && $Match['originalmatchstring'] != '') {
                            $whereString = UnicodeString::str_ireplace($Match['originalmatchstring'], $ReplaceWith, $whereString);
                        } else {
                            $whereString = UnicodeString::str_ireplace($Match['matchstring'], $ReplaceWith, $whereString);
                        }
                    }

                    break;
                default:
                    if (UnicodeString::substr_count($atPromptValue, '@') > 0) {
                        $PromptComplete = false;
                        AddSessionMessage('ERROR', 'Error with \'' . $fieldDef->getLabel() . '\' field : You may not use \'@\' codes in @PROMPT fields');
                    } else {
                        if ($atPromptValue) {
                            if ($isUDF) {
                                $replacement = $Field . ' IN (\'' . implode('\',\'', explode('|', Sanitize::SanitizeString($atPromptValue))) . '\')';
                                $ReplaceWith = preg_replace("/{$Field}.*/u", $replacement, $Match['matchstring']);
                            } else {
                                $splitValues = explode('|', Sanitize::SanitizeString($atPromptValue));

                                if ($fieldDef->getType() == FieldInterface::TREE) {
                                    $mapperType = $fieldDef->getMapperType();
                                    $splitValues = $treeFieldService->getFlatIdListIncludingChildren($mapperType, $splitValues);
                                }

                                $ReplaceWith = '(' . $Field . ' IN (\'' . implode('\',\'', $splitValues) . '\'))';
                            }
                        } else {
                            if ($isUDF) {
                                $ReplaceWith = '1=1';
                                $aux = str_replace("'", "\'", $Match['matchstring']);
                                $aux = str_replace('(', "\(", $aux);

                                $pattern = '/\(' . $table . '\.recordid IN \(SELECT cas_id FROM ' . $aux . '\){2}/iu';

                                if (preg_match($pattern, $whereString, $result) != false) {
                                    $Match['matchstring'] = $result[0];
                                }
                            } else {
                                $ReplaceWith = '1=1';
                            }
                        }

                        $whereString = str_replace($Match['matchstring'], $ReplaceWith, $whereString);
                    }
            }
        }
    }

    return $whereString;
}

/**
 * Function that gets all saved queries that are accessible to everyone.
 *
 * @param string $module the module to which the saved queries belong to
 *
 * @return array $savedQueries a list of saved queries
 */
function getSavedQueriesAccessibleToEveryone($module)
{
    $savedQueries = [];

    $sql = '
        SELECT
            recordid, sq_name
        FROM
            queries
        WHERE
            sq_module = :sq_module AND
            sq_user_id IS NULL AND
            (sq_type != :sq_type OR sq_type IS NULL)
        ORDER BY
            sq_name
    ';

    $result = DatixDBQuery::PDO_fetch_all($sql, [
        'sq_module' => $module,
        'sq_type' => 'UI',
    ]);

    foreach ($result as $row) {
        $savedQueries[$row['recordid']] = $row['sq_name'];
    }

    return $savedQueries;
}

/**
 * Saves a query the old-fashion way as a where string instead of a where object.
 *
 * @param string $user_id the id of the user that is saving the query
 * @param string $query_id the id of the query
 * @param SavedQueryModelFactory $factory the factory
 *
 * @return Entity the saved query object
 */
function saveWhereAsString($user_id, $query_id, SavedQueryModelFactory $factory)
{
    // Bah, we'll have to save the where clause as a string then.  How old-fashioned.
    $queryData = [
        'module' => $_POST['module'],
        'name' => $_POST['sq_name'],
        'user_id' => $user_id,
        'where_string' => $_POST['sq_where'],
        'old_query_id' => $query_id,
    ];

    $savedQuery = $factory->getEntityFactory()->createObject($queryData);

    return $savedQuery;
}

/**
 * @param $queryId
 *
 * @return bool
 */
function removeActionTriggers($queryId)
{
    $success = true;
    $factory = new ActionTriggerModelFactory();
    $mapper = $factory->getMapper();
    $actionTriggers = $factory->getCollection();
    $actionTriggers->setQuery((new Query())->where(['query_id' => (int) $queryId]));
    foreach ($actionTriggers as $actionTrigger) {
        $result = $mapper->delete($actionTrigger);
        $success = $success && $result;
    }

    return $success;
}
