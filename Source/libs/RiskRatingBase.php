<?php

use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageServiceFactory;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;

function GetRatingLevel()
{
    $fieldLabels = Container::get(Registry::class)->getFieldLabels();
    $language = LanguageSessionFactory::getInstance()->getLanguage();

    $module = Sanitize::SanitizeString($_REQUEST['module']);
    $consequence = Sanitize::SanitizeString($_REQUEST['consequence']);
    $likelihood = Sanitize::SanitizeString($_REQUEST['likelihood']);
    $riskRow = Sanitize::SanitizeString($_REQUEST['riskrow']);

    // Level
    $sql = "SELECT
                rating as lvl,
                colour,
                consequence,
                likelihood,
                cigd.code,
                COALESCE (cigd.description, cig.description) AS description,
                cigd.description,
                cod_web_colour
            FROM
                code_inc_grades cig
            JOIN
                ratings_map rm
            ON
                cig.code = rm.rating AND
                rm.consequence = '{$consequence}' AND
                rm.likelihood = '{$likelihood}'
            LEFT JOIN
                code_inc_grades_descr cigd
            ON
                cig.code = cigd.code AND
                cigd.language = {$language}
    ";

    $row = DatixDBQuery::PDO_fetch($sql);

    if ($row) {
        $levelDescr = $row['description'];
        $levelValue = $row['code'];
        $bgColor = 'style="background-color: #' . $row['cod_web_colour'] . '"';
    }

    $table = Container::get(ModuleDefs::class)[$module]['TABLE'];

    $levelFieldNameSuffix = '_grade';
    if ($module == 'INC' && $riskRow == 'initial') {
        $levelFieldNameSuffix .= '_initial';
    }
    $languageService = (new LanguageServiceFactory())->create();
    $domain = $languageService->getModuleDomain($module);
    $levelFieldName = \UnicodeString::strtolower($module) . $levelFieldNameSuffix;
    $levelTitle = $fieldLabels->getLabel($table, $levelFieldName, $domain) ?: _fdtk('grade');

    $content = $levelTitle . ': <input type="hidden" name="' . $levelFieldName . '"' . ' id="' . $levelFieldName . '" value="' . $levelValue . '" />
            <input type="text" name="' . $levelFieldName . '_Descr" id="' . $levelFieldName . '_Descr" size="20" value="' . $levelDescr . '" readonly="readonly" ' . $bgColor . ' />
            <input type="hidden" name="CHANGED-' . $levelFieldName . '" id="CHANGED-' . $levelFieldName . '" value="1" />';

    // setting value="1" here means these will always audit, even if the value hasn't technically changed, but otherwise we'll have to start passing the current values around messily.

    echo $content;
}

/**
 * Gets full audit data for risk rating fields for both incidents and risks from database and
 * collates the data by date to be read by GetAuditLine().
 *
 * @return array an array of audit data, keyed by date
 *
 * @todo $FullAudit is not always defined, so can technically resolve to null. either default empty array needs to be declared, or return type needs to be explicitly array|null
 */
function GetRiskRatingAudit($aParams)
{
    $fieldWhere = [];
    $rcFieldWhere = [];
    foreach ($aParams['fields'] as $Field) {
        $fieldWhere[] = "aud_action = 'WEB:{$Field}'";
        $rcFieldWhere[] = "aud_detail like '%{$Field} =%'";
        $CurrentData[$Field] = '';
    }

    $fieldWhereString = implode(' OR ', $fieldWhere);
    $rcFieldWhereString = implode(' OR ', $rcFieldWhere);

    // aud_date_split is used to distinguish between updates made in the same minute, since php cannot distinuish these.
    $sql = 'SELECT aud_login, CAST(aud_date as float) as aud_date_split, aud_date, aud_action, aud_detail, aud_delegate_id
        FROM full_audit
        WHERE aud_module = :aud_module
        AND aud_record = :aud_record';

    if (!empty($fieldWhereString)) {
        $sql .= " AND ({$fieldWhereString}) ";
    }

    if (!empty($aParams['include_rich_client_data'])) {
        $sql .= " OR (aud_action = 'UPDATE' AND ({$rcFieldWhereString})) ";
    }

    $sql .= ' ORDER BY aud_date ASC';

    $resultArray = DatixDBQuery::PDO_fetch_all($sql, ['aud_module' => $aParams['module'], 'aud_record' => $aParams['recordid']]);

    $containsData = false;
    // Loops through audit rows and pulls out changes made at the same moment in batches.
    foreach ($resultArray as $row) {
        // add the delegate id to the data array used for full audit
        $CurrentData['aud_delegate_id'] = $row['aud_delegate_id'];

        if ($CurrentDate == '') {
            $CurrentDate = $row['aud_date_split'];
            $CurrentData['aud_date'] = $row['aud_date'];
        }

        if ($CurrentDate != $row['aud_date_split']) {
            if ($containsData) {
                $FullAudit[] = $CurrentData;
            }
            $CurrentDate = $row['aud_date_split'];
            $CurrentData['aud_date'] = $row['aud_date'];
        }

        $containsData = false;

        if ($row['aud_action'] == 'UPDATE') { // data from the Rich Client
            foreach ($aParams['fields'] as $Field) {
                // Data from the RC is stored as part of a string of the form Field = Val, Field2 = Val, etc.
                if (preg_match_all('/' . $Field . ' = ([^, ]*)(,|$)/iu', $row['aud_detail'], $matches) !== 0) {
                    if ($matches[1][0]) {
                        $containsData = true;
                        $CurrentData[$Field] = $matches[1][0];
                    }
                }
            }
        } else {
            $containsData = true;
            $CurrentData[str_replace('WEB:', '', $row['aud_action'])] = $row['aud_detail'];
        }

        $CurrentData['aud_login'] = $row['aud_login'];
    }
    if (count($resultArray) > 0) {
        $FullAudit[] = $CurrentData;
    }

    return $FullAudit;
}
