<?php

use app\models\forms\specifications\EditableFormSpecification;
use app\services\approvalStatus\ApprovalStatusDescriptionRepository;
use app\services\approvalStatus\ApprovalStatusDescriptionRepositoryFactory;
use app\services\securityGroups\SecurityGroupService;
use Source\services\ModuleService;
use src\component\form\FormTable;
use src\framework\controller\Response;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;

/**
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws Throwable
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws InvalidDataException
 */
function LinkSection($rec, $FormType, string $currentModule)
{
    global $FieldDefs, $ListingDesigns;

    $moduleDefs = Container::get(ModuleDefs::class);

    if (!empty($_POST['openMod'])) {
        $_SESSION['ModuleLinks']['openMod'] = Sanitize::SanitizeString($_POST['openMod']);
    }

    $link_id = $rec['recordid'];

    $modArray = $moduleDefs->getModulesThatCanLinkRecords();

    $languageSession = LanguageSessionFactory::getInstance(true);
    $languageCode = $languageSession->getLanguage();

    $approvalDescriptionRepo = (new ApprovalStatusDescriptionRepositoryFactory())->create();

    $editable = (new EditableFormSpecification())->isSatisfiedBy($FormType) && ($_GET['print'] ?? 0) == 0;

    $unsupported = [];
    $linkArray = [];

    $moduleService = Container::get(ModuleService::class);
    $securityGroupService = Container::get(SecurityGroupService::class);

    foreach ($modArray as $forModuleCode => $ModuleName) {
        if ($moduleService->canSeeModule($forModuleCode)) {
            $ListingArray[$forModuleCode] = Listings_ListingFactory::getListing($forModuleCode);

            $ListingArray[$forModuleCode]->LoadColumnsFromDB($ListingDesigns['linked_records'][$forModuleCode] ?? null);

            $ListingCols = array_keys($ListingArray[$forModuleCode]->Columns);

            foreach ($ListingCols as $col_id => $ListingField) {
                if (!isset($FieldDefs[$forModuleCode][$ListingField])) {
                    unset($ListingCols[$col_id]);
                }
            }

            if (!in_array('recordid', $ListingCols)) {
                $ListingCols[] = 'recordid';
            }

            $implodedListingCols = implode(',', $ListingCols);

            if (!empty(array_intersect($ListingCols, PSIMSIncidentFields::getFields()))
                || !empty(array_intersect($ListingCols, PSIMSIncidentCodedFields::getFields()))
                || !empty(array_intersect($ListingCols, PSIMSIncidentEquipmentFields::getFields()))
            ) {
                $linkArray[$forModuleCode] = null;
                $unsupported[$forModuleCode] = true;
            } else {
                $unsupported[$forModuleCode] = false;
                $linkArray[$forModuleCode] = GetLinksForModule($securityGroupService, $forModuleCode, $currentModule, $implodedListingCols, $moduleDefs, $link_id, $approvalDescriptionRepo, $languageCode);
            }
        }
    }

    OuputLinkSectionHtml(
        $modArray,
        $ListingArray,
        $ListingDesigns['linked_records'] ?? null,
        $ListingCols,
        $linkArray,
        $unsupported,
        $editable,
        $moduleDefs,
        $FormType,
        $link_id,
        $currentModule,
    );
}

function GetLinksForModule(SecurityGroupService $securityGroupService, string $forModuleCode, string $currentModule, string $implodedListingCols, ModuleDefs $moduleDefs, $link_id, ApprovalStatusDescriptionRepository $approvalDescriptionRepo, int $languageCode): array
{
    $securityWhere = $securityGroupService->makeSecurityWhereClause('', $forModuleCode, $_SESSION['initials'], '', '', false, false, null);

    // links are stored in a different table depending on whether they are within the same module or not.
    if ($forModuleCode == $currentModule) {
        $recordSql = 'SELECT ' . $implodedListingCols . ', link_notes FROM ' . $moduleDefs[$forModuleCode]->getDbReadObj() . ', links
                WHERE ((LNK_ID1=:current_recordid AND LNK_MOD1=:current_module AND LNK_ID2=recordid AND LNK_MOD2=:target_module) OR (LNK_ID2=:current_recordid2 AND LNK_MOD2=:current_module2 AND LNK_ID1=recordid AND LNK_MOD1=:target_module2))';

        if ($securityWhere != '') {
            $recordSql .= ' AND ' . $securityWhere;
        }

        $recordSql .= 'ORDER BY ' . $moduleDefs[$forModuleCode]->getDbReadObj() . '.recordid';

        $links = DatixDBQuery::PDO_fetch_all($recordSql, ['current_recordid' => $link_id, 'current_recordid2' => $link_id, 'current_module' => $currentModule, 'current_module2' => $currentModule, 'target_module' => $forModuleCode, 'target_module2' => $forModuleCode]);
    } else {
        $recordSql = 'SELECT ' . $implodedListingCols . ', link_notes FROM ' . $moduleDefs[$forModuleCode]->getDbReadObj() . ', link_modules
                WHERE (recordid = ' . $moduleDefs[$forModuleCode]['FK'] . ' AND ' . $moduleDefs[$currentModule]['FK'] . ' = :current_recordid)';

        if ($securityWhere != '') {
            $recordSql .= ' AND ' . $securityWhere;
        }

        $recordSql .= 'ORDER BY ' . $moduleDefs[$forModuleCode]->getDbReadObj() . '.recordid';

        $links = DatixDBQuery::PDO_fetch_all($recordSql, ['current_recordid' => $link_id]);
    }

    foreach ($links as $index => $row) {
        if (array_key_exists('rep_approved', $row)) {
            $links[$index]['rep_approved'] = $approvalDescriptionRepo->GetApprovalStatusDescription($forModuleCode, $row['rep_approved'], $languageCode);
        }
    }

    return $links;
}

/**
 * @throws InvalidDataException
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 */
function OuputLinkSectionHtml(array $modArray, array $ListingArray, ?array $ListingDesign, ?array $ListingCols, array $linkArray, array $unsupported, bool $editable, ModuleDefs $moduleDefs, $FormType, $link_id, string $currentModule): void
{
    global $scripturl;

    $LinksExist = false;

    $moduleService = Container::get(ModuleService::class);

    foreach ($modArray as $forModuleCode => $ModuleName) {
        if ($moduleService->canSeeModule($forModuleCode)) {
            $ListingArray[$forModuleCode] = Listings_ListingFactory::getListing($forModuleCode);

            $ListingArray[$forModuleCode]->LoadColumnsFromDB($ListingDesign[$forModuleCode] ?? null);

            $ListingCols = array_keys($ListingArray[$forModuleCode]->Columns);

            if (!in_array('recordid', $ListingCols)) {
                $ListingCols[] = 'recordid';
            }

            // load UDF fields
            $udfFields = [];
            $recordIDs = [];
            foreach ($ListingCols as $name) {
                if (substr($name, 0, 4) == 'UDF_') {
                    $parts = explode('_', $name);
                    $udfFields[] = $parts[1];
                }
            }
            foreach ($linkArray[$forModuleCode] as $link) {
                $recordIDs[] = $link['recordid'];
            }

            if (!empty($udfFields)) {
                $udfValues = getUDFData($forModuleCode, $recordIDs, $udfFields);
                foreach ($udfValues as $recordid => $values) {
                    foreach ($values as $field => $fieldValue) {
                        // look for the entry with the correct recordid
                        foreach ($linkArray[$forModuleCode] as $index => $row) {
                            if ($row['recordid'] == $recordid) {
                                $linkArray[$forModuleCode][$index]['UDF_' . $field] = $fieldValue;
                            }
                        }
                    }
                }
            }
        }

        // build html listing
        if (!empty($linkArray[$forModuleCode]) || !empty($unsupported[$forModuleCode])) {
            $LinksExist = true;

            $countDisplay = ($unsupported[$forModuleCode]) ? '(?)' : '(' . count($linkArray[$forModuleCode]) . ')';

            echo '
            <li class="new_titlebg" name="linked_records_row" id="linked_records_row">
               <div class="new-title"><a class="toggle-trigger"><img src="images/collapse.gif" alt="-" border="0"/></a>' . _fdtk($forModuleCode . '_linked') . ' ' . $countDisplay . '</div>
            </li>
            <li name="' . $forModuleCode . 'row" id="' . $forModuleCode . 'row" class="new_windowbg toggle-target">';

            if ($unsupported[$forModuleCode]) {
                echo '<div class="error_div">Unable to Display Results. Unsupported use of LFPSE fields in the listing design, please remove to continue without error.</div>';
            } else {
                if ($editable && GetParm($moduleDefs[$forModuleCode]['PERM_GLOBAL']) != '') {
                    foreach ($linkArray[$forModuleCode] as $key => $data) {
                        if (empty($linkArray[$forModuleCode][$key]['no_edit']) && $FormType !== FormTable::MODE_PRINT) {
                            $linkArray[$forModuleCode][$key]['edit'] = '<a href="Javascript:if(CheckChange()){SendTo(\'' . $scripturl . '?action=editlinkedrecord&currentID=' . $link_id . '&currentMod=' . $currentModule . '&module=' . $currentModule . '&editID=' . $linkArray[$forModuleCode][$key]['recordid'] . '&editMod=' . $forModuleCode . '\');}"><span class="fa fa-pencil"></span></a>';
                        }
                    }
                }

                $ListingArray[$forModuleCode]->LoadData($linkArray[$forModuleCode]);
                $ListingArray[$forModuleCode]->AddAdditionalColumns(['link_notes' => ['width' => 10, 'title' => _fdtk('link_notes')], 'edit' => ['width' => 5, 'custom' => true, 'title' => _fdtk('edit')]]);
                echo $ListingArray[$forModuleCode]->GetListingHTML($FormType);
            }

            echo '</li>';
        }
    }

    if (!$LinksExist) {
        echo '
        <li name="linked_records_row" id="linked_records_row" class="section_link_row">
            ' . _fdtk('no_linked_records') . '
        </li>
    ';
    }
    echo '<input type="hidden" name="edit" id="edit" value="">';

    $total = 0;
    foreach ($modArray as $forModuleCode => $ModuleName) {
        $total += count($linkArray[$forModuleCode] ?? []);
    }

    if ($editable) {
        echo '
            <li class="linked-data-action-button">
                <button type="button" class="dtx-button dtx-button-small button-secondary" onclick="if(CheckChange()){SendTo(\'' . $scripturl . '?action=editlinkedrecord&currentID=' . $link_id . '&currentMod=' . $currentModule . '&module=' . $currentModule . '\');}"><span>' . ($total > 0 ? _fdtk('linked_records_link_another_record') : _fdtk('linked_records_link_a_record')) . '</span></button>
                <input type="hidden" id="currentMod" name="currentMod" value="' . Escape::EscapeEntities($currentModule) . '">
                <input type="hidden" id="currentID" name="currentID" value="' . Sanitize::SanitizeInt($link_id) . '">
                <input type="hidden" id="postedaction" name="postedaction" value="action=' . Escape::EscapeEntities($moduleDefs[$currentModule]['ACTION']) . '">
                <input type="hidden" name="editMod" id="editMod" value="">
                <input type="hidden" name="editID" id="editID" value="">
                <input type="hidden" name="openMod" id="openMod" value="' . Escaper::escapeForHTMLParameter($_POST['openMod'] ?? '') . '">
            </li>
        ';
    }
}

/**
 * @return never
 */
function SaveLink($current_id, $current_mod, $new_id, $new_mod, $linkNotes): void
{
    global $scripturl, $ModuleDefs;

    if ($current_mod == $new_mod) {
        $sql = 'UPDATE links SET link_notes = :linkNotes WHERE (LNK_MOD1 = :current_mod AND LNK_MOD2 = :new_mod AND LNK_ID1 = :current_id AND LNK_ID2 = :new_id)
        OR (LNK_MOD2 = :current_mod2 AND LNK_MOD1 = :new_mod2 AND LNK_ID2 = :current_id2 AND LNK_ID1 = :new_id2)';

        $pdoParams = [
            'linkNotes' => $linkNotes,
            'current_mod' => $current_mod,
            'current_mod2' => $current_mod,
            'new_mod' => $new_mod,
            'new_mod2' => $new_mod,
            'current_id' => $current_id,
            'current_id2' => $current_id,
            'new_id' => $new_id,
            'new_id2' => $new_id,
        ];
    } else {
        $sql = 'UPDATE link_modules SET link_notes = :linkNotes WHERE ' . $current_mod . '_ID = :current_id AND ' . $new_mod . '_ID = :new_id';

        $pdoParams = [
            'linkNotes' => $linkNotes,
            'current_id' => $current_id,
            'new_id' => $new_id,
        ];
    }

    DatixDBQuery::PDO_query($sql, $pdoParams);

    $location = $scripturl . '?action=' . $ModuleDefs[$current_mod]['ACTION'] . "&recordid={$current_id}&panel=linked_records";
    Container::get(Response::class)->redirect($location);
}

/**
 * @return never
 */
function RemoveLink($current_id, $current_mod, $linked_id, $linked_mod): void
{
    global $scripturl, $ModuleDefs;

    if ($current_mod == $linked_mod) {
        $pdoParams = [
            'current_mod' => $current_mod,
            'linked_mod' => $linked_mod,
            'current_id' => $current_id,
            'linked_id' => $linked_id,
        ];

        $sql = 'DELETE FROM links WHERE (LNK_MOD1 = :current_mod AND LNK_MOD2 = :linked_mod AND LNK_ID1 = :current_id AND LNK_ID2 = :linked_id)';
        DatixDBQuery::PDO_query($sql, $pdoParams);

        $sql = 'DELETE FROM links WHERE (LNK_MOD2 = :current_mod AND LNK_MOD1 = :linked_mod AND LNK_ID2 = :current_id AND LNK_ID1 = :linked_id)';
        DatixDBQuery::PDO_query($sql, $pdoParams);
    } else {
        $pdoParams = [
            'current_id' => $current_id,
            'linked_id' => $linked_id,
        ];

        $sql = 'DELETE FROM link_modules WHERE '
            . $ModuleDefs[$current_mod]['FK'] . ' = :current_id AND ' . $ModuleDefs[$linked_mod]['FK'] . ' = :linked_id';
        DatixDBQuery::PDO_query($sql, $pdoParams);
    }

    $location = $scripturl . '?action=' . $ModuleDefs[$current_mod]['ACTION'] . "&recordid={$current_id}&panel=linked_records";
    Container::get(Response::class)->redirect($location);
}

/**
 * @return never
 */
function InsertLink($current_id, $current_mod, $linked_id, $linked_mod, $linkNotes): void
{
    global $scripturl, $ModuleDefs;

    if ($current_mod == $linked_mod && $current_id == $linked_id) {
        AddSessionMessage('ERROR', 'You cannot link a record to itself');

        $location = $scripturl . "?action=editlinkedrecord&currentID={$current_id}&currentMod={$current_mod}&module={$current_mod}";
        Container::get(Response::class)->redirect($location);
    }

    if ($current_mod == $linked_mod) {
        // check link does not already exist.
        $sqlcheck = 'SELECT count(*) as num FROM links WHERE (LNK_MOD1 = :current_mod AND LNK_MOD2 = :linked_mod AND LNK_ID1 = :current_id AND LNK_ID2 = :linked_id)
        OR (LNK_MOD2 = :current_mod2 AND LNK_MOD1 = :linked_mod2 AND LNK_ID2 = :current_id2 AND LNK_ID1 = :linked_id2)';

        $pdoParams = [
            'current_mod' => $current_mod,
            'current_mod2' => $current_mod,
            'linked_mod' => $linked_mod,
            'linked_mod2' => $linked_mod,
            'current_id' => $current_id,
            'current_id2' => $current_id,
            'linked_id' => $linked_id,
            'linked_id2' => $linked_id,
        ];
    } else {
        // check link does not already exist.
        $sqlcheck = 'SELECT count(*) as num FROM link_modules WHERE ' . $ModuleDefs[$current_mod]['FK'] . ' = :current_id AND ' . $ModuleDefs[$linked_mod]['FK'] . ' = :linked_id';

        $pdoParams = [
            'current_id' => $current_id,
            'linked_id' => $linked_id,
        ];
    }

    if (DatixDBQuery::PDO_fetch($sqlcheck, $pdoParams, PDO::FETCH_COLUMN)) { // already exists
        AddSessionMessage('ERROR', 'A link to that record already exists.');
        $location = $scripturl . "?action=editlinkedrecord&currentID={$current_id}&currentMod={$current_mod}&module={$current_mod}";
        Container::get(Response::class)->redirect($location);
    }

    // check record to link to exists.
    $sqlcheck = 'SELECT count(*) as num FROM ' . $ModuleDefs[$linked_mod]['TABLE'] . ' WHERE recordid = :linked_id';
    $pdoParams = ['linked_id' => $linked_id];

    if (!DatixDBQuery::PDO_fetch($sqlcheck, $pdoParams, PDO::FETCH_COLUMN)) { // doesn't exist
        AddSessionMessage('ERROR', 'Target record does not exist.');
        $location = $scripturl . "?action=editlinkedrecord&currentID={$current_id}&currentMod={$current_mod}&module={$current_mod}";
        Container::get(Response::class)->redirect($location);
    }

    // insert link
    if ($current_mod == $linked_mod) {
        $sql = 'INSERT INTO links (LNK_MOD1, LNK_ID1, LNK_MOD2, LNK_ID2, LINK_NOTES)
        VALUES (:current_mod, :current_id, :linked_mod, :linked_id, :linkNotes)';

        $pdoParams = [
            'current_mod' => $current_mod,
            'current_id' => $current_id,
            'linked_mod' => $linked_mod,
            'linked_id' => $linked_id,
            'linkNotes' => $linkNotes,
        ];
    } else {
        $sql = 'INSERT INTO link_modules ( ' . $ModuleDefs[$current_mod]['FK'] . ', ' . $ModuleDefs[$linked_mod]['FK'] . ', LINK_NOTES)
        VALUES (:current_id, :linked_id, :linkNotes)';

        $pdoParams = [
            'current_id' => $current_id,
            'linked_id' => $linked_id,
            'linkNotes' => $linkNotes,
        ];
    }

    DatixDBQuery::PDO_query($sql, $pdoParams);

    $location = $scripturl . '?action=' . $ModuleDefs[$current_mod]['ACTION'] . "&recordid={$current_id}&panel=linked_records";
    Container::get(Response::class)->redirect($location);
}
