<?php
/*
 * WARNING – PLEASE READ.
 *
 * Further work here should ensure that any new form migrations please ensure that the function being used, DO NOT call SaveFormDesignValuesToFile.
 *
 * SaveFormDesignValuesToFile builds from an empty array adding the various form definition sections.
 * However, if this code is not in sync with all the possible for definition sections they will not be included in the save – i.e.
 * it is lossy
 *
 * Going forward, use the repository methods updateContentWithBackup, updateContent or setContent.
 *
 * See this file's method AddHelpText and AddFieldShowAction as a guide.
 *
 * AddHelpText and AddFieldShowAction also make a back-up of the form design contents.
 * This is as 'just in case' feature in the event that a form migration goes wrong/issues discovered after.
 * This is triggered by passing in the name of the migration function ('migration_version').
 *
 * @see https://rldatix.atlassian.net/browse/IQ-39793
 */

// WARNING ^^^^ READ WARNING COMMENT AT TOP

// WARNING ^^^^ READ WARNING COMMENT AT TOP

// WARNING ^^^^ READ WARNING COMMENT AT TOP

use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use Doctrine\DBAL\ConnectionException;
use src\complaints\model\FeedbackFields;
use src\contacts\model\ContactsFields;
use src\equipment\models\EquipmentFields;
use src\formdesign\forms\FormDesignGlobals;
use src\formdesign\forms\repository\FormDesignRepository;
use src\formdesign\forms\service\FormDesignLoader;
use src\formdesign\model\FormDesign;
use src\formdesign\model\FormDesignModelFactory;
use src\framework\registry\Registry;
use src\incidents\model\IncidentsFields;
use src\incidents\model\IncidentsSections;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentFields;
use src\medications\models\MedicationFields;
use src\medications\models\MedicationSections;
use src\organisations\model\OrganisationFields;
use src\redress\models\RedressFields;
use src\redress\models\RedressSections;
use src\safeguarding\models\SafeguardingFields;
use src\safeguarding\models\SafeguardingForm;
use src\system\container\facade\Container;
use src\security\CompatEscaper;

function CheckSpecificMigrationNeeded(string $version): bool
{
    if (CompareVersions($version, GetVersion())) {
        // The migration is for a later version than the current one.
        return false;
    }

    $formMigrateVersion = Container::get(Registry::class)
        ->getParm('FORM_MIGRATE_VERSION')
        ->toScalar();

    if (CompareVersions($version, $formMigrateVersion)) {
        // The migration is for a later version than the last migration done, so needs to be run
        return true;
    }

    return false;
}

function MigrateForms()
{
    global $scripturl, $OriginalMigrateVersion;

    $OriginalMigrateVersion = GetParm('FORM_MIGRATE_VERSION');

    $VersionsWithMigrations = [
        '15.0',
        // Hide BNF Classification field for Incidents > Medications
        // Hide Clinical trial field for Incidents > Medications

        '16.1',
        // Hide show_illness and illness sections
        // Hide new reserve respondent fields
        // Hide new employee rate fields
        // Hide new contacts + claims absence fields
        // Hide new MMSEA fields
        // Hide EDI fields
        // Hide new Event reference fields on Claim forms
        // Hide City and State fields
        // Hide RRE ID field
        // Set default ReadOnly EDI fields
        // Hide MMSEA Asset fields
        // Hide TPOC sections
        // Hide quick ref field
        // Hide equipment section and add to new panel
        // Hide quick ref field
        // Hide tax id field
        // hide social security and middle name fields
        // Hide city and state on organisations forms
        // Hide county on organisations forms
        // Hide FROI fields
        // Hide State ID field
        // Hide additional Mercy contacts fields

        '16.1.1',
        // Hide fields added for Mercy individually

        '16.1.2',
        // Hide moved edi_cause_code field

        '16.1.3',
        // Hide SROI fields

        '16.1.4',
        // Hide SROI IAIABC field

        '16.1.5',

        '16.1.6',

        '16.1.7',

        '16.1.8',

        '16.1.9',

        '16.1.10',

        '16.1.11',

        '16.1.12',

        '16.1.13',

        '16.1.14',

        '16.1.15',

        '16.1.16',

        // hid pressure sores section
        '16.1.17',

        // hid skin lesions section
        '16.1.18',

        // hid sharps section
        '16.1.19',

        // Hide incidents SPSC fields
        '16.1.20',

        // hid time_taken_to_submit field
        '16.1.21',

        // Hide Feedback SPSC fields
        '16.1.22',

        // Hide Mortality Review SPSC fields
        '16.1.23',

        // Hide Pressure Ulcers on DIF1 forms
        '16.1.24',

        // Hide Pressure Ulcers on DIF1 form
        '16.1.25',

        // hide SPSC feedback fields
        '16.1.26',

        // Hide Sharps on DIF1 forms
        '16.1.27',

        // Remove instance of staff fields being displayed as checkbox or radio buttons
        '16.1.28',

        // Properly hide Pressure ulcers section
        '16.1.29',

        '16.1.30',

        // Remove excess slashes from title and introductory text
        '16.1.31',

        // Add SPSC lessons learned mortality fields
        '16.1.32',

        // Hide time_taken_to_submit on COM
        '16.1.33',

        // Hide SPSC feedback fields
        '16.1.34',

        // Hide Claims SPSC fields
        '16.1.35',

        // Hide SPSC Specialty field for capture modules
        '16.1.36',

        // Hide SPSC field for feedback
        '16.1.37',

        '16.1.38',

        // Add tags for feedback subjects
        '16.1.39',

        // Hide New Anywhere fields
        '16.1.40',

        // Hide Nationality field on CON forms
        '16.1.41',

        // Hide Adverse Drud Reaction fields
        '16.1.42',

        // Hide Position fields
        '16.1.43',

        // Hide Contact ID Numbers section
        '16.1.44',

        // Add mandatory Learnings fields as they can't be added on form designs
        '16.1.45',

        '16.1.46',

        '16.1.47',

        '16.1.48',

        // hide exact_location field
        '16.1.49',

        '16.1.50',

        '16.1.51',

        '16.1.52',

        // hide spsc ccom fields
        '16.1.53',
        // hide send_to_sfda field
        '16.1.54',
        // hide inc_rep_id field
        '16.1.55',

        '16.1.56',

        '16.1.57',
        // hide inc_grade_rating and inc_grade_initial_rating
        '16.1.58',
        '16.1.59',
        '16.1.60',

        // Hide 'Ombudsman subject' by default.
        '16.1.61',

        // Show 'Ombudsman Subject' section when “Was this complaint referred to the ombudsman?” is checked.
        '16.1.62',

        // Hide NCDS fields by default
        '16.1.63',

        // Hide Outbreak fields by default.
        '16.1.64',

        // Hide Feedback Tasks section by default
        '16.1.65',

        // Hide Vanessa Law fields by default
        '16.1.66',

        // Hide Safeguarding link contact fields
        '16.1.67',

        // Add action to Safeguarding form for anonymous reporting
        '16.1.68',

        // Hide Safeguarding Organisation fields
        '16.1.69',

        // Hide Vanessa's Law fields
        '16.1.70',

        // Add Sac Date Diff fields
        '16.1.71',

        // New Medication PSIMS fields for capture
        '16.1.72',

        // New Equipment PSIMS fields for capture
        '16.1.73',

        // More new PSIMS fields.
        '16.1.74',

        // PSIMS reporter fields.
        '16.1.75',

        // PSIMS link contact fields
        '16.1.76',

        // PSIMS reporter field states.
        '16.1.77',

        // PSIMS new string fields.
        '16.1.78',

        // PSIMS medication field states.
        '16.1.79',

        // new PSIMS coded fields.
        '16.1.80',

        // PSIMS link contact mandatory & trigger fields
        '16.1.81',

        // PSIMS equipments mandatory and triggers
        '16.1.82',

        // PSIMS Went Well field states.
        '16.1.83',

        // PSIMS - Built environment, additional field & trigger field
        '16.1.84',

        // PSIMS - risk section field & trigger field
        '16.1.85',

        // PSIMS - Tissues & Organs, additional fields and trigger field
        '16.1.86',

        // PSIMS - Blood products section
        '16.1.87',

        // PSIMS - INVOLVED PERSONS section
        '16.1.88',

        // PSIMS - IT System & Software section
        '16.1.89',

        // PSIMS section
        '16.1.90',

        // Unhide all the PSIMS fields
        '16.1.91',

        // PSIMS - Furniture section
        '16.1.92',

        // Contact height and weight fields to decimal
        '16.1.93',

        // Vanessa's Law new Incidents form fields
        '16.1.94',

        // Vanessa's Law remove Contacts form fields
        '16.1.95',

        // Task completed date in Task table
        '16.1.96',

        // PHP medications form
        '16.1.97',

        // Early settlement section added to Feedback module
        '16.1.98',

        // ombudsman subject location and service
        '16.1.99',

        // Re-implementing meds mandatory fields (incl. new combined dose fields)
        '16.1.100',

        // Added Ombudsman recommendations and lession learned
        '16.1.101',

        // Add help text to PSIMS fields
        '16.1.105',

        // Add help text to PSIMS patient's sex field
        '16.1.106',

        // PHP equipment form
        '16.1.107',

        // Show last child first for locations and services
        '16.1.108',

        // Apply PSIMS DIF1 progressive disclosure settings to DIF2.
        '16.1.109',

        // Add field - Feedback Escalated
        '16.1.110',

        // Add 2 fields to Feedback - Issue Pathway, Issue Type.
        '16.1.111',

        // Set default value for equipment search category dropdown
        '16.1.112',

        // Hide the contact 'organisation' fields by default.
        '16.1.113',

        // Add Notifications to Feedback forms
        '16.1.114',

        // Add CCS2 fields for mortality review
        '16.1.115',

        // Add CCS2 fields for redress review
        '16.1.116',

        // Add CCS2 fields for safeguarding
        '16.1.117',

        // Add Job Title field to contacts
        '16.1.118',

        // Add Date of event field
        '16.1.119',

        // Add source of record field to the contact form
        '16.1.120',

        // Add more PSIMS triggers
        '16.1.122',

        // Add triggers for new PSIMS fields,
        '16.1.123',

        // Add missing triggers for LFPSE Fields
        '16.1.124',

        // Add missed Trigger for Detection Point
        '16.1.125',

        // Adds more missing triggers for PSIMS fields
        '16.1.126',

        // Add missing triggers for Incidents
        '16.1.127',

        // Fixing triggers missing for LFPSE Estates services
        '16.1.128',

        // Add mandatory field for Incident and Outcome - LFPSE event types
        '16.1.129',

        // Adds trigger for LFPSE - ResponsibleSpecialtyOther field and removes the mandatory status of the LocationAtRisk field
        '16.1.130',

        // Fixes issue with location at risk not showing on outcome event types
        '16.1.131',

        // Removes the LFPSE - Risk section appearing when Outcome is selected as an Event Type
        '16.1.132',

        // Add send to LFPSE/PSIMS toggling fields
        '16.1.133',

        // Adds triggers to risk imminent and risk populations fields for LFPSE
        '16.1.134',

        // Add section triggers for PSIMS Incident Advert Event sections
        '16.1.135',

        // Remove bad triggers related to PSIMS Incident event type
        '16.1.136',

        // Make the LFPSE toggling field mandatory on DIF1 forms and read-only on DIF2 and removes them as hidden fields
        '16.1.137',

        // Make the Estimate the patient's age field mandatory irrespective of setting
        '16.1.138',

        // Hide LFPSE sections behind is this a PSIMS record level 1
        '16.1.139',

        // Hide LFPSE sections behind is this a PSIMS record level 2
        '16.1.140',

        // Add new LFPSE field and associated form actions
        '16.1.141',

        // Remove LFPSE why_anonymous action and mandatory settings from level 1 and 2 forms
        '16.1.142',

        // Update help text for LFPSE fields
        // https://rldatix.atlassian.net/browse/IQ-38896
        '16.1.143',

        // Add field triggers for LFPSE fields
        // https://rldatix.atlassian.net/browse/IQ-38899
        '16.1.144',

        // Removed Triggers for uneeded DIF 1 LFPSE Fields.
        // https://rldatix.atlassian.net/browse/IQ-39407
        '16.1.145',

        // Removed Uneeded Adverse Event Problem Extensions
        // https://rldatix.atlassian.net/browse/IQ-38901
        '16.1.146',

        // '16.1.147', // Superseded by 148, this is not needed anymore

        // Revised method of triggered LFPSE fields
        // https://rldatix.atlassian.net/browse/IQ-40633
        '16.1.148',

        // Removing trigger related to removed field
        // https://rldatix.atlassian.net/browse/IQ-40637
        '16.1.149',

        // Add triggers for LFPSE Estimated Time
        // https://rldatix.atlassian.net/browse/IQ-40963
        '16.1.150',

        // Add triggers for Equipment Field
        // https://rldatix.atlassian.net/browse/IQ-41229
        // https://rldatix.atlassian.net/browse/IQ-41166
        // '16.1.151',
        '16.1.152',

        // Make psims-risk-description mandatory
        // https://rldatix.atlassian.net/browse/IQ-40437
        '16.1.153',

        // Hide psims_adverse_event_problem_blood_products, unless psims_record_lvl_one has the value of yes
        // https://rldatix.atlassian.net/browse/IQ-43251
        '16.1.154',

        // LFPSE triggers not read-only on DIF2
        // https://rldatix.atlassian.net/browse/IQ-43101
        '16.1.155',

        // Fix form action to be correct
        // https://rldatix.atlassian.net/browse/IQ-44031
        '16.1.156',

        // Fix form action to be correct
        // https://rldatix.atlassian.net/browse/IQ-44030
        '16.1.157',

        // remove psims_precise_time
        // https://rldatix.atlassian.net/browse/IQ-44125
        '16.1.158',

        // remove psimm fields from hidden
        // https://rldatix.atlassian.net/browse/IQ-44079
        '16.1.159',

        // added show trigger for psims_estimated_time
        // https://rldatix.atlassian.net/browse/IQ-44459
        '16.1.160',

        // set always mandatory psims contact fields
        // https://rldatix.atlassian.net/browse/IQ-43634
        '16.1.161',

        // Remove hidden from psims_patient_ethnicity
        // // https://rldatix.atlassian.net/browse/IQ-43634
        '16.1.162',

        // Make INC_PSIMS_RECORD_LV1 hidden by default to non extisting PSIMS clients
        // // https://rldatix.atlassian.net/browse/IQ-45072
        '16.1.163',

        // Remove Mandatory on fields
        // // https://rldatix.atlassian.net/browse/IQ-44493
        '16.1.164',

        // Remove Correct show section
        // // https://rldatix.atlassian.net/browse/IQ-43251
        '16.1.165',

        // Add new max harm fields
        // // https://rldatix.atlassian.net/browse/IQ-43663
        '16.1.166',

        // hide PsychologicalHarm when PhysicalHarm=fatal
        // https://rldatix.visualstudio.com/risk/_backlogs/backlog/plenty-team/Backlog%20items/?workitem=83859
        '16.1.167',

        // hide strengthOfAssociation when PhysicalHarm=Low or No
        // https://rldatix.visualstudio.com/risk/_sprints/taskboard/plenty-team/risk/fy24/24-q2/24-q2-s1?workitem=83855
        '16.1.168',

        // Remove the LFPSE field ProblemDescriptionAction
        // https://rldatix.visualstudio.com/risk/_workitems/edit/83857
        '16.1.169',

        // remove IdentifiedLocationOther and DrugWrongUsageDetails LFPSE Fields
        '16.1.170',

        // removing device used and device not used LFPSE fields used in device usage factors
        // because of incorrect triggers and adding them again with correct triggers.
        '16.1.171',

        // Removing generic name mandatory fields from medical form as they are read only now
        '16.1.172',

        // Ticket 87220 - Re-add the mandatory requirement removed in 16.1.172
        '16.1.173',

        // Estimated time field is to be hidden when event type is not incident or outcome.
        '16.1.174',

        // Estimated time field is to be hidden when event type is not incident or outcome for both level 1 and 2.
        '16.1.175',

        // Fix a typo
        '16.1.176',

        '16.1.177',

        // Remove Help Text from psims_gender
        '16.1.178',

        // Change trigger of psims_marvin_reference_number field for different option of psims_safety_challenges
        '16.1.179',

        // Add psims_people_availability field and trigger for psims_people_involvement_factor
        '16.1.180',

        // Return psims_radiotherapy_incident_code to the correct section
        '16.1.181',

        // Add time chain fields
        '16.1.182',

        // Hide time chain section, not fields
        '16.1.183',

        // Make the total time to handle readonly
        '16.1.184',

        // Re-add the LFPSE field ProblemDescriptionAction
        // https://dev.azure.com/rldatix/risk/_workitems/edit/135073
        '16.1.185',

        // Add a hidden field for enforced contact matching
        '16.1.186',

        // Make the enforced contact matching mandatory.
        '16.1.187',

        // Add Help Text for the INC_BYPASS_MANDATORY_FIELDS newly added field
        '16.1.188',

        // Add the first Psims Psirf mandatory field
        '16.1.189',

        // Add Psims Psirf fields and triggers
        '16.1.190',

        // Add PSIMS Response section
        '16.1.191',

        // Add PSIMS Response fields as read only
        '16.1.192',

        // Add time fields as hidden
        '16.1.193',

        // Add Help Text for STATUS and WARNINGS fields in INCIDENTS
        '16.1.194',

        // Add LOCAL field to SAFEGUARDING
        '16.1.195',

        // Add PSIMS Location Known and Organisation fields and triggers
        '16.1.196',

        // Add Handler fields to RED1 and SFG1
        '16.1.197',

        // This is a redo of 16.1.167 migration
        // hide PsychologicalHarm when PhysicalHarm=fatal
        '16.1.198',

        // removed show_documents property from SFG2
        '16.1.199',

        // THIS COMMENT SHOULD ALWAYS BE AT THE BOTTOM
        // WARNING ^^^^ READ WARNING COMMENT AT TOP REGARDING LOSSY MIGRATIONS THAT USE SaveFormDesignValuesToFile
    ];

    $OutcomeMessages = [];
    $MigrationError = false;
    foreach ($VersionsWithMigrations as $VersionWithMigration) {
        if (CheckSpecificMigrationNeeded($VersionWithMigration) && !$MigrationError) {
            $formMigrationCode = 'FormMigrationCode_' . str_replace('.', '_', $VersionWithMigration);
            if (function_exists($formMigrationCode)) {
                if ($formMigrationCode()) {
                    SetGlobal('FORM_MIGRATE_VERSION', $VersionWithMigration);
                    $_SESSION['Globals']['FORM_MIGRATE_VERSION'] = $VersionWithMigration; // ensure we don't require a logout/in

                    $OutcomeMessages[] = '<i>The form migration released with version ' . $VersionWithMigration . ' has been successfully completed</i>';
                } else {
                    $MigrationError = true;
                    $OutcomeMessages[] = 'There was an error while performing the form migration released with version ' . $VersionWithMigration . '. The form migration function returned an error. Please contact Datix Support.';
                }
            } else {
                $MigrationError = true;
                $OutcomeMessages[] = 'There was an error while performing the form migration released with version ' . $VersionWithMigration . '. The form migration function was not found. Please contact Datix Support.';
            }
        }
    }

    if (!$MigrationError) { // update to the latest version in case we are using a version without any migrations
        SetGlobal('FORM_MIGRATE_VERSION', GetVersion());
        $_SESSION['Globals']['FORM_MIGRATE_VERSION'] = GetVersion(); // ensure we don't require a logout/in
    }

    template_header();

    echo '<div class="general_div">
    ';

    echo '<div class="padded_div page_title">Form Migration.</div>
    ';

    echo '<div class="padded_div">Due to a recent Datix Cloud IQ upgrade, the form designs saved on this system are being migrated. This process is automatic.</div>
    ';

    if ($MigrationError) {
        foreach ($OutcomeMessages as $Message) {
            echo '<div class="padded_div">' . $Message . '</div>
            ';
        }
    } else {
        echo '<div class="padded_div">All form designs have been successfully migrated</div>
        ';
    }

    echo '<div class="padded_div"><a href="' . $scripturl . '?' . $_SERVER['QUERY_STRING'] . '">' . _fdtk('proceed_to_your_destination') . '</a></div>';

    echo '</div>
    ';

    footer();
}

function AlterAllDesigns($aParams)
{
    $mapper = (new FormDesignModelFactory())->getMapper();
    $formDesigns = $mapper->selectAllGroupedByModuleAndLevel();

    foreach ($aParams['modules'] as $module) {
        if (!empty($aParams['levels'][1]) && is_array($formDesigns[$module][1])) {
            /** @var FormDesign $formDesign */
            foreach ($formDesigns[$module][1] as $formDesign) {
                if ($formDesign->getContent() != null) {
                    $aParams['params']['file'] = $formDesign->getFilename();
                    $aParams['function']($aParams['params']);
                }
            }
        }

        if (!empty($aParams['levels'][2]) && is_array($formDesigns[$module][2])) {
            /** @var FormDesign $formDesign */
            foreach ($formDesigns[$module][2] as $formDesign) {
                if ($formDesign->getContent() != null) {
                    $aParams['params']['file'] = $formDesign->getFilename();
                    $aParams['function']($aParams['params']);
                }
            }
        }
    }
}

function FormMigrationCode_16_1_199(): bool
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => SafeguardingFields::SHOW_DOCUMENTS,
            'action' => [
                'section' => SafeguardingForm::SECTION_DOCUMENTS,
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_198(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_physical_harm',
            'action' => [
                'field' => 'psims_psychological_harm',
                'values' => ['2', '3', '4', '5'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_197()
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                SafeguardingForm::SECTION_YOUR_MANAGER,
                SafeguardingFields::HANDLER,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::REDRESS],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                RedressSections::YOUR_MANAGER,
                RedressFields::CASE_MANAGER,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_196()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    $fieldTriggers = [
        PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE => [
            PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN => ['1', '2', '4'],
        ],
        PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN => [
            PSIMSIncidentCodedFields::PSIMS_ORGANISATION => ['n'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_195(): bool
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                'local_authority',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_194(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHelpText',
        'params' => [
            'field' => IncidentsFields::STATUS,
            'text' => ['7' => (
                'Search for individual status in the following format:'
                . "\n"
                . '<br />'
                . 'accepted'
                . "\n"
                . '<br />'
                . 'acceptedwithwarnings'
                . "\n"
                . '<br />'
                . 'notaccepted'
            )],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHelpText',
        'params' => [
            'field' => IncidentsFields::WARNINGS,
            'text' => ['7' => (
                'Search for warnings with asterisks on either side of the warning message.'
                . "\n"
                . '<br />'
                . 'For example:'
                . "\n"
                . '<br />'
                . 'For "LocationKnown was not included in the submission" search for *LocationKnown*'
                . "\n"
                . '<br />'
                . 'For multiple warnings, use a pipe symbol for OR e.g. *ReporterOrganisation*|*LocationKnown*'
            )],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_193(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                'time_fields',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                'time_field_1',
                'time_field_2',
                'time_field_3',
                'time_field_4',
                'time_field_5',
                'time_field_from_1',
                'time_field_from_2',
                'time_field_from_3',
                'time_field_from_4',
                'time_field_from_5',
                'time_field_from_6',
                'time_field_from_7',
                'time_field_to_1',
                'time_field_to_2',
                'time_field_to_3',
                'time_field_to_4',
                'time_field_to_5',
                'time_field_to_6',
                'time_field_to_7',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}
function FormMigrationCode_16_1_192(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'reference_number',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'submission_date_time',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'status',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'warning',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_191(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHelpText',
        'params' => [
            'field' => IncidentsFields::SUBMISSION_DATE_TIME,
            'text' => ['7' => (
                'Search for date or time in the following formats:'
                . "\n"
                . '<br />'
                . 'dd* OR *dd/mm* OR *dd/mm/yyyy* OR *yyyy* OR *mm/yyyy'
                . "\n"
                . '<br />'
                . '*hh:mm:ss OR *hh:mm* OR *hh*'
                . "\n"
                . '<br />'
                . 'For example:'
                . "\n"
                . '<br />'
                . '"June 2024" search *06/2024*'
                . "\n"
                . '<br />'
                . '"26th June 2024" search 26/06/2024*'
                . "\n"
                . '<br />'
                . '"11?am" search *11:*'
                . "\n"
                . '<br />'
                . '*10* will return any date or time, e.g. 10/06/2024 or 9:10'
            )],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddExtraText',
        'params' => [
            'field' => IncidentsSections::PSIMS_RESPONSE,
            'text' => ['7' => 'Submission to LFPSE may take a few minutes. If section is blank, refresh page to load data'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'psims_response',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'reference_number',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'submission_date_time',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'status',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'warning',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_190()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS,
                PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT,
                PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => false, 2 => true],
    ]);

    $fieldTriggers = [
        PSIMSIncidentCodedFields::PSIMS_INCIDENT_FRAMEWORK => [
            PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => ['1'],
        ],
        PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => [
            PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND => ['1', '2', '3'],
            PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS => ['1', '2'],
            PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT => ['1', '2'],
            PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED => ['1', '2'],
        ],
        PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED => [
            PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED => ['Y'],
            PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT_ADDRESSED => ['N'],
        ],
        PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND => [
            PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPONDED => ['1', '3'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => false, 2 => true],
            ]);
        }
    }

    $helpText = [
        PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => 'View the Patient Safety Incident Response Framework supporting guidance for examples of events requiring a specific type of response as set out in policies or regulations',
        PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS => 'Please provide details of the findings from the patient safety response',
        PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT => 'Please describe each area for improvement separately',
        PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED => 'Include who the safety actions are being made to, and what they entail. Please add each safety action separately',
    ];

    foreach ($helpText as $fieldName => $fieldHelpText) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddHelpText',
            'params' => [
                'field' => $fieldName,
                'text' => ['7' => $fieldHelpText],
                'migration_version' => __FUNCTION__,
            ],
            'levels' => [2 => true],
        ]);
    }

    return true;
}

function FormMigrationCode_16_1_189()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'field' => 'psims_incident_framework',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => false, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'section' => IncidentsSections::PSIMS_PSIRF,
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => false, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_188(): bool
{
    AlterAllDesigns([
        'modules' => [Module::USERS],
        'function' => 'AddHelpText',
        'params' => [
            'field' => \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS,
            'text' => ['7' => 'Users can be granted permission to save incident records without completing mandatory fields. This excludes any records in Final approval, mandatory fields must be completed in this status'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_187(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => ['con_know_id_field'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_186(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => ['con_know_id_field', 'contact_matching'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_185(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS,
            'action' => [
                'field' => 'psims_problem_description_actions',
                'values' => ['5'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHelpText',
        'params' => [
            'field' => 'psims_problem_description_actions',
            'text' => ['7' => 'A description of the problem involving people\'s actions'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_184(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'inc_total_time_to_handle',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_183(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'time_chain',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                'inc_date_org_aware',
                'inc_date_in_person_notification_made_due',
                'inc_date_in_person_notification_made_completed',
                'inc_date_written_notification_made_due',
                'inc_date_written_notification_made_completed',
                'inc_total_time_to_handle',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_182(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_date_org_aware',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_date_in_person_notification_made_due',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_date_in_person_notification_made_completed',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_date_written_notification_made_due',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_date_written_notification_made_completed',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'inc_total_time_to_handle',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_181(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => null,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => ['4', '7'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    $fieldTriggers = [
        PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => [
            PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE => ['4'],
            PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER => ['7'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => null,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);

            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_180(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR,
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS,
            'action' => [
                'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR,
                'values' => ['10'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY,
            'action' => [
                'field' => PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR,
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_179(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => ['4'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => ['7'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    $fieldTriggers = [
        PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => [
            PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER => ['7'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => null,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);

            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}
function FormMigrationCode_16_1_178(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS, Module::CONTACTS],
        'function' => 'RemoveHelpText',
        'params' => [
            'fields' => [
                'psims_gender',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_177(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_products_involved',
            'action' => [
                'field' => 'psims_blood_products_not_used',
                'values' => ['4'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_176(): bool
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'RemoveHelpText',
        'params' => [
            'fields' => [
                'psims_record_lvl_one',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'RemoveHelpText',
        'params' => [
            'fields' => [
                'psims_record_lvl_two',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHelpText',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'text' => [7 => 'A patient safety event is any event that could have or did impact the safety of one or more patients during the provision of health care, including risks to patient safety in the future, and positive events that could be learned from to improve safety.'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHelpText',
        'params' => [
            'field' => 'psims_record_lvl_two',
            'text' => [7 => 'A patient safety event is any event that could have or did impact the safety of one or more patients during the provision of health care, including risks to patient safety in the future, and positive events that could be learned from to improve safety.'],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_175(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_record_lvl_two',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['N'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['1', '2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_174(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_lvl_one_record',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['1', '2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_173(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                [MedicationFields::ADMINISTERED_MED_NAME, MedicationSections::ADMINISTERED_DRUG],
                [MedicationFields::CORRECT_MED_NAME, MedicationSections::CORRECT_DRUG],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_172(): bool
{
    // 87220: Making the administered generic name and intended generic name to be a read only field
    // and keeping these fields mandatory stops the forms submitting.

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => [
                MedicationFields::ADMINISTERED_MED_NAME,
                MedicationFields::CORRECT_MED_NAME,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_171(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_used_unnecessarily',
                'values' => null,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_not_enough_details',
                'values' => null,
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_used_unnecessarily',
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_not_enough_details',
                'values' => ['2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_170(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveHelpText',
        'params' => [
            'fields' => [
                'psims_identified_location_other',
                'drug_wrong_usage_details',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_169(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'field' => 'psims_problem_description_actions',
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveHelpText',
        'params' => [
            'fields' => [
                'psims_problem_description_actions',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_168(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_physical_harm',
            'action' => [
                'field' => 'psims_strength_of_association',
                'values' => ['1', '2', '3'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_psychological_harm',
            'action' => [
                'field' => 'psims_strength_of_association',
                'values' => ['1', '2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_167(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_physical_harm',
            'action' => [
                'field' => 'psims_psychological_harm',
                'values' => ['2', '3', '4', '5'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_166(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'max_physical_harm_person',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'max_psychological_harm_person',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_165(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'action' => [
                'section' => 'psims_adverse_event_problem_blood_products',
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_adverse_event_problem_blood_products',
                'values' => ['12'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_164(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => [
                'drug_reaction_other',
                'drug_involved_processes_other',
                'psims_reporter_role_other',
                'psims_reporter_involvement_other',
                'psims_risk_theme_other',
                'psims_good_care_detection_factor_other',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_163(): bool
{
    $psimsEnabled = Container::get(DatixConfig::class)->getPsimsEnabled();

    if ($psimsEnabled) {
        return true;
    }

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_162(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                'psims_patient_ethnicity',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

/**
 * PSIMS DIF2.
 */
function FormMigrationCode_16_1_161()
{
    // Set Mandatory fields for the PSIMS section.
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_age_years',
                'psims_physical_harm',
                'psims_gender',
                'psims_clinical_outcome',
                'psims_psychological_harm',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_160(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_estimated_time',
                'values' => ['1', '2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_159(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                'psims_strength_of_association',
                'psims_clinical_outcome',
                'psims_physical_harm',
                'psims_psychological_harm',
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_158(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'field' => 'psims_precise_time',
                'values' => ['1', '2'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_157(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_AGENT,
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_AGENT,
                'values' => ['12', '9', '3', '4'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_156(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => ['1'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
            'action' => [
                'section' => IncidentsSections::PSIMS_ADVERSE_EVENT_SAFETY_CHALLENGES,
                'values' => ['4'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_155(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddReadOnlyField',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [2 => true],
    ]);

    return true;
}
function FormMigrationCode_16_1_154(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_record_lvl_one',
            'action' => [
                'section' => 'psims_adverse_event_problem_blood_products',
                'values' => ['Y'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_153(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                ['psims_risk_description', 'psims_risk'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_152(): bool
{
    $sectionTriggers = [
        'psims_involved_agents' => [
            'iq_equipment' => ['3'],
        ],
        'show_equipment' => [
            'iq_equipment' => ['Y'],
        ],
    ];

    $removeSectionTriggers = [
        'show_equipment' => [
            'equipment' => ['Y'],
        ],
    ];

    foreach ($sectionTriggers as $triggerField => $actions) {
        foreach ($actions as $triggeredSection => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddSectionAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'section' => $triggeredSection,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    foreach ($removeSectionTriggers as $triggerField => $actions) {
        foreach ($actions as $triggeredSection => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveSectionAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'section' => $triggeredSection,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_150(): bool
{
    $fieldTriggers = [
        'psims_event_type' => [
            'psims_estimated_time' => ['1', '2'],
        ],
    ];

    $langCode = '7';

    $helpText = [
        Module::INCIDENTS => [
            'psims_estimated_time' => 'This information is used to identify time-related trends in incidents. Your answer should be based on the information you have at this point, and can be changed if further information becomes available.',
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    foreach ($helpText as $module => $moduleHelpText) {
        foreach ($moduleHelpText as $fieldName => $fieldValue) {
            AlterAllDesigns([
                'modules' => [$module],
                'function' => 'AddHelpText',
                'params' => [
                    'field' => $fieldName,
                    'text' => [$langCode => $fieldValue],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_149(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_incident_occurred_today',
            'action' => [
                'field' => 'approximate_date',
                'values' => ['u'],
            ],
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_148(): bool
{
    $fieldTriggers = [
        'psims_event_type' => [
            'psims_went_well' => ['1', '2', '3'],
            'psims_people_involvement_factor' => ['1'],
            'psims_reporter_involvement' => ['1'],
            'psims_precise_time' => ['1', '2'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => null,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);

            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_146(): bool
{
    $removedTriggerSections = [
        'psims_involved_agents' => [
            'psims_furniture_fittings_section' => ['5'],
            'psims_blood_products' => ['7'],
            'psims_built_environment' => ['6'],
        ],
    ];

    $removedMandatoryFields = [
        'psims_furniture_fittings_other',
    ];

    foreach ($removedTriggerSections as $removedTriggerSection) {
        foreach ($removedTriggerSection as $triggeredSection => $value) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveSectionAction',
                'params' => [
                    'field' => $removedTriggerSection,
                    'action' => [
                        'section' => $triggeredSection,
                        'values' => $value,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => $removedMandatoryFields,
            'migration_version' => __FUNCTION__,
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_145(): bool
{
    $removedTriggerSections = [
        'psims_record_lvl_one' => [
            'psims_adverse_event_governance' => ['Y'],
        ],
    ];

    $removedTriggerFields = [
        'psims_never_event' => [
            'psims_never_event_type' => ['Y'],
        ],
        'psims_cqc_notify' => [
            'psims_cqc_criteria' => ['Y'],
        ],
        'psims_cqc_criteria' => [
            'psims_designations_mental_health' => ['1'],
            'psims_deprivation_of_liberty' => ['1'],
            'psims_local_authority_safeguarding' => ['3'],
        ],
    ];

    foreach ($removedTriggerFields as $removedTriggerField) {
        foreach ($removedTriggerField as $triggeredField => $value) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $removedTriggerField,
                    'action' => [
                        'field' => $triggeredField,
                        'values' => $value,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true],
            ]);
        }
    }

    foreach ($removedTriggerSections as $removedTriggerSection) {
        foreach ($removedTriggerSection as $triggeredSection => $value) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveSectionAction',
                'params' => [
                    'field' => $removedTriggerSection,
                    'action' => [
                        'section' => $triggeredSection,
                        'values' => ['Y'],
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_144(): bool
{
    $fieldTriggers = [
        'psims_event_type' => [
            'psims_problem_description_actions' => ['1'],
        ],
        'psims_involved_agents' => [
            'psims_yellow_card_reference' => ['3', '4', '9', '12'],
        ],
        'psims_safety_challenges' => [
            'psims_marvin_reference_number' => ['4'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_143(): bool
{
    $langCode = '7';

    $helpText = [
        Module::INCIDENTS => [
            'psims_risk_identified_location' => 'Start typing and select the organisation from the list. If you are unsure of your organisation\'s ODS code, you can use the ODS portal here to find it.',
            'psims_radiotherapy_incident_code' => (
                'For example, Level 2 / 13ff / 13cc / 13dd / MD13hh / CF3a / CF2c / CF1c'
                . '<br />'
                . 'TSRT9 - '
                . '<br />'
                . 'Guidance on the radiotherapy taxonomy and its application'
            ),
        ],
        Module::CONTACTS => [
            'psims_strength_of_association' => 'Your answer should be based on your own judgement, given the information you have at this point, and can be changed if further information becomes available.',
            'psims_physical_harm' => (
                'Your answer should be based on the information you have at this point, and can be changed if further information becomes available.'
                . '<br />'
                . 'If a death has occurred and you are not aware of any patient safety incident that preceded the death (including stillbirth or pregnancy loss) but want to notify others so that Learning from Deaths or other standard reviews can be conducted, please return to the start and record a ‘Something routinely reported locally that at this time does not appear to be a patient safety incident but may have been preceded by one’ rather than a patient safety incident.'
                . '<br />'
                . '<br />'
                . '<b>Low physical harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Low physical harm is when all of the following apply:'
                . '<ul>'
                . '<li>'
                . 'did not or is unlikely to need further healthcare beyond a single GP, community healthcare professional, emergency department or clinic visit'
                . '</li>'
                . '<li>'
                . 'did not or is unlikely to need further treatment beyond simple dressing changes or short courses of oral medication'
                . '</li>'
                . '<li>'
                . 'did not or is unlikely to affect that patient’s independence'
                . '</li>'
                . '<li>'
                . 'did not or is unlikely to affect the success of treatment for existing health conditions'
                . '</li>'
                . '</ul>'
                . '<br />'
                . '<br />'
                . '<b>Moderate physical harm</b>'
                . '</br>'
                . 'What does this mean?'
                . '</br>'
                . 'Moderate harm is when at least one of the following apply:'
                . '<ul>'
                . '<li>'
                . 'has needed or is likely to need healthcare beyond a single GP, community healthcare professional, emergency department or clinic visit, and beyond simple dressing changes or short courses of medication, but less than 2 weeks additional inpatient care and/or less than 6 months of further treatment, and did not need immediate life-saving intervention'
                . '</li>'
                . '<li>'
                . 'has limited or is likely to limit the patient\s independence, but for less than 6 months'
                . '</li>'
                . '<li>'
                . 'has affected or is likely to affect the success of treatment, but without meeting the criteria for reduced life expectancy or accelerated disability described under severe harm'
                . '</li>'
                . '</ul>'
                . '<br />'
                . '<br />'
                . '<b>Severe physical harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Severe harm is when at least one of the following apply:'
                . '<ul>'
                . '<li>'
                . 'needed immediate live-saving clinical intervention'
                . '</li>'
                . '<li>'
                . 'is likely to have reduced the patient’s life expectancy'
                . '</li>'
                . '<li>'
                . 'has, or is likely to have, reduced the chances of preventing or delaying disability from their existing healthcare conditions'
                . '</li>'
                . '<li>'
                . 'needed or likely to need additional inpatient care of more than 2 weeks and/or more than 6 months of further treatment'
                . '</li>'
                . '<li>'
                . 'has limited or is likely to limit the patient\'s independence for 6 months or more'
                . '</li>'
                . '</ul>'
                . '<br />'
                . '<br />'
                . '<b>Fatal</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'You should select this option if the patient has died and there is at least a slight possibility the incident that you are recording may have contributed to the death, including stillbirth or pregnancy loss. You will have the option later to estimate to what extent a patient safety incident contributed to this fatal outcome.'
            ),
            'psims_psychological_harm' => (
                'Distress is inherent in being involved in any patient safety incident,'
                . '    but please select ‘no harm’ if you are not aware of any specific psychological harm over and above this.'
                . '<br />'
                . 'Pain should be recorded under physical harm rather than psychological harm.'
                . '<br />'
                . 'Your answer should be based on the information you have at this point,'
                . '    and can be changed if further information becomes available.'
                . '<br />'
                . '<br />'
                . '<b>No psychological harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Distress is inherent in being involved in any patient safety incident,'
                . '    but please select this option if there is no specific psychological harm over and above this'
                . '<br />'
                . '<br />'
                . '<b>Low psychological harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Low psychological harm is when at least one of the following apply:'
                . '<ul style="list-style:inside disc;">'
                . '<li>'
                . 'distress that did not or is unlikely to need extra treatment beyond a single GP,'
                . '    community healthcare professional, emergency department or clinic visit'
                . '</li>'
                . '<li>'
                . 'distress that did not or is unlikely to affect the patient’s normal activities for more than a few days'
                . '</li>'
                . '<li>'
                . 'distress that did not or is unlikely to result in a new mental health diagnosis or a significant deterioration in an existing mental health condition'
                . '</li>'
                . '</ul>'
                . '<br />'
                . '<br />'
                . '<b>Moderate psychological harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Moderate psychological harm is when at least one of the following apply:'
                . '<ul style="list-style:inside disc;">'
                . '<li>'
                . 'distress that did or is likely to need a course of treatment or therapy sessions that extends for less than six months'
                . '</li>'
                . '<li>'
                . 'distress that did or is likely to affect the patient’s normal activities for more than a few days but'
                . '    is unlikely to affect the patient’s ability to live independently for more than six months'
                . '</li>'
                . '<li>'
                . 'distress that did or is likely to result in a new mental health diagnosis,'
                . '    or a significant deterioration in an existing mental health condition,'
                . '    but where recovery is expected within six months'
                . '</li>'
                . '</ul>'
                . '<br />'
                . '<br />'
                . '<b>Severe psychological harm</b>'
                . '<br />'
                . 'What does this mean?'
                . '<br />'
                . 'Severe psychological harm is when at least one of the following apply:'
                . '<ul style="list-style:inside disc;">'
                . '<li>'
                . 'distress that did or is likely to need a course of treatment or therapy sessions that continues for more than six months'
                . '</li>'
                . '<li>'
                . 'distress that did or is likely to affect the patient’s normal activities'
                . '    or ability to live independently for more than six months'
                . '</li>'
                . '<li>'
                . 'distress that did or is likely to result in a new mental health diagnosis,'
                . '    or a significant deterioration in an existing mental health condition,'
                . '    and recovery is not expected within six months'
                . '</li>'
                . '</ul>'
            ),
            'psims_patient_ethnicity' => 'This may be in their record, or you can ask them or a family member. If this information has not been provided by the patient or their family, please select ‘I don’t know’.',
            'psims_clinical_outcome' => 'Describe any physical or psychological impact on the patient as a result of the incident, or how their care was subsequently changed as a result. Your answer should be based on the information you have at this point, and can be changed if further information becomes available.',
        ],
    ];

    foreach ($helpText as $module => $moduleHelpText) {
        foreach ($moduleHelpText as $fieldName => $fieldValue) {
            AlterAllDesigns([
                'modules' => [$module],
                'function' => 'AddHelpText',
                'params' => [
                    'field' => $fieldName,
                    'text' => [$langCode => $fieldValue],
                    'migration_version' => __FUNCTION__,
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_142(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_why_anonymous',
            'action' => [
                'field' => 'psims_why_anonymous_other',
                'values' => null,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => ['psims_why_anonymous', 'psims_why_anonymous_other'],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_141(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_patient_event',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_patient_event',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    $fieldTriggers = [
        'psims_event_type' => [
            'psims_risk_imminent' => ['3'],
            'psims_risk_population' => ['3'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => null,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);

            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_140(): bool
{
    $psimsCoreSections = ['psims', 'psims_reporter', 'psims_adverse_event_governance'];

    foreach ($psimsCoreSections as $section) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddSectionAction',
            'params' => [
                'field' => 'psims_record_lvl_one',
                'action' => [
                    'section' => $section,
                    'values' => ['Y'],
                ],
            ],
            'levels' => [2 => true],
        ]);
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddSectionAction',
            'params' => [
                'field' => 'psims_record_lvl_two',
                'action' => [
                    'section' => $section,
                    'values' => ['N'],
                ],
            ],
            'levels' => [2 => true],
        ]);
    }

    return true;
}

function FormMigrationCode_16_1_139(): bool
{
    $psimsCoreSections = ['psims', 'psims_reporter', 'psims_adverse_event_governance'];

    foreach ($psimsCoreSections as $section) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddSectionAction',
            'params' => [
                'field' => 'psims_record_lvl_one',
                'action' => [
                    'section' => $section,
                    'values' => ['Y'],
                ],
            ],
            'levels' => [1 => true],
        ]);
    }

    return true;
}

function FormMigrationCode_16_1_138(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_age_years',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_137(): bool
{
    $formFields = [
        'psims_record_lvl_one' => [
            'AddReadOnlyField' => 2,
            'RemoveHiddenField' => 1,
            'AddMandatoryField' => 1,
        ],
        'psims_record_lvl_two' => [
            'RemoveHiddenField' => 2,
        ],
    ];

    foreach ($formFields as $field => $formChange) {
        foreach ($formChange as $function => $formLevel) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => $function,
                'params' => ['field' => $field],
                'levels' => [$formLevel => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_136(): bool
{
    AlterAllDesigns([ // Remove all event type triggers for psims_risk
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveSectionAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'section' => 'psims_risk',
                'values' => null,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([ // Re-add just the one for event type "risk"(id 3)
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'section' => 'psims_risk',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveFieldShowAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'field' => 'psims_tissue_involvement_factor',
                'values' => null,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_135(): bool
{
    $oldSectionTriggers = [
        'psims_event_type' => [
            'psims_risk' => ['3', '1'],
        ],
    ];
    $newSectionTriggers = [
        'psims_event_type' => [
            'psims_adverse_event_agent' => ['1'],
            'psims_adverse_event_safety_challenges' => ['1'],
            'psims_risk' => ['3'],
        ],
        'psims_involved_agents' => [
            'psims_adverse_event_problem_blood' => ['11'],
            'psims_adverse_event_problem_blood_products' => ['12'],
            'psims_adverse_event_problem_buildings_infrastructure' => ['13'],
            'psims_adverse_event_problem_estates_services' => ['14'],
        ],
    ];
    $oldFieldTriggers = [
        'psims_involved_agents' => [
            'psims_blood_involved' => ['11'],
            'psims_blood_products_details' => ['12'],
            'psims_blood_products_brand' => ['12'],
            'psims_blood_products_batch' => ['12'],
            'psims_blood_products_involved' => ['12'],
            'psims_buildings_infrastructure' => ['13'],
            'psims_buildings_infrastructure_involved' => ['13'],
            'psims_estates_services' => ['14'],
            'psims_estates_services_involved' => ['14'],
        ],
    ];

    foreach ($oldSectionTriggers as $triggerField => $actions) {
        foreach ($actions as $targetSection => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveSectionAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'section' => $targetSection,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }
    foreach ($newSectionTriggers as $triggerField => $actions) {
        foreach ($actions as $targetSection => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddSectionAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'section' => $targetSection,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    foreach ($oldFieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_134(): bool
{
    $fieldTriggers = [
        'psims_event_type' => [
            'psims_risk_imminent' => ['1', '3'],
            'psims_risk_population' => ['1', '3'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_133()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_record_lvl_one'],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_record_lvl_two'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_132(): bool
{
    $sectionTriggers = [
        'psims_event_type' => [
            'psims_risk' => ['1', '2', '3'],
        ],
    ];

    foreach ($sectionTriggers as $field => $actions) {
        foreach ($actions as $section => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'RemoveSectionAction',
                'params' => [
                    'field' => $field,
                    'action' => [
                        'section' => $section,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_131(): bool
{
    $sectionTriggers = [
        'psims_event_type' => [
            'psims_risk' => ['1', '2', '3'],
        ],
    ];

    foreach ($sectionTriggers as $field => $actions) {
        foreach ($actions as $section => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddSectionAction',
                'params' => [
                    'field' => $field,
                    'action' => [
                        'section' => $section,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_130(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_responsible_specialty_other',
                'values' => ['1', '2', '3'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => ['psims_location_at_risk'],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_129(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_location_within_service',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    $fieldTriggers = [
        'psims_event_type' => [
            'psims_location_within_service' => ['1', '2'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_128(): bool
{
    $fieldTriggers = [
        'psims_involved_agents' => [
            'psims_estates_services_involved' => ['14'],
            'psims_estates_services' => ['14'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_127(): bool
{
    $sectionTriggers = [
        'psims_event_type' => [
            'psims_risk' => ['1', '3'],
        ],
    ];

    foreach ($sectionTriggers as $field => $actions) {
        foreach ($actions as $section => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddSectionAction',
                'params' => [
                    'field' => $field,
                    'action' => [
                        'section' => $section,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_126(): bool
{
    $fieldTriggers = [
        'psims_event_type' => [
            'psims_went_well' => ['1', '3'],
            'psims_location_within_service' => ['1'],
            'psims_service_area' => ['1', '2'],
            'psims_precise_time' => ['1'],
            'psims_level_of_concern' => ['1', '2', '3'],
            'psims_responsible_specialty' => ['1', '2', '3'],
            'psims_detection_point' => ['1', '2'],
        ],
        'psims_device_type' => [
            'device_type_other' => ['88'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_125(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_detection_point',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}


function FormMigrationCode_16_1_124(): bool
{
    $fieldTriggers = [
        'psims_involved_agents' => [
            'psims_sabre_report_number' => ['11'],
            'psims_shot_report_number' => ['11'],
            'psims_blood_involved' => ['11'],
            'psims_blood_products_details' => ['12'],
            'psims_blood_products_brand' => ['12'],
            'psims_blood_products_batch' => ['12'],
            'psims_blood_products_involved' => ['12'],
            'psims_nhsbt_report_number' => ['8'],
            'psims_tissue_involvement_factor' => ['8'],
            'psims_involved_persons_actions' => ['10'],
            'psims_buildings_infrastructure' => ['13'],
            'psims_buildings_infrastructure_involvement' => ['13'],
            'psims_estates_services' => ['14'],
            'psims_estates_services_involvement' => ['14'],
        ],
        'psims_people_involvement_factor' => [
            'psims_problem_description_involvement' => ['4'],
            'psims_people_unavailable_detail' => ['2'],
        ],
        'psims_estates_services_involved' => [
            'psims_estates_services_not_used' => ['2'],
            'psims_wrong_estates_services' => ['3'],
            'psims_estates_services_problem' => ['5'],
        ],
        'psims_cqc_criteria' => [
            'psims_designations_mental_health' => ['1'],
            'psims_deprivation_of_liberty' => ['1'],
            'psims_local_authority_safeguarding' => ['3'],
        ],
        'psims_never_event' => [
            'psims_never_event_type' => ['Y'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_123(): bool
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_incident_occurred_today',
            'action' => [
                'field' => 'approximate_date',
                'values' => ['u'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_122(): bool
{
    $fieldTriggers = [
        'psims_blood_involved' => [
            'psims_too_much_blood_used' => ['1'],
            'psims_too_little_blood_used' => ['2'],
            'psims_wrong_blood' => ['3'],
            'psims_blood_was_not_used' => ['4'],
            'psims_damaged_blood' => ['5'],
            'psims_blood_problem' => ['6'],
        ],
        'psims_blood_products_involved' => [
            'psims_too_much_blood_products' => ['1'],
            'psims_too_little_blood_products' => ['2'],
            'psims_wrong_blood_products' => ['3'],
            'psims_blood_products_not_used' => ['4'],
            'psims_damaged_blood_products' => ['5'],
            'psims_blood_products_problem' => ['6'],
        ],
        'psims_buildings_infrastructure' => [
            'psims_buildings_infrastructure_other' => ['16'],
        ],
        'psims_estates_services' => [
            'psims_estates_services_other' => ['9'],
        ],
        'psims_buildings_infrastructure_involved' => [
            'psims_buildings_infrastructure_not_used' => ['2'],
            'psims_wrong_buildings_infrastructure' => ['3'],
            'psims_buildings_infrastructure_problem' => ['5'],
        ],
        'psims_cqc_notify' => [
            'psims_cqc_criteria' => ['Y'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [1 => true, 2 => true],
            ]);
        }
    }

    return true;
}

function FormMigrationCode_16_1_120(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'source_of_record'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_119(): bool
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_of_event'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_118(): bool
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'job_title'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_117()
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ccs2'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_116()
{
    AlterAllDesigns([
        'modules' => [Module::REDRESS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ccs2'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_115()
{
    AlterAllDesigns([
        'modules' => [Module::MORTALITY_REVIEW],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ccs2'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_114()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'history'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_113()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_organisation'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_112()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddDefaultValue',
        'params' => ['field' => 'equipment_search_category', 'value' => 'genericName'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_111()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_issue_pathway'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_issue_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_110()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_redress_escalated'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

/**
 * PSIMS DIF2.
 */
function FormMigrationCode_16_1_109()
{
    // Set Mandatory fields for the PSIMS section.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_furniture_fittings_other',
                'psims_event_type',
                'psims_safety_incident_occurred',
                'psims_outcome_type',
                'psims_involved_agents',
                'psims_level_of_concern',
                'psims_detection_point',
                'psims_detection_point_other',
                'psims_safety_challenges',
                'psims_radiotherapy_incident_code',
                'psims_responsible_specialty',
                'psims_service_area',
                'psims_risk_theme_other',
                'psims_risk_imminent',
                'psims_risk_timeframe_other',
                'psims_risk_description',
                'psims_good_care_detection_factor_other',
                'psims_how_future_occurrence',
                'device_type_other',
                'psims_clinical_outcome',
                'psims_gender',
                'psims_patient_sequence',
                'psims_physical_harm',
                'psims_psychological_harm',
                'drug_reaction_other',
                'drug_involved_processes_other',
                'psims_reporter_involvement_other',
                'psims_reporter_role_other',
                'psims_reporter_type_other',
                'psims_why_anonymous_other',
            ],
        ],
        'levels' => [2 => true],
    ]);

    // Setup SECTION actions for the PSIMS section.

    $sectionTriggers = [
        'psims_event_type' => [
            'psims_risk' => ['3'],
            'psims_went_well_section' => ['4'],
        ],
        'psims_involved_agents' => [
            'psims_equipment' => ['3'],
            'psims_medications' => ['4'],
            'psims_furniture_fittings_section' => ['5'],
            'psims_built_environment' => ['6'],
            'psims_blood_products' => ['7'],
            'psims_tissues_organs' => ['8'],
            'psims_it_systems_software' => ['9'],
            'psims_involved_persons' => ['10'],
        ],
    ];

    foreach ($sectionTriggers as $field => $actions) {
        foreach ($actions as $section => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddSectionAction',
                'params' => [
                    'field' => $field,
                    'action' => [
                        'section' => $section,
                        'values' => $values,
                    ],
                ],
                'levels' => [2 => true],
            ]);
        }
    }

    // Set up FIELD actions for the PSIMS section.
    $fieldTriggers = [
        'psims_furniture_not_used' => [
            'psims_furniture_involved_factor' => ['2'],
        ],
        'psims_furniture_wrong_usaged' => [
            'psims_furniture_involved_factor' => ['3'],
        ],
        'psims_furniture_broken_details' => [
            'psims_furniture_involved_factor' => ['4'],
        ],
        'psims_problem_description_furniture_fittings' => [
            'psims_furniture_involved_factor' => ['5'],
        ],
        'psims_event_type' => [
            'psims_involved_agents' => ['1'],
            'psims_level_of_concern' => ['1', '2'],
            'psims_safety_challenges' => ['1'],
            'psims_responsible_specialty' => ['1', '3'],
            'psims_safety_incident_occurred' => ['2'],
            'psims_outcome_type' => ['2'],
            'psims_detection_point' => ['2'],
        ],
        'psims_detection_point' => [
            'psims_detection_factors' => ['1', '2', '3', '4', '5', '6', '7'],
            'psims_detection_point_other' => ['8'],
        ],
        'psims_safety_challenges' => [
            'psims_radiotherapy_incident_code' => ['4'],
        ],
        'psims_it_involvement_factors' => [
            'psims_problem_description_systems_software' => ['6'],
        ],
        'psims_involved_persons_actions' => [
            'psims_involved_persons_actions_other' => ['5'],
        ],
        'psims_people_action_factors' => [
            'psims_people_action_too_much' => ['1'],
            'psims_people_action_details' => ['2'],
            'psims_people_wrong_action' => ['3'],
            'psims_people_omitted_action' => ['4'],
        ],
        'psims_people_involvement_factor' => [
            'psims_people_unavailable_detail' => ['2'],
            'psims_problem_description_involvement' => ['5'],
        ],
        'psims_blood_involvement_factors' => [
            'psims_blood_used_too_much' => ['1'],
            'psims_blood_insufficient_detail' => ['2'],
            'psims_blood_wrong_details' => ['3'],
            'psims_blood_not_used' => ['4'],
            'psims_blood_damaged_details' => ['5'],
            'psims_problem_description_blood' => ['6'],
        ],
        'psims_tissue_involvement_factor' => [
            'psims_tissue_used_too_much' => ['1'],
            'psims_tissue_deficient_details' => ['2'],
            'psims_tissue_wrong_details' => ['3'],
            'psims_tissue_not_used' => ['4'],
            'psims_tissue_damaged_details' => ['5'],
            'psims_problem_description_tissues_organs' => ['6'],
        ],
        'psims_risk_theme' => [
            'psims_risk_theme_other' => ['8'],
        ],
        'psims_risk_imminent' => [
            'psims_risk_time_frame' => ['n', 'u'],
        ],
        'psims_risk_time_frame' => [
            'psims_risk_timeframe_other' => ['4'],
        ],
        'psims_env_involvement_factors' => [
            'psims_problem_description_built_environment' => ['5'],
        ],
        'psims_care_detection_factor' => [
            'psims_good_care_detection_factor_other' => ['6'],
        ],
        'device_usage_factors' => [
            'device_used_unnecessarily' => ['1', '2'],
            'device_usage_details' => ['3'],
            'device_broken_details' => ['4'],
            'device_problem_description' => ['6'],
        ],
        'psims_physical_harm' => [
            'psims_psychological_harm' => ['2', '3', '4', '5'],
            'psims_strength_of_association' => ['1', '2', '3'],
        ],
        'psims_psychological_harm' => [
            'psims_strength_of_association' => ['1', '2'],
        ],
        'drug_involvement_factors' => [
            'drug_used_too_much' => ['1'],
            'drug_insufficient_details' => ['2'],
            'drug_given_incorrectly' => ['3'],
            'problem_meds_packaging' => ['4'],
            'problem_description_drugs' => ['5'],
        ],
        'drug_reaction' => [
            'drug_reaction_other' => ['8'],
        ],
        'drug_involved_processes' => [
            'drug_involved_processes_other' => ['4'],
        ],
        'psims_reporter_involvement' => [
            'psims_reporter_involvement_other' => ['6'],
        ],
        'psims_reporter_role' => [
            'psims_reporter_role_other' => ['9'],
        ],
        'psims_reporter_type' => [
            'psims_reporter_type_other' => ['5'],
        ],
        'psims_why_anonymous' => [
            'psims_why_anonymous_other' => ['8'],
        ],
    ];

    foreach ($fieldTriggers as $triggerField => $actions) {
        foreach ($actions as $targetField => $values) {
            AlterAllDesigns([
                'modules' => [Module::INCIDENTS],
                'function' => 'AddFieldShowAction',
                'params' => [
                    'field' => $triggerField,
                    'action' => [
                        'field' => $targetField,
                        'values' => $values,
                    ],
                ],
                'levels' => [2 => true],
            ]);
        }
    }

    // Set up an action that includes a message.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'field' => 'psims_medication_admin',
                'alerttext' => ['7' => 'Provide your email address in the Reporter section so that the MHRA can follow up with you about this incident if they need to.'],
                'values' => ['3', '4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    // Set up an action that just shows a message...
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_safety_incident_occurred',
            'action' => [
                'alerttext' => [7 => 'Please change the type of event to Incident'],
                'values' => ['y'],
            ],
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_108()
{
    AlterAllDesigns([
        'modules' => ['CLA', 'INC', 'COM', 'MOR', 'RED', 'SFG'],
        'function' => 'AddFieldShowLastChildFirst',
        'params' => [
            'fields' => [
                'location_id',
                'service_id',
                'admission_location_id',
                'admission_service_id',
                'principle_service_id',
            ],
        ],
    ]);

    return true;
}

function FormMigrationCode_16_1_107()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                [EquipmentFields::SEARCH, IncidentsSections::EQUIPMENT],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                [EquipmentFields::DEVICE_TYPE, IncidentsSections::EQUIPMENT],
                [EquipmentFields::QUANTITY_USED, IncidentsSections::EQUIPMENT],
                [EquipmentFields::BATCH_NUMBER, IncidentsSections::EQUIPMENT],
                [EquipmentFields::EXPIRY_DATE, IncidentsSections::EQUIPMENT],
                [EquipmentFields::CATELOGUE_NUMBER, IncidentsSections::EQUIPMENT],
                [EquipmentFields::OPERATOR, IncidentsSections::EQUIPMENT],
                [EquipmentFields::USAGE, IncidentsSections::EQUIPMENT],
                [EquipmentFields::CURRENT_LOCATION, IncidentsSections::EQUIPMENT],
            ],
        ],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_101()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_recommendations'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_lessons_learned'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_105()
{
    $helpTextPsims = [
        'psims_safety_incident_occurred' => ['7' => 'To indicate if there is cause to worry or suspect patient safety incident occurred'],
        'approximate_date' => ['7' => 'An estimated date of the adverse event'],
        'psims_estimated_time' => ['7' => 'Estimated time of the event'],
        'psims_outcome_type' => ['7' => 'The type of outcome that is being recorded'],
        'psims_involved_agents' => ['7' => 'What people and or things were involved in the event'],
        'psims_medication_admin' => ['7' => 'Was a the medication administered by using a device'],
        'psims_healthcare_process' => ['7' => 'The process(es) being undertaken when something went wrong'],
        'psims_problem_description_actions' => ['7' => 'A description of the problem involving people\'s actions'],
        'psims_problem_description_involvement' => ['7' => 'A description of the problem in regards to people involved'],
        'psims_level_of_concern' => ['7' => 'Indication of how concerned the reporter feels about this adverse event given its wider implications'],
        'psims_detection_factors' => ['7' => 'Factors involved in detecting the incident'],
        'psims_detection_point' => ['7' => 'High level detection factors'],
        'psims_detection_point_other' => ['7' => 'Other detection factors'],
        'duty_of_candour' => ['7' => 'Indication of whether the adverse event meets the requirements for duty of candour'],
        'psims_went_well' => ['7' => 'Details of a positive patient safety event'],
        'psims_safety_challenges' => ['7' => 'What safety challenges were faced in the incident'],
        'psims_radiotherapy_incident_code' => ['7' => 'To identify the radiotherapy incident classification, severity, pathway and causative factors'],
        'psims_responsible_specialty' => ['7' => 'Specialty caring for the patient at the time the problem occurred'],
        'psims_responsible_specialty_other' => ['7' => 'Other responsible specialty'],
        'psims_service_area' => ['7' => 'Service areas involved in the event'],
        'psims_risk_theme' => ['7' => 'The theme of the risk'],
        'psims_risk_theme_other' => ['7' => 'Other theme of the event'],
        'psims_risk_service_area' => ['7' => 'What service areas could be affected by this risk'],
        'psims_risk_imminent' => ['7' => 'Is the risk of death or severe harm imminent'],
        'psims_risk_population' => ['7' => 'What population could be affected by this risk'],
        'psims_risk_time_frame' => ['7' => 'The timeframe in which a risk is most likely to manifest itself and cause harm'],
        'psims_risk_timeframe_other' => ['7' => 'Other timeframe where a risk may manifest'],
        'psims_risk_description' => ['7' => 'Description of the risk'],
        'psims_care_detection_factor' => ['7' => 'To determine how the example of good care came to the reporters attention'],
        'psims_good_care_detection_factor_other' => ['7' => 'Other factor which is not listed involved in noticing the example of good care'],
        'psims_how_future_occurrence' => ['7' => 'To indicate how the care that went well could be amplified or re-created in future'],
        'psims_strengths_of_care' => ['7' => 'The strengths of care or service delivery which went will in the provision of care'],
        'psims_risk_identified_location' => ['7' => 'The location where a risk was identified'],
        'psims_identified_location_other' => ['7' => 'Other location where the event was identified'],
        'psims_location_at_risk' => ['7' => 'The location where a risk is present'],
        'psims_location_known' => ['7' => 'The location where there event took place'],
        'psims_location_within_service' => ['7' => 'The location within the service where an adverse event occurred'],
        'drug_insufficient_details' => ['7' => 'How too little of a drug was used'],
        'drug_involvement_factors' => ['7' => 'How drugs were involved in an unexpected way'],
        'drug_reaction' => ['7' => 'Indication of whether an adverse event was due to an adverse drug reaction'],
        'drug_reaction_other' => ['7' => 'Other indication of drug reaction'],
        'drug_used_too_much' => ['7' => 'How too much of a drug was used'],
        'drug_wrong_details' => ['7' => 'How the right drug was not used as it should have been'],
        'drug_wrong_usage_details' => ['7' => 'How the wrong drug was used'],
        'drug_involved_processes' => ['7' => 'Processes involved in what went wrong'],
        'drug_involved_processes_other' => ['7' => 'Processes involved in what went wrong other response'],
        'drug_given_incorrectly' => ['7' => 'Reason the medication was administered incorrectly'],
        'problem_meds_packaging' => ['7' => 'What was wrong with the medication or its packaging'],
        'problem_description_drugs' => ['7' => 'A description of the problem involving drugs'],
        'psims_device_type' => ['7' => 'The type of medical device that was involved in an incident'],
        'device_type_other' => ['7' => 'Other device type'],
        'device_broken_details' => ['7' => 'How a device was broken'],
        'device_not_enough_details' => ['7' => 'How the use of devices was insufficient'],
        'device_usage_factors' => ['7' => 'How devices were involved in an unexpected way'],
        'device_used_unnecessarily' => ['7' => 'How a device was used unnecessarily'],
        'device_usage_details' => ['7' => 'How a device was used incorrectly'],
        'device_problem_description' => ['7' => 'A description of the problem involving devices'],
        'psims_built_environment_involved' => ['7' => 'Elements of the built environment involved in the incident'],
        'psims_env_involvement_factors' => ['7' => 'How the built environment was involved in an unexpected way'],
        'psims_problem_description_built_environment' => ['7' => 'A description of the problem involving built environment'],
        'psims_furniture_fittings' => ['7' => 'Furniture or fittings that were involved in the event'],
        'psims_furniture_fittings_other' => ['7' => 'Other furniture or fittings'],
        'psims_furniture_broken_details' => ['7' => 'How broken furniture or fittings were involved'],
        'psims_furniture_involved_factor' => ['7' => 'How furniture and fittings were involved in an unexpected way'],
        'psims_furniture_not_used' => ['7' => 'Reasons for correct furniture and fittings not being used'],
        'psims_furniture_wrong_usaged' => ['7' => 'How the wrong furniture or fittings were involved'],
        'psims_problem_description_furniture_fittings' => ['7' => 'A description of the problem involving furniture and fittings'],
        'psims_it_involvement_factors' => ['7' => 'How IT systems and software were involved in an unexpected way'],
        'psims_problem_description_systems_software' => ['7' => 'A description of the problem involving IT systems and software'],
        'psims_blood_not_used' => ['7' => 'Reasons for why blood or blood products were not used'],
        'psims_blood_damaged_details' => ['7' => 'How damaged blood or blood products were involved'],
        'psims_blood_insufficient_detail' => ['7' => 'How too little blood or blood products were involved'],
        'psims_blood_involvement_factors' => ['7' => 'How blood and blood products were involved in an unexpected way'],
        'psims_blood_used_too_much' => ['7' => 'How too much blood or blood products were used'],
        'psims_blood_wrong_details' => ['7' => 'How the wrong blood or blood products were involved'],
        'psims_problem_description_blood' => ['7' => 'A description of the problem involving blood and blood products'],
        'psims_problem_blood_products' => ['7' => 'What was the problem with the blood or blood products'],
        'psims_tissue_deficient_details' => ['7' => 'How insufficient tissue or organs for transplant were involved'],
        'psims_tissue_involvement_factor' => ['7' => 'How tissue and organs for transplant were involved in an unexpected way'],
        'psims_tissue_not_used' => ['7' => 'Reasons for tissues or organs not being used when they should have been'],
        'psims_tissue_used_too_much' => ['7' => 'How too much tissue and organs for transplant were used'],
        'psims_tissue_wrong_details' => ['7' => 'How the wrong tissues or organs for transplant were involved'],
        'psims_tissue_damaged_details' => ['7' => 'How damaged tissue or organs for transplant were involved'],
        'psims_problem_description_tissues_organs' => ['7' => 'A description of the problem involving tissue and organs for transplant'],
        'psims_involved_persons_actions' => ['7' => 'The role(s) of the people involved in the event'],
        'psims_involved_persons_actions_other' => ['7' => 'Other role description'],
        'psims_people_action_factors' => ['7' => 'How the actions of people differed from expected'],
        'psims_people_action_too_much' => ['7' => 'How people took too much of an action'],
        'psims_people_incorrect_details' => ['7' => 'How the incorrect people were involved'],
        'psims_people_action_details' => ['7' => 'How people did not take enough of a necessary action'],
        'psims_people_involvement_factor' => ['7' => 'How the involvement of people differed from expected'],
        'psims_people_involved_details' => ['7' => 'How the right people were not involved'],
        'psims_people_omitted_action' => ['7' => 'How people did not do something that was needed'],
        'psims_people_unavailable_detail' => ['7' => 'How people were not available who should have been'],
        'psims_people_wrong_action' => ['7' => 'How people took the wrong actions'],
        'psims_gender' => ['7' => 'The gender of the patient'],
        'psims_reporter_involvement' => ['7' => 'How was the reporter involved in the event'],
        'psims_reporter_involvement_other' => ['7' => 'Other involvement in the event'],
        'psims_reporter_organisation' => ['7' => 'The organisation from which the report is being made'],
        'psims_reporter_organisation_other' => ['7' => 'Other reporting organisation'],
        'psims_reporter_role' => ['7' => 'The role of the reporter'],
        'psims_reporter_role_other' => ['7' => 'Other role'],
        'psims_reporter_type' => ['7' => 'Who the reporter of an adverse event is'],
        'psims_reporter_type_other' => ['7' => 'Other reporter type'],
        'psims_why_anonymous' => ['7' => 'Indication of why the reporter has logged the event anonymously'],
        'psims_why_anonymous_other' => ['7' => 'Other reason for anonymous reporting'],
    ];

    foreach ($helpTextPsims as $fieldName => $fieldvalue) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddHelpText',
            'params' => ['field' => $fieldName, 'text' => $fieldvalue],
            'levels' => [1 => true, 2 => true],
        ]);
    }

    $helpTextPsimsContact = [
        'psims_age_years' => ['7' => 'The patient\'s age in years when the exact age is unknown'],
        'psims_clinical_outcome' => ['7' => 'Clinical outcome for the patient'],
        'psims_patient_sequence' => ['7' => 'Sequence id for the patient details for sorting purposes'],
        'psims_physical_harm' => ['7' => 'Level of physical harm sustained by the patient'],
        'psims_psychological_harm' => ['7' => 'Level of psychological harm sustained by the patient'],
        'psims_strength_of_association' => ['7' => 'Extent to which the problem was associated with the patient outcome'],
    ];

    foreach ($helpTextPsimsContact as $fieldName => $fieldvalue) {
        AlterAllDesigns([
            'modules' => [Module::CONTACTS],
            'function' => 'AddHelpText',
            'params' => ['field' => $fieldName, 'text' => $fieldvalue],
            'levels' => [1 => true, 2 => true],
        ]);
    }

    return true;
}

function FormMigrationCode_16_1_106()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHelpText',
        'params' => ['field' => 'psims_gender', 'text' => ['7' => 'The gender of the patient']],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}


/**
 * Once more with feeling..
 *
 * Turns out that the previous attempt (FormMigrationCode_16_1_97()) to migrate mandatory meds fields
 * didn't work because the AddMandatoryField function didn't allow you to define the section in which
 * the fields appear, which is required for the form submission routines to work properly. So I've
 * amended that function and we go again 💪
 */
function FormMigrationCode_16_1_100()
{
    $registry = Container::get(Registry::class);

    // Certain clients had hardcoded form design elements applied by feature switches previously,
    // so we need to pay attention to that here so we don't change their existing meds forms
    $nswh = $registry->getParm('NSWH_DATASET')->isTrue();
    $spsc = $registry->getParm('SPSC_DATASET')->isTrue();

    // The case for all configs
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                [MedicationFields::ADMINISTERED_MED_NAME, MedicationSections::ADMINISTERED_DRUG],
                [MedicationFields::CORRECT_MED_NAME, MedicationSections::CORRECT_DRUG],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    if ($nswh || $spsc) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddMandatoryField',
            'params' => [
                'fields' => [
                    [MedicationFields::ADMINISTERED_ROUTE, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::ADMINISTERED_DOSE, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::ADMINISTERED_FORM, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::CORRECT_ROUTE, MedicationSections::CORRECT_DRUG],
                    [MedicationFields::CORRECT_DOSE, MedicationSections::CORRECT_DRUG],
                    [MedicationFields::CORRECT_FORM, MedicationSections::CORRECT_DRUG],
                ],
            ],
            'levels' => [1 => true, 2 => true],
        ]);
    } else {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddMandatoryField',
            'params' => [
                'fields' => [
                    [MedicationFields::ADMINISTERED_ROUTE, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::ADMINISTERED_DOSE, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::ADMINISTERED_FORM, MedicationSections::ADMINISTERED_DRUG],
                    [MedicationFields::CORRECT_ROUTE, MedicationSections::CORRECT_DRUG],
                    [MedicationFields::CORRECT_DOSE, MedicationSections::CORRECT_DRUG],
                    [MedicationFields::CORRECT_FORM, MedicationSections::CORRECT_DRUG],
                    [MedicationFields::STAGE_OF_ERROR, MedicationSections::MEDICATIONS_OTHER_FIELDS],
                    [MedicationFields::TYPE_OF_ERROR, MedicationSections::MEDICATIONS_OTHER_FIELDS],
                    [MedicationFields::OTHER_IMPORTANT_FACTORS, MedicationSections::MEDICATIONS_OTHER_FIELDS],
                ],
            ],
            'levels' => [2 => true],
        ]);
    }

    // While we're here, we need to remove the "old" mandatory dose fields, as they've since been "combined"
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveMandatoryField',
        'params' => [
            'fields' => [
                MedicationFields::ADMINISTERED_QUANTITY,
                MedicationFields::ADMINISTERED_UNIT,
                MedicationFields::CORRECT_QUANTITY,
                MedicationFields::CORRECT_UNIT,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_99()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_location_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_service_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_98()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => [
            'fields' => [
                FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_RECEIVED,
                FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_REQUESTED,
                FeedbackFields::EARLY_SETTLEMENT_PROPOSAL_DATE_SUBMITTED,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_97()
{
    $registry = Container::get(Registry::class);

    // Certain clients had hardcoded form design elements applied by feature switches previously,
    // so we need to pay attention to that here so we don't change their existing meds forms
    $nswh = $registry->getParm('NSWH_DATASET')->isTrue();
    $spsc = $registry->getParm('SPSC_DATASET')->isTrue();

    // The case for all configs
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                MedicationFields::ADMINISTERED_MED_NAME,
                MedicationFields::CORRECT_MED_NAME,
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    if ($nswh || $spsc) {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddMandatoryField',
            'params' => [
                'fields' => [
                    MedicationFields::ADMINISTERED_ROUTE,
                    MedicationFields::ADMINISTERED_QUANTITY,
                    MedicationFields::ADMINISTERED_UNIT,
                    MedicationFields::ADMINISTERED_FORM,
                    MedicationFields::CORRECT_ROUTE,
                    MedicationFields::CORRECT_QUANTITY,
                    MedicationFields::CORRECT_UNIT,
                    MedicationFields::CORRECT_FORM,
                ],
            ],
            'levels' => [1 => true, 2 => true],
        ]);
    } else {
        AlterAllDesigns([
            'modules' => [Module::INCIDENTS],
            'function' => 'AddMandatoryField',
            'params' => [
                'fields' => [
                    MedicationFields::ADMINISTERED_ROUTE,
                    MedicationFields::ADMINISTERED_QUANTITY,
                    MedicationFields::ADMINISTERED_UNIT,
                    MedicationFields::ADMINISTERED_FORM,
                    MedicationFields::CORRECT_ROUTE,
                    MedicationFields::CORRECT_QUANTITY,
                    MedicationFields::CORRECT_UNIT,
                    MedicationFields::CORRECT_FORM,
                    MedicationFields::STAGE_OF_ERROR,
                    MedicationFields::TYPE_OF_ERROR,
                    MedicationFields::OTHER_IMPORTANT_FACTORS,
                ],
            ],
            'levels' => [2 => true],
        ]);
    }

    // progressive disclosure for vanessa's law fields
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => MedicationFields::REPORTED_TO_MANUFACTURER,
            'action' => [
                'field' => MedicationFields::DATE_REPORTED_TO_MANUFACTURER,
                'values' => ['yes'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => MedicationFields::REPORTED_TO_MANUFACTURER,
            'action' => [
                'field' => MedicationFields::MANUFACTURER_REF,
                'values' => ['yes'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => MedicationFields::ACTION_TAKEN,
            'action' => [
                'field' => MedicationFields::ADR_LESSENED,
                'values' => ['withdrawn', 'dose_reduced'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => MedicationFields::ACTION_TAKEN,
            'action' => [
                'field' => MedicationFields::DATE_LAST_USED,
                'values' => ['withdrawn'],
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_96()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'completed'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_95()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                'con_vanessa_law_org_name',
                'con_vanessa_law_email_id',
                'con_vanessa_law_hcid',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_94()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => IncidentsFields::VLC_HOSPITAL_REP_EMAIL_ID],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => IncidentsFields::VLC_HEALTH_CANADA_INST_ID],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_93()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => ContactsFields::LINK_HEIGHT],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_92()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => ['field' => 'psims_furniture_fittings_other'],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_furniture_not_used',
            'action' => [
                'field' => 'psims_furniture_involved_factor',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_furniture_wrong_usaged',
            'action' => [
                'field' => 'psims_furniture_involved_factor',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_furniture_broken_details',
            'action' => [
                'field' => 'psims_furniture_involved_factor',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_problem_description_furniture_fittings',
            'action' => [
                'field' => 'psims_furniture_involved_factor',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_91()
{
    // Unhide all the PSIMS fields.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                // These are sections, but they all get hidden in the same place.
                'psims',
                'psims_risk',
                'psims_went_well_section',
                'psims_medications',
                'psims_equipment',
                'psims_built_environment',
                'psims_furniture_fittings_section',
                'psims_it_systems_software',
                'psims_blood_products',
                'psims_tissues_organs',
                'psims_involved_persons',
                'psims_reporter',

                // Actual fields.
                'drug_insufficient_details',
                'drug_involvement_factors',
                'drug_reaction',
                'drug_reaction_other',
                'drug_used_too_much',
                'drug_wrong_details',
                'drug_wrong_usage_details',
                'drug_involved_processes',
                'drug_involved_processes_other',
                'drug_given_incorrectly',
                'problem_meds_packaging',
                'problem_description_drugs',
                'psims_device_type',
                'device_type_other',
                'device_broken_details',
                'device_not_enough_details',
                'device_usage_factors',
                'device_used_unnecessarily',
                'device_usage_details',
                'device_problem_description',
                'approximate_date',
                'duty_of_candour',
                'psims_event_type',
                'psims_safety_incident_occurred',
                'psims_estimated_time',
                'psims_outcome_type',
                'psims_involved_agents',
                'psims_medication_admin',
                'psims_healthcare_process',
                'psims_level_of_concern',
                'psims_detection_factors',
                'psims_detection_point',
                'psims_safety_challenges',
                'psims_responsible_specialty',
                'psims_service_area',
                'psims_risk_theme',
                'psims_risk_service_area',
                'psims_risk_imminent',
                'psims_risk_time_frame',
                'psims_care_detection_factor',
                'psims_risk_identified_location',
                'psims_location_at_risk',
                'psims_location_known',
                'psims_location_within_service',
                'psims_env_involvement_factors',
                'psims_furniture_fittings',
                'psims_furniture_broken_details',
                'psims_furniture_involved_factor',
                'psims_furniture_not_used',
                'psims_furniture_wrong_usaged',
                'psims_it_involvement_factors',
                'psims_blood_not_used',
                'psims_blood_damaged_details',
                'psims_blood_insufficient_detail',
                'psims_blood_involvement_factors',
                'psims_blood_used_too_much',
                'psims_blood_wrong_details',
                'psims_problem_blood_products',
                'psims_tissue_deficient_details',
                'psims_tissue_involvement_factor',
                'psims_tissue_not_used',
                'psims_tissue_used_too_much',
                'psims_tissue_wrong_details',
                'psims_tissue_damaged_details',
                'psims_involved_persons_actions',
                'psims_people_action_factors',
                'psims_people_action_too_much',
                'psims_people_incorrect_details',
                'psims_people_action_details',
                'psims_people_involvement_factor',
                'psims_people_involved_details',
                'psims_people_omitted_action',
                'psims_people_unavailable_detail',
                'psims_people_wrong_action',
                'psims_problem_description_actions',
                'psims_problem_description_involvement',
                'psims_detection_point_other',
                'psims_went_well',
                'psims_radiotherapy_incident_code',
                'psims_responsible_specialty_other',
                'psims_risk_theme_other',
                'psims_risk_population',
                'psims_risk_timeframe_other',
                'psims_risk_description',
                'psims_good_care_detection_factor_other',
                'psims_how_future_occurrence',
                'psims_strengths_of_care',
                'psims_identified_location_other',
                'psims_identified_organisation_other',
                'psims_built_environment_involved',
                'psims_problem_description_built_environment',
                'psims_furniture_fittings_other',
                'psims_problem_description_furniture_fittings',
                'psims_problem_description_systems_software',
                'psims_problem_description_blood',
                'psims_problem_description_tissues_organs',
                'psims_involved_persons_actions_other',
                'psims_reporter_role',
                'psims_reporter_role_other',
                'psims_reporter_involvement',
                'psims_reporter_involvement_other',
                'psims_reporter_organisation',
                'psims_reporter_organisation_other',
                'psims_reporter_type',
                'psims_reporter_type_other',
                'psims_why_anonymous',
                'psims_why_anonymous_other',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    // Same, but on the Contacts module.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'RemoveHiddenField',
        'params' => [
            'fields' => [
                'psims_age_years',
                'psims_clinical_outcome',
                'psims_patient_sequence',
                'psims_physical_harm',
                'psims_psychological_harm',
                'psims_strength_of_association',
                'psims_gender',
            ],
        ],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_90()
{
    // Set Mandatory fields for the PSIMS section.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_event_type',
                'psims_safety_incident_occurred',
                'psims_outcome_type',
                'psims_involved_agents',
                'psims_level_of_concern',
                'psims_detection_point',
                'psims_detection_point_other',
                'psims_safety_challenges',
                'psims_radiotherapy_incident_code',
                'psims_responsible_specialty',
                'psims_service_area',
            ],
        ],
        'levels' => [1 => true],
    ]);

    // Setup SECTION actions for the PSIMS section.

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'section' => 'psims_risk',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'section' => 'psims_went_well_section',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_medications',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_equipment',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_built_environment',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_furniture_fittings_section',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_it_systems_software',
                'values' => ['9'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_blood_products',
                'values' => ['7'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_tissues_organs',
                'values' => ['8'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'section' => 'psims_involved_persons',
                'values' => ['10'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    // Set up FIELD actions for the PSIMS section.

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_involved_agents',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_level_of_concern',
                'values' => ['1', '2'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_safety_challenges',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_action_factors',
            'action' => [
                'field' => 'psims_people_action_too_much',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_responsible_specialty',
                'values' => ['1', '3'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_safety_incident_occurred',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_outcome_type',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_event_type',
            'action' => [
                'field' => 'psims_detection_point',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'field' => 'psims_medication_admin',
                'alerttext' => ['7' => 'Provide your email address in the Reporter section so that the MHRA can follow up with you about this incident if they need to.'],
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_involved_agents',
            'action' => [
                'field' => 'psims_medication_admin',
                'alerttext' => ['7' => 'Provide your email address in the Reporter section so that the MHRA can follow up with you about this incident if they need to.'],
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_detection_point',
            'action' => [
                'field' => 'psims_detection_factors',
                'values' => ['1', '2', '3', '4', '5', '6', '7'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_detection_point',
            'action' => [
                'field' => 'psims_detection_point_other',
                'values' => ['8'],
            ],
        ],
        'levels' => [1 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_safety_challenges',
            'action' => [
                'field' => 'psims_radiotherapy_incident_code',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    // Set up an action that just shows a message...
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_safety_incident_occurred',
            'action' => [
                'alerttext' => [7 => 'Please change the type of event to Incident'],
                'values' => ['y'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_89()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_it_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_it_involvement_factors',
            'action' => [
                'field' => 'psims_problem_description_systems_software',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_88()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_involved_persons_actions',
            'action' => [
                'field' => 'psims_involved_persons_actions_other',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_action_factors',
            'action' => [
                'field' => 'psims_people_action_too_much',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_action_factors',
            'action' => [
                'field' => 'psims_people_action_details',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_action_factors',
            'action' => [
                'field' => 'psims_people_wrong_action',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_action_factors',
            'action' => [
                'field' => 'psims_people_omitted_action',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_involvement_factor',
            'action' => [
                'field' => 'psims_people_unavailable_detail',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_people_involvement_factor',
            'action' => [
                'field' => 'psims_problem_description_involvement',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_87()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_not_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_damaged_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_insufficient_detail'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_used_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_wrong_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_blood_products'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_blood_used_too_much',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_blood_insufficient_detail',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_blood_wrong_details',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_blood_not_used',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_blood_damaged_details',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_blood_involvement_factors',
            'action' => [
                'field' => 'psims_problem_description_blood',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_86()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_deficient_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_involvement_factor'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_not_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_used_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_damaged_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_tissue_used_too_much',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_tissue_deficient_details',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_tissue_wrong_details',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_tissue_not_used',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_tissue_damaged_details',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_tissue_involvement_factor',
            'action' => [
                'field' => 'psims_problem_description_tissues_organs',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_85()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_theme'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_service_area'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_imminent'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_time_frame'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_risk_theme_other',
                'psims_risk_imminent',
                'psims_risk_timeframe_other',
                'psims_risk_description',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_risk_theme',
            'action' => [
                'field' => 'psims_risk_theme_other',
                'values' => ['8'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_risk_imminent',
            'action' => [
                'field' => 'psims_risk_time_frame',
                'values' => ['n', 'u'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_risk_time_frame',
            'action' => [
                'field' => 'psims_risk_timeframe_other',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_84()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_env_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_env_involvement_factors',
            'action' => [
                'field' => 'psims_problem_description_built_environment',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_83()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_good_care_detection_factor_other',
                'psims_how_future_occurrence',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_care_detection_factor',
            'action' => [
                'field' => 'psims_good_care_detection_factor_other',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_82()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'device_type_other',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_used_unnecessarily',
                'values' => ['1', '2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_usage_details',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_broken_details',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'device_usage_factors',
            'action' => [
                'field' => 'device_problem_description',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_81()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_clinical_outcome',
                'psims_gender',
                'psims_patient_sequence',
                'psims_physical_harm',
                'psims_psychological_harm',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_physical_harm',
            'action' => [
                'field' => 'psims_psychological_harm',
                'values' => [
                    '2',
                    '3',
                    '4',
                    '5',
                ],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_physical_harm',
            'action' => [
                'field' => 'psims_strength_of_association',
                'values' => [
                    '1',
                    '2',
                    '3',
                ],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_psychological_harm',
            'action' => [
                'field' => 'psims_strength_of_association',
                'values' => [
                    '1',
                    '2',
                ],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_80()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_event_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_safety_incident_occurred'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_estimated_time'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_outcome_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_involved_agents'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_medication_admin'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_healthcare_process'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_level_of_concern'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_detection_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_detection_point'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_safety_challenges'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_responsible_specialty'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_service_area'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_theme'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_service_area'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_imminent'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_time_frame'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_care_detection_factor'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_identified_location'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_location_at_risk'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_location_known'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_location_within_service'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_env_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_fittings'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_broken_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_involved_factor'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_not_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_wrong_usaged'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_it_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_not_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_damaged_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_insufficient_detail'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_used_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_blood_wrong_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_blood_products'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_deficient_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_involvement_factor'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_not_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_used_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_wrong_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_tissue_damaged_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_involved_persons_actions'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_action_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_action_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_incorrect_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_action_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_involvement_factor'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_involved_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_omitted_action'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_unavailable_detail'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_people_wrong_action'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_79()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'drug_reaction_other',
                'drug_involved_processes_other',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involvement_factors',
            'action' => [
                'field' => 'drug_used_too_much',
                'values' => ['1'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involvement_factors',
            'action' => [
                'field' => 'drug_insufficient_details',
                'values' => ['2'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involvement_factors',
            'action' => [
                'field' => 'drug_given_incorrectly',
                'values' => ['3'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involvement_factors',
            'action' => [
                'field' => 'problem_meds_packaging',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involvement_factors',
            'action' => [
                'field' => 'problem_description_drugs',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_reaction',
            'action' => [
                'field' => 'drug_reaction_other',
                'values' => ['8'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'drug_involved_processes',
            'action' => [
                'field' => 'drug_involved_processes_other',
                'values' => ['4'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_78()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_actions'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_involvement'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_detection_point_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_went_well'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_radiotherapy_incident_code'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_responsible_specialty_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_theme_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_population'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_timeframe_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_risk_description'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_good_care_detection_factor_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_how_future_occurrence'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_strengths_of_care'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_identified_location_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_identified_organisation_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_built_environment_involved'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_built_environment'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_furniture_fittings_other'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_furniture_fittings'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_systems_software'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_blood'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_problem_description_tissues_organs'],
        'levels' => [1 => true, 2 => true],
    ]);
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_involved_persons_actions_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_77()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddMandatoryField',
        'params' => [
            'fields' => [
                'psims_reporter_involvement_other',
                'psims_reporter_role_other',
                'psims_reporter_type_other',
                'psims_why_anonymous_other',
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_reporter_involvement',
            'action' => [
                'field' => 'psims_reporter_involvement_other',
                'values' => ['6'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_reporter_role',
            'action' => [
                'field' => 'psims_reporter_role_other',
                'values' => ['9'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_reporter_type',
            'action' => [
                'field' => 'psims_reporter_type_other',
                'values' => ['5'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddFieldShowAction',
        'params' => [
            'field' => 'psims_why_anonymous',
            'action' => [
                'field' => 'psims_why_anonymous_other',
                'values' => ['8'],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_75()
{
    // Hide the section.
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter'],
        'levels' => [1 => true, 2 => true],
    ]);

    // Then hide all the fields in the section.
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_role'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_involvement'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_involvement_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_organisation'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_organisation_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_role_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_type_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_why_anonymous'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_why_anonymous_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_76()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_age_years'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_clinical_outcome'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_patient_sequence'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_physical_harm'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_psychological_harm'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_strength_of_association'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'gender'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_role'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_involvement'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_involvement_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_organisation_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_role_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_reporter_type_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_why_anonymous'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_why_anonymous_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_74()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'approximate_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'duty_of_candour'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_73()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'psims_device_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_type_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_broken_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_not_enough_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_usage_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_used_unnecessarily'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_usage_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'device_problem_description'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_72()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_insufficient_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_involvement_factors'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_reaction'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_reaction_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_used_too_much'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_wrong_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_wrong_usage_details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_involved_processes'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_involved_processes_other'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'drug_given_incorrectly'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'problem_meds_packaging'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'problem_description_drugs'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_71()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_date_diff_days'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_date_diff_hours'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_score_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_score_time'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_70()
{
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_drug_documented_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_drug_reaction_effect'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_medical_condition'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_health_restored_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_concomitant_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_device_documented_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_device_effect_health'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'vlc_device_contribution'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_69()
{
    AlterAllDesigns([
        'modules' => [Module::ORGANISATIONS],
        'function' => 'AddHiddenField',
        'params' => ['field' => OrganisationFields::TYPE],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::ORGANISATIONS],
        'function' => 'AddHiddenField',
        'params' => ['field' => OrganisationFields::SUBTYPE],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_68()
{
    AlterAllDesigns([
        'modules' => [Module::SAFEGUARDING],
        'function' => 'AddSectionAction',
        'params' => [
            'field' => SafeguardingFields::ANON_REPORTING,
            'action' => [
                'section' => SafeguardingForm::SECTION_REPORTER,
                'values' => [
                    'N',
                ],
            ],
        ],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_67()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'apc_has_employment_child_contact'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'apc_aware_report'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'apc_aar'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'apc_employment_adult_contact'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'referral_subject_child'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'interpreter_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'child_witness'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'witness_aware_report'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_66()
{
    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_vanessa_law_org_name'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_vanessa_law_email_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::CONTACTS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_vanessa_law_hcid'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_65()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'tasks'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_64()
{
    // Hide the wrapping section...
    AlterAllDesigns([
        'modules' => [Module::INCIDENTS, Module::MORTALITY_REVIEW, Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'outbreak'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS, Module::MORTALITY_REVIEW, Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'outbreak_impact'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::INCIDENTS, Module::MORTALITY_REVIEW, Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'outbreak_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_63()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_outcome_grading'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_serious_incident'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_welsh_language'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_62()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddSectionAction',
        'params' => ['field' => 'referred_to_ombudsman', 'action' => ['section' => 'com_ombudsman_subject', 'values' => ['Y']]],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_61()
{
    // Hide the section.
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject'],
        'levels' => [1 => true, 2 => true],
    ]);

    // Then hide all of the fields in the section.
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_subject'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_sub_subject'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_narrative'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_outcome'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_recommendation'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_due_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_subject_submitted_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_60()
{
    AlterAllDesigns([
        'modules' => [Module::CLAIMS],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_grade_rating'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_59()
{
    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'referred_to_ombudsman'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_first_contact'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_evidence_due'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_evidence_submitted'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_reference'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_handler'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_current_stage'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'early_settlement_proposal'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_investigation_began'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_response_due'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_doc_inv_sub'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_inv_sub'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_draft_received'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_draft_response_due'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_rec_recieved'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_action_plan'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_draft_report_sub'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_report_received'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'final_rep_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_outcome'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => [Module::FEEDBACK],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ombudsman_learning'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_58()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_grade_rating'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_grade_initial_rating'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_57()
{
    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'admitting_physician'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'attending_physician'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'room_no'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'bed_no'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'admitting_service'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'attending_service'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'diagnostic'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'procedures'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_56()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'diagnostic'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'procedures'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'allergy_reaction'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'allergy_severity'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'allergen_type'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'allergy_clinical_status'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'onset_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_of_admission'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'recovery_date'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_55()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_rep_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_54()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'send_to_sfda'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_53()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_pat_expectations'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_pat_update_pref'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_52()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_blood_type_product'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_was_red_code_activated'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_was_fire_alarm_activated'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_was_building_evacuated'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_was_portable_fire_extinguishers_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_was_local_fire_department_informed'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_were_any_damages_to_property'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_morbidity_record_is'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_is_trigger_related_to_improper_assessment_of_patient'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_additional_morbidity_triggers'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_51()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'source_of_record'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'source_of_record'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'source_of_record'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'source_of_record'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_50()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'approved_by'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_49()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'exact_location'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_48()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'anon_reporting'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_47()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'classification_tree'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_46()
{
    return true;
}

function FormMigrationCode_16_1_45()
{
    AlterAllDesigns([
        'modules' => ['COM', 'INC'],
        'function' => 'AddMandatoryField',
        'params' => ['fields' => [
            'learnings_title',
            'key_learnings',
        ]],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_44()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'contact_numbers'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_43()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_position'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['ADM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'positions'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_42()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'adverse_drug_reactions'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_41()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'nationality'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_40()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_mob_category'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_mob_anonymous'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_39()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddFieldShowTags',
        'params' => [
            'fields' => [
                'csu_location_id',
                'csu_service_id',
            ],
        ],
    ]);

    return true;
}

function FormMigrationCode_16_1_38()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_ot_q21'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_ot_q22'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'flag_for_rib'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'flag_for_rib'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_37()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mca_or_na'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_36()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_specialty'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_specialty'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_specialty'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mor_specialty'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_35()
{
    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_inv_lessons_sub_category'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_hro_characteristics'],
        'levels' => [2 => true],
    ]);

    return true;
}


function FormMigrationCode_16_1_34()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_lesson_learned_sub_category'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'com_hro_characteristics'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_33()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'time_taken_to_submit'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_32()
{
    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mor_lessons'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mor_lessons_subcat'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mor_hro_characteristics'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_31()
{
    AlterAllDesigns([
        'modules' => ['INC', 'COM', 'MOR', 'CLA', 'CON', 'ACT', 'PAY', 'USE'],
        'function' => 'RemoveSlashes',
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_30()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_severity_initial'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_result_initial'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_score'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sac_score_initial'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_29()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'pressure_ulcers'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_28()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'removeDisplayCheckboxForStaffFields',
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'removeDisplayCheckboxForStaffFields',
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'removeDisplayCheckboxForStaffFields',
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_27()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sharps'],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_26()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'csu_level_of_harm'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_25()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'skin_lesions'],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_24()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'pressure_ulcer'],
        'levels' => [1 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_23()
{
    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mam_mortality_location_different_to_event_location'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mam_were_interventions_performed_necessary'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mam_discuss_case_at_mam'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'category'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sub_category'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'causes_of_death'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sub_causes'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'complications_causes'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sub_complications'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'non_cardiovascular_causes'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'surgical_complications'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_within_48_hours_of_surgical_or_invasive_procedure'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'diagnostic_workup_adequate'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_within_48_hours_of_admission'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'terminal_illness_known_on_admission'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'abnormal_test_results_addressed'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'quality_issues_identified'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'patient_under_50'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_associated_with_drug_reaction'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_associated_with_adverse_event'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_preventable_under_optimal_conditions'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'terminal_events_anticipated'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'diagnostic_workup_before_and_after_event_adequate_timely'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'documentation_deficiency'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'medication_error'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'hospital_acquired_infection'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'extended_length_of_stay'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'unplanned_return_to_or'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'unplanned_return_to_icu'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'family_patient_complaint'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'opportunity_report_initiated'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'preventative_measures_adequate'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'treatment_adequate'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'eol_preparation_met'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'future_corrective_measures_required'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'abnormal_investigations_result_or_physical_admission'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'quality_of_care_issue_identified'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'death_due_to_surgery'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'rrt_called'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'prognosis_discussed_with_family'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'deviation_from_standard_care'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'equipment_failure_identified'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'iatrogenic_event'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cause_of_death_identified'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'what_internal_factors_contributed_to_death'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_22()
{
    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'requested_consultant'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'priority_scale'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'is_this_record_sensitive'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_21()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'time_taken_to_submit'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_20()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_inv_lessons_sub_category'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'hro_characteristics'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_19()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'sharps'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_18()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'skin_lesions'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_17()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'pressure_ulcer'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_16()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'inc_consultants'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_15()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'spsc_national_data'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_14()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddFieldShowAction',
        'params' => ['field' => 'learnings_to_share', 'action' => ['field' => 'key_learnings', 'values' => [0 => 'Y']]],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddFieldShowAction',
        'params' => ['field' => 'learnings_to_share', 'action' => ['field' => 'learnings_title', 'values' => [0 => 'Y']]],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddFieldShowAction',
        'params' => ['field' => 'learnings_to_share', 'action' => ['field' => 'key_learnings', 'values' => [0 => 'Y']]],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddFieldShowAction',
        'params' => ['field' => 'learnings_to_share', 'action' => ['field' => 'learnings_title', 'values' => [0 => 'Y']]],
        'levels' => [2 => true],
    ]);

    return true;
}

/**
 * New fields for SPSC need to be hidden on DIF1 and DIF2.
 */
function FormMigrationCode_16_1_13()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'falls'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fire'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'blood_transfusion'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_12()
{
    AlterAllDesigns([

        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'learning'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'learning'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_11()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'subject_subtype'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'subject_type'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_10()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_sedation'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_9()
{
    AlterAllDesigns([

        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'learnings'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['COM'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'learnings'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_8()
{
    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'worker_details'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'health_service_site'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'rep_feedback_codes'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'rep_feedback_notes'],
        'levels' => [2 => true],
    ]);
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_weight'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_7()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHelpText',
        'params' => ['field' => 'first_day_of_disability'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_6()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'first_day_of_disability'],
        'levels' => [2 => true],
    ]);
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'initial_rtw_same_employer'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'agreement_to_compensate'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'late_reason_code'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'change_element_segment'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'change_reason'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cancel_reason'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'number_managed_care_orgs'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'number_changed_data_elements'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'number_cancel_elements'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'verification_number'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_5()
{
    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'edi_froi_maintenance_type'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_4()
{
    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'edi_iaiabc_sroi_filing_status'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_3()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'employer_paid_salary_as_compensation'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_maximum_medical_improvement'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'date_claim_admin_knew_lost_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'maintenance_type_code_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'maintenance_type_correction_code_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'employee_id_assigned_by_jurisdiction'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'dum_death_result'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'dum_impaired_percent'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'full_denial_effective_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'full_denial_reason_code'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'denial_rescission_date'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_2()
{
    AlterAllDesigns([
        'modules' => ['INC', 'CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'edi_cause_code'],
        'levels' => [2 => true],
    ]);

    return true;
}

function FormMigrationCode_16_1_1()
{
    $con1FieldsToHide = ['show_illness', 'illness', 'link_illness', 'absence_total', 'total_lost_time', 'show_restricted_time', 'restriction_total', 'total_restricted_time'];
    $con2FieldsToHide = ['show_illness', 'illness', 'link_illness', 'osha_date_hired', 'edi_wage_period', 'edi_employment_status', 'edi_employee_id_type', 'edi_work_loss_list', 'edi_physical_restrictions', 'edi_disability_type', 'edi_diagnosis', 'edi_agency_code', 'edi_ncci_class', 'edi_type_loss', 'edi_reporting_period', 'edi_date_disability_known_employer', 'edi_accident_premises', 'edi_return_to_work_type', 'edi_return_to_work_qualif', 'edi_froi_maintenance_type', 'edi_cause_code', 'edi_drug_screen_summary', 'claimant_medicare_beneficiary', 'claimant_payments_non_medical', 'date_medicare_confirmed', 'claimant_medicare_claim_number', 'date_mmsea_last_reported', 'delete_if_field_with_medicate', 'no_fault', 'no_fault_insurance_limit', 'no_fault_exhaust_date', 'mmsea_relationship_to_beneficiary', 'mmsea_type_of_representative', 'msp_effective_date', 'msp_termination_date', 'msp_type', 'disposition_code', 'tpoc_date', 'tpoc_amount', 'tpoc_date_delayed', 'tpoc_date_filed', 'tpoc_date_court_approval', 'tpoc_date_payment_issued', 'tpoc_deleted', 'absence_total', 'total_lost_time', 'show_restricted_time', 'restriction_total', 'total_restricted_time', 'icd_classification', 'icd_diagnosis_codes', 'icd_procedure_codes', 'hours_worked', 'hourly_rate', 'weekly_rate', 'monthly_rate'];
    $cla1FieldsToHide = ['has_orm', 'orm_termination_date'];
    $cla2FieldsToHide = ['osha_registered_establishment', 'osha_recordable', 'osha_privacy_case', 'osha_case_classification', 'osha_treated_in_er', 'osha_hospitalized_overnight', 'osha_before_incident_description', 'osha_direct_harm_cause', 'edi_jurisdiction', 'edi_date_extracted', 'edi_date_processed', 'edi_agency_claim_number', 'edi_r1_froi_status', 'edi_cause_code', 'edi_drug_screen_summary', 'edi_froi_correction_date', 'edi_claim_status'];
    $inc2FieldsToHide = ['osha_recordable'];

    foreach ($con1FieldsToHide as $fieldName) {
        AlterAllDesigns([
            'modules' => ['CON'],
            'function' => 'AddHiddenField',
            'params' => ['field' => $fieldName],
            'levels' => [1 => true],
        ]);
    }

    foreach ($con2FieldsToHide as $fieldName) {
        AlterAllDesigns([
            'modules' => ['CON'],
            'function' => 'AddHiddenField',
            'params' => ['field' => $fieldName],
            'levels' => [2 => true],
        ]);
    }

    foreach ($cla1FieldsToHide as $fieldName) {
        AlterAllDesigns([
            'modules' => ['CLA'],
            'function' => 'AddHiddenField',
            'params' => ['field' => $fieldName],
            'levels' => [1 => true],
        ]);
    }

    foreach ($cla2FieldsToHide as $fieldName) {
        AlterAllDesigns([
            'modules' => ['CLA'],
            'function' => 'AddHiddenField',
            'params' => ['field' => $fieldName],
            'levels' => [2 => true],
        ]);
    }

    foreach ($inc2FieldsToHide as $fieldName) {
        AlterAllDesigns([
            'modules' => ['INC'],
            'function' => 'AddHiddenField',
            'params' => ['field' => $fieldName],
            'levels' => [2 => true],
        ]);
    }

    return true;
}

function FormMigrationCode_16_1()
{
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'illness'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'show_illness'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddSectionAction',
        'params' => ['field' => 'show_illness', 'action' => ['section' => 'illness', 'values' => [0 => 'Y']]],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_medical_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_legal_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_temporary_indemnity_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_permanent_indemnity_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_remaining_medical_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_remaining_legal_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_remaining_temporary_indemnity_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'fin_remaining_permanent_indemnity_reserve_assigned'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['MOR'],
        'function' => 'ReplaceSectionKey',
        'params' => ['find' => 'speciality', 'replace' => 'details'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC', 'CLA', 'CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'osha'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA', 'INC', 'COM', 'MOR'],
        'function' => 'AddFieldShowTags',
        'params' => [
            'fields' => [
                'other_location',
                'other_service',
                'location_id',
                'service_id',
                'admission_location_id',
                'admission_service_id',
            ],
        ],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'workers_comp'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'pay_rates'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'lost_restricted_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddNewPanel',
        'params' => ['field' => 'lost_restricted_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'icd'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'mmsea'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA', 'CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'edi'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_inc_ourref'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_inc_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_city'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_state'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'time_employee_began_work'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'rre_id'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'msp_effective_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'msp_termination_date'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'msp_type'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'disposition_code'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'claimant_medicare_claim_number'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'edi_date_extracted'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'edi_date_processed'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'edi_agency_claim_number'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['AST'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'ast_generic_name'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['AST'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_product_liability_indicator'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['AST'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'link_product_alleged_harm'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'include_in_tpoc'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'tpoc'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddSectionAction',
        'params' => ['field' => 'include_in_tpoc', 'action' => ['section' => 'tpoc', 'values' => [0 => 'Y']]],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'tprop'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddNewPanel',
        'params' => ['field' => 'tprop'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'cla_generated_reference_id'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'cla_inc_ourref'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CLA'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'cla_inc_time'],
        'levels' => [2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddReadOnlyField',
        'params' => ['field' => 'date_medicare_confirmed'],
        'levels' => [2 => true],
    ]);

    // Hide Respondents section
    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'tax_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    // Hide Respondents section
    AlterAllDesigns([
        'modules' => ['ORG'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'tax_id'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_social_security_number'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_middle_name'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['ORG'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'org_city'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['ORG'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'org_state'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['ORG'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'org_county'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC', 'CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'incident_occurred_on_employer_premises'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC', 'CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'safeguard_provided'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC', 'CLA'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'safeguard_used'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'employment_termination_date'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'employee_state_hired'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'full_pay_injury_day'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'salary_continued'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_county'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_employment_status_code'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_process_level'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_job_code'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_supervisor_name'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_department'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_location_code'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_fte'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['CON'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'con_lawson_number'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function FormMigrationCode_15_0()
{
    // Change CON_DEFAULT to CON2_DEFAULT
    AlterAllDesigns([
        'modules' => ['ADM'],
        'function' => 'ReplaceFieldKey',
        'params' => ['find' => 'CON2_DEFAULT', 'replace' => 'CON_DEFAULT'],
        'levels' => [1 => true],
    ]);

    // Change AST_DEFAULT to AST2_DEFAULT
    AlterAllDesigns([
        'modules' => ['ADM'],
        'function' => 'ReplaceFieldKey',
        'params' => ['find' => 'AST2_DEFAULT', 'replace' => 'AST_DEFAULT'],
        'levels' => [1 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'imed_bnf_classification'],
        'levels' => [1 => true, 2 => true],
    ]);

    AlterAllDesigns([
        'modules' => ['INC'],
        'function' => 'AddHiddenField',
        'params' => ['field' => 'imed_clinical_trial'],
        'levels' => [1 => true, 2 => true],
    ]);

    return true;
}

function AddHiddenField($aParams)
{
    if (!Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        return;
    }
    if (!empty($aParams['migration_version'])) {
        $repository = Container::get(FormDesignRepository::class);
        if ($aParams['fields']) {
            foreach ($aParams['fields'] as $fieldName) {
                $repository->setContentAddHiddenField($aParams['file'], $fieldName, $aParams['migration_version']);
            }
        }
        if ($aParams['field']) {
            $repository->setContentAddHiddenField($aParams['file'], $aParams['field'], $aParams['migration_version']);
        }
    } else {
        Container::get(FormDesignLoader::class)->load($aParams['file']);
        if ($aParams['fields']) {
            foreach ($aParams['fields'] as $field) {
                $GLOBALS['HideFields'][$field] = true;
            }
        } else {
            $GLOBALS['HideFields'][$aParams['field']] = true;
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function RemoveHiddenField($aParams)
{
    if (!Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        return;
    }
    if (!empty($aParams['migration_version'])) {
        $repository = Container::get(FormDesignRepository::class);
        if ($aParams['fields']) {
            foreach ($aParams['fields'] as $fieldName) {
                $repository->setContentRemoveHiddenField($aParams['file'], $fieldName, $aParams['migration_version']);
            }
        }
        if ($aParams['field']) {
            $repository->setContentRemoveHiddenField($aParams['file'], $aParams['field'], $aParams['migration_version']);
        }
    } else {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if ($aParams['fields']) {
            foreach ($aParams['fields'] as $field) {
                unset($GLOBALS['HideFields'][$field]);
            }
        } else {
            unset($GLOBALS['HideFields'][$aParams['field']]);
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function AddFieldShowTags($aParams)
{
    if (file_exists($aParams['file'])) {
        require $aParams['file'];

        foreach ($aParams['fields'] as $field) {
            $GLOBALS['taggedFields'][$field] = true;
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function AddFieldShowLastChildFirst($aParams)
{
    if (file_exists($aParams['file'])) {
        require $aParams['file'];

        foreach ($aParams['fields'] as $field) {
            $GLOBALS['lastChildFirstFields'][$field] = true;
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function AddListingDesign($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        $GLOBALS['ListingDesigns'][$aParams['section']][$aParams['key']] = $aParams['listing_id'];

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function MigrateExtraFieldGroups($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (is_array($GLOBALS['DIF1UDFGroups'])) {
            foreach ($GLOBALS['DIF1UDFGroups'] as $GroupID) {
                $MaxOrder = 0;

                $NextID = count($GLOBALS['ExtraSections']) + 1;
                $GLOBALS['ExtraSections'][$NextID] = 'Migrated Extra Field Section ' . $GroupID;

                if (!$GLOBALS['UserLabels']['UDFGROUP' . $GroupID]) {
                    $GroupName = DatixDBQuery::PDO_fetch('SELECT grp_descr FROM udf_groups WHERE recordid = :group_id', ['group_id' => $GroupID], PDO::FETCH_COLUMN);
                    $GLOBALS['UserLabels']['UDFGROUP' . $GroupID] = $GroupName;
                }

                $Fields = DatixDBQuery::PDO_fetch_all('SELECT recordid, fld_type, listorder FROM udf_links, udf_fields WHERE group_id = :group_id and recordid = field_id ORDER BY listorder', ['group_id' => $GroupID]);

                $SectionReplaceArray['UDFGROUP' . $GroupID] = 'section' . $NextID;

                // Find any pre-defined listorders
                if ($GLOBALS['FieldOrders']['UDFGROUP' . $GroupID]) {
                    $Order = 0;
                }

                if (is_array($GLOBALS['FieldOrders']['UDFGROUP' . $GroupID])) {
                    foreach ($GLOBALS['FieldOrders']['UDFGROUP' . $GroupID] as $OrderedField) {
                        ++$Order;
                        $FieldParts = explode('_', $OrderedField);
                        $FieldOrders[$FieldParts[3]] = $Order;
                    }

                    $MaxOrder = $Order;
                }

                foreach ($Fields as $FieldDetails) {
                    // If moved to another section, we don't have to generate an order for it in this section.
                    if (!$GLOBALS['MoveFieldsToSections']['UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid']]) {
                        $GLOBALS['ExtraFields'][$FieldDetails['recordid'] . '_' . $GroupID] = 'section' . $NextID;

                        if ($FieldOrders[$FieldDetails['recordid']]) {
                            $FieldOrder = $FieldOrders[$FieldDetails['recordid']];
                        } else {
                            ++$MaxOrder;
                            $FieldOrder = $MaxOrder;
                        }

                        $GLOBALS['FieldOrders']['section' . $NextID][$FieldOrder] = 'UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid'];
                    } else {
                        // moved to another section, so we need to add it to this section
                        $GLOBALS['ExtraFields'][$FieldDetails['recordid'] . '_' . $GroupID] = $GLOBALS['MoveFieldsToSections']['UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid']]['New'];
                        unset($GLOBALS['MoveFieldsToSections']['UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid']]);
                    }

                    $FieldReplaceArray['UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid']] = 'UDF_' . $FieldDetails['fld_type'] . '_' . $GroupID . '_' . $FieldDetails['recordid'];
                }
            }
        }

        unset($GLOBALS['DIF1UDFGroups'], $GLOBALS['FieldOrders']['UDFGROUP' . $GroupID]);

        if ($GLOBALS['FieldOrders']['section' . $NextID]) {
            ksort($GLOBALS['FieldOrders']['section' . $NextID]);
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();

        if (isset($SectionReplaceArray) && is_array($SectionReplaceArray)) {
            ReplaceSectionKey(['file' => $aParams, 'replace_array' => $SectionReplaceArray]);
        }

        if (!empty($FieldReplaceArray)) {
            foreach ($FieldReplaceArray as $find => $replace) {
                ReplaceFieldKey(['find' => $find, 'replace' => $replace, 'file' => $aParams['file']]);
            }
        }

        ReplaceSectionKey(['file' => $aParams['file'], 'replace_array' => ['udffrommain' => 'orphanedudfs']]);
    }
}

/**
 * @param array $aParams
 *
 * @psalm-param array{
 *     file: string,
 *     fields?: list<array{string,string}|string>,
 *     field?: string,
 *     migration_version?: string
 * } $aParams
 *
 * @throws ConnectionException
 * @throws JsonException
 * @throws Throwable
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 */
function AddMandatoryField($aParams): void
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        $repository = Container::get(FormDesignRepository::class);

        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (is_array($aParams['fields'])) {
            foreach ($aParams['fields'] as $field) {
                if (is_array($field)) {
                    $fieldName = $field[0];
                    $value = $field[1];
                } else {
                    $fieldName = $field;
                    $value = true;
                }

                $GLOBALS['MandatoryFields'][$fieldName] = $value;

                if (!empty($aParams['migration_version'])) {
                    $repository->setContentAddFieldToType(
                        $aParams['file'],
                        $fieldName,
                        FormDesignGlobals::MANDATORY_FIELDS,
                        $aParams['migration_version'],
                        $value,
                    );
                }
            }
        } else {
            $field = $aParams['field'];
            $GLOBALS['MandatoryFields'][$field] = true;

            if (!empty($aParams['migration_version'])) {
                $repository->setContentAddFieldToType(
                    $aParams['file'],
                    $field,
                    FormDesignGlobals::MANDATORY_FIELDS,
                    $aParams['migration_version'],
                    true,
                );
            }
        }

        if (empty($aParams['migration_version'])) {
            SaveFormDesignValuesToFile($aParams['file']);
        }

        unsetFormDesignSettings();
    }
}


/**
 * @param array $aParams
 *
 * @psalm-param array{
 *  file: string,
 *  fields: list<string>,
 *  migration_version?: string
 * } $aParams
 *
 * @throws Throwable
 * @throws ConnectionException
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 *
 * Remove field from mandatory list
 *
 * @todo This should eventually be refactored to use plural form of fields for all calls.
 */
function RemoveMandatoryField($aParams)
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        foreach ($aParams['fields'] as $field) {
            unset($GLOBALS[FormDesignGlobals::MANDATORY_FIELDS][$field]);

            if (!empty($aParams['migration_version'])) {
                $repository->setContentRemoveFieldFromType(
                    $aParams['file'],
                    $field,
                    FormDesignGlobals::MANDATORY_FIELDS,
                    $aParams['migration_version'],
                );
            }
        }

        if (empty($aParams['migration_version'])) {
            SaveFormDesignValuesToFile($aParams['file']);
        }

        unsetFormDesignSettings();
    }
}

function ReplaceSectionKey(array $params): void
{
    $repository = Container::get(FormDesignRepository::class);
    if (!$repository->formDesignExists($params['file'])) {
        return;
    }

    $json = $repository->getContent($params['file']);

    $content = $json === null ? [] : json_decode($json, true, 512, JSON_THROW_ON_ERROR);

    foreach ($params['repalce_array'] as $key => $value) {
        $content[$key] = $value;
    }

    $repository->updateContent($params['file'], empty($content) ? null : json_encode($content, JSON_THROW_ON_ERROR));
}

// custom field used when splitting contact sections
function SplitContactSection($aParams)
{
    global $ModuleDefs;

    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (is_array($GLOBALS['OrderSections'])) {
            $SectionOrderArray = array_flip($GLOBALS['OrderSections']);

            foreach ($GLOBALS['OrderSections'] as $order => $Section) {
                if ($order > $SectionOrderArray['contacts']) {
                    $NewOrderArray[$order + count($ModuleDefs[$aParams['module']]['CONTACTTYPES'])] = $Section;
                } else {
                    $NewOrderArray[$order] = $Section;
                }
            }

            if ($SectionOrderArray['contacts']) {
                $increment = 0;

                foreach ($ModuleDefs[$aParams['module']]['CONTACTTYPES'] as $ContactTypeDetails) {
                    $NewOrderArray[$SectionOrderArray['contacts'] + $increment] = 'contacts_' . $ContactTypeDetails['Type'];
                    ++$increment;
                }
            }

            $GLOBALS['OrderSections'] = $NewOrderArray;
        }

        if ($GLOBALS['NewPanels']['contacts']) {
            $PanelSet = false;

            foreach ($ModuleDefs[$aParams['module']]['CONTACTTYPES'] as $ContactTypeDetails) {
                if (!$PanelSet) {
                    $GLOBALS['NewPanels']['contacts_type_' . $ContactTypeDetails['Type']] = true;
                    $PanelSet = true;
                }
            }
        }

        if (is_array($GLOBALS['ExpandSections'])) {
            foreach ($GLOBALS['ExpandSections'] as $field => $actions) {
                foreach ($actions as $key => $ActionDetails) {
                    if ($ActionDetails['section'] == 'contacts') {
                        foreach ($ModuleDefs[$aParams['module']]['CONTACTTYPES'] as $ContactTypeDetails) {
                            $NewActionDetails = $ActionDetails;
                            $NewActionDetails['section'] = 'contacts_type_' . $ContactTypeDetails['Type'];
                            $GLOBALS['ExpandSections'][$field][] = $NewActionDetails;
                        }

                        unset($GLOBALS['ExpandSections'][$field][$key]);
                    }
                }
            }
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

// Custom field used when moving ConForms data to FormDesigns
function ConFormsToFormDesigns($aParams)
{
    global $ModuleDefs;

    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (is_array($GLOBALS['ContactForms'])) {
            foreach ($GLOBALS['ContactForms'] as $Type => $Form) {
                $GLOBALS['FormDesigns']['contacts_type_' . $Type][$Type] = $Form;
            }

            unset($GLOBALS['ContactForms']);
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

/**
 * @param array $aParams
 *
 * @psalm-param array{
 *  file: string,
 *  fields: list<string>,
 *  migration_version?: string
 * } $aParams
 *
 * @throws Throwable
 * @throws ConnectionException
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 *
 * Add Read Only Field
 */
function AddReadOnlyField($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        $repository = Container::get(FormDesignRepository::class);

        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (is_array($aParams['fields'])) {
            foreach ($aParams['fields'] as $field) {
                if (is_array($field)) {
                    $fieldName = $field[0];
                    $value = $field[1];
                } else {
                    $fieldName = $field;
                    $value = true;
                }

                $GLOBALS[FormDesignGlobals::READ_ONLY_FIELDS][$fieldName] = $value;

                if (!empty($aParams['migration_version'])) {
                    $repository->setContentAddFieldToType(
                        $aParams['file'],
                        $fieldName,
                        FormDesignGlobals::READ_ONLY_FIELDS,
                        $aParams['migration_version'],
                        $value,
                    );
                }
            }
        } else {
            $field = $aParams['field'];
            $GLOBALS[FormDesignGlobals::READ_ONLY_FIELDS][$field] = true;

            if (!empty($aParams['migration_version'])) {
                $repository->setContentAddFieldToType(
                    $aParams['file'],
                    $field,
                    FormDesignGlobals::READ_ONLY_FIELDS,
                    $aParams['migration_version'],
                    true,
                );
            }
        }

        if (empty($aParams['migration_version'])) {
            SaveFormDesignValuesToFile($aParams['file']);
        }

        unsetFormDesignSettings();
    }
}

function AddDefaultValue($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (!$GLOBALS['DefaultValues'][$aParams['field']]) {
            $GLOBALS['DefaultValues'][$aParams['field']] = $aParams['value'];
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function RelabelField($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (!$GLOBALS['UserLabels'][$aParams['field']]) {
            $GLOBALS['UserLabels'][$aParams['field']] = $aParams['value'];
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

/**
 * @throws ConnectionException if there is a problem with the connection
 * @throws Throwable if there is a problem updating or backing up the content
 *
 * Add Section Action
 */
function AddSectionAction(array $aParams): void
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        if (!empty($aParams['migration_version'])) {
            $content = [];
            $contentString = $repository->getContent($aParams['file']);
            if (!empty($contentString)) {
                $content = json_decode($contentString, true, 512, JSON_THROW_ON_ERROR);
                $content = $content[FormDesignGlobals::EXPAND_SECTIONS][$aParams['field']] ?: [];
            }

            if (!in_array($aParams['action'], $content, true)) {
                $content[] = $aParams['action'];
            }

            $repository->setContent($aParams['file'], $aParams['field'], FormDesignGlobals::EXPAND_SECTIONS, $content, $aParams['migration_version']);
        } else {
            Container::get(FormDesignLoader::class)->load($aParams['file']);

            if (
                !$GLOBALS['ExpandSections'][$aParams['field']]
                || !in_array($aParams['action'], $GLOBALS['ExpandSections'][$aParams['field']])
            ) {
                $GLOBALS['ExpandSections'][$aParams['field']][] = $aParams['action'];
            }

            SaveFormDesignValuesToFile($aParams['file']);
        }

        unsetFormDesignSettings();
    }
}

// Remove section action
/**
 * @throws Throwable
 * @throws ConnectionException
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws JsonException
 */
function RemoveSectionAction(array $actionParameters): void
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($actionParameters['file'])) {
        Container::get(FormDesignLoader::class)->load($actionParameters['file']);

        if ($GLOBALS['ExpandSections'][$actionParameters['field']]) {
            $expandSections = $GLOBALS['ExpandSections'][$actionParameters['field']];
            $targetSection = $actionParameters['action']['section'];
            $triggerValues = $actionParameters['action']['values'];
            if (is_array($triggerValues)) {
                sort($triggerValues); // For comparison reasons to minimise chance of [3,1] != [1,3]
            }

            foreach ($expandSections as $index => $sectionData) {
                if ($sectionData['section'] !== $targetSection) {
                    continue;
                }
                sort($sectionData['values']);
                if ($triggerValues === null || $triggerValues == $sectionData['values']) {
                    unset($GLOBALS['ExpandSections'][$actionParameters['field']][$index]);
                }
            }

            $triggerField = $actionParameters['field'];


            $GLOBALS[FormDesignGlobals::EXPAND_SECTIONS][$triggerField] = array_values($GLOBALS[FormDesignGlobals::EXPAND_SECTIONS][$triggerField]);
            if (!empty($actionParameters['migration_version'])) {
                $repository->setContent(
                    $actionParameters['file'],
                    $actionParameters['field'],
                    FormDesignGlobals::EXPAND_SECTIONS,
                    array_values($GLOBALS[FormDesignGlobals::EXPAND_SECTIONS][$actionParameters['field']]),
                    $actionParameters['migration_version'],
                );
            }
        }

        if (empty($actionParameters['migration_version'])) {
            SaveFormDesignValuesToFile($actionParameters['file']);
        }

        unsetFormDesignSettings();
    }
}

/**
 * Add trigger to field.
 *
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws ConnectionException
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws JsonException
 * @throws Throwable
 */
function AddFieldShowAction(array $aParams): void
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        if (!empty($aParams['migration_version'])) {
            $content = [];
            $contentString = $repository->getContent($aParams['file']);
            if (!empty($contentString)) {
                $content = json_decode($contentString, true, 512, JSON_THROW_ON_ERROR);
                $content = $content[FormDesignGlobals::EXPAND_FIELDS][$aParams['field']] ?: [];
            }

            if (!in_array($aParams['action'], $content, true)) {
                $content[] = $aParams['action'];
            }

            $repository->setContent($aParams['file'], $aParams['field'], FormDesignGlobals::EXPAND_FIELDS, $content, $aParams['migration_version']);
        } else {
            Container::get(FormDesignLoader::class)->load($aParams['file']);

            if (!$GLOBALS['ExpandFields'][$aParams['field']] || !in_array($aParams['action'], $GLOBALS['ExpandFields'][$aParams['field']], true)) {
                $GLOBALS['ExpandFields'][$aParams['field']][] = $aParams['action'];
            }

            SaveFormDesignValuesToFile($aParams['file']);
        }

        unsetFormDesignSettings();
    }
}

/**
 * Remove field action.
 *
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws ConnectionException
 * @throws Throwable
 * @throws JsonException
 *
 * @todo fully document array shape of $actionParameters
 */
function RemoveFieldShowAction(array $actionParameters): void
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($actionParameters['file'])) {
        Container::get(FormDesignLoader::class)->load($actionParameters['file']);
        $triggerField = $actionParameters['field'];
        $targetField = $actionParameters['action']['field'];
        $triggerValues = $actionParameters['action']['values'];
        if (is_array($triggerValues)) {
            sort($triggerValues);
        }

        if (isset($GLOBALS[FormDesignGlobals::EXPAND_FIELDS][$triggerField])) {
            $expandFields = $GLOBALS[FormDesignGlobals::EXPAND_FIELDS][$triggerField];

            foreach ($expandFields as $index => $fieldData) {
                if ($fieldData['field'] !== $targetField) {
                    continue;
                }
                sort($fieldData['values']);
                if ($triggerValues === null || $triggerValues == $fieldData['values']) {
                    unset($expandFields[$index]);
                }
            }
            // Re-index before assigning back to ensure it still works with legacy logic that does something like
            //   for ($index = 0; $index < count(...); $index++)
            $GLOBALS[FormDesignGlobals::EXPAND_FIELDS][$triggerField] = array_values($expandFields);
            if (!empty($actionParameters['migration_version'])) {
                $repository->setContent(
                    $actionParameters['file'],
                    $triggerField,
                    FormDesignGlobals::EXPAND_FIELDS,
                    array_values($expandFields),
                    $actionParameters['migration_version'],
                );
            }
        }

        if (empty($actionParameters['migration_version'])) {
            SaveFormDesignValuesToFile($actionParameters['file']);
        }

        unsetFormDesignSettings();
    }
}

function AddExtraText($aParams)
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        if (!empty($aParams['migration_version'])) {
            $repository->setContent($aParams['file'], $aParams['field'], FormDesignGlobals::USER_EXTRA_TEXT, $aParams['text'], $aParams['migration_version']);
        } else {
            Container::get(FormDesignLoader::class)->load($aParams['file']);

            if (!isset($GLOBALS['UserExtraText'][$aParams['field']]) || $GLOBALS['UserExtraText'][$aParams['field']] == '') {
                $GLOBALS['UserExtraText'][$aParams['field']] = $aParams['text'];
            }

            SaveFormDesignValuesToFile($aParams['file']);
            unsetFormDesignSettings();
        }
    }
}

/**
 * @param array $aParams
 *
 * @psalm-param array{
 *  file: string,
 *  field: string,
 *  text: array<numeric-string|int, string>,
 *  migration_version?: string
 * } $aParams
 *
 * @throws ConnectionException if there is a problem with the connection
 * @throws Throwable if there is a problem updating or backing up the content
 */
function AddHelpText($aParams)
{
    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        if (!empty($aParams['migration_version'])) {
            $repository->setContent($aParams['file'], $aParams['field'], FormDesignGlobals::HELP_TEXTS, $aParams['text'], $aParams['migration_version']);
        } else {
            Container::get(FormDesignLoader::class)->load($aParams['file']);

            if (!isset($GLOBALS['HelpTexts'][$aParams['field']]) || $GLOBALS['HelpTexts'][$aParams['field']] == '') {
                $GLOBALS['HelpTexts'][$aParams['field']] = $aParams['text'];
            }

            SaveFormDesignValuesToFile($aParams['file']);
            unsetFormDesignSettings();
        }
    }
}

/**
 * @psalm-param array{
 *  file: string,
 *  fields: list<string>,
 *  migration_version?: string
 * } $aParams
 *
 * @throws Throwable
 * @throws ConnectionException
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \Psr\Container\ContainerExceptionInterface
 *
 * Remove Help Text for the fields
 */
function RemoveHelpText(array $aParams)
{
    if (empty($aParams['migration_version'])) {
        throw new \Exception('RemoveHelpText is not supported without a migration version');
    }

    $repository = Container::get(FormDesignRepository::class);
    if ($repository->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        foreach ($aParams['fields'] as $field) {
            unset($GLOBALS[FormDesignGlobals::HELP_TEXTS][$field]);

            $repository->setContentRemoveFieldFromType(
                $aParams['file'],
                $field,
                FormDesignGlobals::HELP_TEXTS,
                $aParams['migration_version'],
            );
        }

        unsetFormDesignSettings();
    }
}

function ReplaceFieldKey($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        foreach (FormDesignGlobals::FORM_DESIGN_GLOBALS as $DesignKey) {
            $NewArray = [];

            if ($DesignKey == 'FieldOrders') {
                if (is_array($GLOBALS[$DesignKey])) {
                    foreach ($GLOBALS[$DesignKey] as $Section => $Details) {
                        foreach ($Details as $Order => $Field) {
                            if ($Field == $aParams['find']) {
                                $GLOBALS[$DesignKey][$Section][$Order] = $aParams['replace'];
                            }
                        }
                    }
                }
            } elseif (is_array($GLOBALS[$DesignKey])) {
                foreach ($GLOBALS[$DesignKey] as $Key => $Details) {
                    if ($Key == $aParams['find']) {
                        $NewArray[$aParams['replace']] = $GLOBALS[$DesignKey][$Key];
                    } else {
                        $NewArray[$Key] = $GLOBALS[$DesignKey][$Key];
                    }
                }

                $GLOBALS[$DesignKey] = $NewArray;
            }
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function AddNewPanel($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        $GLOBALS['NewPanels'][$aParams['panel']] = true;

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function RemoveSlashes($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        // Reduce any instance of 4 or more "\"s to just two. This won't necessarily be correct, but will prevent
        // the processing overwhelming memory and will allow us to assess the various usages
        foreach ($GLOBALS['FormTitle'] as $key => $FormTitle) {
            $GLOBALS['FormTitle'][$key] = preg_replace('/\\\\{3,}/u', '\\\\\\\\', $FormTitle);
        }

        foreach ($GLOBALS['FormTitleDescr'] as $key => $FormTitleDescr) {
            $GLOBALS['FormTitleDescr'][$key] = preg_replace('/\\\\{3,}/u', '\\\\\\\\', $FormTitleDescr);
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function DefineDefaultDSTContactForm($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        $GLOBALS['FormTitle'] = 'SABS and Distribution List Contact Form';
        $GLOBALS['Show_all_section'] = 'N';
        $GLOBALS['HideFields'] = [
            'link' => true,
            'injury' => true,
            'link_sirs' => true,
            'link_worked_alone' => true,
            'link_become_unconscious' => true,
            'link_req_resuscitation' => true,
            'link_hospital_24hours' => true,
            'property_section' => true,
            'con_police_number' => true,
            'con_work_alone_assessed' => true,
            'employee' => true,
            'udffrommain' => true,
            'progress_notes' => true,
            'incidents' => true,
            'risks' => true,
            'pals' => true,
            'complaints' => true,
            'word' => true,
        ];

        $GLOBALS['MandatoryFields'] = [
            'link_role' => 'link',
            'con_surname' => 'contact',
        ];

        $GLOBALS['UserExtraText'] = [
            'con_dod' => 'Fill this in for a patient who has died',
        ];

        $GLOBALS['ExpandSections'] = [
            'show_injury' => [
                0 => [
                    'section' => 'injury',
                    'alerttext' => '',
                    'values' => [
                        0 => 'Y',
                    ],
                ],
            ],
            'link_pprop_damaged' => [
                0 => [
                    'section' => 'property_section',
                    'alerttext' => '',
                    'values' => [
                        0 => 'Y',
                    ],
                ],
            ],
        ];

        $GLOBALS['ExpandFields'] = [
            'link_police_pursue' => [
                0 => [
                    'field' => 'link_police_persue_reason',
                    'alerttext' => '',
                    'values' => [
                        0 => 'N',
                    ],
                ],
            ],
        ];

        $GLOBALS['NewPanels'] = [
            'link' => true,
            'contact' => true,
            'employee' => true,
            'incidents' => true,
            'risks' => true,
            'pals' => true,
            'complaints' => true,
            'word' => true,
        ];

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

function SetContactForm($module, $ModFormID, $CONFormID, $ConLinkType = 'N', $FormLevel = 2)
{
    $File = GetSettingsFilename($ModFormID, $module, $FormLevel);

    if (Container::get(FormDesignRepository::class)->formDesignExists($File)) {
        Container::get(FormDesignLoader::class)->load($File);
    } elseif ($ModFormID == 0) {
        $StandardFile = GetSettingsFilename($ModFormID, $module, $FormLevel, '', 'standard');

        if (file_exists($StandardFile)) {
            require $StandardFile;
        }
    }

    if (!$GLOBALS['ContactForms'][$ConLinkType]) {
        $GLOBALS['ContactForms'][$ConLinkType] = $CONFormID;
    }

    SaveFormDesignValuesToFile($File);
    unsetFormDesignSettings();
}

// Returns the relative path to a settings file.
// Parameters:
// $FormID:         The ID number of a form
// $Module:         Three-letter module code
// $formlevel:      Form level - 1 or 2
// $SettType:       String: "form" (form design settings file) or "column"
//                  (listing setting file)
// $SettType2:      String: "user" (UserSettings file) or "standard"
function GetSettingsFilename($FormID = '', $Module = 'DIF1', $formlevel = '', $SettType = 'form', $SettType2 = 'user')
{
    global $ModuleDefs;

    $formlevel = ($formlevel == '' ? 1 : $formlevel);
    $Module = ($Module == 'DIF1' ? 'INC' : $Module);

    if (!array_key_exists($Module, $ModuleDefs)) {
        return '';
    }

    if ($SettType == 'column') {
        if ($SettType2 == 'standard') {
            if ($ModuleDefs[$Module]['GENERIC']) {
                if (file_exists('Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/StandardListSettings.php')) {
                    $SettingsFilename = 'Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/StandardListSettings.php';
                } else {
                    $SettingsFilename = 'Source/generic/StandardListSettings.php';
                }
            } else {
                $SettingsFilename = "{$ModuleDefs[$Module]['LIBPATH']}/Standard{$Module}ListSettings.php";
            }
        } elseif ($SettType2 == 'user') {
            $SettingsFilename = "client/User{$Module}ListSettings.php";
        }
    } else {
        if ($SettType2 == 'standard') {
            if ($ModuleDefs[$Module]['GENERIC']) {
                if (file_exists('Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/StandardLevel' . $formlevel . 'Settings.php')) {
                    $SettingsFilename = 'Source/generic_modules/' . $ModuleDefs[$Module]['GENERIC_FOLDER'] . '/StandardLevel' . $formlevel . 'Settings.php';
                } else {
                    $SettingsFilename = 'Source/generic/StandardLevel' . $formlevel . 'Settings.php';
                }
            } else {
                $SettingsFilename = "{$ModuleDefs[$Module]['LIBPATH']}/Standard{$ModuleDefs[$Module]['FORMS'][$formlevel]['CODE']}Settings.php";
            }
        } else {
            $formCode = $ModuleDefs[$Module]['FORMS'][$formlevel]['CODE'];

            $SettingsFileVar = "User{$formCode}SettingsFile";

            if ($GLOBALS[$SettingsFileVar]) {
                $SettingsFilename = $GLOBALS[$SettingsFileVar];
            } else {
                $SettingsFilename = "User{$formCode}Settings";
            }

            if ($FormID) {
                $SettingsFilename .= "_{$FormID}";
            }

            $SettingsFilename .= '.php';
        }

        $_SESSION['LASTUSEDFORMDESIGN'] = $SettingsFilename;
        $_SESSION['LASTUSEDFORMDESIGNFORMLEVEL'] = $formlevel;
    }

    return $SettingsFilename;
}

function SaveFormDesignValuesToFile(string $filename): void
{
    // form design globals
    global $HideFields, $ReadOnlyFields, $MandatoryFields, $TimestampFields, $OrderSections, $UserLabels, $UserExtraText,
    $DefaultValues, $TextareaMaxChars, $FieldOrders, $DIF1UDFGroups, $taggedFields,
    $ExpandSections, $ExpandFields, $HelpTexts, $UDFGroups, $NewPanels,
    $ContactMatch, $ContactForms, $FormDesigns, $EquipmentForms, $ListingDesigns, $MoveFieldsToSections,
    $FormTitle, $FormTitleDescr, $ExtraFields, $ExtraSections, $Show_all_section, $OrganisationMatch,
    $DisplayAsRadioButtons, $DisplayAsCheckboxes, $AccessibleForLoggedOutUsers, $LfpseFieldsHidable;

    $content = [];

    if ($FormTitle) {
        $content[FormDesignGlobals::FORM_TITLE] = $FormTitle;
    }

    if ($FormTitleDescr) {
        $content[FormDesignGlobals::FORM_TITLE_DESCR] = $FormTitleDescr;
    }

    if ($AccessibleForLoggedOutUsers) {
        $content[FormDesignGlobals::ACCESSIBLE_FOR_LOGGED_OUT_USERS] = $AccessibleForLoggedOutUsers;
    }

    if ($LfpseFieldsHidable) {
        $content[FormDesignGlobals::LFPSE_FIELDS_HIDABLE] = $LfpseFieldsHidable;
    }

    if ($Show_all_section) {
        $content[FormDesignGlobals::SHOW_ALL_SECTION] = $Show_all_section;
    }

    if ($HideFields) {
        $content[FormDesignGlobals::HIDE_FIELDS] = $HideFields;
    }

    if ($ReadOnlyFields) {
        $content[FormDesignGlobals::READ_ONLY_FIELDS] = $ReadOnlyFields;
    }

    if ($taggedFields) {
        $content[FormDesignGlobals::TAGGED_FIELDS] = $taggedFields;
    }

    if ($MandatoryFields) {
        $content[FormDesignGlobals::MANDATORY_FIELDS] = $MandatoryFields;
    }

    if ($DisplayAsCheckboxes) {
        $content[FormDesignGlobals::DISPLAY_AS_CHECKBOXES] = $DisplayAsCheckboxes;
    }

    if ($DisplayAsRadioButtons) {
        $content[FormDesignGlobals::DISPLAY_AS_RADIO_BUTTONS] = $DisplayAsRadioButtons;
    }

    if ($TimestampFields) {
        $content[FormDesignGlobals::TIMESTAMP_FIELDS] = $TimestampFields;
    }

    if ($OrderSections) {
        $content[FormDesignGlobals::ORDER_SECTIONS] = $OrderSections;
    }

    if ($UserLabels) {
        $content[FormDesignGlobals::USER_LABELS] = $UserLabels;
    }

    if ($UserExtraText) {
        $content[FormDesignGlobals::USER_EXTRA_TEXT] = $UserExtraText;
    }

    if ($DefaultValues) {
        $content[FormDesignGlobals::DEFAULT_VALUES] = $DefaultValues;
    }

    if ($TextareaMaxChars) {
        $content[FormDesignGlobals::TEXTAREA_MAX_CHARS] = $TextareaMaxChars;
    }

    if ($FieldOrders) {
        $content[FormDesignGlobals::FIELD_ORDERS] = $FieldOrders;
    }

    if ($DIF1UDFGroups) {
        $content[FormDesignGlobals::DIF_1_UDF_GROUPS] = $DIF1UDFGroups;
    }

    if ($ExpandSections) {
        $content[FormDesignGlobals::EXPAND_SECTIONS] = $ExpandSections;
    }

    if ($ExpandFields) {
        $content[FormDesignGlobals::EXPAND_FIELDS] = $ExpandFields;
    }

    if ($HelpTexts) {
        $content[FormDesignGlobals::HELP_TEXTS] = $HelpTexts;
    }

    if ($NewPanels) {
        $content[FormDesignGlobals::NEW_PANELS] = $NewPanels;
    }

    if ($ContactMatch) {
        $content[FormDesignGlobals::CONTACT_MATCH] = $ContactMatch;
    }

    if ($OrganisationMatch) {
        $content[FormDesignGlobals::ORGANISATION_MATCH] = $OrganisationMatch;
    }

    if ($ContactForms) {
        $content[FormDesignGlobals::CONTACT_FORMS] = $ContactForms;
    }

    if ($FormDesigns) {
        $content[FormDesignGlobals::FORM_DESIGNS] = $FormDesigns;
    }

    if ($EquipmentForms) {
        $content['EquipmentForms'] = $EquipmentForms;
    }

    if ($ListingDesigns) {
        $content[FormDesignGlobals::LISTING_DESIGNS] = $ListingDesigns;
    }

    if ($MoveFieldsToSections) {
        $content[FormDesignGlobals::MOVE_FIELDS_TO_SECTIONS] = $MoveFieldsToSections;
    }

    if ($ExtraSections) {
        $content[FormDesignGlobals::EXTRA_SECTIONS] = $ExtraSections;
    }

    if ($ExtraFields) {
        $content[FormDesignGlobals::EXTRA_FIELDS] = $ExtraFields;
    }

    if ($LockFieldAtribs) {
        $content[FormDesignGlobals::LOCK_FIELDS_ATTRIBUTES] = $LockFieldAtribs;
    }

    Container::get(FormDesignRepository::class)->updateContent($filename, empty($content) ? null : json_encode($content, JSON_THROW_ON_ERROR));
}

/**
 * Attempts to automatically fix LIB/AST1 form design files with malformed filenames (see DW-6367).
 */
function fixMissingFilenames()
{
    global $ModuleDefs, $FieldDefs;

    $fielddefs = $FieldDefs;

    // add linked equipment fields, which are defined under INC
    $fielddefs['AST'] = array_merge($fielddefs['AST'], [
        'link_damage_type' => [],
        'link_other_damage_info' => [],
        'link_replaced' => [],
        'link_replace_cost' => [],
        'link_repaired' => [],
        'link_repair_cost' => [],
        'link_written_off' => [],
        'link_disposal_cost' => [],
        'link_sold' => [],
        'link_sold_price' => [],
        'link_decommissioned' => [],
        'link_decommission_cost' => [],
        'link_residual_value' => [],
    ]);

    $badFiles = [];
    $goodFiles = [];

    require 'Source/generic_modules/LIB/BasicForm2.php';

    $moduleSections['LIB'] = array_keys($FormArray);

    // check client folder for form design files with incorrect filenames
    $dir = scandir(DatixConfig::CLIENT_FOLDER);

    foreach ($dir as $file) {
        if ($file == '.php' || preg_match('/^_\d+\.php$/u', $file)) {
            $badFiles[] = $file;
        }
    }

    // attempt to identify the module form the design file contents
    foreach ($badFiles as $badFile) {
        require DatixConfig::CLIENT_FOLDER . '/' . $badFile;

        $modules = ['LIB' => $ModuleDefs['LIB']['FORM_DESIGN_LEVEL_2_FILENAME'], 'AST' => $ModuleDefs['AST']['FORM_DESIGN_LEVEL_1_FILENAME']];

        // first check all form design arrays where the keys are field names for module-specific fields
        $settings = [
            'HideFields', 'ReadOnlyFields', 'MandatoryFields', 'TimestampFields', 'UserLabels', 'UserExtraText',
            'DefaultValues', 'TextareaMaxChars', 'ExpandSections', 'ExpandFields', 'HelpTexts', 'MoveFieldsToSections',
        ];

        foreach ($settings as $setting) {
            if (is_array($GLOBALS[$setting])) {
                foreach (array_keys($GLOBALS[$setting]) as $field) {
                    foreach ($modules as $module => $correctFileName) {
                        if (in_array($field, array_diff(array_keys($fielddefs[$module]), array_keys($fielddefs[$module == 'LIB' ? 'AST' : 'LIB'])))) {
                            // the field name is unique to this module's field definitions, so we can infer the correct filename for this form design file
                            $goodFiles[$badFile] = $correctFileName . $badFile;

                            break 3;
                        }
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // next, check all form design arrays where the keys are section names for module-specific sections
        $settings = ['HideFields', 'ReadOnlyFields', 'UserLabels', 'UserExtraText', 'NewPanels'];

        foreach ($settings as $setting) {
            if (is_array($GLOBALS[$setting])) {
                foreach (array_keys($GLOBALS[$setting]) as $section) {
                    foreach ($modules as $module => $correctFileName) {
                        if (in_array($section, array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                            // the section name is unique to this module, so we can infer the correct filename for this form design file
                            $goodFiles[$badFile] = $correctFileName . $badFile;

                            break 3;
                        }
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // still no idea what the file should be - let's look at other settings, starting with 'OrderSections'
        if (is_array($GLOBALS['OrderSections'])) {
            foreach ($GLOBALS['OrderSections'] as $section) {
                foreach ($modules as $module => $correctFileName) {
                    if (in_array($section, array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                        // the section name is unique to this module, so we can infer the correct filename for this form design file
                        $goodFiles[$badFile] = $correctFileName . $badFile;

                        break 2;
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // no? okay, check both sections and field in 'FieldOrders'
        if (is_array($GLOBALS['FieldOrders'])) {
            foreach ($GLOBALS['FieldOrders'] as $section => $fields) {
                foreach ($modules as $module => $correctFileName) {
                    // can we identify from the section?
                    if (in_array($section, array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                        // the section name is unique to this module, so we can infer the correct filename for this form design file
                        $goodFiles[$badFile] = $correctFileName . $badFile;

                        break 2;
                    }

                    // can we identify from any of the fields in this section?
                    foreach ($fields as $field) {
                        if (in_array($field, array_diff(array_keys($fielddefs[$module]), array_keys($fielddefs[$module == 'LIB' ? 'AST' : 'LIB'])))) {
                            // the field name is unique to this module's field definitions, so we can infer the correct filename for this form design file
                            $goodFiles[$badFile] = $correctFileName . $badFile;

                            break 3;
                        }
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // we're running out of options - perhaps one of the sections in 'ExpandSections'?
        if (is_array($GLOBALS['ExpandSections'])) {
            foreach ($GLOBALS['ExpandSections'] as $field => $sections) {
                foreach ($modules as $module => $correctFileName) {
                    foreach ($sections as $section) {
                        if (in_array($section['section'], array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                            // the section name is unique to this module, so we can infer the correct filename for this form design file
                            $goodFiles[$badFile] = $correctFileName . $badFile;

                            break 3;
                        }
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // or fields in 'ExpandFields'?
        if (is_array($GLOBALS['ExpandFields'])) {
            foreach ($GLOBALS['ExpandFields'] as $f => $fields) {
                foreach ($modules as $module => $correctFileName) {
                    foreach ($fields as $field) {
                        if (in_array($field['field'], array_diff(array_keys($fielddefs[$module]), array_keys($fielddefs[$module == 'LIB' ? 'AST' : 'LIB'])))) {
                            // the field name is unique to this module's field definitions, so we can infer the correct filename for this form design file
                            $goodFiles[$badFile] = $correctFileName . $badFile;

                            break 3;
                        }
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // perhaps one of the sections in 'MoveFieldsToSections'...
        if (is_array($GLOBALS['MoveFieldsToSections'])) {
            foreach ($GLOBALS['MoveFieldsToSections'] as $sections) {
                foreach ($modules as $module => $correctFileName) {
                    if (in_array($sections['Original'], array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                        // the section name is unique to this module, so we can infer the correct filename for this form design file
                        $goodFiles[$badFile] = $correctFileName . $badFile;

                        break 2;
                    }

                    if (in_array($sections['New'], array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                        // the section name is unique to this module, so we can infer the correct filename for this form design file
                        $goodFiles[$badFile] = $correctFileName . $badFile;

                        break 2;
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        // last chance - sections in 'ExtraFields'
        if (is_array($GLOBALS['ExtraFields'])) {
            foreach ($GLOBALS['ExtraFields'] as $section) {
                foreach ($modules as $module => $correctFileName) {
                    if (in_array($section, array_diff($moduleSections[$module], $moduleSections[$module == 'LIB' ? 'AST' : 'LIB']))) {
                        // the section name is unique to this module, so we can infer the correct filename for this form design file
                        $goodFiles[$badFile] = $correctFileName . $badFile;

                        break 2;
                    }
                }
            }
        }

        if (isset($goodFiles[$badFile])) {
            // we've identified the correct file name, move onto the next bad file
            unsetFormDesignSettings();

            continue;
        }

        unsetFormDesignSettings();
    }

    // rename files that we've identified
    foreach ($badFiles as $key => $badFile) {
        if (isset($goodFiles[$badFile])) {
            rename(DatixConfig::CLIENT_FOLDER . '/' . $badFile, DatixConfig::CLIENT_FOLDER . '/' . $goodFiles[$badFile]);
            unset($badFiles[$key]);
        }
    }

    // report files that couldn't be automatically fixed
    $message = '';

    if (!empty($badFiles)) {
        foreach ($badFiles as $badFile) {
            $message .= DatixConfig::CLIENT_FOLDER . '/' . $badFile . '<br />';
        }
    }

    if ($message != '') {
        $message = 'An error has been detected with the following form design files:<br /><br />' . $message . '<br />Please contact Datix Support.';
        AddSessionMessage('ERROR', $message);
    }
}

/**
 * Detects security groups with blank where clauses.
 */
function getBlankWhereClauses()
{
    global $ModuleDefs;

    $output = \src\security\groups\model\Group::getGroupsWithBlankWhereClauses();

    if (empty($output)) {
        $message = 'The system has detected that there are no security groups with blank where clauses.';
    } else {
        $message = '<div style="margin-bottom:20px;">The following security groups have blank where clauses for the listed modules:</div>';

        foreach ($output as $id => $details) {
            $message .= '<div style="font-weight:bold;margin-bottom:10px;">' . $id . ': ' . CompatEscaper::encodeCharacters($details['name']) . '</div>';

            foreach ($details['modules'] as $module) {
                $message .= '<div style="margin-left:20px">' . CompatEscaper::encodeCharacters($ModuleDefs[$module]['NAME']) . '</div>';
            }

            $message .= '<div style="margin-top:20px"></div>';
        }
    }

    AddSessionMessage('INFO', $message);
}

function RemoveSectionReadOnly($aParams)
{
    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        if (isset($GLOBALS['ReadOnlyFields'][$aParams['section']]) || $GLOBALS['ReadOnlyFields'][$aParams['section']] === true) {
            unset($GLOBALS['ReadOnlyFields'][$aParams['section']]);
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}

// Removes the ability to show staff fields as checkboxes/radio buttons, because this list could be very large and
// filtering staff by location requires fields to be coded fields
function removeDisplayCheckboxForStaffFields($aParams)
{
    $staffFields = [
        'inc_mgr',
        'inc_head',
        'inc_investigator',
        'inc_consultants',
        'com_mgr',
        'com_head',
        'com_investigator',
        'cla_mgr',
        'cla_head',
        'cla_investigator',
    ];

    if (Container::get(FormDesignRepository::class)->formDesignExists($aParams['file'])) {
        Container::get(FormDesignLoader::class)->load($aParams['file']);

        foreach ($staffFields as $field) {
            if (isset($GLOBALS['DisplayAsCheckboxes'][$field]) || $GLOBALS['DisplayAsCheckboxes'][$field] === true) {
                unset($GLOBALS['DisplayAsCheckboxes'][$field]);
            }

            if (isset($GLOBALS['DisplayAsRadioButtons'][$field]) || $GLOBALS['DisplayAsRadioButtons'][$field] === true) {
                unset($GLOBALS['DisplayAsRadioButtons'][$field]);
            }
        }

        SaveFormDesignValuesToFile($aParams['file']);
        unsetFormDesignSettings();
    }
}
