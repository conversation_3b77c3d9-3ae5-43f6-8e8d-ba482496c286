<?php

use app\services\forms\PageTitleProvider;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\idGenerator\UpdateIdGenerator;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Source\libs\ReportsAdminOptionRetriever;
use Source\libs\ReportsAdminOptionRetrieverFactory;
use src\admin\services\CapturePermissionService;
use src\component\field\CustomFieldFactory;
use src\component\field\DivCheckboxFieldFactory;
use src\component\field\DivYesNoFieldFactory;
use src\component\field\FieldLabel;
use src\component\field\InputFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\field\TextAreaFieldFactory;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\framework\controller\AssetManager;
use src\framework\controller\Response;
use src\framework\registry\Registry;
use src\generic\services\QueryValidationService;
use src\incidents\helpers\IncidentsDataHelperFactory;
use src\reports\exceptions\ReportException;
use src\reports\model\report\Report;
use src\reports\model\report\ReportModelFactory;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\database\field\StringField;
use src\system\database\FieldInterface;
use src\system\database\fieldset\Fieldset;
use src\system\database\fieldset\FieldsetCollection;
use src\system\database\fieldset\FieldsetModelFactory;
use src\system\moduledefs\ModuleDefs;

/**
 * @return never
 *
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 */
function ReportsAdminAction(): void
{
    global $scripturl;

    $module = Sanitize::SanitizeString($_REQUEST['module'] ?? null);
    checkPermissionIncludingModular($module);

    $form_action = Sanitize::SanitizeString($_REQUEST['form_action']);
    $rep_id = Sanitize::SanitizeInt($_REQUEST['rep_id']);


    $showExcluded =
            $_REQUEST['show_excluded'] !== null ?
                Sanitize::SanitizeString($_REQUEST['show_excluded']) :
                $_SESSION['reports_administration:show_excluded'];

    // Default value is hide forms/fields
    if ($showExcluded === null) {
        $showExcluded = '0';
    }

    $_SESSION['reports_administration:show_excluded'] = $showExcluded;

    if ($rep_id && !$module) {
        $report = (new ReportModelFactory())->getMapper()->findByBaseListingReportID($rep_id);
        $module = $report->module;
    }

    switch ($form_action) {
        case 'edit':
            ReportsAdminEdit($rep_id, $module);

            break;
        case 'delete':
            ReportsAdminDelete();

            break;
        case 'new':
            ReportsAdminNew($module);

            break;
        case 'cancel':
        default:
            $location = "{$scripturl}?module=ADM";
            Container::get(Response::class)->redirect($location);

            break;
    }
}

/**
 * @param $rep_id
 * @param $module
 *
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 */
function ReportsAdminEdit($rep_id, $module): void
{
    checkPermission();
    ReportsAdminForm($rep_id, $module, 'edit');
}

/**
 * @param $module
 *
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 * @throws Exception
 */
function ReportsAdminNew($module): void
{
    checkPermission();
    ReportsAdminForm('', $module, 'new');
}

function getAdminFormButtons(int $reportId, string $scripturl, bool $lockedForm): ButtonGroup
{
    $buttonGroup = new ButtonGroup();

    $buttonGroup->AddButton([
        'id' => 'btnCancel',
        'name' => 'btnCancel',
        'label' => _fdtk('btn_cancel'),
        'onclick' => 'if(confirm(\'Are you sure you want to cancel?\')){javascript:document.forms[0].rbWhat.value=\'cancel\';document.frmReportDocument.submit()}',
        'action' => 'CANCEL',
        'class' => 'button-clear',
    ]);

    if ($reportId > 0) {
        $messageText = _fdtk('delete_base_report_confirm_msg');

        if (!$lockedForm) {
            $buttonGroup->AddButton([
                'id' => 'btnDelete',
                'name' => 'btnDelete',
                'label' => _fdtk('delete_base_report'),
                'onclick' => "deleteBaseReport('{$messageText}'); return false",
                'action' => 'DELETE',
                'class' => 'button-warning',
            ]);
        }

        $buttonGroup->AddButton([
            'id' => 'btnExport',
            'name' => 'btnExport',
            'label' => _fdtk('export_report_design'),
            'onclick' => 'SendTo(\'' . $scripturl . '?action=exportabasereport&rep_id=' . urlencode($reportId) . '\');',
            'action' => 'BACK',
            'class' => 'button-secondary',
        ]);
    }

    if (!$lockedForm) {
        $buttonGroup->AddButton([
            'id' => 'btnSave',
            'name' => 'btnSave',
            'label' => _fdtk('btn_save'),
            'onclick' => 'selectAllMultiCodes();document.forms[0].rbWhat.value=\'save\';document.frmReportDocument.submit()',
            'action' => 'SAVE',
            'class' => 'button-primary',
        ]);
    }

    return $buttonGroup;
}

/**
 * @throws Exception
 */
function getReport(int $repId): array
{
    $sql = 'SELECT wr.title AS rep_name,
           wr.merge_repeated_values,
           re.recordid,
           re.rep_format,
           re.rep_module,
           re.rep_type,
           re.rep_file,
           re.updateid,
           re.rep_fields,
           re.rep_subtype,
           wr.import_id
        FROM web_reports AS wr
        JOIN reports_extra AS re
        ON wr.base_listing_report = re.recordid
        WHERE re.recordid = :repId
    ';

    $rep = \DatixDBQuery::PDO_fetch($sql, ['repId' => $repId]);

    if (empty($rep)) {
        throw new Exception("Report number {$repId} does not exist");
    }

    return $rep;
}

function getFormArray(): array
{
    return [
        'Parameters' => [
            'Panels' => 'True',
            'Condition' => false,
        ],
        'name' => [
            'Title' => _fdtk('listing_report_settings_title'),
            'MenuTitle' => 'Reports admin',
            'NewPanel' => true,
            'Function' => 'makeListingReportEditPage',
            'Rows' => [],
        ],
        'view' => [
            'Title' => _fdtk('listing_report_view_settings_title'),
            'Function' => 'MakeReportsViewAdminEditPanel',
            'Rows' => [],
        ],
        'additional' => [
            'Title' => 'Additional options',
            'Function' => 'MakeAdditionalOptionsPanel',
            'Rows' => [],
        ],
        'packages' => [
            'Function' => 'MakePackagedReportsList',
            'NotModes' => ['new'],
            'Rows' => [],
        ],
    ];
}

/**
 * @param $repId
 * @param $module
 * @param $form_action
 * @param string $error
 * @param array $rep
 *
 * @return never
 *
 * @throws Exception
 */
function ReportsAdminForm($repId, $module, $form_action, $error = '', $rep = []): void
{
    global $scripturl;

    LoggedIn();

    $assetManager = new AssetManager();
    $assetManager->sendVariablesToJs(['AlertAfterChange' => true], false);

    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            $module,
            _fdtk('reports_administration_title'),
        );

    if ($repId > 0) {
        $rep = getReport($repId);
    }
    $lockedForm = false;
    if ($rep['import_id']) {
        $lockedForm = true;
    }

    $buttonGroup = getAdminFormButtons((int) $repId, $scripturl, $lockedForm);

    GetSideMenuHTML(['module' => 'ADM', 'buttons' => $buttonGroup, 'action' => 'reportsadminaction']);

    template_header(null, null, $assetManager);


    if ($error) {
        $rep['error'] = true;
        $rep['rep_type'] = $_POST['rep_type'];
        $rep['rep_type_code'] = $_POST['rep_type_code'];
        $rep['rep_name'] = $_POST['rep_name'];
    }

    $formArray = getFormArray();

    echo '
        <form enctype="multipart/form-data" method="post" name="frmReportDocument" id="frmReportDocument" id="frmReportDocument" action="'
          . $scripturl . '?action=reportsadmineditsave">
            <input type="hidden" id="form_action" name="form_action" value="' . Escape::EscapeEntities($form_action) . '" />
            <input type="hidden" id="rep_type_code" name="rep_type_code" value="R" />';


    $repTable = FormTableFactory::create($form_action);
    $repTable->makeForm($formArray, $rep, $module);

    $repTable->makeTable();
    echo $repTable->getFormTable();

    echo $buttonGroup->getHTML();

    echo '</form>';

    if (!$_GET['print']) {
        echo JavascriptPanelSelect(false, Sanitize::SanitizeString($_GET['panel']), $repTable);
    }

    footer(); ?>

<script type="text/javascript">

function moveSelectedItems($from, $to, sortToList) {
    $from.find('option:selected').appendTo($to).attr('selected', false);
    if(sortToList === true) {
        sortListBox($to.get(0));
    }
    // Set that a change has occured
    GlobalChangeTracker = true;
}

function deleteBaseReport($messageText)
{
    if(confirm($messageText))
	{
		jQuery('#rbWhat').val('delete');
        jQuery('#frmReportDocument').submit();
	}
}

</script>

<?php
    obExit();
}

/**
 * @return never
 */
function ReportsAdminSaveAction(): void
{
    checkPermission();

    global $scripturl;

    $module = $_POST['module'];

    if ($_POST['rbWhat'] == 'cancel') {
        //do nothing
    } elseif ($_POST['rbWhat'] == 'delete') {
        ReportsAdminDelete();
    } elseif ($_POST['rbWhat'] == 'deletepackage') {
        ReportsAdminDeletePackaged();
    } else {
        ReportsAdminSave();
    }

    $location = "{$scripturl}?action=listlistingreports&module={$module}";
    Container::get(Response::class)->redirect($location);
}

/**
 * @return never
 */
function ReportsAdminSave(): void
{
    checkPermission();

    global $scripturl;

    $rep_id = Sanitize::SanitizeInt($_POST['rep_id']);
    $module = Sanitize::SanitizeString($_POST['module']);
    $rep_type = Sanitize::SanitizeString($_POST['rep_type']);
    $rep_type_code = Sanitize::SanitizeString($_POST['rep_type_code']);
    $rep_table = Sanitize::SanitizeString($_POST['rep_table']);
    $form_action = Sanitize::SanitizeString($_POST['form_action']);
    $rep = QuotePostArray(Sanitize::SanitizeStringArray($_POST));
    $CustomRepUploadFileName = '';

    $ErrorMark = '<span style="color: red; font-size: 10px;">*</span>';

    if ($_POST['rep_name'] == '') {
        AddSessionMessage('ERROR', 'You must enter a report name.');
        $error = true;
    }

    if ($_POST['rep_type_code'] == '') {
        AddSessionMessage('ERROR', 'You must select the report type.');
        $error = true;
    }

    if ($_FILES['userfile']['name'] == '' && $_POST['rep_type'] == 'CW' && !$rep_id) {
        AddSessionMessage('ERROR', 'You must select a file to upload.');
        $error = true;
    }

    if ($_FILES['userfile']['name'] != '' && $_POST['rep_type'] == 'CW' && !$rep_id) {
        $ext = \UnicodeString::strtolower(\UnicodeString::strrchr($_FILES['userfile']['name'], '.'));
        if ($ext != '.rpt') {
            AddSessionMessage('ERROR', 'The file must be a Crystal report template (.rpt).');
            $error = true;
        }
    }

    if ($_POST['limittotop'] == 'Y' && !is_numeric($_POST['limittotopnum'])) {
        AddSessionMessage('ERROR', 'Limit to top must be a numeric value');
        $error = true;
    }

    if ($_POST['rows'] == '' && $_POST['rep_type'] && $_POST['rep_type'] != 'CW' && $_POST['rep_type'] != 'R' && $_POST['rep_type'] != 'GW_GAUGE') {
        AddSessionMessage('ERROR', 'You must select rows for the report ');
        $error = true;
    }

    if ($_POST['listing_fields_list'] == '' && $_POST['rep_type'] == 'R') {
        AddSessionMessage('ERROR', 'You must select fields for the report ');
        $error = true;
    }

    if ($_POST['cla_dclaim_checkbox'] == 'on' && $_POST['cla_dclaim_group_by_options'] == '') {
        AddSessionMessage('ERROR', 'You must select a date option if you wish to group by "Claim date" ');
        $error = true;
    }

    if ($_POST['pol_start_date_checkbox'] == 'on' && $_POST['pol_start_date_group_by_options'] == '') {
        AddSessionMessage('ERROR', 'You must select a date option if you wish to group by "Coverage start date"');
        $error = true;
    }

    if ($error) {
        ReportsAdminForm($rep_id, $module, $form_action, $error, $rep);
        obExit();
    }

    if ($rep_id == '') {
        $recordIdGenerator = (new RecordIdGeneratorFactory())->create('reports_extra');
        $rep_id = $recordIdGenerator->generateRecordId();
        $recordIdGenerator->createBlankRecordWithoutApprovalStatus($rep_id);
        $NewRecord = true;
    }

    if ($_FILES['userfile']['name'] != '') {
        $CustomRepUploadFileName = 'CUSTOMREP' . $rep_id . '.rpt';
        GetParms($_SESSION['user']);
        $ToFile = Sanitize::SanitizeFilePath(GetParm('CUSTOM_REP_PATH', '', true) . '\\' . $CustomRepUploadFileName);

        if ($_FILES['userfile']['size'] == '0' || !@move_uploaded_file($_FILES['userfile']['tmp_name'], $ToFile)) {
            fatal_error('Cannot copy file to server.  Please contact your IT department.');
        }
    }

    $rep['merge_repeated_values'] = $rep['merge_repeated_values'] == 'Y' ? 1 : 0;

    // Update the web_reports table title so that titles work consistently with graphical reports
    $sql = "UPDATE web_reports
        SET
        title = N'{$rep['rep_name']}',
        merge_repeated_values = :merge_repeated_values
        WHERE base_listing_report = :rep_id
        ";

    DatixDBQuery::PDO_query($sql, ['merge_repeated_values' => $rep['merge_repeated_values'], 'rep_id' => $rep_id]);

    $sql = "UPDATE reports_extra
        SET
        rep_type = '{$rep_type}',
        rep_name = N'{$rep['rep_name']}',
        rep_module = '{$module}',
        rep_dupdated = '" . date('Y-m-d H:i:s') . "',
        rep_updatedby = '{$_SESSION['initials']}',
        ";

    if ($rep_type_code == 'GW_SPC') {
        $sql .= "rep_subtype = '{$rep['rep_subtype']}', ";
    }

    if ($rep_type == 'SC' || $rep_type == 'SS') {
        $sql .= 'rep_option = 255, ';
    }

    if ($NewRecord) {
        $sql .= "rep_createdby = '{$_SESSION['initials']}',
                 rep_dcreated = '" . date('Y-m-d H:i:s') . "',
                 rep_private = 'N',
                 rep_app_show = 'WEB',
                 rep_format = 'L|Arial,10,Y|Arial,10,Y|Arial,10,Y|2.54,2.54,2.54,2.54|50.00|0.00|0.00', ";
    }

    // //////////////////////////////////////////////////
    // set rep_fields
    // //////////////////////////////////////////////////

    if ($rep['rows'] != '' || $rep['columns'] != '') {
        if (!$NewRecord) {
            $sql_rep_fields = "SELECT rep_fields
                FROM reports_extra
                WHERE recordid = {$rep_id}";
            $result_rep_fields = db_query($sql_rep_fields);
            $row = db_fetch_array($result_rep_fields);
            $ReportFields = explode(',', $row['rep_fields']);
            $RowParts = explode(';', $ReportFields[0]);
            $ColumnParts = explode(';', $ReportFields[1]);
            // $rep_fields_parts[2] is the group by information
        }

        if ($rep['rows'] != '') {
            $RowParts[0] = $rep['rows'];
            $RowParts[1] = $rep_table;
            $ReportFields[0] = implode(';', $RowParts);
        }

        if ($rep['columns'] != '') {
            $ColumnParts[0] = $rep['columns'];
            $ColumnParts[1] = $rep_table;
            $ReportFields[1] = implode(';', $ColumnParts);
        }

        $rep_fields = implode(',', $ReportFields);

        $sql .= "rep_fields = '{$rep_fields}', ";
    }

    if ($CustomRepUploadFileName) {
        $sql .= "rep_file = '" . $CustomRepUploadFileName . "', ";
    }

    $sql .= ' updateid = :updateid_new
        WHERE recordid = :rep_id
        AND (updateid = :updateid OR updateid IS NULL)';

    $updateId = (new UpdateIdGenerator())->generateUpdateId(Sanitize::SanitizeString($_POST['updateid']));

    if (!DatixDBQuery::PDO_query($sql, [
        'updateid_new' => $updateId,
        'rep_id' => $rep_id,
        'updateid' => $_POST['updateid'], ])
    ) {
        fatal_error('Could not save report' . $sql);
    } else {
        $message = 'Report saved.';
    }

    // TODO use the model to persist listing reports
    if ($rep_type_code == 'R') { // save listing report fields
        $fieldDefs = Container::get(Registry::class)->getFieldDefs();

        $sql = 'DELETE FROM reports_formats WHERE rpf_rep_id = :rep_id';
        DatixDBQuery::PDO_query($sql, ['rep_id' => $rep_id]);

        if (is_array($_POST['listing_fields_list'])) {
            foreach ($_POST['listing_fields_list'] as $Order => $Field) {
                $condition = null;
                [$fieldset, $table, $field] = explode('.', $Field);

                // Complex data types such as JSON data may have additional specifiers attached to the fieldname
                // The specifier is delimited by a |
                // If the field has an additional specifier, strip off the specifier for field handling purposes
                if (strpos($field, '|')) {
                    $explodeArray = explode('|', $field);
                    $field = $explodeArray[0];
                    $condition = $explodeArray[1];
                    $Field = explode('|', $Field)[0];
                }

                // extra fields are stored in the format U0_<id>
                $field = str_replace('UDF_', 'U0_', $field);

                $type = '';

                $fieldObj = $fieldDefs[$Field];

                if ($fieldObj !== null) {
                    $type = $fieldObj->getType();
                }

                switch ($type) {
                    case FieldInterface::CODE:
                        $Width = 2;
                        $Description = 'Y';

                        break;
                    case FieldInterface::MULTICODE:
                        $Width = 4;
                        $Description = 'Y';

                        break;
                    case FieldInterface::YESNO:
                        $Width = 1.5;
                        $Description = 'Y';

                        break;
                    case FieldInterface::TEXT:
                        $Width = 7;
                        $Description = 'N';

                        break;
                    case FieldInterface::DATE:
                        $Width = 1.5;
                        $Description = 'N';

                        break;
                    default:
                        $Width = 2;
                        $Description = 'N';

                        break;
                }

                DatixDBQuery::PDO_build_and_insert('reports_formats', [
                    'rpf_rep_id' => $rep_id,
                    'rpf_fieldset' => $fieldset,
                    'rpf_table' => $table,
                    'rpf_field' => $field,
                    'rpf_order' => $Order,
                    'rpf_section' => 'DETAIL',
                    'rpf_descr' => $Description,
                    'rpf_login' => $_SESSION['login'],
                    'rpf_total' => 'N',
                    'rpf_width' => $Width,
                    'rpf_conditions' => $condition,
                ]);
            }

            if (is_array($_POST['listing_orders_list'])) {
                foreach ($_POST['listing_orders_list'] as $Order => $Field) {
                    [$fieldset, $table, $field] = explode('.', $Field);

                    DatixDBQuery::PDO_build_and_insert('reports_formats', [
                        'rpf_rep_id' => $rep_id,
                        'rpf_fieldset' => $fieldset,
                        'rpf_table' => $table,
                        'rpf_field' => $field,
                        'rpf_order' => $Order,
                        'rpf_section' => 'SORT',
                        'rpf_descr' => 'N',
                        'rpf_login' => $_SESSION['login'],
                        'rpf_total' => 'N',
                        'rpf_width' => 1,
                    ]);
                }
            }

            if (is_array($_POST['listing_groups_list'])) {
                foreach ($_POST['listing_groups_list'] as $Order => $Field) {
                    [$fieldset, $table, $field] = explode('.', $Field);

                    DatixDBQuery::PDO_build_and_insert('reports_formats', [
                        'rpf_rep_id' => $rep_id,
                        'rpf_fieldset' => $fieldset,
                        'rpf_table' => $table,
                        'rpf_field' => $field,
                        'rpf_order' => $Order,
                        'rpf_section' => 'GROUP',
                        'rpf_descr' => 'N',
                        'rpf_login' => $_SESSION['login'],
                        'rpf_total' => 'N',
                        'rpf_width' => 1,
                    ]);
                }
            }
        }

        // Save group by additional options for claims
        if ($module == 'CLA') {
            $noGroupBys = count($_POST['listing_groups_list']);

            if ($_POST['cla_dclaim_checkbox'] == 'on') {
                DatixDBQuery::PDO_build_and_insert(
                    'reports_formats',
                    [
                        'rpf_rep_id' => $rep_id,
                        'rpf_fieldset' => '0',
                        'rpf_table' => 'claims_main',
                        'rpf_field' => 'cla_dclaim',
                        'rpf_order' => $noGroupBys++,
                        'rpf_section' => 'GROUP',
                        'rpf_descr' => 'N',
                        'rpf_login' => $_SESSION['login'],
                        'rpf_total' => 'N',
                        'rpf_width' => 1,
                        'rpf_date_format' => $_POST['cla_dclaim_group_by_options'],
                    ],
                );
            }

            if ($_POST['pol_start_date_checkbox'] == 'on') {
                DatixDBQuery::PDO_build_and_insert(
                    'reports_formats',
                    [
                        'rpf_rep_id' => $rep_id,
                        'rpf_fieldset' => '57',
                        'rpf_table' => 'policies',
                        'rpf_field' => 'start_date',
                        'rpf_order' => $noGroupBys++,
                        'rpf_section' => 'GROUP',
                        'rpf_descr' => 'N',
                        'rpf_login' => $_SESSION['login'],
                        'rpf_total' => 'N',
                        'rpf_width' => 1,
                        'rpf_date_format' => $_POST['pol_start_date_group_by_options'],
                    ],
                );
            }
        }
    }

    // need to check that there is a linked web_reports record
    $web_report_id = DatixDBQuery::PDO_fetch('SELECT recordid FROM web_reports WHERE base_listing_report = :base_listing_report', ['base_listing_report' => $rep_id], \PDO::FETCH_COLUMN);

    if (!$web_report_id) {
        $report = (new ReportModelFactory())->getEntityFactory()->createObject(
            [
                'title' => $_POST['rep_name'],
                'base_listing_report' => $rep_id,
                'module' => $module,
                'type' => Report::LISTING,
                'merge_repeated_values' => $rep['merge_repeated_values'],
            ],
        );

        (new ReportModelFactory())->getMapper()->save($report);

        $web_report_id = $report->recordid;
    }

    $location = "{$scripturl}?action=reportsadminaction&rep_id={$rep_id}&module={$module}&form_action=edit";

    if ($message) {
        $location .= "&message={$message}";
    }

    AddSessionMessage('INFO', 'Report saved.');
    Container::get(Response::class)->redirect($location);
}

/**
 * @return never
 */
function ReportsAdminDelete(): void
{
    global $scripturl;

    checkPermission();

    $rep_id = Sanitize::SanitizeInt($_POST['rep_id']);
    $module = Sanitize::SanitizeString($_POST['module']);

    db_query("DELETE FROM report_parameters WHERE rpar_report_id = {$rep_id}");
    $sql = "DELETE FROM reports_extra WHERE recordid = {$rep_id}";

    if (!db_query($sql)) {
        fatal_error('Could not delete report');
    } else {
        // The listing report should be linked to a web_report record, so we need to clean that record up here too.
        $web_report = (new ReportModelFactory())->getMapper()->findByBaseListingReportID($rep_id);

        if ($web_report instanceof Report) {
            (new ReportModelFactory())->getMapper()->delete($web_report);
        }

        AddSessionMessage('INFO', 'Report has been deleted.');
    }

    $location = "{$scripturl}?action=listlistingreports&module={$module}";
    Container::get(Response::class)->redirect($location);
}

/**
 * @param string $mode
 */
function outputFieldSets(
    FieldsetCollection $fieldsets,
    bool $showExcluded,
    array $currentListingFields,
    string $mainTable,
    string $name,
    ReportsAdminOptionRetriever $reportsAdminOptionRetriever,
    $mode,
    Registry $registry
): void {
    $disabled = '';
    $disabledStyle = '';
    if ($mode === FormTable::MODE_DISABLED) {
        $disabled = 'disabled="disabled';
        $disabledStyle = 'background-color: #e4e4e4;opacity: 1;';
    }
    foreach ($fieldsets as $fieldset) {
        $id = $fieldset->recordid;

        echo '
        <div id="' . $id . '_' . $name . 's_row" class="subform_' . $name . '_row" ' . ($id !== '0' ? 'style="display:none"' : '') . '>
        <select id="' . $id . '_' . $name . 's_list" ' . $disabled . ' multiple="multiple" size="12" style="resize:both; width: 300px;' . $disabledStyle . '">';

        outputFields($fieldset, $currentListingFields, $name, $mainTable, $id, $showExcluded, $reportsAdminOptionRetriever, $registry);
    }
}

function outputFields(
    Fieldset $fieldSet,
    array $currentListingFields,
    string $name,
    string $mainTable,
    string $id,
    bool $showExcluded,
    ReportsAdminOptionRetriever $reportsAdminOptionRetriever,
    Registry $registry
): void {
    if ($id != Report::FIELDSET_PSIMS) {
        $fields = $showExcluded ? $fieldSet->getAllFields() : $fieldSet->getFields();

        foreach ($fields as $field) {
            if ($reportsAdminOptionRetriever->isOptionExcluded($field)) {
                continue;
            }

            $fqn = $field->getFieldset() . '.' . $field->getTable() . '.' . $field->getName();

            if ($field instanceof StringField && $field->getCondition()) {
                $fqn = $fqn . '|' . $field->getCondition();
            }

            $notCurrentField = !array_key_exists($fqn, $currentListingFields);

            $escapedFqn = Escaper::escapeForHTMLParameter($fqn);

            $isAValidGroupField = $name === 'group'
                && in_array($field->getType(), [FieldInterface::CODE, FieldInterface::MULTICODE, FieldInterface::DATE])
                && !$reportsAdminOptionRetriever->isTreeTagFieldWithName($field);

            $isAValidOrderField = $name === 'order'
                && in_array($field->getType(), [FieldInterface::CODE, FieldInterface::MULTICODE, FieldInterface::DATE, FieldInterface::NUMBER])
                && !$reportsAdminOptionRetriever->isTreeTagFieldWithName($field);

            if ($notCurrentField
                && ($name == 'field' || $isAValidGroupField || $isAValidOrderField)) {
                echo '<option value="' . $escapedFqn . '" source="' . $id;

                $optionLabel = Escaper::escapeForHTML($reportsAdminOptionRetriever->getOptionLabel($mainTable, $field));
                $escapedTooltip = str_replace('"', '&quot; ', $optionLabel);
                echo '" title="' . $escapedTooltip . '">' . $optionLabel . '</option>';
            }
        }
    }

    // Add PSIMS_* fields for Incidents and People Affected already added above
    // Or add PSIMS fields (not already added to avoid duplication)
    if (in_array($id, [Report::FIELDSET_PSIMS, Report::FIELDSET_PEOPLE_AFFECTED])) {
        $isTranslate = true;
        $isbuildQuery = false;
        $includePsirfFields = $registry->getParm('LFPSE_PSIRF', 'N')->isTrue();
        $psimsFields = getPSIMSFields($isTranslate, $isbuildQuery, (int) $id, $includePsirfFields);
        foreach ($psimsFields as $key => $val) {
            $psimsField = $id . '.' . $key;
            if (!isset($currentListingFields[$psimsField])) {
                echo '<option value="' . $psimsField . '" source="' . $id;


                $val = $val . ' (' . _fdtk('inc_psims_title') . ')';
                $escapedTooltip = str_replace('"', '&quot; ', $val);
                echo '" title="' . $escapedTooltip . '">' . $val . '</option>';
            }
        }
    }

    echo '</select></div>';
}

function makeReportTemplateFile(array $rep, string $formType): void
{
    if ($rep['rep_file'] != '') {
        $formTemplateField = CustomFieldFactory::create($formType, $rep['rep_file']);
        echo GetDivFieldHTML('Template file:', $formTemplateField->GetField(), '', '', 'rep_file_row');

        $formNewTemplateField = CustomFieldFactory::create($formType, '<input name="userfile" type="file"  size="50"/>');
        echo GetDivFieldHTML('Import new template file (.rpt):', $formNewTemplateField->GetField(), '', '', 'userfile_row');
    } else {
        $formTemplateImportField = CustomFieldFactory::create($formType, '<input name="userfile" type="file"  size="50"/>');
        echo GetDivFieldHTML('Template file to import (.rpt):', $formTemplateImportField->GetField(), '', '', 'userfile_row', true);
    }
}

function makeListingReportEditPage(array $rep, string $formType, string $module)
{
    checkPermission();

    $moduleDefs = Container::get(ModuleDefs::class);

    $showExcluded = $_SESSION['reports_administration:show_excluded'] ?? '0';

    echo '
    <input type="hidden" id="rep_id" name="rep_id" value="' . Sanitize::SanitizeInt($rep['recordid']) . '" />
    <input type="hidden" id="module" name="module" value="' . Escape::EscapeEntities($module) . '" />
    <input type="hidden" id="rep_type" name="rep_type" value="R" />
    <input type="hidden" id="updateid" name="updateid" value="' . Escape::EscapeEntities($rep['updateid']) . '" />
    <input type="hidden" id="rbWhat" name="rbWhat" value="Save" />';

    makeReportTemplateFile($rep, $formType);

    $mainTable = $moduleDefs[$module]->getDbReadObj();
    $fieldsets = (new FieldsetModelFactory())->getMapper()->getFieldsetsForModule($moduleDefs[$module], false);

    $fieldsetIds = getRecordIds($fieldsets);

    $currentListingFields = [];
    $mode = FormTable::MODE_NEW;
    if ($rep['recordid']) {
        $report = (new ReportModelFactory())->getMapper()->findByBaseListingReportID($rep['recordid']);
        $mode = $report->import_id ? FormTable::MODE_DISABLED : FormTable::MODE_NEW;
        if ($report === null) {
            throw new ReportException('Unable to find the listing report with ID: ' . $rep['recordid']);
        }
        $isTranslate = true;
        $isbuildQuery = false;
        $psimsFields = getPSIMSFields($isTranslate, $isbuildQuery);

        foreach ($report->getColumns() as $column) {
            $columnLabel = $column->getTitle();
            $fqn = $column->getFQN();

            if ($columnLabel instanceof FieldLabel) {
                $columnLabel = $columnLabel->withFieldsetSuffix();
            }

            // set label for psism_ fields
            $columnLabel = getPSIMSFieldLabel($fqn, $columnLabel, $psimsFields);

            // Skip any currentListingFields that are not in a recognised fieldset.
            $columnFieldset = $column->getFieldset();
            if ($columnFieldset && empty($fieldsetIds[$columnFieldset])) {
                continue;
            }

            if ($column->getConditions()) {
                $fqn = $fqn . '|' . $column->getConditions();
            }
            $currentListingFields[$fqn] = $columnLabel;
        }
    } else {
        if (is_array($rep['listing_fields_list'])) {
            // We arrive here if we're attempting to save a new report design but there are form validation issues
            $fieldDefs = Container::get(Registry::class)->getFieldDefs();

            foreach ($rep['listing_fields_list'] as $field) {
                $fieldDef = $fieldDefs[$field];

                if ($fieldDef !== null) {
                    $reportsAdminOptionRetriever = (new ReportsAdminOptionRetrieverFactory())->create();
                    $currentListingFields[$field] = $reportsAdminOptionRetriever->getOptionLabel($mainTable, $fieldDef);
                }
            }
        }
    }
    $disabled = '';
    $disabledStyle = '';
    if ($mode === FormTable::MODE_DISABLED) {
        $disabled = 'disabled="disabled"';
        $disabledStyle = 'background-color: #e4e4e4;opacity: 1;';
    }

    $repNameField = InputFieldFactory::create($mode, 'rep_name', 70, 256, $rep['rep_name'], '');
    echo GetDivFieldHTML('Name:', $repNameField->GetField());

    $reportsAdminOptionRetriever = (new ReportsAdminOptionRetrieverFactory())->create();

    $field = SelectFieldFactory::createSelectField('subforms', $module, 0, $mode);
    $field->setCustomCodes($fieldsets->getLabels());
    $field->setOnChangeExtra('jQuery(\'.subform_field_row\').hide();jQuery(\'#\'+jQuery(\'#subforms\').val()+\'_fields_row\').show()');

    echo '
    <li id="listing_field_select_row">
    <table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
    ';

    echo '
    <tr>
    <td class="windowbg2" align="left" colspan="3">
    Select fields to use on the report:
    </td>
    </tr>';

    echo '
    <tr>
    <td class="windowbg2" align="left">
    <div><div style="float:left; padding-top:3px;">Form:&nbsp;</div><div style="float:left; padding-top:3px;"> ' . $field->getField() . '</div></div>
    </td>
    <td class="windowbg2" align="left">
    </td>
    <td class="windowbg2" align="left">
    Report fields:
    </td>
    </tr>
    <tr>
        <td class="windowbg2" align="center">';

    $registry = Container::get(Registry::class);
    outputFieldSets($fieldsets, $showExcluded, $currentListingFields, $mainTable, 'field', $reportsAdminOptionRetriever, $mode, $registry);

    echo '
        </td>
        <td class="windowbg2" align="center">';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
            onclick="moveSelectedItems($(\'[id$=_fields_list]:visible\'), $(\'#listing_fields_list\'), false);" >
            <span>' . _fdtk('btn_add') . ' >>></span>
        </button><br /><br />
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
            onclick="var source_list_name = jQuery(\'#listing_fields_list option:selected\').attr(\'source\')+\'_fields_list\'; moveSelectedItems($(\'#listing_fields_list\'), $(\'#\' + source_list_name), true);" >
            <span><<< ' . _fdtk('btn_remove') . '</span>
        </button>';
    }

    echo '
        </td>
        <td class="windowbg2" align="center">
        <table>
        <tr>
        <td>';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <img src="images/up_windowbg2.gif" border="0" onclick="moveUp(\'listing_fields_list\');"/> <br />
        <img src="images/collapse_windowbg2.gif" border="0" onclick="moveDown(\'listing_fields_list\');"/>';
    }

    echo '
        </td>
        <td>
        <select id="listing_fields_list" name="listing_fields_list[]" ' . $disabled . ' multiple="multiple" size="12" style="resize:both; width: 300px;' . $disabledStyle . '">';
    foreach ($currentListingFields as $field => $label) {
        echo '<option value="' . Escaper::escapeForHTMLParameter($field) . '" source="' . getFieldsetId($field) . '">' . Escaper::escapeForHTML($label) . '</option>';
    }
    echo '
        </select>
        </td>
        </tr>
        </table>
        </td>
    </tr>';

    echo '
    </table>
    </li>';
}

function MakeReportsViewAdminEditPanel($rep)
{
    checkPermission();

    $fieldMode = FormTable::MODE_NEW;
    // default merge repeated values to on for new listing report designs
    if (empty($rep)) {
        $rep['merge_repeated_values'] = true;
    } elseif ($rep['import_id']) {
        $fieldMode = FormTable::MODE_DISABLED;
    }

    $boolCheckValue = filter_var($rep['merge_repeated_values'], FILTER_VALIDATE_BOOLEAN);

    echo GetDivFieldHTML(_fdtk('merge_repeated_values'), DivCheckboxFieldFactory::create($fieldMode, 'merge_repeated_values', '', $boolCheckValue, 'merge_repeated_values', true)->GetField());
}

function MakeAdditionalOptionsPanel($rep)
{
    $moduleDefs = Container::get(ModuleDefs::class);

    $module = Sanitize::getModule($_REQUEST['module']);
    $mainTable = $moduleDefs[$module]->getDbReadObj();

    $showExcluded = $_SESSION['reports_administration:show_excluded'] ?? '0';

    $fieldLabels = Container::get(Registry::class)->getFieldLabels();

    $fieldsets = (new FieldsetModelFactory())->getMapper()->getFieldsetsForModule($moduleDefs[$module], true);

    $currentListingGroupFields = [];

    $fieldsetIds = getRecordIds($fieldsets);
    $mode = FormTable::MODE_NEW;
    if ($rep['recordid']) {
        $report = (new ReportModelFactory())->getMapper()->findByBaseListingReportID($rep['recordid']);
        $module = $module ?: $report->module;
        $mode = $report->import_id ? FormTable::MODE_DISABLED : FormTable::MODE_NEW;
        $isTranslate = true;
        $isbuildQuery = false;
        $psimsFields = getPSIMSFields($isTranslate, $isbuildQuery);
        foreach ($report->getGroupBys() as $groupBy) {
            // Skip any currentListingFields that are not in a recognised fieldset.
            $columnFieldset = $groupBy->getFieldset();
            if ($columnFieldset && empty($fieldsetIds[$columnFieldset])) {
                continue;
            }
            $groupByFqn = $groupBy->getFQN();
            $groupByTitle = $groupBy->getTitle();

            // set label for psism_ fields
            $groupByTitle = getPSIMSFieldLabel($groupByFqn, $groupByTitle, $psimsFields);

            $currentListingGroupFields[$groupByFqn] = $groupByTitle;
        }
    }

    $disabled = '';
    $disabledStyle = '';
    if ($mode === FormTable::MODE_DISABLED) {
        $disabled = 'disabled="disabled"';
        $disabledStyle = 'background-color: #e4e4e4;opacity: 1;';
    }

    if (!$rep['recordid']) {
        $rep['merge_repeated_values'] = true;
    }

    $field = SelectFieldFactory::createSelectField('groups_forms', $module, 0, $mode);
    $field->setCustomCodes($fieldsets->getLabels());
    $field->setOnChangeExtra('jQuery(\'.subform_group_row\').hide();jQuery(\'#\'+jQuery(\'#groups_forms\').val()+\'_groups_row\').show()');

    echo '
    <li id="listing_group_select_row">
    <table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
    ';

    echo '
    <tr>
    <td class="windowbg2" align="left" colspan="3">
    Select fields to group by:
    </td>
    </tr>';

    echo '
    <tr>
    <td class="windowbg2" align="left">
    <div><div style="float:left; padding-top:3px;">Form:&nbsp;</div><div style="float:left; padding-top:3px;"> ' . $field->getField() . '</div></div>
    </td>
    <td class="windowbg2" align="left">
    </td>
    <td class="windowbg2" align="left">
    Current group columns:
    </td>
    </tr>
    <tr>
        <td class="windowbg2" align="center">
        ';

    $reportsAdminOptionRetriever = (new ReportsAdminOptionRetrieverFactory())->create();
    $registry = Container::get(Registry::class);
    outputFieldSets($fieldsets, $showExcluded, $currentListingGroupFields, $mainTable, 'group', $reportsAdminOptionRetriever, $mode, $registry);

    echo '
        </td>
        <td class="windowbg2" align="center">';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
            onclick="moveSelectedItems($(\'[id$=_groups_list]:visible\'), $(\'#listing_groups_list\'), false);" >
            <span>' . _fdtk('btn_add') . ' >>></span>
        </button>
        <br /><br />
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
           onclick="var source_list_name = jQuery(\'#listing_groups_list option:selected\').attr(\'source\')+\'_groups_list\'; moveSelectedItems($(\'#listing_groups_list\'), $(\'#\' + source_list_name), true);" >
           <span><<< ' . _fdtk('btn_remove') . '</span>
        </button>';
    }
    echo '
        </td>
        <td class="windowbg2" align="center">
        <table>
        <tr>
        <td>';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <img src="images/up_windowbg2.gif" border="0" onclick="moveUp(\'listing_groups_list\');"/> <br />
        <img src="images/collapse_windowbg2.gif" border="0" onclick="moveDown(\'listing_groups_list\');"/>';
    }
    echo '
        </td>
        <td>
        <select id="listing_groups_list" ' . $disabled . ' name="listing_groups_list[]" multiple="multiple" size="12" style="resize:both; width: 300px;' . $disabledStyle . '">';

    foreach ($currentListingGroupFields as $field => $label) {
        if (!in_array($field, ['0.claims_main.cla_dclaim', '57.policies.start_date'])) {
            echo '<option value="' . Escaper::escapeForHTMLParameter($field) . '" source="' . getFieldsetId($field) . '">' . Escaper::escapeForHTML($label) . '</option>';
        }
    }

    echo '
        </select>
        </td>
        </tr>
        </table>
        </td>
    </tr>';

    echo '
    </table>
    </li>';

    if ($module == 'CLA') {
        $claimDateCheckbox = '';
        $policyDateCheckbox = '';
        $claimDateFormat = '';
        $policyDateFormat = '';

        if (array_key_exists('0.claims_main.cla_dclaim', $currentListingGroupFields)) {
            $claimDateCheckbox = 'checked="checked"';

            $sql = '
                SELECT
                    rpf_date_format
                FROM
                    reports_formats
                WHERE
                    rpf_rep_id = :rep_id AND
                    rpf_section = \'GROUP\' AND
                    rpf_field = \'cla_dclaim\'
            ';

            $claimDateFormat = DatixDBQuery::PDO_fetch($sql, ['rep_id' => $rep['recordid']]);
        }

        if (array_key_exists('57.policies.start_date', $currentListingGroupFields)) {
            $policyDateCheckbox = 'checked="checked"';

            $sql = '
                SELECT
                    rpf_date_format
                FROM
                    reports_formats
                WHERE
                    rpf_rep_id = :rep_id AND
                    rpf_section = \'GROUP\' AND
                    rpf_field = \'start_date\'
            ';

            $policyDateFormat = DatixDBQuery::PDO_fetch($sql, ['rep_id' => $rep['recordid']]);
        }

        $field1 = SelectFieldFactory::createSelectField('cla_dclaim_group_by_options', 'CLA', $claimDateFormat['rpf_date_format'], $mode);
        $field1->setCustomCodes([
            Report::MONTH_AND_YEAR => _fdtk('month_and_year'),
            Report::YEAR => _fdtk('year'),
            Report::FINANCIAL_YEAR => _fdtk('financial_year'),
        ]);

        $field2 = SelectFieldFactory::createSelectField('pol_start_date_group_by_options', 'POL', $policyDateFormat['rpf_date_format'], $mode);
        $field2->setCustomCodes([
            Report::MONTH_AND_YEAR => _fdtk('month_and_year'),
            Report::YEAR => _fdtk('year'),
            Report::FINANCIAL_YEAR => _fdtk('financial_year'),
        ]);

        echo '
            <li id="listing_group_select_row">
                <table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
                    <tr>
                        <td class="windowbg2" align="left" colspan="3">Additional group options for Claims:</td>
                    </tr>
                    <tr>
                        <td class="windowbg2" align="left">
                            <input type="checkbox" id="cla_dclaim_checkbox" name="cla_dclaim_checkbox" class="listing_checkbox" ' . $claimDateCheckbox . '>&nbsp;' . $fieldLabels->getLabel('claims_main', 'cla_dclaim') . '
                        </td>
                        <td class="windowbg2" align="left">
                            ' . $field1->getField() . '
                        </td>
                    </tr>
                    <tr>
                        <td class="windowbg2" align="left">
                            <input type="checkbox" id="pol_start_date_checkbox" name="pol_start_date_checkbox" class="listing_checkbox" ' . $policyDateCheckbox . '>&nbsp;' . $fieldLabels->getLabel('policies', 'start_date') . '
                        </td>
                        <td class="windowbg2" align="left">
                            ' . $field2->getField() . '
                        </td>
                    </tr>
                </table>
            </li>
        ';
    }

    $currentListingOrderFields = [];

    if ($report !== null) {
        foreach ($report->getOrderBys() as $orderBy) {
            $orderByFqn = $orderBy->getFQN();
            $orderByTitle = $orderBy->getTitle();

            // set label for psism_ fields
            $orderByTitle = getPSIMSFieldLabel($orderByFqn, $orderByTitle, $psimsFields);

            $currentListingOrderFields[$orderByFqn] = $orderByTitle;
        }
    }

    echo '
    <li id="listing_order_select_row" >
    <table class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
    ';

    echo '
    <tr>
    <td class="windowbg2" align="left" colspan="3">
    Select fields to order by:
    </td>
    </tr>';

    $sortBySelectField = SelectFieldFactory::createSelectField('orders_forms', $module, 0, $mode);
    $sortBySelectField->setCustomCodes($fieldsets->getLabels());
    $sortBySelectField->setOnChangeExtra('jQuery(\'.subform_order_row\').hide();jQuery(\'#\'+jQuery(\'#orders_forms\').val()+\'_orders_row\').show()');

    echo '
    <tr>
    <td class="windowbg2" align="left">
    <div><div style="float:left; padding-top:3px;">Form:&nbsp;</div><div style="float:left; padding-top:3px;"> ' . $sortBySelectField->getField() . '</div></div>
    </td>
    <td class="windowbg2" align="left">
    </td>
    <td class="windowbg2" align="left">
    Current sort columns:
    </td>
    </tr>
    <tr>
        <td class="windowbg2" align="center">
        ';

    outputFieldSets($fieldsets, $showExcluded, $currentListingOrderFields, $mainTable, 'order', $reportsAdminOptionRetriever, $mode, $registry);

    echo '
        </td>
        <td class="windowbg2" align="center">';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
            onclick="moveSelectedItems($(\'[id$=_orders_list]:visible\'), $(\'#listing_orders_list\'), false);" >
            <span>' . _fdtk('btn_add') . ' >>></span>
        </button>
        <br /><br />
        <button type="button" class="dtx-button dtx-button-small dtx-button-small-min-width button-clear"
            onclick="var source_list_name = jQuery(\'#listing_orders_list option:selected\').attr(\'source\')+\'_orders_list\'; moveSelectedItems($(\'#listing_orders_list\'), $(\'#\' + source_list_name), true);" >
            <span><<< ' . _fdtk('btn_remove') . '</span>
        </button>';
    }
    echo '
        </td>
        <td class="windowbg2" align="center">
        <table>
        <tr>
        <td>';
    if ($mode !== FormTable::MODE_DISABLED) {
        echo '
        <img src="images/up_windowbg2.gif" border="0" onclick="moveUp(\'listing_orders_list\');"/> <br />
        <img src="images/collapse_windowbg2.gif" border="0" onclick="moveDown(\'listing_orders_list\');"/>';
    }
    echo '
        </td>
        <td>
        <select id="listing_orders_list" ' . $disabled . ' name="listing_orders_list[]" multiple="multiple" size="12" style="resize:both; width: 300px;' . $disabledStyle . '">';

    foreach ($currentListingOrderFields as $field => $label) {
        echo '<option value="' . Escaper::escapeForHTMLParameter($field) . '" source="' . getFieldsetId($field) . '">' . Escaper::escapeForHTML($label) . '</option>';
    }

    echo '
        </select>
        </td>
        </tr>
        </table>
        </td>
    </tr>';

    echo '
    </table>
    </li>';
}

/**
 * Extracts the correct fieldset ID from a fully qualified field name.
 *
 * @param string $fullyQualifiedName
 *
 * @return string
 */
function getFieldsetId($fullyQualifiedName)
{
    [$fieldset, , $field] = explode('.', $fullyQualifiedName);

    if (substr($field, 0, 3) == 'U0_') {
        // extra field fieldset Id for the purpose of this form
        return '-1';
    }

    return $fieldset;
}

/**
 * Extracts all "recordid" properties from the Fieldsets in a FieldsetCollection.
 *
 * @return array array of recordids, keyed by recordid
 */
function getRecordIds(FieldsetCollection $fieldsets): array
{
    // Make a list of fieldset IDs for comparison later.
    $fieldsetIds = [];
    foreach ($fieldsets as $fieldset) {
        $fieldsetIds[$fieldset->recordid] = $fieldset->recordid;
    }

    return $fieldsetIds;
}

function MakePackagedReportsList($rep, $FormType, $module)
{
    echo '<div class="new_titlebg"><div class="title_text_wrapper"><div class="section_title_group"><div class="section_title">' . _fdtk('packaged_reports_title') . '</div></div></div>';

    $web_report_id = DatixDBQuery::PDO_fetch('SELECT recordid FROM web_reports WHERE base_listing_report = :base_listing_report', ['base_listing_report' => $rep['recordid']], \PDO::FETCH_COLUMN);

    if (!$rep['import_id']) {
        echo '
        <div class="title_rhs_container">
            <button type="button" id="btnNewPackage" name="btnNewPackage"
              class="dtx-button dtx-button-small button-tertiary"
              onClick="SendTo(\'index.php?action=designapackagedreport&web_report_id=' . $web_report_id . '&module=' . $module . '\');">
                <span>' . _fdtk('new_packaged_report') . '</span>
            </button>
        </div>';
    }

    echo '
        </div>';

    if ($web_report_id) {
        $sql = "SELECT web_packaged_reports.recordid, [name], import_id
                FROM web_packaged_reports
                WHERE web_report_id = {$web_report_id}";

        $resultArray = DatixDBQuery::PDO_fetch_all($sql);
    }

    if (count($resultArray) == 0) {
        echo '<div class="page_title_background">' . _fdtk('no_packaged_reports') . '</div>';
    } else {
        echo '<input type="hidden" name="rep_pack_id" value="" />';
        echo '
        <table class="gridlines" cellspacing="1" cellpadding="4" width="99%" align="center" border="0">
        <tr>
            <td class="windowbg" width="50%" align="left">' . _fdtk('package_report_header_name') . '</td>
            <td class="windowbg" width="5%" align="center"></td>
            <td class="windowbg" width="5%" align="center"></td>
        </tr>   ';

        foreach ($resultArray as $row) {
            if ($rep['import_id']) {
                echo '
            <tr>
                <td class="windowbg2" width="50%" align="left"><a href="index.php?action=designapackagedreport&recordid=' . $row['recordid'] . '">' . $row['name'] . '</a></td>
                <td class="windowbg2" width="5%" align="center"></td>
                <td class="windowbg2" width="5%" align="center"></td>
            </tr>   ';
            } else {
                echo '
            <tr>
                <td class="windowbg2" width="50%" align="left"><a href="index.php?action=designapackagedreport&recordid=' . $row['recordid'] . '">' . $row['name'] . '</a></td>
                <td class="windowbg2" width="5%" align="center"><a href="index.php?action=designapackagedreport&recordid=' . $row['recordid'] . '">[edit]</a></td>
                <td class="windowbg2" width="5%" align="center"><a href="javascript:if(confirm(\'' . _fdtk('delete_packaged_report_q') . '\')){SendTo(\'index.php?action=deleteapackagedreport&recordid=' . $row['recordid'] . '\')}"> [delete]</a></td>
            </tr>   ';
            }
        }

        echo '</table>';
    }
}

function RecordPermissionsREP($rep, $FormAction, $Module = 'REP')
{
    MakeRecordSecPanel('REP', 'reports_packaged', $rep['rep_pack_id'], $FormAction, 'both');
}

/**
 * @return never
 */
function PackagedReport(): void
{
    $rep_id = Sanitize::SanitizeInt($_GET['rep_id']);
    $rep_pack_id = Sanitize::SanitizeInt($_GET['rep_pack_id']);

    if ($rep_pack_id != '') {
        $FormType = 'edit';
        $sql = "SELECT recordid as rep_pack_id, rep_pack_name, rep_pack_where, updateid, rep_pack_tables, rep_pack_override_sec
            FROM reports_packaged
            WHERE recordid = {$rep_pack_id}";

        $result = db_query($sql);
        $rep = db_fetch_array($result);
        $rep['rep_id'] = $rep_id;
    } else {
        $FormType = 'new';

        if ($rep_id != '') {
            $sql = "SELECT recordid as rep_id, rep_name
                FROM reports_extra
                WHERE recordid = {$rep_id}";

            $result = db_query($sql);
            $rep = db_fetch_array($result);
        }
    }

    PackagedReportForm($rep, $FormType, Sanitize::getModule($_GET['module']), $error);
}

/**
 * @return never
 */
function PackagedReportForm($rep, $FormType, $module, $error): void
{
    global $dtxtitle, $scripturl;

    LoggedIn();

    $dtxtitle = _fdtk('reports_administration_title');

    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            $module,
            $dtxtitle,
        );

    $ButtonGroup = new ButtonGroup();

    $ButtonGroup->AddButton([
        'id' => 'btnCancel',
        'name' => 'btnCancel',
        'label' => _fdtk('btn_cancel'),
        'onclick' => 'document.forms[0].rbWhat.value=\'cancel\';document.frmPackagedReportDocument.submit()',
        'action' => 'CANCEL',
        'class' => 'button-clear',
    ]);
    if ($rep['rep_pack_id']) {
        $ButtonGroup->AddButton([
            'id' => 'btnDelete',
            'name' => 'btnDelete',
            'label' => _fdtk('delete_packaged_report'),
            'onclick' => 'if(confirm(\'' . _fdtk('delete_packaged_report_q') . '\')){document.forms[0].rbWhat.value=\'delete\';document.frmPackagedReportDocument.submit();}',
            'action' => 'DELETE',
            'class' => 'button-warning',
        ]);
    }
    $ButtonGroup->AddButton([
        'id' => 'btnSave',
        'name' => 'btnSave',
        'label' => _fdtk('btn_save'),
        'onclick' => 'document.forms[0].rbWhat.value=\'save\';document.frmPackagedReportDocument.submit()',
        'action' => 'SAVE',
        'class' => 'button-primary',
    ]);

    GetSideMenuHTML(['module' => 'ADM', 'buttons' => $ButtonGroup]);

    template_header();

    $form_action = $FormType;
    $rep_pack_id = $rep['rep_pack_id'];
    $colspan = 4;

    $FormArray = [
        'Parameters' => ['Panels' => 'True', 'Condition' => false],
        'name' => [
            'Title' => _fdtk('packaged_report_settings_title'),
            'MenuTitle' => _fdtk('packaged_report_admin_menu_title'),
            'NewPanel' => true,
            'Function' => 'MakePackagedReportsAdminEditPanel',
            'Rows' => [],
        ],
        'permissions' => [
            'Title' => 'Permissions',
            'NewPanel' => false,
            'NoFieldAdditions' => true,
            'Function' => 'RecordPermissionsREP',
            'Condition' => $rep_pack_id,
            'NotModes' => ['new'],
            'Rows' => [],
        ],
    ];

    $RepTable = FormTableFactory::create($form_action);
    $RepTable->makeForm($FormArray, $rep, $module);

    echo '
    <form enctype="multipart/form-data" method="post" id="frmPackagedReportDocument" name="frmPackagedReportDocument" action="'
            . $scripturl . '?action=packagedreportsadmineditsave">
    <input type="hidden" id="form_action" name="form_action" value="' . Escape::EscapeEntities($form_action) . '" />
    <input type="hidden" id="rep_pack_id" name="rep_pack_id" value="' . Escape::EscapeEntities($rep_pack_id) . '" />
    <input type="hidden" id="rep_id" name="rep_id" value="' . Escape::EscapeEntities($rep['rep_id']) . '" />
    <input type="hidden" id="rbWhat" name="rbWhat" value="" />
    <input type="hidden" id="module" name="module" value="' . Escape::EscapeEntities($module) . '" />
    <input type="hidden" id="rep_pack_tables" name="rep_pack_tables" value="' . Escape::EscapeEntities($rep['rep_pack_tables']) . '" />
    <input type="hidden" id="updateid" name="updateid" value="' . Escape::EscapeEntities($rep['updateid']) . '" />';

    if ($error || $_GET['message']) {
        echo '<div class="form_error_wrapper">';

        if ($error) {
            echo '<div class="form_error_title">' . _fdtk('form_errors') . '</div>';

            foreach ($error as $thisError) {
                echo '<div class="form_error">' . $thisError . '</div>';
            }
        }

        echo '
                <div class="form_error">' . Escaper::escapeForHTML($_GET['message']) . '</div>
            </div>
        ';
    }

    // Display the sections
    $RepTable->makeTable();
    echo $RepTable->getFormTable();

    echo $ButtonGroup->getHTML();

    echo '</form>';

    footer();

    obExit();
}

function MakePackagedReportsAdminEditPanel($rep, $FormType, $module)
{
    ?>
    <script language="javascript" type="text/javascript">
        function getQueryString(query_id, module)
        {
            jQuery.ajax({
                url: scripturl + '?action=httprequest&type=getQueryString&responsetype=json',
                type: "POST",
                data: '&query_id=' + query_id + '&module=' + module,
                dataType: 'json',
                async: true,
                success: function(data) {
                    jQuery('#rep_pack_where').val(data[0]);
                    jQuery('#rep_pack_tables').val(data[1]);
                },
                error: function(jqXHR, textStatus, errorThrown ) {
                    alert('Error retrieving query...');
                }
            });
        }

        AlertAfterChange = true;
    </script>
    <?php
    if ($rep['rep_pack_name'] == '') {
        $rep['rep_pack_name'] = $rep['rep_name'];
    }

    $ModuleWhere = $_SESSION[$module]['WHERE'];
    $QBE = (isset($_SESSION['packaged_report']) && $_SESSION['packaged_report']['success'] == true && $_SESSION['packaged_report']['rep_pack_id'] == $rep['rep_pack_id']);
    unset($_SESSION['packaged_report']);

    if (\UnicodeString::strpos($ModuleWhere, 'rep_approved') === false && $module == 'INC') {
        $aiFlags = explode(',', GetParm('AI_FLAGS', 'Y,I,C'));

        if (!$aiFlags) {
            $aiFlags = ['Y', 'I', 'C'];
        }

        foreach ($aiFlags as $flag) {
            $flags[] = "'{$flag}'";
        }

        $aiFlags = implode(',', $flags);
    }

    $repPackNameField = InputFieldFactory::create('New', 'rep_pack_name', 70, 256, $rep['rep_pack_name'], '');
    echo GetDivFieldHTML('Name:', $repPackNameField->GetField());

    $saved_queries = get_saved_queries($module);

    if (!$saved_queries) {
        $saved_queries = [];
    }

    $field = SelectFieldFactory::createSelectField('rep_query', $module, '', '');
    $field->setCustomCodes($saved_queries);
    $field->setOnChangeExtra('initDropdown(jQuery("#rep_query_title"));getQueryString(jQuery("#rep_query_title").data("datixselect").field.val(), "' . $module . '")');
    echo GetDivFieldHTML(_fdtk('query_colon'), $field->getField());

    $queryWhere = ($QBE ? $ModuleWhere : $rep['rep_pack_where']);
    $queryField = TextAreaFieldFactory::create(
        $FormType,
        'rep_pack_where',
        7,
        70,
        $queryWhere,
        null,
        false,
    );
    echo GetDivFieldHTML('SQL where clause:', $queryField->GetField());

    $SearchField = '
        <input type="hidden" name="searchwhere" id="searchwhere" value="' . Escape::EscapeEntities($ModuleWhere) . '" />
        <input type="hidden" name="CHANGED-rep_pack_where" id="CHANGED-rep_pack_where" value="0" />
        <input type="button" value="Use current search criteria"' .
            ' onclick="javascript:document.getElementById(\'rep_pack_where\').value = document.getElementById(\'searchwhere\').value;setChanged(\'CHANGED-rep_pack_where\');">&nbsp;
        <input type="submit" value="Define criteria using query by example" onclick="document.forms[0].rbWhat.value=\'search\';" />';

    if ($QBE) {
        echo '
            <script language="javascript" type="text/javascript">
                setChanged(\'CHANGED-rep_pack_where\');
            </script>
        ';
    }

    $FormField = CustomFieldFactory::create($FormType, $SearchField);
    echo GetDivFieldHTML('', $FormField->GetField());

    echo '<li class="new_windowbg field_div"><div style="float:left;padding:6px;width:25%;">Override user security?<br />Set this option if you want the report viewed by users to include records to which they do not have access. Note: Setting this option will disable record links in the report</div>';
    $field = DivYesNoFieldFactory::create('New', 'rep_pack_override_sec', '', $rep['rep_pack_override_sec']);
    echo '<div style="width:70%;float:left;padding:4px">' . $field->GetField() . '</div></li>';
}

/**
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 */
function PackagedReportsAdminSaveAction(): void
{
    checkPermission();

    global $scripturl;

    $rep_pack = QuotePostArray($_POST);

    if ($_POST['rbWhat'] == 'cancel') {
        $location = "{$scripturl}?action=reportsadminaction&rep_id={$rep_pack['rep_id']}&module={$rep_pack['module']}&form_action=edit";
        Container::get(Response::class)->redirect($location);
    } elseif ($_POST['rbWhat'] == 'delete') {
        ReportsAdminDeletePackaged();
    } elseif ($_POST['rbWhat'] == 'save') {
        ReportsAdminSavePackaged();
    } elseif ($_POST['rbWhat'] == 'search') {
        ReportsAdminSavePackaged();
    }

    $location = "{$scripturl}?action=listlistingreports";
    Container::get(Response::class)->redirect($location);
}

/**
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 * @throws \Doctrine\DBAL\Driver\Exception
 * @throws \Doctrine\DBAL\Exception
 */
function ReportsAdminSavePackaged(): void
{
    checkPermission();

    global $scripturl, $ModuleDefs;

    $rep_pack = Sanitize::SanitizeStringArray($_POST);
    $rep_pack_id = $rep_pack['rep_pack_id'];
    $module = $rep_pack['module'];

    $error = [];

    if ($rep_pack['rep_pack_name'] == '') {
        $error[] = 'You must enter a report name.';
    }

    $Table = $rep_pack['rep_pack_tables'];

    if ($Table == '') {
        $Table = $ModuleDefs[$module]['TABLE'];
    }

    $where = $_POST['rep_pack_where'];

    $validator = new QueryValidationService();

    if ($where != '' && !$validator->validateWhereClause($where, $module)) {
        $error[] = "'SQL where clause' contains a syntax error";
    }

    if ($error) {
        $rep_pack['rep_pack_where'] = $where;
        PackagedReportForm($rep_pack, $form_action, $module, $error);
        obExit();
    }

    if (!$rep_pack_id) {
        $recordIdGenerator = (new RecordIdGeneratorFactory())->create('reports_packaged');
        $rep_pack_id = $recordIdGenerator->generateRecordId();
        $recordIdGenerator->createBlankRecordWithoutApprovalStatus($rep_pack_id);

        $NewRecord = true;
    }

    $sql = "UPDATE reports_packaged
        SET
        rep_id = {$rep_pack['rep_id']},
        rep_pack_name = N'" . EscapeQuotes($rep_pack['rep_pack_name']) . "',
        rep_pack_where = '" . EscapeQuotes($rep_pack['rep_pack_where']) . "',
        rep_pack_tables = '" . EscapeQuotes($rep_pack['rep_pack_tables']) . "',
        rep_pack_dupdated = '" . date('Y-m-d H:i:s') . "',
        rep_pack_updatedby = '{$_SESSION['initials']}',
        rep_pack_override_sec = '" . EscapeQuotes($rep_pack['rep_pack_override_sec']) . "',
        ";

    if ($NewRecord) {
        $sql .= "rep_pack_createdby = '{$_SESSION['initials']}',
                 rep_pack_dcreated = '" . date('Y-m-d H:i:s') . "',";
    }

    $updateId = (new UpdateIdGenerator())->generateUpdateId($_POST['updateid']);

    $sql .= " updateid = '" . $updateId . "' WHERE recordid = {$rep_pack_id}";

    if (!db_query($sql)) {
        fatal_error('Could not save packaged report' . $sql);
    } else {
        if ($_POST['rbWhat'] != 'search') {
            AddSessionMessage('INFO', _fdtk('packaged_report_saved'));
        }

        $aParam = [
            'module' => 'REP',
            'table' => 'reports_packaged',
            'recordid' => $rep_pack_id,
        ];

        $UserAccessList = GetRecordAccessUserList($aParam);
        $GroupAccessList = GetRecordAccessGroupList($aParam);
        $SomeoneLinked = false;

        if (is_array($UserAccessList)) {
            if (count($UserAccessList) > 0) {
                $SomeoneLinked = true;
            }
        }

        if (is_array($GroupAccessList)) {
            if (count($GroupAccessList) > 0) {
                $SomeoneLinked = true;
            }
        }

        if ($SomeoneLinked === false && $_POST['rbWhat'] != 'search') {
            AddSessionMessage('INFO', _fdtk('packaged_report_saved_permission_warning'));
        }
    }

    if ($_POST['rbWhat'] == 'search') {
        $_SESSION['packaged_report']['rep_id'] = $rep_pack['rep_id'];
        $_SESSION['packaged_report']['rep_pack_id'] = $rep_pack_id;
        $_SESSION['packaged_report']['module'] = $module;

        $location = $scripturl;

        if (isset($ModuleDefs[$module]['SEARCH_URL'])) {
            $location .= '?' . $ModuleDefs[$module]['SEARCH_URL'] . '&module=' . $module;
        } elseif ($ModuleDefs[$module]['GENERIC']) {
            $location .= '?action=search&module=' . $module;
        }
    } else {
        $location = "{$scripturl}?action=reportpackage&rep_id={$rep_pack['rep_id']}&rep_pack_id={$rep_pack_id}&module={$rep_pack['module']}";
    }

    Container::get(Response::class)->redirect($location);
}

/**
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 */
function ReportsAdminDeletePackaged(): void
{
    global $scripturl;

    checkPermission();

    $rep_pack = QuotePostArray($_POST);

    $sql = 'DELETE FROM reports_packaged WHERE recordid = :recordid';

    if (!DatixDBQuery::PDO_query($sql, ['recordid' => $rep_pack['rep_pack_id']])) {
        fatal_error('Could not delete packaged report');
    } else {
        $message = 'Packaged report has been deleted.';
    }

    $location = "{$scripturl}?action=reportsadminaction&rep_id={$rep_pack['rep_id']}&module={$rep_pack['module']}&form_action=edit";
    if ($message) {
        $location .= "&message={$message}";
    }
    Container::get(Response::class)->redirect($location);
}

function getPSIMSFields(bool $isTranslate, bool $isbuildQuery, $psimsFieldId = null, bool $includePsirfFields = true): array
{
    $dataHelper = (new IncidentsDataHelperFactory())->create();

    return $dataHelper->getPSIMSFields($isTranslate, $isbuildQuery, $psimsFieldId, $includePsirfFields);
}

function getPSIMSFieldLabel(string $fqn, string $label, array $psimsFields): string
{
    [$psimsFieldId, $psimsTable, $psimsField] = explode('.', $fqn);
    if (stripos($psimsTable, 'psims') !== false) {
        if (isset($psimsFields[$psimsTable . '.' . $psimsField])) {
            $label = $psimsFields[$psimsTable . '.' . $psimsField] . ' (' . _fdtk('inc_psims_title') . ')';
        }
    }

    return $label;
}

/**
 * @throws ContainerExceptionInterface
 * @throws PermissionDeniedException
 * @throws NotFoundExceptionInterface
 */
function checkPermission(): void
{
    if (!Container::get(CapturePermissionService::class)->userHasReportsAdminPermission()) {
        throw new PermissionDeniedException(_fdtk('no_module_access'));
    }
}

/**
 * @throws ContainerExceptionInterface
 * @throws NotFoundExceptionInterface
 * @throws PermissionDeniedException
 */
function checkPermissionIncludingModular(?string $module)
{
    checkPermission();
    $capturePermissionService = Container::get(CapturePermissionService::class);

    if (!$capturePermissionService->userHasOnlyModularPermission()) {
        return;
    }

    $accessibleModules = $capturePermissionService->getModulesUserCanAccess(
        CapturePermissionService::PERMISSION_REPORTS_ADMINISTRATION,
    );

    if (!in_array($module, $accessibleModules, true)) {
        throw new PermissionDeniedException(_fdtk('no_module_access'));
    }
}
