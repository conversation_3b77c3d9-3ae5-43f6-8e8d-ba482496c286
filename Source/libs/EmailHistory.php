<?php

use app\models\document\valueObjects\LinkedRecord;
use app\models\framework\config\DatixConfigFactory;
use app\models\location\services\TreeFieldService;
use app\services\document\DocumentListServiceFactory;
use app\services\user\UserDisplayDataService;
use Source\libs\EmailHistoryAttachments;
use Source\libs\NewEmailTemplate;
use src\component\field\ChangedFlagFieldFactory;
use src\component\field\CustomFieldFactory;
use src\component\field\EmailFieldFactory;
use src\component\field\InputFieldFactory;
use src\component\field\SelectFieldFactory;
use src\component\field\TextAreaFieldFactory;
use src\component\form\FormTableFactory;
use src\email\AwsSesEmailLimits;
use src\email\models\Notification;
use src\email\service\NotificationService;
use src\framework\registry\Registry;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\language\LanguageSessionFactory;
use src\users\model\User;
use src\users\model\UserModelFactory;

function SectionFeedbacktoReporter($module, $data, $FormType)
{
    $registry = Container::get(Registry::class);
    $moduleDef = $registry->getModuleDefs()->getModuleData($module);
    $notificationService = Container::get(NotificationService::class);

    $fetchNotificationCentreTemplate = $notificationService->shouldEmailHistoryFetchNotificationCentreTemplate($module);

    $scripturl = $registry->getScriptUrl();

    $showJobTitle = $registry->getParm('STAFF_NAME_DROPDOWN', 'N');

    $addMinExtension = $registry->getDatixConfig()->isMinifierOn() ? '.min' : '';

    $recordid = $data['recordid'];

    $fb = getFeedbackMessageHistory($module, $recordid);

    $inc = $data; // some feedback templates will use $inc[...]

    $Perms = $moduleDef->getPermissionsGlobal();

    $bHideContacts = false;

    $EmailTo = [];

    $feedbackFormDesign = \Forms_FormDesign::GetFormDesign([
        'module' => $module,
        'level' => 2,
    ]);

    echo '
<script language="JavaScript" type="text/javascript">
    var FeedbackValidationNeeded = true;
    const TotalAllowedMessageSize = ' . (AwsSesEmailLimits::AWS_SES_MAIL_SIZE_MB_LIMIT * 1000000) . ';
</script>
';

    // work out which permission-sets can see which contacts.
    if (!CanSeeContacts('INC', $Perms, $data['rep_approved'])) {
        $bHideContacts = true;
    }

    // These two lines are legacy from when you could send feedback from readonly forms. Can be factored out now that this functionality has been removed.
    $FeedbackFormType = ($FormType == 'Print' ? $FormType : 'Edit');
    $RecipientsTable = FormTableFactory::create($FeedbackFormType, $module);

    $readOnly = $FormType === 'ReadOnly'
        || $bHideContacts
        || isset($feedbackFormDesign->ReadOnlyFields['feedback']);

    // /////////////////////////////////////
    // / Feedback //////////////////////////
    // /////////////////////////////////////

    if (!$readOnly) {
        // Display recipients subtitle
        echo '
        <li class="section_title_row" id="recipients_title_row" name="recipients_title_row">
            <div class="section_title_group">
                <div class="section_title">' . _fdtk('recipients') . '</div>
            </div>
        </li>
        ';

        $handlerInitials = $data[$moduleDef->getFieldName('HANDLER')];

        if ($handlerInitials) {
            $Factory = new UserModelFactory();
            $handler = $Factory->getMapper()->findByInitials($handlerInitials);

            if ($handler !== null) {
                $EmailTo[$handler->recordid] = '[Handler] ' . formatDisplayName($handler);
            }
        }

        /** @var ?string $investigatorsInitials */
        $investigatorsInitials = $data[$moduleDef->getFieldName('INVESTIGATORS')] ?? null;

        if ($investigatorsInitials) {
            $Factory = new UserModelFactory();

            foreach (explode(' ', $investigatorsInitials) as $investigatorInitials) {
                $investigator = $Factory->getMapper()->findByInitials($investigatorInitials);

                if ($investigator !== null) {
                    $EmailTo[$investigator->recordid] = '[Investigator] ' . formatDisplayName($investigator);
                }
            }
        }

        /** @var ?string $managersInitials */
        $managersInitials = $data[$moduleDef->getFieldName('MANAGER')] ?? null;

        if ($managersInitials) {
            $Factory = new UserModelFactory();
            $manager = $Factory->getMapper()->findByInitials($managersInitials);

            if ($manager !== null) {
                $EmailTo[$manager->recordid] = '[Manager] ' . formatDisplayName($manager);
            }
        }

        if (!empty($_GET['feedbackmessage'])) {
            $CustomField = '<span style="color: red;">' . _fdtk('feedback_sent')
                . '</span>';
            $RecipientsTable->makeTitleRow($CustomField, 'windowbg2');
        }

        if (!empty($_GET['feedbackerrormessage'])) {
            $CustomField = '<span style="color: red;">' . _fdtk('feedback_err')
                . '</span>';
            $RecipientsTable->makeTitleRow($CustomField, 'windowbg2');
        }

        if ($recordid && (!$bHideContacts && $Perms != 'DIF2_READ_ONLY')) {
            $foreignKey = $moduleDef->getForeignKey();

            $sql = "SELECT link_role, con_email, con_surname, con_forenames, con_title, fullname, job_title
                    from contacts_main, link_contacts
                    where contacts_main.recordid = link_contacts.con_id
                    AND con_email != '' AND con_email IS NOT NULL
                    AND link_contacts." . $foreignKey . ' = :recordid
                    AND contacts_main.rep_approved = :rep_approved
                    AND con_dod IS NULL';

            $contacts = DatixDBQuery::PDO_fetch_all($sql, ['recordid' => $recordid, 'rep_approved' => 'FA']);
            [$reporterAdded, $list] = formatContactDisplayName($contacts);
            $EmailTo += $list;

            // get reporter details
            if ($module === 'INC' && !$reporterAdded) {
                if ($data['inc_rep_email'] && !$fb['email'] && !$bHideContacts && $Perms != 'DIF2_READ_ONLY') {
                    $EmailTo[$data['inc_rep_email']] = ($showJobTitle == 'B' && $data['inc_reportedby'] != '' ? $data['inc_reportedby'] . ' - ' : '') . '[Reporter] ' . $data['inc_repname'] . ($showJobTitle == 'A' && $data['inc_reportedby'] != '' ? ' - ' . $data['inc_reportedby'] : '');
                }
            }

            // Get linked Organisations
            if ($moduleDef->showOrganisationsInEmailingToField()) {
                $organisationTerminology = $moduleDef->getOrganisationsTerminology();

                $sql = "SELECT orgs.recordid AS org_id, orgs.org_email AS email, orgs.org_name AS name
                    FROM organisations_main orgs
                    JOIN link_respondents link
                    ON orgs.recordid = link.org_id
                    WHERE orgs.org_email != '' AND orgs.org_email IS NOT NULL
                    AND link.main_recordid = :recordid
                    AND link.main_module = :module";

                $organisations = DatixDBQuery::PDO_fetch_all($sql, [
                    'recordid' => $recordid,
                    'module' => $module,
                ]);

                foreach ($organisations as $organisation) {
                    $EmailTo[$organisation['email']] = '[' . $organisationTerminology . '] ' . $organisation['name'];
                }
            }

            $sql = '
                SELECT
                    recordid,
                    use_email,
                    use_surname,
                    use_forenames,
                    use_title,
                    use_jobtitle
                FROM
                    staff
            ';

            $Where[] = '
                (use_email != \'\' AND use_email IS NOT NULL)
            AND
                (use_staff_include IS NULL OR use_staff_include = \'Y\' OR use_staff_include = \'\')
            AND
                (use_dclosed IS NULL OR use_dclosed > GETDATE())';

            if ($registry->getParm('STAFF_EMPL_FILTERS', 'N')->isTrue()) {
                $staffFilterParentsGlobalValue = $registry->getParm('STAFF_EMPL_FILTERS_PARENTS', 'location service')->toScalar();
                $staffFieldParents = explode(' ', $staffFilterParentsGlobalValue);

                foreach ($staffFieldParents as $staffFieldParent) {
                    $fieldName = $moduleDef->getStaffFieldMapping($staffFieldParent) ?? $staffFieldParent . '_id';
                    $parentValues = (new TreeFieldService())->getFlatIdListIncludingChildren($staffFieldParent, [$data[$fieldName]], true);
                    $parentWhere = [];
                    foreach ($parentValues as $parentValue) {
                        $parentWhere[] = '( '
                            . $staffFieldParent . '_id LIKE \'' . $parentValue . ' %\' OR '
                            . $staffFieldParent . '_id LIKE \'% ' . $parentValue . '\' OR '
                            . $staffFieldParent . '_id LIKE \'% ' . $parentValue . ' %\' OR '
                            . $staffFieldParent . '_id = \'' . $parentValue . '\')';
                    }
                    if (!empty($parentWhere)) {
                        $Where[] = '( ' . implode(' OR ', $parentWhere) . ' )';
                    }
                }
            }

            $Where[] = 'active = 1';
            $WhereClause = MakeSecurityWhereClause($Where, 'USE');

            $sql .= 'WHERE ' . $WhereClause . ' ORDER BY sta_surname ASC';

            $Result = DatixDBQuery::PDO_fetch_all($sql, []);

            $GlobalAddressBook = [];

            $isSurnameFirst = $registry->getParm('CONTACT_SURNAME_SORT', 'Y')->isTrue();
            $jobTitlePosition = $registry->getParm(UserDisplayDataService::JOB_TITLE_CONFIG_PARAM);
            $userDisplayService = Container::get(UserDisplayDataService::class);
            foreach ($Result as $user) {
                $GlobalAddressBook[$user['recordid']] = $userDisplayService
                    ->formatArrayDisplayValue($user, $isSurnameFirst, $jobTitlePosition);
            }
        }

        if (!$bHideContacts && $Perms != 'DIF2_READ_ONLY') {
            // Staff and contacts attached to this record
            $staffField = SelectFieldFactory::createSelectField('fbk_to', $module, '', $FeedbackFormType, true);
            if (empty($EmailTo)) {
                $EmailTo = [
                    '!NOCODES!' => _fdtk('no_codes_available'),
                ];
            }
            $staffField->setCustomCodes($EmailTo);
            $staffField->setIgnoreMaxLength();

            $staffHtml = '<label for="fbk_to_title">' . ($feedbackFormDesign->UserLabels['dum_fbk_to'] ?? null ?: _fdtk('staff_contacts'));
            $config = (new DatixConfigFactory())->getInstance();
            if ($config->getMultiSelect2Enabled()) {
                $staffHtml .= '<div class="field_extra_text_ms2_div_fbk_to" style="display:none"><ul>';
                foreach ($EmailTo as $k => $v) {
                    $staffHtml .= '<li class="field_extra_text_ms2_li_fbk_to" data-key="' . $k . '" data-val="' . $v . '">' . $v . '</li>';
                }
                $staffHtml .= '</ul></div>';
            }

            if (isset($feedbackFormDesign->HelpTexts['dum_fbk_to']) && $feedbackFormDesign->HelpTexts['dum_fbk_to'] != '') {
                $label = ($feedbackFormDesign->UserLabels['dum_fbk_to'] ?: _fdtk('staff_contacts'));
                $label = str_replace('"', '&quot;', $label);
                $label = str_replace('\'', '\\\'', $label);

                $staffHtml .= '&nbsp;
                <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                    . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_to&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                    . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                    <img id="dum_fbk_to_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="Help (' . $label . ')" />
                </a>';
            }

            if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_to'])) {
                $staffHtml .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_to'] . '</div>';
            }

            $staffHtml .= '</label>';

            if ($config->getMultiSelect2Enabled()) {
                $staffHtml .= '<div class="field_extra_text_ms2_div_fbk_gab" style="display:none"><ul>';
                foreach ($GlobalAddressBook as $k => $v) {
                    $staffHtml .= '<li class="field_extra_text_ms2_li_fbk_gab" data-key="' . $k . '" data-val="' . $v . '">' . $v . '</li>';
                }
                $staffHtml .= '</ul></div>';
            }

            // All users
            $allUsersField = SelectFieldFactory::createSelectField('fbk_gab', $module, '', $FeedbackFormType, true);
            $allUsersField->setCustomCodes($GlobalAddressBook);
            $allUsersField->setIgnoreMaxLength();

            $allUsersHtml = '<label for="fbk_gab_title">' . ($feedbackFormDesign->UserLabels['dum_fbk_gab'] ?? null ?: _fdtk('all_users'));

            if (isset($feedbackFormDesign->HelpTexts['dum_fbk_gab']) && $feedbackFormDesign->HelpTexts['dum_fbk_gab'] != '') {
                $label = ($feedbackFormDesign->UserLabels['dum_fbk_gab'] ?: _fdtk('all_users'));
                $label = str_replace('"', '&quot;', $label);
                $label = str_replace('\'', '\\\'', $label);

                $allUsersHtml .= '&nbsp;
                <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                    . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_gab&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                    . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                    <img id="dum_fbk_gab_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="Help (' . $label . ')" />
                </a>';
            }

            if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_gab'])) {
                $allUsersHtml .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_gab'] . '</div>';
            }

            $allUsersHtml .= '</label>';

            // Additional recipients
            $additionalRecipientsHtml = '<label for="fbk_email">' . ($feedbackFormDesign->UserLabels['dum_fbk_email'] ?? null ?: _fdtk('additional_recipients'));

            if (isset($feedbackFormDesign->HelpTexts['dum_fbk_email']) && $feedbackFormDesign->HelpTexts['dum_fbk_email'] != '') {
                $label = ($feedbackFormDesign->UserLabels['dum_fbk_email'] ?: _fdtk('additional_recipients'));
                $label = str_replace('"', '&quot;', $label);
                $label = str_replace('\'', '\\\'', $label);

                $additionalRecipientsHtml .= '&nbsp;
                <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                    . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_email&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                    . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                    <img id="dum_fbk_email_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="Help (' . $label . ')" />
                </a>';
            }

            if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_email'])) {
                $additionalRecipientsHtml .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_email'] . '</div>';
            }

            $additionalRecipientsHtml .= '</label>';

            $additionalRecipientsField = EmailFieldFactory::create($FeedbackFormType, 'fbk_email', 68, 200, '');

            $fieldOrders = [
                'dum_fbk_to' => [
                    'html' => $staffHtml,
                    'field' => $staffField,
                ],
                'dum_fbk_gab' => [
                    'html' => $allUsersHtml,
                    'field' => $allUsersField,
                ],
                'dum_fbk_email' => [
                    'html' => $additionalRecipientsHtml,
                    'field' => $additionalRecipientsField,
                ],
            ];

            $formDesignFieldOrders = $feedbackFormDesign->FieldOrders['feedback'] ?? null;

            $ordered = [];

            if (!empty($formDesignFieldOrders)) {
                foreach ($formDesignFieldOrders as $key) {
                    if (array_key_exists($key, $fieldOrders)) {
                        $ordered[$key] = $fieldOrders[$key];
                        unset($fieldOrders[$key]);
                    }
                }
            }

            $orderedFields = $ordered + $fieldOrders;

            foreach ($orderedFields as $orderedFieldKey => $orderedField) {
                if (empty($feedbackFormDesign->HideFields[$orderedFieldKey])) {
                    $RecipientsTable->makeRow($orderedField['html'], $orderedField['field']);
                }
            }
        } else {
            $fbkToObj = CustomFieldFactory::create('', '<input type="hidden" name="fbk_to" id="fbk_to" value="' . $row['con_id']
                . '" />' . $EmailTo[$row['con_id']], );
            $RecipientsTable->makeRow(_fdtk('staff_contacts'), $fbkToObj);
        }

        if (empty($_GET['print'])) {
            echo $RecipientsTable->getFormTable();
        }

        // Display message subtitle
        echo '
            <li class="section_title_row" id="message_title_row" name="message_title_row">
                <div class="section_title_group">
                    <div class="section_title">' . _fdtk('message_title') . '</div>
                </div>
            </li>
        ';

        $MessageTable = FormTableFactory::create($FeedbackFormType, $module); // Even if read only, want to be able to send messages.
        $MessageTable->Contents .= '<script language="javascript" src="js_functions/email' . $addMinExtension . '.js"></script>';

        $language = LanguageSessionFactory::getInstance()->getLanguage();

        $templateType = 'FBK';
        $templateId = $registry->getParm('EMT_' . $module . '_' . $templateType)->toScalar();
        $template = new NewEmailTemplate($templateId, $module, $templateType);
        $template->ConstructEmailToSend($data['recordid'], $language);

        $EmailText['Feedback']['Subject'] = $template->EmailSubject;
        $EmailText['Feedback']['Body'] = $template->EmailBody;


        $html = '<label for="fbk_subject">' . ($feedbackFormDesign->UserLabels['dum_fbk_subject'] ?? null ?: _fdtk('subject'));

        if (isset($feedbackFormDesign->HelpTexts['dum_fbk_subject']) && $feedbackFormDesign->HelpTexts['dum_fbk_subject'] != '') {
            $label = ($feedbackFormDesign->UserLabels['dum_fbk_subject'] ?: _fdtk('subject'));
            $label = str_replace('"', '&quot;', $label);
            $label = str_replace('\'', '\\\'', $label);

            $html .= '&nbsp;
            <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_subject&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                <img id="dum_fbk_subject_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="Help (' . $label . ')" />
            </a>';
        }

        if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_subject'])) {
            $html .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_subject'] . '</div>';
        }

        $html .= '</label>';

        $MessageTable->makeRow(
            $html,
            InputFieldFactory::create($FeedbackFormType, 'fbk_subject', 68, 80, $EmailText['Feedback']['Subject']),
            false,
            false,
            ['hidden' => $fetchNotificationCentreTemplate, 'dbfield' => 'fbk_subject'],
        );

        $ChangedField = ChangedFlagFieldFactory::create($FeedbackFormType, 'fbk_body');
        $fb_body = TextAreaFieldFactory::create(
            $FeedbackFormType,
            'fbk_body',
            10,
            70,
            $EmailText['Feedback']['Body'],
            null,
            true,
            true,
            '',
            '',
            '',
            '',
            '',
            '',
            true,
        );
        $fb_body->ConcatFields($fb_body, $ChangedField);

        $html = '<label for="fbk_body">' . ($feedbackFormDesign->UserLabels['dum_fbk_body'] ?? null ?: _fdtk('body_of_message_header'));

        if (isset($feedbackFormDesign->HelpTexts['dum_fbk_body']) && $feedbackFormDesign->HelpTexts['dum_fbk_body'] != '') {
            $label = ($feedbackFormDesign->UserLabels['dum_fbk_body'] ?: _fdtk('body_of_message_header'));
            $label = str_replace('"', '&quot;', $label);
            $label = str_replace('\'', '\\\'', $label);

            $html .= '&nbsp;
            <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_body&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                <img id="dum_fbk_body_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="Help (' . $label . ')" />
            </a>';
        }

        if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_body'])) {
            $html .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_body'] . '</div>';
        }

        $html .= '</label>';

        $MessageTable->makeRow(
            $html,
            $fb_body,
            false,
            false,
            ['hidden' => $fetchNotificationCentreTemplate, 'dbfield' => 'fbk_body'],
        );

        if ($fetchNotificationCentreTemplate) {
            $html = '<div class="linked-data-action-button">';
            $html .= '<button class="dtx-button dtx-button-small button-secondary" id="btn_load_template" onclick="LoadEmailTemplate(\'' . $module . '\');">'
                . _fdtk('btn_create_message')
                . '</button>';
            $html .= '</div>';

            $variableKeys = Container::get(Notification::class)->getAllVariableKeys();

            $html .= '<script> fbk_variable_keys = ' . json_encode($variableKeys[$module] ?? null ?: [], JSON_THROW_ON_ERROR) . '; </script>';
            $MessageTable->makeTitleRow($html, 'new_windowbg');
        }

        if (empty($feedbackFormDesign->HideFields['dum_fbk_attachments'])) {
            $attachmentsField = SelectFieldFactory::createSelectField('fbk_attachments', $module, '', $FeedbackFormType, true);

            $mainRecordId = $data['recordid'];
            $record = new LinkedRecord($module, $mainRecordId);
            $docListService = DocumentListServiceFactory::create();
            $documents = $docListService->getListOfDocuments($record);

            $emailHistoryAttachment = Container::get(EmailHistoryAttachments::class);

            $documentCodes = $emailHistoryAttachment->fetchDocumentCodes($documents);

            $attachmentsField->setCustomCodes($documentCodes);
            $attachmentsField->setIgnoreMaxLength();

            $html = '<label for="fbk_attachments">' . ($feedbackFormDesign->UserLabels['dum_fbk_attachments'] ?? null ?: _fdtk('attachments'));
            $config = (new DatixConfigFactory())->getInstance();

            if ($config->getMultiSelect2Enabled()) {
                $html .= '<div class="field_extra_text_ms2_div_fbk_attachments" style="display:none">';

                $html .= $emailHistoryAttachment->buildAttachmentListItemsHtml($documents);

                $html .= '</div>';
            } else {
                $attachmentsFieldJSON = json_encode(
                    $emailHistoryAttachment->buildAttachmentJSON($documents),
                    JSON_THROW_ON_ERROR,
                );
                $html .= <<<HTML
                    <script>
                    fbk_attachments_details = {$attachmentsFieldJSON};
                    </script>
                    HTML;
            }

            if (isset($feedbackFormDesign->HelpTexts['dum_fbk_attachments']) && $feedbackFormDesign->HelpTexts['dum_fbk_attachments'] != '') {
                $label = ($feedbackFormDesign->UserLabels['dum_fbk_attachments'] ?: _fdtk('attachments'));
                $label = str_replace('"', '&quot;', $label);
                $label = str_replace('\'', '\\\'', $label);

                $html .= '&nbsp;
                <a href="javascript:PopupDivFromURL(\'help\', \'' . _fdtk('datix_help') . $label . '\', \''
                    . $scripturl . '?action=fieldhelp&module=' . $module . '&field=dum_fbk_attachments&level=2&parent_module=&link_type=&parent_level=2&id=' . $feedbackFormDesign->getId()
                    . '\', \'\', [{\'value\':\'' . _fdtk('close') . '\',\'onclick\':\'GetFloatingDiv(\\\'help\\\').CloseFloatingControl()\'}], \'' . DROPDOWN_WIDTH_DEFAULT . 'px\')">
                    <img id="dum_fbk_attachments_help_image" src="images/Help.gif" style="cursor:pointer;border:0" alt="' . _fdtk('help_alt') . ' (' . $label . ')" />
                </a>';
            }

            if (!empty($feedbackFormDesign->UserExtraText['dum_fbk_attachments'])) {
                $html .= '<div class="field_extra_text">' . $feedbackFormDesign->UserExtraText['dum_fbk_attachments'] . '</div>';
            }

            $html .= '</label>';

            $MessageTable->makeRow($html, $attachmentsField);
        }

        // TODO: when refactoring, it would be good to be able to add hidden fields easily using FormField without having to write some custom html to inject in
        if ($FormType != 'Print' && empty($_GET['print'])) {
            $hiddenField = '<input type="hidden" id="fbk_html" name="fbk_html" value="true" />';
            $MessageTable->makeTitleRow($hiddenField, '');

            $CustomField = '



            <div class="linked-data-action-button">
                <button type="button" class="dtx-button dtx-button-small button-secondary" id="btn_send_feedback" onclick="SendFeedback(\'' . $module . '\',' . $recordid . ')"' . ($fetchNotificationCentreTemplate ? ' disabled="true"' : '') . '><span>'
                . _fdtk('btn_send_message') . '</span></button>';

            $CustomField .= '
            </div>
            <div id="ajax_email_status" class="email_status_div" style="display:none">Sending feedback...</div>';
            $MessageTable->makeTitleRow($CustomField, 'new_windowbg');
        }

        if (empty($_GET['print'])) {
            echo $MessageTable->getFormTable();
        }
    }

    // /////////////////////////////
    // /// History /////////////////
    // /////////////////////////////

    if ($recordid) {
        echo '
        <li class="section_title_row" id="message_title_row" name="message_title_row">
                <div class="section_title_group">
                    <div class="section_title">' . _fdtk('message_history_title') . '</div>
                </div>
         </li>
    <li>

  <div class="table-responsive">

    <table class="linked-data-table table table-striped table-hover" cellspacing="0" cellpadding="0" width="100%" align="center" border="0">
        <thead>
        <tr>
            <th>
            ' . _fdtk('date_time_header') . '
            </th>
            <th>
            ' . _fdtk('sender') . '
            </th>
            <th>
            ' . _fdtk('recipient') . '
            </th>
            <th>
            ' . _fdtk('body_of_message_header') . '
            </th>
            <th>
            ' . _fdtk('attachments') . '
            </th>
        </tr>
        </thead>
        <tbody>
        ';

        if (!$fb) {
            echo '
        <tr>
            <td colspan="5">
            ' . _fdtk('no_messages_for_inc') . '
            </td>
        </tr>';
        } elseif (is_array($fb)) {
            foreach ($fb as $history) {
                if ($history['emh_from_id']) {
                    $sql = 'select use_email, use_surname, use_forenames, use_title
                                         from users_main
                                         where recordid = :recordid';
                    $sender = DatixDBQuery::PDO_fetch($sql, ['recordid' => $history['emh_from_id']]);
                }

                if ($sender['use_forenames'] || $sender['use_title']) {
                    $sender_name = "{$sender['use_surname']}, {$sender['use_forenames']} {$sender['use_title']}";
                } elseif ($sender['use_surname']) {
                    $sender_name = "{$sender['use_surname']}";
                } else {
                    $sender_name = '';
                }

                $sender = '';

                if ($history['con_id']) {
                    $sql = 'select use_email, use_surname, use_forenames, use_title
                                         from users_main
                                         where recordid = :recordid';
                    $recipient = DatixDBQuery::PDO_fetch($sql, ['recordid' => $history['con_id']]);
                }

                if ($recipient['use_forenames'] || $recipient['use_title']) {
                    $recipient_name = "{$recipient['use_surname']}, {$recipient['use_forenames']} {$recipient['use_title']}";
                } elseif ($recipient['use_surname']) {
                    $recipient_name = "{$recipient['use_surname']}";
                } elseif ($history['emh_email']) {
                    $recipient_name = str_replace(',', ', ', $history['emh_email']);
                } else {
                    $recipient_name = '';
                }

                $recipient = null;

                echo "
                    <tr>
                        <td valign='top' nowrap='nowrap' align='right'>" . formatDateForDisplay($history['emh_dsent'], true, true) . "</td>
                        <td valign='top'>" . Escaper::escapeForHTML($sender_name) . "</td>
                        <td valign='top' style='word-break:break-all;'>" . Escaper::escapeForHTML($recipient_name) . "</td>
                        <td valign='middle' style='word-break:break-all;'>" . strip_tags($history['emh_body'], Escaper::ALLOWED_HTML_TAGS) . "</td>
                        <td valign='top' style='word-break:break-all;'>" . Escaper::escapeForHTML($history['emh_attachments']) . '</td>
                    </tr>';
            }
        }

        echo '</tbody></table></div></li>';
    }

    if ($Perms == 'DIF2_READ_ONLY') {
        $FormType = 'ReadOnly';
    }
}

function getFeedbackMessageHistory(string $module, $recordid): ?array
{
    if (empty($recordid)) {
        return null;
    }

    $sql = '
          SELECT emh_body, emh_email, emh_dsent, emh_type, con_id, emh_from_id, emh_attachments
            FROM email_history
           WHERE mod_id = :mod_id
             AND link_id = :link_id
             AND emh_type LIKE :emh_type
             AND (emh_not_sent != 1 OR emh_not_sent IS NULL)
        ORDER BY emh_dsent DESC
    ';

    $queryResult = DatixDBQuery::PDO_fetch_all($sql, ['mod_id' => GetModIDFromShortName($module), 'link_id' => abs($recordid), 'emh_type' => 'FBK_%']);

    return empty($queryResult) ? null : $queryResult;
}

function formatDisplayName(?User $user): ?string
{
    $registry = Container::get(Registry::class);
    $jobTitlePosition = $registry->getParm(UserDisplayDataService::JOB_TITLE_CONFIG_PARAM);
    $isSurnameFirst = $registry->getParm('CONTACT_SURNAME_SORT', 'Y')->isTrue();

    return Container::get(UserDisplayDataService::class)->formatUserDisplayValue(
        $user,
        $isSurnameFirst,
        $jobTitlePosition,
    );
}

/**
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 */
function formatContactDisplayName(array $contacts): array
{
    $list = [];
    $reporterAdded = false;
    $registry = Container::get(Registry::class);
    $reporterRole = $registry->getParm('REPORTER_ROLE', 'REP')->toScalar();
    $isSurnameFirst = $registry->getParm('CONTACT_SURNAME_SORT', 'Y')->isTrue();
    $jobTitlePosition = $registry->getParm(UserDisplayDataService::JOB_TITLE_CONFIG_PARAM);
    $userDisplayService = Container::get(UserDisplayDataService::class);

    foreach ($contacts as $row) {
        $role = null;
        if (isset($row['link_role']) && $row['link_role'] === $reporterRole) {
            $role = 'Reporter';
        }

        $list[$row['con_email']] = ($role ? "[{$role}] " : '') . $userDisplayService->formatArrayDisplayValue(
            [
                'use_title' => $row['con_title'],
                'use_forenames' => $row['con_forenames'],
                'use_surname' => $row['con_surname'],
                'use_jobtitle' => $row['job_title'],
            ],
            $isSurnameFirst,
            $jobTitlePosition,
        );

        if ($role === 'Reporter') {
            $reporterAdded = true;
        }
    }

    return [$reporterAdded, $list];
}
