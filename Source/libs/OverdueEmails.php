<?php

use app\models\generic\valueObjects\Module;
use app\services\overdue\OverdueServiceFactory;
use src\email\EmailSenderFactory;
use src\email\models\Notification;
use src\email\service\AdditionalEmailServiceFactory;
use src\logger\Facade\Log;
use src\system\language\LanguageSessionFactory;
use src\users\model\UserModelFactory;

function GetRecipients()
{
    global $ModuleDefs;

    $module = $_GET['module'];
    $overdueService = (new OverdueServiceFactory())->create();
    if ($module === 'INC') {
        $OverdueStatuses = array_keys($overdueService->getOverdueStatuses('INC'));
        $StatusesToEmail = array_intersect(explode('|', $_GET['statuses']), $OverdueStatuses);

        SetGlobal('OVERDUE_EMAIL_STATUS', implode(' ', explode('|', Sanitize::SanitizeString($_GET['statuses']))));
        SetGlobal('OVERDUE_EMAIL_USERS', implode(' ', explode('|', Sanitize::SanitizeString($_GET['users']))));
    } elseif ($module == 'COM') {
        foreach ($ModuleDefs['COM']['HARD_CODED_LISTINGS'] as $key => $listingDef) {
            if (isset($listingDef['OverdueWhere'])) {
                $OverdueStatuses[$key] = $listingDef['Title'];
                $OverdueWhere[$key] = translateStaticAtCodes($listingDef['OverdueWhere']);
            }
        }

        $StatusesToEmail = array_intersect(explode('|', $_GET['statuses']), array_flip($OverdueStatuses));

        SetGlobal('OVERDUE_EMAIL_STATUS_COM', implode(' ', explode('|', Sanitize::SanitizeString($_GET['statuses']))));
        SetGlobal('OVERDUE_EMAIL_USERS_COM', implode(' ', explode('|', Sanitize::SanitizeString($_GET['users']))));
    } elseif ($module === 'MOR') {
        $overdueStatuses = $overdueService->getOverdueStatuses('MOR');
        $StatusesToEmail = array_intersect(explode('|', $_GET['statuses']), array_keys($overdueStatuses));

        SetGlobal('OVERDUE_EMAIL_STATUS_MOR', implode(' ', explode('|', Sanitize::SanitizeString($_GET['statuses']))));
        SetGlobal('OVERDUE_EMAIL_USERS_MOR', implode(' ', explode('|', Sanitize::SanitizeString($_GET['users']))));
    }

    $ContactsToEmail = [];
    $JSONdata = ['complete' => 1];

    if (is_array($StatusesToEmail) && !empty($StatusesToEmail)) {
        foreach ($StatusesToEmail as $Status) {
            if ($module == 'INC') {
                $overduesql = $overdueService->getOverdueSQL('INC', $Status);

                $sql = 'SELECT recordid, inc_head, inc_mgr, inc_investigator FROM incidents_main ' . $overduesql['join'] . ' WHERE rep_approved = \'' . $Status . '\'' . ($overduesql['where'] ? ' AND ' . $overduesql['where'] : '');
            } elseif ($module == 'COM') {
                if ($OverdueWhere[$Status]) {
                    $sql = 'SELECT recordid, com_head, com_mgr, com_investigator FROM compl_main WHERE ' . $OverdueWhere[$Status];
                }
            } elseif ($module == 'MOR') {
                $overdueSql = $overdueService->getOverdueSQL('MOR', $Status);
                $overdueWhere = isset($overdueSql['where']) ? ' AND ' . $overdueSql['where'] : '';

                $sql = "SELECT recordid, reviewer
                    FROM mortality_main {$overdueSql['join']}
                    WHERE rep_approved = '{$Status}' {$overdueWhere}";
            }

            $result = db_query($sql);

            while ($row = db_fetch_array($result)) {
                $SpecificContactsToEmail = getContactsToOverdueEmail($row, $_GET['users'], $module);

                foreach ($SpecificContactsToEmail as $Contact) {
                    $ContactsToEmail[$Contact][] = $row['recordid'];
                }
            }
        }
    }

    $userMapper = (new UserModelFactory())->getMapper();
    $users = $userMapper->findByInitialsList(array_keys($ContactsToEmail), true)->toArray();

    $additionalEmailService = (new AdditionalEmailServiceFactory())->create();
    $users = $additionalEmailService->addAdditionalEmails($users);

    $contactEmailAddresses = [];
    foreach ($users as $user) {
        $contactEmailAddresses[$user->getInitials()] = $user;
    }

    foreach ($ContactsToEmail as $Initials => $Records) {
        if ($contactEmailAddresses[$Initials]) {
            $user = $contactEmailAddresses[$Initials];
            $JSONdata['contacts'][] = ['email' => $user->getEmailAddress(), 'records' => $Records, 'language' => $user->getLanguage()];

            foreach ($user->getAdditionalEmails() as $additionalEmail) {
                $JSONdata['contacts'][] = ['email' => $additionalEmail, 'records' => $Records, 'language' => $user->getLanguage()];
            }
        }
    }

    // encode and return json data...
    echo json_encode($JSONdata);
}

function SendOverdueEmails()
{
    $Records = array_unique(explode('|', Sanitize::SanitizeString($_GET['records'])));

    $language = $_GET['language'] ?? null;

    if (!is_numeric($language)) {
        $language = LanguageSessionFactory::getInstance()->getLanguage();
    } else {
        $language = (int) $language;
    }

    $Result = SendOverdueEmailToContact(Sanitize::SanitizeEmail($_GET['email']), $Records, Sanitize::getModule($_GET['module']), $language);

    $JSONdata['html'] = '<div style="padding-bottom:5px">' . $Result . '</div>';
    $JSONdata['get'] = Sanitize::SanitizeStringArray($_GET);

    // encode and return json data...
    echo json_encode($JSONdata);
}

function SendOverdueEmailToContact($email, $records, $module, $language)
{
    global $scripturl;

    $data['overdue_list_html'] = '';

    sort($records, SORT_NUMERIC);

    foreach ($records as $recordid) {
        if ($module == 'INC') {
            $data['overdue_list_html'] .= $scripturl . '?action=incident&recordid=' . $recordid . '
';
        } elseif ($module == 'COM') {
            $data['overdue_list_html'] .= $scripturl . '?action=record&module=COM&recordid=' . $recordid . '
';
        } elseif ($module == 'MOR') {
            $data['overdue_list_html'] .= $scripturl . '?action=record&module=MOR&recordid=' . $recordid . '
';
        }
    }

    $data['overdue_num'] = count($records);
    Log::info('Sending Overdue notification e-mail to ' . $email . ' concerning ' . $data['overdue_num'] . ' overdue ' . _fdtk('INCNames') . '.');

    $emailSender = EmailSenderFactory::createEmailSender($module, 'Overdue', '', '', $language);
    $emailSender->addRecipientEmail($email);
    $Success = $emailSender->sendEmails($data);

    $moduleName = (new Module($module))->getPluralName();

    if (!$Success) {
        return '<div>Attempted unsuccessfully to send an email to ' . $email . ' concerning ' . $data['overdue_num'] . ' overdue ' . $moduleName . '.</div>';
    }

    return '<div>Sent an email to ' . $email . ' concerning ' . $data['overdue_num'] . ' overdue ' . $moduleName . '.</div>';
}

function getContactsToOverdueEmail($data, $users, $module)
{
    global $ModuleDefs;

    $SpecificContactsToEmail = [];

    $TypesToEmail = explode('|', $users);

    if (is_array($TypesToEmail) && !empty($TypesToEmail)) {
        foreach ($TypesToEmail as $Type) {
            switch ($Type) {
                case Notification::USER_MANAGER:
                    if ($data[$ModuleDefs[$module]['FIELD_NAMES']['MANAGER']]) {
                        $SpecificContactsToEmail[] = $data[$ModuleDefs[$module]['FIELD_NAMES']['MANAGER']];
                    }

                    break;
                case Notification::USER_HANDLER:
                case Notification::USER_REVIEWER:
                    if ($data[$ModuleDefs[$module]['FIELD_NAMES']['HANDLER']]) {
                        $SpecificContactsToEmail[] = $data[$ModuleDefs[$module]['FIELD_NAMES']['HANDLER']];
                    }

                    break;
                case Notification::USER_INVESTIGATOR:
                    if (!empty($data[$ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']])) {
                        foreach (explode(' ', $data[$ModuleDefs[$module]['FIELD_NAMES']['INVESTIGATORS']]) as $Investigator) {
                            $SpecificContactsToEmail[] = $Investigator;
                        }
                    }

                    break;
            }
        }
    }

    return array_unique($SpecificContactsToEmail);
}
