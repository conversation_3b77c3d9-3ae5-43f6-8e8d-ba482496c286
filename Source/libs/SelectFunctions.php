<?php

use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use app\services\carlton\i18n\I18nServiceFactory;
use src\formdesign\forms\service\FormDesignDefaultGlobalService;
use src\framework\query\QueryFactory;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\payments\model\payeefield\PayeeField;
use src\payments\model\respondentfield\RespondentField;
use src\reportfields\ReportFieldFilters;
use src\reportfields\ReportFieldLookup;
use src\reportfields\TermFilter;
use src\reports\model\report\Report;
use src\security\CompatEscaper;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\system\database\CodeFieldInterface;
use src\system\database\field\EquipmentField;
use src\system\database\field\MedicationField;
use src\system\database\FieldInterface;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;

/**
 * Gets content for Ajax code selection controls by retrieving info from database on-the-fly.
 *
 * Can be used to help resolve performance issues (mainly in IE) associated with
 * building code selection controls from Javascript arrays containing a large number of codes.
 */
function getSelectFunction()
{
    echo formatSelectFunctionOutput();
}

/**
 * Generates response output from data returned by select functions.
 *
 * Calls a specified function to retrieve the data, then creates either JSON (by default) or XML output.
 *
 * @return string $output the codes available for selection, in either JSON or XML format
 */
function formatSelectFunctionOutput()
{
    $codes = $_POST['function']();
    if ($_POST['method'] == 'xml') {
        $output = getInitialXML();
        if (array_key_exists('!ERROR!', $codes)) {
            // we are returning a custom error message
            $output .= '<error>' . $codes['!ERROR!'] . '</error>';
        } else {
            // return the codes
            foreach ($codes as $value => $description) {
                $output .= '<code>
                                <value>' . $value . '</value>
                                <description>' . $description . '</description>
                            </code>';
            }
        }
        $output .= '</codes>';
    } else {
        // we use json by default
        if ($_GET['responsetype'] == 'autocomplete') {
            if (empty($codes)) {
                $code['value'] = '!NOCODES!';
                $code['description'] = _fdtk('no_codes_available');
                $output[] = $code;
            } elseif (array_key_exists('!ERROR!', $codes)) {
                // we are returning a custom error message
                $output['!ERROR!'] = $codes['!ERROR!'];
            } else {
                // return the codes
                foreach ($codes as $value => $description) {
                    $code = [];
                    $code['value'] = $value;
                    $code['description'] = $description;
                    $output[] = $code;
                }
            }
        } else {
            // old code can be removed when all selectFunction fields are v11 dropdowns
            $output = getInitialJson();
            if (array_key_exists('!ERROR!', $codes)) {
                // we are returning a custom error message
                $output['!ERROR!'] = $codes['!ERROR!'];
            } else {
                // return the codes
                foreach ($codes as $value => $description) {
                    $code = [];
                    $code['value'] = $value;
                    $code['description'] = $description;
                    $output['code'][] = $code;
                }
            }
        }
        $output = json_encode($output);
    }

    return $output;
}

/**
 * Creates the initial XML for the contents of a code selection control.
 *
 * @return string $xml the initial XML
 */
function getInitialXML()
{
    $title = preg_replace('/&(?![A-Za-z]{0,7};)/u', '&amp;', $_POST['title']);
    $title = StripHTML($title);

    $xml = '<codes>';
    $xml .= '<title>' . $title . '</title>';
    $xml .= '<ctrlidx>' . $_POST['ctrlidx'] . '</ctrlidx>';

    if ($_POST['searchmode'] == 'true' || $_POST['multilistbox'] == 'true') {
        $xml .= '<multiple>multiple</multiple>';
    }

    if ($_POST['searchmode'] == 'true') {
        $xml .= '<search>search</search>';
    }

    if ($_POST['freetext'] == 'freetext') {
        $xml .= '<freetext>freetext</freetext>';
    }

    if ($_POST['searchmode'] != 'true' && $_POST['multilistbox'] != 'true') {
        $xml .= '<code><value/><description/></code>';
    }

    return $xml;
}

/**
 * Creates the initial JSON data for the contents of a code selection control.
 *
 * @return array $json the initial JSON data
 */
function getInitialJson()
{
    $title = preg_replace('/&(?![A-Za-z]{0,7};)/u', '&amp;', $_POST['title']);
    $title = StripHTML($title);

    $json = [];
    $json['title'] = $title;
    $json['ctrlidx'] = $_POST['ctrlidx'];

    if ($_POST['searchmode'] == 'true' || $_POST['multilistbox'] == 'true') {
        $json['multiple'] = true;
    }

    if ($_POST['searchmode'] == 'true') {
        $json['search'] = true;
    }

    if ($_POST['freetext'] == 'freetext') {
        $json['freetext'] = true;
    }

    if ($_POST['searchmode'] != 'true' && $_POST['multilistbox'] != 'true') {
        $json['code'][] = ['value' => '', 'description' => ''];
    }

    return $json;
}

/**
 * Creates a custom error which can be passed back to the code control.
 *
 * @param string $errorMessage the error message to be displayed
 *
 * @return array $error        the representation of the error
 */
function createCustomError($errorMessage)
{
    return ['!ERROR!' => $errorMessage];
}

/**
 * Gets a list of security groups not currently assigned to this profile/user.
 *
 * @return array $codes a list of security groups IDs/descriptions
 */
function getSecurityGroups()
{
    $current = '';
    $term = '';
    $excludeAdmin = '';
    $bindArray = [];

    if (isset($_POST['current']) && is_array($_POST['current'])) {
        $i = 0;
        $current = 'AND recordid NOT IN (';
        foreach ($_POST['current'] as $value) {
            $current .= ':current' . $i . ',';
            $bindArray['current' . $i] = $value;
            ++$i;
        }
        $current = \UnicodeString::substr($current, 0, -1) . ')';
    }

    if ($_POST['term']) {
        $term = 'AND grp_code LIKE :term';
        $bindArray['term'] = '%' . $_POST['term'] . '%';
    }

    $currentUser = (new UserSessionFactory())->create();
    if ($currentUser->isLocalAdmin() && !$currentUser->isFullAdmin()) {
        // A "Local Admin" only has limited permission to administer local users (sharing a location) and
        // cannot assign them to certain security groups.
        // The restricted security groups are defined in the global GROUPS_RESTRICTED_FROM_LOCAL_ADMINS.
        $adminIds = Container::get(Registry::class)->getParm('GROUPS_RESTRICTED_FROM_LOCAL_ADMINS')->toArray();
        if ($adminIds) {
            $i = 0;
            $excludeAdmin = ' AND recordid NOT IN (';
            foreach ($adminIds as $adminId) {
                $excludeAdmin .= ':adminId_' . $i . ',';
                $bindArray['adminId_' . $i] = $adminId;
                ++$i;
            }
            $excludeAdmin = \UnicodeString::substr($excludeAdmin, 0, -1) . ') ';
        }
    }

    if ($_POST['pfl_id']) {
        $sql = "SELECT recordid, grp_code
                FROM sec_groups
                WHERE recordid NOT IN (
                    SELECT lpg_group
                    FROM link_profile_group
                    WHERE lpg_profile = :lpg_profile)
                {$current}
                {$term}
                {$excludeAdmin}
                ORDER BY grp_code";
        $bindArray['lpg_profile'] = $_POST['pfl_id'];
    } elseif ($_POST['use_id']) {
        $sql = "SELECT recordid, grp_code
                FROM sec_groups
                WHERE recordid NOT IN (
                    SELECT grp_id
                    FROM sec_staff_group
                    WHERE use_id = :use_id)
                {$current}
                {$term}
                {$excludeAdmin}
                ORDER BY grp_code";
        $bindArray['use_id'] = $_POST['use_id'];
    } else { // no profile or contact specified - just return all groups
        $sql = "SELECT recordid, grp_code
                FROM sec_groups
                WHERE (1=1)
                {$current}
                {$term}
                {$excludeAdmin}
                ORDER BY grp_code";
    }

    $codes = DatixDBQuery::PDO_fetch_all($sql, $bindArray, PDO::FETCH_KEY_PAIR);
    $codes = array_map('htmlfriendly', $codes);

    if (empty($codes)) {
        $codes['!NOCODES!'] = 'No security groups available';
    }

    return $codes;
}

/**
 * Gets a list of managers for DIF1 'Your manager' field.
 *
 * @global array $DefaultValues
 * @global array $HideFields
 * @global array $ModuleDefs
 *
 * @return array $codes a list of managers' initials/names
 */
function getDIF1Managers()
{
    global $DefaultValues, $HideFields, $ModuleDefs;

    $formDesignDefaultGlobalService = Container::get(FormDesignDefaultGlobalService::class);

    $codes = [];

    // Form ID either comes through GET or is set in the session or by the default form design in the app.
    if (isset($_REQUEST['form_id']) && is_numeric($_REQUEST['form_id'])) {
        $FormID = $_REQUEST['form_id'];
        $_SESSION['form_id']['INC'][1] = $FormID;
    } elseif (isset($_SESSION['form_id']['INC'][1]) && is_numeric($_SESSION['form_id']['INC'][1])) {
        $FormID = $_SESSION['form_id']['INC'][1];
    } else {
        $FormID = $formDesignDefaultGlobalService->getDefault(Module::INCIDENTS, 1);
    }

    // This code is repeated a lot.  Maybe needs a function
    IncludeCurrentFormDesign('INC', 1, $FormID);

    $today = getdate();
    $xml = getInitialXML();

    $Where = ["use_email IS NOT NULL AND con.login IS NOT NULL AND use_dclosed is null AND initials IS NOT NULL AND initials != ''  AND (use_staff_include IS NULL OR use_staff_include != 'N')"];

    $sql = GetContactListSQLByAccessLevel([
        'module' => 'INC',
        'where' => $Where,
        'levels' => ['DIF2', 'RM'],
    ]);

    $result = DatixDBQuery::PDO_fetch_all($sql);
    foreach ($result as $row) {
        $ManagerName = FormatUserNameForList(['data' => $row]);
        if ($_POST['term'] == ''
            || \UnicodeString::stripos($ManagerName, $_POST['term']) !== false) {
            $codes[$row['initials']] = htmlfriendly($ManagerName);
        }
    }

    return $codes;
}

/**
 * Retrieves a list of e-mail templates for a given module.
 *
 * @return array $codes a list of e-mail templates
 */
function getEmailTemplates()
{
    $codes = [];
    $params = [];

    $sql = 'SELECT recordid, emt_name FROM email_templates WHERE emt_module = :module';
    $params['module'] = $_POST['module'];

    if ($_POST['term']) {
        $sql .= ' AND emt_name LIKE :term';
        $params['term'] = '%' . $_POST['term'] . '%';
    }

    $sql .= ' ORDER BY emt_name';
    $codes = DatixDBQuery::PDO_fetch_all($sql, $params, PDO::FETCH_KEY_PAIR);

    if (empty($codes)) {
        $codes = ['!NOCODES!' => 'No templates available'];
    }

    return $codes;
}

/**
 * Retrieves codes for the consequence field on the standard grading section of the Risk form.
 *
 * @return array the code array
 */
function getRamConsequence()
{
    $sql = 'SELECT code, description FROM ' . (bYN(GetParm('RISK_MATRIX', 'N')) ? 'code_inc_conseq' : 'code_ra_conseq') . ' ORDER BY listorder';

    return DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_KEY_PAIR);
}

/**
 * Retrieves codes for the likelihood field on the standard grading section of the Risk form.
 *
 * @return array the code array
 */
function getRamLikelihood()
{
    $sql = 'SELECT code, description FROM ' . (bYN(GetParm('RISK_MATRIX', 'N')) ? 'code_inc_likeli' : 'code_ra_likeli') . ' ORDER BY listorder';

    return DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_KEY_PAIR);
}

/**
 * Returns a list of months for the ISD export.
 *
 * @return array
 */
function getIsdMonth()
{
    return array_combine(
        ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
        ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
    );
}

/**
 * Returns a list of years (from 5 years previous to 10 years ahead) for the ISD export.
 *
 * @return array
 */
function getIsdYear()
{
    return array_combine(range(date('Y') - 5, date('Y') + 10), range(date('Y') - 5, date('Y') + 10));
}

/**
 * Returns a list of actions chains that can be assigned to a record.
 *
 * @return array $chains
 */
function getActionChains()
{
    $where[] = 'ach_module IN (:module, \'ALL\')';
    $PDOParams['module'] = $_POST['module'];
    if ($_POST['term']) {
        $where[] = 'ach_title LIKE :term';
        $PDOParams['term'] = '%' . $_POST['term'] . '%';
    }
    $sql = 'SELECT recordid, ach_title FROM action_chains WHERE ' . implode(' AND ', $where) . ' AND ach_archive = 0 ORDER BY ach_title';
    $result = DatixDBQuery::PDO_fetch_all($sql, $PDOParams);

    foreach ($result as $chain) {
        $chains[$chain['recordid']] = htmlfriendly($chain['ach_title']);
    }

    return $chains;
}

/**
 * Retrieves a list of fields that can be selected for a given table on the report designer.
 *
 * @return array
 */
function getNewReportFields()
{
    global $ModuleDefs;

    [$rawFieldset, $contactType] = explode('-', $_POST['tableValues'][$_POST['table']] ?? '');
    $reportType = $_POST['reportType']['type'];
    $module = $_POST['module'];
    $showExcluded = $_POST['showExcluded'] ?? '0';
    $context = $_POST['context'];

    if ($rawFieldset === '' && $reportType != Report::TRAFFIC) {
        return createCustomError(_fdtk('reports_no_table_error'));
    }
    $fieldset = ($rawFieldset === '') ? null : (int) $rawFieldset;

    $queryFactory = new src\framework\query\QueryFactory();
    $query = $queryFactory->getQuery();

    if ($reportType == Report::TRAFFIC) {
        $where = $queryFactory->getWhere();
        $where->add($queryFactory->getFieldCollection()->field('code')->in($ModuleDefs[$module]['TRAFFICLIGHTS_FIELDS']));
        $query->where($where);
    } elseif ($reportType != Report::LISTING) {
        $where = $queryFactory->getWhere();
        $where->add(
            'OR',
            $queryFactory->getFieldCollection()->field('data_type')->in(FieldInterface::GRAPHICAL_REPORT_DATA_TYPES),
            $queryFactory->getFieldCollection()->field('code')->in(FieldInterface::GRAPHICAL_REPORT_ADDITIONAL_REPORTABLE_FIELDS),
        );
        $query->where($where);
    }

    $code_list = (new src\reports\model\field\ReportField($fieldset, $module, false, ($showExcluded == '1' && $context == 'administration') ? true : false))->getCodes($query);

    $i18nService = (new I18nServiceFactory())->create();
    $locale = LanguageSessionFactory::getInstance()->getLocale();

    $fieldDefs = Container::get(Registry::class)->getFieldDefs();

    $codes = [];
    foreach ($code_list as $code) {
        $field = $fieldDefs[$code->code];
        if ($field instanceof CodeFieldInterface && !$field->supportsi18n() && $field->getLabel() !== '') {
            switch ($fieldset) {
                case MedicationField::ADMINISTERED_INCIDENT_FIELDSET:
                    // Intentional fallthrough
                case MedicationField::ADMINISTERED_FEEDBACK_FIELDSET:
                    $placeholder = sprintf($field->getLabel(), 'ADMINISTERED');
                    $translation = $i18nService->getTranslation($placeholder, 'medications', $locale);

                    break;
                case MedicationField::INTENDED_INCIDENT_FIELDSET:
                    // Intentional fallthrough
                case MedicationField::INTENDED_FEEDBACK_FIELDSET:
                    $placeholder = sprintf($field->getLabel(), 'INTENDED');
                    $translation = $i18nService->getTranslation($placeholder, 'medications', $locale);

                    break;
                case EquipmentField::EQUIPMENT_FIELDSET:
                    $translation = $i18nService->getTranslation($field->getLabel(), 'equipments', $locale);

                    break;
            }

            if (isset($translation)) {
                $codes[$code->code] = CompatEscaper::encodeCharacters($translation);
            }
        }

        if (!isset($codes[$code->code])) {
            $codes[$code->code] = CompatEscaper::encodeCharacters($code->description);
        }
    }

    $filters = new ReportFieldFilters([new TermFilter($_POST['term'])]);

    $reportFieldsLookup = new ReportFieldLookup($filters, LanguageSessionFactory::getInstance()->getLocale());
    $reportFieldsLookup->addCodes($codes);

    return $reportFieldsLookup->getCodes();
}

/**
 * Retrieves a list of fields that can be selected for drilling down on a crosstab.
 *
 * @return array
 */
function getDrilldownFields()
{
    global $ModuleDefs;

    $module = $_POST['module'];
    $queryFactory = new src\framework\query\QueryFactory();
    $query = $queryFactory->getQuery();
    $where = $queryFactory->getWhere();

    if ($_POST['term'] != '') {
        $where->add($queryFactory->getFieldCollection()->field('description')->like('%' . $_POST['term'] . '%'));
    }

    // Excludes fields used to design a crosstab from the drill down select box
    if ($_POST['rowsField'] != '' && $_POST['columnsField'] != '') {
        $where->add($queryFactory->getFieldCollection()->field('code')->notIn([$_POST['rowsField'], $_POST['columnsField']]));
    }

    $where->add($queryFactory->getFieldCollection()->field('data_type')->in(['C', 'Y', 'T']));
    $query->where($where);

    foreach ((new src\reports\model\field\ReportField(0, $module))->getCodes($query) as $code) {
        $codes[$code->code] = CompatEscaper::encodeCharacters($code->description);
    }

    return $codes;
}

/**
 * Retrieves a list of respondents for a payment.
 *
 * @return array
 */
function getRespondentsForPayment()
{
    $mainRecordId = (int) $_POST['main_recordid'];
    $mainModule = $_POST['main_module'];

    $module = Container::get(ModuleDefs::class)[$mainModule] ?? null;
    if ($module === null) {
        return [];
    }

    $fk = $module->getForeignKey();

    $queryFactory = new src\framework\query\QueryFactory();
    [$query, $where, $fieldCollection] = $queryFactory->getQueryObjects();

    if ($mainRecordId > 0) {
        // We're providing a list of respondents linked to a specific record in order to add to a payment
        $fieldCollection->field($fk)->eq($mainRecordId);
    } else {
        // There's no main recordid in a search context, so we provide a list of all possible respondents to search on
        $fieldCollection->field($fk)->notNull();
    }

    if ($_POST['term'] != '') {
        $fieldCollection->field('name')->like('%' . $_POST['term'] . '%');
    }

    $where->add($fieldCollection);
    $query->where($where);

    $field = new RespondentField();
    $limitResults = Container::get(Registry::class)->getParm('CODED_FIELD_CODE_DISPLAY_LIMIT', 500)->toScalar();

    if ($limitResults > 0) {
        $field->setLimitResults($limitResults);
    }

    $code_list = $field->getCodes($query);
    $codes = [];

    foreach ($code_list as $code) {
        $codes[$code->code] = CompatEscaper::encodeCharacters($code->description);
    }

    return $codes;
}

function getPayeesForPayment()
{
    $mainId = $_POST['main_id'];
    $mainModule = $_POST['main_module'];

    if (!isset($mainId) || !isset($mainModule)) {
        return [];
    }
    $fk = Container::get(Registry::class)->getModuleDefs()[$mainModule]['FK'];

    $queryFactory = new src\framework\query\QueryFactory();
    $query = $queryFactory->getQuery();

    // Search has no id parameter so search query should have no where clause - BD 14/1/2019
    if ($_POST['form_type'] != 'Search') {
        $query->where(['link_contacts.' . $fk => $mainId]);
    }

    $activeWhere = $queryFactory->getWhere();

    if ($_POST['term'] != '') {
        $activeWhere->add($queryFactory->getFieldCollection()->field('fullname')->like('%' . $_POST['term'] . '%'));
    } else {
        $activeWhere->add($queryFactory->getFieldCollection()->field('fullname')->notEmpty()->notNull());
    }
    $query->where($activeWhere);

    $field = new PayeeField();
    $limitResults = Container::get(Registry::class)->getParm('CODED_FIELD_CODE_DISPLAY_LIMIT', 500)->toScalar();

    if ($limitResults > 0) {
        $field->setLimitResults($limitResults);
    }

    $code_list = $field->getCodes($query);

    foreach ($code_list as $code) {
        $codes[$code->code] = CompatEscaper::encodeCharacters($code->description);
    }

    return $codes;
}
// Selects the approval status with english.php codes for generating records from other modules
function getRelevantApprovalStatus()
{
    if (empty($_POST['parent_value'][0])) {
        $codes = createCustomError(_fdtk('generate_select_module'));
    } else {
        $codesList = GetLevelsTo($_POST['parent_value'][0], GetAccessLevel($_POST['parent_value'][0]), 'NEW');
        $codes = [];
        foreach ($codesList as $code => $desc) {
            $codes = array_merge($codes, [$code => $desc['description']]);
        }
    }
    if (empty($codes)) {
        $codes = ['!NOCODES!' => _fdtk('generate_select_module_no_perms')];
    }

    return $codes;
}

function getMapCodeFieldsList()
{
    global $ModuleDefs;

    $module = $_POST['module']['module'];

    $queryFactory = new src\framework\query\QueryFactory();
    $query = $queryFactory->getQuery();
    $where = $queryFactory->getWhere();

    if ($module != '') {
        $setupTableList = [];

        $setupModuleList = [$module];

        foreach ($setupModuleList as $setupModule) {
            $setupTableList[] = $ModuleDefs[$setupModule]['TABLE'];
        }

        $where->add($queryFactory->getFieldCollection()->field('fdr_table')->in($setupTableList));
    }

    $where->add($queryFactory->getFieldCollection()->field('fdr_data_type')->in(['C', 'T']));

    if ($_POST['term'] != '') {
        $where->add($queryFactory->getFieldCollection()->field('COALESCE(relabelling.field_label, fdr_label)')->like('%' . $_POST['term'] . '%'));
    }

    // Filter approval status and a dummy field from the results
    $where->add($queryFactory->getFieldCollection()->field('fdr_name')->notIn(['rep_approved', 'initial_current']));

    $coalesce = 'COALESCE(relabelling.field_label, fdr_label) AS description';

    $query->select(['fdr_name AS code', $coalesce])
        ->from('field_directory')
        ->join('relabelling', 'field_directory.id = relabelling.field_directory_id', 'LEFT')
        ->where($where)
        ->orderBy(['description']);

    $writer = $queryFactory->getSqlWriter();
    [$sql, $parameters] = $writer->writeStatement($query);

    $rawQuery = $writer->combineSqlAndParameters($sql, $parameters);

    // This is going to be used to key the SESSION array to enable us to have multiple queries cached
    $keyedBy = '_' . $_POST['term'] . '_' . $_POST['module'];

    /*
     * If we are fetching the same results return them from the cache instead if querying the DB again
     * (Massive performance gains)
     */
    if (isset($_SESSION['MapCodeFieldsList::rawQuery' . $keyedBy], $_SESSION['MapCodeFieldsList::codes' . $keyedBy]) && $_SESSION['MapCodeFieldsList::rawQuery' . $keyedBy] === $rawQuery) {
        return $_SESSION['MapCodeFieldsList::codes' . $keyedBy];
    }

    $codes = DatixDBQuery::PDO_fetch_all($sql, $parameters, PDO::FETCH_KEY_PAIR);

    // Cache rawQuery and codes array to use it next time if we are fetching the same results from the DB
    $_SESSION['MapCodeFieldsList::rawQuery' . $keyedBy] = $rawQuery;
    $_SESSION['MapCodeFieldsList::codes' . $keyedBy] = $codes;

    return $codes;
}

/**
 * @return array<string, string>
 */
function getMedicationsDrugSearchByField(): array
{
    $fieldName = $_POST['medicationsFieldName'];
    $term = $_POST['term'];
    if (!in_array($fieldName, ['med_name', 'med_brand', 'med_manufacturer', 'med_strength', 'med_supplier', 'med_class'])) {
        return ['!NOCODES!' => 'Invalid field name: ' . $fieldName];
    }

    $queryFactory = Container::get(QueryFactory::class);
    $query = $queryFactory->getQuery()
        ->select([$fieldName])
        ->from(Tables::MEDS_MAIN)
        ->groupBy([$fieldName])
        ->orderBy([[$fieldName, 'ASC']])
        ->limit(
            0,
            Container::get(Registry::class)
                ->getParm('CODED_FIELD_CODE_DISPLAY_LIMIT')
                ->toInt() ?: 500,
        );

    $where = $queryFactory->getWhere();

    if (!empty($term)) {
        $where->add(
            $queryFactory->getFieldCollection()
                ->field($fieldName)
                ->like('%' . $term . '%'),
        );
    } else {
        $where->add(
            $queryFactory->getFieldCollection()
                ->field($fieldName)
                ->notEmpty(),
        );
    }

    $query->where($where);

    $writer = $queryFactory->getSqlWriter();
    [$sql, $parameters] = $writer->writeStatement($query);

    $codeRows = DatixDBQuery::PDO_fetch_all($sql, $parameters, PDO::FETCH_ASSOC);

    if (!$codeRows) {
        return ['!NOCODES!' => _fdtk('no-codes')];
    }

    $codes = [];
    foreach ($codeRows as $codeRow) {
        $code = $codeRow[$fieldName];
        $codes[Escaper::escapeForHTMLParameter($code)] = $code;
    }

    return $codes;
}
