<?php

namespace Source\libs;

use app\Exceptions\Attachments\AttachmentPermissionDeniedException;
use app\models\document\valueObjects\DownloadInfo;
use app\services\document\DocumentDownloadService;
use app\services\document\MissingDocumentFlashMessageBuilder;
use app\services\document\UserDocumentPermissions;
use Aws\S3\Exception\S3Exception;
use DateTime;
use DateTimeImmutable;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityNotFoundException;
use Exception;
use FileNotFoundException;
use src\logger\Facade\Log;
use src\system\container\facade\Container;
use Throwable;

use function array_key_exists;

use const ENT_QUOTES;
use const ENT_IGNORE;

class EmailHistoryAttachments
{
    private MissingDocumentFlashMessageBuilder $missingDocumentFlashMessage;
    private Connection $db;

    /** @readonly */
    private DateTimeImmutable $startOfTodayDateTime;

    public function __construct(MissingDocumentFlashMessageBuilder $missingDocumentFlashMessage, Connection $db)
    {
        $this->missingDocumentFlashMessage = $missingDocumentFlashMessage;
        $this->db = $db;
        $this->startOfTodayDateTime = (new DateTimeImmutable())->setTime(0, 0);
    }

    /**
     * Create html list items of all uploaded documents.
     *
     * @param array $documents list of all upload documents
     *
     * @throws Exception
     */
    public function buildAttachmentListItemsHtml(array $documents): string
    {
        $listElement = '<ul>';

        foreach ($documents as $document) {
            $documentInfo = $this->getDocumentInfo($document);

            $listElement .= '<li
                class="field_extra_text_ms2_li_fbk_attachments"
                data-key="' . $document['id'] . '" .
                data-val="' . htmlspecialchars($documentInfo['description'], ENT_QUOTES | ENT_IGNORE, 'utf-8') . '" .
                data-size="' . $documentInfo['fileSize'] . '"
                >' . htmlspecialchars($documentInfo['description'], ENT_QUOTES | ENT_IGNORE, 'utf-8') .
            '</li>';
        }

        return $listElement . '</ul>';
    }

    /**
     * @return array[] a list of arrays containing the file id & file size of an attachment
     *
     * @psalm-return list<array{
     *  id:numeric-string,
     *  size:0:positive-int
     * }>
     */
    public function buildAttachmentJSON(array $documents): array
    {
        $json = [];

        foreach ($documents as $document) {
            $documentInfo = $this->getDocumentInfo($document);

            $json[] = [
                'id' => (string) $document['id'],
                'size' => $documentInfo['fileSize'],
            ];
        }

        return $json;
    }

    /**
     * Creates a list of document codes used to populate the custom code list.
     *
     * @param array $documents list of all upload documents
     *
     * @throws Exception
     */
    public function fetchDocumentCodes(array $documents): array
    {
        $documentCodes = [];

        foreach ($documents as $document) {
            $documentInfo = $this->getDocumentInfo($document);

            $documentCodes[$document['id']] = $documentInfo['description'];
        }

        return $documentCodes;
    }

    /**
     * Fetches the file size of the document and creates the description include formatted file size
     * For performance purposes will not always connect to the S3 storage, where possible uses locally
     * stored details but ensures that a check against S3 is made at least once a day to
     * update the is_missing field.
     *
     * @param array $document contains information about the uploaded document
     *
     * @throws Throwable
     * @throws S3Exception
     */
    private function getDocumentInfo(array $document): array
    {
        // cache used as when rendering form multiple calls may be made for the same document
        static $fileSizeCache = [];

        $documentId = $document['documentId'];
        $fileSizeFromDB = $document['file_size'] ?? null;
        $fileSizeFromS3 = null;
        $description = empty($document['description']) ? null : $document['description'];

        $inCacheSoAlreadyChecked = array_key_exists($documentId, $fileSizeCache);

        if ($fileSizeFromDB === null && $inCacheSoAlreadyChecked) {
            $fileSizeFromDB = $fileSizeCache[$documentId];

            // if in cache and null filesize, this is a second call to this method for
            // the same document - we already know it is missing. We cannot confirm using
            // $document['is_missing'] as this may be outdated and set before the first check.
            if ($fileSizeFromDB === null) {
                $this->missingDocumentFlashMessage->sendMissingDocumentFlashMessage();

                return [
                    'description' => $description . ' (' . _fdtk('view_missing_documents_missing_column_title') . ')',
                ];
            }
        }

        if ($description === null) {
            // when SPSC_DATASET is enabled there is no description field when adding documents
            // use original_filename instead
            $description = ($document['original_filename'] ?? null);
        }

        $userDocumentPermissions = Container::get(UserDocumentPermissions::class);
        $canUserAccessDocumentType = $userDocumentPermissions->canUserAccessDocumentType($document['type_code'], $document['owner_id']);

        if (!$canUserAccessDocumentType) {
            return [
                'description' => $description,
            ];
        }

        // always check if missing status is unknown (null) or flagged as missing in case it has been re-uploaded
        $checkIfMissing = ($inCacheSoAlreadyChecked === false && $document['is_missing'] !== '0');

        // always check if not yet checked today
        if ($document['last_missing_checked_on'] === null) {
            $checkForToday = true;
        } else {
            $lastCheckedDate = new DateTime($document['last_missing_checked_on']);
            $checkForToday = ($lastCheckedDate < $this->startOfTodayDateTime);
        }

        // for performance, only connect to service (S3) if necessary/making check
        if ((($fileSizeFromDB === null || $checkForToday) && $inCacheSoAlreadyChecked === false) || $checkIfMissing) {
            $fileInfo = $this->getDocumentInfoFromDocumentLink($document['id'], $documentId);
            if ($fileInfo === null) {
                $this->missingDocumentFlashMessage->sendMissingDocumentFlashMessage();
                $fileSizeCache[$documentId] = null;
                $this->updateDocumentFileSize($documentId, null);

                return [
                    'description' => $description . ' (' . _fdtk('view_missing_documents_missing_column_title') . ')',
                ];
            }

            if ($fileSizeFromDB === null) {
                $fileSizeFromS3 = $fileInfo->getFileSize();
                $this->updateDocumentFileSize($documentId, $fileSizeFromS3);
            }
        } elseif ($document['is_missing']) {
            // if not checking service and known to be missing - trigger flash message
            $this->missingDocumentFlashMessage->sendMissingDocumentFlashMessage();

            return [
                'description' => $description . ' (' . _fdtk('view_missing_documents_missing_column_title') . ')',
            ];
        }

        $filesizeToReturn = $fileSizeFromDB ?: $fileSizeFromS3 ?: 0;
        $fileSizeCache[$documentId] = $filesizeToReturn;

        return [
            'description' => $description . ' - (' . $this->formatFilesize($filesizeToReturn) . ')',
            'fileSize' => $filesizeToReturn,
        ];
    }

    private function getDocumentInfoFromDocumentLink(int $linkdocumentId, int $documentId): ?DownloadInfo
    {
        $downloadService = Container::get(DocumentDownloadService::class);

        try {
            $fileInfo = $downloadService->getDownloadInfoFromDocumentLink($linkdocumentId);
            $this->setDocumentIsMissingFlag($documentId, false);

            return $fileInfo;
        } catch (S3Exception $exception) {
            if (($exception->getResponse() !== null) && $exception->getResponse()->getStatusCode() === 404) {
                $this->setDocumentIsMissingFlag($documentId, true);
                $this->missingDocumentFlashMessage->sendMissingDocumentFlashMessage();
            } else {
                throw $exception;
            }
        } catch (FileNotFoundException|EntityNotFoundException $fileNotFound) {
            $this->setDocumentIsMissingFlag($documentId, true);
            $this->missingDocumentFlashMessage->sendMissingDocumentFlashMessage();
        } catch (AttachmentPermissionDeniedException) {
            Log::info('User does not have access to the linked attachment.', [
                'documentId' => $documentId,
                'linkDocumentId' => $linkdocumentId,
            ]);
        }

        return null;
    }

    private function setDocumentIsMissingFlag(int $documentId, bool $isMissing): void
    {
        $this->db->update(
            'documents',
            [
                'is_missing' => $isMissing,
                'last_missing_checked_on' => (new DateTime())->format('Y-m-d H:i:s'),
            ],
            ['id' => $documentId],
        );
    }

    /**
     * Convert number of bytes into a readable MB format.
     */
    private function formatFilesize(int $fileSize): string
    {
        return (float) number_format($fileSize / 1000000, 2) . 'MB';
    }

    private function updateDocumentFileSize(int $documentId, ?int $fileSize): void
    {
        $this->db->update('documents', ['file_size' => $fileSize], ['id' => $documentId]);
    }
}
