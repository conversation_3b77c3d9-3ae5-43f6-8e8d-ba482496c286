<?php

function CheckLock()
{
    $SQL = 'SELECT lck_active FROM record_locks WHERE lck_sessionid = :session_id';

    $data = DatixDBQuery::PDO_fetch($SQL, ['session_id' => $_SESSION['session_id']]);

    $JSONdata = [
        'success' => true,
        'lock_active' => ($data['lck_active'] ?? 'N') === 'Y',
    ];

    echo json_encode($JSONdata);
}

function CheckLockRecord()
{
    $recordArray = ['incident', 'risk', 'record', 'standard', 'element', 'prompt', 'editcontact', 'action', 'asset'];

    if (in_array($_GET['action'] ?? '', $recordArray)) {
        return true;
    }

    return false;
}

function ResetTimer()
{
    if ($_GET['timertype'] == 'SES') {
        DatixDBQuery::CallStoredProcedure([
            'procedure' => 'SetSessionActive',
            'parameters' => [
                ['@session_id', $_SESSION['session_id'], 'SQLINT4'],
                ['@lock_id', null, 'SQLINT4'],
            ],
        ]);
    } else {
        DatixDBQuery::CallStoredProcedure([
            'procedure' => 'SetSessionActive',
            'parameters' => [
                ['@session_id', null, 'SQLINT4'],
                ['@lock_id', $_GET['id'], 'SQLINT4'],
            ],
        ]);
    }
}

function UnlockRecord_ajax()
{
    UnlockRecord($_GET);
}

function UnlockRecord($aParams)
{
    if (($aParams['table'] && $aParams['link_id']) || $aParams['lock_id']) {
        if (!($aParams['lock_id'] ?? false)) {
            $params = [
                'lck_table' => $aParams['table'],
                'lck_linkid' => $aParams['link_id'],
                'lck_sessionid' => $_SESSION['session_id'],
            ];
            $sSQL = 'SELECT recordid FROM record_locks';
            $sSQL .= ' WHERE lck_table = :lck_table';
            $sSQL .= ' AND lck_linkid = :lck_linkid';
            $sSQL .= ' AND lck_sessionid= :lck_sessionid';

            $row = DatixDBQuery::PDO_fetch($sSQL, $params);

            $aParams['lock_id'] = $row ? $row['recordid'] : null;
        }

        DatixDBQuery::CallStoredProcedure(
            [
                'procedure' => 'UnlockRecord',
                'parameters' => [
                    ['@session_id', $_SESSION['session_id'], 'SQLINT4'],
                    ['@lock_id', $aParams['lock_id'], 'SQLINT4'],
                ],
            ],
        );
    }
}

function LockRecord($aParams)
{
    $oResult = DatixDBQuery::CallStoredProcedure(
        [
            'procedure' => 'LockRecord',
            'parameters' => [
                ['@link_id', $aParams['link_id'], 'SQLINT4'],
                ['@table', $aParams['table'], 'SQLVARCHAR'],
                ['@session_id', $_SESSION['session_id'], 'SQLINT4'],
            ],
        ],
    );

    // process the resulting queries with the sp
    if (is_array($oResult) && count($oResult) > 2) {
        $res_offset = count($oResult) - 2;
        $data_offset = count($oResult) - 1;

        if ($oResult[$res_offset][0] === '0') {
            $_SESSION['Record_Locks']['Current_Lock'] = Sanitize::SanitizeString($oResult[$data_offset][0]);
        } else {
            $aParams['lock_message'] = sprintf(_fdtk('record_lock_message'), Sanitize::SanitizeString($oResult[$data_offset][0]));
            $aParams['formtype'] = 'Locked';
        }
    }

    return $aParams;
}
