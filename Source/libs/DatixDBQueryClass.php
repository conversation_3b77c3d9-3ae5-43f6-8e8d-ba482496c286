<?php

use app\framework\DBALConnectionFactory;
use app\models\framework\config\DatixConfig;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\system\container\facade\Container;

/**
 * @desc Wraps PDO classes, allowing us to perform our own SQL logging/error catching.
 */
class DatixDBQuery
{
    /** @var PDO */
    public $PDO;

    /** @var PDOStatement */
    public $PDOStatement;

    /** @var string */
    public $sql;

    /**
     * The actual query run (i.e. when all parameters have been bound).
     *
     * @var string
     */
    protected $rawQuery;
    private DatixConfig $datixConfig;

    /**
     * Constructor for DatixDBQuery class.
     *
     * Sets up global PDO connection object if it doesn't already exist and assigns SQL string to member variable.
     *
     * @param string $sql The SQL string
     */
    public function __construct($sql = '')
    {
        static $PDO;

        $this->datixConfig = Container::get(DatixConfig::class);

        // Needed because we're including subs.php under /api/. Once that is cleaned up, this can be removed.
        $dbal = (new DBALConnectionFactory())->getInstance();

        if (empty($PDO)) {
            $PDO = Container::get(PDO::class);
            // ensure SQL SERVER text fields aren't truncated to 4096 bytes
            $PDO->query('SET TEXTSIZE 2147483647');
        }

        $this->PDO = $PDO;
        $this->sql = $sql;
        $this->datixConfig = Container::get(DatixConfig::class);
    }

    public function __sleep()
    {
        unset($this->PDO);
    }

    public function __wakeup()
    {
        $this->__construct($this->sql);
    }

    /**
     * @desc Wrapper for PDO::prepare. Takes the sql statement stores in the $sql variable and stores the
     * resulting PDOStatement object in the variable $PDOStatement
     */
    public function prepare()
    {
        $this->PDOStatement = $this->PDO->prepare($this->sql);
    }

    /**
     * @desc allows you bind a single params to the PDO query
     * useful for when executing stored proceedures, also provides custom conversion from MSSQL types to PDO
     */
    public function bind($parameter, &$variable, $data_type = null, $length = null)
    {
        $this->PDOStatement->bindParam($parameter, $variable, $data_type, $length);
    }

    /**
     * @desc execute a stored proceedure to retrieve results values
     * need to be consumed by iteration on the new sqlsrv driver.
     * Data bindings must be by using $this->bind and cannot be passed as options
     *
     * @return bool true if stored proceedure executed successfully
     */
    public function executeStoredProcedure()
    {
        $res = $this->PDOStatement->execute();

        // You MUST consume to receive the result set using the sqlsrv driver (stupid huh!)
        if ($res) {
            $rows = [];
            do {
                if ($this->PDOStatement->columnCount() > 0) {
                    $rows[] = $this->PDOStatement->fetchAll(PDO::FETCH_COLUMN);
                } else {
                    $rows[] = [];
                }
            } while ($this->PDOStatement->nextRowset());

            return $rows;
        }

        return $res;
    }

    /**
     * @desc Wrapper for PDOStatement:execute - includes normal datix error-catching code
     *
     * @param array $options replacements for keys within the sql statement provided as kay-value pairs
     *
     * @return bool result of the sql query - true if successful, false if an error occurred
     *
     * @throws DatixDBQueryException
     */
    public function execute(array $options = [], $suppress_errors = false, $suppress_logging = false): bool
    {
        // FIXME $suppress_logging is now obsolete and can be removed, but has a lot of usages to troll through
        global $dtxdebug;

        $this->setRawQuery($options);

        if ($this->datixConfig->queryLoggingEnabled()) {
            Log::debug('SQL query log.', [
                'query' => $this->PDOStatement->queryString,
                'parameters' => $options,
            ]);
        }

        $MaxRetries = GetParm('INSERT_RETRY', '100');
        $Retry = 0;
        $result = false;
        $start = microtime(true);

        while (!$result && $Retry <= $MaxRetries) {   // Work around if deadlock occurs. System will re-try a re-execute SQL statements.
            if (!$result = $this->PDOStatement->execute($options)) {
                if (!$suppress_errors && $Retry >= $MaxRetries) { // Query has failed and we've run out of retries
                    $this->handleSqlError($options);
                }
            }
            ++$Retry;
        }

        $end = microtime(true) - $start;

        if ($end > 5 && !$suppress_logging) {
            Log::info('Long-running SQL query', [
                'duration' => $end,
                'query' => $this->PDOStatement->queryString,
                'parameters' => $options,
            ]);
        }

        return $result;
    }

    /**
     * @desc Shortcut when no further action is needed between preparing and executing - this function
     * performs both functions, taking the same parameter and returning the same result as execute().
     *
     * @param array $options replacements for keys within the sql statement provided as kay-value pairs
     *
     * @return bool result of the sql query - true if successful, false if an error occurred
     *
     * @throws DatixDBQueryException
     */
    public function prepareAndExecute(array $options = [], $suppress_logging = false)
    {
        $this->prepare();

        return $this->execute($options, false, $suppress_logging);
    }

    /**
     * @desc Gets the next result from the most recently executed query.
     *
     * @return array An array of the values selected from the database. Array format determined by $fetchStyle parameter.
     */
    public function fetch($fetchStyle = PDO::FETCH_ASSOC)
    // $result_type:
    // MSSQL_ASSOC - stores the data in the associative indices of the result array
    // MSSQL_NUM - stores the data in the numeric indices of the result array
    // MSSQL_BOTH - stores the data both in the numeric and the associative indices of the result array
    {
        return $this->PDOStatement->fetch($fetchStyle);
    }

    /**
     * @desc Gets all results from the most recently executed query.
     *
     * @return array An array of the values selected from the database. Array format determined by $fetchStyle parameter.
     */
    public function fetchAll($fetchStyle = PDO::FETCH_ASSOC)
    // $result_type:
    // MSSQL_ASSOC - stores the data in the associative indices of the result array
    // MSSQL_NUM - stores the data in the numeric indices of the result array
    // MSSQL_BOTH - stores the data both in the numeric and the associative indices of the result array
    {
        return $this->PDOStatement->fetchAll($fetchStyle);
    }

    /**
     * Gets the number of rows affected by the last SQL statment.
     *
     * @return int number of rows affected
     */
    public function getRowsAffected()
    {
        return $this->PDOStatement->rowCount();
    }

    /**
     * Inserts a record into an identity table.
     *
     * @param array $options replacements for keys within the sql statement provided as kay-value pairs
     *
     * @return ?int $id the ID of the inserted record
     */
    public function insert(array $options = []): ?int
    {
        $this->sql .= ';SELECT SCOPE_IDENTITY()';

        $this->prepareAndExecute($options);

        $this->PDOStatement->nextRowset();

        if ($this->PDOStatement->columnCount() === 0) {
            // When the table doesn't have an identity column
            return null;
        }

        return (int) $this->fetch(PDO::FETCH_COLUMN);
    }

    /**
     * Begins a transaction.
     */
    public function beginTransaction()
    {
        if (!$this->PDO->inTransaction) {
            $this->PDO->inTransaction = $this->PDO->beginTransaction();
        }
    }

    /**
     * Rolls back a transaction.
     */
    public function rollBack()
    {
        if ($this->PDO->inTransaction) {
            $this->PDO->rollback();
            $this->PDO->inTransaction = false;
        }
    }

    /**
     * Commits a transaction.
     */
    public function commit()
    {
        if ($this->PDO->inTransaction) {
            $this->PDO->commit();
            $this->PDO->inTransaction = false;
        }
    }

    /**
     * Setter for the SQL query.
     */
    public function setSQL($sql): void
    {
        $this->sql = $sql;
    }

    /**
     * Creates a DatixDBQuery object and prepares and executes SQL passed to it. Replacement for db_query().
     *
     * @param string $sql SQL statement
     * @param array $PDOParamsArray array containing key/values for SQL variables
     *
     * @return bool Successful execution
     */
    public static function PDO_query($sql, $PDOParamsArray = [], $query = null): bool
    {
        if (!$query) {
            $query = new self('');
        }

        $query->setSQL($sql);
        $result = $query->prepareAndExecute($PDOParamsArray);
        unset($query);

        return $result;
    }

    /**
     * Fetches complete result set for SQL SELECT with given variables.
     *
     * @param string $sql SQL statement
     * @param array $PDOParamsArray array containing key/values for SQL variables
     * @param string $fetchStyle controls the contents of the returned array as documented in PDOStatement::fetch()
     *
     * @return array complete result set for SQL SELECT with given variables
     */
    public static function PDO_fetch_all($sql, $PDOParamsArray = [], $fetchStyle = PDO::FETCH_ASSOC, $query = null)
    {
        if (!$query) {
            $query = new self('');
        }

        $query->setSQL($sql);
        $query->prepareAndExecute($PDOParamsArray);

        return $query->fetchAll($fetchStyle);
    }

    /**
     * Fetches a single row from a result set for SQL SELECT with given variables.
     *
     * @param string $sql SQL statement
     * @param array $PDOParamsArray array containing key/values for SQL variables
     * @param string $fetchStyle controls the contents of the returned array as documented in PDOStatement::fetch()
     *
     * @return array|string|int complete result set for SQL SELECT with given variables
     */
    public static function PDO_fetch($sql, $PDOParamsArray = [], $fetchStyle = PDO::FETCH_ASSOC, $query = null)
    {
        if (!$query) {
            $query = new self('');
        }

        $query->setSQL($sql);
        $query->prepareAndExecute($PDOParamsArray);
        $row = $query->fetch($fetchStyle);
        unset($query);

        return $row;
    }

    /**
     * Instantiates a DatixDBQuery object and uses it to insert a record.
     *
     * @param string $sql SQL statement
     * @param array $PDOParamsArray array containing key/values for SQL variables
     *
     * @return string the insert ID
     */
    public static function PDO_insert($sql, $PDOParamsArray = [])
    {
        $query = new self($sql);
        $id = $query->insert($PDOParamsArray);
        unset($query);

        return $id;
    }

    /**
     * Instantiates a DatixDBQuery object and uses it to insert a record.
     *
     * @param string $table the table we're inserting into
     * @param array $PDOParamsArray array containing key/values for SQL variables
     *
     * @return string the insert ID
     */
    public static function PDO_build_and_insert($table, $PDOParamsArray = [])
    {
        $fields = array_keys($PDOParamsArray);
        $query = new self('INSERT INTO ' . $table . ' (' . implode(',', $fields) . ') VALUES (:' . implode(', :', $fields) . ')');
        $id = $query->insert($PDOParamsArray);
        unset($query);

        return $id;
    }

    public function buildAndInsert(string $table, array $parameters): int
    {
        return (int) self::PDO_build_and_insert($table, $parameters);
    }

    /**
     * Begins a transaction.
     */
    public static function static_beginTransaction()
    {
        $query = new self('');
        $query->beginTransaction();
    }

    /**
     * Rolls back a transaction.
     */
    public static function static_rollBack()
    {
        $query = new self('');
        $query->rollback();
    }

    /**
     * Commits a transaction.
     */
    public static function static_commit()
    {
        $query = new self('');
        $query->commit();
    }

    /**
     * @desc Generates the sql and parameter array for an "IN" statement, based on an array
     */
    public static function getINforPDO(array $Values)
    {
        $ParameterName = 'param';
        $ParameterCount = 0;

        $Parameters = [];

        foreach ($Values as $Value) {
            $SQL[] = ':' . $ParameterName . $ParameterCount;
            $Parameters[$ParameterName . $ParameterCount] = $Value;

            ++$ParameterCount;
        }

        $returnArray['SQL'] = 'IN (' . implode(', ', $SQL) . ')';
        $returnArray['Parameters'] = $Parameters;

        return $returnArray;
    }

    public static function CallStoredProcedure($aParams)
    {
        $queryParams = [];
        for ($x = 0; $x < count($aParams['parameters']); ++$x) {
            $queryParams[] = str_replace('@', ':', $aParams['parameters'][$x][0]);
        }
        $query = 'EXECUTE ' . $aParams['procedure'] . ' ' . implode(', ', $queryParams);
        $dbQuery = new self($query);
        $dbQuery->prepare();

        // Bind all available parameters
        for ($x = 0; $x < count($aParams['parameters']); ++$x) {
            // Ensure all @'s are replaced with :
            $parameter = $aParams['parameters'][$x][0];
            if (\UnicodeString::strstr($parameter, '@') !== false) {
                $parameter = str_replace('@', ':', $parameter);
            }
            if (\UnicodeString::substr($parameter, 0, 1) != ':') {
                $parameter = ':' . $parameter;
            }

            // Convert old mssql data types over to PDO PARAM TYPES
            switch ($aParams['parameters'][$x][2]) {
                // SQLTEXT, SQLVARCHAR, SQLCHAR, SQLINT1, SQLINT2, SQLINT4, SQLBIT, SQLFLT4, SQLFLT8, SQLFLTN
                case 'SQLTEXT':
                case 'SQLVARCHAR':
                case 'SQLCHAR':
                    $data_type = PDO::PARAM_STR;

                    break;
                case 'SQLINT1':
                case 'SQLINT2':
                case 'SQLINT4':
                case 'SQLBIT':
                case 'SQLFLT4':
                case 'SQLFLT8':
                case 'SQLFLTN':
                    $data_type = PDO::PARAM_INT;

                    break;
                default:
                    $data_type = PDO::PARAM_STR;

                    break;
            }

            // If is_output pass a parameter length to PDO:bind();
            $length = ($aParams['parameters'][$x][3] ?? false) ? 999 : null;

            // Must pass the variable that came it, they're passed by reference and populated by PDO
            $dbQuery->bind($parameter, $aParams['parameters'][$x][1], $data_type, $length);
        }

        return $dbQuery->executeStoredProcedure();
    }

    /**
     * Places quotes around the input string and escapes special characters.
     *
     * @param string $var the string to be quoted
     *
     * @return string
     */
    public static function quote($var)
    {
        $query = new self();

        return $query->PDO->quote($var);
    }

    /**
     * Builds the actual query as it would be run on the DB.  Useful for debugging.
     *
     * @param array $options Parameters to bind to the query
     */
    protected function setRawQuery(array $options)
    {
        $this->rawQuery = $this->sql;
        foreach ($options as $param => $value) {
            $value = EscapeQuotes($value);
            $this->rawQuery = preg_replace('/' . ((is_string($param) && $param[0] == ':') ? $param : ':' . $param) . '(?![_a-z0-9])/', $options[$param] === null ? 'NULL' : "'" . $value . "'", $this->rawQuery);
        }
    }

    /**
     * Handles SQL errors by logging them and printing an appropriate message to the screen.
     *
     * @param array $options Arguments that were passed to the PDO statement, for logging
     *
     * @throws DatixDBQueryException
     */
    private function handleSqlError($options): void
    {
        $registry = Container::get(Registry::class);
        $seeDetailedErrors = $registry->getParm('DATIX_SUPPORT_ACCOUNT', 'N')->isTrue();

        $errorInfo = $this->PDOStatement->errorInfo();
        if ($errorInfo[0] == 'HY093') {
            $errorInfo[2] = 'Incorrect number of bound parameters.  SQL: ' . $this->sql . '; Parameters: ' . json_encode($options);
        }

        $debugMessage = "Error: {$errorInfo[2]}  <br><br>SQL statement: {$this->rawQuery}\n";
        Log::error($debugMessage);

        if ($seeDetailedErrors) {
            $message = $debugMessage;
        } else {
            $message = _fdtk('sql_error');
        }

        // Current dir is the dir of PHP installation for some reason? Hence the use of the unusual include path
        require_once __DIR__ . '/../classes/Exceptions/DatixDBQueryException.php';

        throw new DatixDBQueryException($message);
    }
}
