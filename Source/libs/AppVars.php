<?php

// Set names, foreign keys, codes, etc. for each module
// (similar to AppVars.apl for the main app)

use app\models\contact\ContactTypes;
use app\models\equipment\entities\IncidentEquipmentLinkEntity;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use app\models\medication\entities\IncidentMedicationLink;
use app\models\modules\ModuleDisplayAcronyms;
use app\services\approvalStatus\ApprovalStatus;
use app\services\forms\RecordHeaderProvider;
use Source\generic_modules\ModuleDefKeys;
use Source\libs\AppVarsHelper;
use src\contacts\controllers\PayRatesController;
use src\contacts\controllers\TpocController;
use src\contacts\helpers\ContactsDataHelper;
use src\contacts\model\ContactsFields;
use src\framework\controller\Loader;
use src\framework\events\FormEventsHelper;
use src\framework\registry\Registry;
use src\incidents\helpers\IncidentsDataHelper;
use src\incidents\helpers\IncidentsDataHelperFactory;
use src\incidents\helpers\SendToSFDAHelper;
use src\incidents\model\IncidentsFields;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\incidents\model\PSIMSIncidentMedicationFields;
use src\incidents\model\PSIMSLinkContactsFields;
use src\psims\transport\PSIMSTransportFactory;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);
$spscDataset = $registry->getParm('SPSC_DATASET')->isTrue();
$captureTimeTaken = $spscDataset || $registry->getParm('TIME_TO_SUBMIT')->isTrue();
$actionTriggersEnabled = $registry->getParm('ACTION_TRIGGERS', 'N')->isTrue();
global $ModuleDefs;

$ModuleDefs['CON'] = [
    'MOD_ID' => MOD_CONTACTS,
    'CODE' => 'CON',
    'NAME' => _fdtk('mod_contacts_title'),
    'REC_NAME' => _fdtk('CONName'),
    'REC_NAME_PLURAL' => _fdtk('CONNames'),
    'REC_NAME_TITLE' => _fdtk('CONNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('CONNamesTitle'),
    'USES_APPROVAL_STATUSES' => true,
    'FK' => 'con_id',
    'TABLE' => 'contacts_main',
    'LINK_TABLE' => [// link table by contact type
        'A' => 'link_contacts',
        'C' => 'link_contacts',
        'E' => 'link_contacts',
        'I' => 'link_contacts',
        'M' => 'link_contacts',
        'N' => 'link_contacts',
        'P' => 'link_contacts',
        'Q' => 'link_contacts',
        'R' => 'link_contacts',
        'S' => 'link_contacts',
        'T' => 'link_contacts',
        'V' => 'link_contacts',
        'W' => 'link_contacts',
        'L' => 'link_contacts',
        'O' => 'link_respondents',
    ],
    'ACTION' => 'editcontact',
    'LINK_ACTION' => 'linkcontactgeneral',
    'LIBPATH' => 'Source/contacts',
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'CON1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::CONTACTS_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'CON', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::CONTACTS_LEVEL_2],
    ],
    'FORMCODE' => 'CON',
    'ICON' => 'icons/icon_CON.png',
    'PERM_GLOBAL' => 'CON_PERMS',
    'SEARCH_URL' => 'action=contactssearch',
    'CAN_LINK_DOCS' => true,
    'FORMCODE1' => 'CON',
    'BASIC_FORM_FILES' => [
        1 => 'Source/contacts/BasicForm_Simple.php',
        2 => 'Source/contacts/BasicForm.php',
    ],
    'FIELD_ARRAY' => [
        'con_type', 'con_title', 'con_forenames', 'con_surname', 'con_dob', 'con_dod', 'con_ethnicity',
        'con_disability', 'con_language', 'con_line1', 'con_line2', 'con_line3', 'con_city', 'con_county', 'con_country',
        'con_postcode', 'con_gender', 'con_number', 'con_nhsno',
        'con_police_number', 'con_work_alone_assessed', 'con_religion', 'con_sex_orientation', 'con_name', 'con_tel1',
        'con_tel2', 'con_email', 'location_id', 'service_id', 'con_subtype', 'con_notes', 'rep_approved', 'show_document',
        'osha_date_hired', 'con_social_security_number', 'con_middle_name', 'con_state', 'tax_id',
        'employee_state_hired', 'employment_termination_date', 'con_employment_status_code', 'con_process_level',
        'con_job_code', 'con_supervisor_name', 'con_department', 'con_location_code', 'con_fte', 'con_lawson_number',
        'employee_id_assigned_by_jurisdiction', 'con_remote_id', 'con_empl_grade',
        ContactsFields::NATIONALITY,
        ContactsFields::JOB_TITLE,
        ContactsFields::SOURCE_OF_RECORD,
        ContactsFields::API_SOURCE,
    ],
    // Required for work around when selecting VARCHAR data fields greater than 255 in length.
    'FIELD_ARRAY_SELECT' => [
        'con_type', 'con_title', 'con_forenames', 'con_surname', 'con_dob', 'con_dod', 'con_ethnicity',
        'con_disability', 'con_language', 'con_line1', 'con_line2', 'con_line3', 'con_city', 'con_county', 'con_country',
        'con_postcode', 'con_gender', 'con_number', 'con_nhsno',
        'con_police_number', 'con_work_alone_assessed', 'con_religion', 'con_sex_orientation', 'con_name', 'con_tel1',
        'con_tel2', 'con_email', 'location_id', 'service_id', 'con_subtype', 'con_notes', 'rep_approved', 'show_document',
        'osha_date_hired', 'con_social_security_number', 'con_middle_name', 'con_state', 'tax_id',
        'employee_state_hired', 'employment_termination_date', 'con_employment_status_code', 'con_process_level',
        'con_job_code', 'con_supervisor_name', 'con_department', 'con_location_code', 'con_fte', 'con_lawson_number',
        'employee_id_assigned_by_jurisdiction', 'con_hours_worked', 'con_hourly_rate', 'con_ncci_code', 'con_occupation',
        'con_marital_status', 'con_empl_grade', 'con_id_numbers',
        ContactsFields::NATIONALITY,
        ContactsFields::JOB_TITLE,
        ContactsFields::SOURCE_OF_RECORD,
        ContactsFields::API_SOURCE,
    ],
    'LINKED_FIELD_ARRAY' => [
        'link_type', 'link_role', 'link_status', 'link_notes', 'link_treatment', 'link_bodypart1', 'link_injury1',
        'link_ref', 'link_dear', 'link_npsa_role', 'link_deceased', 'link_age', 'link_age_band', 'link_occupation',
        'link_riddor', 'link_is_riddor', 'link_daysaway', 'link_clin_factors', 'link_direct_indirect',
        'link_injury_caused', 'link_attempted_assault', 'link_discomfort_caused', 'link_public_disorder',
        'link_verbal_abuse', 'link_harassment', 'link_police_pursue', 'link_police_persue_reason', 'link_pprop_damaged',
        'link_worked_alone', 'link_become_unconscious', 'link_req_resuscitation', 'link_hospital_24hours',
        'link_abs_start', 'link_abs_end', 'link_mhact_section', 'link_mhcpa', 'link_patrelation', 'link_legalaid',
        'link_lip', 'link_ndependents', 'link_agedependents', 'link_marriage', 'link_injuries', 'link_resp',
        'link_plapat', 'link_notify_progress', 'link_date_admission', 'show_injury', 'link_illness', 'show_illness',
        'hourly_rate', 'hours_worked', 'weekly_rate', 'monthly_rate', 'link_position',
        'total_lost_time', 'total_restricted_time', 'show_restricted_time', 'icd_classification', 'icd_diagnosis_codes', 'icd_procedure_codes',
        'claimant_medicare_beneficiary', 'claimant_payments_non_medical', 'date_medicare_confirmed', 'claimant_medicare_claim_number',
        'date_mmsea_last_reported', 'delete_if_field_with_medicate', 'no_fault', 'no_fault_insurance_limit',
        'no_fault_exhaust_date',
        'edi_wage_period', 'edi_employment_status', 'edi_employee_id_type', 'edi_work_loss_list', 'edi_physical_restrictions',
        'edi_disability_type', 'edi_diagnosis', 'edi_agency_code', 'edi_ncci_class', 'edi_type_loss', 'edi_reporting_period',
        'edi_date_disability_known_employer', 'first_day_of_disability', 'edi_accident_premises', 'edi_return_to_work_type',
        'edi_return_to_work_qualif', 'initial_rtw_same_employer',
        'mmsea_relationship_to_beneficiary', 'mmsea_type_of_representative', 'time_employee_began_work',
        'msp_effective_date', 'msp_termination_date', 'msp_type', 'disposition_code', 'edi_drug_screen_summary',
        'include_in_tpoc', 'full_pay_injury_day', 'salary_continued', 'employer_paid_salary_as_compensation', 'date_maximum_medical_improvement',
        'date_claim_admin_knew_lost_time', 'maintenance_type_code_date', 'maintenance_type_correction_code_date', 'verification_number',
        'link_sedation',
        ContactsFields::LINK_HEIGHT,
        ContactsFields::LINK_WEIGHT,
        ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT,
        ContactsFields::APC_AWARE_REPORT,
        ContactsFields::APC_AAR,
        ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT,
        ContactsFields::REFERRAL_SUBJECT_CHILD,
        ContactsFields::INTERPRETER_USED,
        ContactsFields::CHILD_WITNESS,
        ContactsFields::WITNESS_AWARE_REPORT,
        ContactsFields::LINK_ORGANISATION,
        ContactsFields::CON_KNOW_ID_FIELD,
    ],
    'LINKED_FIELD_ARRAY_PSIMS' => [
        PSIMSLinkContactsFields::PSIMS_AGE_YEARS,
        PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME,
        PSIMSLinkContactsFields::PSIMS_GENDER,
        PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY,
        PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM,
        PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM,
        PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION,
    ],
    'ONE_TO_MANY_LINKED_FIELD_ARRAY' => [
        'link_bodypart1', 'link_injury1',
    ],
    'LINKED_FIELD_ARRAY_RESPONDENT' => [
        'link_type', 'con_id', 'link_notes', 'link_resp', 'link_role', 'main_recordid',
        'indemnity_reserve_assigned', 'expenses_reserve_assigned', 'fin_medical_reserve_assigned', 'fin_legal_reserve_assigned',
        'fin_temporary_indemnity_reserve_assigned', 'fin_permanent_indemnity_reserve_assigned',
        'remaining_indemnity_reserve_assigned', 'remaining_expenses_reserve_assigned',
        'fin_remaining_medical_reserve_assigned', 'fin_remaining_legal_reserve_assigned',
        'fin_remaining_temporary_indemnity_reserve_assigned', 'fin_remaining_permanent_indemnity_reserve_assigned',
        'resp_total_paid', 'main_module',
    ],
    'LINKED_FIELD_DEFAULT_MAPPINGS' => [
        'hours_worked' => 'con_hours_worked',
        'hourly_rate' => 'con_hourly_rate',
        'edi_ncci_class' => 'con_ncci_code',
        'link_occupation' => 'con_occupation',
        'link_marriage' => 'con_marital_status',
    ],
    'OVERRIDE_READONLY_FIELDS' => [
        'con_dob',
        'con_dod',
    ],
    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserCON1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserCONSettings',

    'HARD_CODED_LISTINGS' => [
        'all' => [
            'Where' => '(1=1)',
        ],
    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Contacts/c_dx_contacts_guide.xml',

    'FIELDSET_MAPPINGS' => [
        'A' => 5,
        'E' => 6,
        'N' => 7,
        'INC' => 78,
        'COM' => 79,
    ],
    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    static function (array $data): void {
                        $loader = new Loader();

                        $payRatesController = $loader->getController([
                            'controller' => PayRatesController::class,
                        ]);
                        $payRatesController->calculateWeeklyAndMonthlyRates($data);

                        $controller = $loader->getController([
                            'controller' => TpocController::class,
                        ]);
                        $controller->saveTpocRecords($data);

                        Container::get(ContactsDataHelper::class)->savePsimsContactFieldsValues($data);

                        if ($data['module'] === Module::INCIDENTS) {
                            Container::get(IncidentsDataHelper::class)->auditMaxHarmFields(
                                $data['main_recordid'],
                                $data['incidentMaxHarmFieldValuesBeforeSave'] ?? [],
                                $data['hideUsersNameFromSubmission'],
                            );
                        }

                        $PSIMSTransport = (new PSIMSTransportFactory())->create();
                        $PSIMSTransport->sendContact($data, $data['module'] ?? null, $data['main_recordid'] ?? null);
                    },
                ],
            ],
        ],
    ],
    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    static function (array $eventParameters): array {
                        return Container::get(ContactsDataHelper::class)->loadPsimsContactFieldsValues($eventParameters['recordId']);
                    },
                ],
            ],
        ],
    ],
    'COMBO_EXCLUDE' => [
        PSIMSLinkContactsFields::PSIMS_AGE_YEARS,
        PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME,
        PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM,
        PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM,
        PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION,
        PSIMSLinkContactsFields::PSIMS_GENDER,
    ],
];

$ModuleDefs['USE'] = [
    'MOD_ID' => MOD_USERS,
    'CODE' => 'USE',
    'PERM_GLOBAL' => 'USE_PERMS',
    'NAME' => _fdtk('mod_users_title'),
    'REC_NAME' => _fdtk('USEName'),
    'REC_NAME_PLURAL' => _fdtk('USENames'),
    'REC_NAME_TITLE' => _fdtk('USENameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('USENamesTitle'),
    'ICON' => 'icons/icon_ADM.png',
    'PK' => 'recordid',
    'ACTION' => 'edituser',
    'TABLE' => 'users_main',
    'NO_REP_APPROVED' => true,
    'LIBPATH' => 'Source/security',
    'FORMS' => [
        2 => ['LEVEL' => 2, 'CODE' => 'USE', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::USERS_LEVEL_2],
    ],
    'BASIC_FORM_FILES' => [2 => 'Source/security/BasicForm.php'],
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserUSESettings',
    'SEARCH_URL' => 'action=search',
    'FIELD_ARRAY' => ['login', 'initials', 'permission', 'lockout', 'use_staff_include', 'use_forenames', 'use_surname', 'use_tel1', 'use_email', 'positions'],
];

$ModuleDefs['ADM'] = [
    'MOD_ID' => MOD_ADMIN,
    'CODE' => 'ADM',
    'PERM_GLOBAL' => 'ADM_PERMS',
    'NAME' => _fdtk('mod_admin_short_title'),
    'REC_NAME' => _fdtk('mod_admin_short_title'),
    'ICON' => 'icons/icon_ADM.png',
    'PK' => 'recordid',
    'TABLE' => 'staff',
    'NO_REP_APPROVED' => true,
    'LIBPATH' => 'Source/security',
    'FORMS' => [
        2 => ['LEVEL' => 2, 'CODE' => 'ADM', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::ADMIN_LEVEL_2],
    ],
    'BASIC_FORM_FILES' => [2 => 'Source/security/BasicForm.php'],
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserUSESettings',
    'SEARCH_URL' => 'action=search',
    'FIELD_ARRAY' => array_merge($ModuleDefs['CON']['FIELD_ARRAY'], $ModuleDefs['USE']['FIELD_ARRAY']),
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Admin/c_dx_admin_guide.xml',
];

// Incidents
$ModuleDefs['INC'] = [
    'MOD_ID' => MOD_INCIDENTS,
    'CODE' => 'INC',
    'IS_MAIN_MODULE' => true,
    'NAME' => _fdtk('mod_incidents_title'),
    'REC_NAME' => _fdtk('INCName'),
    'REC_NAME_PLURAL' => _fdtk('INCNames'),
    'REC_NAME_TITLE' => _fdtk('INCNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('INCNamesTitle'),
    'FK' => 'inc_id',
    'NAME_FIELD' => 'inc_name',
    'WORKFLOWS_ALLOWING_READONLY_STATUS_EDIT' => [8],
    'STATUSES_ALLOWING_READONLY_STATUS_EDIT' => ['FA'],
    'USES_APPROVAL_STATUSES' => true,
    'OURREF' => 'inc_ourref',
    'TABLE' => 'incidents_main',
    'HAS_DEFAULT_LISTING' => true,
    'ACTION' => 'incident',
    ModuleDefKeys::NON_GRAPHICAL_FIELDSETS => [167, 168],
    'PERM_GLOBAL' => 'DIF_PERMS',
    'NO_LEVEL1_GLOBAL' => 'DIF_NO_OPEN',
    'MAIN_URL' => 'action=incident',
    'LIBPATH' => 'Source/incidents',
    RecordHeaderProvider::APPVARS_KEY => [
        'recordid',
        'inc_ourref',
        'inc_name',
        'inc_type',
        'inc_carestage',
        'inc_clin_detail',
        'inc_clin_type',
        'inc_category',
        'inc_subcategory',
    ],
    'DEFAULT_ORDER' => 'inc_dincident',
    'FIELD_NAMES' => [
        'NAME' => 'inc_name',
        'HANDLER' => 'inc_mgr',
        'MANAGER' => 'inc_head',
        'INVESTIGATORS' => 'inc_investigator',
        'INCIDENT_DATE' => 'inc_dincident',
    ],
    'LOCATION_FIELD_PREFIX' => 'inc_',
    'LEVEL1_PERMS' => ['DIF1'],
    'FORMS' => [
        1 => ['LEVEL' => 1, 'CODE' => 'DIF1', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::INCIDENTS_LEVEL_1],
        2 => ['LEVEL' => 2, 'CODE' => 'DIF2', 'DISPLAY_ACRONYM' => ModuleDisplayAcronyms::INCIDENTS_LEVEL_2],
    ],
    'LOGGED_OUT_LEVEL1' => true,
    'FORMCODE1' => 'DIF1',
    'FORMCODE2' => 'DIF2',
    'BASIC_FORM_FILES' => [
        1 => 'Source/incidents/BasicForm.php',
        2 => 'Source/incidents/BasicFormDIF2.php',
    ],
    'LINKCONTACTACTION' => 'linkcontactgeneral',
    'ICON' => 'icons/icon_INC.png',
    'SEARCH_URL' => 'action=incidentssearch',
    'CAN_CREATE_EMAIL_TEMPLATES' => true,
    'CAN_LINK_DOCS' => true,
    'CAN_LINK_CONTACTS' => true,
    'CAN_LINK_MODULES' => true,
    'CAN_LINK_NOTES' => true,
    'CONTACTTYPES' => [
        ContactTypes::PERSON_AFFECTED => ['Type' => 'A', 'Name' => _fdtk('person_affected'), 'Plural' => _fdtk('table_link_contacts_INC_A'), 'None' => _fdtk('no_com_person_affected_plural'), 'CreateNew' => _fdtk('person_affected_link')],
        ContactTypes::EMPLOYEE => ['Type' => 'E', 'Name' => _fdtk('employee'), 'Plural' => _fdtk('employee_plural'), 'None' => _fdtk('no_employee_plural'), 'CreateNew' => _fdtk('employee_link')],
        ContactTypes::OTHER_CONTACT => ['Type' => 'N', 'Name' => _fdtk('other_contact'), 'Plural' => _fdtk('other_contact_plural'), 'None' => _fdtk('no_other_contact_plural'), 'CreateNew' => _fdtk('other_contact_link')],
    ],
    'LEVEL1_CON_OPTIONS' => [
        ContactTypes::PERSON_AFFECTED => ['Title' => _fdtk('person_affected'), 'Show' => 'show_person', 'DivName' => 'contacts_type_A'],
        ContactTypes::EMPLOYEE => ['Title' => _fdtk('employee'), 'Show' => 'show_employee', 'DivName' => 'contacts_type_E'],
        ContactTypes::OTHER_CONTACT => ['Title' => _fdtk('other_contact'), 'Show' => 'show_other_contacts', 'DivName' => 'contacts_type_N'],
        ContactTypes::WITNESS => ['Title' => 'Witness', 'Role' => 'WITN', 'Show' => 'show_witness', 'ActualType' => 'N', 'DivName' => 'contacts_type_W'],
        ContactTypes::REPORTER => ['Title' => 'Reporter', 'Role' => GetParm('REPORTER_ROLE', 'REP'), 'ActualType' => 'N', 'DivName' => 'contacts_type_R', 'Max' => 1],
        ContactTypes::POLICE_OFFICER => ['Title' => 'Police Officer', 'ActualType' => 'N', 'DivName' => 'contacts_type_P'],
        ContactTypes::ALLEGED_ASSAILANT => ['Title' => 'Alleged Assailant', 'ActualType' => 'N', 'DivName' => 'contacts_type_L'],
    ],
    'AGE_AT_DATE' => 'inc_dincident',
    'LINKED_DOCUMENTS' => true,
    'DOCUMENT_SECTION_KEY' => 'extra_document',
    'LINKED_CONTACT_LISTING_COLS' => ['inc_name', 'inc_dincident', 'inc_type', 'location_id', 'inc_notes'],
    'LINKED_CONTACT_ORDERBY_COLS' => ['inc_name'],
    'SHOW_EMAIL_GLOBAL' => 'DIF_SHOW_EMAIL',
    'EMAIL_REPORTER_GLOBAL' => 'DIF_1_ACKNOWLEDGE',
    'DIF_EMAIL_MGR' => 'DIF_EMAIL_MGR',
    'EMAIL_HANDLER_GLOBAL' => 'EMAIL_HANDLER_GLOBAL',
    'EMAIL_USER_PARAMETER' => 'DIF_STA_EMAIL_LOCS',
    'FORM_DESIGN_LEVEL_1_FILENAME' => 'UserDIF1Settings',
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserDIF2Settings',
    'LEVEL_1_ONLY_FORM_GLOBAL' => 'DIF1_ONLY_FORM',
    'STAFF_EMPL_FILTER_MAPPINGS' => [
        'location' => 'location_id',
        'service' => 'service_id',
    ],
    'FIELD_ARRAY' => [
        'uuid', 'rep_approved', 'inc_mgr', 'inc_type', 'inc_category', 'inc_subcategory', 'inc_dincident', 'inc_time',
        'inc_time_band', 'inc_notes', 'inc_actiontaken', 'inc_result', 'inc_rep_tel', 'inc_rep_email',
        'inc_head', 'inc_inv_dstart', 'inc_inv_dcomp', 'inc_inv_outcome', 'inc_is_riddor',
        'inc_root_causes', 'inc_reportedby', 'inc_repname', 'inc_dreported', 'location_id', 'service_id', 'other_location', 'exact_location', 'confirm_location_id', 'confirm_service_id',
        'other_service', 'inc_severity', 'inc_name', 'inc_ourref', 'inc_consequence', 'inc_inv_action', 'inc_inv_lessons', 'inc_inv_lessons_sub_category',
        'inc_lessons_code', 'inc_action_code', 'inc_likelihood', 'inc_grade', 'inc_clin_detail', 'inc_clintype',
        'inc_consequence_initial', 'inc_likelihood_initial', 'inc_grade_initial', 'inc_cnstitype', 'inc_carestage',
        'inc_cost', 'inc_inquiry', 'inc_dopened',
        'inc_submittedtime', 'inc_dsched', 'inc_med_stage', 'inc_med_error', 'inc_med_drug', 'inc_med_drug_rt',
        'inc_med_form', 'inc_med_form_rt', 'inc_med_dose', 'inc_med_dose_rt', 'inc_med_route', 'inc_med_route_rt',
        'inc_dnotified', 'inc_ridloc', 'inc_address', 'inc_localauth', 'inc_acctype', 'inc_riddor_ref', 'inc_riddorno',
        'inc_report_npsa', 'inc_dnpsa', 'inc_agg_issues', 'inc_pol_crime_no', 'inc_pol_called', 'inc_pol_call_time',
        'inc_pol_attend', 'inc_pol_att_time', 'inc_pol_action', 'inc_pars_pri_type', 'inc_pars_sec_type',
        'inc_pars_clinical', 'inc_user_action', 'inc_pars_address', 'inc_postcode', 'inc_tprop_damaged',
        'inc_investigator', 'inc_consultants', 'inc_notify', 'inc_injury', 'inc_bodypart', 'inc_pasno1',
        'inc_pasno2', 'inc_pasno3', 'inc_rc_required', 'show_person', 'show_witness', 'show_employee',
        'show_other_contacts', 'show_document', 'show_medication', 'show_pars', 'show_assailant',
        'inc_pars_first_dexport', 'inc_pars_dexport', 'inc_affecting_tier_zero', 'inc_type_tier_one',
        'inc_type_tier_two', 'inc_type_tier_three', 'inc_level_intervention', 'inc_level_harm', 'inc_never_event',
        'inc_last_updated', 'inc_ot_q1', 'inc_ot_q2', 'inc_ot_q3', 'inc_ot_q4', 'inc_ot_q5', 'inc_ot_q6', 'inc_ot_q7',
        'inc_ot_q8', 'inc_ot_q9', 'inc_ot_q10', 'inc_ot_q11', 'inc_ot_q12', 'inc_ot_q13', 'inc_ot_q14', 'inc_ot_q15',
        'inc_ot_q16', 'inc_ot_q17', 'inc_ot_q18', 'inc_ot_q19', 'inc_ot_q20', 'inc_ot_q21', 'inc_ot_q22', 'inc_causal_factors_linked',
        'inc_mob_severity', 'inc_mob_category', 'inc_mob_location', 'inc_mob_anonymous', 'flag_for_investigation', 'flag_for_rib', 'show_equipment', 'osha_recordable',
        'incident_occurred_on_employer_premises', 'safeguard_provided', 'safeguard_used', 'edi_cause_code',
        'anzco_coding', 'breakdown_agency', 'agency_of_injury', 'mech_of_injury_1', 'mech_of_injury_2',
        'nature_of_injury_1', 'nature_of_injury_2', 'toocs_1', 'toocs_2', 'health_service_site', 'rep_feedback_codes',
        'rep_feedback_notes', 'learnings_to_share', 'key_learnings', 'learnings_title', 'fire_detected', 'fire_cause_known',
        'fire_action_taken', 'fire_department_response', 'fire_department_time_taken', 'blood_date_transfusion_started',
        'blood_time_transfusion_started', 'blood_time_transfusion_stopped', 'blood_date_reaction_started',
        'blood_time_reaction_started', 'blood_amount_transfused', 'fall_unassisted_assisted', 'fall_observed_by',
        'fall_physical_injury', 'fall_patient_doing', 'fall_patient_doing_other', 'fall_risk_assessment',
        'fall_protocols_in_place', 'fall_score', 'fall_medication_risk', 'fall_fell_from', 'fall_post_fall_action',
        'fall_risk_assessment_score', 'spsc_international_goals', 'spsc_national_goals', 'spsc_surgery_type',
        'spsc_morbidity_anticipated', 'spsc_reason_for_lama', 'spsc_recovery_date', 'spsc_visit_type',
        'spsc_hazardous_substance_type', 'spsc_injury_level', 'spsc_code_white_person_injury', 'spsc_code_white_gender',
        'hro_characteristics', 'time_taken_to_submit', 'sac_score', 'inc_result_initial', 'inc_severity_initial',
        'sac_score_initial', 'approved_by',
        IncidentsFields::SAC_DATE_DIFF_DAYS,
        IncidentsFields::SAC_DATE_DIFF_HOURS,
        IncidentsFields::SAC_SCORE_DATE,
        IncidentsFields::SAC_SCORE_TIME,
        IncidentsFields::INC_SPECIALTY,
        IncidentsFields::INC_NARANJO_TOTAL_SCORE,
        IncidentsFields::INC_ADR_PROBABILITY_SCALE,
        IncidentsFields::INC_ADR_ACTION_TAKEN,
        IncidentsFields::INC_ADR_ORGAN_SYSTEM_FIELDS,
        IncidentsFields::INC_ADR_TOTAL_SCORE,
        IncidentsFields::SPSC_WHICH_OCCURRED,
        IncidentsFields::SPSC_LOCATION_DVT,
        IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CONFIRMED_DVT,
        IncidentsFields::SPSC_WAS_VTE_RISK_ASSESSMENT_DOCUMENTED,
        IncidentsFields::SPSC_PATIENTS_DOCUMENTED_RISK_OF_VTE,
        IncidentsFields::SPSC_DOCUMENTED_RISK_OF_BLEEDING,
        IncidentsFields::SPSC_PHARMACOLOGICAL_MECHANICAL_PROPHYLAXIS,
        IncidentsFields::SPSC_PARMACOLOGICAL_ANTICOAGULANT_ADMINISTERED,
        IncidentsFields::SPSC_WHY_ANTICOAGULANT_NOT_GIVE,
        IncidentsFields::SPSC_WAS_MECHANICAL_PROPHYLAXIS_APPLIED,
        IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CORFIRMED_PE,
        IncidentsFields::SPSC_VTE_OUTCOME,
        IncidentsFields::SPSC_VTE_TYPE,
        IncidentsFields::SPSC_VTE_SUB_TYPE,
        IncidentsFields::SPSC_VTE_FEMALE,
        IncidentsFields::ANON_REPORTING,
        IncidentsFields::SPSC_BLOOD_TYPE_PRODUCT,
        IncidentsFields::SPSC_WAS_RED_CODE_ACTIVATED,
        IncidentsFields::SPSC_WAS_FIRE_ALARM_ACTIVATED,
        IncidentsFields::SPSC_WAS_BUILDING_EVACUATED,
        IncidentsFields::SPSC_WAS_PORTABLE_FIRE_EXTINGUISHERS_USED,
        IncidentsFields::SPSC_WAS_LOCAL_FIRE_DEPARTMENT_INFORMED,
        IncidentsFields::SPSC_WERE_ANY_DAMAGES_TO_PROPERTY,
        IncidentsFields::SPSC_MORBIDITY_RECORD_IS,
        IncidentsFields::SPSC_IS_TRIGGER_RELATED_TO_IMPROPER_ASSESSMENT_OF_PATIENT,
        IncidentsFields::SPSC_ADDITIONAL_MORBIDITY_TRIGGERS,
        IncidentsFields::SOURCE_OF_RECORD,
        IncidentsFields::SEND_TO_SFDA,
        IncidentsFields::INC_GRADE_RATING,
        IncidentsFields::INC_GRADE_INITIAL_RATING,
        IncidentsFields::INC_REP_ID,
        IncidentsFields::DIAGNOSTIC,
        IncidentsFields::PROCEDURES,
        IncidentsFields::ALLERGY_REACTION,
        IncidentsFields::ALLERGY_SEVERITY,
        IncidentsFields::ALLERGEN_TYPE,
        IncidentsFields::ALLERGY_CLINICAL_STATUS,
        IncidentsFields::ONSET_DATE,
        IncidentsFields::DATE_OF_ADMISSION,
        IncidentsFields::RECOVERY_DATE,
        IncidentsFields::OUTBREAK_IMPACT,
        IncidentsFields::OUTBREAK_TYPE,
        IncidentsFields::VLC_DRUG_DOCUMENTED_DATE,
        IncidentsFields::VLC_DRUG_REACTION_EFFECT,
        IncidentsFields::VLC_MEDICAL_CONDITION,
        IncidentsFields::VLC_HEALTH_RESTORED_DATE,
        IncidentsFields::VLC_CONCOMITANT_USED,
        IncidentsFields::VLC_DEVICE_DOCUMENTED_DATE,
        IncidentsFields::VLC_DEVICE_EFFECT_HEALTH,
        IncidentsFields::VLC_DEVICE_CONTRIBUTION,
        IncidentsFields::VLC_HOSPITAL_REP_EMAIL_ID,
        IncidentsFields::VLC_HEALTH_CANADA_INST_ID,
        IncidentsFields::MAX_PHYSICAL_HARM_PERSON,
        IncidentsFields::MAX_PSYCHOLOGICAL_HARM_PERSON,
    ],
    'LINKED_RECORDS' => [
        'inc_causal_factor' => [
            'section' => 'causal_factor',
            'type' => 'inc_causal_factor', 'table' => 'causal_factors', 'recordid_field' => 'recordid', 'save_listorder' => true,
            'basic_form' => ['Rows' => ['caf_level_1', 'caf_level_2']],
            'main_recordid_label' => 'INC_ID',
            'useIdentity' => true,
        ],
        'pressure_ulcers' => [
            'section' => 'pressure_ulcers',
            'type' => 'pressure_ulcers',
            'table' => 'pressure_ulcers',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => [
                    'hospital',
                    'location',
                    'nature',
                    'advanced_stage',
                    'admission_status',
                    'admission_status_3_4',
                    'skin_inspection',
                    'risk_assessment',
                    'risk_assessment_type',
                    'increased_risk',
                    'preventative_intervention_yn',
                    'preventative_intervention',
                    'preventative_intervention_detail',
                    'device',
                    'device_type',
                    'device_detail',
                    'secondary_morbidity',
                    'risk_assessment_score',
                ],
            ],
            'main_recordid_label' => 'INC_ID',
            'fieldFormatsTable' => 'INCPSO',
            'useIdentity' => true,
            'title' => _fdtk('pressure_sore'),
        ],
        'skin_lesions' => [
            'section' => 'skin_lesions',
            'type' => 'skin_lesions',
            'table' => 'skin_lesions',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => [
                    'site',
                    'development_level',
                ],
            ],
            'main_recordid_label' => 'INC_ID',
            'fieldFormatsTable' => 'INCSKL',
            'useIdentity' => true,
            'title' => _fdtk('skin_lesions'),
        ],
        'sharps' => [
            'section' => 'sharps',
            'type' => 'sharps',
            'table' => 'sharps',
            'recordid_field' => 'recordid',
            'save_listorder' => true,
            'basic_form' => [
                'Rows' => [
                    'nature_of_injury',
                    'type_of_tool',
                    'contamination',
                    'contamination_type',
                    'contamination_other',
                ],
            ],
            'main_recordid_label' => 'INC_ID',
            'fieldFormatsTable' => 'INCSHP',
            'useIdentity' => true,
            'title' => _fdtk('sharps'),
        ],
    ],
    'LINKED_MODULES' => [
        'CON' => ['action' => 'linkcontactgeneral'],
    ],
    'MEDICATION_LINK_ENTITY' => IncidentMedicationLink::class,
    'EQUIPMENT_LINK_ENTITY' => IncidentEquipmentLinkEntity::class,
    'USE_WORKFLOWS' => true,
    'WORKFLOW_GLOBAL' => 'WORKFLOW',
    'DEFAULT_WORKFLOW' => 2,
    'APPROVAL_LEVELS' => true,
    'TRAFFICLIGHTS_FIELDS' => [
        'rep_approved', 'inc_grade', 'inc_result', 'inc_severity', 'inc_type',
    ],
    'ADDITIONAL_COPY_LIB' => 'Source/generic_modules/INC/CopyFunctions.php',
    'ADDITIONAL_COPY_CHECK_OPTIONS' => [
        'copy_causal_factors' => true,
    ],
    'ADDITIONAL_COPY_FUNCTION_CALLS' => [
        'CopyCausalFactors',
    ],
    'ADDITIONAL_GENERATE_LIB' => 'Source/generic_modules/INC/CopyFunctions.php',
    'ADDITIONAL_GENERATE_FUNCTION_CALLS' => [
        'CopyCausalFactors',
    ],
    'HOME_SCREEN_STATUS_LIST' => true,
    // A list of all possible contacts fields that a user can search on from a DIF1 form.
    // Exact fields used in a specific installation will be set in the CON_PAS_CHK_FIELDS global
    'DIF1_CON_SEARCHING_FIELDS' => [
        'con_title',
        'con_forenames',
        'con_surname',
        'con_email',
        'con_line1',
        'con_line2',
        'con_line3',
        'con_city',
        'con_county',
        'con_country',
        'con_postcode',
        'con_gender',
        'con_dob',
        'con_dod',
        'con_type',
        'con_subtype',
        'con_tel1',
        'con_tel2',
        'con_number',
        'con_nhsno',
        'con_police_number',
        'con_ethnicity',
        'con_language',
        'con_disability',
        'con_religion',
        'con_sex_orientation',
    ],
    'CAUSAL_FACTOR_TYPE' => 'inc_causal_factor',
    'CCS2_FIELDS' => [
        'inc_affecting_tier_zero',
        'inc_type_tier_one',
        'inc_type_tier_two',
        'inc_type_tier_three',
        'inc_level_intervention',
        'inc_level_harm',
    ],
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Incidents/c_dx_incidents_guide.xml',

    'FIELDSET_MAPPINGS' => [
        'A' => 5,
        'E' => 6,
        'N' => 7,
        'linked_actions' => 2,
        'payments' => 73,

    ],

    ModuleDefKeys::POST_SAVE_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    static function (array $data): void {
                        (new SendToSFDAHelper())->sendToSFDA($data);
                        $dataHelper = (new IncidentsDataHelperFactory())->create();
                        $dataHelper->savePsimsIncidentValues($data);
                        $dataHelper->savePsimsIncidentMedicationFieldsValues($data);
                        $dataHelper->savePsimsIncidentEquipmentFieldsValues($data);
                        $dataHelper->savePsimsIncidentCodedFieldsValues($data);
                        $dataHelper->saveIncidentsTimeChainValues($data);
                        $dataHelper->saveIncidentsTimeFieldValues($data);
                        $PSIMSTransport = (new PSIMSTransportFactory())->create();
                        $PSIMSTransport->sendIncident($data);
                    },
                ],
            ],
        ],
    ],


    ModuleDefKeys::EXTRA_RECORD_DATA_HOOK => [
        FormEventsHelper::EVENT_LISTENERS => [
            [
                FormEventsHelper::EVENT_FUNCTIONS => [
                    static function (array $eventParameters): array {
                        $dataHelper = Container::get(IncidentsDataHelper::class);

                        return array_merge(
                            $dataHelper->loadPsimsIncidentValues($eventParameters['recordId']),
                            $dataHelper->loadPsimsIncidentMedicationFieldsValues($eventParameters['recordId']),
                            $dataHelper->loadPsimsIncidentEquipmentFieldsValues($eventParameters['recordId']),
                            $dataHelper->loadPsimsIncidentCodedFieldsValues($eventParameters['recordId']),
                            $dataHelper->loadIncidentsTimeChainValues($eventParameters['recordId']),
                            $dataHelper->loadPsimsResponseWarningsData($eventParameters['recordId']),
                            $dataHelper->loadTimeFieldData($eventParameters['recordId']),
                        );
                    },
                ],
            ],
        ],
    ],

    'RECORD_SUBMIT_DURATION' => $captureTimeTaken,

    'ACTION_TRIGGERS' => $spscDataset || $actionTriggersEnabled,
    'ALLOWS_ANONYMOUS_REPORTING' => true,
    // OVERDUE
    'CAN_HAVE_OVERDUE_RECORDS' => true,
    'OVERDUE_STATUSES' => [
        ApprovalStatus::HOLDING_AREA,
        ApprovalStatus::DRAFT,
        ApprovalStatus::BEING_REVIEWED,
        ApprovalStatus::AWAITING_FINAL,
        ApprovalStatus::BEING_APPROVED,
    ],
    'OVERDUE_DAY_GLOBAL' => 'DIF_OVERDUE_DAYS',
    'OVERDUE_TYPE_GLOBAL' => 'DIF_OVERDUE_TYPE',
    'OVERDUE_CHECK_FIELD' => IncidentsFields::DATE_REPORTED,
    'COMBO_EXCLUDE' => [
        PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE,
        PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED,
        PSIMSIncidentCodedFields::PSIMS_ESTIMATED_TIME,
        PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE,
        PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS,
        PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN,
        PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN,
        PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT,
        PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES,
        PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY,
        PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA,
        PSIMSIncidentCodedFields::PSIMS_RISK_THEME,
        PSIMSIncidentCodedFields::PSIMS_RISK_SERVICE_AREA,
        PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT,
        PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME,
        PSIMSIncidentCodedFields::PSIMS_CARE_DETECTION_FACTOR,
        PSIMSIncidentCodedFields::PSIMS_RISK_IDENTIFIED_LOCATION,
        PSIMSIncidentCodedFields::PSIMS_LOCATION_AT_RISK,
        PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE,
        PSIMSIncidentCodedFields::PSIMS_FURNITURE_FITTINGS,
        PSIMSIncidentCodedFields::PSIMS_IT_INVOLVEMENT_FACTORS,
        PSIMSIncidentCodedFields::PSIMS_BLOOD_NOT_USED,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_DEFICIENT_DETAILS,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_INVOLVEMENT_FACTOR,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_NOT_USED,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_USED_TOO_MUCH,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_WRONG_DETAILS,
        PSIMSIncidentCodedFields::PSIMS_TISSUE_DAMAGED_DETAILS,
        PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_TOO_MUCH,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_DETAILS,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_OMITTED_ACTION,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_UNAVAILABLE_DETAIL,
        PSIMSIncidentCodedFields::PSIMS_PEOPLE_WRONG_ACTION,
        PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY,
        PSIMSIncidentFields::PSIMS_REPORTER_ROLE,
        PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT,
        PSIMSIncidentMedicationFields::DRUG_INSUFFICIENT_DETAILS,
        PSIMSIncidentMedicationFields::DRUG_INVOLVEMENT_FACTORS,
        PSIMSIncidentMedicationFields::DRUG_REACTION,
        PSIMSIncidentMedicationFields::DRUG_USED_TOO_MUCH,
        PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES,
        PSIMSIncidentMedicationFields::DRUG_GIVEN_INCORRECTLY,
        PSIMSIncidentMedicationFields::PROBLEM_MEDS_PACKAGING,
        PSIMSIncidentEquipmentFields::PSIMS_DEVICE_TYPE,
        PSIMSIncidentEquipmentFields::DEVICE_BROKEN_DETAILS,
        PSIMSIncidentEquipmentFields::DEVICE_NOT_ENOUGH_DETAILS,
        PSIMSIncidentEquipmentFields::DEVICE_USAGE_FACTORS,
        PSIMSIncidentEquipmentFields::DEVICE_USED_UNNECESSARILY,
        PSIMSIncidentEquipmentFields::DEVICE_USAGE_DETAILS,
        PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_OTHER,
        PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_PROBLEM,
        PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_OTHER,
        PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_PROBLEM,
        PSIMSIncidentFields::PSIMS_BLOOD_PROBLEM,
        PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BATCH,
        PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BRAND,
        PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_DETAILS,
        PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_PROBLEM,
    ],
    'DATE_OPENED_AT' => IncidentsFields::DATE_OPENED,
    'IS_MEDS_ENABLED_FOR_MODULE' => Container::get(DatixConfig::class)->usePhpMedsForm(),
];

if ($registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue()) {
    $usClaimsFieldsToAdd = [
        'tax_id',
        'employee_state_hired',
        'employment_termination_date',
    ];

    $ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'] = array_merge($ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'], $usClaimsFieldsToAdd);
}

if ($registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue()) {
    $usGeneralFieldsToAdd = [
        'con_state',
        'con_social_security_number',
        'con_middle_name',
        'con_lawson_number',
    ];

    $ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'] = array_merge($ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'], $usGeneralFieldsToAdd);
}

if ($registry->getParm('MULTI_ID_NUMBER_SECTION', 'N')->isTrue()) {
    $nwshFieldsToAdd = [
        'con_id_number',
    ];
    $nwshFieldsToRemove = [
        'con_number',
        'con_nhsno',
        'con_police_number',
    ];

    $ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'] = array_merge($ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'], $nwshFieldsToAdd);
    $ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'] = array_diff($ModuleDefs['INC']['DIF1_CON_SEARCHING_FIELDS'], $nwshFieldsToRemove);
}

// Actions
$ModuleDefs['ACT'] = [
    'MOD_ID' => MOD_ACTIONS,
    'CODE' => 'ACT',
    'NAME' => _fdtk('mod_actions_title'),
    'REC_NAME' => _fdtk('ACTName'),
    'REC_NAME_PLURAL' => _fdtk('ACTNames'),
    'REC_NAME_TITLE' => _fdtk('ACTNameTitle'),
    'REC_NAME_PLURAL_TITLE' => _fdtk('ACTNamesTitle'),
    'FK' => 'act_id',
    'TABLE' => 'ca_actions',
    'HAS_DEFAULT_LISTING' => true,
    'LIBPATH' => 'Source/actions',
    'FORMCODE' => 'ACT',
    'PERM_GLOBAL' => 'ACT_PERMS',
    'SEARCH_URL' => 'action=actionssearch',
    'FORMS' => [
        2 => ['LEVEL' => 2, 'CODE' => 'ACT'],
    ],
    'FORMCODE1' => 'ACT',
    'BASIC_FORM_FILES' => [2 => 'Source/actions/BasicForm.php'],
    'FORM_DESIGN_LEVEL_2_FILENAME' => 'UserACTSettings',
    'FIELD_ARRAY' => [
        'act_module',
        'status',
        'act_cas_id',
        'act_ddue',
        'act_dstart',
        'act_descr',
        'act_priority',
        'type',
        'act_synopsis',
        'act_from_inits',
        'act_to_inits',
        'location_id',
        'service_id',
    ],
    'STAFF_EMPL_FILTER_MAPPINGS' => [
        'location' => 'location_id',
        'service' => 'service_id',
    ],
    'TRAFFICLIGHTS_FIELDS' => [
        'act_module', 'act_priority',
    ],
    'CAN_LINK_ACTIONS' => ['INC', 'COM', 'CLA', 'AST', 'MOR'],
    'CAN_LINK_ACTIONS_CHAINS' => ['INC', 'COM', 'CLA', 'AST', 'MOR'],
    'CAN_LINK_ACTION_PLANS' => ['INC', 'COM', 'CLA', 'MOR'],

    'HARD_CODED_LISTINGS' => [
        'all' => [
            'Where' => '(1=1)',
            'NoOverdue' => true,
        ],
        'overdue' => [
            'OverdueWhere' => "ca_actions.act_ddue is not null and ca_actions.act_ddue < '" . date('Y-m-d') . "'",
            'Title' => _fdtk('overdue_actions_listing'),
        ],
    ],
    'DEFAULT_ORDER' => 'act_ddue',
    'DEFAULT_ORDER_DIRECTION' => 'DESC',
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Actions/c_dx_actions_guide.xml',

];

// Reports
$ModuleDefs['REP'] = [
    'CODE' => 'REP',
    'DUMMY_MODULE' => true,
    'NAME' => 'Reports',
    'REC_NAME' => 'report',
    'REC_NAME_PLURAL' => 'reports',
    'REC_NAME_TITLE' => 'Report',
    'REC_NAME_PLURAL_TITLE' => 'Reports',
    'TABLE' => 'reports_packaged',
];

// Document templates
$ModuleDefs['TEM'] = [
    'CODE' => 'TEM',
    'DUMMY_MODULE' => true,
    'NAME' => 'Document Templates',
    'REC_NAME' => 'document template',
    'REC_NAME_PLURAL' => 'document templates',
    'REC_NAME_TITLE' => 'Document Template',
    'REC_NAME_PLURAL_TITLE' => 'Document Templates',
    'TABLE' => 'templates_main',
];

// Dashboard
$ModuleDefs['DAS'] = [
    'MOD_ID' => MOD_DASHBOARD,
    'CODE' => 'DAS',
    'NAME' => 'Dashboard',
    'REC_NAME' => 'dashboard',
    'REC_NAME_PLURAL' => 'dashboards',
    'REC_NAME_TITLE' => 'Dashboard',
    'REC_NAME_PLURAL_TITLE' => 'Dashboards',
    'TABLE' => 'reports_dashboards',
    'PERM_GLOBAL' => 'DAS_PERMS',
    'NO_SEC_WHERE_CLAUSE' => true,
    'REMOTE_HELP' => 'http://help.datix.co.uk/?topicUri=/Content/en-GB/DatixWebHelp/Dashboard/c_dx_dashboard_guide.xml',
];

// Delegations
$ModuleDefs['DEL'] = [
    'MOD_ID' => MOD_DELEGATION,
    'CODE' => 'DEL',
    'DUMMY_MODULE' => true,
    'ICON' => 'icons/icon_ADM.png',
    'NAME' => 'Delegations',
    'REC_NAME' => 'delegation',
    'REC_NAME_PLURAL' => 'delegations',
    'REC_NAME_TITLE' => 'Delegation',
    'REC_NAME_PLURAL_TITLE' => 'Delegations',
    'TABLE' => 'delegation_rules',
    'FIELD_ARRAY' => [
        'rule_delegator_id', 'rule_delegate_id', 'rule_created_by',
        'rule_start_date', 'rule_end_date', 'rule_notice_message',
        'rule_can_receive_emails', 'rule_can_move_delegation',
        'rule_module_id', ],
    'DELEGATABLE_MODLUES' => [
        'INC' => '3', 'COM' => '1', 'CLA' => '2', 'MOR' => '48',
    ],

];

loadGenericModuleDefs();

Container::get(AppVarsHelper::class)->setGlobalAccessLevels();
