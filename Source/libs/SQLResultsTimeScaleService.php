<?php

namespace Source\libs;

use RecordLists_RecordListShell;
use app\services\udf\UdfService;
use Sanitize;
use app\models\generic\Tables;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSLinkContactsFields;
use src\system\container\facade\Container;
use UnicodeString;

use function is_array;
use function in_array;

class SQLResultsTimeScaleService
{
    public function getSQLResultsTimeScale(
        $selectfields,
        $dbtable,
        $order,
        $orderby,
        $WhereClause,
        $listnum,
        &$listdisplay,
        &$listnum2,
        &$listCount,
        &$sql,
        $join = null,
        $module = ''
    ) {
        $WhereClause = replaceAtPrompts($WhereClause, $_GET['module']);

        $_SESSION[$_GET['module']]['PROMPT']['ORIGINAL_JOIN'] = $join;
        $_SESSION[$_GET['module']]['PROMPT']['ORIGINAL_WHERE'] = $WhereClause;

        DoPromptSection(['module' => $_GET['module']]);

        if ($_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE']) {
            $WhereClause = $_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE'];
            $_SESSION[$_GET['module']]['PROMPT']['NEW_WHERE'] = '';
        }

        $selectfields = $this->addOrderByFields($orderby, $selectfields);
        $selectfields = $this->addFieldsToSelectFields($module, $selectfields);

        if (!isset($selectfields['recordid'])) {
            $selectfields['recordid'] = 'recordid';
        }

        /** @var list<string> $lfpseLinkContactsFields */
        $lfpseLinkContactsFields = [];
        /** @var list<string> $lfpseIncidentCodedFields */
        $lfpseIncidentCodedFields = [];

        foreach ($selectfields as $key => $field) {
            if (in_array($field, PSIMSIncidentCodedFields::getFields())) {
                $lfpseIncidentCodedFields[] = $field;
                unset($selectfields[$key]);

                continue;
            }
            if (in_array($field, PSIMSLinkContactsFields::getFields())) {
                $lfpseLinkContactsFields[] = $field;
                unset($selectfields[$key]);
            }
        }

        $selectFieldsWithTable = [];
        if (is_array($selectfields)) {
            foreach ($selectfields as $key => $field) {
                if (strpos($field, 'UDF_') === false) {
                    $selectFieldsWithTable[$key] = $dbtable . '.' . $field;
                } else {
                    $selectFieldsWithTable[$key] = str_replace('recordid', $dbtable . '.recordid', $field);
                }
            }
            $selectfields = implode(', ', $selectfields);
        }
        $selectFieldsWithTable = implode(', ', $selectFieldsWithTable);
        if (is_array($dbtable)) {
            $dbtable = implode(', ', $dbtable);
        }

        if (!empty($lfpseIncidentCodedFields)) {
            foreach ($lfpseIncidentCodedFields as $field) {
                $selectfields .= ' ,' . $field;
                $selectFieldsWithTable .= ',' . Tables::PSIMS_INCIDENT_CODED_FIELDS . '.' . $field;
            }
            $selectFieldsWithTable = trim($selectFieldsWithTable, ',');
            $join .= ' LEFT JOIN ' . Tables::PSIMS_INCIDENT_CODED_FIELDS . ' ON ' . Tables::PSIMS_INCIDENT_CODED_FIELDS . '.recordid = ' . Tables::INCIDENTS_MAIN . '.recordid';
        }

        if (!empty($lfpseLinkContactsFields)) {
            foreach ($lfpseLinkContactsFields as $field) {
                $selectfields .= ',' . $field;
                $selectFieldsWithTable .= ',' . Tables::PSIMS_CONTACT_FIELDS . '.' . $field;
            }
            $selectFieldsWithTable = trim($selectFieldsWithTable, ',');
            $join .= ' LEFT JOIN ' . Tables::PSIMS_CONTACT_FIELDS . ' ON ' . Tables::PSIMS_CONTACT_FIELDS . '.link_recordid = ' . Tables::INCIDENTS_MAIN . '.recordid';
        }

        $listdisplay = GetParm('LISTING_DISPLAY_NUM', 20);

        if (!$listnum) {
            $listnum = $listdisplay;
        }

        $listCount = (isset($_SESSION[$module]['RECORDLIST']) && $_SESSION[$module]['RECORDLIST'] instanceof RecordLists_RecordListShell)
            ? $_SESSION[$module]['RECORDLIST']->getRecordCount()
            : $this->getListNumAll($WhereClause, $dbtable, $join);

        $offset = Sanitize::SanitizeInt($listnum) - $listdisplay;
        $limit = $listdisplay;

        if ($offset < 0 || $offset >= $listCount) {
            $offset = 0;
            $listnum = $listdisplay;
        }

        // When sorting by ourref fields we want a natural sort (which cannot be done easily in SQL)
        // so sorting by Length of the value and then the value provides us with the same result DW-13049
        if ($orderby && substr($orderby, -7) == '_ourref') {
            $sqlSortFields = 'LEN(' . $orderby . ') ' . $order . ', ' . $orderby . ' ' . $order . ',';
        } elseif ($orderby && $orderby != 'recordid') {
            $sqlSortFields = "{$orderby} {$order},";
        } else {
            $sqlSortFields = '';
        }

        $sql = "SELECT {$selectFieldsWithTable} FROM {$dbtable} {$join}";

        if ($WhereClause) {
            $sql .= ' WHERE ' . $WhereClause;
        }

        $sql .= " ORDER BY {$sqlSortFields} recordid {$order} ";
        $sql .= " OFFSET {$offset} ROWS FETCH NEXT {$limit} ROWS ONLY ";

        $request = db_query($sql);
        $listnum2 = $listnum;

        return $request;
    }

    private function addFieldsToSelectFields($module, array $selectfields): array
    {
        global $FieldDefs;

        if (!$module) {
            return $selectfields;
        }

        foreach ($selectfields as $key => $field) {
            if (isset($FieldDefs[$module][$field])) {
                continue;
            }

            if (!(strtolower(substr($field, 0, 4)) === 'udf_') && strpos($field, 'psims') === false) {
                $selectfields[$key] = "null as '{$field}'";

                continue;
            }

            if (!preg_match('/^UDF_.*_[0-9]+$/ui', $field)) {
                continue;
            }

            $selectfields[$key] = Container::get(UdfService::class)->getUdfSelectSql($module, $field);
        }

        return $selectfields;
    }

    private function addOrderByFields($orderByFields, array $selectfields): array
    {
        if (!$orderByFields) {
            return $selectfields;
        }

        if (!is_array($orderByFields)) {
            $orderByFields = explode(',', $orderByFields);
        }

        foreach ($orderByFields as $orderByField) {
            $orderByField = UnicodeString::trim($orderByField);

            if (!isset($selectfields[$orderByField])) {
                $selectfields[] = $orderByField;
            }
        }

        return $selectfields;
    }

    private function getListNumAll($WhereClause, $dbtable, $join): int
    {
        global $dtxdebug;

        if (empty($WhereClause)) {
            return 0;
        }

        $whereString = !empty($WhereClause) ? "WHERE {$WhereClause}" : '';
        $sqlnum = "
            SELECT count(*) as listnumall
            FROM {$dbtable} WITH (NOLOCK)
            {$join}
            {$whereString}
        ";

        $requestnum = db_query($sqlnum, true);

        if ($requestnum === false) {
            fatal_error(_fdtk('search_errors') . $dtxdebug ? $sqlnum : '');
        }

        if ($row = db_fetch_array($requestnum)) {
            return (int) $row['listnumall'];
        }

        return 0;
    }
}
