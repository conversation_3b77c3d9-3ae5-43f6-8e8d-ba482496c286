<?php

use app\controllers\users\PendingUsers;
use src\admin\controllers\PsimsTriggerMappingsTemplateController;
use src\admin\controllers\SafeguardingLocalAuthorityController;
use src\admin\filters\SubAdminOrCanEditSecurityGroupsAndProfilesFilter;
use src\admin\model\specifications\OverdueEmailsSpecification;
use src\contacts\controllers\TpocController;
use src\framework\controller\LoggedInFilter;
use src\framework\controller\Request;
use src\framework\session\UserSessionFactory;
use src\logger\DatixLogger;
use src\system\container\ContainerFactory;
use src\system\container\facade\Container;
use Teapot\StatusCode\Http;

/**
 * @return never
 */
function httpRequest(): void
{
    $action = $_GET['type'];

    $userSession = (new UserSessionFactory())->create();
    $isFullAdmin = $userSession->isFullAdmin();
    $isLocalAdmin = $userSession->isLocalAdmin();

    $overdueEmailsAllowed = Container::get(OverdueEmailsSpecification::class)->isSatisfied();

    $requestType = [
        // dropdown codes
        'getcodes' => [
            'controller' => src\codeselection\controllers\CodesController::class,
        ],
        'selectfunction' => ['Source/libs/SelectFunctions.php', 'getSelectFunction'],

        // Contact match on Patient number
        'contactlist' => [
            'controller' => src\contacts\controllers\ContactCheckController::class,
        ],

        // email templates
        'gettemplatetypes' => [
            'controller' => src\emailtemplates\controllers\EmailTemplateTypeController::class,
            'filters' => [
                src\admin\filters\ManageEmailTemplatesFilter::class,
            ],
        ],
        'getemaildetails' => [
            'controller' => src\emailtemplates\controllers\EmailDetailController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],

        // overdue emails
        'getoverdueemailrecipients' => [
            'Source/libs/OverdueEmails.php',
            'GetRecipients',
            'condition' => $overdueEmailsAllowed,
        ],
        'sendoverdueemails' => [
            'Source/libs/OverdueEmails.php',
            'SendOverdueEmails',
            'condition' => $overdueEmailsAllowed,
        ],

        // udf searching
        'addudfsearchrow' => ['Source/libs/UDF.php', 'AddUDFRow'],

        // injury table
        'insertinjuryrow' => [
            'controller' => src\contacts\controllers\InjuryDetailsController::class,
        ],

        // contact numbers table row
        'getcontactnumberhtml' => [
            'controller' => src\contacts\controllers\ContactIdNumbersController::class,
        ],

        // absense table row
        'getlosttimerowhtml' => [
            'controller' => src\contacts\controllers\LostAndRestrictedTimeController::class,
        ],

        // absense table row
        'getrestrictedtimerowhtml' => [
            'controller' => src\contacts\controllers\LostAndRestrictedTimeController::class,
        ],

        // property table
        'insertpropertyrow' => [
            'controller' => src\contacts\controllers\PropertyDetailsController::class,
        ],
        // adding generic sections to form
        'adddynamicsection' => ['Source/libs/Subs.php', 'addDynamicSection'],
        'getrowlist' => ['Source/libs/Subs.php', 'getRowList'],

        // change expanding fields/sections
        'changeexpand' => [
            'controller' => src\formdesign\controllers\ChangeExpandController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        // change section a field appears in
        'changefieldsection' => [
            'Source/AdminSetup.php',
            'ChangeFieldSection',
            'condition' => isset($_SESSION['logged_in']),
        ],

        // Record Locking
        'checklock' => [
            'Source/libs/RecordLocks.php',
            'CheckLock',
            'condition' => isset($_SESSION['logged_in']),
        ],
        'resettimer' => ['Source/libs/RecordLocks.php', 'ResetTimer'],
        'getservertimeout' => [
            'controller' => src\timeout\controllers\TimeoutController::class,
        ],
        'unlockrecord' => [
            'Source/libs/RecordLocks.php',
            'UnlockRecord_ajax',
            'condition' => isset($_SESSION['logged_in']),
        ],

        // Staff responsibilities
        'staffrespresponse' => [
            'Source/incidents/StaffRespResponse.php',
            'StaffRespResponse',
            'condition' => isset($_SESSION['logged_in']) && $isFullAdmin,
        ],

        // Main page
        'setglobal' => [
            'Source/libs/Subs.php',
            'SetGlobalAJAX',
            'condition' => isset($_SESSION['logged_in']) && $isFullAdmin,
        ],
        'setsession' => ['Source/libs/Subs.php', 'SetSessionAJAX'],

        // Security groups
        'unlink_user_groups' => [
            'Source/security/SecurityUserGroupLinks.php',
            'UnlinkUserGroups',
            'condition' => $isFullAdmin || $isLocalAdmin,
        ],
        'unlink_profiles' => [
            'Source/security/SecurityUserGroupLinks.php',
            'UnlinkProfiles',
            'filters' => [
                SubAdminOrCanEditSecurityGroupsAndProfilesFilter::class,
                LoggedInFilter::class,
            ],
        ],

        // Risk Rating
        'getratinglevel' => [
            'Source/libs/RiskRatingBase.php',
            'GetRatingLevel',
        ],

        // Saved queries
        'getquerywhere' => [
            'controller' => src\savedqueries\controllers\SavedQueriesController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],

        // Email alerts
        'sendemailalerts' => ['Source/libs/Email.php', 'EmailAlertsService'],

        // Group report
        'usergrouprepresponse' => [
            'controller' => src\admin\controllers\UserGroupReportController::class,
            'filters' => [
                src\admin\filters\FullAdminFilter::class,
            ],
        ],

        // Dashboards
        'getwidgetcontents' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'editwidget' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'movewidget' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'setwidgetorders' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'addtomydashboard' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'createnewdashboard' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'setdefaultdashboard' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'updatedashboardsettings' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'deletedashboard' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'editdashboard' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'getdashboardhtml' => [
            'controller' => src\dashboard\controllers\DashboardController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],

        // Reporting
        'exporttopdfoptions' => [
            'Source/reports.php',
            'ExportToPDFOptions',
            'condition' => isset($_SESSION['logged_in']),
        ],
        // KO41
        'ko41export' => [
            'controller' => src\ko41\controllers\KO41ExportOptionsController::class,
            'filters' => [
                src\admin\filters\CaptureConfigurationFilter::class,
            ],
        ],
        // NCDS
        'ncdsexport' => [
            'controller' => src\ncds\controllers\NCDSExportOptionsController::class,
            'filters' => [
                src\admin\filters\NCDSExportFilter::class,
            ],
        ],
        'getTrafficLightOptions' => [
            'controller' => src\reports\controllers\ReportDesignFormController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'getExportFormHTML' => [
            'controller' => src\reports\controllers\ReportDesignFormController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'exportreport' => [
            'controller' => src\reports\controllers\ReportController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'addreportprompt' => [
            'controller' => src\reports\controllers\ReportController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'addtomyreports' => [
            'controller' => src\reports\controllers\ReportController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        // CodeTags
        'GetCodeTagLinkEditDropdown' => [
            'controller' => src\admin\controllers\CodeTagLinkController::class,
            'filters' => [
                src\admin\filters\AssignCodeTagsFilter::class,
            ],
        ],
        'generatein05' => [
            'controller' => src\admin\controllers\IN05Controller::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'getanddeletepngfile' => [
            'controller' => src\reports\controllers\ReportController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],

        // Risk matrix mapping
        'generateriskmatrix' => [
            'Source/setups/RiskMatrix.php',
            'generateRiskMatrix',
            'condition' => isset($_SESSION['logged_in']),
        ],

        'generatesacscore' => [
            'Source/setups/SacScore.php',
            'generateSacScore',
            'condition' => isset($_SESSION['logged_in']),
        ],

        // Listing checkboxes
        'flagrecord' => [
            'Source/classes/RecordLists/RecordListClass.php',
            [
                'RecordLists_RecordListShell',
                'FlagSessionRecordList',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],
        'flagallrecords' => [
            'Source/classes/RecordLists/RecordListClass.php',
            [
                'RecordLists_RecordListShell',
                'FlagAllSessionRecordList',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],
        'unflagrecord' => [
            'Source/classes/RecordLists/RecordListClass.php',
            [
                'RecordLists_RecordListShell',
                'UnFlagSessionRecordList',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],
        'unflagallrecords' => [
            'Source/classes/RecordLists/RecordListClass.php',
            [
                'RecordLists_RecordListShell',
                'UnFlagAllSessionRecordList',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],

        // set flag state for session recordlists
        'setsessionflagstate' => [
            'Source/libs/Subs.php',
            [
                'RecordLists_RecordListShell',
                'SetSessionFlagState',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],
        'clearsessionrecordlist' => [
            'Source/libs/Subs.php',
            [
                'RecordLists_RecordListShell',
                'ClearSessionRecordList',
            ],
            'condition' => isset($_SESSION['logged_in']),
        ],

        // cascade data up through parent fields
        'getparentvalue' => ['Source/libs/Subs.php', 'GetParentValue'],

        // add new/edit stage history dialogue
        'editstagehistory' => [
            'controller' => src\claims\controllers\StageHistoryController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'savestagehistory' => [
            'controller' => src\claims\controllers\StageHistoryController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'deletestagehistory' => [
            'controller' => src\claims\controllers\StageHistoryController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'renderfields' => [
            'controller' => src\formdesign\controllers\RenderFieldsController::class,
        ],
        // Organisation match on CLA1
        'organisationlist' => [
            'controller' => src\organisations\controllers\OrganisationsController::class,
        ],

        'duplicaterecordcheck' => [
            'controller' => src\generic\controllers\DuplicateRecordCheckController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'generatedatixtablecell' => [
            'controller' => src\datixtable\controllers\DatixtableCellController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class, ],
        ],
        'getdynamicfielddata' => [
            'controller' => src\datixtable\controllers\DatixtableCellController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class, ],
        ],
        'getprogressnotestypefield' => [
            'controller' => src\progressnotes\controllers\ProgressNotesController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'getcontactchoicearray' => [
            'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'getorganisationchoicesarray' => [
            'controller' => src\organisations\controllers\OrganisationsController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'addnewtpocrecord' => [
            'controller' => TpocController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'pendingusers' => [
            'controller' => PendingUsers::class,
            'condition' => isset($_SESSION['logged_in']), // Uses condition instead of filter as the filter updates the session start time
        ],
        'generatepsimstriggerdata' => [
            'controller' => PsimsTriggerMappingsTemplateController::class,
            'filters' => [
                src\framework\controller\LoggedInFilter::class,
            ],
        ],
        'resendreferral' => [
            'controller' => SafeguardingLocalAuthorityController::class,
            'condition' => isset($_SESSION['logged_in']),
        ],
    ];

    try {
        if (isset($requestType[$action]['controller']) && class_exists($requestType[$action]['controller'])
            && in_array(src\framework\controller\Controller::class, class_parents($requestType[$action]['controller']))) {
            // use new Controller class structure
            $loader = new src\framework\controller\Loader();
            $controller = $loader->getController($requestType[$action]);

            echo $controller->doAction($action);
        } else {
            // Check conditions for non controller routes and throw URLNotFoundException if it set to false
            if (isset($requestType[$action]['condition']) && !$requestType[$action]['condition']) {
                throw new \URLNotFoundException();
            }

            $includeFile = $requestType[$action][0];
            $executeFunction = $requestType[$action][1];

            require_once $includeFile;

            if ($executeFunction != '') {
                $executeFunction();
            }
        }

        $request = new Request();

        // Only inject javascript into the response if it is NOT of these type of requests as it will cause invalid return array.
        if (!in_array($_GET['responsetype'] ?? null, ['json', 'autocomplete'], true)
            && !in_array($_POST['responseType'] ?? null, ['json', 'autocomplete'], true)
            && $request->getParameter('type') !== 'exportreport'
        ) {
            echoJSFunctions();
        }
    } catch (Throwable $e) {
        http_response_code(Http::INTERNAL_SERVER_ERROR);
        header('Content-type: application/json');

        /** @var DatixLogger $logger */
        (new ContainerFactory())->create()['logger']->critical('Error during ajax request', ['exception' => $e, 'request' => $action]);

        echo json_encode(['error:' => $e->getMessage()], JSON_THROW_ON_ERROR);
    }

    obExit();
}
