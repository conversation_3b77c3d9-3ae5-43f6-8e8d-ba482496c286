<?php
// Module constants
const MOD_COUNT = 40; // Number of modules
const MOD_CLAIMS = 1; // Claims module
const MOD_COMPLAINTS = 2; // Complaints module
const MOD_INCIDENTS = 3; // Incidents module
const MOD_CONTACTS = 4; // Contacts module
const MOD_ASSESS = 6; // Controls assurance module
const MOD_ACTIONS = 7; // Actions module
const MOD_TRAINING = 9; // Training module
const MOD_ADMIN = 10; // Admin (dummy) module
const MOD_RFI = 11; // RFI module
const MOD_TIME = 12; // Time module
const MOD_ELEMENTS = 18; // Elements module (Standards)
const MOD_PROMPTS = 19; // Prompts module (Standards)
const MOD_INQUESTS = 20; // Inquest module
const MOD_DIARY = 21; // Diary module
const MOD_COSTS = 23; // Assets (Equipment) module
const MOD_ASSETS = 24; // Assets (Equipment) module
const MOD_DISTRIBUTION = 25; // Distribution lists
const MOD_CIV = 27; // Clinical outcomes
const MOD_PERFORMANCE = 28; // Performance module
const MOD_INDICATORS = 29; // Performance indicators module
const MOD_DASHBOARD = 30; // Dashboard
const MOD_MEDICATIONS = 31; // Medications module
const MOD_PAYMENTS = 37; // Payments
const MOD_TODO = 38; // To Do List
const MOD_LOCATIONS = 39; // Locations
const MOD_POLICIES = 45; // Policies module (claims)
const MOD_ORGANISATIONS = 46; // Organisations module
const MOD_DELEGATION = 47; // (dummy) Delegations module
const MOD_MORTALITY = 48; // Mortality
const MOD_USERS = 49; // Mortality
const MOD_REDRESS = 50; // Redress
const MOD_SAFEGUARDING = 51; // Safeguarding
const MOD_ICONWALL = 52; // iconwall
// DATIX Globals Defaults
const LOGIN_TRY = 3;
const PWD_EXPIRY_DAYS = 90;
const PWD_MIN_LENGTH = 6;
const PWD_UNIQUE = 'Y';
const PWD_LOWERCASE = 0;
const PWD_UPPERCASE = 0;
const PWD_NUMBERS = 0;
const PWD_SPECIAL = 0;
const VAT_RATE = 17.5;
// Incidents permission levels
const DIF1 = 1;              // DIF1 input only
const DIF2_READ_ONLY = 2;    // DIF2 read-only access
const DIF2_NO_APPROVAL = 3;  // DIF2 access only - no review of DIF1 forms
const DIF2 = 4;              // DIF2 access and review of DIF1 forms
// Risks permission levels
const RISK1 = 1;              // RISK1 input only
const RISK2_READ_ONLY = 2;    // RISK2 read-only access
const RISK2_NO_APPROVAL = 3;  // RISK2 access only - no review of RISK2 forms
const RISK2 = 4;              // RISK2 access and review of RISK1 forms
// CLA permission levels
const CLA1 = 1;              // CLA1 input only
const CLA2_READ_ONLY = 2;    // CLA2 read-only access
const CLA2_NO_APPROVAL = 3;  // CLA2 access only - no review of CLA1 forms
const CLA2 = 4;              // CLA2 access and review of CLA1 forms
const RM = 5;                // Final approval of DIF2, RISK2, PAL2, CLA1

const BTN_CANCEL = 16;

// Security group types - Those are bitwise values!!
const GRP_TYPE_ACCESS = 1;
const GRP_TYPE_EMAIL = 2;
const GRP_TYPE_DENY_ACCESS = 4;

const GRP_TYPE_ACCESS_AND_EMAIL = 3;

// Other
const DROPDOWN_WIDTH_DEFAULT = 300; // default dropdown width
