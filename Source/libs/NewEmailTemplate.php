<?php

namespace Source\libs;

use app\models\treeFields\TreeFieldCodeRetrieverFactory;
use app\services\systemConfiguration\SystemConfigurationService;
use DatixDBQuery;
use src\email\AbstractEmailTemplate;
use src\emailtemplates\model\EmailTemplateContent;
use src\emailtemplates\model\EmailTemplateModelFactory;
use src\emailtemplates\service\EmailTemplatesDefaultRepositoryFactory;
use src\framework\query\Query;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;
use src\system\database\field\CodeField;
use src\system\database\field\DateField;
use src\system\database\field\MoneyField;
use src\system\database\field\MultiCodeField;
use src\system\database\field\TreeField;
use src\system\database\field\YesNoField;
use src\system\language\LanguageSession;
use src\system\moduledefs\ModuleDefs;

class NewEmailTemplate extends AbstractEmailTemplate
{
    public $TemplateData = [];
    public $RecordID;
    public $Module = '';
    public $RecordData;
    public $ExtraData;
    public $SubjectMatchArray = [];
    public $BodyMatchArray = [];
    public $EmailSubject;
    public $EmailBody;

    /** @var Registry */
    protected $registry;

    /** @var array */
    protected $content = [];

    public function __construct($TemplateID, $module = null, $templateType = null)
    {
        $this->registry = Container::get(Registry::class);

        if ($TemplateID !== '' && $TemplateID !== 'DEFAULT') {
            $templateMapper = (new EmailTemplateModelFactory())->getMapper();
            $template = $templateMapper->find($TemplateID);

            if ($template) {
                $this->TemplateData = $template->getVars();
                $this->Module = $template->emt_module;
                $this->content = $template->getContent()->toArray();
            }
        }

        if (!$this->TemplateData) {
            $emailId = "EMT_{$module}_{$templateType}";
            $defaultTemplateRepo = (new EmailTemplatesDefaultRepositoryFactory())->create();
            $this->TemplateData = $defaultTemplateRepo->getTemplateByType($emailId);
            $this->Module = $this->TemplateData['emt_module'];
            if (isset($this->TemplateData['recordid'])) {
                $this->content = $defaultTemplateRepo->getContentByTemplateId($this->TemplateData['recordid']);
            }
        }
    }

    public function DefineExtraData($data)
    {
        $this->ExtraData = $data;
    }

    public function FindDataPoints($text)
    {
        preg_match_all('/<datix [^>]*>/iu', $text, $Matches);

        $MatchArray = [];

        foreach ($Matches[0] as $id => $Match) {
            $ParamList = [];
            preg_match_all("/([^ ><=\"]+)[ ]?=[ ]?(?:\"|')([^ ><=\"']+)(?:\"|')/iu", $Match, $Params);

            foreach ($Params[0] as $paramId => $Param) {
                $ParamList[$Params[1][$paramId]] = $Params[2][$paramId];
            }

            $ParamList['MatchString'] = $Match;
            $MatchArray[$id] = $ParamList;
        }

        return $MatchArray;
    }

    public function GetRecordData($RecordID)
    {
        $ModuleDefs = Container::get(ModuleDefs::class);

        $this->RecordID = $RecordID;

        $FieldList = [];
        $ModuleList[] = $this->TemplateData['emt_module'];
        $TableList = [];

        foreach ($this->SubjectMatchArray as $Match) {
            $this->processMatch($Match, $ModuleList, $FieldList, $TableList, $WhereList);
        }

        foreach ($this->BodyMatchArray as $Match) {
            $this->processMatch($Match, $ModuleList, $FieldList, $TableList, $WhereList);
        }

        if (!empty($FieldList)) {
            array_unique($FieldList);

            $sql = 'SELECT ' . implode(', ', $FieldList) . ' FROM ' . $ModuleDefs[$this->TemplateData['emt_module']]['TABLE'];

            // Include other tables and JOIN with LEFT OUTER JOIN to main table
            foreach ($TableList as $i => $table) {
                if (!empty($WhereList[$i])) {
                    $sql .= ' LEFT OUTER JOIN ' . $table . ' ON ' . $WhereList[$i];
                }
            }

            $sql .= ' WHERE ' . $ModuleDefs[$this->TemplateData['emt_module']]['TABLE'] . '.recordid = ' . $RecordID;

            $data = DatixDBQuery::PDO_fetch($sql, []);
        }

        return $data;
    }

    public function ConstructEmailToSend($RecordID, $language = null)
    {
        $fallbackLanguage = Container::get(SystemConfigurationService::class)->getSystemConfigurationDto()->getSystemLanguage();

        /** @var EmailTemplateContent $content */
        $content = $this->content[$language] ?? $this->content[$fallbackLanguage] ?? $this->content[LanguageSession::ENGLISH_UK];
        if (!$content) {
            $this->EmailSubject = '';
            $this->EmailBody = '';

            return;
        }

        if ($content instanceof EmailTemplateContent) {
            $subject = $content->getSubject();
            $body = $content->getBody();
        } else {
            $subject = $this->removeXmlTags($content['subject']);
            $body = $this->removeXmlTags($content['body']);
        }

        $this->SubjectMatchArray = $this->FindDataPoints($subject);
        $this->BodyMatchArray = $this->FindDataPoints($body);

        if ($RecordID) {
            $this->RecordData = $this->GetRecordData($RecordID);
        }

        $this->EmailSubject = $this->ReplaceDataPoints($subject, $this->SubjectMatchArray, $language);
        $this->EmailBody = $this->ReplaceDataPoints($body, $this->BodyMatchArray, $language);
    }

    /**
     * Replaces a data point in the text for the default case of `other` match type.
     *
     * This handles text and html messages. When the type of email is html, converts new lines to brs.
     */
    public static function ReplaceDataPointMatchTypeOtherDefaultCase(
        string $text,
        array $Match,
        array $ExtraData,
        bool $isHTML
    ): string {
        if (!$ExtraData[$Match['other']]) {
            return $text;
        }

        /** string value */
        $value = $ExtraData[$Match['other']];

        if ($isHTML) {
            $value = nl2br($value);
        }

        return str_replace($Match['MatchString'], $value, $text);
    }

    /**
     * @param $text
     * @param $matchArray
     * @param $language
     *
     * @return mixed
     *
     * @throws InvalidDataException
     */
    public function ReplaceDataPoints($text, $matchArray, $language)
    {
        $scripturl = $this->registry->getScriptUrl() ?? getenv('BASE_URL');
        $ModuleDefs = $this->registry->getModuleDefs();
        $FieldDefs = $this->registry->getFieldDefs();

        $userSession = (new UserSessionFactory())->create();
        foreach ($matchArray as $match) {
            switch ($match['type']) {
                case 'other':
                    if ($match['other']) {
                        switch ($match['other']) {
                            case 'hyperlink':
                                $text = str_replace($match['MatchString'], $scripturl . '?action=' . $ModuleDefs[$this->Module]['ACTION'] . '&recordid=' . $this->RecordID, $text);

                                break;
                            case 'fullname':
                                $text = str_replace($match['MatchString'], $userSession->getCurrentUser()->getFullname(), $text);

                                break;
                            case 'local_authority':
                                $text = str_replace(
                                    $match['MatchString'],
                                    $scripturl
                                    . '?action='
                                    . $ModuleDefs[$this->Module]['LOCAL_AUTHORITY_LINK_FORM']
                                    . '&id='
                                    . urlencode(base64_encode($this->ExtraData['safeguarding_main_id']))
                                    . '&document_id='
                                    . urlencode(base64_encode($this->ExtraData['document_id'])),
                                    $text,
                                );

                                break;
                            default:
                                $text = self::ReplaceDataPointMatchTypeOtherDefaultCase($text, $match, $this->ExtraData, $this->isHTMLEmail());
                        }
                    }

                    break;
                case 'field':
                default:
                    if ($match['field']) {
                        $data = $this->RecordData[$match['field']];
                        $fieldModule = ($match['module'] ?: $this->Module);
                        $table = $ModuleDefs[$fieldModule]['TABLE'];
                        $field = $FieldDefs[$table . '.' . $match['field']];

                        if ($field instanceof TreeField) {
                            $type = '';
                            if (str_contains($match['field'], 'location')) {
                                $type = 'location';
                            }
                            if (str_contains($match['field'], 'service')) {
                                $type = 'service';
                            }
                            if ($type !== '') {
                                $data = (new TreeFieldCodeRetrieverFactory())
                                    ->create($type)
                                    ->getBreadcrumb((int) $data, (int) $language);
                            }
                        } elseif ($field instanceof MultiCodeField) {
                            $codes = $field->getCodes(new Query());

                            $dataItems = [];
                            foreach (explode(' ', $data) as $dataItem) {
                                if (isset($codes[$dataItem])) {
                                    $dataItems[] = $codes[$dataItem]->getDescription();
                                } else {
                                    $dataItems[] = $dataItem;
                                }
                            }

                            $data = implode(', ', $dataItems);
                        } elseif ($field instanceof YesNoField) {
                            $YNArray = ['Y' => _fdtk('yes'), 'N' => _fdtk('no')];
                            $data = $YNArray[$data];
                        } elseif ($field instanceof DateField) {
                            $data = formatDateForDisplay($data);
                        } elseif ($field instanceof MoneyField) {
                            $data = FormatMoneyVal($data);
                        } elseif ($field instanceof CodeField) {
                            if ($data) {
                                $codes = $field->getCodes((new Query())->where(['code' => $data]));
                                if (isset($codes[$data])) {
                                    $data = $codes[$data]->getDescription($language);
                                }
                            }
                        }

                        $text = str_replace($match['MatchString'], $data, $text);
                    }
            }
        }

        return $text;
    }

    public function getContentType()
    {
        return $this->TemplateData['emt_content_type'];
    }

    public function getBody($data)
    {
        $language = $data[AbstractEmailTemplate::LANGUAGE_KEY] ?? null;
        $this->DefineExtraData($data);
        $this->ConstructEmailToSend($data['recordid'], $language);

        return $this->EmailBody;
    }

    public function getSubject($data)
    {
        $language = $data[AbstractEmailTemplate::LANGUAGE_KEY] ?? null;
        $this->DefineExtraData($data);
        $this->ConstructEmailToSend($data['recordid'], $language);

        return $this->EmailSubject;
    }

    public function isHTMLEmail()
    {
        return $this->TemplateData['emt_content_type'] == 'HTML' ? true : false;
    }

    private function processMatch(&$Match, &$ModuleList, &$FieldList, &$TableList, &$WhereList)
    {
        $ModuleDefs = Container::get(ModuleDefs::class);

        if ($Match['type'] == 'other') {
            return;
        }

        if (!empty($Match['module'])) {
            $ModuleList[] = $Match['module'];
        }

        if ($Match['field']) {
            $module = ($Match['module'] ?: $this->TemplateData['emt_module']);

            // this all obviously needs to be replaced by FieldDef object when refactored.
            $TableAdded = false;
            if ($Match['field'] == 'recordid') {
                $Match['field'] = $ModuleDefs[$module]['TABLE'] . '.recordid';
                $TableAdded = true;
            }

            $field_fmt = getFieldFormat($Match['field'] ?? null, $module, $Match['table'] ?? null);

            // Only include fields defined in field_formats
            if (!empty($field_fmt) || $TableAdded) {
                $current_table = $TableAdded ? $ModuleDefs[$module]['TABLE'] : $field_fmt['fmt_table'];

                if ($current_table == $ModuleDefs[$this->TemplateData['emt_module']]['TABLE'] || in_array($current_table, $TableList)) {
                    if ($TableAdded) {
                        $FieldList[] = $Match['field'];
                    } else {
                        $FieldList[] = $current_table . '.' . $Match['field'];
                    }
                }
            }
        }
    }

    private function removeXmlTags($string)
    {
        $string = str_replace('<xml>', '', $string);

        return str_replace('</xml>', '', $string);
    }
}
