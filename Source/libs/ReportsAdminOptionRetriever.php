<?php

namespace Source\libs;

use app\models\framework\config\DatixConfig;
use app\models\generic\Tables;
use app\services\module\ModuleResolver;
use src\component\field\FieldLabelRepository;
use src\incidents\model\IncidentsFields;
use src\system\database\FieldInterface;
use src\system\language\LanguageService;

use function in_array;

class ReportsAdminOptionRetriever
{
    private const IS_TREE_TAG_REGEX = '/_tag_\d+$/ui';
    private const TREE_TAG_GET_MAIN_FIELD_REGEX = '/_tag_\d+$/';
    private const FIELDS_IN_MULTIPLE_FIELDSETS = [
        Tables::INCIDENT_PSIMS_RESPONSE . '.' . IncidentsFields::WARNINGS,
    ];

    /** @var LanguageService */
    private $languageService;

    /** @var FieldLabelRepository */
    private $fieldLabelRepository;

    /** @var DatixConfig */
    private $datixConfig;
    private ModuleResolver $moduleResolver;

    public function __construct(
        LanguageService $languageService,
        FieldLabelRepository $fieldLabelRepository,
        DatixConfig $datixConfig,
        ModuleResolver $moduleResolver
    ) {
        $this->languageService = $languageService;
        $this->fieldLabelRepository = $fieldLabelRepository;
        $this->datixConfig = $datixConfig;
        $this->moduleResolver = $moduleResolver;
    }

    /**
     * @param $moduleTable
     */
    public function getOptionLabel($moduleTable, FieldInterface $field): string
    {
        if ($this->isTreeTagFieldWithName($field)) {
            return $field->getLabel();
        }

        $fieldset = $field->getFieldset();
        $table = $field->getTable();
        $fieldName = $field->getName();

        $module = $this->moduleResolver->getModuleFromTable($moduleTable);
        $domain = $this->languageService->getModuleDomain($module);

        // Get field label
        $fieldLabel = $this->fieldLabelRepository->getLabel($table, $fieldName, $domain, $fieldset);
        if ($fieldLabel->getFieldLabel() === '' && !empty($field->getLabel())) {
            $fieldLabel->setFieldLabel($field->getLabel());
        }

        // Add field suffix
        if (method_exists($field, 'getLabelSuffix') && $field->getLabelSuffix()) {
            $fieldLabel->setFieldLabel($fieldLabel->getFieldLabel() . $field->getLabelSuffix());
        }

        // Add fieldset suffix (if field is not from module's main table)
        $fieldInMainTable = in_array($table, [$moduleTable, 'vw_actions_summary', 'compl_isd_issues']);

        return (string) ($fieldInMainTable ? $fieldLabel : $fieldLabel->withFieldsetSuffix());
    }

    public function isTreeTagFieldWithName(FieldInterface $field): bool
    {
        $fieldName = $field->getName();
        $isTagField = $this->isTreeTagOptionField($fieldName);

        if ($isTagField && !empty($field->getLabel())) {
            return true;
        }

        return false;
    }

    public function isTreeTagOptionField(string $fieldName): bool
    {
        return (bool) preg_match(self::IS_TREE_TAG_REGEX, $fieldName);
    }

    public function getMainFieldFromTagFieldName(string $fieldName): string
    {
        return (string) preg_replace(self::TREE_TAG_GET_MAIN_FIELD_REGEX, '', $fieldName);
    }

    public function getTagIdFromName(string $fieldName): int
    {
        $parts = explode('_', $fieldName) ?? [];

        return (int) end($parts);
    }

    public function isOptionExcluded(FieldInterface $field): bool
    {
        if ($this->isOptionExcludedByVanessaLaw($field)) {
            return true;
        }

        return in_array(
            $field->getFullyQualifiedName(),
            self::FIELDS_IN_MULTIPLE_FIELDSETS,
        );
    }

    private function isOptionExcludedByVanessaLaw(FieldInterface $field): bool
    {
        return $field->getFullyQualifiedName() === 'incident_equipment_link.serial_number'
                && !$this->datixConfig->getVanessaLawEnabled();
    }
}
