<?php

use app\models\generic\valueObjects\Module;
use app\services\idGenerator\UpdateIdGenerator;
use src\framework\controller\Response;
use src\redress\emails\RedressEmailTypes;
use src\safeguarding\emails\SafeguardingEmailTypes;
use src\system\container\facade\Container;

function GetMenuArray()
{
    return [
        ['label' => 'New Template', 'link' => 'action=emailtemplate'],
        ['label' => 'List All Templates', 'link' => 'action=listemailtemplates'],
        ['label' => 'List Default Templates', 'link' => 'action=listdefaultemailtemplates'],
        ['label' => 'Configure Email Templates', 'link' => 'action=configureemailtemplates'],
    ];
}

/**
 * DeleteTemplate deletes a template record from the database.
 *
 * @return never
 */
function DeleteTemplate($paramArray): void
{
    global $scripturl;

    $recordid = $paramArray['recordid'];

    $sql = "DELETE FROM EMAIL_TEMPLATES WHERE recordid={$recordid}";
    DatixDBQuery::PDO_query($sql);

    AddSessionMessage('INFO', _fdtk('email_template_deleted'));

    $location = "{$scripturl}?action=listemailtemplates";
    Container::get(Response::class)->redirect($location);
}

/**
 * Saves an e-mail template record to the database or updates an existing record.
 *
 * @param array $paramArray
 *
 * @return never
 *
 * @todo fully document array shape
 */
function SaveTemplate($paramArray): void
{
    global $scripturl;

    $recordid = $paramArray['recordid'];
    $posted = $paramArray['posted'];

    $posted['emt_subject'] = '<xml>' . $posted['emt_subject'] . '</xml>';
    $posted['emt_text'] = '<xml>' . $posted['emt_text'] . '</xml>';

    $updateIdGenerator = (new UpdateIdGenerator());

    if ($recordid) {
        $sql = 'UPDATE EMAIL_TEMPLATES SET
        emt_module = :emt_module,
        emt_name = :emt_name,
        emt_type = :emt_type,
        emt_notes = :emt_notes,
        emt_subject = :emt_subject,
        emt_text = :emt_text,
        emt_content_type = :emt_content_type,
        updateid = :updateid,
        updatedby = :updatedby,
        updateddate = :updateddate
        WHERE recordid = :recordid';

        DatixDBQuery::PDO_query($sql, [
            'emt_module' => $posted['emt_module'],
            'emt_name' => $posted['emt_name'],
            'emt_type' => $posted['emt_type'],
            'emt_notes' => $posted['emt_notes'],
            'emt_subject' => $posted['emt_subject'],
            'emt_text' => $posted['emt_text'],
            'emt_content_type' => $posted['emt_content_type'],
            'updateid' => $updateIdGenerator->generateUpdateId($posted['updateid']),
            'updatedby' => $_SESSION['initials'],
            'updateddate' => date('Y-m-d H:i:s'),
            'recordid' => $recordid,
        ]);
    } else {
        $recordid = DatixDBQuery::PDO_build_and_insert(
            'email_templates',
            [
                'emt_module' => $posted['emt_module'],
                'emt_name' => $posted['emt_name'],
                'emt_type' => $posted['emt_type'],
                'emt_notes' => $posted['emt_notes'],
                'emt_subject' => $posted['emt_subject'],
                'emt_text' => $posted['emt_text'],
                'emt_content_type' => $posted['emt_content_type'],
                'emt_author' => $_SESSION['initials'],
                'updateid' => $updateIdGenerator->generateUpdateId($posted['updateid']),
                'updatedby' => $_SESSION['initials'],
                'updateddate' => date('d-M-Y H:i:s'),
            ],
        );
    }

    AddSessionMessage('INFO', 'Template has been saved successfully');

    // redirect back to display 'saved' message.
    $location = "{$scripturl}?action=emailtemplate&pagetype=edittemplate&recordid={$recordid}&saved=1";
    Container::get(Response::class)->redirect($location);
}

/**
 * SaveTemplateOptions saves the template options (which template to use as default in which situation.
 *
 * @return never
 */
function SaveTemplateOptions($parameterArray): void
{
    global $scripturl;

    $posted = $parameterArray['posted'];

    $DefaultArray = include_config_array();

    foreach ($DefaultArray as $module => $GlobalArray) {
        foreach ($GlobalArray as $globalval => $aDetails) {
            $num = DatixDBQuery::PDO_fetch('SELECT count(*) as num FROM GLOBALS WHERE parameter = :parameter', ['parameter' => $globalval], \PDO::FETCH_COLUMN);

            if ($num) {
                $sql = 'UPDATE GLOBALS SET parmvalue = :parmvalue WHERE parameter = :parameter';
                DatixDBQuery::PDO_query($sql, ['parmvalue' => $posted[$globalval], 'parameter' => $globalval]);
            } else {
                DatixDBQuery::PDO_build_and_insert(
                    'globals',
                    [
                        'parameter' => $globalval,
                        'description' => $aDetails['title'],
                        'parmvalue' => $posted[$globalval],
                    ],
                );
            }
        }
    }

    AddSessionMessage('INFO', _fdtk('email_template_configured'));

    // redirect back to display 'saved' message.
    $location = "{$scripturl}?action=configureemailtemplates";
    Container::get(Response::class)->redirect($location);
}

/**
 * include_config_array returns an array of globals (so new ones only need to be added here to appear everywhere).
 */
function include_config_array()
{
    return [
        'INC' => [
            'EMT_INC_ACK' => ['title' => 'Acknowledgment ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Acknowledge'],
            'EMT_INC_NOT' => ['title' => 'Notifications ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            'EMT_INC_FBK' => ['title' => 'Feedback ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Feedback'],
            'EMT_INC_NHA' => ['title' => 'New handler ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewHandler'],
            'EMT_INC_NIN' => ['title' => 'New investigator ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewInvestigator'],
            'EMT_INC_OVD' => ['title' => 'Overdue ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Overdue'],
            'EMT_INC_PRO' => ['title' => 'Reporter progress notification', 'type' => 'AUTO', 'history_map' => 'REPPROGRESS'],
            'EMT_INC_UPD' => ['title' => 'Updated ' . _fdtk('INCName') . ' notification', 'type' => 'AUTO', 'history_map' => 'UpdatedRecord'],
        ],
        'COM' => [
            'EMT_COM_ACK' => ['title' => 'Acknowledgment ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Acknowledge'],
            'EMT_COM_NOT' => ['title' => 'Notifications ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            'EMT_COM_FBK' => ['title' => 'Feedback ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Feedback'],
            'EMT_COM_NHA' => ['title' => 'New handler ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewHandler'],
            'EMT_COM_NIN' => ['title' => 'New investigator ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewInvestigator'],
            'EMT_COM_OVD' => ['title' => 'Overdue ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Overdue'],
            'EMT_COM_PRO' => ['title' => 'Reporter progress notification', 'type' => 'AUTO', 'history_map' => 'REPPROGRESS'],
            'EMT_COM_UPD' => ['title' => 'Updated ' . _fdtk('COMName') . ' notification', 'type' => 'AUTO', 'history_map' => 'UpdatedRecord'],
        ],
        'MOR' => [
            'EMT_MOR_ACK' => ['title' => 'Acknowledgment ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Acknowledge'],
            'EMT_MOR_NOT' => ['title' => 'Notifications ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            'EMT_MOR_FBK' => ['title' => 'Feedback ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Feedback'],
            'EMT_MOR_UPD' => ['title' => 'Updated ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'UpdatedRecord'],
            'EMT_MOR_REJ' => ['title' => 'Rejected ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Rejected'],
            'EMT_MOR_OVD' => ['title' => 'Overdue ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Overdue'],
            'EMT_MOR_NHA' => ['title' => 'New reviewer ' . _fdtk('MORName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewHandler'],
        ],
        'CLA' => [
            'EMT_CLA_ACK' => ['title' => 'Acknowledgment ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Acknowledge'],
            'EMT_CLA_NOT' => ['title' => 'Submission ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            'EMT_CLA_FBK' => ['title' => 'Feedback ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Feedback'],
            'EMT_CLA_NHA' => ['title' => 'New handler ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewHandler'],
            'EMT_CLA_NIN' => ['title' => 'New investigator ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewInvestigator'],
            'EMT_CLA_UPD' => ['title' => 'Updated ' . _fdtk('CLAName') . ' notification', 'type' => 'AUTO', 'history_map' => 'UpdatedRecord'],
        ],
        Module::REDRESS => [
            RedressEmailTypes::NOTIFY_TEMPLATE => ['title' => 'Submission ' . _fdtk('REDName') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            RedressEmailTypes::FEEDBACK_TEMPLATE => ['title' => _fdtk('feedback_title'), 'type' => 'AUTO', 'history_map' => 'Feedback'],
            RedressEmailTypes::NEW_CASE_MANAGER_TEMPLATE => ['title' => 'New case manager ' . _fdtk('REDName') . ' notification', 'type' => 'AUTO', 'history_map' => RedressEmailTypes::NEW_CASE_MANAGER],
            RedressEmailTypes::NEW_ESCALATION_MANAGER_TEMPLATE => ['title' => 'New escalation manager ' . _fdtk('REDName') . ' notification', 'type' => 'AUTO', 'history_map' => RedressEmailTypes::NEW_ESCALATION_MANAGER],
        ],
        Module::SAFEGUARDING => [
            SafeguardingEmailTypes::ACKNOWLEDGEMENT_TEMPLATE => ['title' => 'Acknowledgement ' . _fdtk('sfgname') . ' notification', 'type' => 'AUTO', 'history_map' => 'Acknowledge'],
            SafeguardingEmailTypes::NOTIFY_TEMPLATE => ['title' => 'Submission ' . _fdtk('sfgname') . ' notification', 'type' => 'AUTO', 'history_map' => 'Notify'],
            SafeguardingEmailTypes::FEEDBACK_TEMPLATE => ['title' => _fdtk('feedback_title'), 'type' => 'AUTO', 'history_map' => 'Feedback'],
            SafeguardingEmailTypes::RECORD_UPDATE_TEMPLATE => ['title' => 'Updated ' . _fdtk('sfgname') . ' notification', 'type' => 'AUTO', 'history_map' => 'UpdatedRecord'],
            SafeguardingEmailTypes::RECORD_PROGRESS_TEMPLATE => ['title' => 'Reporter Feedback email', 'type' => 'AUTO', 'history_map' => 'REPPROGRESS'],
            SafeguardingEmailTypes::LOCAL_AUTHORITY_REFERRAL_TEMPLATE => ['title' => 'Local Authority Referral email', 'type' => 'AUTO', 'history_map' => 'LocalAuthorityReferral'],
            SafeguardingEmailTypes::NEW_HANDLER => ['title' => 'New Handler ' . _fdtk('sfgname') . ' notification', 'type' => 'AUTO', 'history_map' => 'NewHandler'],
        ],
    ];
}
