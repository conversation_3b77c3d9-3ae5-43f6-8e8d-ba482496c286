<?php

declare(strict_types=1);

namespace Source\libs;

use Doctrine\DBAL\Connection;
use Psr\SimpleCache\CacheInterface;
use src\framework\registry\Registry;

class AppVarsHelper
{
    private const WORKFLOW_CACHE_KEY = 'system_module_workflows';
    private Connection $connection;
    private Registry $registry;
    private CacheInterface $cache;

    public function __construct(Connection $connection, Registry $registry, CacheInterface $cache)
    {
        $this->connection = $connection;
        $this->registry = $registry;
        $this->cache = $cache;
    }

    public function setGlobalAccessLevels(): void
    {
        $result = $this->cache->get(self::WORKFLOW_CACHE_KEY);
        if ($result === null) {
            $result = $this->buildModuleWorkflowArray();
            $this->cache->set(self::WORKFLOW_CACHE_KEY, $result);
        }

        $ModuleDefs = $this->registry->getModuleDefs();
        foreach ($result as $row) {
            $name = $ModuleDefs[$row['acl_module']]['PERM_GLOBAL'] ?? '';
            addGlobalAccessLevels(
                $name,
                $row['acl_code'],
                $row['acl_module'],
                _fdtk($row['acl_description']),
                $row['acl_order'],
            );
        }
    }

    private function buildModuleWorkflowArray(): array
    {
        $modules = $this->connection->executeQuery('SELECT DISTINCT acl_module FROM ACCESS_LEVELS')->fetchFirstColumn();

        $ModuleWorkflows = [];
        foreach ($modules as $module) {
            $workflowId = $this->registry->getWorkflowId($module);
            if (!isset($ModuleWorkflows[$workflowId])) {
                $ModuleWorkflows[$workflowId] = [];
            }

            $ModuleWorkflows[$workflowId][] = $module;
        }

        if (empty($ModuleWorkflows)) {
            return [];
        }

        $qb = $this->connection->createQueryBuilder();
        $query = $qb->addSelect('acl_module, acl_code, acl_description, acl_order')
            ->from('ACCESS_LEVELS')
            ->orderBy('acl_order');

        foreach ($ModuleWorkflows as $Workflow => $Modules) {
            $workflowHash = hash('crc32c', (string) $Workflow);
            $query->orWhere("acl_module IN (:modules_{$workflowHash}) AND acl_workflow = :workflow_{$workflowHash}");
            $query->setParameter("modules_{$workflowHash}", $Modules, Connection::PARAM_STR_ARRAY);
            $query->setParameter("workflow_{$workflowHash}", $Workflow);
        }

        return $query->executeQuery()->fetchAllAssociative();
    }
}
