<?php

use app\models\codefield\services\CodeInfoRetriever;
use app\models\treeFields\TreeFieldCodeRetrieverFactory;
use src\component\form\FormTable;
use src\contacts\service\ContactIdNumbersService;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\security\CompatEscaper;
use src\system\database\extrafield\ExtraField;
use src\system\database\fielddef\FieldDefCollection;
use src\system\database\FieldInterface;
use src\system\database\FieldTypeMapper;
use src\system\language\LanguageService;
use src\system\language\LanguageSession;
use src\system\moduledefs\ModuleDefs;

class Listing
{
    public $Module; // module the listing belongs to
    public $LinkModule; // module the records in the listing are linked to (usually the module the listing appears in).
    public $ListingId; // id of the listing
    public $Columns; // array of column details
    public $Title; // the name of the form on the listings listing page
    public $Data; // the data to display in this listing;
    public $ReadOnly = false; // controls whether the design can be edited.
    public $Sortable = false; // controls whether the listing will allow for sorting by columns.

    /** @var string */
    public $EmptyMessage = ''; // message to appear if there are no items to be displayed in the listing.
    public $Checkbox = false; // Flag to display checkboxes
    public $CheckboxType; // Additional type field to cater for more than one section using checkboxes e.g. different contact type sections
    public $CheckboxIDField = 'recordid'; // Data field used to uniquly identify individual checkbox with checkbox_include_ prefix.
    public $OverrideLinkType; // Link type to use instead of the link type provided in the data. Needed because of the weird data structure in complaints.
    public $ExtraParameters; // Extra parameters to be stored against the listing
    public $WhereClause; // Where clause to use when retrieving data for the listing
    public $PDOParamArray = []; // parameters to be inserted into the where clause (optional)
    public $OrderBy;
    public ?string $overrideModuleCode = null;

    /**
     * @var string
     *
     * @desc the "action" that these listing items should link to. Only required if no module provided by RecordList.
     */
    public $Action;

    /** @var Registry */
    protected $registry;

    /** @var ContactIdNumbersService */
    protected $contactIdNumberService;

    /** @var LanguageService */
    private $languageService;

    /** @var LanguageSession */
    private $languageSession;

    /** @var CodeInfoRetriever */
    private $codeInfoRetriever;

    /** @var TreeFieldCodeRetrieverFactory */
    private $treeFieldCodeRetrieverFactory;

    /**
     * @param $Module
     * @param null $ListingId
     */
    public function __construct(
        $Module,
        $ListingId,
        LanguageService $languageService,
        LanguageSession $languageSession,
        CodeInfoRetriever $codeInfoRetriever,
        TreeFieldCodeRetrieverFactory $treeFieldCodeRetrieverFactory,
        Registry $registry,
        ContactIdNumbersService $contactIdNumberService
    ) {
        $this->Module = $Module;
        $this->ListingId = $ListingId;
        $this->languageService = $languageService;
        $this->languageSession = $languageSession;
        $this->codeInfoRetriever = $codeInfoRetriever;
        $this->treeFieldCodeRetrieverFactory = $treeFieldCodeRetrieverFactory;
        $this->registry = $registry;
        $this->contactIdNumberService = $contactIdNumberService;
    }

    /**
     * @param int|null $listingId
     */
    public function LoadColumnsFromDB($listingId = null): void
    {
        if (isset($listingId)) {
            $this->ListingId = $listingId;
        }

        if (!isset($this->ListingId)) {
            $this->ListingId = self::GetDefaultListing($this->Module);
        }

        if ($this->ListingId != 0) {
            $sessionCache = $this->registry->getSessionCache();
            if (!$sessionCache->exists('listing-design.modules-for-listings')) {
                $listingModules = DatixDBQuery::PDO_fetch_all('SELECT recordid, lst_module FROM WEB_LISTING_DESIGNS', [], PDO::FETCH_KEY_PAIR);
                $sessionCache->set('listing-design.modules-for-listings', $listingModules);
            }

            $listingModule = $sessionCache->get('listing-design.modules-for-listings')[(int) $this->ListingId];
        }

        if ($this->ListingId == 0 || $listingModule != $this->Module) {
            $this->loadFromDefaultFile();
        } else {
            $sql = 'SELECT lcl_field, lcl_width FROM WEB_LISTING_COLUMNS WHERE listing_id = :listing_id ORDER BY LCL_order';

            $columns = DatixDBQuery::PDO_fetch_all($sql, ['listing_id' => $this->ListingId]);

            foreach ($columns as $columnDetails) {
                $this->Columns[$columnDetails['lcl_field']] = [
                    'width' => $columnDetails['lcl_width'],
                    'type' => $this->getType($columnDetails['lcl_field']),
                ];
            }
        }
    }

    /**
     * @desc Loads column definitions directly from an array
     *
     * @param array $columns array of columns
     */
    public function LoadColumnsFromArray($columns): void
    {
        $this->Columns = $columns;
    }

    /**
     * @desc Determines whether the listing represented by this object is the listing to be used by default in the system.
     *
     * @return bool true if this listing is the default, false otherwise
     */
    public function isDefault(): bool
    {
        return System_Globals::GetCurrentValue_GlobalOnly($this->Module . '_LISTING_ID', 0) == $this->ListingId;
    }

    /**
     * @desc Loads columns from the default listing file - if this listing is then displayed in the listing designer,
     * it will be read-only, since you can't change the datix default listing.
     */
    public function loadFromDefaultFile(): void
    {
        $colSettingsFile = $this->GetDefaultFile();

        try {
            /** @var array $list_columns_standard */
            include $colSettingsFile;
            $this->Columns = $list_columns_standard;
        } catch (Exception $e) {
            Log::warning('Failed to load listing columns from file: ' . $colSettingsFile . ' - ' . $e->getMessage());
        }
    }

    public function GetDefaultFile(): string
    {
        $moduleDefs = $this->registry->getModuleDefs();

        $libPath = $moduleDefs[$this->Module]['LIBPATH'] ?? '';
        $settingsFilename = "{$libPath}/Standard{$this->Module}ListSettings.php";

        if (!empty($moduleDefs[$this->Module]['GENERIC'])) {
            $settingsFilename = 'Source/generic/StandardListSettings.php';
            if (file_exists('Source/generic_modules/' . $moduleDefs[$this->Module]['GENERIC_FOLDER'] . '/StandardListSettings.php')) {
                $settingsFilename = 'Source/generic_modules/' . $moduleDefs[$this->Module]['GENERIC_FOLDER'] . '/StandardListSettings.php';
            }
        }

        return $settingsFilename;
    }

    /**
     * @desc Loads listing data into this object from an array.
     *
     * @param array $DataArray array of data
     */
    public function LoadData($DataArray): void
    {
        $this->Data = $DataArray;
    }

    /**
     * @desc Constructs a select statement and collects record data for this listing from the appropriate table.
     */
    public function LoadDataFromDB(): void
    {
        $moduleDefs = $this->registry->getModuleDefs();

        $table = $moduleDefs[$this->Module]['TABLE'];

        if (empty($table)) {
            throw new Exception('No TABLE defined for module "' . $this->Module . '"');
        }

        $sql = '
            SELECT recordid, ' .
            implode(', ', array_keys($this->Columns)) .
            ' FROM ' .
            $table .
            ($this->WhereClause ? ' WHERE ' . $this->WhereClause : '') .
            ($this->OrderBy ? ' ORDER BY ' . $this->OrderBy : '');

        $this->Data = DatixDBQuery::PDO_fetch_all($sql, $this->PDOParamArray);
    }

    /**
     * @desc Constructs the url to link the user to a particular record referenced in this listing.
     *
     * @param array $datarow Array of data from this record
     *
     * @return string the url of the record
     */
    public function getRecordUrl($datarow): string
    {
        $moduleDefs = $this->registry->getModuleDefs();
        $scripturl = $this->registry->getScriptUrl();
        $fromSearch = !empty($_REQUEST['fromsearch']);

        if ($this->LinkModule) {
            return $scripturl .
                '?action=' . ($this->Action ?? $moduleDefs[$this->Module]['LINK_ACTION']) .
                '&module=' . ($this->overrideModuleCode ?? $this->LinkModule) .
                '&link_recordid=' . $datarow['link_recordid'] .
                '&link_type=' . ($this->OverrideLinkType ?: $datarow['link_type']) .
                '&main_recordid=' . $datarow[$moduleDefs[$this->LinkModule]['FK']] .
                ($fromSearch ? '&fromsearch=1' : '') .
                (($this->Module === 'CON' && $datarow['main_location']) ? '&main_locationid=' . $datarow['main_location'] : '');
        }

        return
            $scripturl .
            '?action=' . ($this->Action ?? $moduleDefs[$this->Module]['ACTION']) .
            '&module=' . ($this->overrideModuleCode ?? $this->Module) .
            '&' . ($moduleDefs[$this->Module]['URL_RECORDID'] ?? 'recordid') . '=' . $datarow['recordid'] .
            ($fromSearch ? '&fromsearch=1' : '');
    }

    /**
     * @desc Appends additional (probably hard coded) columns to the end of the existing column list.
     */
    public function AddAdditionalColumns($additionalColumns): void
    {
        $this->Columns = SafelyMergeArrays([$this->Columns, $additionalColumns]);
    }

    /**
     * @desc Constructs HTML representation of the listing as a table. Currently using copy-and-pasted code from browselist,
     * this will expand and refine in time.
     *
     * @param string $formType the current form mode
     *
     * @return string HTML representation of the listing
     *
     * @codeCoverageIgnoreStart
     * No unit test, since it deals with constructing HTML (which will, in this case, evolve regularly in the future),
     * rather than providing any testable return value.
     *
     * @throws InvalidDataException
     */
    public function GetListingHTML($formType = FormTable::MODE_EDIT)
    {
        $moduleDefs = $this->registry->getModuleDefs();
        $fieldDefsExtra = $this->registry->getFieldDefsExtra();
        $fieldDefs = $this->registry->getFieldDefs();

        if (!$this->ReadOnly) {
            $this->setFormAccess($formType);
        }

        if (count($this->Columns) == 0) {
            return '<div class="padded_div">This listing has no columns. Please contact your Datix administrator</div>';
        }

        if (count($this->Data) == 0) {
            return '<div class="padded_div windowbg2">' . $this->EmptyMessage . '</div>';
        }

        $html = '<div class="table-responsive">';

        $html .= '<table class="linked-data-table table table-striped table-hover" cellspacing="0" cellpadding="0" width="100%" align="center" border="0">';

        $html .= '<thead><tr name="element_section_row" id="element_section_row">'; // title row

        if ($this->Checkbox) {
            $html .= '<th class="windowbg" width="1%">
                <input type="checkbox" id = "check_all_checkbox" name="check_all_checkbox" onclick="ToggleCheckAll(\'checkbox_id_' . $this->CheckboxType . '\', this.checked)"/>
            </th>';
        }

        $table = $moduleDefs[$this->Module]['TABLE'];

        if (empty($table)) {
            throw new Exception('No TABLE defined for module "' . $this->Module . '"');
        }

        $html = $this->writeColumnTitles($html, $fieldDefs, $fieldDefsExtra, $formType);

        $html .= '</tr></thead><tbody>';

        $html .= $this->writeRows($fieldDefsExtra, $moduleDefs);

        $html .= '</tbody></table>';

        $html .= '</div>';

        return $html;
    }

    /**
     * @desc Similar to the listing HTML generated when viewing a listing, this generates HTML for use in the listing designer,
     * representing a preview of the columns, with fields to set their widths.
     *
     * @return string HTML representing a preview of the listing
     */
    public function GetListingPreviewHTML(): string
    {
        $fieldDefsExtra = $this->registry->getFieldDefsExtra();

        $html = '<table id="listing_preview_table" class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">';

        $html .= '<tr name="element_section_row" id="element_section_row">'; // title row

        if (is_array($this->Columns)) {
            foreach ($this->Columns as $col_field => $col_info) {
                $html .= '<th id="col_heading_' . $col_field . '" class="windowbg"' . ($col_info['width'] ? ' width="' . $col_info['width'] . '%"' : '') . '>';

                $fieldDef = $fieldDefsExtra[$this->Module][$col_field] ?? [];

                if ($col_field == 'mod_title') {
                    $currentCols['mod_title'] = _fdtk('module');
                } elseif ($col_field == 'recordid') {
                    $currentCols[$col_field] = $this->getColumnLabel($col_field, $fieldDef);
                } else {
                    $currentCols[$col_field] = $this->getColumnLabel($col_field, $fieldDef, $this->Module);
                }

                $html .= $currentCols[$col_field];

                $html .= '</th>';
            }

            $html .= '</tr>';
            $html .= '<tr>';

            foreach ($this->Columns as $col_field => $col_info) {
                if ((float) $col_info['width'] - (int) $col_info['width'] == 0) {
                    $col_info['width'] = (int) $col_info['width'];
                }

                $html .= '<td class="windowbg" style="text-align:center">';

                if ($this->ReadOnly) {
                    $html .= $col_info['width'] . '%</td>';
                } else {
                    $html .= '<input id="col_width_' . $col_field . '" name="col_width_' . $col_field . '" type="string" size="2" maxlength="3" value="' . $col_info['width'] . '" onchange="GlobalChangeTracker = true;jQuery(\'#col_heading_' . $col_field . '\').width(this.value.toString()+\'%\')"></td>';
                }
            }
        }

        $html .= '</tr>';

        $html .= '</table>';

        return $html;
    }

    // @codeCoverageIgnoreEnd

    /**
     * @desc Deletes the current listing design from the database.
     */
    public function DeleteFromDB(): void
    {
        DatixDBQuery::PDO_query('DELETE FROM WEB_LISTING_COLUMNS WHERE listing_id = :listing_id', ['listing_id' => $this->ListingId]);
        DatixDBQuery::PDO_query('DELETE FROM WEB_LISTING_DESIGNS WHERE recordid = :listing_id', ['listing_id' => $this->ListingId]);

        if ($this->isDefault()) {// we need to reassign the default design to one that will still exist.
            SetGlobal($this->Module . '_LISTING_ID', 0);
            $_SESSION['Globals'][$_GET['global']] = $_GET['value']; // make sure the change is picked up straight away.
        }
    }

    /**
     * @desc Saves the current listing design to the database.
     */
    public function SaveToDB(): void
    {
        if (!$this->ListingId) {
            $this->ListingId = DatixDBQuery::PDO_build_and_insert(
                'WEB_LISTING_DESIGNS',
                ['lst_title' => $this->Title, 'lst_module' => $this->Module, 'createddate' => GetTodaysDate(), 'createdby' => $_SESSION['initials']],
            );
        } else {
            DatixDBQuery::PDO_query(
                'UPDATE WEB_LISTING_DESIGNS SET updateddate = :updateddate, updatedby = :updatedby WHERE recordid = :recordid',
                ['recordid' => $this->ListingId, 'updateddate' => GetTodaysDate(), 'updatedby' => $_SESSION['initials']],
            );
        }

        DatixDBQuery::PDO_query('DELETE FROM WEB_LISTING_COLUMNS WHERE listing_id = :listing_id', ['listing_id' => $this->ListingId]);

        $insertColumnQuery = new DatixDBQuery('INSERT INTO WEB_LISTING_COLUMNS (listing_id, LCL_field, LCL_width, LCL_order, updatedby, updateddate) VALUES (:listing_id, :field, :width, :order, :updatedby, :updateddate)');
        $insertColumnQuery->prepare();

        $order = 0;

        if (is_array($this->Columns)) {
            foreach ($this->Columns as $colField => $colOptions) {
                if ($colOptions['width'] == '') {// otherwise we get an error trying to put a blank string into an integer field.
                    $colOptions['width'] = 0.0;
                }

                $insertColumnQuery->execute(
                    ['listing_id' => $this->ListingId,
                        'field' => $colField,
                        'width' => $colOptions['width'],
                        'order' => $order++,
                        'updatedby' => $_SESSION['initials'],
                        'updateddate' => date('Y-m-d H:i:s'), ],
                );
            }
        }
    }

    /**
     * @desc Loads the default listing from the database and returns its columns as an array.
     *
     * @param string $module The module listing to return
     *
     * @return array An array of columns
     */
    public static function GetListingColumns($module)
    {
        $defaultListing = self::GetDefaultListing($module);
        $Listing = Listings_ListingFactory::getListing($module, $defaultListing);

        $Listing->LoadColumnsFromDB();

        return $Listing->Columns;
    }

    /**
     * @desc Returns the default listing for a given module.
     *
     * @return int the id of the default listing for this module
     */
    public static function GetDefaultListing($module): int
    {
        return (int) System_Globals::GetCurrentValue($module . '_LISTING_ID', 0);
    }

    /**
     * @desc Returns the default listing for a given module, ignoring user and profile parameters.
     *
     * @return int the id of the default listing for this module
     */
    public static function GetGlobalDefaultListing($module)
    {
        return System_Globals::GetCurrentValue_GlobalOnly($module . '_LISTING_ID', 0);
    }

    /**
     * Retrieves the label for a listing column header.
     */
    public function getColumnLabel(string $fieldName, array $fieldDef, ?string $overrideMainModule = null): string
    {
        $moduleDefs = $this->registry->getModuleDefs();
        $fieldLabels = $this->registry->getFieldLabels();

        // The language strings will have been persisted against the module the field is defined against in FieldDefs,
        // so we should use that instead of the module the listing is defined against to ensure we get the correct label.
        // We also need to ensure we use the correct field table, which may not be the module main table (e.g. for linked fields).
        // This does not apply in the list editing screen, requiring the need for an override to the main module.
        $fieldModule = $overrideMainModule ?? $fieldDef['Module'] ?? $this->Module;
        $fieldDomain = $this->languageService->getModuleDomain($fieldModule);
        $fieldTable = $fieldDef['Table']['DEFAULT'] ?? $fieldDef['Table'] ?? $moduleDefs[$fieldModule]->getDbReadObj();

        return $fieldLabels->getLabel($fieldTable, $fieldName, $fieldDomain, null, $fieldDef['Title'] ?? '');
    }

    protected function setFormAccess(string $formType): void
    {
        $moduleDefs = $this->registry->getModuleDefs();

        if (empty($this->registry->getParm($moduleDefs[$this->Module]['PERM_GLOBAL']))
            || in_array($formType, [FormTable::MODE_PRINT, FormTable::MODE_READONLY])
        ) {
            $this->ReadOnly = true;
        }
    }

    /**
     * @param array $fieldDefs
     *
     * @return string
     */
    private function writeColumnTitles(
        string $html,
        FieldDefCollection $fieldDefs,
        array $fieldDefsExtra,
        string $formType
    ) {
        foreach ($this->Columns as $col_field => $col_info) {
            if ($formType == FormTable::MODE_PRINT && $col_field == 'edit') {
                continue;
            }

            $html .= '<th' . ($col_info['width'] ? ' width="' . $col_info['width'] . '%"' : '') . '>';

            $fieldDef = $fieldDefsExtra[$this->Module][$col_field] ?? [];
            $fieldType = $fieldDefs[$this->Module][$col_field]['Type'] ?? null;

            $isSortable = $fieldType != FieldInterface::TEXT_DB && $this->Sortable && !$col_info['custom'];
            if ($isSortable) {
                $html .= '<a href="Javascript:sortList(\'' . $col_field . '\');">';
            }

            if ($col_field == 'mod_title') {
                $currentCols['mod_title'] = _fdtk('module');
            } elseif ($col_field == 'recordid') {
                $currentCols[$col_field] = $this->getColumnLabel($col_field, $fieldDef);
            } elseif (!empty($col_info['title'])) {
                $currentCols[$col_field] = $col_info['title'];
            } else {
                $currentCols[$col_field] = $this->getColumnLabel($col_field, $fieldDef);
            }

            $html .= $currentCols[$col_field];

            if ($isSortable) {
                $html .= '</a>';
            }

            $html .= '</th>';
        }

        return $html;
    }

    /**
     * @throws InvalidDataException
     */
    private function writeRows(array $fieldDefsExtra, ModuleDefs $moduleDefs): string
    {
        $rowsHtml = '';

        foreach ($this->Data as $row) {
            $recordUrl = $this->getRecordUrl($row);

            $rowsHtml .= '<tr>';

            if ($this->Checkbox) {
                $rowsHtml .= '<td style="width:1%">
                        <input type="checkbox" id="checkbox_id_' . $this->CheckboxType . '" class="list_checkbox_' . $this->CheckboxType . '" name="checkbox_include_' . Escape::EscapeEntities($row[$this->CheckboxIDField]) . '" onclick="" />
                        </td>';
            }

            foreach ($this->Columns as $colField => $colInfo) {
                $fieldType = $fieldDefsExtra[$this->Module][$colField]['Type'] ?? null;
                if (!empty($colInfo['type'])) {
                    $fieldType = $colInfo['type'];
                }


                if ($colField === 'inc_grade') {
                    $rowsHtml .= $this->getIncGradeColumn($row['inc_grade']);

                    continue;
                }

                $closingRowNoColourString = '<td valign="top"';
                $isCodedField = in_array($fieldType, [
                    FieldInterface::YESNO_DB,
                    FieldInterface::CODE_DB,
                    FieldInterface::MULTI_SELECT_DB,
                ], true);

                if ($isCodedField) {
                    $moduleToUseForCodeRetrieval = $this->Module;
                    if ($this->Module === 'CON'
                        && $colField !== 'link_rsp_type'
                        && UnicodeString::substr($colField, 0, 5) === 'link_'
                    ) {
                        $moduleToUseForCodeRetrieval = 'INC';
                    }

                    $fieldTable = $fieldDefsExtra[$moduleToUseForCodeRetrieval][$colField]['Table'] ?? '';
                    $fullFieldName = $fieldTable . '.' . $colField;

                    $codes = $row[$colField];

                    $colour = '';
                    $codeString = '';

                    if ($fieldType === FieldInterface::MULTI_SELECT_DB) {
                        $multiCodes = explode(' ', UnicodeString::rtrim($codes));

                        foreach ($multiCodes as $code) {
                            $codeInfo = $this->codeInfoRetriever->retrieve($fullFieldName, $code);
                            $codeString .= $codeInfo->getDescription();
                            $codeString .= ', ';
                        }
                        $codeString = substr($codeString, 0, -3);
                    } elseif ($fieldType === FieldInterface::CODE_DB || $fieldType === FieldInterface::YESNO_DB) {
                        $codeInfo = $this->codeInfoRetriever->retrieve($fullFieldName, $codes);
                        $codeString = $codeInfo->getDescription();
                        $colour = $codeInfo->getCodWebColour();
                    }

                    if (!empty($colour)) {
                        $closingRowNoColourString = '<td valign="left" style="background-color:#' . $colour . '"';
                    }
                }

                $rowsHtml .= $closingRowNoColourString;

                $rowsHtml .= '>';
                $isEmptyColInfoCustom = empty($colInfo['custom']);

                if ($row[$colField] && !$this->ReadOnly && $isEmptyColInfoCustom) {
                    $rowsHtml .= '<a href="javascript:if(CheckChange()){SendTo(\'' . $recordUrl . '\');}" >';
                }

                if ($isEmptyColInfoCustom) {
                    $row[$colField] = CompatEscaper::encodeCharacters($row[$colField]);
                }

                if ($colField === 'recordid') {
                    $rowsHtml .= $row[$colField];
                } elseif ($colField === 'act_module') {
                    $rowsHtml .= $moduleDefs[$row[$colField]]['NAME'];
                } elseif ($colField === 'rep_approved') {
                    $rowsHtml .= $this->languageSession->getStatusString($this->Module, $row[$colField]) ?: $codeString;
                } elseif ($isCodedField) {
                    $rowsHtml .= $codeString;
                } elseif ($fieldType === FieldInterface::DATE_DB) {
                    $rowsHtml .= formatDateForDisplay($row[$colField]);
                } elseif ($fieldType === FieldInterface::MONEY_DB) {
                    $rowsHtml .= FormatMoneyVal($row[$colField]);
                } elseif ($fieldType === FieldInterface::TREE_DB && !empty($row[$colField])) {
                    $mapperType = $fieldDefsExtra[$this->Module][$colField]['mapperType'];
                    $rowsHtml .= $this->treeFieldCodeRetrieverFactory->create($mapperType)->getBreadcrumb($row[$colField]);
                } elseif ($fieldType === FieldInterface::TIME_DB && UnicodeString::strlen($row[$colField]) === 4) {
                    $row[$colField] = $row[$colField][0] . $row[$colField][1] . ':' . $row[$colField][2] . $row[$colField][3];
                    $rowsHtml .= $row[$colField];
                } else {
                    $rowsHtml .= $row[$colField] ?? '';
                }

                if ($row[$colField] && !$this->ReadOnly && $isEmptyColInfoCustom) {
                    $rowsHtml .= '</a>';
                }

                $rowsHtml .= '</td>';
            }
            $rowsHtml .= '</tr>';
        }

        return $rowsHtml;
    }

    /**
     * @param string $gradeValue
     */
    private function getIncGradeColumn(?string $gradeValue): string
    {
        if ($gradeValue === null) {
            return '<td align="left" class="rowfieldbg"></td>';
        }

        $sql = 'SELECT description, cod_web_colour FROM code_inc_grades WHERE code = :incGradeCode';

        $result = DatixDBQuery::PDO_fetch($sql, ['incGradeCode' => $gradeValue]);

        if ($result) {
            $colour = '';
            if ($result['cod_web_colour']) {
                $colour = 'bgcolor="#' . $result['cod_web_colour'] . '"';
            }

            return '<td align="left" ' . $colour . '>' . $result['description'] . '</td>';
        }

        return '<td align="left" class="rowfieldbg">' . $gradeValue . ' </td>';
    }

    private function getType(string $field): ?string
    {
        $fieldDef = $this->registry->getFieldDefs()[$field] ?? null;

        if (!$fieldDef instanceof ExtraField) {
            return null;
        }

        return FieldTypeMapper::getDatabaseType($fieldDef);
    }
}
