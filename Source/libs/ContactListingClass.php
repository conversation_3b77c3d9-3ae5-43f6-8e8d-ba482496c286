<?php

use app\models\accessLevels\AccessLevels;
use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\generic\valueObjects\Module;
use src\component\form\FormTable;
use src\component\form\FormTableFactory;
use src\framework\registry\Registry;
use src\reports\model\listingreport\column\formatters\TreeColumnFormatterFactory;
use src\security\CompatEscaper;
use src\system\container\facade\Container;
use src\system\database\code\Code;
use src\system\database\FieldInterface;
use src\system\language\LanguageSessionFactory;
use src\system\moduledefs\ModuleDefs;

require_once 'Source/libs/ListingClass.php';
// needed because contact listings have some weird custom behaviour.

class ContactListing extends Listing
{
    private const IDNUMBERSCOLUMN = 'con_id_numbers';
    private const IDTYPECOLUMN = 'con_number_type';
    public $ShowFullContactDetails = false;

    /**
     * @desc Constructs HTML representation of the listing as a table. Currently using copy-and-pasted code from browselist,
     * this will expand and refine in time.
     *
     * @param string $formType the current form mode
     * @param null $mainLocation
     *
     * @return string HTML representation of the listing
     *
     * @throws InvalidDataException
     *
     * @codeCoverageIgnoreStart
     * No unit test, since it deals with constructing HTML (which will, in this case, evolve regularly in the future),
     * rather than providing any testable return value.
     */
    public function GetListingHTML($formType = '', $mainLocation = null): string
    {
        global $FieldDefsExtra, $ModuleDefs;

        if ($formType === FormTable::MODE_PRINT || !$this->CanAccessContacts()) {
            $this->ReadOnly = true;
        }

        if (empty($this->Columns)) {
            return '<div class="padded_div">This listing has no columns. Please contact your Datix administrator</div>';
        }

        if (empty($this->Data)) {
            return '<div class="padded_div windowbg2">' . $this->EmptyMessage . '</div>';
        }

        $HTML = '<table class="linked-data-table table table-striped table-hover" cellspacing="0" cellpadding="0" width="100%" align="center" border="0">';

        $HTML .= $this->getHead();

        $HTML .= '<tbody>';

        foreach ($this->Data as $row) {
            $row['main_module'] = $this->LinkModule;
            $row['main_location'] = $mainLocation;
            $recordUrl = $this->getRecordUrl($row);

            $recordRowLink = '';

            if (!$this->ReadOnly) {
                $recordRowLink = ' onclick="if(CheckChange()){SendTo(\'' . $recordUrl . '\')}"';
            }

            $HTML .= '<tr' . $recordRowLink . '>';

            if ($this->ShowFullContactDetails) {
                $HTML .= '<td><span class="toggle-trigger icon-caret closed" data-target="contact_' . $row['link_type'] . '_' . $row['con_id'] . '"></span></td>';
            }

            if ($this->Checkbox) {
                $HTML .= '<td class="" width="1%">
                        <input type="checkbox" id="checkbox_id_' . $this->CheckboxType . '" name="checkbox_include_' . $row[$this->CheckboxIDField] . '" onclick="" />
                        </td>';
            }

            if ((!empty($row['con_id']))
                && (array_key_exists(self::IDNUMBERSCOLUMN, $this->Columns)
                    || array_key_exists(self::IDTYPECOLUMN, $this->Columns))) {
                $idNumbers = $this->contactIdNumberService->getContactIdNumbers($row['con_id']);

                foreach ($idNumbers as $idNumber) {
                    $row[self::IDNUMBERSCOLUMN] = $idNumber;
                }
            }

            foreach ($this->Columns as $col_field => $col_info) {
                if ($col_info['type']) {
                    $fieldType = $col_info['type'];
                } else {
                    $fieldType = $FieldDefsExtra[$this->Module][$col_field]['Type'];
                }

                $isCodedField = in_array($fieldType, [FieldInterface::CODE_DB, FieldInterface::MULTI_SELECT_DB], true);

                // special columns
                if (in_array($col_field, [
                    'ram_cur_level',
                    'ram_after_level',
                    'ram_level',
                ], true)) {
                    $HTML .= $this->getRiskMatrixTd($row, $col_field);
                } elseif ($col_field === 'inc_grade') {
                    $HTML .= $this->getGradeTd($row);
                } elseif ($col_field === self::IDTYPECOLUMN) {
                    $codeInfoObj = '';
                    $codeInfoArr = $this->getCodeInfo($col_field, $row, $fieldType, $FieldDefsExtra);
                    $HTML .= '<td class="" valign="top">';
                } elseif ($col_field === self::IDNUMBERSCOLUMN) {
                    $HTML .= $this->getIdNumbersTd($row);
                } elseif ($isCodedField) {
                    $codeInfoObj = '';
                    $codeInfoArr = '';
                    if ($fieldType === FieldInterface::CODE_DB) {
                        $codeInfoObj = $this->getCodeInfoCodeDb($col_field, $row, $fieldType, $FieldDefsExtra);
                    }
                    if ($fieldType === FieldInterface::MULTI_SELECT_DB) {
                        $codeInfoArr = $this->getCodeInfoMultiSelectDb($col_field, $row, $fieldType, $FieldDefsExtra);
                    } else {
                        $codeInfoArr = $this->getCodeInfo($col_field, $row, $fieldType, $FieldDefsExtra);
                    }
                    $colour = '';
                    if ($codeInfoObj instanceof Code) {
                        $colour = $codeInfoObj->getCodWebColour();
                    }
                    if ($colour) {
                        $HTML .= '<td valign="left" style="background-color:#' . $colour . '">';
                    } else {
                        $HTML .= '<td class="" valign="top">';
                    }
                } else {
                    $HTML .= '<td class="" valign="top">';
                }

                if (!$col_info['custom']) {
                    if ($col_field === self::IDNUMBERSCOLUMN) {
                        $contactIds = $row[$col_field] ?? [];
                        if (is_iterable($contactIds)) {
                            foreach ($contactIds as $contact_id) {
                                $contact_id['number'] = CompatEscaper::encodeCharacters($contact_id['number']);
                                $contact_id['type'] = CompatEscaper::encodeCharacters($contact_id['type']);
                            }
                        }
                    } else {
                        $row[$col_field] = CompatEscaper::encodeCharacters($row[$col_field]);
                    }
                }
                if ($col_field === 'recordid') {
                    $HTML .= $row[$col_field];
                } elseif ($col_field === 'act_module') {
                    $HTML .= $ModuleDefs[$row[$col_field]]['NAME'];
                } elseif ($col_field === 'rep_approved') {
                    $HTML .= LanguageSessionFactory::getInstance()->getStatusString($this->Module, $row[$col_field]) ?: $codeInfoArr['description'];
                } elseif ($isCodedField) {
                    if ($codeInfoObj instanceof Code) {
                        $language = LanguageSessionFactory::getInstance()->getLanguage();
                        $desc = $codeInfoObj->getDescription($language);
                        // if code ID (alphanumeric) is returned as desc, then try getting the description from the $_SESSION['CachedValues']['cod_descr']
                        if ($desc === $row[$col_field]) {
                            $desc = code_descr($this->Module, $col_field, $row[$col_field]);
                        }
                    }
                    $HTML .= $desc ?? $codeInfoArr['description'];
                    unset($desc);
                } elseif ($fieldType === FieldInterface::YESNO_DB) {
                    $HTML .= code_descr($this->Module, $col_field, $row[$col_field]);
                } elseif (in_array($fieldType, [FieldInterface::DATE_CODE, FieldInterface::DATE_DB], true)) {
                    $HTML .= formatDateForDisplay($row[$col_field]);
                } elseif ($fieldType === FieldInterface::MONEY_DB) {
                    $HTML .= FormatMoneyVal($row[$col_field]);
                } elseif ($fieldType === FieldInterface::TREE_DB) {
                    // The 'tree' type maps in the relevant (last) node for the contact.
                    $treeFilter = (new TreeColumnFormatterFactory())->create($col_field, 'contacts_main');
                    $HTML .= $treeFilter->format($row[$col_field], self);
                } elseif ($fieldType === FieldInterface::TIME_DB) {
                    if (UnicodeString::strlen($row[$col_field]) == 4) {
                        $row[$col_field] = $row[$col_field][0] . $row[$col_field][1] . ':' . $row[$col_field][2] . $row[$col_field][3];
                    }
                    $HTML .= $row[$col_field];
                } elseif ($col_field !== self::IDNUMBERSCOLUMN) {
                    $HTML .= $row[$col_field];
                }

                if (!$col_info['custom']) {
                    // need to undo any html encoding here so it's not duplicated when showing full contact details below
                    if ($col_field === self::IDNUMBERSCOLUMN) {
                        $contactIds = $row[$col_field] ?? [];
                        if (is_iterable($contactIds)) {
                            foreach ($contactIds as $contact_id) {
                                $contact_id['number'] = CompatEscaper::decodeEntities($contact_id['number']);
                                $contact_id['type'] = CompatEscaper::decodeEntities($contact_id['type']);
                            }
                        }
                    } else {
                        $row[$col_field] = CompatEscaper::decodeEntities($row[$col_field]);
                    }
                }

                $HTML .= '</td>';
            }
            $HTML .= '</tr>';

            if ($this->ShowFullContactDetails) {
                $HTML .= $this->ShowFullContactDetails($row);
            }
        }

        $HTML .= '</tbody>';

        $HTML .= '</table>';

        return $HTML;
    }

    /**
     * @desc Simple function used when drawing contacts sections.
     *
     * @return bool true if this user has permission to link contacts to records, false otherwise
     */
    public function CanAccessContacts(): bool
    {
        $registry = Container::get(Registry::class);

        // "cascade permissions" mean that permissions for the main module cascade onto linked
        // modules. Since the user has permission to the main module, he has permission to the contacts linked.
        if ($registry->getParm('CASCADE_PERMISSIONS', 'Y')->isTrue()) {
            return true;
        }

        $accessLevel = GetAccessLevel(Module::CONTACTS);

        return !in_array($accessLevel, [AccessLevels::CODE_CON_INPUT_ONLY, ''], true);
    }

    // gives full printout of details of contact.
    private function ShowFullContactDetails(array $contact): string
    {
        global $FormDesigns;

        $FormType = FormTable::MODE_PRINT;
        $ExtraParameters = array_merge(['RowClass' => 'windowbg2_light'], $this->ExtraParameters);

        $FormDesignSave = $FormDesigns;

        // A contact form can be rendered inside another form (e.g. Claims).
        // This means the visibility settings are available for two different modules.
        // If a module (e.g. CON) hides the documents section, and a containing module (e.g. CLA) form renders,
        // the global will have the documents section hidden when it's supposed to show.
        // So here we copy the globals to non-global variables...
        $SectionVisibilityCache = $GLOBALS['ShowHideSections'];
        $FieldVisibilityCache = $GLOBALS['ShowHideFields'];

        // ... unset the globals so that they don't bleed across modules.
        unset($GLOBALS['ShowHideSections'], $GLOBALS['ShowHideFields']);

        $SavedFormDesignSettings = saveFormDesignSettings();
        unsetFormDesignSettings();

        // Recreate and Reset $FormDesigns global to old value
        global $FormDesigns;
        $FormDesigns = $FormDesignSave;

        $HTML = '';

        $formDesign = Forms_FormDesign::GetFormDesign([
            'module' => Module::CONTACTS,
            'level' => 2,
            'form_type' => $FormType,
            'link_type' => $contact['link_type'],
            'parent_module' => $this->LinkModule,
        ]);

        $HTML .= '<tr class="windowbg2_light full-contact-listing" id="contact_' . $contact['link_type'] . '_' . $contact['con_id'] . '" style="display: none"><td style="padding:0px;spacing:0px">';

        $module = $this->LinkModule;  // referenced in form array
        $ModuleDefs = Container::get(ModuleDefs::class);

        // For the benefit of the basic form file below, which will reference contact data for field conditions etc
        $con = $contact;

        /** @var array $FormArray */
        include 'Source/contacts/BasicForm.php';

        $HTML .= '
        </td><td style="padding:0px;spacing:0px" colspan="' . count($this->Columns) . '">';

        $Table = FormTableFactory::create($FormType, Module::CONTACTS, $formDesign);
        $Table->makeForm($FormArray, $contact, $this->Module, $ExtraParameters);
        $HTML .= $Table->getFormTable();

        $HTML .= '</td></tr>';

        loadFormDesignSettings($SavedFormDesignSettings);

        // Here we reset the visibility globals in line with their original settings.
        $GLOBALS['ShowHideSections'] = $SectionVisibilityCache;
        $GLOBALS['ShowHideFields'] = $FieldVisibilityCache;

        return $HTML;
    }

    private function getHead(): string
    {
        $fieldDefsExtra = $this->registry->getFieldDefsExtra();
        $fieldDefs = $this->registry->getFieldDefs();

        $headHtml = '<thead>';

        $headHtml .= '<tr name="element_section_row" id="element_section_row">';

        if ($this->ShowFullContactDetails) {
            $headHtml .= '<th width="1%"></th>';
        }

        if ($this->Checkbox) {
            $headHtml .= '<th width="1%">
                <input type="checkbox" id = "check_all_checkbox" name="check_all_checkbox" onclick="ToggleCheckAll(\'checkbox_id_' . $this->CheckboxType . '\', this.checked)"/>
            </th>';
        }

        foreach ($this->Columns as $columnField => $columnInfo) {
            $headHtml .= '<th ' . ($columnInfo['width'] ? 'width="' . $columnInfo['width'] . '%"' : '') . '>';
            $fieldDef = $fieldDefsExtra[$this->Module][$columnField] ?? [];

            if ($fieldDefs[$this->Module][$columnField]['Type'] !== FieldInterface::TEXT_DB
                && $this->Sortable
                && !$columnInfo['custom']) {
                $headHtml .= '<a href="Javascript:sortList(\'' . $columnField . '\');">';
            }

            $headHtml .= $this->getColumnHeaderLabel($columnField, $fieldDef, $columnInfo);

            if ($fieldDefs[$this->Module][$columnField]['Type'] !== FieldInterface::TEXT_DB
                && $this->Sortable && !$columnInfo['custom']) {
                $headHtml .= '</a>';
            }

            $headHtml .= '</th>';
        }

        $headHtml .= '</tr></thead>';

        return $headHtml;
    }

    private function getColumnHeaderLabel(string $columnField, array $fieldDef, array $columnInfo)
    {
        if ($columnField === 'mod_title') {
            return _fdtk('module');
        }

        if ($columnField === 'recordid') {
            return $this->getColumnLabel($columnField, $fieldDef);
        }

        if ($columnInfo['title']) {
            return $columnInfo['title'];
        }

        return $this->getColumnLabel($columnField, $fieldDef);
    }

    private function getRiskMatrixTd(array $row, string $colField): string
    {
        $registry = Container::get(Registry::class);
        $col = $row[$colField];
        $table = 'code_ra_levels';

        if ($registry->getParm('RISK_MATRIX', 'N')->isTrue()) {
            $table = 'code_inc_grades';
        }

        $sql = "SELECT cod_web_colour, description FROM {$table} WHERE code = :code";

        $level = DatixDBQuery::PDO_fetch($sql, ['code' => $col]);

        if (!isset($level['description'])) {
            $level['description'] = $col;
        }

        $colour = $level['cod_web_colour'];

        return '<td align="left" ' . (!empty($colour) ? 'class="">' : 'bgcolor="#' . $colour . '">') . $level['description'] . '</td>';
    }

    private function getGradeTd(array $row): string
    {
        $sql = 'SELECT cod_web_colour, description FROM code_inc_grades WHERE code = :code';

        $incgrade = DatixDBQuery::PDO_fetch($sql, ['code' => $row['inc_grade']]);

        if (!empty($incgrade)) {
            return '<td align="left" bgcolor="#' . $incgrade['cod_web_colour'] . '">' . $incgrade['description'] . '</td>';
        }

        return '<td align="left" class="rowfieldbg">' . $row['inc_grade'] . ' </td>';
    }

    private function getIdNumbersTd(array $row): string
    {
        // @why - sometimes $row is not a contact but an organisation for which ID numbers are not a thing
        $innerHTML = '';
        if (!empty($row['con_id'])) {
            $idNumbers = [];

            if (!empty($row[self::IDNUMBERSCOLUMN])) {
                $idNumbers = $this->contactIdNumberService->formatContactIdNumbersArray($row[self::IDNUMBERSCOLUMN]);
            }

            $innerHTML = implode('<br/>', $idNumbers);
        }


        return '<td valign="top" class="">' . $innerHTML . '</td>';
    }

    private function getCodeInfoCodeDb(string $colField, array $row, string $fieldType, array $FieldDefsExtra): ?Code
    {
        if ($this->Module === Module::CONTACTS && UnicodeString::substr($colField, 0, 5) === 'link_') {
            // hack needed because we store link fields under 'INC' rather than 'CON'
            $table = $FieldDefsExtra[Module::INCIDENTS][$colField]['Table'];
            if (is_array($table)) {
                $table = $table[$this->OverrideLinkType] ?? $table['DEFAULT'] ?? 'link_contacts';
            }
            $fullFieldName = "{$table}.{$colField}";

            return (new CodeInfoRetrieverFactory())->create()->retrieve($fullFieldName, $row[$colField]);
        }

        $table = $FieldDefsExtra[$this->Module][$colField]['Table'];
        $fullFieldName = $table . '.' . $colField;

        return (new CodeInfoRetrieverFactory())->create()->retrieve($fullFieldName, $row[$colField]);
    }

    private function getCodeInfoMultiSelectDb(string $colField, array $row, string $fieldType, array $FieldDefsExtra): array
    {
        $codeinfo = [];
        if ($this->Module === Module::CONTACTS && UnicodeString::substr($colField, 0, 5) === 'link_') {
            $codeinfo['description'] = GetCodeDescriptions(Module::INCIDENTS, $colField, $row[$colField], '', ', ');
        } else {
            $codeinfo['description'] = GetCodeDescriptions($this->Module, $colField, $row[$colField], '', ', ');
        }

        return $codeinfo;
    }

    private function getCodeInfo(string $colField, array $row, string $fieldType, array $FieldDefsExtra): array
    {
        $codeinfo = [];
        if ($this->Module === Module::CONTACTS && UnicodeString::substr($colField, 0, 5) === 'link_') {
            // hack needed because we store link fields under 'INC' rather than 'CON'
            $moduleDefs = Container::get(ModuleDefs::class);
            if (in_array($colField, $moduleDefs[$this->Module]['ONE_TO_MANY_LINKED_FIELD_ARRAY'], true)) {
                $injuryDescr = [];
                foreach ($row['injury_table'] as $injuries) {
                    $fieldKey = 'inc_' . substr($colField, 5, -1);
                    $injuryDescr[] = GetCodeDescriptions(Module::INCIDENTS, $colField, $injuries[$fieldKey], '', ', ');
                }
                $codeinfo['description'] = implode('<br/>', $injuryDescr);
            }
        } elseif ((!empty($row['con_id'])) && $colField === self::IDTYPECOLUMN) {
            $types = [];
            $idNumbers = $row[self::IDNUMBERSCOLUMN] ?? [];
            if (is_iterable($idNumbers)) {
                foreach ($idNumbers as $idNumber) {
                    $types[] = GetCodeDescriptions($this->Module, $colField, $idNumber['type']);
                }
            }
            $codeinfo['description'] = implode('<br/>', $types);
        } else {
            $codeinfo['description'] = GetCodeDescriptions($this->Module, $colField, $row[$colField], '', ', ');
        }

        return $codeinfo;
    }
}
