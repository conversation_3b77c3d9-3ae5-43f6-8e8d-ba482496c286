<?php

use src\contacts\model\ContactsFields;
use src\incidents\model\PSIMSLinkContactsFields;

/** @var array $con */
$GLOBALS['FormTitle'][7] = 'Datix Contact Form';

$GLOBALS['DefaultValues'] = [
    'link_type' => $con['link_type'],
    'lcom_iscomplpat' => 'Y',
];

$GLOBALS['MandatoryFields'] = [
    'link_role' => 'link',
    'con_surname' => 'contact',
    PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME => 'contact',
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => 'contact',
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => 'contact',
    PSIMSLinkContactsFields::PSIMS_GENDER => 'contact',
    PSIMSLinkContactsFields::PSIMS_AGE_YEARS => 'contact',
];

$GLOBALS['HelpTexts'] = [
    'first_day_of_disability' => ['7' => 'The first day of disability must be more than 3 days after the date of injury.'],
    'psims_age_years' => ['7' => 'The patient\'s age in years when the exact age is unknown'],
    PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION => ['7' => 'Your answer should be based on your own judgement, given the information you have at this point, and can be changed if further information becomes available.'],
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => ['7' => file_get_contents(__DIR__ . '/HelpTexts/en_GB/psims_physical_harm.html')],
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => ['7' => file_get_contents(__DIR__ . '/HelpTexts/en_GB/psims_psychological_harm.html')],
    PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY => ['7' => 'This may be in their record, or you can ask them or a family member. If this information has not been provided by the patient or their family, please select ‘I don’t know’.'],
    PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME => ['7' => 'Describe any physical or psychological impact on the patient as a result of the incident, or how their care was subsequently changed as a result. Your answer should be based on the information you have at this point, and can be changed if further information becomes available.'],
];

$GLOBALS['HideFields'] = [
    'link_worked_alone' => true,
    'link_become_unconscious' => true,
    'link_req_resuscitation' => true,
    'link_hospital_24hours' => true,
    'con_police_number' => true,
    'con_work_alone_assessed' => true,
    'sirs' => true,
    'property_section' => true,
    'progress_notes' => true,
    'additional_info' => true,
    'documents' => true,
    'link_age_band' => true,
    'link_date_admission' => true,
    'link_notify_progress' => true,
    'osha' => true,
    'pay_rates' => true,
    'lost_restricted_time' => true,
    'icd' => true,
    'mmsea' => true,
    'edi' => true,
    'con_state' => true,
    'time_employee_began_work' => true,
    'include_in_tpoc' => true,
    'tpoc' => true,
    'tax_id' => true,
    'con_social_security_number' => true,
    'con_middle_name' => true,
    'employee_state_hired' => true,
    'employment_termination_date' => true,
    'full_pay_injury_day' => true,
    'salary_continued' => true,
    'con_employment_status_code' => true,
    'con_process_level' => true,
    'con_job_code' => true,
    'con_supervisor_name' => true,
    'con_department' => true,
    'con_location_code' => true,
    'con_fte' => true,
    'con_lawson_number' => true,
    'show_illness' => true,
    'illness' => true,
    'link_illness' => true,
    'osha_date_hired' => true,
    'edi_wage_period' => true,
    'edi_employment_status' => true,
    'edi_employee_id_type' => true,
    'edi_work_loss_list' => true,
    'edi_physical_restrictions' => true,
    'edi_disability_type' => true,
    'edi_diagnosis' => true,
    'edi_agency_code' => true,
    'edi_ncci_class' => true,
    'edi_type_loss' => true,
    'edi_reporting_period' => true,
    'edi_date_disability_known_employer' => true,
    'first_day_of_disability' => true,
    'initial_rtw_same_employer' => true,
    'verification_number' => true,
    'edi_accident_premises' => true,
    'edi_return_to_work_type' => true,
    'edi_return_to_work_qualif' => true,
    'edi_drug_screen_summary' => true,
    'claimant_medicare_beneficiary' => true,
    'claimant_payments_non_medical' => true,
    'date_medicare_confirmed' => true,
    'claimant_medicare_claim_number' => true,
    'date_mmsea_last_reported' => true,
    'delete_if_field_with_medicate' => true,
    'no_fault' => true,
    'no_fault_insurance_limit' => true,
    'no_fault_exhaust_date' => true,
    'mmsea_relationship_to_beneficiary' => true,
    'mmsea_type_of_representative' => true,
    'msp_effective_date' => true,
    'msp_termination_date' => true,
    'msp_type' => true,
    'disposition_code' => true,
    'tpoc_date' => true,
    'tpoc_amount' => true,
    'tpoc_date_delayed' => true,
    'tpoc_date_filed' => true,
    'tpoc_date_court_approval' => true,
    'tpoc_date_payment_issued' => true,
    'tpoc_deleted' => true,
    'absence_total' => true,
    'total_lost_time' => true,
    'show_restricted_time' => true,
    'restriction_total' => true,
    'total_restricted_time' => true,
    'icd_classification' => true,
    'icd_diagnosis_codes' => true,
    'icd_procedure_codes' => true,
    'hours_worked' => true,
    'hourly_rate' => true,
    'weekly_rate' => true,
    'monthly_rate' => true,
    'employer_paid_salary_as_compensation' => true,
    'date_maximum_medical_improvement' => true,
    'date_claim_admin_knew_lost_time' => true,
    'maintenance_type_code_date' => true,
    'maintenance_type_correction_code_date' => true,
    'employee_id_assigned_by_jurisdiction' => true,
    'dum_death_result' => true,
    'dum_impaired_percent' => true,
    ContactsFields::NATIONALITY => true,
    ContactsFields::JOB_TITLE => true,
    'contact_numbers' => true,
    ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT => true,
    ContactsFields::APC_AWARE_REPORT => true,
    ContactsFields::APC_AAR => true,
    ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT => true,
    ContactsFields::REFERRAL_SUBJECT_CHILD => true,
    ContactsFields::INTERPRETER_USED => true,
    ContactsFields::CHILD_WITNESS => true,
    ContactsFields::WITNESS_AWARE_REPORT => true,
    ContactsFields::LINK_HEIGHT => true,
    ContactsFields::LINK_WEIGHT => true,
    ContactsFields::LINK_ORGANISATION => true,
    ContactsFields::SOURCE_OF_RECORD => true,
    ContactsFields::API_SOURCE => true,
];

$GLOBALS['ReadOnlyFields'] = [
    'msp_effective_date' => true,
    'msp_termination_date' => true,
    'msp_type' => true,
    'disposition_code' => true,
    'claimant_medicare_claim_number' => true,
    'date_medicare_confirmed' => true,
];

$GLOBALS['NewPanels'] = [
    'link' => true,
    'contact' => true,
    'employee' => true,
    'risks' => true,
    'incidents' => true,
    'pals' => true,
    'complaints' => true,
    'word' => true,
    'progress_notes' => true,
];

$GLOBALS['UserExtraText'] = [
    'con_dod' => ['7' => 'Fill this in for a patient who has died'],
];

$GLOBALS['ExpandFields'] = [
    'lcom_iscomplpat' => [
        [
            'field' => 'link_patrelation',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
    'link_police_pursue' => [
        [
            'field' => 'link_police_persue_reason',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
    'link_plapat' => [
        [
            'field' => 'link_patrelation',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
];

$GLOBALS['ExpandSections'] = [
    'show_injury' => [
        [
            'section' => 'injury',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'show_illness' => [
        [
            'section' => 'illness',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'link_pprop_damaged' => [
        [
            'section' => 'property_section',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'show_document' => [
        [
            'section' => 'documents',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'include_in_tpoc' => [
        [
            'section' => 'tpoc',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
];
