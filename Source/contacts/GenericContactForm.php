<?php

/* Variables:
 * main_recordid : recordid in incidents_main, civ_main, pals_main etc.
 * con_recordid: recordid in users_main
 * conrep_recordid : recordid in increp_contacts, palrep_contacts etc.
 * link_recordid : recordid in link_contacts
 *
 */

// Called when the user clicks Cancel on the match list page.
use app\framework\DBALConnectionFactory;
use app\models\contact\ContactTypes;
use app\models\contact\hydrators\CarltonContactHydrator;
use app\models\contact\services\ContactService;
use app\models\document\valueObjects\LinkedRecord;
use app\models\forms\specifications\EditableFormSpecification;
use app\models\framework\config\DatixConfig;
use app\models\framework\config\DatixConfigFactory;
use app\models\generic\RecordSources;
use app\models\generic\valueObjects\Module;
use app\services\approvalStatus\ApprovalStatus;
use app\services\contacts\AgeCalculatorFactory;
use app\services\document\DocumentListServiceFactory;
use app\services\forms\PageTitleProvider;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Source\generic_modules\ModuleDefKeys;
use src\admin\model\specifications\UnlinkContactSpecification;
use src\component\form\FormTableFactory;
use src\contacts\externalcontacts\services\ExternalContactsService;
use src\contacts\model\ContactDataAdapter;
use src\contacts\model\TpocModelFactory;
use src\contacts\service\ExternalContactsServiceHelper;
use src\contacts\service\LinkedContactsService;
use src\formdesign\forms\service\Loaders\FormDesignInstanceLoader;
use src\framework\controller\Loader;
use src\framework\controller\Response;
use src\framework\events\FormEventsService;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\logger\Facade\Log;
use src\security\CompatEscaper;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

function ReshowContactLink()
{
    $mainRecordid = Sanitize::SanitizeInt($_POST['main_recordid']);

    $con = Sanitize::SanitizeStringArray($_POST);
    $con['link_exists'] = false;

    $mainLocation = Sanitize::SanitizeInt($_POST['main_locationid']);

    ShowContactForm($con, $mainRecordid, $mainLocation);
}

/**
 * @throws NotFoundExceptionInterface
 * @throws MapperException
 * @throws \Doctrine\DBAL\Driver\Exception
 * @throws ContainerExceptionInterface
 * @throws InvalidDataException
 * @throws InvalidParameterException
 * @throws JsonException
 * @throws \Doctrine\DBAL\Exception
 */
function ContactLink()
{
    $registry = Container::get(Registry::class);
    $moduleDefs = $registry->getModuleDefs();

    $module = Sanitize::getModule($_GET['module']);
    $mainRecordid = Sanitize::SanitizeInt($_GET['main_recordid']);

    LoggedIn();

    if ($_POST['link_action'] == 'Cancel') {
        ReshowContactLink();
    }

    $error = [];

    $externalContactsServiceHelper = Container::get(ExternalContactsServiceHelper::class);
    $isMatchOnMrnEnabled = $registry->getParm('MATCH_ON_MRN', 'N')->isTrue();
    $contactIdNumber = null;
    $contactIdNumberPresent = false;

    $lastContactSearched = $_SESSION[Module::CONTACTS]['LAST_SEARCH'];

    if (isset($lastContactSearched) && $isMatchOnMrnEnabled) {
        try {
            $contactIdNumber = $externalContactsServiceHelper
                ->getMrnNumber($lastContactSearched);

            $contactIdNumberPresent = $contactIdNumber !== null;
        } catch (Exception $e) {
            $error['message'] = $e->getMessage();
        }
    }

    $shouldGrabDataFromExternal = shouldGrabDataFromExternal($contactIdNumberPresent);

    if ($shouldGrabDataFromExternal) {
        try {
            $externalContactsService = Container::get(ExternalContactsService::class);
            $request = $externalContactsService->getExternalCheckedContacts(
                $lastContactSearched ?? [],
                [],
                $contactIdNumber,
            );
        } catch (Throwable $e) {
            Log::critical('Failed external contact lookup, exception thrown', ['exception' => $e]);
            $error['message'] = $e->getMessage();
        }
        $contact = $request[0] ?? [];

        $contact['source_of_record'] = RecordSources::API;

        if (!$isMatchOnMrnEnabled) {
            $contact['recordid'] = '';
        } else {
            $localCon = getLocalContactFromDb();
            if (!empty($localCon)) {
                $matchingContact = $externalContactsServiceHelper->mergeOnMrnIfSuitable([$contact], [$localCon]);
                $contact = $matchingContact[0];
            }
        }

        if ($contact['con_dob'] != '') {
            $contact['con_dob'] = date('Y-m-d H:i:s.000', strtotime(str_replace('T', ' ', $contact['con_dob'])));
        }

        if ($contact['con_dod'] != '') {
            $contact['con_dod'] = date('Y-m-d H:i:s.000', strtotime(str_replace('T', ' ', $contact['con_dod'])));
        }


        $contact = array_merge(Sanitize::SanitizeStringArray($_POST), $contact);
    // If the link already exists and is being updated without being matched to a new contact
    } elseif ($_GET['link_recordid'] != '') {
        $dbal = (new DBALConnectionFactory())->getInstance();
        $isRespondent = $_GET['link_type'] === ContactTypes::INDIVIDUAL_RESPONDENT;
        $fieldArray = $isRespondent ? 'LINKED_FIELD_ARRAY_RESPONDENT' : 'LINKED_FIELD_ARRAY';

        // also need initials to check that someone is a user, so we know whether their email address is editable.
        // also need remote id to check if this contact came from an external source
        $qb = $dbal->createQueryBuilder();
        $qb->addSelect(...array_map(static function (string $field): string {
            return "c.[{$field}]";
        }, $moduleDefs[Module::CONTACTS]['FIELD_ARRAY_SELECT']))
            ->addSelect(...$moduleDefs[Module::CONTACTS][$fieldArray])
            ->addSelect(
                'c.recordid AS con_recordid',
                'con_remote_id',
                'con_id',
            );

        if ($isRespondent) {
            $qb->addSelect('l.recordid as link_recordid')
                ->addSelect('l.recordid')
                ->addSelect('l.updateid')
                ->from('link_respondents', 'l')
                ->join('l', 'contacts_main', 'c', 'c.recordid = l.con_id')
                ->andWhere('l.recordid = :link_recordid')
                ->andWhere('l.main_recordid = :main')
                ->andWhere('l.main_module = :mainModule')
                ->setParameter('mainModule', $module);
        } else {
            $qb->addSelect('c.recordid')
                ->addSelect('c.updateid')
                ->addSelect("l.{$moduleDefs[$module]['FK']}")
                ->addSelect('l.link_recordid')
                ->from('link_contacts', 'l')
                ->join('l', 'contacts_main', 'c', 'c.recordid = l.con_id')
                ->andWhere('l.link_recordid = :link_recordid')
                ->andWhere("l.{$moduleDefs[$module]['FK']} = :main");
        }

        $contact = $qb->setParameter('link_recordid', $_GET['link_recordid'])
            ->setParameter('main', $mainRecordid)
            ->executeQuery()
            ->fetchAssociative();


        if ($contact === false) {
            throw new Exception('Unable to retrieve details for contact with link id ' . $_GET['link_recordid'] . '.');
        }

        $contact['link_exists'] = true;

        foreach (Container::get(FormEventsService::class)
            ->triggerEventReturnGenerator(
                ModuleDefKeys::EXTRA_CONTACT_LINK_DATA_EVENT,
                $contact,
                $module,
            ) as $result) {
            if (is_array($result)) {
                $contact = array_merge($contact, $result);
            }
        }

        if (in_array($module, [Module::INCIDENTS, Module::FEEDBACK], true)) {
            $contact['link_position'] = (new ContactService())->getLinkPosition($module, $contact);
        }


        // todo: refactor single use
        if ($module === Module::INCIDENTS) {
            $sql = "SELECT inc_injury, inc_bodypart, death_result_injury, permanent_impairment_percentage, recordid, listorder,
                con_id, inc_id
                FROM inc_injuries
                WHERE con_id = {$contact['con_id']}
                AND inc_id = " . $contact[$moduleDefs[$module]['FK']] . '
                ORDER BY listorder ASC';

            $request = db_query($sql);

            while ($row = db_fetch_array($request)) {
                $contact['injury_table'][$row['listorder']] = $row;
            }

            // Check for property information in inc_personal_property
            $sql = "SELECT recordid, ipp_description, ipp_damage_type, ipp_value, listorder,
                con_id, inc_id
                FROM inc_personal_property
                WHERE con_id = {$contact['con_id']}
                AND inc_id = " . $contact[$moduleDefs[$module]['FK']] . '
                ORDER BY listorder ASC';

            $request = db_query($sql);

            while ($row = db_fetch_array($request)) {
                $contact['property_table'][$row['listorder']] = $row;
            }
        } elseif (in_array($module, [Module::CLAIMS, Module::SAFEGUARDING], true)) {
            $sql = 'SELECT injury, bodypart, death_result_injury, permanent_impairment_percentage, listorder
            FROM link_injuries
            WHERE link_id = :link_id
            ORDER BY listorder ASC';


            $injuries = DatixDBQuery::PDO_fetch_all($sql, ['link_id' => $contact['link_recordid']]);

            foreach ($injuries as $injury) {
                $contact['injury_table'][$injury['listorder']] = $injury;
            }

            if ($registry->getParm('SHOW_MMSEA_FIELDS', 'N')->isTrue()) {
                $factory = new TpocModelFactory();

                $tpocRecords = $factory->getMapper()->findByLinkId($contact['link_recordid']);

                foreach ($tpocRecords as $tpocRecord) {
                    // Prefix recordid variable with tpoc_ to avoid clashing with contact recordid field
                    $tpocRecord['tpoc_recordid'] = $tpocRecord['recordid'];

                    $contact['tpoc'][$tpocRecord['tpoc_listorder']] = $tpocRecord;
                }
            }
        }
    // If an existing contact has been matched for linking
    } elseif ($_GET['con_recordid'] != '') {
        $contact = getMatchedLocalContact($module, $mainRecordid);
    }

    // If the contact might have changed we need to get rid of the link details which may now be innaccurate
    if (!$_GET['from_contact_match']) {
        unset($_SESSION['CON']['LINK_DETAILS']);
    }

    if (is_array($_SESSION['CON']['LINK_DETAILS']['injury']) && $module == 'INC') {
        foreach ($_SESSION['CON']['LINK_DETAILS']['injury'] as $id => $injury) {
            if ($injury) {
                $_SESSION['CON']['LINK_DETAILS']['injury_table'][$id] = ['inc_injury' => $injury, 'inc_bodypart' => $_SESSION['CON']['LINK_DETAILS']['bodypart'][$id], 'death_result_injury' => $_SESSION['CON']['LINK_DETAILS']['death_result_injury'][$id], 'permanent_impairment_percentage' => $_SESSION['CON']['LINK_DETAILS']['permanent_impairment_percentage'][$id]];
            }
        }
    }

    if (!empty($contact['con_dob'])) {
        $dateOfDeath = null;
        if (!empty($contact['con_dod'])) {
            $dateOfDeath = new DateTime(UserDateToSQLDate($contact['con_dod']));
        }

        $dateOfBirth = new DateTime(UserDateToSQLDate($contact['con_dob']));
        $age = (new AgeCalculatorFactory())->create()->calculate($module, Sanitize::SanitizeInt($_GET['main_recordid']), $dateOfBirth, $dateOfDeath);

        if (isset($age['years'])) {
            $contact['link_age'] = $age['years'];
        }

        if (!isset($contact['link_age_band']) && !empty($age['years']) && !empty($age['days'])) {
            $ageCalculator = (new AgeCalculatorFactory())->create();
            $contact['link_age_band'] = $ageCalculator->getAgeBand($age['years'], $age['days']);
        }
    }

    // Save main module
    $contact['main_module'] = $module;

    if ($_GET['from_contact_match']) {
        $contact = Container::get(ContactDataAdapter::class)->prefillDefaultLinkData($contact);
    }

    if (isset($contact['recordid']) && $contact['recordid'] !== '') {
        $contact['show_document'] = shouldShowDocument($contact['recordid']);
    }

    $mainLocation = $_GET['main_locationid'] ?? '';

    if ($contact['link_recordid'] !== null) {
        $contact = array_merge($contact, GetExtraData(Module::CONTACTS, $contact['link_recordid'], $contact));
    }

    ShowContactForm($contact, $mainRecordid, $mainLocation, $error);
}

function getMatchedLocalContact(string $module, int $mainRecordid): array
{
    $con = getLocalContactFromDb();
    $con['conrep_recordid'] = Sanitize::SanitizeInt($_GET['conrep_recordid']);

    if ($module === Module::INCIDENTS) {
        addIncSpecificLinkDataToContact($con, $mainRecordid);
    }

    return $con;
}

function addIncSpecificLinkDataToContact(array $con, int $mainRecordid): array
{
    $sql = 'SELECT inc_injury, inc_bodypart, recordid, listorder,
                con_id, inc_id
                FROM inc_injuries
                WHERE con_id = :con_id
                AND inc_id = :inc_id
                ORDER BY listorder ASC';

    $rows = DatixDBQuery::PDO_fetch_all($sql, ['con_id' => $_GET['con_recordid'], 'inc_id' => $mainRecordid]);

    foreach ($rows as $row) {
        $con['injury_table'][$row['listorder']] = $row;
    }

    // Check for property information in inc_personal_property
    $sql = 'SELECT recordid, ipp_description, ipp_damage_type, ipp_value, listorder,
                con_id, inc_id
                FROM inc_personal_property
                WHERE con_id = :con_id
                AND inc_id = :inc_id
                ORDER BY listorder ASC';

    $rows = DatixDBQuery::PDO_fetch_all($sql, ['con_id' => $_GET['con_recordid'], 'inc_id' => $mainRecordid]);

    foreach ($rows as $row) {
        $con['property_table'][$row['listorder']] = $row;
    }

    return $con;
}

function getLocalContactFromDb(): array
{
    global $ModuleDefs;

    $contactFields = implode(', ', $ModuleDefs[Module::CONTACTS]['FIELD_ARRAY_SELECT']) . ' ,initials, con_remote_id';

    $sql = "SELECT recordid, recordid AS con_recordid, rep_approved, updateid, {$contactFields} FROM contacts_main
                WHERE recordid = :con_recordid";

    $result = DatixDBQuery::PDO_fetch($sql, ['con_recordid' => Sanitize::SanitizeInt($_GET['con_recordid'])]);

    return $result ?: [];
}

function shouldGrabDataFromExternal(bool $isMatchOnMrnEnabled): bool
{
    $external = $_GET['external'];
    if ($external !== '1') {
        return false;
    }

    $conRecordId = $_GET['con_recordid'];

    if ($isMatchOnMrnEnabled && $conRecordId) {
        return true;
    }

    if (!$conRecordId) {
        return true;
    }

    return false;
}

function shouldShowDocument($recordid)
{
    $linkedRecord = new LinkedRecord('CON', $recordid);
    $docListService = DocumentListServiceFactory::create();
    $list = $docListService->getListOfDocuments($linkedRecord);

    return count($list) > 0 ? 'Y' : 'N';
}

/**
 * @return never
 */
function ShowContactForm($con, $main_recordid, $mainLocation = '', $error = ''): void
{
    global $scripturl, $dtxtitle, $ModuleDefs, $JSFunctions, $OnSubmitJS;

    $globalFormLoader = Container::get(FormDesignInstanceLoader::class);

    $registry = Container::get(Registry::class);
    $datixConfig = Container::get(DatixConfig::class);

    $addMinExtension = $datixConfig->isMinifierOn() ? '.min' : '';

    AuditOpenRecord(Module::CONTACTS, $con['recordid'], '');

    // Give full permissions for the purposes of linking.
    $CONPerms = 'CON_FULL';
    if ($registry->getParm('CASCADE_PERMISSIONS', 'N')->isFalse()) {
        $CONPerms = $registry->getParm('CON_PERMS')->toScalar();
    }

    $con_recordid = is_numeric($con['con_recordid']) ? (int) $con['con_recordid'] : '';
    $contact_match_link_id = is_numeric($_GET['contact_match_link_id']) ? (int) $_GET['contact_match_link_id'] : '';
    $main_recordid = is_numeric($main_recordid) ? (int) $main_recordid : '';
    $link_exists = isset($con['link_exists']) ? (bool) $con['link_exists'] : false;
    $userSession = (new UserSessionFactory())->create();

    $ReadOnly = false;

    if (!$userSession->isFullorLocalAdmin()
        && $con_recordid
        && $registry->getParm('CONTACTS_ADMIN_ENABLED', 'N')->isTrue()
    ) {
        $ReadOnly = true;
    }

    if (!$con_recordid) {
        $FormType = 'New';
    }

    $module = $_REQUEST['module'] ?? '';
    $Module = $module;

    if (empty($con['main_module'])) {
        $con['main_module'] = $module;
    }

    // Check whether we are in ReadOnly mode
    // New session variable ContactsReadOnlyMode is used when we only
    // want to make the Contacts module read-only, not anything else
    if ($_SESSION['ReadOnlyMode'] || $_SESSION['ContactsReadOnlyMode']) {
        $ReadOnly = true;
    }

    if ($registry->getParm('CASCADE_PERMISSIONS', 'N')->isFalse()) {
        // ---------------------------------------------------------------//
        // Access based on DB table link_access_approvalstatus
        $AccessFlag = GetAccessFlag('CON', $CONPerms, $con['rep_approved'] ?: 'NEW');

        if ($AccessFlag == '') {
            $msg = 'You do not have the necessary permissions to view the ' . $ModuleDefs['CON']['REC_NAME'] . " with ID {$recordid}.";

            fatal_error($msg, 'Information');
        }

        if ($AccessFlag == 'R') {
            $ReadOnly = true;
        }
    }

    $LinkTypes = $ModuleDefs[$module]['CONTACTTYPES'];

    if ($link_exists) {
        if ($_REQUEST['link_type'] != $con['link_type']) {
            $con['link_type'] = Sanitize::SanitizeString($_REQUEST['link_type']);
        }

        $dtxtitle = $LinkTypes[$con['link_type']]['Name'];
    } else {
        $dtxtitle = 'Link new contact';
        $con['link_type'] = Sanitize::SanitizeString($_REQUEST['link_type']);
    }

    if ($con['link_status'] != '' && !$link_exists) {
        $con['con_subtype'] = $con['link_status'];
    }

    if ($con_recordid === '' && empty($con['source_of_record'])) {
        $con['source_of_record'] = RecordSources::MANUAL;
    }

    // Set this so that the link details section is displayed
    $ShowLinkDetails = true;
    SetUpFormTypeAndApproval('CON', $con, $FormType, $form_action, $CONPerms);

    $con['recordid'] = $con['con_recordid'];
    CheckForRecordLocks('CON', $con, $FormType, $sLockMessage);

    $FormDesign = Forms_FormDesign::GetFormDesign(['module' => 'CON', 'level' => 2, 'form_type' => $FormType, 'link_type' => $con['link_type'], 'parent_module' => $module]);
    $globalFormLoader->load($FormDesign);

    if (is_array($_SESSION['CON']['LINK_DETAILS'])) {
        foreach ($_SESSION['CON']['LINK_DETAILS'] as $fname => $fval) {
            if (!in_array(
                $fname,
                [$ModuleDefs[$module]['FK'], 'con_recordid', 'main_recordid', 'updateid', 'link_exists',
                    'link_recordid', 'injury_table', ],
            )
                && !($con[$fname] != '' && $fval == '')   // don't overwrite a db value with a blank
                && !($fname == 'link_age' && $con[$fname] != '') // don't overwrite a newly calculated age with one from a past submission that might now be wrong
                && !($fname == 'link_age_band' && $con[$fname] != '') // don't overwrite a newly calculated age band with one from a past submission that might now be wrong

            ) {
                $con[$fname] = $fval;
            } elseif ($fname == 'injury_table' && !empty($fval)) { // need to merge contacts.
                foreach ($fval as $injuryarray) {
                    $con[$fname][] = $injuryarray;
                }
            }
        }

        if (!empty($con['injury_table'])) {
            $mandatoryFields = $FormDesign->getMandatoryFields();
            $con['show_injury'] = isset($mandatoryFields['show_injury']) ? 'Y' : true;
        }
    }

    $_SESSION['CON']['LINK_DETAILS'] = [];

    if ($FormType != 'New') {
        $FormDesign->UnsetDefaultValues();
    }

    StoreContactLabels();

    SetUpApprovalArrays('CON', $con, $CurrentApproveObj, $ApproveObj, $FormType, $FormDesign, 'CON_FULL');

    /** @global $FormArray */
    require_once $ModuleDefs['CON']['BASIC_FORM_FILES'][2];

    $FormTitle = $LinkTypes[$con['link_type']]['Name'] ?? '';

    if (!$link_exists) {
        // todo: IQ-18603 replace with single translation
        $FormTitle = sprintf("%s '%s' %s %s", ucfirst(_fdtk('add')), $LinkTypes[$con['link_type']]['Name'], _fdtk('link_to'), $ModuleDefs[$module]['REC_NAME']);
    }

    if ($ReadOnly) {
        $FormType = 'ReadOnly';
    }

    // if a contact came from an external source, Datix users cannot make updates to that information, but may edit additional fields
    if (!empty($con['con_remote_id']) && $registry->getParm('EXTERNAL_CONTACTS', 'N')->isTrue() && $registry->getParm('ALLOW_EXTERNAL_CONTACTS_EDITED', 'N', true)->isFalse()) {
        $protectedECSFields = [];
        if ($datixConfig->isExternalContactsServiceEnabled()) {
            foreach (CarltonContactHydrator::CARLTON_FIELD_TO_CAPTURE_MAP as $prince) {
                if (is_array($prince)) {
                    $protectedECSFields = array_merge($protectedECSFields, $prince);

                    continue;
                }

                $protectedECSFields[] = $prince;
            }
        }

        $responseFields = array_merge(
            $registry->getParm('EXTERNAL_CONTACTS_RESPONSE_FIELDS')->toArray(),
            $protectedECSFields,
        );
        foreach ($responseFields as $responseField) {
            $FormDesign->ReadOnlyFields[$responseField] = true;
        }
    }

    $ConTable = FormTableFactory::create($FormType, 'CON', $FormDesign);

    if (!$link_exists) {
        $ConTable->setNewLink(true);
    }

    $ConTable->makeForm($FormArray, $con, 'CON');

    $_REQUEST['form_design_id'] = $ConTable->getFormDesign()->getId();

    $FormTitleDescr ??= '';
    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            Module::CONTACTS,
            $FormTitle,
            $FormTitleDescr,
        );

    // Display different options if showing a link or creating a link
    $HideLinkButton = $registry->getParm('REQUIRE_CONTACT_MATCH_CHECK')->isTrue()
        && !$contact_match_link_id
        && empty($con['con_remote_id'])
        && $con['rep_approved'] !== ApprovalStatus::FINAL_APPROVAL;

    $ButtonGroup = new ButtonGroup();
    $ButtonGroup->DefaultVals['rbWhat'] = 'match';

    if (!$_GET['print']) {
        $ButtonGroup->AddButton([
            'label' => (_fdtk('btn_back_to_record') ?: _fdtk('back_to') . ' ' . $ModuleDefs[$module]['REC_NAME']),
            'name' => 'btnBack',
            'id' => 'btnBack',
            'onclick' => getConfirmCancelJavascript('frmContact'),
            'action' => 'BACK',
            'class' => 'button-clear',
        ]);

        $payId = $_GET['payments_id'];
        if (is_numeric($payId)) {
            $ButtonGroup->AddButton([
                'label' => _fdtk('back_to') . ' ' . $ModuleDefs['PAY']['REC_NAME'],
                'name' => 'btnGoToPayment',
                'id' => 'btnGoToPayment',
                'onclick' => 'javascript:if(CheckChange()){SendTo(\'' . getRecordURL(['module' => 'PAY', 'recordid' => $payId]) . '\');}',
                'action' => 'BACK',
                'class' => 'button-clear',
            ]);
        }

        $hasPermissionToUnlink = Container::get(UnlinkContactSpecification::class)->isSatisfiedBy($module);

        if ($hasPermissionToUnlink && $link_exists) {
            if ($con['link_type'] == 'O') {
                $ButtonGroup->AddButton([
                    'label' => _fdtk('unlink_contact'),
                    'name' => 'btnUnlink',
                    'id' => 'btnUnlink',
                    'onclick' => 'unlinkRespondent(\'' . $main_recordid . '\', \'' . $con['link_recordid'] . '\', \'' . CompatEscaper::encodeCharacters($Module) . '\', \'contact\');',
                    'action' => 'DELETE',
                    'class' => 'button-warning',
                ]);
            } else {
                $ButtonGroup->AddButton([
                    'label' => _fdtk('unlink_contact'),
                    'name' => 'btnUnlink',
                    'id' => 'btnUnlink',
                    'onclick' => 'if(confirm(\'' . _fdtk('unlink_contact_confirm') . '\')){submitClicked = true;document.frmContact.rbWhat.value=\'unlink\';document.frmContact.submit()}',
                    'action' => 'DELETE',
                    'class' => 'button-warning',
                ]);
            }
        }

        if (!$ReadOnly) {
            if ($HideLinkButton) {
                $JSFunctions[] = 'jQuery(\'#rep_approved_row\').hide();jQuery(\'#btnSave\').hide();jQuery(\'#icon_link_btnSave\').hide();';
            }

            if (!$link_exists) {
                // If we are here, then we are looking at a contact that has been matched but not yet linked.
                $ButtonGroup->AddButton([
                    'label' => _fdtk('check_matching_contacts'),
                    'id' => 'btnCheckMatchingContacts',
                    'onclick' => ($HideLinkButton ? 'jQuery(\'#btnSave, #icon_link_btnSave, #rep_approved_row\').show();' : '') . 'MatchExistingContacts();',
                    'action' => 'SEARCH',
                    'class' => 'button-secondary',
                ]);
                $ButtonGroup->AddButton([
                    'label' => _fdtk('create_new_link'),
                    'name' => 'btnSave',
                    'id' => 'btnSave',
                    'onclick' => 'this.disabled=true;' . ($OnSubmitJS['btnSave'] ?: '') . 'selectAllMultiCodes();submitClicked = true;document.frmContact.rbWhat.value=\'new\';if(validateOnSubmit()){document.frmContact.submit()}else{this.disabled=false}',
                    'action' => 'SAVE',
                    'class' => 'button-primary',
                ]);
            } else {
                // If we are here, we are looking at a contact that has already been linked to a record.
                if ($con['rep_approved'] == 'UN' || $con['rep_approved'] == 'REJECT') {
                    $ButtonGroup->AddButton([
                        'label' => _fdtk('check_matching_contacts'),
                        'id' => 'btnCheckMatchingContacts',
                        'onclick' => ($HideLinkButton ? 'jQuery(\'#btnSave, #icon_link_btnSave, #rep_approved_row\').show();' : '') . 'MatchExistingContacts();',
                        'action' => 'SEARCH',
                        'class' => 'button-secondary',
                    ]);
                    $ButtonGroup->AddButton([
                        'label' => _fdtk('save_contact'),
                        'name' => 'btnSave',
                        'id' => 'btnSave',
                        'onclick' => 'this.disabled=true;' . ($OnSubmitJS['btnSave'] ?: '') . 'selectAllMultiCodes();submitClicked = true;document.frmContact.rbWhat.value=\'new\';if(validateOnSubmit()){document.frmContact.submit()}else{this.disabled=false}',
                        'action' => 'SAVE',
                        'class' => 'button-primary',
                    ]);
                } else {
                    $ButtonGroup->AddButton([
                        'label' => _fdtk('save_contact'),
                        'name' => 'btnSave',
                        'id' => 'btnSave',
                        'onclick' => 'this.disabled=true;' . ($OnSubmitJS['btnSave'] ?: '') . 'selectAllMultiCodes();submitClicked = true;document.frmContact.rbWhat.value=\'new\';if(validateOnSubmit()){document.frmContact.submit()}else{this.disabled=false}',
                        'action' => 'SAVE',
                        'class' => 'button-primary',
                    ]);
                }
            }
        }

        if (strpos($_SERVER['QUERY_STRING'], 'from_parent_record=1') != false) {
            $_REQUEST['fromsearch'] = true;
        }
    }

    if ($registry->getParm('VALID_NHSNO', 'Y')->isTrue()) {
        $JSFunctions[] = 'formatNhsNo(jQuery(\'#con_nhsno\'))';
        $JSFunctions[] = 'formatNhsNo(jQuery(\'#con_nhsno_row .field_input_div\'))';
    }

    // If we are in "Print" mode, we don't want to display the menu
    if ($FormType != 'Print') {
        GetSideMenuHTML(['module' => 'CON', 'table' => $ConTable, 'buttons' => $ButtonGroup, 'recordid' => $con_recordid, 'no_audit' => true, 'printLinkUrl' => $scripturl . '?' . $_SERVER['QUERY_STRING'] . '&print=1']);

        $template = null;

        template_header();
    } else {
        $template = new Template(['noMenu' => true]);

        template_header($template, null);
    }

    // TODO: this should be removed when this is refactored into a controller?><script type="text/javascript">globals.FormType = '<?php echo $FormType; ?>';</script>
    <script type="text/javascript" src="src/respondents/js/respondents<?php echo $addMinExtension; ?>.js"></script><?php

    if ($FormType == 'Print' || $_GET['print'] == 1) {
        // Add js for a popup to enable user to choose which sections are displayed.
        echo '<script type="text/javascript" src="src/generic/js/print' . $addMinExtension . '.js"></script>
        <script type="text/javascript">
        globals.FormType = \'' . $FormType . '\';
        globals.printSections = ' . json_encode($ConTable->printSections) . '
        </script>';
    }

    echo '<form method="post" name="frmContact" class="dtx-form" action="' . $scripturl . '?action=contactlinkactiongeneral' . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '') . '">'; ?>
<input type="hidden" name="con_recordid" value="<?php echo Sanitize::SanitizeInt($con_recordid); ?>" />
<input type="hidden" name="link_recordid" value="<?php echo is_numeric($con['link_recordid']) ? (int) $con['link_recordid'] : $contact_match_link_id; ?>" />
<input type="hidden" name="updateid" value="<?php echo Escape::EscapeEntities($con['updateid']); ?>" />
<input type="hidden" name="main_recordid" value="<?php echo Sanitize::SanitizeInt($main_recordid); ?>" />
<input type="hidden" name="main_location_id" value="<?php echo Sanitize::SanitizeInt($mainLocation); ?>" />
<input type="hidden" name="con_remote_id" value="<?php echo Escape::EscapeEntities($con['con_remote_id']); ?>" />
<input type="hidden" name="api_source" value="<?php echo htmlspecialchars($con['api_source']); ?>" />
<input type="hidden" name="source_of_record" value="<?php echo htmlspecialchars($con['source_of_record']); ?>" />
<input type="hidden" id="rbWhat" name="rbWhat" value="match" />
<input type="hidden" name="link_exists" value="<?php echo Escape::EscapeEntities($link_exists); ?>" />
<input type="hidden" name="link_type" value="<?php echo Escape::EscapeEntities($con['link_type']); ?>" />
<input type="hidden" name="form_action" value="" />
<input type="hidden" name="module" value="<?php echo Sanitize::getModule($module); ?>" />
<input type="hidden" name="from_contact_match" value="<?php echo Sanitize::SanitizeInt($_GET['from_contact_match']); ?>" />
<input type="hidden" name="contact_match_link_id" value="<?php echo Sanitize::SanitizeInt($contact_match_link_id); ?>" />
<input type="hidden" name="formlevel" id="formlevel" value="2" />
<?php
    if ($sLockMessage) {
        echo '
        <div class="lock_message">
            <div id="LockMessage">' . $sLockMessage . '</div>
            <div id="userLockMessage"></div>
        </div>';
    }

    if ($error) {
        echo '
        <div class="form_error_wrapper">
            <div class="form_error">' . _fdtk('form_general_errors') . '</div>' .
            (!empty($error['message']) ? '<div class="form_error">' . $error['message'] . '</div>' : '') .
            (!empty($error['Validation']['message']) ? '<div class="form_error">' . $error['Validation']['message'] . '</div>' : '') .
        '</div>';
    }

    $ConTable->makeTable();
    echo $ConTable->getFormTable();

    echo $ButtonGroup->getHTML();

    echo '
    </form>'; ?>
    <script type="text/javascript" src="src/incidents/js/lfpse.js"></script>
    <script type="text/javascript" src="js_functions/autopopulate.js"></script>
    <script type="text/javascript" language="javascript">
        function MatchExistingContacts()
        {
            document.frmContact.rbWhat.value = 'link';
            document.frmContact.form_action.value = 'link';
            var url = '<?php echo $scripturl . '?action=contactlinkactiongeneral'; ?>' +
            '<?php echo '&token=' .
            CSRFGuard::getCurrentToken(); ?>';
            document.frmContact.target = "wndMatchExisting";
            document.frmContact.onsubmit = "window.open(url, 'wndMatchExisting', 'dependent,menubar=false,screenX=200,screenY=330,titlebar,scrollbars,resizable')";
            selectAllMultiCodes();
            document.frmContact.submit();
            document.frmContact.target = "";
        }
        <?php echo MakeJavaScriptValidation($module, $FormDesign); ?>
        var submitClicked = false;
    </script>
<?php

    if ($FormType != 'Print') {
        echo JavascriptPanelSelect($Show_all_section, Sanitize::SanitizeString($_GET['panel']), $ConTable);
    }

    footer($template);

    obExit();
}

/**
 * @return never
 */
function ContactLinkAction(): void
{
    global $scripturl;

    $form_action = $_POST['rbWhat'];
    $module = Sanitize::getModule($_POST['module']);

    $moduleDefs = Container::get(ModuleDefs::class)->getModuleData($module);

    UnlockLinkedRecords(
        [
            'main' => [
                'module' => $module,
                'recordid' => $_POST['main_recordid'],
            ],
            'link' => [
                'module' => 'CON',
                'recordid' => $_POST['con_recordid'],
            ],
        ],
    );

    // set multi select values
    $config = (new DatixConfigFactory())->getInstance();
    if ($config->getMultiSelect2Enabled()) {
        foreach ($_POST as $Field => $Value) {
            $multiSelectFieldArr = $Value;
            if (is_array($multiSelectFieldArr)
                && count($multiSelectFieldArr) > 0
                && isset($_POST['preselected_' . $Field])) {
                $_POST[$Field] = explode(',', $_POST['preselected_' . $Field]);
            }
        }
    }

    switch ($form_action) {
        case 'link':
            $loader = new Loader();
            $controller = $loader->getController(
                ['controller' => src\contacts\controllers\ContactsDoSelectionController::class],
            );
            $controller->setRequestParameter('form_action', 'link');
            echo $controller->doAction('searchcontact');
            obExit();

            break;
        case 'new':
            // Update record last updated
            $loader = new Loader();
            $controller = $loader->getController(['controller' => src\generic\controllers\RecordController::class]);
            $controller->setRequestParameter('module', $module);
            $controller->setRequestParameter('recordId', $_POST['main_recordid']);
            $controller->doAction('updateRecordLastUpdated');

            Container::get(LinkedContactsService::class)->saveLinkedContact([
                'main_recordid' => Sanitize::SanitizeInt($_POST['main_recordid']),
                'module' => $module,
                'formlevel' => 2,
            ]);

            // redirect back to main record
            $panel = $_POST['link_type'] == 'O' ? 'respondents' : GetContactPanelName($module, $_POST['link_type']);

            $location = "{$scripturl}?action=" . ($moduleDefs->getGeneric() ? 'record&module=' . $module : $moduleDefs->getAction()) . '&recordid=' .
                            $_POST['main_recordid'] . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '') . '&panel=' . $panel;
            Container::get(Response::class)->redirect($location);

            break;
        case 'unlink':
            LoggedIn();
            // Update record last updated
            $loader = new Loader();
            $controller = $loader->getController(['controller' => src\generic\controllers\RecordController::class]);
            $controller->setRequestParameter('module', $module);
            $controller->setRequestParameter('recordId', $_POST['main_recordid']);
            $controller->doAction('updateRecordLastUpdated');
            $contactId = Sanitize::SanitizeInt($_POST['con_recordid']);
            $mainRecordId = Sanitize::SanitizeInt($_POST['main_recordid']);
            $contactLinkId = Sanitize::SanitizeInt($_POST['link_recordid']);
            $module = Sanitize::getModule($_POST['module']);
            Container::get(LinkedContactsService::class)->unlinkContact(
                $contactId,
                $mainRecordId,
                $contactLinkId,
                $module,
            );

            $panel = $_POST['link_type'] === 'O' ? 'respondents' : GetContactPanelName($module, $_POST['link_type']);
            $location = "{$scripturl}?action="
                . ($moduleDefs->getGeneric() ? 'record&module='
                . $module : $moduleDefs->getAction())
                . "&recordid={$mainRecordId}&panel=" . $panel;
            Container::get(Response::class)->redirect($location);

            break;
        case 'cancel':
        case 'Cancel':
            // redirect back to main record
            $panel = $_POST['link_type'] == 'O' ? 'respondents' : GetContactPanelName($module, $_POST['link_type']);

            $location = "{$scripturl}?action=" . ($moduleDefs->getGeneric() ? 'record&module=' . $module : $moduleDefs->getAction()) .
                            "&recordid={$_POST['main_recordid']}&panel=" . $panel . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '');
            Container::get(Response::class)->redirect($location);

            break;
    }

    obExit();
}

function ListContactsSection($aParams)
{
    global $scripturl, $ModuleDefs, $ListingDesigns;

    $module = $aParams['module'];
    $data = $aParams['data'];
    $FormType = $aParams['formtype'];

    $UseLists = (isset($aParams['uselists']) ? (bool) $aParams['uselists'] : true); // Just in case this came from $_POST/$_GET

    // Contacts are stored inside the data array
    $con = $data['con'];
    $recordid = $data['recordid'];

    $AdminUser = $_SESSION['AdminUser'];
    $Perms = GetParm($ModuleDefs[$module]['PERM_GLOBAL']);

    $LinkPerms = (!$_GET['print'] && (new EditableFormSpecification())->isSatisfiedBy($FormType) && CanLinkNewContacts());
    $ShowFullContactDetails = ($_GET['print'] && bYN(GetParm('PRINT_CON_DETAILS', 'Y')) && CanSeeContacts($module, $Perms, $data['rep_approved']));

    if ($UseLists) {
        echo '<li name="contacts_row" id="contacts_row">';
    } else {
        echo '<div name="contacts_row" id="contacts_row">';
    }

    echo '

<input type="hidden" name="contact_url" value="" />
';
    if ($_GET['panel'] == 'contacts' && $_GET['contactwarning']) {
        echo '
        <div class="new_windowbg">
            The report has been saved. The reference number is ' . $recordid . '
        </div>
        <div class="new_windowbg form_error">
            There are still unapproved contacts attached to this record. They are listed below.
        </div> ';
    }

    $ContactArray = $ModuleDefs[$module]['CONTACTTYPES'];

    foreach ($ContactArray as $Type => $ContactGroup) {
        $total = count($con[$Type]);

        echo '
        <div style="margin:5px;">
            <div class="section_title_row row_above_table">
                ' . ($ContactGroup['Max'] == 1 ? $ContactGroup['Name'] : $ContactGroup['Plural']) . '
            </div>
        ';


        $Listing = Listings_ListingFactory::getListing('CON', $ListingDesigns['contacts'][$Type]);
        $Listing->LinkModule = $module;
        $Listing->LoadColumnsFromDB();
        $Listing->LoadData($con[$Type]);
        $Listing->EmptyMessage = $ContactGroup['None'];

        echo $Listing->GetListingHTML($FormType);

        if ($LinkPerms && ($ContactGroup['Max'] == '' || $total < $ContactGroup['Max'])) {
            $url = $scripturl . '?action=' . ($ModuleDefs[$module]['GENERIC'] ? 'linkcontactgeneral' : $ModuleDefs[$module]['LINKCONTACTACTION']) . '&module=' . $module . '&main_recordid=' . Sanitize::SanitizeInt($_GET['recordid']) . '&formtype=' . CompatEscaper::encodeCharacters($FormType) . '&link_type=' . CompatEscaper::encodeCharacters($Type);

            echo '
            <div class="section_link_row row_below_table">
                <a href="javascript:if(CheckChange()){SendTo(\'' . $url . '\');}" >';

            if (_fdtk('create_new_link')) {
                echo CompatEscaper::encodeCharacters(_fdtk('create_new_link'));
            } else {
                echo 'Create a new ' . $ContactGroup['Name'] . ' link';
            }

            echo '</a>
            </div>';
        }
        echo '
        </div>';
    }

    if ($UseLists) {
        echo '
        </li>';
    } else {
        echo '
        </div>';
    }
}

/**
 * Gets the panel name for the contact link type defined in ModuleDefs.
 *
 * Returns the panel name (Default is contacts).
 *
 * @param string $Link_type Contact link type
 *
 * @return string panel name
 */
function GetContactPanelName($module, $Link_type)
{
    global $ModuleDefs;

    if (!empty($ModuleDefs[$module]['CONTACTTYPES'][$Link_type]['PanelName'])) {
        return $ModuleDefs[$module]['CONTACTTYPES'][$Link_type]['PanelName'];
    }

    return 'contacts_type_' . $Link_type;
}
