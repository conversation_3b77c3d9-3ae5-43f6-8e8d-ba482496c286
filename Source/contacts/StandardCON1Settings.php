<?php

use src\contacts\model\ContactsFields;
use src\contacts\model\ContactsSections;
use src\incidents\model\PSIMSLinkContactsFields;

/** @var array $con */
$GLOBALS['FormTitle'][7] = 'Datix Contact Form';

$GLOBALS['DefaultValues'] = [
    'lcom_dreceived' => 'TODAY',
    'link_type' => $con['link_type'],
];

$GLOBALS['MandatoryFields'] = [
    'link_role' => 'contact',
    'con_surname' => 'contact',
    PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME => 'contact',
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => 'contact',
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => 'contact',
    PSIMSLinkContactsFields::PSIMS_GENDER => 'contact',
    PSIMSLinkContactsFields::PSIMS_AGE_YEARS => 'contact',
    ContactsFields::CON_KNOW_ID_FIELD => ContactsSections::CONTACT_MATCHING,
];

$GLOBALS['HideFields'] = [
    'link_worked_alone' => true,
    'link_become_unconscious' => true,
    'link_req_resuscitation' => true,
    'link_hospital_24hours' => true,
    'lcom_dreceived' => true,
    'con_police_number' => true,
    'con_work_alone_assessed' => true,
    'sirs' => true,
    'property_section' => true,
    'link_age_band' => true,
    'link_date_admission' => true,
    'link_notify_progress' => true,
    'lost_restricted_time' => true,
    'con_city' => true,
    'con_state' => true,
    'con_county' => true,
    'time_employee_began_work' => true,
    'tax_id' => true,
    'con_social_security_number' => true,
    'con_middle_name' => true,
    'employee_state_hired' => true,
    'employment_termination_date' => true,
    'full_pay_injury_day' => true,
    'salary_continued' => true,
    'con_employment_status_code' => true,
    'con_process_level' => true,
    'con_job_code' => true,
    'con_supervisor_name' => true,
    'con_department' => true,
    'con_location_code' => true,
    'con_fte' => true,
    'con_lawson_number' => true,
    'show_illness' => true,
    'illness' => true,
    'link_illness' => true,
    'absence_total' => true,
    'total_lost_time' => true,
    'show_restricted_time' => true,
    'restriction_total' => true,
    'total_restricted_time' => true,
    'dum_death_result' => true,
    'dum_impaired_percent' => true,
    ContactsFields::NATIONALITY => true,
    ContactsFields::JOB_TITLE => true,
    'contact_numbers' => true,
    ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT => true,
    ContactsFields::APC_AWARE_REPORT => true,
    ContactsFields::APC_AAR => true,
    ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT => true,
    ContactsFields::REFERRAL_SUBJECT_CHILD => true,
    ContactsFields::INTERPRETER_USED => true,
    ContactsFields::CHILD_WITNESS => true,
    ContactsFields::WITNESS_AWARE_REPORT => true,
    ContactsFields::LINK_HEIGHT => true,
    ContactsFields::LINK_WEIGHT => true,
    ContactsFields::LINK_ORGANISATION => true,
    ContactsFields::SOURCE_OF_RECORD => true,
    ContactsFields::API_SOURCE => true,
    ContactsFields::CON_KNOW_ID_FIELD => true,
    ContactsSections::CONTACT_MATCHING => true,
];

$GLOBALS['UserExtraText'] = [
    'con_dod' => ['7' => 'Fill this in for a patient who has died'],
];

$GLOBALS['ExpandFields'] = [
    'lcom_iscomplpat' => [
        [
            'field' => 'link_patrelation',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
    'con_police_pursue' => [
        [
            'field' => 'con_police_persue_reason',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
    'link_plapat' => [
        [
            'field' => 'link_patrelation',
            'alerttext' => '',
            'values' => [
                'N',
            ],
        ],
    ],
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => [
        [
            'field' => PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM,
            'alerttext' => '',
            'values' => [
                '2',
                '3',
                '4',
                '5',
            ],
        ],
        [
            'field' => PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION,
            'alerttext' => '',
            'values' => [
                '1',
                '2',
                '3',
            ],
        ],
    ],
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => [
        [
            'field' => PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION,
            'alerttext' => '',
            'values' => [
                '1',
                '2',
            ],
        ],
    ],
];

$GLOBALS['ExpandSections'] = [
    'show_injury' => [
        [
            'section' => 'injury_section',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'show_illness' => [
        [
            'section' => 'illness',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
    'link_pprop_damaged' => [
        [
            'section' => 'property_section',
            'alerttext' => '',
            'values' => [
                'Y',
            ],
        ],
    ],
];

$GLOBALS['HelpTexts'] = [
    'psims_age_years' => ['7' => 'The patient\'s age in years when the exact age is unknown'],
    PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION => ['7' => 'Your answer should be based on your own judgement, given the information you have at this point, and can be changed if further information becomes available.'],
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => ['7' => file_get_contents(__DIR__ . '/HelpTexts/en_GB/psims_physical_harm.html')],
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => ['7' => file_get_contents(__DIR__ . '/HelpTexts/en_GB/psims_psychological_harm.html')],
    PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY => ['7' => 'This may be in their record, or you can ask them or a family member. If this information has not been provided by the patient or their family, please select ‘I don’t know’.'],
    PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME => ['7' => 'Describe any physical or psychological impact on the patient as a result of the incident, or how their care was subsequently changed as a result. Your answer should be based on the information you have at this point, and can be changed if further information becomes available.'],
];
