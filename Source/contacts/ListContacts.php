<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\framework\config\DatixConfigFactory;
use app\models\generic\valueObjects\Module;
use app\models\treeFields\TreeFieldCodeRetrieverFactory;
use app\services\contact\ConSearchTitleProvider;
use app\services\forms\PageTitleProvider;
use Source\generic_modules\FieldDefKeys;
use src\contacts\externalcontacts\services\ExternalContactsLegacyService;
use src\contacts\externalcontacts\services\ExternalContactsService;
use src\contacts\service\ContactIdNumbersServiceFactory;
use src\contacts\service\ExternalContactsServiceHelper;
use src\contacts\service\ExternalContactsServiceHelperFactory;
use src\contacts\specifications\ShowExternalContactResultsSpecification;
use src\formdesign\forms\factories\FormDesignFactory;
use src\framework\controller\Request;
use src\framework\controller\Response;
use src\framework\query\Query;
use src\framework\query\SqlWriter;
use src\framework\query\Where;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\search\atprompt\AtPromptSessionFactory;
use src\security\Escaper;
use src\system\container\facade\Container;
use src\security\CompatEscaper;
use src\system\database\FieldInterface;
use src\system\language\LanguageSessionFactory;

/**
 * @return never
 *
 * @throws InvalidDataException
 * @throws \Psr\Container\ContainerExceptionInterface
 * @throws \Psr\Container\NotFoundExceptionInterface
 * @throws \app\models\framework\config\InvalidConfigurationException
 * @throws \src\framework\query\exceptions\IncompleteQueryException
 */
function ListContacts($message = '', $userLogin = ''): void
{
    global $dtxtitle, $scripturl, $dtxdebug, $ModuleDefs, $FieldDefs;

    $registry = Container::get(Registry::class);

    // page load time display - only testing purpose
    if ($dtxdebug) {
        $starttime = explode(' ', microtime());
        $starttime = $starttime[1] + $starttime[0];
    }

    LoggedIn();

    $atPromptSession = (new AtPromptSessionFactory())->create();

    if ($_POST['promptsubmitted'] == '1') {
        // we've arrived here from the @prompt form, so use the request to set the @prompt values in the session
        $atPromptSession->set('CON', new Request());
    }

    $dtxtitle = _fdtk('contact_list');

    // Set LAST_PAGE to be able to redirect user when a Bacth Update is performed
    $_SESSION['LAST_PAGE'] = $scripturl . '?' . Sanitize::SanitizeString($_SERVER['QUERY_STRING']);

    if ($userLogin) {
        $MatchLetter = \UnicodeString::strtoupper($userLogin[0]);
    } elseif (isset($_GET['match'])) {
        $MatchLetter = Sanitize::SanitizeString($_GET['match']);
    }

    $fromsearch = (isset($_GET['fromsearch']) ? Sanitize::SanitizeString($_GET['fromsearch']) : 0);

    $LinkMode = $_GET['link'] === '1';

    if ($fromsearch || $MatchLetter < 'A' || $MatchLetter > 'Z') {
        $MatchLetter = 'all';
    }

    if (!$fromsearch) { // this will already have been done at the search stage otherwise.
        // get rid of any stored recordlist
        unset($_SESSION['CON']['RECORDLIST']);
    }

    $MatchLetter = EscapeQuotes($MatchLetter);

    $MatchField = Sanitize::SanitizeString($_GET['field']);

    if (!$MatchField || $MatchField == 'fullname') {
        $MatchField = 'con_surname';
        $orderby = 'con_surname, con_forenames, recordid';
    } elseif ($MatchField == 'con_forenames') {
        $orderby = 'con_forenames, con_surname, recordid';
    } else {
        $orderby = $MatchField . ($MatchField != 'recordid' ? ', recordid' : '');
    }

    $MatchField = EscapeQuotes($MatchField);

    $sort = Sanitize::SanitizeString($_GET['sort']);

    if ($sort != 'desc') {
        $sort = 'asc';
    }

    $listdisplay = $registry->getParm('LISTING_DISPLAY_NUM', 20)->toScalar();

    $page = 1;
    if ($_REQUEST['page']) {
        $page = Sanitize::SanitizeString($_REQUEST['page']);
    }

    if ($_SESSION['FROMMODULE'] != '' && $_SESSION[$_SESSION['FROMMODULE']]['CURRENTID'] != '') {
        $sql = 'SELECT ' . $ModuleDefs[$_SESSION['FROMMODULE']]['FIELD_NAMES']['NAME'] . ' as rectitle'
        . ' FROM ' . $ModuleDefs[$_SESSION['FROMMODULE']]['TABLE']
        . ' WHERE recordid =' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTID'];

        $result = db_query($sql);
        $row = db_fetch_array($result);
        $RecordTitle = $row['rectitle'];
    }

    $where = [];

    if ($LinkMode && $_SESSION['CON']['WHERELINK'] != '') {
        $where[] = '(' . $_SESSION['CON']['WHERELINK'] . ')';
    } elseif ($fromsearch && $_SESSION['CON']['NEW_WHERE'] instanceof Where) {
        $table = 'contacts_main';
        $whereObject = clone $_SESSION['CON']['NEW_WHERE'];

        if (!$atPromptSession->hasBeenSet('CON') && $whereObject->hasAtPrompt()) {
            redirectToAtpromptForm();
        }

        $query = (new Query())->select([$table . '.recordid'])
            ->from($table)
            ->where($whereObject)
            ->groupBy([$table . '.recordid']);

        $writer = Container::get(SqlWriter::class);

        [$statement, $parameters] = $writer->writeStatement($query);

        foreach ($parameters as $parameter) {
            $statement = $writer->replacePlaceholdersInStatement($parameter, $statement);
        }

        $where[] = $table . '.recordid IN (' . $statement . ')';
    } elseif ($fromsearch && $_SESSION['CON']['WHERE'] != '') {
        if (!$atPromptSession->hasBeenSet('CON') && \UnicodeString::stripos($_SESSION['CON']['WHERE'], '@PROMPT') !== false) {
            redirectToAtpromptForm();
        }

        $where[] = '(' . $_SESSION['CON']['WHERE'] . ')';
    } elseif (isset($MatchLetter) && $MatchLetter != 'all') {
        $_SESSION['CON']['WHERE'] = "({$MatchField} LIKE '{$MatchLetter}%')";
        $where[] = '(' . $_SESSION['CON']['WHERE'] . ')';
    } elseif ($MatchLetter == 'all') {
        $_SESSION['CON']['WHERE'] = $ModuleDefs['CON']['HARD_CODED_LISTINGS']['all']['Where'];
    }

    $conPermWhere = MakeSecurityWhereClause('', 'CON', $_SESSION['initials']);

    if ($conPermWhere) {
        $where[] = "({$conPermWhere})";
    }

    if (!empty($where)) {
        $ContactsWhere = implode(' AND ', $where);
    }

    $ContactsWhere = TranslateWhereCom($ContactsWhere);

    // ==========================================================================//
    // Columns to be displayed                                                   //
    // ==========================================================================//

    $module = Module::CONTACTS;

    $listingId = (int) $registry->getParm('CON_MATCH_LISTING_ID')->toScalar() ?: 0;
    $Listing = Listings_ListingFactory::getListing($module, $listingId);

    $Listing->LoadColumnsFromDB();
    $list_columns = $Listing->Columns ?? [];

    $formDesign = Container::get(FormDesignFactory::class)->create(['module' => $module, 'level' => 2]);

    // ==========================================================================//
    // Prepare SQL Statement                                                    //
    // ==========================================================================//

    if (is_array($list_columns)) {
        foreach ($list_columns as $col_name => $col_info) {
            $table = GetTableForField($col_name, 'CON');

            if ($table == 'contacts_main') {
                $selectfields[$col_name] = $col_name;
            } else {
                // linked fields should be excluded
                unset($list_columns[$col_name]);
            }
        }
    }

    if (!isset($selectfields['recordid'])) {
        $selectfields['recordid'] = 'recordid';
    }

    foreach ($selectfields as $key => $field) {
        if (!isset($FieldDefs['CON'][$field])) {
            $selectfields[$key] = 'null as ' . $field;
        }
    }

    $isSupportAccount = $registry->getParm('DATIX_SUPPORT_ACCOUNT', 'N')->isTrue();
    $isExternalContactsEnabled = $registry->getParm('EXTERNAL_CONTACTS', 'N')->isTrue();
    $isMatchOnMrnEnabled = $registry->getParm('MATCH_ON_MRN', 'N')->isTrue();

    $externalContactsServiceHelper = Container::get(ExternalContactsServiceHelper::class);

    $contactIdNumber = null;
    $hasFailed = false;
    $failedMessage = null;

    $lastContactSearched = $_SESSION[$module]['LAST_SEARCH'];
    if ($isExternalContactsEnabled
        && $isMatchOnMrnEnabled
        && isset($lastContactSearched)) {
        try {
            $contactIdNumber = $externalContactsServiceHelper->getMrnNumber($lastContactSearched);
        } catch (Exception $e) {
            $hasFailed = true;

            $actualError = $e->getPrevious();

            if ($isSupportAccount && $actualError) {
                $failedMessage = $actualError->getMessage();
            }
        }
    }

    if ($isMatchOnMrnEnabled && !isset($selectfields['con_id_numbers'])) {
        $selectfields['con_id_numbers'] = 'con_id_numbers';
    }

    $request = GetPagedResults(
        'contacts_main',
        $selectfields,
        $ContactsWhere,
        $orderby,
        $sort,
        $page,
        $recordsPerPage,
        $overralCount,
        $actualCount,
        $sqloutout,
    );

    // ==========================================================================//
    // Push record list into session                                                    //
    // ==========================================================================//

    if (!$_SESSION['CON']['RECORDLIST']) {
        $sql = 'SELECT recordid FROM contacts_main';

        if ($ContactsWhere) {
            $sql .= ' WHERE ' . $ContactsWhere;
        }

        if ($orderby) {
            if ($_GET['sort'] == 'desc') {
                $order = 'DESC';
            } else {
                $order = 'ASC';
            }

            $orderByArray = explode(',', $orderby);

            foreach ($orderByArray as $orderByField) {
                $orderByArrayFinal[] = $orderByField . ' ' . $order;
            }

            $sql .= ' ORDER BY ' . implode(',', $orderByArrayFinal);
        }

        $RecordData = DatixDBQuery::PDO_fetch_all($sql, [], PDO::FETCH_COLUMN);

        $RecordList = new RecordLists_RecordListShell();
        $RecordList->AddRecordData($RecordData);

        $_SESSION['CON']['RECORDLIST'] = $RecordList;
    }

    $list_columns_count = count($list_columns);

    if ($LinkMode || $_SESSION['CON']['DUPLICATE_SEARCH']) {
        ++$list_columns_count;
    }

    $title = ($LinkMode ? _fdtk('matching_contacts') : _fdtk('contact_list'));
    Container::get(PageTitleProvider::class)
        ->preGeneratePageTitleHtml(
            '',
            Module::CONTACTS,
            $title,
        );

    if ($LinkMode) {
        echo getBasicHeader();
    } else {
        GetSideMenuHTML(['module' => 'CON', 'listing' => $_GET['fromsearch']]);

        $template = new Template(['noPadding' => true]);

        template_header($template, null);
    }

    if ($isSupportAccount) {
        echo $sqloutout;
    }

    $data = DatixDBQuery::PDO_fetch_all($request->queryString, []) ?? [];
    foreach ($data as $key => $val) {
        $recordids[] = $val['recordid'];

        // put spaces between the nhs number value
        if (strlen($val['con_nhsno']) == 10) {
            $data[$key]['con_nhsno'] = substr($val['con_nhsno'], 0, 3) . ' ' . substr($val['con_nhsno'], 3, 3) . ' ' . substr($val['con_nhsno'], 6, 4);
        }
    }

    $data = convertTreeFieldLabels($data, $FieldDefs, $module);

    foreach ($list_columns as $col_field => $width) {
        if (preg_match('/^UDF.*_([0-9]+)$/ui', $col_field, $matches)) {
            $udf_field_ids[] = $matches[1];
        }
    }

    $udfData = [];

    if (!empty($udf_field_ids) && !empty($recordids)) {
        $udfData = getUDFDisplayData($module, $recordids, $udf_field_ids);
    }


    echo '<table class="gridlines" align="center" border="0" cellpadding="4" cellspacing="1" width="100%">';
    if ($message !== '') {
        echo '<tr><td class="titlebg" colspan="' . $list_columns_count . '">' . $message . '</td></tr>';
    }

    if ($LinkMode || ($fromsearch && $_SESSION['FROMMODULE'])) {
        if ($LinkMode) {
            echo Container::get(ConSearchTitleProvider::class)->getSearchFieldTitle($list_columns_count);
            echo '<tr>
	    <td class="title-row" colspan="' . $list_columns_count . '" width="80%">' . _fdtk('matching_contacts') . '
	    </td></tr>';
        } else {
            echo '<tr>
	    <td class="title-row" colspan="' . $list_columns_count . '" width="80%">' . (($fromsearch && $_SESSION['FROMMODULE']) ? "Link to record '" . $RecordTitle . "' as type '" . $ModuleDefs[$_SESSION['FROMMODULE']]['SUBFORMS']['CON']['LINKTYPES'][$_SESSION[$_SESSION['FROMMODULE']]['CURRENTLINKTYPE']] . "'" : '') . '
	    </td></tr>';
        }
    }

    if (!$_GET['fromsearch']) {
        echo '<tr class="page-numbers"><td colspan="' . $list_columns_count . '">';

        for ($a = ord('A'); $a <= ord('Z'); ++$a) {
            $url = $scripturl . '?action=listcontacts&match=' . chr($a) . '&field=' . Escape::EscapeEntities($MatchField) . '&sort=' . Escape::EscapeEntities($sort) . '&page=1&fromsearch=' . Escape::EscapeEntities($fromsearch);

            echo "<a href=\"{$url}\">";

            if (ord($MatchLetter) == $a) {
                echo '[' . chr($a) . ']';
            } else {
                echo chr($a);
            }

            echo '</a>&nbsp;&nbsp;';
        }

        echo '&nbsp;&nbsp;&nbsp;';

        $url = $scripturl . '?action=listcontacts&match=all&sort=' . Escape::EscapeEntities($sort) . '&page=1&fromsearch=' . $fromsearch;

        echo "<a href=\"{$url}\">";

        if ($MatchLetter == 'all') {
            echo '[All]';
        } else {
            echo 'All';
        }

        echo '</a></td></tr>';
    } elseif (bYN(GetParm('SAVED_QUERIES', 'Y')) && !$LinkMode) {
        ?>    <tr>
        <td class="titlebg" colspan="<?php echo $list_columns_count; ?>" nowrap="nowrap">
            <form method="post" name="queryform" action="<?php echo "{$scripturl}?action=executequery&module=CON"; ?>">
            <table>
                <tr>
                    <td class="titlebg" width="100%">
                        <?php echo _fdtk('query_colon'); ?>
                        <select name="qry_name" <?php if (!is_array($saved_queries) || empty($saved_queries)) {
                            echo 'style="width:' . DROPDOWN_WIDTH_DEFAULT . 'px"';
                        } ?> onchange="Javascript:if (document.queryform.qry_name.options[document.queryform.qry_name.selectedIndex].value != 'range' && document.queryform.qry_name.options[document.queryform.qry_name.selectedIndex].value != '') {document.queryform.submit()}">
<?php
                        $query_recordid = Sanitize::SanitizeString($_GET['query']);
        $saved_queries = get_saved_queries('CON');
        if (is_array($saved_queries)) {
            echo '<option value=""'
                    . ($query_recordid == $query ? ' selected="selected"' : '') . '>'
                    . 'Choose</option>';

            foreach ($saved_queries as $query => $querytitle) {
                echo '<option value="' . $query . '"'
                    . ($query_recordid == $query ? ' selected="selected"' : '') . '>'
                    . src\security\Escaper::escapeForHTMLParameter($querytitle) . '</option>';
            }
        } ?>
                        </select>
                    </td>
                </tr>
            </table>
                <input type="hidden" name="orderby" value="<?php echo $orderby; ?>" />
            </form>
        </td>
    </tr>
<?php
    }

    if ($overralCount === 0 && !$isExternalContactsEnabled) {
        echo '<tr><td class="windowbg2" colspan="' . $list_columns_count . '">' . _fdtk('no_contacts_found') . '</td><tr>';
    } else {
        $contactString = _fdtk($overralCount === 1 ? 'contact' : 'contacts');
        if (!$isMatchOnMrnEnabled) {
            echo '<tr><td class="windowbg2" colspan="' . $list_columns_count . '">'
                . $overralCount . ' ' . $contactString . _fdtk('found_displaying')
                . (($page - 1) * $recordsPerPage + 1) . '-' . (($page - 1) * $recordsPerPage + $actualCount)
                . '.</td></tr>';
        }

        if ($overralCount > $recordsPerPage) {
            $prevPageLink = '';
            $nextPageLink = '';
            if ($page > 1) {
                $prevPageUrl = Sanitize::SanitizeURL($scripturl . '?action=listcontacts&module=CON&match=' . $MatchLetter
                . '&field=' . Escape::EscapeEntities($_GET['field']) . '&sort=' . Escape::EscapeEntities($_GET['sort'])
                . '&page=' . ($page - 1) . '&fromsearch=' . $fromsearch
                . ($LinkMode ? '&link=1' : '') . ($query_recordid ? "&query={$query_recordid}" : '')
                . (bYN(GetParm('CSRF_PREVENTION', 'N')) ? '&token=' . CSRFGuard::getCurrentToken() : ''), );

                $prevPageLink = '<a href="' . $prevPageUrl . '">' . _fdtk('previous_page') . '</a>';
            }

            if ($overralCount > ($recordsPerPage * ($page - 1) + $actualCount)) {
                $nextPageUrl = Sanitize::SanitizeURL($scripturl . '?action=listcontacts&module=CON&match=' . $MatchLetter
                . '&field=' . Escape::EscapeEntities($_GET['field']) . '&sort=' . Escape::EscapeEntities($_GET['sort'])
                . '&page=' . ($page + 1) . '&fromsearch=' . $fromsearch
                . ($LinkMode ? '&link=1' : '') . ($query_recordid ? "&query={$query_recordid}" : '')
                . (bYN(GetParm('CSRF_PREVENTION', 'N')) ? '&token=' . CSRFGuard::getCurrentToken() : ''), );

                $nextPageLink = '<a href="' . $nextPageUrl . '">' . _fdtk('next_page') . '</a>';
            } ?>
        <tr>
        <td class="windowbg" align="left" nowrap="nowrap" >
            <?php echo $prevPageLink; ?>
        </td>
        <td colspan="<?php echo $list_columns_count - 2; ?>">
<?php

        $tmplistnum = $overralCount;

            $pagenumber = ($_GET['page'] ?: 1);

            $CurrentPage = $pagenumber;
            if ($listdisplay != 0) {
                $maxpagenum = min([$pagenumber + 10, floor($overralCount / $listdisplay)]) + 1;
            }
            if ($pagenumber > 10) {
                $pagenumber = $pagenumber - 10;
            } else {
                $pagenumber = 1;
            }
            if ($CurrentPage > 11) {
                $backwardsURL = Sanitize::SanitizeURL($scripturl . '?action=listcontacts&match=' . $MatchLetter
            . '&field=' . Escape::EscapeEntities($_GET['field']) . '&sort=' . Escape::EscapeEntities($_GET['sort'])
            . '&page=' . $pagenumber . '&fromsearch=' . $fromsearch
            . ($LinkMode ? '&link=1' : '') . ($query_recordid ? "&query={$query_recordid}" : '')
            . (bYN(GetParm('CSRF_PREVENTION', 'N')) ? '&token=' . CSRFGuard::getCurrentToken() : ''), );

                echo '<a href="' . $backwardsURL . '">&lt;&lt;</a>';
            }

            while ($pagenumber <= $maxpagenum) {
                $pageURL = Sanitize::SanitizeURL($scripturl . '?action=listcontacts&match=' . $MatchLetter
            . '&field=' . Escape::EscapeEntities($_GET['field']) . '&sort=' . Escape::EscapeEntities($_GET['sort'])
            . '&page=' . $pagenumber . '&fromsearch=' . $fromsearch
            . ($LinkMode ? '&link=1' : '') . ($query_recordid ? "&query={$query_recordid}" : '')
            . (bYN(GetParm('CSRF_PREVENTION', 'N')) ? '&token=' . CSRFGuard::getCurrentToken() : ''), );

                echo '<a href="' . $pageURL . '">' . ($pagenumber == $CurrentPage ? '' . $pagenumber . '' : $pagenumber) . ' </a>';

                $pagenumber = $pagenumber + 1;
            }
            if ($listdisplay != 0 && $pagenumber < floor($overralCount / $listdisplay)) {
                $forwardsURL = Sanitize::SanitizeURL($scripturl . '?action=listcontacts&match=' . $MatchLetter
            . '&field=' . Escape::EscapeEntities($_GET['field']) . '&sort=' . Escape::EscapeEntities($_GET['sort'])
            . '&page=' . $pagenumber . '&fromsearch=' . $fromsearch
            . ($LinkMode ? '&link=1' : '') . ($query_recordid ? "&query={$query_recordid}" : '')
            . (bYN(GetParm('CSRF_PREVENTION', 'N')) ? '&token=' . CSRFGuard::getCurrentToken() : ''), );
                echo '<a href="' . $forwardsURL . '">&gt;&gt;</a>';
            }
            echo '</td>'; ?>
        </td>
        <td class="windowbg" align="right">
            <?php echo $nextPageLink; ?>
        </td>
        </tr>
<?php
        }

        echo '<tr class="tableHeader">';

        if (is_array($list_columns)) {
            // Take into account extra column
            if ($LinkMode) {
                echo '<th class="windowbg" width="6%" align="center">' . _fdtk('choose') . '</th>';
            } elseif ($_SESSION['CON']['DUPLICATE_SEARCH']) { // flag for duplicate searching
                echo '<th class="windowbg" width="2%" align="center">';

                if (count($_SESSION['CON']['RECORDLIST']->FlaggedRecords) == count($_SESSION['CON']['RECORDLIST']->Records)) {
                    echo '<img src="images/flag_filled_list_header.png" ';
                } else {
                    echo '<img src="images/flag_unfilled_list_header.png" ';
                }

                echo '
                id="record_flag_all" class="record_flag_list_header" style="cursor:pointer"
                onclick="
                    if(jQuery(\'#record_flag_all\').attr(\'src\')==\'images/flag_unfilled_list_header.png\')
                    {
                        FlagAllRecords(\'CON\');
                        jQuery(\'.record_flag\').attr(\'src\', \'images/flag_filled.png\')
                        jQuery(\'.record_flag_list_header\').attr(\'src\', \'images/flag_filled_list_header.png\')
                    }
                    else
                    {
                        UnFlagAllRecords(\'CON\');
                        jQuery(\'.record_flag\').attr(\'src\',\'images/flag_unfilled.png\')
                        jQuery(\'.record_flag_list_header\').attr(\'src\',\'images/flag_unfilled_list_header.png\')
                    }
                ">';


                echo '</th>';
            }

            $languageSession = LanguageSessionFactory::getInstance();

            foreach ($list_columns as $col_field => $col_info) {
                echo '<th class="windowbg"' . ($col_info['width'] ? 'width="' . $col_info['width'] . '%"' : '') . '>';
                if ($FieldDefs[$module][$col_field]['Type'] != 'textarea' && in_array($col_field, $ModuleDefs[$module]['FIELD_ARRAY']) || $col_field == 'recordid') {
                    echo '<a href="' . "{$scripturl}?action=listcontacts&match={$MatchLetter}"
                    . '&field=' . $col_field . '&sort=' . ($_GET['field'] == $col_field && $sort == 'asc' ? 'desc' : 'asc')
                    . "&page=1&fromsearch={$fromsearch}" . ($LinkMode ? '&link=1' : '')
                    . ($query_recordid ? "&query={$query_recordid}" : '') . '">';
                }
                if ($col_field == 'mod_title') {
                    $currentCols['mod_title'] = 'Module';
                } elseif ($col_field == 'recordid') {
                    $currentCols[$col_field] = $languageSession->getFieldString('CONTACTS', $col_field);
                } else {
                    $currentCols[$col_field] = $languageSession->getFieldString('CONTACTS', $col_field);
                }
                echo '' . $currentCols[$col_field] . '';
                if ($FieldDefs[$module][$col_field]['Type'] != 'textarea' && in_array($col_field, $ModuleDefs[$module]['FIELD_ARRAY']) || $col_field == 'recordid') {
                    echo '</a>';
                }
                echo '</th>';
            }
        }

        echo '</tr>';
    }

    $shouldHandleExternalContacts = Container::get(ShowExternalContactResultsSpecification::class)->isSatisfiedBy($LinkMode, $overralCount);

    $mrnDuplicates = '';

    // Making sure both conditions are the same value to allow external contact matches if true or false.
    $mrnConditionsEqual = $isMatchOnMrnEnabled === ($contactIdNumber !== null);

    if ($shouldHandleExternalContacts && $mrnConditionsEqual) {
        $request = [];
        $hasFailed = false;
        $failedMessage = null;

        try {
            $externalContactsService = Container::get(ExternalContactsService::class);
            $request = $externalContactsService->getExternalCheckedContacts(
                $lastContactSearched ?? [],
                [],
                $contactIdNumber,
            );
        } catch (Throwable $e) {
            Log::critical('Error with external contact matching', ['exception' => $e]);
            $hasFailed = true;
            if ($isSupportAccount) {
                $failedMessage = $e->getMessage();
            }
        }
        $externalContactsService = (new DatixConfigFactory())->getInstance()->isExternalContactsServiceEnabled();

        if (empty($request)) {
            if ($overralCount == 0) {
                echo '<tr><td class="windowbg2" colspan="' . $list_columns_count . '">' . _fdtk('no_contacts_found') . '</td><tr>';
            }

            if (!$isMatchOnMrnEnabled) {
                outputLocalContacts($data, $LinkMode, $list_columns, $module, $udfData, $hasFailed, $failedMessage);
            }
        }


        if (!empty($request)) {
            $request = convertIdNumbers($request);
        }

        foreach ($request as $row) {
            if ($isMatchOnMrnEnabled) {
                $externalContactsServiceHelper = (new ExternalContactsServiceHelperFactory())->create();
                $matchedContact = $externalContactsServiceHelper->mergeOnMrnIfSuitable([$row], $data);
                $mrnDuplicates .= $matchedContact['duplicates'] ?? '';
                if (!empty($matchedContact[0])) {
                    $row = $matchedContact[0];
                }
            }
            echo '<tr class="listing-row">';

            $row = escapeData($row);

            $submitButton = '<td class="windowbg2">'
                . '<input type="button" value="' . _fdtk('choose') . '" onclick="console.log($(this).closest(\'select\').find(\'option:selected\').text());opener.location.href=\''
                . $scripturl
                . $_SESSION[Module::CONTACTS]['LINK']['URL'];

            if (array_key_exists('recordid', $row) && !$externalContactsService) {
                $submitButton .= "&{$_SESSION[Module::CONTACTS]['LINK']['MAIN_ID']['NAME']}={$row['recordid']}";
            }

            if (array_key_exists('con_remote_id', $row) && $externalContactsService) {
                $submitButton .= "&con_remote_id={$row['con_remote_id']}";
            }

            $submitButton .= '&external=1&from_contact_match=1&contact_match_link_id=' . $_SESSION[Module::CONTACTS]['LINK']['LINK_RECORDID']['VALUE']
            . '&' . Escaper::escapeForHTMLParameter($_SESSION[Module::CONTACTS]['EXTERNAL_KEY']) . '=' . Escaper::escapeForHTMLParameter($row[$_SESSION[Module::CONTACTS]['EXTERNAL_KEY']])
            . ($row['recordid'] ? "&con_recordid={$row['recordid']}" : '')
            . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '')
            . '&token=' . CSRFGuard::getCurrentToken()
            . '\';window.open(\'\', \'_self\');window.close();" /></td>';

            echo $submitButton;

            if (!empty($row['con_dob'])) {
                $row['con_dob'] = ExternalContactsLegacyService::formatExternalContactsDate($row['con_dob']);
            }

            if (!empty($row['con_dod'])) {
                $row['con_dod'] = ExternalContactsLegacyService::formatExternalContactsDate($row['con_dod']);
            }

            foreach ($list_columns as $col_field => $col_info) {
                if ($FieldDefs[$module][$col_field]['Type'] == 'ff_select') {
                    $codeinfo = get_code_info($module, $col_field, $row[$col_field]);

                    $colour = '';

                    if ($codeinfo['cod_web_colour']) {
                        $colour = $codeinfo['cod_web_colour'];
                    }

                    if ($colour) {
                        echo '<td valign="left" style="background-color:#' . CompatEscaper::encodeCharacters($colour) . '"';
                    } else {
                        echo '<td class="windowbg2" valign="top"';
                    }
                } else {
                    echo '<td class="windowbg2" valign="top"';
                }

                echo '>';

                // extra field hack
                if (preg_match('/^UDF.*_([0-9]+)$/', $col_field, $matches)) {
                    echo $udfData[$row['recordid']][$matches[1]];
                } elseif ($col_field == 'recordid') {
                    echo Escaper::escapeForHTML($row[$col_field]);
                } elseif ($col_field == 'rep_approved') {
                    $codeinfo = get_code_info('INC', $col_field, $row[$col_field]);
                    echo Escape::EscapeEntities(FirstNonNull([$codeinfo['description']]));
                } elseif ($FieldDefs[$module][$col_field]['Type'] == 'ff_select') {
                    $codeinfo = get_code_info($module, $col_field, $row[$col_field]);
                    echo Escape::EscapeEntities($codeinfo['description']);
                } elseif ($FieldDefs[$module][$col_field]['Type'] == 'date') {
                    echo formatDateForDisplay($row[$col_field]);
                } elseif ($FieldDefs[$module][$col_field]['Type'] == 'textarea') {
                    echo Escaper::escapeForHTML($row[$col_field]);
                } elseif ($FieldDefs[$module][$col_field]['Type'] == 'time') {
                    if (\UnicodeString::strlen($row[$col_field]) == 4) {
                        $row[$col_field] = $row[$col_field][0] . $row[$col_field][1] . ':' . $row[$col_field][2] . $row[$col_field][3];
                    }

                    echo Escaper::escapeForHTML($row[$col_field]);
                } else {
                    echo $row[$col_field];
                }

                echo '</td>';
            }

            echo '</tr>';
            if (!$isMatchOnMrnEnabled) {
                ++$overralCount;
                ++$actualCount;
            }
        }
    } else {
        outputLocalContacts($data, $LinkMode, $list_columns, $module, $udfData, $hasFailed, $failedMessage);
    }


    if ($overralCount != 0 || $isExternalContactsEnabled) {
        ?>
        <tr>
        <td class="windowbg" align="left" nowrap="nowrap" colspan="<?php echo $list_columns_count - 1; ?>">
            <?php echo $prevPageLink; ?>
        </td>
        <td class="windowbg" align="right">
            <?php echo $nextPageLink; ?>
        </td>
        </tr>
<?php
    }

    if (!$LinkMode && $query_recordid == '' && $fromsearch && bYN(GetParm('SAVED_QUERIES', 'Y'))) {
        echo '<tr>
                  <td class="windowbg2" colspan="' . $list_columns_count . '">
                      <a href="' . $scripturl . '?action=savequery&module=CON&form_action=new">' . _fdtk('save_as_query') . '</a>
                  </td>
              </tr>';
    } ?>
	<tr>
    	<td class="button_wrapper" align="center" colspan="<?php echo $list_columns_count; ?>">
        	<?php
            if ($LinkMode) {
                echo '<input type="button" value="' . _fdtk('cancel') . '" onClick="window.open(\'\', \'_self\');window.close()">';
            } elseif ($fromsearch) {
                echo '<table><tr>';

                if ($_SESSION['FROMMODULE'] != '' && $overralCount > 0) {
                    echo '<td><form method="post" name="frmBatchLink" action="' . $scripturl
                    . '?action=' . $contactlinkaction
                    . '&' . $ModuleDefs[$_SESSION['FROMMODULE']]['FK'] . '=' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTID']
                    . '&formtype=Main&linktype=' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTLINKTYPE']
                    . '"><input type="hidden" name="form_action" value="batch" />'
                    . '<input type="submit" value="Link contacts (' . $overralCount . ') to '
                    . $ModuleDefs[$_SESSION['FROMMODULE']]['REC_NAME'] . '"  /></form></td>';
                }

                if ($_SESSION['FROMMODULE'] != '') {
                    echo '<td><form method="post" action="' . $scripturl . '?action=contactssearch&module=CON&from_module=' . $_SESSION['FROMMODULE'] . '&' . $ModuleDefs[$_SESSION['FROMMODULE']]['FK'] . '=' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTID'] . '&searchtype=lastsearch'
                    . '" name="contactaction"><input type="submit" value="' . _fdtk('btn_back') . '" /></form></td>';

                    echo '<td><form method="post" action="' . $scripturl . '?action=' . $ModuleDefs[$_SESSION['FROMMODULE']]['ACTION']
                    . '&recordid=' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTID']
                    . '&panel=' . $_SESSION[$_SESSION['FROMMODULE']]['CURRENTPANEL']
                    . '"><input type="submit" value="' . _fdtk('back_to') . ' ' . $ModuleDefs[$_SESSION['FROMMODULE']]['REC_NAME'] . '" /></form></td>';
                } else {
                    if ($_SESSION['CON']['DUPLICATE_SEARCH'] === true) {
                        echo '<td><input type="button" onclick="SendTo(\'?service=DuplicateSearch&event=performsearch\');" value="' . _fdtk('btn_back') . '"></td>';
                    } elseif ($_GET['query']) {
                        echo '<td><form method="post" action="' . $scripturl . '?action=savedqueries&module=CON&searchtype=lastsearch'
                            . '" name="contactaction"><input type="submit" value="' . _fdtk('btn_back') . '" /></form></td>';
                    } else {
                        echo '<td><form method="post" action="' . $scripturl . '?action=contactssearch&module=CON&searchtype=lastsearch'
                            . '" name="contactaction"><input type="submit" value="' . _fdtk('btn_back') . '" /></form></td>';
                    }

                    echo '<td><form method="post" action="' . $scripturl . '?module=CON">'
                    . '<input type="submit" value="' . _fdtk('btn_cancel') . '" /></form></td>';
                }

                echo '</tr></table>';
            } else {
                echo '<form method="post" action="' . $scripturl . '?action=newcontact" name="contactaction">'
                . '<input type="submit" value="' . _fdtk('add_new_contact') . '" /></form>';
            } ?>
		</td>
	</tr>
    <?php
        if (!empty($mrnDuplicates)) {
            echo '<tr><td class="badge-red" style="color: #F14B4B;" colspan="100%">' . _fdtk('duplicate_mrn_message2') . $mrnDuplicates . '</td></tr>';
        } ?>
</table>
<?php
if ($dtxdebug) {
    $mtime = explode(' ', microtime());
    $totaltime = $mtime[0] + $mtime[1] - $starttime;
    echo _fdtk('page_loaded_in') . ' ' . $totaltime . ' ' . _fdtk('seconds');
} ?>

<?php
    if ($LinkMode) {
        echo getBasicFooter();
    } else {
        footer();
    }

    obExit();
}

function outputLocalContacts(
    array $data,
    bool $LinkMode,
    array $list_columns,
    string $module,
    array $udfData,
    bool $externalSearchHasFailed = false,
    ?string $externalSearchErrorMessage = null
) {
    global $scripturl, $FieldDefs;

    // Retrieve list of already-linked contacts which cannot thus be re-linked.
    $DisallowedContacts = GetDisallowedContacts($_SESSION[Module::CONTACTS]['LINK_DETAILS']);

    $data = convertIdNumbers($data);

    if ($externalSearchHasFailed) {
        echo sprintf(
            '<tr><td colspan="%d" class="text-center bg-danger"><strong>%s</strong></td>',
            count($list_columns) + 1,
            _fdtk('unable_external_contacts') . ($externalSearchErrorMessage ? " ({$externalSearchErrorMessage})" : ''),
        );
    }

    foreach ($data as $key => $row) {
        echo '<tr class="listing-row">';
        $row = escapeData($row);

        if ($LinkMode) {
            if (is_array($DisallowedContacts) && in_array($row['recordid'], $DisallowedContacts)) {
                // This contact is already linked with the relevent link_type, and so cannot be re-selected to link again.
                echo '<td class="windowbg2" style="font-color:grey">'
                    . '<i>Link already exists</i></td>';
            } else {
                echo '<td class="windowbg2">'
                    . '<input type="button" value="' . _fdtk('choose') . '" onclick="
                    opener.location.href=\''
                    . $scripturl
                    . $_SESSION['CON']['LINK']['URL']
                    . '&from_contact_match=1&contact_match_link_id=' . $_SESSION['CON']['LINK']['LINK_RECORDID']['VALUE']
                    . "&{$_SESSION['CON']['LINK']['MAIN_ID']['NAME']}={$row['recordid']}"
                    . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '')
                    . '&token=' . CSRFGuard::getCurrentToken()
                    . '\';window.open(\'\', \'_self\');window.close();" /></td>';
            }
        } elseif ($_SESSION['CON']['DUPLICATE_SEARCH']) {
            $CurrentIndex = $_SESSION['CON']['RECORDLIST']->getRecordIndex(['recordid' => $row['recordid']]);

            // flag for duplicate searching.
            echo '<td class="windowbg2">';

            if ($_SESSION['CON']['RECORDLIST']->FlaggedRecords[$CurrentIndex]) {
                echo '<img src="images/flag_filled.png" ';
            } else {
                echo '<img src="images/flag_unfilled.png" ';
            }

            echo '
                id="record_flag_' . $row['recordid'] . '" class="record_flag" style="cursor:pointer"
                onclick="
                    if(jQuery(\'#record_flag_' . $row['recordid'] . '\').attr(\'src\')==\'images/flag_unfilled.png\')
                    {
                        FlagRecord(\'CON\', ' . $row['recordid'] . ');
                        jQuery(\'#record_flag_' . $row['recordid'] . '\').attr(\'src\', \'images/flag_filled.png\')
                    }
                    else
                    {
                        UnFlagRecord(\'CON\', ' . $row['recordid'] . ');
                        jQuery(\'#record_flag_' . $row['recordid'] . '\').attr(\'src\',\'images/flag_unfilled.png\')
                    }
                ">';
        }

        $url = "{$scripturl}?action=editcontact&module=CON&recordid={$row['recordid']}";

        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
        foreach ($list_columns as $col_field => $col_info) {
            if ($FieldDefs[$module][$col_field]['Type'] == 'ff_select') {
                $codeinfo = $codeInfoRetriever->retrieve("contacts_main.{$col_field}", $row[$col_field]);
                $colour = $codeinfo->getCodWebColour();

                if (!empty($colour)) {
                    echo '<td valign="left" style="background-color:#' . Escape::EscapeEntities($colour) . '"';
                } else {
                    echo '<td class="windowbg2" valign="top"';
                }
            } else {
                echo '<td class="windowbg2" valign="top"';
            }

            echo '>';

            if (!$LinkMode && $row[$col_field] || (preg_match('/^UDF.*_([0-9]+)$/', $col_field, $matches) && $udfData[$row['recordid']][$matches[1]])) {
                echo '<a href="' . $url . '">';
            }

            // extra field hack
            if (preg_match('/^UDF.*_([0-9]+)$/', $col_field, $matches)) {
                echo $udfData[$row['recordid']][$matches[1]];
            } elseif ($col_field == 'recordid') {
                echo $row[$col_field];
            } elseif ($col_field == 'rep_approved') {
                $codeinfo = $codeInfoRetriever->retrieve("contacts_main.{$col_field}", $row[$col_field]);
                echo $codeinfo->getDescription();
            } elseif ($FieldDefs[$module][$col_field]['Type'] == 'ff_select') {
                $codeinfo = $codeInfoRetriever->retrieve("contacts_main.{$col_field}", $row[$col_field]);
                echo Escape::EscapeEntities($codeinfo->getDescription());
            } elseif ($FieldDefs[$module][$col_field]['Type'] == 'date') {
                echo formatDateForDisplay($row[$col_field]);
            } elseif ($FieldDefs[$module][$col_field]['Type'] == 'textarea') {
                echo $row[$col_field];
            } elseif ($FieldDefs[$module][$col_field]['Type'] == 'time') {
                if (\UnicodeString::strlen($row[$col_field]) == 4) {
                    $row[$col_field] = $row[$col_field][0] . $row[$col_field][1] . ':' . $row[$col_field][2] . $row[$col_field][3];
                }

                echo $row[$col_field];
            } else {
                echo $row[$col_field];
            }

            if (!$LinkMode && $row[$col_field] || (preg_match('/^UDF.*_([0-9]+)$/', $col_field, $matches) && $udfData[$row['recordid']][$matches[1]])) {
                echo '</a>';
            }
            echo '</td>';
        }

        echo '</tr>';
    }
}

function escapeData(array $data): array
{
    global $FieldDefsExtra;

    foreach ($data as $field => $value) {
        if ($FieldDefsExtra[Module::CONTACTS][$field][FieldDefKeys::TYPE] !== FieldInterface::DATE_DB) {
            $data[$field] = Escaper::escapeForHTML($value);
        }
    }

    return $data;
}

/**
 * @param $module
 *
 * @throws Exception
 */
function convertTreeFieldLabels(array $data, array $FieldDefs, $module): array
{
    foreach ($data as $index => $contact) {
        foreach ($contact as $field => $value) {
            $isTreeField = $FieldDefs[$module][$field]['Type'] === 'tree';
            if ($isTreeField) {
                $mapperType = $FieldDefs[$module][$field]['mapperType'];
                $data[$index][$field] = (new TreeFieldCodeRetrieverFactory())
                    ->create($mapperType)
                    ->getBreadcrumb((int) $value);
            }
        }
    }

    return $data;
}

function convertIdNumbers(array $data): array
{
    foreach ($data as $index => $contact) {
        foreach ($contact as $field => $value) {
            if ($field === 'con_id_numbers') {
                $idNumberService = (new ContactIdNumbersServiceFactory())->create();
                $idNumberArray = $idNumberService->decodeIdNumbers($value);
                $data[$index][$field] = $idNumberService->getReportableIdNumberString($idNumberArray);

                break;
            }
        }
    }

    return $data;
}

/**
 * @desc Finds any contacts that cannot be linked to a particular record with a particular type since such a link
 * already exists.
 *
 * @param array $aParams array of parameters
 *
 * @return array An array of the contact record ids that cannot be re-linked.*
 */
function GetDisallowedContacts($aParams): array
{
    // we ignore "other contacts" for now, since sometimes people will be linked with different roles.
    if (!(
        $aParams['link_type']
        && $aParams['link_type'] != 'N'
        && $aParams['module']
        && $aParams['main_recordid']
    )) {
        return [];
    }

    // exception for complainants in complaints, since they are sometimes linked with link_type 'A'
    if ($aParams['module'] == Module::FEEDBACK
        && $aParams['link_type'] == 'C'
    ) {
        $sql = getDisallowedComplainantSql($aParams);
    } elseif (in_array($aParams['module'], [Module::CLAIMS, Module::REDRESS])
        && $aParams['link_type'] == 'O'
    ) {
        $sql = getDisallowedIndividualsLinkedAsRespondentsSql($aParams);
    } else {
        $sql = getDisallowedContactSql($aParams);
    }

    $resultSet = [];
    $result = db_query($sql);

    while ($row = db_fetch_array($result)) {
        $resultSet[] = $row['con_id'];
    }

    return $resultSet;
}

function getDisallowedComplainantSql(array $aParams): string
{
    global $ModuleDefs;

    return "
        SELECT con_id
        FROM link_contacts
        WHERE link_type = '{$aParams['link_type']}' AND
            {$ModuleDefs[$aParams['module']]['FK']} = {$aParams['main_recordid']}
        UNION
            SELECT link_contacts.con_id
            FROM link_contacts, link_compl
            WHERE link_contacts.con_id = link_compl.con_id AND
             link_contacts.com_id = link_compl.com_id AND
             link_contacts.link_type = 'A' AND
             link_contacts.com_id = {$aParams['main_recordid']} AND
             link_compl.lcom_iscomplpat = 'Y'
    ";
}

function getDisallowedIndividualsLinkedAsRespondentsSql(array $aParams): string
{
    return "
        SELECT con_id
        FROM link_respondents
        WHERE link_type = 'CON' AND
            main_recordid = {$aParams['main_recordid']} AND
            main_module = '{$aParams['module']}'
    ";
}

function getDisallowedContactSql(array $aParams): string
{
    global $ModuleDefs;

    return "
        SELECT con_id
        FROM link_contacts
        WHERE link_type = '{$aParams['link_type']}' AND
        {$ModuleDefs[$aParams['module']]['FK']} = {$aParams['main_recordid']}
    ";
}

/**
 * Redirects the request to the form used to enter concrete values for @prompt searches.
 *
 * Formerly resident in BrowseList.php.
 *
 * @return never
 */
function redirectToAtpromptForm(): void
{
    $location = 'index.php?action=atpromptform&module=' . Sanitize::getModule($_GET['module']) . '&query_id=' . ((int) $_GET['query']);

    if ($_GET['fromhomescreen']) {
        $location .= '&fromhomescreen=' . $_GET['fromhomescreen'];
    }

    Container::get(Response::class)->redirect($location);
}
