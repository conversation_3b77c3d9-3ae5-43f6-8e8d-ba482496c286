<?php

/**
 * LEVEL 1 CONTACT FORM.
 */

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use src\component\field\SelectFieldFactory;
use src\component\form\FormTable;
use src\contacts\condition\resolvers\PsimsLinkContactsConditionResolver;
use src\contacts\controllers\ContactIdNumbersController;
use src\contacts\helpers\ContactsSearchByIdHelper;
use src\contacts\model\ContactsFields;
use src\contacts\model\ContactsSections;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\incidents\model\PSIMSLinkContactsFields;
use src\psims\form\PsimsFieldPropertyManager;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);

$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$config = Container::get(DatixConfig::class);

// variables for globals table values
$showUsGeneralFields = $registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue();
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showNRLSFields = $registry->getParm('NRLS_ENABLED', 'N')->isTrue();
$showMultipleIdNumbersSection = $registry->getParm('MULTI_ID_NUMBER_SECTION', 'N')->isTrue();
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showPsimsFields = PsimsLinkContactsConditionResolver::resolve(
    $config,
    $module ?? $Module,
    $Type,
    $FormType,
    $main_recordid ?? null,
    new Request(),
);
if ($FormType == FormTable::MODE_DESIGN) {
    $showContactMatchingById = Container::get(ContactsSearchByIdHelper::class)->isForceSearchByIdEnabled();
} elseif ($aParams) {
    $showContactMatchingById = Container::get(ContactsSearchByIdHelper::class)->isForceSearchByIdVisible($aParams['level'], $aParams['FormType'], $aParams['suffix']);
} else {
    $showContactMatchingById = Container::get(ContactsSearchByIdHelper::class)->isForceSearchByIdVisible(1, $FormType, $Suffix ? (int) $Suffix : 0);
}

// The positions field on the user entity contains a json array of position strings. We need to manually create
// a dropdown containing these strings here as this is a completely custom data structure. This should only appear
// as a dropdown at the point of adding a contact based on a user. Since there is no contact-user link, we cannot
// even edit this data reliably in future.
$userSession = (new UserSessionFactory())->create();
$isLoggedIn = $userSession->isLoggedIn();
if ($isLoggedIn) {
    $positionJson = json_decode($userSession->getCurrentUser()->getPositions(), false);
    $positionArray = [];
    $today = new DateTime();
    if (is_array($positionJson)) {
        foreach ($positionJson as $positionObject) {
            if (!empty($positionObject)
            && (empty($positionObject->startDate) || (new DateTime($positionObject->startDate) <= $today))
            && (empty($positionObject->endDate) || (new DateTime($positionObject->endDate) >= $today))
            && !empty($positionObject->name)) {
                $positionArray[$positionObject->id] = $positionObject->name;
            }
        }
    }
    $positionField = SelectFieldFactory::createSelectField(
        'link_position' . ($Suffix ? '_' . $Suffix : ''),
        'CON',
        $data['link_position' . ($Suffix ? '_' . $Suffix : '')],
        'Edit',
        false,
        'Position',
        _fdtk('contact_details', $useFormDesignLanguage),
        $data['CHANGED-link_position' . ($Suffix ? '_' . $Suffix : '')],
    );
    $positionField->setCustomCodes($positionArray);
}

/*
 * This form array is used to generate the forms where a simpler implementation
 * of the Contact Form is required, with just the link and contact fields, and without sections
 *
 * It is used in the functions get_contact_section() and ShowFullContactDetails()
 */

if ($module) {
    $Module = $module; // because this will get called from multiple files
}

$linkDetailsSectionForNormalContacts = [
    ['Name' => 'link_role', 'ReadOnly' => $Type === ContactTypes::REPORTER],
    'link_status',
    'link_notes',
    'link_ref',
    'link_dear',
    ['Name' => 'link_npsa_role', 'Condition' => $registry->getParm('NRLS_ENABLED', 'N')->isTrue()],
    'link_deceased',
    'link_age',
    ContactsFields::LINK_HEIGHT,
    ContactsFields::LINK_WEIGHT,
    'link_age_band',
    'link_occupation',
    'link_riddor',
    'link_is_riddor',
    'link_daysaway',
    'link_mhact_section',
    'link_mhcpa',
    'link_marriage',
    ['Name' => 'link_sedation', 'Condition' => ($FormType == 'Design' || ($Module == 'INC' && $Type == 'A')) && $showNRLSFields],
    ['Name' => 'link_legalaid', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'link_lip', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'link_ndependents', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'link_agedependents', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'link_injuries', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'link_resp', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && $Type == 'E'))],
    [
        'Name' => 'link_abs_start',
        'Title' => 'Absence start',
        'Type' => 'date',
        'onChangeExtra' => 'getAbsenceDays(' . $Suffix . ')',
    ],
    [
        'Name' => 'link_abs_end',
        'Title' => 'Absence end',
        'Type' => 'date',
        'onChangeExtra' => 'getAbsenceDays(' . $Suffix . ')',
    ],
    'link_date_admission',
    [
        'Type' => 'formfield',
        'Name' => 'link_position',
        'FormField' => $positionField,
        'Condition' => ($FormType == 'Design' || ($isLoggedIn && ($Module == 'INC' || $Module === 'COM') && $Role == $registry->getParm('REPORTER_ROLE', 'REP')->toScalar())),
    ],
    ['Name' => 'link_plapat', 'Condition' => ($FormType == 'Design' || ($Module == 'CLA' && $Type == 'M'))],
    ['Name' => 'lcom_iscomplpat', 'Condition' => ($FormType == 'Design' || ($Module == 'COM' && $Type == 'C'))],
    ['Name' => 'lcom_dreceived', 'Condition' => ($FormType == 'Design' || ($Module == 'COM' && $Type == 'C'))],
    ['Name' => 'link_patrelation', 'Condition' => ($FormType == 'Design' || ($Module == 'COM' && $Type == 'C') || ($Module == 'CLA' && ($Type == 'M' || $Type == 'A')))],
    ['Name' => 'lcom_primary', 'Condition' => ($FormType == 'Design' || ($Module == 'COM' && $Type == 'C'))],
    ['Name' => 'link_notify_progress', 'Condition' => ($FormType == 'Design' || ($Role && $Role == GetParm('REPORTER_ROLE', 'REP')))],
    ['Name' => 'time_employee_began_work', 'Condition' => ($FormType == 'Design' || $FormType == 'Search' || ($Module == 'CLA' || $Module == 'INC'))],
    // FIX GLOBAL DECLARATIONS WHEN IQ-4040 MERGED IN
    ['Name' => 'full_pay_injury_day', 'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC'))],
    ['Name' => 'salary_continued', 'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC'))],
    [
        'Name' => ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT,
        'Condition' => $FormType == FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::APC_AWARE_REPORT,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::APC_AAR,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::REFERRAL_SUBJECT_CHILD,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::INTERPRETER_USED,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::CHILD_WITNESS,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    [
        'Name' => ContactsFields::WITNESS_AWARE_REPORT,
        'Condition' => $FormType === FormTable::MODE_DESIGN || $Module == Module::SAFEGUARDING,
    ],
    ContactsFields::LINK_ORGANISATION,
    ContactsFields::JOB_TITLE,
    ['Name' => ContactsFields::SOURCE_OF_RECORD, 'ReadOnly' => true],
    ['Name' => ContactsFields::API_SOURCE, 'Condition' => $FormType !== FormTable::MODE_DESIGN],
];


$linkDetailsSectionForRespondentsContacts = [
    'link_notes',
    'link_resp',
    'link_role',
];


$FormArray = [
    'Parameters' => [
        'Condition' => false,
        'Suffix' => $Suffix,
    ],
    ContactsSections::CONTACT_MATCHING . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('contact_matching'),
        'ContactSuffix' => $Suffix,
        'LinkType' => $Type,
        'Condition' => $showContactMatchingById,
        'NoFieldRemoval' => true,
        'NoFieldAdditions' => true,
        'Rows' => [
            [
                'Name' => ContactsFields::CON_KNOW_ID_FIELD,
                'Condition' => $showContactMatchingById,
                'NoReadOnly' => true,
                'AlwaysMandatory' => true,
            ],
        ],
    ],
    ($SectionName && $FormType != 'Design' ? $SectionName : 'contact') => [
        'Title' => ($Title ?: _fdtk('contact_details', $useFormDesignLanguage)),
        'ContactSuffix' => $Suffix,
        'LinkType' => $Type,
        'LinkRole' => $Role,
        'ContactsLinkTable' => ($Type == 'O' || $Type == 'G' ? 'link_respondents' : 'link_contacts'),
        'Rows' => array_merge($Type == 'O' ? $linkDetailsSectionForRespondentsContacts : $linkDetailsSectionForNormalContacts, [
            ['Name' => 'recordid', 'Title' => 'Contact ID', 'Type' => 'string', 'ReadOnly' => (($form_action == 'Design' || $form_action == 'search') ? false : true)],
            'con_title',
            'con_forenames',
            [
                'Name' => 'con_middle_name',
                'Condition' => $showUsGeneralFields,
            ],
            'con_surname',
            'con_email',
            [
                'Name' => 'con_line1',
            ],
            [
                'Name' => 'con_line2',
            ],
            [
                'Name' => 'con_line3',
            ],
            [
                'Name' => 'con_city',
            ],
            [
                'Name' => 'con_state',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'con_county',
            ],
            [
                'Name' => 'con_country',
            ],
            'con_postcode',
            'con_gender',
            [
                'Name' => 'con_dob',
                'ShowHijri' => $showSpscFields && !in_array($Module, [Module::REDRESS, Module::SAFEGUARDING], true),
                'onChangeExtra' => 'calculateHijri(' . $Suffix . ')',
            ],
            'con_dod',
            [
                'Name' => 'con_tel1',
                'Type' => 'string',
                'Title' => 'Telephone no. 1',
                'Width' => 30,
                'MaxLength' => 254,
            ],
            [
                'Name' => 'con_tel2',
                'Type' => 'string',
                'Title' => 'Telephone no. 2',
                'Width' => 30,
                'MaxLength' => 254,
            ],
            [
                'Name' => 'con_number',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            [
                'Name' => 'con_nhsno',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            [
                'Name' => 'con_social_security_number',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'con_police_number',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            ContactsFields::NATIONALITY,
            'con_ethnicity',
            'con_language',
            'con_disability',
            'con_religion',
            'con_sex_orientation',
            'con_work_alone_assessed',
            'con_type',
            'con_subtype',
            'con_notes',
            [
                'Name' => 'tax_id',
                'Condition' => $showUsClaimsFields,
            ],
            [
                'Name' => 'employee_state_hired',
                'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'employment_termination_date',
                'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_employment_status_code',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_process_level',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_job_code',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_supervisor_name',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_department',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_location_code',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_fte',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            [
                'Name' => 'con_lawson_number',
                'Condition' => $showUsGeneralFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
            ],
            'location_id',
            'service_id',
            [
                'Name' => 'show_injury',
                'Condition' => (
                    in_array($Module, [Module::INCIDENTS, Module::CLAIMS, Module::SAFEGUARDING], true)
                    || $FormType == FormTable::MODE_DESIGN
                    || (
                        $_GET['action'] == 'httprequest'
                        && $_GET['type'] == 'renderfields'
                        && in_array($_POST['parent_module'], [Module::INCIDENTS, Module::CLAIMS, Module::SAFEGUARDING], true)
                    )
                ),
            ],
            [
                'Name' => 'show_illness',
                'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'INC' || $Module == 'CLA') || ($_GET['action'] == 'httprequest' && $_GET['type'] == 'renderfields' && ($_POST['parent_module'] == 'INC' || $_POST['parent_module'] == 'CLA'))),
            ],
            [
                'Name' => 'con_empl_grade',
                'Condition' => $showNRLSFields,
            ],
            ['Name' => PSIMSLinkContactsFields::PSIMS_AGE_YEARS, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_AGE_YEARS]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_GENDER, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_GENDER]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY]],
        ]),
    ],
    'contact_numbers' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('contact_numbers', $useFormDesignLanguage),
        'Condition' => $showMultipleIdNumbersSection,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'ControllerAction' => [
            'showContactNumbersSection' => [
                'controller' => ContactIdNumbersController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $con['link_type'], 'contact_suffix' => $Suffix],
        'LinkFields' => true,
        'LinkType' => $con['link_type'],
        'Rows' => [
            [
                'Name' => 'con_number_type',
                'Title' => _fdtk('con_number_type', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
                'NoRadio' => true,
            ],
            [
                'Name' => 'con_id_number',
                'Title' => _fdtk('con_id_number', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
        ],
    ],
    'injury_section' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('injury_details', $useFormDesignLanguage),
        'ContactSuffix' => $Suffix,
        'Condition' => (
            $Type !== ContactTypes::REPORTER
            && (
                in_array($Module, [Module::INCIDENTS, Module::CLAIMS, Module::SAFEGUARDING])
                || $FormType == FormTable::MODE_DESIGN
            )
        ),
        'ControllerAction' => [
            'showInjuryDetailsSection' => [
                'controller' => src\contacts\controllers\InjuryDetailsController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $con['link_type'], 'contact_suffix' => $con['contactSuffix'] ?? 0],
        'LinkType' => $Type,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'Rows' => [
            [
                'Name' => 'dum_injury',
                'Title' => _fdtk('injury', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_bodypart',
                'Title' => _fdtk('body_part', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_death_result',
                'Title' => _fdtk('death_result_injury', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_impaired_percent',
                'Title' => _fdtk('permanent_impairment_percentage', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_treatment',
                'Title' => _fdtk('dum_treatment', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
        ],
    ],
    'lost_restricted_time' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('lost_and_restricted_time', $useFormDesignLanguage),
        'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'CLA' || $Module == 'INC')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'NotModes' => ['Search'],
        'ControllerAction' => [
            'showLostAndRestrictedTimeSection' => [
                'controller' => src\contacts\controllers\LostAndRestrictedTimeController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $Type, 'contact_suffix' => $Suffix],
        'LinkFields' => true,
        'LinkType' => $Type,
        'Rows' => [
            [
                'Name' => 'absence_start',
                'Title' => 'Start date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'absence_end',
                'Title' => 'Return date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'absence_total',
                'Title' => 'Number of days away',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => true,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'total_lost_time',
                'Title' => 'Total days away from work',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'show_restricted_time',
                'Title' => 'Was employee on restricted time?',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
            ],
            [
                'Name' => 'restriction_start',
                'Title' => 'Start date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'restriction_end',
                'Title' => 'End date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'restriction_total',
                'Title' => 'Number of days restricted',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => true,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'total_restricted_time',
                'Title' => 'Total days restricted time',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
        ],
    ],
    'illness' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('illness_details', $useFormDesignLanguage),
        'Condition' => $showUsClaimsFields && ($FormType == 'Design' || ($Module == 'INC' || $Module == 'CLA')),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'LinkFields' => true,
        'LinkType' => $Type,
        'Rows' => [
            'link_illness',
        ],
    ],
    'sirs' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('security_incident_details', $useFormDesignLanguage),
        'Condition' => ($Module == 'INC' || $FormType == 'Design'),
        'ContactSuffix' => $Suffix,
        'LinkType' => $Type,
        'Rows' => [
            'link_worked_alone',
            'link_become_unconscious',
            'link_req_resuscitation',
            'link_hospital_24hours',
            'link_clin_factors',
            'link_direct_indirect',
            'link_injury_caused',
            'link_attempted_assault',
            'link_discomfort_caused',
            'link_public_disorder',
            'link_verbal_abuse',
            'link_harassment',
            'link_police_pursue',
            'link_police_persue_reason',
            'link_pprop_damaged',
        ],
    ],
    'property_section' . ($Suffix ? '_' . $Suffix : '') => [
        'Title' => _fdtk('personal_property', $useFormDesignLanguage),
        'ContactSuffix' => $Suffix,
        'Condition' => ($Module == 'INC' || $FormType == 'Design'),
        'LinkType' => $Type,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ExtraParameters' => ['link_type' => $con['link_type']],
        'Rows' => [
            ['Name' => 'dum_description', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_description', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
            ['Name' => 'dum_property_type', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_damage_type', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
            ['Name' => 'dum_value', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_value', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
        ],
        'ControllerAction' => [
            'showPropertyDetailsSection' => [
                'controller' => src\contacts\controllers\PropertyDetailsController::class,
            ],
        ],
    ],
    'udf' . ($Suffix ? '_' . $Suffix : '') => [
        'ExtraFields' => $DIF1UDFGroups,
        'NoFieldAdditions' => true,
        'ContactSuffix' => $Suffix,
        'RecordID' => $con['recordid'],
        'LinkType' => $Type,
    ],
];

$FormArray = PsimsFieldPropertyManager::preventPsimsContactFieldConfiguration($FormArray);

return $FormArray;
