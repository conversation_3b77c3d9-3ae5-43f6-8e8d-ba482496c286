<?php

/**
 * LEVEL 2 CONTACT FORM.
 */

use app\models\contact\ContactTypes;
use app\models\framework\config\DatixConfigFactory;
use app\models\generic\valueObjects\Module;
use src\component\form\FormTable;
use src\contacts\condition\resolvers\PsimsLinkContactsConditionResolver;
use src\contacts\model\ContactsFields;
use src\framework\registry\Registry;
use src\incidents\model\PSIMSLinkContactsFields;
use src\psims\form\PsimsFieldPropertyManager;
use src\redress\models\RedressFields;
use src\framework\controller\Request;
use src\system\container\facade\Container;

$registry ??= Container::get(Registry::class);

$con['link_type'] = $con['link_type'] ?? $linkType ?? null;
$ShowLinkDetails ??= null;
$FormType ??= null;
$Module ??= $module ?? null;

require_once 'Source/generic_modules/COM/ModuleFunctions.php';

$conditionForInjuryFields = in_array($FormType, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true)
    || ($ShowLinkDetails && in_array($Module, [Module::INCIDENTS, Module::CLAIMS, MODULE::SAFEGUARDING], true));
$contactTypeIsClaimant = $Module === Module::CLAIMS && $con['link_type'] === ContactTypes::CLAIMANT;
$useFormDesignLanguage = $FormType === FormTable::MODE_DESIGN;
$config = (new DatixConfigFactory())->getInstance();

// variables for globals table values
$showUsClaimsFields = $registry->getParm('SHOW_US_CLAIMS_FIELDS', 'N')->isTrue();
$showUsGeneralFields = $registry->getParm('SHOW_US_GENERAL_FIELDS', 'N')->isTrue();
$showOshaFields = $registry->getParm('SHOW_OSHA_FIELDS', 'N')->isTrue();
$showMmseaFields = $registry->getParm('SHOW_MMSEA_FIELDS', 'N')->isTrue();
$showEdiFields = $registry->getParm('SHOW_EDI_FIELDS', 'N')->isTrue();
$showNRLSFields = $registry->getParm('NRLS_ENABLED', 'N')->isTrue();
$showMultipleIdNumbersSection = $registry->getParm('MULTI_ID_NUMBER_SECTION', 'N')->isTrue();
$nrlsEnabled = $registry->getParm('NRLS_ENABLED', 'N')->isTrue();
$reporterRole = $registry->getParm('REPORTER_ROLE', 'REP')->toScalar();
$insurancePermissions = $registry->getParm('POL_PERMS')->toScalar();
$isReporter = $con['link_type'] === ContactTypes::REPORTER;
$isExistingRecord = !empty($con['recordid']);
$showSpscFields = $registry->getParm('SPSC_DATASET', 'N')->isTrue();
$showPsimsFields = PsimsLinkContactsConditionResolver::resolve(
    $config,
    $Module ?? null,
    $con['link_type'],
    $FormType ?? $formType ?? null,
    $main_recordid ?? null,
    new Request(),
);

$linkDetailsSectionForNormalContacts = [
    'Title' => _fdtk('link_details', $useFormDesignLanguage),
    'Condition' => $ShowLinkDetails && !in_array($con['link_type'], [ContactTypes::INDIVIDUAL_RESPONDENT, ContactTypes::RESPONDENT], true),
    'LinkFields' => true,
    'ContactsLinkTable' => _fdtk('link_details', $useFormDesignLanguage),
    'Rows' => [
        [
            'Name' => 'link_role',
            'ReadOnly' => (
                $isReporter
                && $isExistingRecord
            ),
        ],
        'link_status',
        'link_occupation',
        'link_abs_start',
        'link_abs_end',
        'link_daysaway',
        'link_is_riddor',
        'link_riddor',
        'link_age',
        'link_age_band',
        ContactsFields::LINK_HEIGHT,
        ContactsFields::LINK_WEIGHT,
        'link_deceased',
        ['Name' => 'link_npsa_role', 'Condition' => $nrlsEnabled],
        'link_mhact_section',
        'link_mhcpa',
        'link_notes',
        'link_dear',
        'link_ref',
        'link_marriage',
        [
            'Name' => 'link_sedation',
            'Condition' => ($FormType === FormTable::MODE_DESIGN || ($Module === Module::INCIDENTS && $con['link_type'] === ContactTypes::PERSON_AFFECTED)) && $showNRLSFields,
        ],
        [
            'Name' => 'link_legalaid',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && in_array($con['link_type'], [ContactTypes::CLAIMANT, ContactTypes::PERSON_AFFECTED], true)),
        ],
        [
            'Name' => 'link_lip',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && in_array($con['link_type'], [ContactTypes::CLAIMANT, ContactTypes::PERSON_AFFECTED], true)),
        ],
        [
            'Name' => 'link_ndependents',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && in_array($con['link_type'], [ContactTypes::CLAIMANT, ContactTypes::PERSON_AFFECTED], true)),
        ],
        [
            'Name' => 'link_agedependents',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && in_array($con['link_type'], [ContactTypes::CLAIMANT, ContactTypes::PERSON_AFFECTED], true)),
        ],
        [
            'Name' => 'link_injuries',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && in_array($con['link_type'], [ContactTypes::CLAIMANT, ContactTypes::PERSON_AFFECTED], true)),
        ],
        [
            'Name' => 'link_resp',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && $con['link_type'] === ContactTypes::EMPLOYEE),
            'Table' => 'link_contacts',
        ],
        [
            'Name' => 'lcom_primary',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::FEEDBACK && $con['link_type'] === ContactTypes::CONSULTANT),
        ],
        [
            'Name' => 'link_plapat',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::CLAIMS && $con['link_type'] === ContactTypes::CLAIMANT),
        ],
        [
            'Name' => 'lcom_iscomplpat',
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::FEEDBACK && $con['link_type'] === ContactTypes::CONSULTANT),
        ],
        [
            'Name' => 'link_patrelation',
            'Condition' => $FormType === FormTable::MODE_DESIGN
                || ($Module === Module::FEEDBACK && $con['link_type'] === ContactTypes::CONSULTANT)
                || ($Module === Module::CLAIMS && $con['link_type'] === ContactTypes::CLAIMANT),
        ],
        ['Name' => 'show_injury', 'Condition' => $conditionForInjuryFields],
        [
            'Name' => 'show_illness',
            'Condition' => $showUsClaimsFields && (
                in_array($FormType, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true)
                || ($ShowLinkDetails && in_array($Module, [Module::INCIDENTS, Module::CLAIMS], true))
            ),
        ],
        'link_date_admission',
        [
            'Name' => 'link_notify_progress',
            'Condition' => in_array($FormType, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true) || ($con['link_role'] ?? null) === $reporterRole,
        ],
        [
            'Name' => 'time_employee_began_work',
            'Condition' => (
                in_array($FormType, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true)
                || ($ShowLinkDetails && in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true))
            ),
        ],
        ['Name' => 'include_in_tpoc', 'Condition' => $showMmseaFields && ($FormType === FormTable::MODE_DESIGN || $contactTypeIsClaimant)],
        [
            'Name' => 'full_pay_injury_day',
            'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
        ],
        [
            'Name' => 'salary_continued',
            'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
        ],
        [
            'Name' => 'link_position',
            'Condition' => $FormType === FormTable::MODE_DESIGN || (in_array($Module, [Module::INCIDENTS, Module::FEEDBACK], true) && $con['link_role'] === $reporterRole),
            'ReadOnly' => true,
        ],
        [
            'Name' => RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT,
            'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::REDRESS && $con['link_type'] === ContactTypes::PERSON_AFFECTED),
        ],
        [
            'Name' => ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::APC_AWARE_REPORT,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::APC_AAR,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::REFERRAL_SUBJECT_CHILD,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::INTERPRETER_USED,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::CHILD_WITNESS,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        [
            'Name' => ContactsFields::WITNESS_AWARE_REPORT,
            'Condition' => $FormType === FormTable::MODE_DESIGN || $Module === Module::SAFEGUARDING,
        ],
        ContactsFields::LINK_ORGANISATION,
    ],
];

$linkDetailsSectionForRespondentsContacts = [
    'Title' => _fdtk('link_details_respondent', $useFormDesignLanguage),
    'Condition' => ($ShowLinkDetails && in_array($con['link_type'], [ContactTypes::INDIVIDUAL_RESPONDENT, ContactTypes::RESPONDENT], true)) || $FormType === FormTable::MODE_DESIGN,
    'LinkFields' => true,
    'ContactsLinkTable' => 'link_respondents',
    'Rows' => [
        'link_resp',
        'link_role',
        'link_notes',
        'indemnity_reserve_assigned',
        'expenses_reserve_assigned',
        'fin_medical_reserve_assigned',
        'fin_legal_reserve_assigned',
        'fin_temporary_indemnity_reserve_assigned',
        'fin_permanent_indemnity_reserve_assigned',
        'remaining_indemnity_reserve_assigned',
        'remaining_expenses_reserve_assigned',
        'fin_remaining_medical_reserve_assigned',
        'fin_remaining_legal_reserve_assigned',
        'fin_remaining_temporary_indemnity_reserve_assigned',
        'fin_remaining_permanent_indemnity_reserve_assigned',
        'resp_total_paid',
    ],
];

if ($Module === Module::REDRESS) {
    $linkDetailsSectionForRespondentsContacts['Rows'] = ['link_role'];
}

if ($contactTypeIsClaimant || in_array($FormType, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true)) {
    $rowsForMmsea = [
        'claimant_medicare_beneficiary',
        'claimant_payments_non_medical',
        'date_medicare_confirmed',
        'claimant_medicare_claim_number',
        'date_mmsea_last_reported',
        'delete_if_field_with_medicate',
        'no_fault',
        'no_fault_insurance_limit',
        'no_fault_exhaust_date',
        'mmsea_relationship_to_beneficiary',
        'mmsea_type_of_representative',
        'msp_effective_date',
        'msp_termination_date',
        'msp_type',
        'disposition_code',
    ];
} else {
    $rowsForMmsea = [
        'mmsea_relationship_to_beneficiary',
        'mmsea_type_of_representative',
    ];
}

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
        'LinkType' => $con['link_type'],
    ],
    'link' => $linkDetailsSectionForNormalContacts,
    'link_respondents' => $linkDetailsSectionForRespondentsContacts,
    'osha' => [
        'Title' => 'OSHA',
        'Condition' => $showOshaFields && ($contactTypeIsClaimant || $FormType === FormTable::MODE_DESIGN),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'LinkFields' => true,
        'Rows' => ['osha_date_hired'],
    ],
    'mmsea' => [
        'Title' => 'MMSEA',
        'LinkFields' => true,
        'ContactsLinkTable' => 'link_contacts',
        'Condition' => $showMmseaFields,
        'Rows' => $rowsForMmsea,
    ],
    'edi' => [
        'Title' => 'EDI',
        'LinkFields' => true,
        'Condition' => $showEdiFields && ($contactTypeIsClaimant || $FormType === FormTable::MODE_DESIGN),
        'Rows' => [
            'edi_wage_period',
            'edi_employment_status',
            'edi_employee_id_type',
            'edi_work_loss_list',
            'edi_physical_restrictions',
            'edi_disability_type',
            'edi_diagnosis',
            'edi_agency_code',
            'edi_ncci_class',
            'edi_type_loss',
            'edi_reporting_period',
            'edi_date_disability_known_employer',
            'edi_accident_premises',
            'edi_return_to_work_type',
            'edi_return_to_work_qualif',
            'edi_drug_screen_summary',
            'employer_paid_salary_as_compensation',
            'date_maximum_medical_improvement',
            'date_claim_admin_knew_lost_time',
            'maintenance_type_code_date',
            'maintenance_type_correction_code_date',
            'employee_id_assigned_by_jurisdiction',
            'first_day_of_disability',
            'initial_rtw_same_employer',
            'verification_number',
        ],
    ],
    'tpoc' => [
        'Title' => 'TPOC',
        'LinkedDataSection' => true,
        'Condition' => $showMmseaFields && ($FormType === FormTable::MODE_DESIGN || ($contactTypeIsClaimant && !in_array($FormType, [FormTable::MODE_PRINT, FormTable::MODE_LINKED_DATA_SEARCH], true))),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ClearSectionOption' => false,
        'DeleteSectionOption' => false,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'ControllerAction' => [
            'tpocSection' => [
                'controller' => src\contacts\controllers\TpocController::class,
            ],
        ],
        'Rows' => [
            'tpoc_date',
            'tpoc_amount',
            'tpoc_date_delayed',
            'tpoc_date_filed',
            'tpoc_date_court_approval',
            'tpoc_date_payment_issued',
            'tpoc_deleted',
        ],
    ],
    'policies' => [
        'Title' => _fdtk('mod_policies_title', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NoReadOnly' => true,
        'ControllerAction' => [
            'listpoliciesforrespondent' => [
                'controller' => src\policies\controllers\PoliciesController::class,
            ],
        ],
        'Condition' => (!empty($insurancePermissions) && ($Module === Module::CLAIMS && !empty($con['link_recordid']))) || $FormType === FormTable::MODE_DESIGN,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH, FormTable::MODE_PRINT],
        'Listings' => ['policies' => ['module' => Module::INSURANCE]],
        'Rows' => [],
    ],
    'response' => [
        'Title' => _fdtk('response', $useFormDesignLanguage),
        'NewPanel' => true,
        'LinkFields' => true,
        'Condition' => (!empty($con['link_exists']) && $con['link_type'] === 'S' && $Module === 'SAB') || ($FormType === FormTable::MODE_DESIGN),
        'Rows' => [
            'link_read_date',
            'link_rsp_type',
            'link_rsp_date',
            'link_comments',
        ],
    ],
    'injury' => [
        'Title' => _fdtk('injury_details', $useFormDesignLanguage),
        'Condition' => $conditionForInjuryFields,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'ControllerAction' => [
            'showInjuryDetailsSection' => [
                'controller' => src\contacts\controllers\InjuryDetailsController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $con['link_type'], 'contact_suffix' => $con['contactSuffix'] ?? 0],
        'LinkFields' => true,
        'LinkType' => $con['link_type'],
        'Rows' => [
            [
                'Name' => 'dum_injury',
                'Title' => _fdtk('injury', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_bodypart',
                'Title' => _fdtk('body_part', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_death_result',
                'Title' => _fdtk('death_result_injury', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_impaired_percent',
                'Title' => _fdtk('permanent_impairment_percentage', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
            [
                'Name' => 'dum_treatment',
                'Title' => _fdtk('dum_treatment', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
            ],
        ],
    ],
    'lost_restricted_time' => [
        'Title' => _fdtk('lost_and_restricted_time', $useFormDesignLanguage),
        'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || ($ShowLinkDetails && in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true))),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'NotModes' => [FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'showLostAndRestrictedTimeSection' => [
                'controller' => src\contacts\controllers\LostAndRestrictedTimeController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $con['link_type'], 'contact_suffix' => $con['contactSuffix'] ?? 0],
        'LinkFields' => true,
        'LinkType' => $con['link_type'],
        'Rows' => [
            [
                'Name' => 'absence_start',
                'Title' => 'Lost time start date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'absence_end',
                'Title' => 'Lost time return date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'absence_total',
                'Title' => 'Number of days away',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => true,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'total_lost_time',
                'Title' => 'Total days away from work',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'show_restricted_time',
                'Title' => 'Was employee on restricted time?',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
            ],
            [
                'Name' => 'restriction_start',
                'Title' => 'Restricted time start date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'restriction_end',
                'Title' => 'Restricted time end date',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
            [
                'Name' => 'restriction_total',
                'Title' => 'Number of days restricted',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => true,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
            [
                'Name' => 'total_restricted_time',
                'Title' => 'Total days restricted time',
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoSearch' => false,
                'NoListCol' => true,
                'ReadOnly' => true,
                'Computed' => true,
            ],
        ],
    ],
    'illness' => [
        'Title' => _fdtk('illness_details', $useFormDesignLanguage),
        'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || (in_array($Module, [Module::INCIDENTS, Module::CLAIMS], true) && ($ShowLinkDetails || $FormType === FormTable::MODE_SEARCH))),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'LinkFields' => true,
        'LinkType' => $con['link_type'],
        'Rows' => [
            'link_illness',
        ],
    ],
    'icd' => [
        'Title' => 'ICD',
        'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'LinkFields' => true,
        'ContactsLinkTable' => 'link_contacts',
        'Rows' => [
            'icd_classification',
            'icd_diagnosis_codes',
            'icd_procedure_codes',
        ],
    ],
    'link_sirs' => [
        'Title' => _fdtk('sirs_link_details', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN || ($ShowLinkDetails && $Module === Module::INCIDENTS)) || ($FormType === FormTable::MODE_SEARCH && $Module === Module::INCIDENTS),
        'LinkFields' => true,
        'Rows' => [
            'link_worked_alone',
            'link_become_unconscious',
            'link_req_resuscitation',
            'link_hospital_24hours',
            'link_clin_factors',
            'link_direct_indirect',
            'link_injury_caused',
            'link_attempted_assault',
            'link_discomfort_caused',
            'link_public_disorder',
            'link_verbal_abuse',
            'link_harassment',
            'link_police_pursue',
            'link_police_persue_reason',
            'link_pprop_damaged',
        ],
    ],
    'property_section' . (!empty($Suffix) ? '_' . $Suffix : '') => [
        'Title' => _fdtk('personal_property', $useFormDesignLanguage),
        'ContactSuffix' => $Suffix ?? '',
        'Condition' => ($FormType === FormTable::MODE_DESIGN || ($ShowLinkDetails && $Module === Module::INCIDENTS)) && $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'LinkType' => $con['link_type'],
        'LinkFields' => true,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ExtraParameters' => ['link_type' => $con['link_type']],
        'Rows' => [
            ['Name' => 'dum_description', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_description', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
            ['Name' => 'dum_property_type', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_damage_type', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
            ['Name' => 'dum_value', 'Title' => Labels_FormLabel::GetFormFieldLabel('ipp_value', '', '', '', '', $useFormDesignLanguage), 'NoOrder' => true, 'NoExtraText' => true, 'NoHelpText' => true],
        ],
        'ControllerAction' => [
            'showPropertyDetailsSection' => [
                'controller' => src\contacts\controllers\PropertyDetailsController::class,
            ],
        ],
    ],
    'pay_rates' => [
        'Title' => _fdtk('pay_rates', $useFormDesignLanguage),
        'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || ($ShowLinkDetails && $contactTypeIsClaimant)),
        'LinkType' => $Type ?? null,
        'LinkFields' => true,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'Rows' => [
            'hours_worked',
            'hourly_rate',
            'weekly_rate',
            'monthly_rate',
        ],
    ],
    'compl_dates' => [
        'Title' => _fdtk('person_providing_feedback_chain', $useFormDesignLanguage),
        'Condition' => $FormType === FormTable::MODE_DESIGN || ($Module === Module::FEEDBACK && $con['link_type'] === ContactTypes::CONSULTANT),
        'LinkFields' => true,
        'ControllerAction' => [
            'complaintDates' => [
                'controller' => src\complaints\controllers\ComplaintController::class,
            ],
        ],
        'ExtraParameters' => ['fieldset' => 19],
    ],
    'compl_dates_history' => [
        'Title' => _fdtk('person_providing_feedback_chain_history', $useFormDesignLanguage),
        'Condition' => ($FormType === FormTable::MODE_DESIGN || ($Module === Module::FEEDBACK && $con['link_type'] === ContactTypes::CONSULTANT && ShowComplaintDatesHistory($con['com_id'], $con['recordid']))),
        'NotModes' => [FormTable::MODE_SEARCH, FormTable::MODE_PRINT],
        'LinkFields' => true,
        'ControllerAction' => [
            'complaintDatesHistory' => [
                'controller' => src\complaints\controllers\ComplaintController::class,
            ],
        ],
    ],
    'contact' => [
        'Title' => _fdtk('contact_details', $useFormDesignLanguage),
        'Rows' => [
            [
                'Name' => 'recordid',
                'Title' => 'Contact ID',
                'Type' => 'string',
                'ReadOnly' => !in_array($form_action ?? null, [FormTable::MODE_DESIGN, FormTable::MODE_SEARCH], true),
            ],
            RepApprovedDisplayBasicFormArray([
                'formtype' => $FormType,
                'module' => Module::CONTACTS,
                'perms' => $CONPerms ?? null,
                'currentapproveobj' => $CurrentApproveObj ?? null,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            RepApprovedBasicFormArray([
                'formtype' => $FormType,
                'data' => $inc ?? [],
                'module' => Module::CONTACTS,
                'perms' => $CONPerms ?? null,
                'approveobj' => $ApproveObj ?? null,
                'useFormDesignLanguage' => $useFormDesignLanguage,
            ]),
            'con_title',
            'con_forenames',
            [
                'Name' => 'con_middle_name',
                'Condition' => $showUsGeneralFields,
            ],
            'con_surname',
            [
                'Name' => 'con_email',
                'ReadOnly' => !in_array($FormType, [FormTable::MODE_SEARCH, FormTable::MODE_DESIGN], true) && !empty($con['initials']),
            ],
            [
                'Name' => 'con_line1',
            ],
            [
                'Name' => 'con_line2',
            ],
            [
                'Name' => 'con_line3',
            ],
            [
                'Name' => 'con_city',
            ],
            [
                'Name' => 'con_state',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'con_county',
            ],
            [
                'Name' => 'con_country',
            ],
            'con_postcode',
            'con_gender',
            [
                'Name' => 'con_dob',
                'ShowHijri' => $showSpscFields
                    && !in_array($Module, [Module::REDRESS, Module::SAFEGUARDING], true)
                    && !in_array($FormType, [FormTable::MODE_SEARCH, FormTable::MODE_LINKED_DATA_SEARCH], true),
                'onChangeExtra' => 'calculateHijri(' . ($Suffix ?? '') . ')',
            ],
            'con_dod',
            [
                'Name' => 'con_tel1',
                'Type' => 'string',
                'Title' => 'Telephone no. 1',
                'Width' => 30,
                'MaxLength' => 254,
            ],
            [
                'Name' => 'con_tel2',
                'Type' => 'string',
                'Title' => 'Telephone no. 2',
                'Width' => 30,
                'MaxLength' => 254,
            ],
            [
                'Name' => 'con_number',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            [
                'Name' => 'con_nhsno',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            [
                'Name' => 'con_social_security_number',
                'Condition' => $showUsGeneralFields,
            ],
            [
                'Name' => 'con_police_number',
                'Condition' => !$showMultipleIdNumbersSection,
            ],
            ContactsFields::NATIONALITY,
            ContactsFields::JOB_TITLE,
            'con_ethnicity',
            'con_language',
            'con_disability',
            'con_religion',
            'con_sex_orientation',
            'con_work_alone_assessed',
            'con_type',
            'con_subtype',
            'con_notes',
            [
                'Name' => 'tax_id',
                'Condition' => $showUsClaimsFields,
            ],
            [
                'Name' => 'employee_state_hired',
                'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'employment_termination_date',
                'Condition' => $showUsClaimsFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_employment_status_code',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_process_level',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_job_code',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_supervisor_name',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_department',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_location_code',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_fte',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            [
                'Name' => 'con_lawson_number',
                'Condition' => $showUsGeneralFields && ($FormType === FormTable::MODE_DESIGN || in_array($Module, [Module::CLAIMS, Module::INCIDENTS], true)),
            ],
            ['Name' => PSIMSLinkContactsFields::PSIMS_AGE_YEARS, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_AGE_YEARS]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_GENDER, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_GENDER]],
            ['Name' => PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY, 'Condition' => $showPsimsFields[PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY]],
            ['Name' => ContactsFields::SOURCE_OF_RECORD, 'ReadOnly' => true],
        ],
    ],
    'contact_numbers' => [
        'Title' => _fdtk('contact_numbers', $useFormDesignLanguage),
        'Condition' => $showMultipleIdNumbersSection,
        'NoFieldAdditions' => true,
        'NoFieldRemoval' => true,
        'ContactSubForm' => true,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'ControllerAction' => [
            'showContactNumbersSection' => [
                'controller' => src\contacts\controllers\ContactIdNumbersController::class,
            ],
        ],
        'ExtraParameters' => ['link_type' => $con['link_type'], 'contact_suffix' => $con['contactSuffix'] ?? 0],
        'LinkFields' => true,
        'LinkType' => $con['link_type'],
        'Rows' => [
            [
                'Name' => 'con_number_type',
                'Title' => _fdtk('con_number_type', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
                'NoRadio' => true,
            ],
            [
                'Name' => 'con_id_number',
                'Title' => _fdtk('con_id_number', $useFormDesignLanguage),
                'NoOrder' => true,
                'NoMandatory' => true,
                'NoExtraText' => true,
                'NoHelpText' => true,
                'NoHide' => true,
                'NoListCol' => true,
                'NoSearch' => true,
            ],
        ],
    ],
    'employee' => [
        'Title' => _fdtk('employee_details', $useFormDesignLanguage),
        'ReadOnly' => false,
        'Rows' => [
            'location_id',
            'service_id',
            [
                'Name' => 'con_empl_grade',
                'Condition' => $showNRLSFields,
            ],
        ],
    ],
    'additional_info' => [
        'Title' => _fdtk('additional_information', $useFormDesignLanguage),
        'Rows' => [
            'show_document',
        ],
        'Condition' => !in_array($FormType, [FormTable::MODE_SEARCH, FormTable::MODE_LINKED_DATA_SEARCH], true),
    ],
    'documents' => [
        'Title' => _fdtk('documents', $useFormDesignLanguage),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'ControllerAction' => [
            'listlinkeddocuments' => [
                'controller' => src\documents\controllers\DocumentController::class,
            ],
        ],
        'Rows' => [],
    ],
    'word' => [
        'Title' => _fdtk('mod_templates_title'),
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'wordmergesection' => [
                'controller' => src\wordmergetemplate\controllers\WordMergeTemplateController::class,
            ],
        ],
        'Condition' => !$ShowLinkDetails || $FormType === FormTable::MODE_DESIGN,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH, FormTable::MODE_PRINT],
        'Rows' => [],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
    'claims' => [
        'Title' => $ModuleDefs['CLA']['NAME'] . (!empty($con['linkedclanum']) ? ' (' . $con['linkedclanum'] . ')' : ''),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Listings' => [
            'CLA' => [
                'module' => 'CLA',
                'Label' => 'Select a listing to use for the list of ' . $ModuleDefs['CLA']['REC_NAME_PLURAL'] ?? '',
            ],
        ],
        'ControllerAction' => [
            'listLinkedRecords' => [
                'controller' => src\contacts\controllers\ContactsController::class,
            ],
        ],
        'ExtraParameters' => ['link_module' => Module::CLAIMS],
        'Condition' => $_SESSION['licensedModules'][MOD_CLAIMS] && $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'Rows' => [],
    ],
];

// If we're in a search view, then we use SearchCriteriaController to generate the search fields for the incidents fields
if ($FormType === FormTable::MODE_SEARCH) {
    $FormArray['incidents'] = [
        'Title' => _fdtk('mod_incidents_title', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => [
            'link_type' => Module::INCIDENTS,
            'sectionId' => 'incidents',
            'linkModule' => Module::INCIDENTS,
            'module' => Module::CONTACTS,
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
        'Condition' => $_SESSION['licensedModules'][MOD_INCIDENTS],
        'Rows' => [],
    ];
} else {
    $FormArray['incidents'] = [
        'Title' => $ModuleDefs['INC']['NAME'] . (!empty($con['linkedincnum']) ? ' (' . $con['linkedincnum'] . ')' : ''),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Listings' => [
            'INC' => [
                'module' => 'INC',
                'Label' => 'Select a listing to use for the list of ' . $ModuleDefs['INC']['REC_NAME_PLURAL'] ?? '',
            ],
        ],
        'ControllerAction' => [
            'listLinkedRecords' => [
                'controller' => src\contacts\controllers\ContactsController::class,
            ],
        ],
        'ExtraParameters' => ['link_module' => Module::INCIDENTS],
        'Condition' => $_SESSION['licensedModules'][MOD_INCIDENTS] && $FormType !== FormTable::MODE_LINKED_DATA_SEARCH,
        'Rows' => [],
    ];
}

// If we're in a seach view, then we use SearchCriteriaController to generate the search fields for the complaints fields
if ($FormType === FormTable::MODE_SEARCH) {
    $FormArray['complaints'] = [
        'Title' => _fdtk('COMNamesTitle', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => [
            'link_type' => Module::FEEDBACK,
            'sectionId' => 'complaints',
            'linkModule' => Module::FEEDBACK,
            'module' => Module::CONTACTS,
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
        'Condition' => $_SESSION['licensedModules'][MOD_COMPLAINTS],
        'Rows' => [],
    ];
} else {
    $FormArray['complaints'] = [
        'Title' => $ModuleDefs['COM']['NAME'] . (!empty($con['linkedcomnum']) ? ' (' . $con['linkedcomnum'] . ')' : ''),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Listings' => [
            'COM' => [
                'module' => 'COM',
                'Label' => 'Select a listing to use for the list of ' . $ModuleDefs['COM']['REC_NAME_PLURAL'] ?? '',
            ],
        ],
        'ControllerAction' => [
            'listLinkedRecords' => [
                'controller' => src\contacts\controllers\ContactsController::class,
            ],
        ],
        'ExtraParameters' => ['link_module' => Module::FEEDBACK],
        'Condition' => $_SESSION['licensedModules'][MOD_COMPLAINTS] && $FormType != 'linkedDataSearch',
        'Rows' => [],
    ];
}

if ($FormType === FormTable::MODE_SEARCH) {
    $FormArray['mortalities'] = [
        'Title' => _fdtk('MORNamesTitle', $useFormDesignLanguage),
        'LinkedDataSection' => true,
        'NoFieldAdditions' => true,
        'ControllerAction' => [
            'generateSearchCriteria' => [
                'controller' => src\contacts\controllers\SearchCriteriaController::class,
            ],
        ],
        'ExtraParameters' => [
            'link_type' => Module::MORTALITY_REVIEW,
            'sectionId' => 'mortalities',
            'linkModule' => Module::MORTALITY_REVIEW,
            'module' => Module::CONTACTS,
        ],
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_DESIGN, FormTable::MODE_PRINT, FormTable::MODE_EDIT],
        'Condition' => $_SESSION['licensedModules'][MOD_MORTALITY],
        'Rows' => [],
    ];
} else {
    $FormArray['mortalities'] = [
        'Title' => $ModuleDefs['MOR']['NAME'] . (!empty($con['linkedcomnum']) ? ' (' . $con['linkedcomnum'] . ')' : ''),
        'NoFieldAdditions' => true,
        'NotModes' => [FormTable::MODE_NEW, FormTable::MODE_SEARCH],
        'Listings' => [
            'MOR' => [
                'module' => 'MOR',
                'Label' => 'Select a listing to use for the list of ' . $ModuleDefs['MOR']['REC_NAME_PLURAL'] ?? '',
            ],
        ],
        'ControllerAction' => [
            'listLinkedRecords' => [
                'controller' => src\contacts\controllers\ContactsController::class,
            ],
        ],
        'ExtraParameters' => ['link_module' => Module::MORTALITY_REVIEW],
        'Condition' => $_SESSION['licensedModules'][MOD_MORTALITY] && $FormType != 'linkedDataSearch',
        'Rows' => [],
    ];
}

$FormArray = PsimsFieldPropertyManager::preventPsimsContactFieldConfiguration($FormArray);

return $FormArray;
