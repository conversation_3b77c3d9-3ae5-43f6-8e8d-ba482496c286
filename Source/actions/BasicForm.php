<?php

$useFormDesignLanguage = $FormType == 'Design';

$FormArray = [
    'Parameters' => [
        'Panels' => true,
        'Condition' => false,
        'LinkType' => ($FormType == 'linkedDataSearch') ? 'linked_actions' : '',
    ],
    'action' => [
        'Title' => _fdtk('action_details', $useFormDesignLanguage),
        'NewPanel' => true,
        'Rows' => [
            [
                'Title' => 'Action ID',
                'Name' => 'recordid',
                'Type' => 'formfield',
                'FormField' => $idFieldObj,
                'Condition' => $FormType !== 'linkedDataSearch',
            ],
            [
                'Title' => 'Module',
                'Name' => 'act_module',
                'Type' => 'formfield',
                'FormField' => $moduleFieldObj,
                'Condition' => $FormType !== 'linkedDataSearch',
            ],
            [
                'Type' => 'string',
                'Name' => 'ach_title',
                'Title' => 'Action chain title',
                'Condition' => $FormType != 'Search' && $act['ach_title'],
                'ReadOnly' => true,
            ],
            [
                'Type' => 'string',
                'Name' => 'act_step_no',
                'Title' => 'Step number',
                'Condition' => $FormType != 'Search' && $act['act_step_no'],
                'ReadOnly' => true,
            ],
            [
                'Name' => 'act_cas_id',
                'Condition' => $FormType !== 'linkedDataSearch',
            ],
            'act_descr',
            'type',
            'act_synopsis',
            'act_priority',
            'act_dstart',
            'act_ddue',
            'status',
            'location_id',
            'service_id',
            'act_to_inits',
            'act_from_inits',
        ],
    ],
    'progress_notes' => [
        'Title' => _fdtk('table_progress_notes', $useFormDesignLanguage),
        'NewPanel' => true,
        'NotModes' => ['New', 'Search'],
        'Special' => 'ProgressNotes',
        'NoFieldAdditions' => true,
        'Rows' => [],
    ],
];

return $FormArray;
