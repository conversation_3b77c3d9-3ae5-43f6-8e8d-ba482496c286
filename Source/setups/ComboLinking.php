<?php

/**
 * @return never
 */
function IsComboLinkLocked(): void
{
    if ($_GET['recordid']) {
        $recordId = Sanitize::SanitizeInt($_GET['recordid']);

        if (IsCentrallyAdminSys()) {
            if (IsCentralAdmin()) {
                echo 'N';
            } else {
                $sql = '
                    SELECT
                        cmb_locked
                    FROM
                        combo_links
                    WHERE
                        recordid = :recordid
                ';

                $result = DatixDBQuery::PDO_fetch($sql, ['recordid' => $recordId], PDO::FETCH_COLUMN);

                switch ($result) {
                    case 'Y':
                    case 'N':
                        echo $result;

                        break;
                    default:
                        echo 'N';

                        break;
                }
            }
        } else {
            echo 'N';
        }
    } else {
        echo 'N';
    }

    obExit();
}
