<?php

namespace Source\setups;

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageService;

/**
 * Will return any coded fields for a given module that don't appear in the field format or field directory tables (because they are not fields)
 * The returned coded-fields will follow the same format as the return values from the SQL query in CodeSetupList function
 * This function is not ideal and should be factored-out, but it is easier to understand than some twisted SQL to get codes like causal groups out of the DB.
 *
 * @param string $module A module code
 *
 * @return array An array of coded fields. Each field will contain the following keys:
 *               - fdr_code_table
 *               - frd_label
 *               - fdr_name
 *               - fdr_table
 */
// TODO We will need to remove all of these once FFormats has been removed and add them all via field directory
function getHardCodedFields(string $module): array
{
    $registry = Container::get(Registry::class);
    $scriptUrl = $registry->getScriptUrl();

    $codes = [
        Module::INCIDENTS => [
            [
                'fdr_code_table' => 'ratings_map',
                'fdr_label' => _fdtk('risk_grading'),
                'url' => $scriptUrl . '?action=riskmatrixsetup',
            ],
            [
                'fdr_code_table' => 'sac_map',
                'fdr_label' => _fdtk('sac_score'),
                'url' => $scriptUrl . '?action=sacscoresetup',
            ],
            [
                'fdr_code_table' => 'code_inc_treatment',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'link_treatment',
                'fdr_table' => 'vw_inc_injuries',
            ],
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Document Type',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
        Module::FEEDBACK => [
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Document Type',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
        Module::MORTALITY_REVIEW => [
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Document Type',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
        Module::CONTACTS => [
            [
                'fdr_code_table' => 'code_inc_injury',
                'fdr_label' => 'Injury',
                'fdr_name' => 'inc_injury',
                'fdr_table' => 'inc_injuries',
            ],
            [
                'fdr_code_table' => 'code_inc_bodypart',
                'fdr_label' => 'Body Part',
                'fdr_name' => 'inc_bodypart',
                'fdr_table' => 'inc_injuries',
            ],
            [
                'fdr_code_table' => 'code_inc_treatment',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'link_treatment',
                'fdr_table' => 'vw_inc_injuries',
            ],
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
            [
                'fdr_code_table' => '!MHSECT',
                'fdr_label' => 'Section (MHA)',
                'fdr_name' => 'link_mhact_section',
                'fdr_table' => 'link_contacts',
            ],
        ],
        Module::EQUIPMENT => [
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
        Module::INSURANCE => [
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
        Module::CLAIMS => [
            [
                'fdr_code_table' => 'code_cla_injury',
                'fdr_label' => 'Injuries',
                'fdr_name' => 'link_injuries',
                'fdr_table' => 'link_contacts',
            ],
            [
                'fdr_code_table' => 'code_doc_type',
                'fdr_label' => 'Treatment',
                'fdr_name' => 'doc_type',
                'fdr_table' => 'link_documents',
            ],
        ],
    ];

    /*
     * Only add this code setup if this configuration option is set to yes, otherwise we will get a
     * 404 error when trying to access it.
     */
    if ($registry->getParm('CLA_SETUP', 'N')->isTrue()) {
        $codes[Module::CLAIMS][] = [
            'fdr_code_table' => 'claims_insurer_excess',
            'fdr_label' => _fdtk('claims_insurer_excess'),
            'url' => $scriptUrl . '?action=excesssetup',
        ];
    }

    $languageService = Container::get(LanguageService::class);

    $fields = $codes[$module] ?? [];
    foreach ($fields as $index => $row) {
        if ($row['fdr_table'] && $row['fdr_name']) {
            $domain = $languageService->getModuleDomain($module);
            $fields[$index]['displayName'] = $registry->getFieldLabels()->getLabel($row['fdr_table'], $row['fdr_name'], $domain)->getFieldLabel() ?? $row['fdr_label'];
        } else {
            $fields[$index]['displayName'] = $row['fdr_label'];
        }
    }

    return $fields;
}
