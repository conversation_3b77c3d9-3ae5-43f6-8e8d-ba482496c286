<?php

function generateSacScore(): void
{
    $types = DatixDBQuery::PDO_fetch_all('SELECT code FROM code_inc_type', [], PDO::FETCH_COLUMN);
    $results = DatixDBQuery::PDO_fetch_all('SELECT code FROM code_inc_result', [], PDO::FETCH_COLUMN);
    $severities = DatixDBQuery::PDO_fetch_all('SELECT code FROM code_inc_severity', [], PDO::FETCH_COLUMN);

    // Remove entries for deleted types
    DatixDBQuery::PDO_query('DELETE FROM sac_map WHERE [type] NOT IN (SELECT code FROM code_inc_type)');

    $codes = DatixDBQuery::PDO_fetch_all("select concat([type], '-', [result], '-', severity) from sac_map", [], PDO::FETCH_COLUMN);

    $rows = [];
    foreach ($types as $type) {
        foreach ($results as $result) {
            foreach ($severities as $severity) {
                if (!in_array("{$type}-{$result}-{$severity}", $codes, true)) {
                    // insert combination if it doesn't already exist
                    $rows[] = "('{$type}', '{$result}', '{$severity}', 'N', 'N')";
                }
            }
        }
    }

    // Maximum number of rows that can be inserted at once is 1000
    foreach (array_chunk($rows, 1000) as $chunk) {
        DatixDbQUery::PDO_query('INSERT INTO sac_map ([type], [result], severity, flag_investigation, flag_rib) VALUES ' . rtrim(implode(',', $chunk), ','));
    }
}
