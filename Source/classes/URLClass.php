<?php

class URL
{
    protected $File;
    protected $QueryStringArray;
    protected $Root;

    public function __construct()
    {
        $ScriptNameExplode = explode('/', $_SERVER['SCRIPT_NAME']);
        $this->File = array_pop($ScriptNameExplode);

        $this->Root = $this->fullPath();

        $QueryString = explode('&', Sanitize::SanitizeString($_SERVER['QUERY_STRING']));

        foreach ($QueryString as $KeyVal) {
            $KeyValPair = explode('=', $KeyVal);
            $this->QueryStringArray[$KeyValPair[0]] = $KeyValPair[1];
        }
    }

    public function AddToQueryString($key, $val)
    {
        $this->QueryStringArray[$key] = $val;
    }

    public function GetURL()
    {
        return $this->Root . '/' . urlencode($this->File) . '?' . $this->implodeQueryString();
    }

    protected function implodeQueryString()
    {
        foreach ($this->QueryStringArray as $key => $val) {
            $QString[] = $key . '=' . $val;
        }

        return implode('&', $QString);
    }

    private function fullPath(): string
    {
        $s = $_SERVER;
        $ssl = (!empty($s['HTTPS']) && (string) $s['HTTPS'] === 'on');
        $sp = strtolower($s['SERVER_PROTOCOL']);
        $protocol = substr($sp, 0, strpos($sp, '/')) . (($ssl) ? 's' : '');

        $port = $s['SERVER_PORT'];
        $port = ((!$ssl && (string) $port === '80') || ($ssl && (string) $port === '443')) ? '' : ':' . $port;

        $host = $s['HTTP_X_FORWARDED_HOST'] ?? $s['HTTP_HOST'] ?? null;

        if (empty($host)) {
            $host = $s['SERVER_NAME'] . $port;
        }

        if (isset($s['HTTP_X_FORWARDED_PREFIX'])) {
            $host .= $s['HTTP_X_FORWARDED_PREFIX'];
        }

        $uri = $protocol . '://' . $host . $s['REQUEST_URI'];
        $segments = explode('?', $uri, 2);
        $url = $segments[0];

        return trim($url, '/');
    }
}
