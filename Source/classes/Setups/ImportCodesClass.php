<?php

use app\models\framework\config\DatixConfig;
use app\models\generic\Tables;
use Doctrine\ORM\EntityManagerInterface;
use src\datixtable\model\listeners\CodeLocationListener;
use src\datixtable\model\listeners\CodeServiceListener;
use src\datixtable\model\listeners\ListenersTrait;
use src\system\container\facade\Container;
use src\system\language\LanguageSession;

/**
 * Class used for importing code setups data in DATIX using XML data via files.
 * Similar to the importing option found in the Rich client.
 */
class Setups_ImportCodes
{
    use ListenersTrait;
    public const METHOD_APPEND = 'APPEND';
    public const METHOD_UPDATE = 'UPDATE';
    public const METHOD_OVERWRITE = 'OVERWRITE';
    public $XMLDoc; // XML document to be loaded
    public $CodeTableName;     // Actual Code table name
    public $CodeDescriptionTableName;
    public $CodeFieldName;     // Code field in code table e.g. cod_code or code.
    public $CodeWhere;         // Where clause for code if using code table such as code_types.
    public $CodeDescription;

    /** @var string */
    public $ImportMethod;      // Sets method of import: APPEND|UPDATE|OVERWRITE

    /** @var string[] */
    public $ErrorMsgs = [];   // Array used to store error messages whilst importing.

    /** @var LanguageSession */
    private $languageSession;
    private array $editableForTreeFields;
    private array $listenerQueue = [];

    public function __construct($CodeTableName, $CodeFieldName, $CodeWhere, $CodeDescription, $ImportMethod)
    {
        $this->CodeTableName = $CodeTableName;
        $this->CodeDescriptionTableName = $CodeTableName . '_descr';
        $this->CodeFieldName = $CodeFieldName;
        $this->CodeWhere = $CodeWhere;
        $this->CodeDescription = $CodeDescription;
        $this->ImportMethod = $ImportMethod;

        $this->editableForTreeFields = [
            'fields' => [Tables::LOCATION, Tables::SERVICE],
            'columns' => ['cod_npsa_', 'cod_ko41_', 'cod_hero'],
            'listeners_columns' => ['psims_'],
        ];

        $this->languageSession = Container::get(LanguageSession::class);

        switch ($this->CodeTableName) {
            case Tables::LOCATION:
                $this->addListener(
                    Container::get(CodeLocationListener::class),
                );

                break;
            case Tables::SERVICE:
                $this->addListener(
                    Container::get(CodeServiceListener::class),
                );

                break;
        }
    }

    /**
     * Loads an XML document from a string.
     *
     * Takes a string containing an XML structure and loads this into a DOMDocument object
     */
    public function LoadXMLDoc($XMLData)
    {
        $this->XMLDoc = new DOMDocument();
        if (!empty($XMLData) && preg_match('/\<CODES[^\>]*>/iu', $XMLData)) {// stripos($XMLData, "<CODES>") !== false)
            libxml_use_internal_errors(true);
            if ($this->XMLDoc->loadXML(UnicodeString::trim($XMLData))) {
                return true;
            }
        }

        // todo: IQ-18605 replace with placeholder
        $this->ErrorMsgs[] = 'Invalid data file';

        return false;
    }

    /**
     * Function used to import data into main Datix tables.
     *
     * Takes the XML object and imports data according to structure/elements/attributes of the XML object.
     *
     * @param string $udf int The id of the udf that we are looking to insert into
     *
     * @return bool
     */
    public function importCodesXMLdata($udf = '')
    {
        $xml = simplexml_import_dom($this->XMLDoc);

        foreach ($xml as $table) {
            $code_table = UnicodeString::strtolower($table->getName());

            if ($code_table !== UnicodeString::strtolower($this->CodeTableName)
                && $code_table !== UnicodeString::strtolower($this->CodeDescriptionTableName)
            ) {
                // todo: IQ-18605 replace with placeholder
                $this->ErrorMsgs[] = 'No valid data for ' . $this->CodeTableName . ' table found in XML file';

                return false;
            }

            if ($this->ImportMethod === self::METHOD_OVERWRITE && $code_table === UnicodeString::strtolower($this->CodeTableName)) {
                $this->deletecodes();
            }

            $this->listenerQueue = [];
            foreach ($table->CODE_TYPE as $record) {
                if ($udf !== '') {
                    $record->FIELD_ID = $udf;
                }

                switch ($code_table) {
                    case Tables::LOCATION:
                    case Tables::SERVICE:
                        if (!$this->savecoderecord($record, $code_table)) {
                            $this->ErrorMsgs[] = 'Unable to save table record for ' . $code_table;

                            return false;
                        }

                        break;
                    default:
                        if (!$this->savecoderecord($record, strtoupper($code_table))) {
                            // todo: IQ-18605 replace with placeholder
                            $this->ErrorMsgs[] = 'Unable to save table record for ' . $this->CodeTableName;

                            return false;
                        }

                        break;
                }
            }

            if (Container::get(DatixConfig::class)->getPsimsEnabled()) {
                $connection = Container::get(EntityManagerInterface::class)->getConnection();
                $connection->beginTransaction();

                try {
                    $this->notifyListeners(strtolower($this->ImportMethod), $this->listenerQueue);

                    $connection->commit();
                } catch (Exception $e) {
                    $connection->rollBack();

                    throw $e;
                }
            }

            $this->listenerQueue = [];
        }

        return true;
    }

    /**
     * Function to delete codes before import when import method set to OVERWRITE.
     */
    private function deletecodes()
    {
        // Safety net for code_types/udf_codes if no WHERE clause has been set.
        if (in_array($this->CodeTableName, ['code_types', 'udf_codes']) && empty($this->CodeWhere)) {
            return false;
        }

        $sql = "DELETE FROM {$this->CodeDescriptionTableName}";
        if (!empty($this->CodeWhere)) {
            $sql .= ' WHERE ' . $this->CodeWhere;
        }

        DatixDBQuery::PDO_fetch($sql);

        $sql = "DELETE FROM {$this->CodeTableName}";
        if (!empty($this->CodeWhere)) {
            $sql .= ' WHERE ' . $this->CodeWhere;
        }

        return DatixDBQuery::PDO_fetch($sql);
    }

    /**
     * Function to perform the insertion/update of code records depending on import method chosen,.
     *
     * @param simpleXMLElement $record simpleXMLElement containing the data for a single code record
     */
    private function savecoderecord($record, $table): bool
    {
        $columns = [];
        $values = [];
        $isDescriptionTable = strtoupper($this->CodeDescriptionTableName) === strtoupper($table);

        $descriptionTableData = [];

        /**
         * @todo: IQ-18604 remove description from being inserted into code table as it's not i18n complaint
         *
         * @var string $column
         * @var SimpleXMLElement $value
         */
        foreach ($record as $column => $value) {
            $columns[] = $column;

            $values[$column] = ($column === 'COD_COST' && (string) $value === '') ? null : (string) $value;

            if ($column === UnicodeString::strtoupper($this->CodeFieldName)) {
                $code_field_value = (string) $value;
            }

            if (
                (!$isDescriptionTable || $this->ImportMethod === self::METHOD_OVERWRITE)
                && in_array($column, ['CODE', 'DESCRIPTION', 'LANGUAGE'], true)
            ) {
                $descriptionTableData[$column] = (string) $value;
            }
        }

        // language hasn't been provided ie it's importing from codes default language
        if (!empty($descriptionTableData) && !isset($descriptionTableData['LANGUAGE'])) {
            $descriptionTableData['LANGUAGE'] = $this->languageSession->getLanguage();
        }

        $hasCodeField = $this->CodeDescription !== $this->CodeFieldName;
        $icdCodeTable = $this->CodeTableName === 'code_icd_codes';

        $standardCodeGreaterThanSixCharacters = $hasCodeField && !$icdCodeTable && UnicodeString::strlen($code_field_value) > 6;
        $icdCodeGreaterThanEightCharacters = $icdCodeTable && UnicodeString::strlen($code_field_value) > 8;

        // Code field value should not be more than 6 characters long
        // (Unless for codes without a code field, or ICD codes which are up to 8 characters)
        if ($standardCodeGreaterThanSixCharacters || $icdCodeGreaterThanEightCharacters) {
            return false;
        }

        $sql = "SELECT count(*) as checkcode FROM {$table} WHERE {$this->CodeFieldName} = '{$code_field_value}'";
        if ($this->CodeTableName === 'code_types') {
            $sql .= " AND cod_type = '" . $values['COD_TYPE'] . "'";
        }

        if ($this->CodeTableName === 'udf_codes') {
            $sql .= ' AND field_id = ' . (int) $values['FIELD_ID'];
        }

        if ($this->CodeTableName === 'udf_codes' && $this->CodeWhere !== '') {
            $sql .= ' AND ' . $this->CodeWhere;
        }

        if (UnicodeString::strtolower($table) === $this->CodeDescriptionTableName) {
            $sql .= ' AND language = ' . $values['LANGUAGE'];
        }

        $checkrow = (int) DatixDBQuery::PDO_fetch($sql, [], PDO::FETCH_COLUMN);

        // Do not allow inserts for tree fields through Prince
        if ($checkrow === 0 && !in_array($this->CodeTableName, $this->editableForTreeFields['fields'], true)) {
            // insert a new record
            $sql = 'INSERT INTO ' . $table . ' ';
            $sql .= GeneratePDOInsertSQLFromArrays(
                [
                    'FieldArray' => $columns,
                    'DataArray' => $values,
                ],
                $PDOParamsArray,
            );
            DatixDBQuery::PDO_query($sql, $PDOParamsArray);
        } else {
            if ($this->ImportMethod === self::METHOD_UPDATE) {
                $listenerValues = [];

                // Only external report mapping can be modified for tree fields via Prince
                // Drop data for any other columns user may try to modify
                if (in_array($this->CodeTableName, $this->editableForTreeFields['fields'])) {
                    $editableColumns = [];
                    $editableValues = [];
                    foreach ($columns as $column) {
                        foreach ($this->editableForTreeFields['listeners_columns'] as $editableForTreeField) {
                            if (stripos($column, $editableForTreeField) !== false) {
                                $listenerValues[strtolower($column)] = $values[$column];
                            }
                        }

                        foreach ($this->editableForTreeFields['columns'] as $editableForTreeField) {
                            if (stripos($column, $editableForTreeField) !== false) {
                                $editableColumns[] = $column;
                                $editableValues[$column] = $values[$column];
                            }
                        }
                    }

                    $columns = $editableColumns;
                    $values = $editableValues;
                }

                $this->listenerQueue[] = [
                    'idFields' => [
                        'id' => $code_field_value,
                    ],
                    'data' => $listenerValues,
                ];

                // update an existing record
                $sql = 'UPDATE ' . $table . ' SET ';
                $sql .= GeneratePDOSQLFromArrays(
                    [
                        'FieldArray' => $columns,
                        'DataArray' => $values,
                    ],
                    $PDOParamsArray,
                );

                $sql .= ' WHERE ' . $this->CodeFieldName . " = '" . $code_field_value . "'";
                if ($this->CodeTableName == 'code_types') {
                    $sql .= " AND cod_type = '" . $values['COD_TYPE'] . "'";
                }

                if ($this->CodeTableName == 'udf_codes') {
                    $sql .= ' AND field_id = ' . (int) $values['FIELD_ID'];
                }

                if (in_array('LANGUAGE', $columns)) {
                    $sql .= " AND language = '" . (int) $values['LANGUAGE'] . "'";
                }

                // no point running an update if there are no parameters
                if (!empty($PDOParamsArray)) {
                    DatixDBQuery::PDO_query($sql, $PDOParamsArray);
                }
            }
        }

        if ($table === $this->CodeTableName || strtoupper($table) === strtoupper($this->CodeDescriptionTableName)) {
            $this->insertDescriptionRecord($descriptionTableData);
        }

        return true;
    }

    private function insertDescriptionRecord(array $data): void
    {
        if (empty($data)) {
            return;
        }

        if (!isset($data['CODE'])) {
            return;
        }

        $query = 'SELECT count(*) FROM %s WHERE [code] = :code AND [language] = :lang';
        $exists = (int) DatixDBQuery::PDO_fetch(
            sprintf($query, $this->CodeDescriptionTableName),
            [
                'code' => $data['CODE'],
                'lang' => $data['LANGUAGE'],
            ],
            PDO::FETCH_COLUMN,
        );

        $query = null;
        if ($exists > 0 && in_array($this->ImportMethod, [self::METHOD_OVERWRITE, self::METHOD_UPDATE], true)) {
            $query = 'UPDATE %s SET [description] = :desc WHERE code = :code AND [language] = :lang';
        } elseif ($exists === 0) {
            $query = 'INSERT INTO %s (code, [language], [description]) VALUES (:code, :lang, :desc)';
        }

        if ($query) {
            $params = [
                'desc' => $data['DESCRIPTION'],
                'code' => $data['CODE'],
                'lang' => $data['LANGUAGE'],
            ];
            DatixDBQuery::PDO_query(sprintf($query, $this->CodeDescriptionTableName), $params);
        }
    }
}
