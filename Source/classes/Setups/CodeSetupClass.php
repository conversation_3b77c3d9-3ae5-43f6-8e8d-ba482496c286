<?php

use app\framework\QueryCacheFactory;
use app\models\generic\Tables;
use src\mortality\model\MortalityFields;
use src\redress\models\RedressFields;
use src\safeguarding\models\SafeguardingFields;

/**
 * Class used for code setups.
 */
class Setups_CodeSetup
{
    public $code_table;
    public $code_description_table;
    public $code_field;
    public $code_type;
    public $code_where;
    public $code_label;
    public $code_description;
    public $ErrorMsgs = [];
    private $fieldname;
    private $fieldtable;

    /**
     * @desc constructor - sets/gets required info about code setup
     */
    public function __construct($fieldname, $fieldtable)
    {
        $this->fieldname = $fieldname;
        $this->fieldtable = $fieldtable;
    }

    /**
     * @desc Outputs setup codes into XML format load file.
     */
    public function OutputCodeSetup()
    {
        $this->getCodeInfo();
        $this->OutputTableXML($this->code_table);
    }

    /**
     * @desc Outputs code descriptions into XML format load file.
     */
    public function OutputCodeDescriptions()
    {
        $this->getCodeInfo();
        $this->OutputTableXML($this->code_description_table, ' Description');
    }

    /**
     * @desc Imports data in XML format outputted via OutputCodeSetup method.
     *
     * @param string $RawXMLData XML data string to be imported
     * @param string $import_action Import method to use (APPEND|UPDATE|OVERWRITE)
     */
    public function ImportCodeSetup($RawXMLData, $import_action): bool
    {
        $this->getCodeInfo();
        $DatixCodesImport = new Setups_ImportCodes(
            $this->code_table,
            $this->code_field,
            $this->code_where,
            $this->code_description,
            $import_action,
        );
        if (!$DatixCodesImport->LoadXMLDoc($RawXMLData)) {
            $this->ErrorMsgs = $DatixCodesImport->ErrorMsgs;

            return false;
        }

        $udf = $this->fieldtable === 'udf_fields' ? $this->fieldname : '';
        $codeImport = $DatixCodesImport->importCodesXMLdata($udf);

        if ($codeImport) {
            $queryCache = (new QueryCacheFactory())->create();
            $queryCache->clear($this->code_table);

            return true;
        }

        $this->ErrorMsgs = $DatixCodesImport->ErrorMsgs;

        return false;
    }

    public function lockCodeSetup()
    {
        if (!IsCentrallyAdminSys()) {
            throw new NoPermissionException('Only centrally administered systems can be locked');
        }

        // we don't know if theres any previous record, so we can't
        // just update. Let's delete first.
        $this->deleteLockStatus();

        $params = [
            'field' => $this->fieldname,
            'table' => $this->fieldtable,
        ];

        DatixDBQuery::PDO_query('INSERT INTO locked_code_fields (code_field, code_table, locked) VALUES (:field, :table, 1)', $params);
    }

    public function unlockCodeSetup()
    {
        if (!IsCentrallyAdminSys()) {
            throw new NoPermissionException('Only centrally administered systems can be locked');
        }

        $this->deleteLockStatus();
    }

    /**
     * Was code field locked by central administrator?
     *
     * @return bool true if field is locked, false otherwise
     */
    public function isCodeFieldLocked()
    {
        if (!IsCentrallyAdminSys()) {
            // system without central administration
            // has no locked code fields
            return false;
        }

        $sql = 'SELECT locked FROM locked_code_fields WHERE code_table = :table AND code_field = :field';

        $params = [
            'field' => $this->fieldname,
            'table' => $this->fieldtable,
        ];

        $result = DatixDBQuery::PDO_fetch_all($sql, $params);

        if (!empty($result[0]['locked'])) {
            return true;
        }

        return false;
    }

    public static function getSystemWideFieldsList()
    {
        return [
            '!affecting_tier_zero' => [
                'inc_affecting_tier_zero',
                'cla_affecting_tier_zero',
                'com_affecting_tier_zero',
                RedressFields::RED_AFFECTING_TIER_ZERO,
                MortalityFields::MOR_AFFECTING_TIER_ZERO,
                SafeguardingFields::SFG_AFFECTING_TIER_ZERO,
            ],
            '!type_tier_one' => [
                'inc_type_tier_one',
                'cla_type_tier_one',
                'com_type_tier_one',
                RedressFields::RED_TYPE_TIER_ONE,
                MortalityFields::MOR_TYPE_TIER_ONE,
                SafeguardingFields::SFG_TYPE_TIER_ONE,
            ],
            '!type_tier_two' => [
                'inc_type_tier_two',
                'cla_type_tier_two',
                'com_type_tier_two',
                RedressFields::RED_TYPE_TIER_TWO,
                MortalityFields::MOR_TYPE_TIER_TWO,
                SafeguardingFields::SFG_TYPE_TIER_TWO,
            ],
            '!type_tier_three' => [
                'inc_type_tier_three',
                'cla_type_tier_three',
                'com_type_tier_three',
                RedressFields::RED_TYPE_TIER_THREE,
                MortalityFields::MOR_TYPE_TIER_THREE,
                SafeguardingFields::SFG_TYPE_TIER_THREE,
            ],
            '!level_harm' => [
                'inc_level_harm',
                'cla_level_harm',
            ],
            '!level_intervention' => [
                'inc_level_intervention',
                'cla_level_intervention',
            ],
        ];
    }

    /**
     * If this is a system wide field, change the fieldname.
     */
    public function useSystemWideFieldNames()
    {
        $list = self::getSystemWideFieldsList();

        foreach ($list as $sysField => $fields) {
            if (in_array($this->fieldname, $fields)) {
                $this->fieldname = $sysField;
                $this->fieldtable = '';
            }
        }
    }

    /**
     * @desc Outputs specified table as an XML format load file.
     *
     * @param string $codeTable name of the code table to output
     * @param string $filenameSuffix (optional) Text to append to the file name
     */
    private function OutputTableXML(string $codeTable, string $filenameSuffix = '')
    {
        switch ($this->code_table) {
            case Tables::LOCATION:
            case Tables::SERVICE:
                $CodeSetupExport = new TreeFieldSetups_ExportCodes($codeTable, $this->code_where);
                $CodeSetupExport->setFieldInfo($this->fieldname, $this->fieldtable);

                break;
            default:
                $CodeSetupExport = new Setups_ExportCodes($codeTable, $this->code_where);

                break;
        }

        $setup_xml = $CodeSetupExport->getCodeSetupsXML();

        $filename = trim(preg_replace('/[^A-Za-z0-9_ \-]/', '', $this->code_label));
        if (!$filename) {
            // This means there are no non-alphanumeric characters in the label, which can happen with
            // international languages (e.g. Arabic).
            $filename = $this->fieldname ?: 'export';
        }
        $filename .= $filenameSuffix . '.xml';

        header('Content-Type: text/xml');
        header("Content-Disposition: attachment; filename={$filename}");
        header('Content-Encoding: none');
        echo $setup_xml;

        exit;
    }

    /**
     * @desc Gets code related info for use in other class methods.
     *
     * @throws Exception
     */
    private function getCodeInfo()
    {
        if ($this->fieldtable === 'udf_fields') {
            $FDR_info = new Fields_ExtraField($this->fieldname);
            $this->code_where = 'field_id = ' . $FDR_info->GetID();
        } else {
            $FDR_info = new Fields_Field($this->fieldname, $this->fieldtable);
        }

        $this->code_table = $FDR_info->getCodeTable();
        $this->code_description_table = $FDR_info->getCodeDescriptionTable();
        $this->code_field = $FDR_info->getCodeTableField();
        $this->code_label = $FDR_info->getLabel();
        $this->code_description = $FDR_info->getCodeDescription();

        if ($this->code_table[0] === '!') {
            $this->code_type = UnicodeString::substr($this->code_table, 1, UnicodeString::strlen($this->code_table));
            $this->code_where = "cod_type = '{$this->code_type}'";
            $this->code_table = 'code_types';
            $this->code_description_table = 'code_types_descr';
            $this->code_field = 'cod_code';
        }
    }

    private function deleteLockStatus()
    {
        $params = [
            'field' => $this->fieldname,
            'table' => $this->fieldtable,
        ];

        DatixDBQuery::PDO_query('DELETE FROM locked_code_fields WHERE code_field = :field AND code_table = :table', $params);
    }
}
