<?php

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * Class used for retrieving code setup mapping info from field_setup_mappings table.
 */
class Setups_FieldSetupMappings
{
    private const MODULES_FOR_KO41 = [
        Module::FEEDBACK,
        Module::CONTACTS,
    ];
    private const MODULES_FOR_ODS = [
        Module::INCIDENTS,
    ];

    /** @var array */
    public $setupMappings;

    /**
     * @psalm-param non-empty-literal-string $fsmSetupTable
     *
     * @param string $fsmSetupType
     * @param string $module
     */
    public function __construct($fsmSetupTable, $fsmSetupType = '', $module = '')
    {
        $registry = Container::get(Registry::class);

        [$sql, $pdoParmsArray] = self::setupMappingsSql($registry, $fsmSetupTable, $fsmSetupType, $module);

        $this->setupMappings = DatixDBQuery::PDO_fetch_all($sql, $pdoParmsArray);
    }

    /**
     * @psalm-param non-empty-literal-string $fsmSetupTable
     *
     * @param string $fsmSetupType
     * @param string $module
     *
     * @psalm-return array{non-empty-literal-string, array{
     *  fsm_setup_table: non-empty-literal-string,
     *  fsm_setup_type?: non-falsy-string
     * }}
     */
    final protected static function setupMappingsSql(Registry $registry, $fsmSetupTable, $fsmSetupType = '', $module = ''): array
    {
        $sql = 'SELECT * FROM field_setup_mappings WHERE fsm_setup_table = :fsm_setup_table';
        $pdoParmsArray = [
            'fsm_setup_table' => $fsmSetupTable,
        ];

        if (!empty($fsmSetupType)) {
            $sql .= ' AND fsm_setup_type = :fsm_setup_type';
            $pdoParmsArray['fsm_setup_type'] = $fsmSetupType;
        }

        if (
            !in_array($module, array_merge(self::MODULES_FOR_KO41, self::MODULES_FOR_ODS), true)
            || (
                (
                    in_array($module, self::MODULES_FOR_KO41, true)
                    && $registry->GetParm('KO41_ENABLED', 'N')->isFalse()
                )
                || (
                    in_array($module, self::MODULES_FOR_ODS, true)
                    && $registry->getParm('ODS_CODES_ENABLED', 'N')->isFalse()
                )
            )
        ) {
            $sql .= " AND fsm_map_type != 'KO41'";
        }

        if (!in_array($module, [Module::FEEDBACK, Module::REDRESS], true)
            || $registry->GetParm('COM_DATA_SUBMISSION', 'N')->isFalse()
        ) {
            $sql .= " AND fsm_map_type != 'NCDS'";
        }

        if ($registry->GetParm('NRLS_ENABLED', 'N')->isFalse()) {
            $sql .= " AND fsm_map_type != 'NRLS'";
        }

        if ($registry->GetParm('SHOW_CFSMS_SETTINGS', 'Y')->isFalse()) {
            $sql .= " AND fsm_map_type != 'SIRS'";
        }

        if ($registry->GetParm('NRLS_ENABLED', 'N')->isTrue()
            && $registry->GetParm('NRLS_IN05_FIELD', 'ccs2')->toScalar() !== 'subcategory'
            && $fsmSetupTable === 'code_inc_subcat') {
            $sql .= " AND (fsm_setup_table != 'code_inc_subcat' AND fsm_map_type != 'NRLS')";
        }

        if ($registry->getParm('ENABLE_BODYMAP', 'N')->isFalse()) {
            $sql .= " AND fsm_map_type != 'BODYMP'";
        }

        if ($registry->getParm('HERO_ENABLED', 'N')->isFalse()) {
            $sql .= " AND fsm_setup_col != 'cod_hero'";
        }

        $sql .= ' ORDER BY fsm_order';

        return [$sql, $pdoParmsArray];
    }
}
