<?php

use app\models\framework\config\DatixConfig;
use app\models\generic\valueObjects\Module;
use app\models\psims\entities\PsimsAutopopulateConfigEntity;
use app\models\psims\entities\PsimsAutopopulateValueEntity;
use Doctrine\ORM\EntityManagerInterface;
use src\datixtable\model\PsimsAutopopulateDatixtableFactory;
use src\system\container\facade\Container;

/**
 * Class used for exporting code setups data in DATIX using XML data via files.
 * Similar to the exporting option found in the Rich client.
 */
class TreeFieldSetups_ExportCodes extends Setups_ExportCodes
{
    private string $fieldname;
    private string $fieldtable;

    public function setFieldInfo(string $fieldname, string $fieldtable): void
    {
        $this->fieldname = $fieldname;
        $this->fieldtable = $fieldtable;
    }

    protected function mergeAutopopulationFields(EntityManagerInterface $entityManager, array $data): array
    {
        if (empty($data)) {
            return $data;
        }

        $repository = $entityManager->getRepository(PsimsAutopopulateConfigEntity::class);

        /** @var array<PsimsAutopopulateConfigEntity[]> $configs */
        $configEntities = $repository->findBy([
            'modModule' => Module::INCIDENTS,
            'sourceFdrTable' => $this->fieldtable,
            'sourceFdrName' => $this->fieldname,
        ]);

        $codesByField = [];
        foreach ($configEntities as $configEntity) {
            $codes = array_map(
                static fn (PsimsAutopopulateValueEntity $value): string => $value->getCode(),
                $configEntity->getValues()->toArray(),
            );

            $codesByField[$configEntity->getDestinationFdrName()][$configEntity->getSourceCode()] = implode(',', $codes);
        }

        foreach ($data as &$item) {
            foreach (PsimsAutopopulateDatixtableFactory::FIELDS[$this->code_table][Module::INCIDENTS] as $field) {
                $item[$field] = $codesByField[$field][$item['id']] ?? '';
            }
        }

        return $data;
    }

    protected function getData(): array
    {
        $data = parent::getData();

        if (!Container::get(DatixConfig::class)->getPsimsEnabled()) {
            return $data;
        }

        $entityManager = Container::get(EntityManagerInterface::class);

        return $this->mergeAutopopulationFields($entityManager, $data);
    }
}
