<?php

use src\security\CompatEscaper;

/**
 * @desc Models a form button whether in the form of an icon on the floating menu to the left of the screen
 * or an actual HTML button at the bottom of a form.
 */
class Button
{
    // The contents of the "id" attribute of the HTML button
    protected $ID;

    // The contents of the "name" attribute of the HTML button
    protected $Name;

    // The label for the HTML button and the alt text for the icon
    protected $Label;

    // The javascrpt to be executed when the button or icon is clicked
    protected $OnClickJS;

    // The image to use for the icon (not required: if not provided, will default to a generic image).
    protected $Icon;

    // general "type" of action being performed. Used as a quick way of deciding on default behaviour/appearance
    protected $Action;

    // specific input type
    protected $Type;

    // data attributes created from this
    protected $Data;

    // whether the button is disabled or not
    private $disabled;

    // CSS class
    private string $class;

    /**
     * @desc Constructor: Assigns parameter values to object properties.
     *
     * @param array $Parameters the array of parameters to be assigned
     */
    public function __construct($Parameters = [])
    {
        $this->ID = $Parameters['id'] ?? null;
        $this->Name = $Parameters['name'] ?? null;
        $this->Label = $Parameters['label'] ?? null;
        $this->OnClickJS = $Parameters['onclick'] ?? null;
        $this->Icon = $Parameters['icon'] ?? null;
        $this->class = ' ' . ($Parameters['class'] ?? '');
        $this->Action = $Parameters['action'] ?? null;
        $this->Type = $Parameters['type'] ?? null ?: 'button';
        // This is an array, due to it being possible to attach multiple data attributes to an element
        // ['testName' => 'testVar'] becomes data-testName="testVar"
        $this->Data = $Parameters['data'] ?? null ?: [];
        $this->disabled = $Parameters['disabled'] ?? null ?: false;
    }

    /**
     * @desc Gets the HTML for an HTML button defined by this object.
     *
     * @return string the HTML for the button
     */
    public function getHTML()
    {
        $return = '<button type="' . $this->Type . '"';
        $return .= ($this->ID ? ' id="' . $this->ID . '"' : '');
        $return .= ' class="dtx-button' . $this->class . '" name="' . $this->Name . '" ';
        $return .= ($this->disabled ? ' disabled' : '');
        foreach ($this->Data as $name => $value) {
            $return .= ' data-' . $name . '="' . CompatEscaper::encodeEntities($value) . '"';
        }
        $return .= ($this->OnClickJS ? ' onClick="' . $this->OnClickJS . '"' : '') . '>
            <span>' . $this->Label . '</span>
        </button>';

        return $return;
    }

    /**
     * @desc Gets the HTML for a clickable icon defined by this object.
     *
     * @return string the HTML for the icon
     */
    public function getSideMenuHTML()
    {
        if ($this->getIcon()) {
            $return = '<button type="button" class="prince-button button-inline"';

            foreach ($this->Data as $name => $value) {
                $return .= ' data-' . $name . '="' . CompatEscaper::encodeEntities($value) . '"';
            }

            $return .= ' title="' . $this->Label . '" name="icon_link_' . $this->Name . '" id="icon_link_' . $this->ID . '"';
            $return .= ' onclick="' . $this->OnClickJS . '"><a>' . $this->getIcon() . '</a></button>';

            return $return;
        }

        return '';
    }

    /**
     * @desc Gets the location of the image file to be used for this button when displayed as an icon.
     *
     * @return string the location of the image
     */
    protected function getIcon()
    {
        // manual override
        if ($this->Icon) {
            return $this->Icon;
        }

        // image selected according to the general type of action the button performs.
        switch ($this->Action) {
            case 'SAVE':
                return '<span class="glyphicon glyphicon-floppy-disk" aria-hidden="true"></span>';
            case 'SEARCH':
                return '<span class="glyphicon glyphicon-search" aria-hidden="true"></span>';
            case 'DELETE':
                return '<span class="glyphicon glyphicon-trash" aria-hidden="true"></span>';
            case 'BACK':
                if ($this->ID === 'btnCopy') {
                    return '<span class="glyphicon glyphicon-copy" aria-hidden="true"></span>';
                }

                return '<span class="glyphicon glyphicon-arrow-left" aria-hidden="true"></span>';
            case 'CANCEL':
                return '<span class="glyphicon glyphicon-remove" aria-hidden="true"></span>';
            case 'RESET':
                return '<span class="glyphicon glyphicon-repeat" aria-hidden="true"></span>';
            default:
                return '';
        }
    }
}
