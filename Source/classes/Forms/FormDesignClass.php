<?php

use src\formdesign\forms\factories\FormDesignFactory;
use src\formdesign\forms\FormDesignGlobals;
use src\formdesign\forms\service\FormDesignTranslator;
use src\system\container\facade\Container;
use src\system\language\LanguageSession;

/**
 * @desc Class representing a form design.
 */
class Forms_FormDesign
{
    public $Module; // The module that this form design falls into
    public $Level; // The form design level (1 or 2)
    public $FormType; // The form mode (New, Edit, Search...)

    // controls whether the form can be edited.
    public $ReadOnly = false;
    public $Show_all_section;
    public $HideFields;
    public $ReadOnlyFields = [];
    public $taggedFields;
    public $lastChildFirstFields;
    public $MandatoryFields;
    public $OrderSections;
    public $DefaultValues;
    public $TextareaMaxChars;
    public $FieldOrders = [];
    public $DIF1UDFGroups;
    public $UDFGroups;
    public $NewPanels;
    public $ContactMatch;
    public $FormDesigns;
    public $ContactForms;
    public $ListingDesigns;
    public $MoveFieldsToSections;
    public $ExtraFields = [];
    public $DisplayAsCheckboxes;
    public $DisplayAsRadioButtons;
    public $TimestampFields;
    public $LockFieldAtribs = [];
    public $OrganisationMatch;
    public $progressNotesShowType = false;
    public $hideAddInjuryButton = false;
    public $hideAddContactNumberButton = false;
    public $EquipmentForms;
    public $showHistory = false;
    public $showHistorySearch = false;
    public $resendReferral;

    // parameters used for linked form designs.
    protected $ParentModule; // the module the this form design sits under. (e.g. an actions form linked to an incident)
    protected $ParentLevel; // the level of the form this form design sits under.
    protected $LinkType; // Identifier in case there are multiple forms linked to the parent form.

    // form design parameters
    protected $FormTitle;
    protected $FormTitleDescr;
    protected $AccessibleForLoggedOutUsers;
    protected $LfpseFieldsHidable;
    protected $UserLabels = [];
    protected $UserExtraText;
    protected $ExpandSections;
    protected $ExpandFields;
    protected $HelpTexts;
    protected $ExtraSections = [];
    private int $ID;

    /**
     * Determines how the object should handle a non-existent file.
     *
     * If "IGNORE", will do nothing.
     * If "DEFAULT", will change the file pointed to to a default file.
     * If blank, will throw an exception.
     */
    private string $NonExistentFile = 'DEFAULT';
    private LanguageSession $languageSession;

    /** @var int */
    private $suffix;

    /** @todo set to string|null or default to empty string */
    private string $filename;
    private FormDesignTranslator $translator;

    public function __construct()
    {
        $this->languageSession = Container::get(LanguageSession::class);
        $this->translator = Container::get(FormDesignTranslator::class);
    }

    public function __set($name, $value)
    {
        $setter = 'set' . $name;
        if (method_exists($this, $setter)) {
            $this->{$setter}($value);
        } else {
            // TODO check that the property name is valid
            $this->{$name} = $value;
        }
    }

    public function __get($name)
    {
        switch ($name) {
            case FormDesignGlobals::FORM_TITLE:
                return $this->translator->getFormTitle($this);
            case FormDesignGlobals::FORM_TITLE_DESCR:
                return $this->translator->getFormTitleDescr($this);
            case FormDesignGlobals::USER_LABELS:
                return $this->translator->getUserLabels($this);
            case FormDesignGlobals::USER_EXTRA_TEXT:
                return $this->translator->getUserExtraTexts($this);
            case FormDesignGlobals::HELP_TEXTS:
                return $this->translator->getHelpTexts($this);
            default:
                return $this->{$name};
        }
    }

    public function getFieldOrderSection(string $section): array
    {
        return $this->FieldOrders[$section] ?? [];
    }

    /**
     * @param string|array $value
     */
    public function setFormTitle($value): void
    {
        $this->translator->setFormTitle($this, $value);
    }

    /**
     * @param string|array<string, string>|null $formTitle
     */
    public function setFormTitleArray($formTitle): void
    {
        $this->FormTitle = $formTitle;
    }

    public function removeExtraSections()
    {
        $this->ExtraSections = null;
    }

    public function getParentModule()
    {
        return $this->ParentModule;
    }

    public function getParentLevel()
    {
        return $this->ParentLevel;
    }

    public function getLinkType()
    {
        return $this->LinkType;
    }

    /**
     * @desc Gets the filename associated with this form design.
     */
    public function GetFilename(): string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): void
    {
        $this->filename = $filename;
    }

    /**
     * Returns the level if its been set to the object (passes definition from array for fortify security check).
     */
    public function getLevel()
    {
        $possibleLevels = [1, 2];
        if (isset($this->Level) && in_array($this->Level, $possibleLevels)) {
            return $possibleLevels[$this->Level - 1];
        }
    }

    public function getId(): int
    {
        return $this->ID;
    }

    public function getModule(): string
    {
        return $this->Module;
    }

    public function setModule(string $module): void
    {
        $this->Module = $module;
    }

    public function deleteUDFrelationsTable($deleted, $table)
    {
        foreach ($this->{$table} as $key => $row) {
            if (substr($key, 0, 4) == 'UDF_') {
                $UDFParts = explode('_', $key);
                if (in_array($UDFParts[3] . '_' . $UDFParts[2], $deleted)) {
                    unset($this->{$table}[$key]);
                }
            }
        }
    }

    public function deleteUDFrelations($deleted)
    {
        $toClear = [
            FormDesignGlobals::HIDE_FIELDS,
            FormDesignGlobals::READ_ONLY_FIELDS,
            FormDesignGlobals::TAGGED_FIELDS,
            FormDesignGlobals::MANDATORY_FIELDS,
            FormDesignGlobals::USER_LABELS,
            FormDesignGlobals::USER_EXTRA_TEXT,
            FormDesignGlobals::DEFAULT_VALUES,
            FormDesignGlobals::TEXTAREA_MAX_CHARS,
            FormDesignGlobals::HELP_TEXTS,
            FormDesignGlobals::DISPLAY_AS_CHECKBOXES,
            FormDesignGlobals::DISPLAY_AS_RADIO_BUTTONS,
            FormDesignGlobals::TIMESTAMP_FIELDS,
            FormDesignGlobals::EXPAND_SECTIONS,
            FormDesignGlobals::EXPAND_FIELDS,
            FormDesignGlobals::LAST_CHILD_FIRST_FIELDS,
        ];

        foreach ($toClear as $row) {
            $this->deleteUDFrelationsTable($deleted, $row);
        }

        if (isset($this->FieldOrders)) {
            foreach ($this->FieldOrders as $key => $section) {
                foreach ($section as $order => $field) {
                    if (substr($field, 0, 4) == 'UDF_') {
                        $UDFParts = explode('_', $field);
                        if (in_array($UDFParts[3] . '_' . $UDFParts[2], $deleted)) {
                            unset($this->FieldOrders[$key][$order]);
                        }
                    }
                }
            }
        }
    }

    /**
     * @desc Returns a URL link to the design page for this form.
     */
    public function getDesignLink()
    {
        $this->RequireModule();
        $this->RequireLevel();

        return '?action=formdesignsetup&module=' . $this->Module . '&form_id=' . $this->getId() . '&formlevel=' . $this->Level;
    }

    public function AddSuffixToFormDesign($Suffix, $typeTitle = '')
    {
        global $ModuleDefs, $JSFunctions;

        $this->suffix = $Suffix;

        $language = $_POST['language'] ?? null ?: $this->languageSession->getLanguage();

        $mandatoryJSChanges = '';

        if ($Suffix) {
            $globals = [
                FormDesignGlobals::HIDE_FIELDS,
                FormDesignGlobals::USER_EXTRA_TEXT,
                FormDesignGlobals::DEFAULT_VALUES,
                FormDesignGlobals::USER_LABELS,
                FormDesignGlobals::TAGGED_FIELDS,
                FormDesignGlobals::CONTACT_MATCH,
                FormDesignGlobals::READ_ONLY_FIELDS,
                FormDesignGlobals::ORGANISATION_MATCH,
                FormDesignGlobals::LAST_CHILD_FIRST_FIELDS,
            ];

            foreach ($globals as $Key) {
                if (is_array($this->{$Key})) {
                    $NewArray = [];
                    foreach ($this->{$Key} as $FieldName => $value) {
                        unset($this->{$Key}[$FieldName]);
                        $NewArray[$FieldName . '_' . $Suffix] = $value;
                    }
                    $this->{$Key} = $NewArray;
                }
            }
            if (is_array($this->ExpandSections)) {
                $NewArray = [];
                foreach ($this->ExpandSections as $FieldName => $SectionArray) {
                    unset($this->ExpandSections[$FieldName]);

                    $NewArray[$FieldName . '_' . $Suffix] = $SectionArray;

                    foreach ($SectionArray as $index => $value) {
                        if ($value['section']) {
                            $NewArray[$FieldName . '_' . $Suffix][$index]['section'] = $value['section'] . '_' . $Suffix;
                        }
                    }
                }
                $this->ExpandSections = $NewArray;
            }
            if (is_array($this->ExpandFields)) {
                $NewArray = [];
                foreach ($this->ExpandFields as $FieldName => $FieldArray) {
                    unset($this->ExpandSections[$FieldName]);

                    $NewArray[$FieldName . '_' . $Suffix] = $FieldArray;

                    foreach ($FieldArray as $index => $value) {
                        if (!empty($value['field'])) {
                            $NewArray[$FieldName . '_' . $Suffix][$index]['field'] = $value['field'] . '_' . $Suffix;
                        }
                    }
                }
                $this->ExpandFields = $NewArray;
            }
            if (is_array($this->HelpTexts)) {
                $NewArray = [];
                foreach ($this->HelpTexts as $FieldName => $value) {
                    unset($this->HelpTexts[$FieldName]);
                    $NewArray[$FieldName . '_' . $Suffix] = $value;
                }
                $this->HelpTexts = $NewArray;
                // $_SESSION['HelpTexts'] = array_merge((is_array($_SESSION['HelpTexts']) ? $_SESSION['HelpTexts'] : array()) , $NewArray);
            }
            if (is_array($this->MandatoryFields)) {
                $NewArray = [];
                foreach ($this->MandatoryFields as $FieldName => $value) {
                    $FieldnameWithSuffix = $FieldName . ($Suffix ? '_' . $Suffix : '');
                    unset($this->MandatoryFields[$FieldName]);
                    $NewArray[$FieldnameWithSuffix] = $value;

                    // If rows are provided, make sure we are returning appropriate mandatory fields.
                    if ($this->Module == 'CON' && in_array($FieldName, $ModuleDefs[$this->Module]['LINKED_FIELD_ARRAY'])) {
                        $table = 'link_contacts';
                    } else {
                        $table = $ModuleDefs[$this->Module]['TABLE'];
                    }

                    $FieldLabel = $this->UserLabels[$FieldnameWithSuffix][$language]
                        ?? null
                        ?: Labels_FormLabel::GetFormFieldLabel($FieldName, '', $this->Module, $FieldnameWithSuffix, $table);

                    $mandatoryJSChanges .= 'if (mandatoryArray){mandatoryArray.push(new Array("' . $FieldName . '_' . $Suffix . '","' . $value . '_' . $Suffix . '","' .

                    \UnicodeString::str_ireplace('"', '\"', $FieldLabel) .

                    (($Suffix > 0 && !empty($typeTitle)) ? ' (' . $typeTitle . ')' : '') . '"))};
                    ';
                }
                $this->MandatoryFields = $NewArray;
            }
            if (is_array($this->FieldOrders)) {
                $NewArray = [];
                foreach ($this->FieldOrders as $id => $Section) {
                    foreach ($Section as $value => $FieldName) {
                        unset($this->FieldOrders[$id][$value]);
                        $NewArray[$id . '_' . $Suffix][$value] = $FieldName . '_' . $Suffix;
                    }
                }
                $this->FieldOrders = $NewArray;
            }
            if (is_array($this->ExtraFields)) {
                $NewArray = [];
                foreach ($this->ExtraFields as $id => $Section) {
                    unset($this->ExtraFields[$id]);
                    $NewArray[$id] = $Section . '_' . $Suffix;
                }
                $this->ExtraFields = $NewArray;
            }
        }

        $JSFunctions[] = $mandatoryJSChanges;
    }

    /**
     * @deprecated use factory instead
     *
     * @see FormDesignFactory::create()
     *
     * @param array{module: string, form_type: string, level: int, id: int, parent_module: string, parent_level: int, link_type: string, readonly: bool} $parameters
     */
    public static function GetFormDesign($parameters): self
    {
        return Container::get(FormDesignFactory::class)->create($parameters);
    }

    public function UnsetDefaultValues()
    {
        if (is_array($this->DefaultValues)) {
            foreach ($this->DefaultValues as $Field => $DefaultVal) {
                if (empty($this->HideFields[$Field])) {
                    unset($this->DefaultValues[$Field]);
                }
            }
        }
    }

    public function unsetUserDefinedElements()
    {
        $this->ExtraFields = null;
        $this->ExtraSections = null;
    }

    public function getUserLabel(string $key): ?string
    {
        $language = $this->languageSession->getLanguage();

        return $this->UserLabels[$key][$language] ?? null;
    }

    public function getUserLabelSection(string $key)
    {
        return $this->UserLabels[$key] ?? null;
    }

    public function isHidden(string $fieldOrSection): bool
    {
        if ($this->suffix) {
            $fieldOrSection .= '_' . $this->suffix;
        }

        return (bool) ($this->HideFields[$fieldOrSection] ?? false);
    }

    public function isReadOnly(string $fieldOrSection): bool
    {
        if ($this->suffix) {
            $fieldOrSection .= '_' . $this->suffix;
        }

        return (bool) ($this->ReadOnlyFields[$fieldOrSection] ?? false);
    }

    public function isAccessibleForLoggedOutUsers(): bool
    {
        return $this->AccessibleForLoggedOutUsers !== 'N';
    }

    public function setAccessibleForLoggedOutUsers(string $accessibleForLoggedOutUsers): void
    {
        $this->AccessibleForLoggedOutUsers = $accessibleForLoggedOutUsers;
    }

    public function isLfpseFieldsHidable(): bool
    {
        return $this->LfpseFieldsHidable === 'Y';
    }

    public function getLfpseFieldsHidable(): string
    {
        return $this->LfpseFieldsHidable ?: '';
    }

    public function setLfpseFieldsHidable(string $lfpseFieldsHidable): void
    {
        $this->LfpseFieldsHidable = $lfpseFieldsHidable;
    }

    /**
     * @param string|array<string, string>|null $formTitleDescr
     */
    public function setFormTitleDescr($formTitleDescr): void
    {
        $this->FormTitleDescr = $formTitleDescr;
    }

    public function hasFormTitleDescr(): bool
    {
        return isset($this->FormTitleDescr) && is_array($this->FormTitleDescr);
    }

    public function hasFormTitle(): bool
    {
        return isset($this->FormTitle) && is_array($this->FormTitle);
    }

    public function unsetExpandField(string $field, ?int $key = null): void
    {
        if ($key === null) {
            unset($this->ExpandFields[$field]);
        } else {
            unset($this->ExpandFields[$field][$key]);
        }
    }

    public function unsetExtraSectionsName(string $key): void
    {
        unset($this->ExtraSections[$key]);
    }

    public function setUserLabel(string $key, int $language, string $value): void
    {
        $this->UserLabels[$key][$language] = $value;
    }

    public function unsetUserLabel(string $key, ?int $language = null): void
    {
        if ($language === null) {
            unset($this->UserLabels[$key]);
        } else {
            unset($this->UserLabels[$key][$language]);
        }
    }

    public function unsetUserExtraText(string $key): void
    {
        unset($this->UserExtraText[$key]);
    }

    public function getUserExtraText(string $section): ?string
    {
        $language = $this->languageSession->getLanguage();

        return $this->UserExtraText[$section][$language] ?? null;
    }

    public function setExtraSection(string $key, string $value): void
    {
        $this->ExtraSections[$key] = $value;
    }

    public function setExpandField(string $section, int $key, array $value): void
    {
        $this->ExpandFields[$section][$key] = $value;
    }

    /**
     * @return string|array|null
     */
    public function getExpandFieldAlertText(string $section, int $key)
    {
        return $this->ExpandFields[$section][$key]['alerttext'] ?? null;
    }

    public function unsetExpandFieldAlertText(string $section, int $key): void
    {
        unset($this->ExpandFields[$section][$key]['alerttext']);
    }

    /**
     * @param string|array $value
     */
    public function setExpandSection(string $section, int $key, $value): void
    {
        $this->ExpandSections[$section][$key] = $value;
    }

    public function unsetExpandSection(string $section, int $key, ?string $fieldType = null): void
    {
        if ($fieldType === null) {
            unset($this->ExpandSections[$section][$key]);
        } else {
            unset($this->ExpandSections[$section][$key][$fieldType]);
        }
    }

    /**
     * @return string|array|null
     */
    public function getExpandSectionAlertText(string $section, int $key)
    {
        return $this->ExpandSections[$section][$key]['alerttext'] ?? null;
    }

    public function getHelpText(string $key): ?array
    {
        return $this->HelpTexts[$key] ?? null;
    }

    public function unsetHelpText(string $key): void
    {
        unset($this->HelpTexts[$key]);
    }

    /**
     * @param string|array $value
     */
    public function setHelpText(string $key, int $language, $value): void
    {
        $this->HelpTexts[$key][$language] = $value;
    }

    /**
     * @return string|array|null
     */
    public function getUserExtraTextSection(string $section)
    {
        return $this->UserExtraText[$section] ?? null;
    }

    public function setUserExtraText(string $key, int $language, string $translation): void
    {
        $this->UserExtraText[$key][$language] = $translation;
    }

    public function setUserExtraTexts($userExtraText): void
    {
        $this->UserExtraText = $userExtraText;
    }

    /**
     * @return string|array<string, string>|null
     */
    public function getFormTitle()
    {
        return $this->FormTitle;
    }

    /**
     * @return string|array<string, string>|null
     */
    public function getFormTitleDescr()
    {
        return $this->FormTitleDescr;
    }

    public function getExpandFields(): ?array
    {
        return $this->ExpandFields;
    }

    public function getExpandField(string $field): ?array
    {
        return $this->ExpandFields[$field] ?? null;
    }

    public function getLockFieldAttributes(): array
    {
        return $this->LockFieldAtribs;
    }

    public function hasLockFieldAttribute(string $field): bool
    {
        return isset($this->LockFieldAtribs[$field]['HIDE']) || isset($this->LockFieldAtribs[$field]['ALLFIELD']);
    }

    public function setShowAllSection($showAllSection): void
    {
        $this->Show_all_section = $showAllSection;
    }

    public function setHideFields($hideFields): void
    {
        $this->HideFields = $hideFields;
    }

    public function setReadOnlyFields($readOnlyFields): void
    {
        $this->ReadOnlyFields = $readOnlyFields;
    }

    public function setTaggedFields($taggedField): void
    {
        $this->taggedFields = $taggedField;
    }

    public function setLastChildFirstFields($lastChildFirstFields): void
    {
        $this->lastChildFirstFields = $lastChildFirstFields;
    }

    public function setMandatoryFields($mandatoryFields): void
    {
        $this->MandatoryFields = $mandatoryFields;
    }

    public function setOrderSections($orderSections): void
    {
        $this->OrderSections = $orderSections;
    }

    public function setUserLabels($userLabels): void
    {
        $this->UserLabels = $userLabels;
    }

    public function setDefaultValues($defaultValues): void
    {
        $this->DefaultValues = $defaultValues;
    }

    public function setTextareaMaxChars($textareaMaxChars): void
    {
        $this->TextareaMaxChars = $textareaMaxChars;
    }

    public function setFieldOrders($fieldOrders): void
    {
        $this->FieldOrders = $fieldOrders;
    }

    public function setDif1UdfGroups($dif1UdfGroups): void
    {
        $this->DIF1UDFGroups = $dif1UdfGroups;
    }

    public function setExpandSections($expandSections): void
    {
        $this->ExpandSections = $expandSections;
    }

    public function setExpandFields($EXPAND_FIELDS): void
    {
        $this->ExpandFields = $EXPAND_FIELDS;
    }

    public function setHelpTexts($helpTexts): void
    {
        $this->HelpTexts = $helpTexts;
    }

    public function setUdfGroups($udfGroups): void
    {
        $this->UDFGroups = $udfGroups;
    }

    public function setNewPanels($newPanels): void
    {
        $this->NewPanels = $newPanels;
    }

    public function setContactMatch($contactMatch): void
    {
        $this->ContactMatch = $contactMatch;
    }

    public function setFormDesigns($formDesigns): void
    {
        $this->FormDesigns = $formDesigns;
    }

    public function setContactForms($contactForms): void
    {
        $this->ContactForms = $contactForms;
    }

    public function setListingDesigns($listingDesigns): void
    {
        $this->ListingDesigns = $listingDesigns;
    }

    public function setExtraSections($extraSections): void
    {
        $this->ExtraSections = $extraSections;
    }

    public function setExtraFields($extraFields): void
    {
        $this->ExtraFields = $extraFields;
    }

    public function setMoveFieldsToSections($moveFieldsToSections): void
    {
        $this->MoveFieldsToSections = $moveFieldsToSections;
    }

    public function setDisplayAsCheckboxes($displayAsCheckboxes): void
    {
        $this->DisplayAsCheckboxes = $displayAsCheckboxes;
    }

    public function setDisplayAsRadioButtons($displayAsRadioButtons): void
    {
        $this->DisplayAsRadioButtons = $displayAsRadioButtons;
    }

    public function setTimestampFields($timestampFields): void
    {
        $this->TimestampFields = $timestampFields;
    }

    public function setProgressNotesShowType($progressNotesShowType): void
    {
        $this->progressNotesShowType = $progressNotesShowType;
    }

    public function setHideAddInjuryButton($hideAddInjuryButton): void
    {
        $this->hideAddInjuryButton = $hideAddInjuryButton;
    }

    public function setHideAddContactNumberButton($hideAddContactNumberButton): void
    {
        $this->hideAddContactNumberButton = $hideAddContactNumberButton;
    }

    public function setHideShowHistory($showHistory): void
    {
        $this->showHistory = $showHistory;
    }

    public function setHideShowHistorySearch($showHistorySearch): void
    {
        $this->showHistorySearch = $showHistorySearch;
    }

    public function setLockFieldsAttributes($lockFieldsAttributes): void
    {
        $this->LockFieldAtribs = $lockFieldsAttributes;
    }

    public function setOrganisationMatch($organisationMatch): void
    {
        $this->OrganisationMatch = $organisationMatch;
    }

    public function hasModule(): bool
    {
        return isset($this->Module);
    }

    public function hasLevel(): bool
    {
        return isset($this->Level);
    }

    public function getFormType()
    {
        return $this->FormType;
    }

    public function hasLinkType(): bool
    {
        return isset($this->LinkType);
    }

    public function getFormDesigns()
    {
        return $this->FormDesigns;
    }

    public function getShowAllSection()
    {
        return $this->Show_all_section;
    }

    public function getAccessibleForLoggedOutUsers()
    {
        return $this->AccessibleForLoggedOutUsers;
    }

    public function getHideFields()
    {
        return $this->HideFields;
    }

    public function getReadOnlyFields()
    {
        return $this->ReadOnlyFields;
    }

    public function getTaggedFields()
    {
        return $this->taggedFields;
    }

    public function getLastChildFirstFields()
    {
        return $this->lastChildFirstFields;
    }

    public function getMandatoryFields()
    {
        return $this->MandatoryFields;
    }

    public function getOrderSections()
    {
        return $this->OrderSections;
    }

    public function getUserLabels()
    {
        return $this->UserLabels;
    }

    public function getDefaultValues()
    {
        return $this->DefaultValues;
    }

    public function getTextareaMaxChars()
    {
        return $this->TextareaMaxChars;
    }

    public function getFieldOrders()
    {
        return $this->FieldOrders;
    }

    public function getDif1UdfGroups()
    {
        return $this->DIF1UDFGroups;
    }

    public function getExpandSections()
    {
        return $this->ExpandSections;
    }

    public function getHelpTexts()
    {
        return $this->HelpTexts;
    }

    public function getUdfGroups()
    {
        return $this->UDFGroups;
    }

    public function getNewPanels()
    {
        return $this->NewPanels;
    }

    public function getContactMatch()
    {
        return $this->ContactMatch;
    }

    public function getContactForms()
    {
        return $this->ContactForms;
    }

    public function getListingDesigns()
    {
        return $this->ListingDesigns;
    }

    public function getExtraSections()
    {
        return $this->ExtraSections;
    }

    public function getExtraFields()
    {
        return $this->ExtraFields;
    }

    public function getMoveFieldsToSections()
    {
        return $this->MoveFieldsToSections;
    }

    public function getDisplayAsCheckboxes()
    {
        return $this->DisplayAsCheckboxes;
    }

    public function getDisplayAsRadioButtons()
    {
        return $this->DisplayAsRadioButtons;
    }

    public function getTimestampFields()
    {
        return $this->TimestampFields;
    }

    public function getProgressNotesShowType()
    {
        return $this->progressNotesShowType;
    }

    public function getShowHistory()
    {
        return $this->showHistory;
    }

    public function getShowHistorySearch()
    {
        return $this->showHistorySearch;
    }

    public function getHideAddInjuryButton()
    {
        return $this->hideAddInjuryButton;
    }

    public function getHideAddContactNumberButton()
    {
        return $this->hideAddContactNumberButton;
    }

    public function getLockFieldsAttributes()
    {
        return $this->LockFieldAtribs;
    }

    public function getOrganisationMatch()
    {
        return $this->OrganisationMatch;
    }

    public function getUserExtraTexts()
    {
        return $this->UserExtraText;
    }

    public function getEquipmentForms()
    {
        return $this->EquipmentForms;
    }

    public function setFormType($formType): void
    {
        $this->FormType = $formType;
    }

    public function setLevel($level): void
    {
        $this->Level = $level;
    }

    public function setId(int $id): void
    {
        $this->ID = $id;
    }

    public function setParentModule($parentModule): void
    {
        $this->ParentModule = $parentModule;
    }

    public function setParentLevel(int $parentLevel): void
    {
        $this->ParentLevel = $parentLevel;
    }

    public function setLinkType($linkType): void
    {
        $this->LinkType = $linkType;
    }

    public function setReadOnly(bool $readOnly): void
    {
        $this->ReadOnly = $readOnly;
    }

    public function getNonExistentFile(): string
    {
        return $this->NonExistentFile;
    }

    public function getFormTitleLanguage(int $language): ?string
    {
        return $this->FormTitle[$language] ?? null;
    }

    public function getFormTitleDescrLanguage(int $language): ?string
    {
        return $this->FormTitleDescr[$language] ?? null;
    }

    /**
     * @param string|array $value
     */
    public function setFormTitleLanguage(int $language, $value): void
    {
        $this->FormTitle[$language] = $value;
    }

    public function getResendReferral(): bool
    {
        return $this->resendReferral ?? false;
    }

    public function setResendReferral(bool $resendReferral): void
    {
        $this->resendReferral = $resendReferral;
    }

    protected function RequireModule()
    {
        if (!$this->Module) {
            throw new Exception('No module provided.');
        }
    }

    protected function RequireLevel()
    {
        if (!$this->Level) {
            throw new Exception('No level provided.');
        }
    }
}
