<?php

use app\models\framework\config\DatixConfig;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * Service class for export functionality.
 */
class Service_Export extends Service_Service
{
    /**
     * Prints the last export error message.
     */
    public function getExportError()
    {
        echo Files_File::getExportError();
    }

    /**
     * Outputs NPSA export errors in CSV format.
     */
    public function getNPSAExportError()
    {
        $csv = new Files_CSV('npsa_export_errors');
        $csv->setData($_SESSION['npsaXMLErrors']);
        $csv->export();
    }

    /**
     * XML export for CFSMS.
     */
    public function cfsms()
    {
        if (isset($_POST['sirs_trust']) && DatixDBQuery::PDO_fetch('SELECT parmvalue FROM globals WHERE parameter = \'SIRS_TRUST\'', [], PDO::FETCH_COLUMN) == '') {
            // set the SIRS_TRUST global before proceeding with export
            DatixDBQuery::PDO_query('DELETE FROM globals WHERE parameter = \'SIRS_TRUST\'');
            DatixDBQuery::PDO_query('INSERT INTO globals (parameter, parmvalue) VALUES (\'SIRS_TRUST\', :sirs_trust)', ['sirs_trust' => $_POST['sirs_trust']]);
        }

        $exporter = new Export_CFSMS(new Files_XML(getParm('SIRS_TRUST', '', true) . '_' . (new DateTime())->format('Ymd')), new DOMDocument('1.0', 'UTF-8'));
        $exporter->doExport();
    }

    /**
     * CSV export for ISD.
     */
    public function isd()
    {
        if ($_GET['agent'] != '' && DatixDBQuery::PDO_fetch('SELECT parmvalue FROM globals WHERE parameter = \'ISD_AGENT\'', [], PDO::FETCH_COLUMN) == '') {
            // set the ISD_AGENT global before proceeding with export
            DatixDBQuery::PDO_query('DELETE FROM globals WHERE parameter = \'ISD_AGENT\'');
            DatixDBQuery::PDO_query('INSERT INTO globals (parameter, parmvalue) VALUES (\'ISD_AGENT\', :agent)', ['agent' => $_GET['agent']]);
        }

        $exporter = new Export_ISD(new Files_CSV('isd_export'), Container::get(Registry::class)->getFieldLabels());
        if (GetParm('ISD_AGENT', '', true) == '') {
            // need to set this global before export
            $exporter->displayAgentDialogue();
        } elseif ($_POST['isd_year'] == '' || $_POST['isd_edition'] == '') {
            // users need to submit month/year/edition values before running the ISD export
            $exporter->displayExportDialogue();
        } else {
            $exporter->doExport($_POST['isd_month'], $_POST['isd_year'], $_POST['isd_edition']);
        }
    }

    /**
     * Outputs ISD export errors in CSV format.
     */
    public function getISDExportError()
    {
        $csv = new Files_CSV('isd_export_errors');
        $csv->setData($_SESSION['isdErrors']);
        $csv->export();
    }

    /**
     * Outputs config files (SQLErrors, form designs etc).
     */
    public function exportConfigFile()
    {
        // extract name and extension, and create new file object
        $ext = \UnicodeString::substr(\UnicodeString::strrchr($this->Parameters['file'], '.'), 1);
        $filename = \UnicodeString::substr($this->Parameters['file'], 0, \UnicodeString::strlen($this->Parameters['file']) - (\UnicodeString::strlen($ext) + 1));

        switch ($ext) {
            case 'xml':
                $file = new Files_XML($filename);

                break;
            default:
                $file = new Files_TXT($filename, 'php');

                break;
        }

        // check for admin user
        LoggedIn();
        if (!$_SESSION['AdminUser']) {
            $file->setExportError('Error');
            $file->failExport();
        }

        // whitelist the file
        if (!in_array($this->Parameters['file'], ['index.php', 'config.xml', 'SQLErrors.xml']) && \UnicodeString::substr($this->Parameters['file'], 0, 4) != 'User') {
            $file->setExportError('Error');
            $file->failExport();
        }

        // add directory to filename
        if ($this->Parameters['file'] != 'index.php') {
            $this->Parameters['file'] = DatixConfig::CLIENT_FOLDER . '/' . $this->Parameters['file'];
        }

        // export the file
        try {
            $file->setContentsFromFile($this->Parameters['file']);
        } catch (FileNotFoundException $e) {
            $file->setExportError($e->getMessage());
            $file->failExport();
        }

        $file->export();
    }

    public function exportChartPdf()
    {
        $file = str_replace('ExportedImages', DatixConfig::CLIENT_FOLDER, Sanitize::SanitizeFilePath($_GET['file']));
        $pdf = new Files_PDF($file);
        $pdf->setContentsFromFile($file);
        $pdf->delete();
        $pdf->export();
    }
}
