<?php

use app\models\generic\valueObjects\Module;
use app\services\forms\PageTitleProvider;
use src\framework\controller\Loader;
use src\framework\registry\Registry;
use src\framework\views\ViewParams;
use src\framework\views\ViewRenderer;
use src\system\container\facade\Container;

class Service_DuplicateSearch extends Service_Service
{
    /** @readonly */
    private ViewRenderer $viewRenderer;

    public function __construct($Parameters = [])
    {
        parent::__construct($Parameters);

        $this->viewRenderer = Container::get(ViewRenderer::class);
    }

    /**
     * @return never
     */
    public function CollectParameters(): void
    {
        global $dtxtitle;

        $this->VerifyDuplicateSearchCredentials();

        $fieldLabels = Container::get(Registry::class)->getFieldLabels();

        $dtxtitle = 'Duplicate contacts search options';

        Container::get(PageTitleProvider::class)
            ->preGeneratePageTitleHtml(
                '',
                Module::CONTACTS,
                $dtxtitle,
            );

        GetSideMenuHTML(['module' => 'CON']);

        $template = new Template(['noPadding' => true]);

        template_header($template, null);

        echo $this->viewRenderer->render('./Views/classes/Service/DuplicateSearchClass/CollectParameters.php', new ViewParams([
            'Fields' => [
                'con_title' => $fieldLabels->getLabel('contacts_main', 'con_title'),
                'con_forenames' => $fieldLabels->getLabel('contacts_main', 'con_forenames'),
                'con_surname' => $fieldLabels->getLabel('contacts_main', 'con_surname'),
                'con_email' => $fieldLabels->getLabel('contacts_main', 'con_email'),
                'con_postcode' => $fieldLabels->getLabel('contacts_main', 'con_postcode'),
                'con_tel1' => $fieldLabels->getLabel('contacts_main', 'con_tel1'),
                'con_tel2' => $fieldLabels->getLabel('contacts_main', 'con_tel2'),
                'con_dob' => $fieldLabels->getLabel('contacts_main', 'con_dob'),
                'con_number' => $fieldLabels->getLabel('contacts_main', 'con_number'),
                'con_nhsno' => $fieldLabels->getLabel('contacts_main', 'con_nhsno'),
                'con_type' => $fieldLabels->getLabel('contacts_main', 'con_type'),
                'con_subtype' => $fieldLabels->getLabel('contacts_main', 'con_subtype'),
            ],
            'SelectedFields' => $_SESSION['CON']['LAST_SEARCH_DUPLICATE_FIELDS'],
        ]));

        footer($template);
        obExit();
    }

    /**
     * Takes fields provided in $_POST and searches db for contacts with these fields in common. Presents results to user.
     */
    public function PerformSearch()
    {
        global $dtxtitle;

        $this->VerifyDuplicateSearchCredentials();

        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $fields = $_POST['duplicate_fields'];
            unset($_SESSION['CON']['LAST_SEARCH_DUPLICATE_FIELDS']);
        } else {
            $fields = $_SESSION['CON']['LAST_SEARCH_DUPLICATE_FIELDS'];
        }


        if (empty($fields)) {
            AddSessionMessage('ERROR', 'No fields provided.');
            $this->CollectParameters();
        } else {
            $FieldsToSearchOn = Sanitize::SanitizeStringArray($_POST['duplicate_fields'] ?? $_SESSION['CON']['LAST_SEARCH_DUPLICATE_FIELDS']);
            $_SESSION['CON']['LAST_SEARCH_DUPLICATE_FIELDS'] = $FieldsToSearchOn;

            foreach ($FieldsToSearchOn as $Field) {
                // Create field object to get type
                $ContactsField = new Fields_Field($Field, 'contacts_main');

                // This is not a listing of contact records, so we need to treat the fields as dummy fields.
                $ListingFields[] = Listings_ListingColumnFactory::getColumn($Field, 'contacts_main');

                if ($ContactsField->getFieldType() == 'D') {
                    $SQLFieldsToSearchOn[] = $Field;
                } else {
                    $SQLFieldsToSearchOn[] = 'ISNULL(' . $Field . ', \'\') as ' . $Field;
                }
            }

            $ListingFields[] = new Fields_DummyField(['name' => 'num', 'label' => 'Number of matches', 'type' => 'N']);

            $Design = new Listings_ListingDesign(['columns' => $ListingFields]);

            $SecurityWhere = MakeSecurityWhereClause('', 'CON', $_SESSION['initials']);

            $Duplicates = DatixDBQuery::PDO_fetch_all(
                'SELECT ' . implode(', ', $FieldsToSearchOn) . ', num FROM
            (SELECT ' . implode(', ', $FieldsToSearchOn) . ', count(*) as num FROM
            (SELECT ' . implode(', ', $SQLFieldsToSearchOn) . ' FROM contacts_main' . ($SecurityWhere ? ' WHERE ' . $SecurityWhere : '') . ') g
            GROUP BY ' . implode(', ', $FieldsToSearchOn) . ') f
            WHERE f.num > 1 ORDER BY f.num DESC',
            );

            $RecordList = new RecordLists_RecordList();
            $RecordList->AddRecordData($Duplicates);

            $listing = new Listings_ListingDisplay($RecordList, $Design);
            $listing->Service = 'DuplicateSearch';
            $listing->Event = 'viewduplicatelisting';
            $listing->URLParameterArray = $FieldsToSearchOn;

            $dtxtitle = 'Possible duplicate contacts';

            Container::get(PageTitleProvider::class)
                ->preGeneratePageTitleHtml(
                    '',
                    Module::CONTACTS,
                    $dtxtitle,
                );

            GetSideMenuHTML(['module' => 'CON']);

            $template = new Template(['noPadding' => true]);

            template_header($template, null);

            $this->viewRenderer->render('./Views/classes/Service/DuplicateSearchClass/PerformSearch.php', new ViewParams([
                'Listing' => $listing,
            ]));

            footer($template);
            obExit();
        }
    }

    /**
     * @return never
     */
    public function ViewDuplicateListing(): void
    {
        global $ModuleDefs;

        foreach ($_GET as $key => $val) {
            if ($key != 'event' && $key != 'service' && isset($val)) {
                // format date fields
                if (in_array($key, $ModuleDefs['CON']['FIELD_ARRAY'])) {
                    $ContactsField = new Fields_Field($key, 'contacts_main');

                    if ($ContactsField->getFieldType() == 'D') {
                        $val = formatDateForDisplay($val);
                    }
                }

                if ($val == '') {
                    $val = '='; // need to pretend the data has come from a search.
                }

                $_POST[$key] = $val;
            }
        }

        $_POST['from_duplicate_search'] = true;

        $loader = new Loader();
        $controller = $loader->getController(
            ['controller' => src\contacts\controllers\ContactsDoSelectionController::class],
        );

        foreach ($_POST as $key => $value) {
            $controller->setRequestParameter($key, $value);
        }

        echo $controller->doAction('searchcontact');
        obExit();
    }

    /**
     * @desc Checks that the current user is permitted to run the duplicate search operation.
     */
    protected function VerifyDuplicateSearchCredentials()
    {
        LoggedIn();

        return true;
    }
}
