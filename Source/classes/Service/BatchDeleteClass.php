<?php

use app\models\framework\config\DatixConfig;
use app\models\framework\config\DatixConfigFactory;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\valueObjects\Module;
use app\services\forms\PageTitleProvider;
use src\framework\query\Query;
use src\framework\query\SqlWriter;
use src\framework\controller\Response;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\helpers\SqlInClauseHelper;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

class Service_BatchDelete extends Service_Service
{
    /**
     * @desc Displays a screen that allows users to confirm the deletion and choose whether documents will be deleted
     *
     * @param array $Parameters array of parameters
     *
     * @return never
     *
     * @codeCoverageIgnoreStart
     * No unit test, since it deals with constructing HTML
     */
    public function CollectParameters($Parameters): void
    {
        if (!(new DatixConfigFactory())->getInstance()->getBatchUpdateDeleteEnabled()) {
            throw new \URLNotFoundException();
        }

        $moduleRepository = Container::get(ModuleRepository::class);
        if (empty($Parameters['module']) || $moduleRepository->carltonModuleExists($Parameters['module'])) {
            throw new \URLNotFoundException();
        }

        global $dtxtitle;

        $registry = Container::get(Registry::class);
        $moduleDefs = $registry->getModuleDefs();

        $this->VerifyBatchDeleteCredentials($Parameters);

        $dtxtitle = 'Batch Delete';

        Container::get(PageTitleProvider::class)
            ->preGeneratePageTitleHtml(
                '',
                $Parameters['module'] ?? '',
                $dtxtitle,
            );

        GetSideMenuHTML(['module' => $Parameters['module']]);

        $template = new Template(['noPadding' => true]);

        template_header($template, null);

        if ($_SESSION[$Parameters['module']]['NEW_WHERE']) {
            $_SESSION[$Parameters['module']]['BATCH_UPDATE_REFERRER'] = $_SESSION['LAST_PAGE'];

            $recordsToDelete = $this->getRecordsToDelete(
                $moduleDefs[$Parameters['module']]->getDbReadObj(),
                $_SESSION[$Parameters['module']]['NEW_WHERE'],
            );
        } else {
            // to prevent additional searches within the same session from affecting this operation.
            $loggedInUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;

            $WhereClause = $_SESSION[$Parameters['module']]['WHERE'];
            $WhereClause = MakeSecurityWhereClause($WhereClause, $Parameters['module'], $loggedInUserInitials);

            // Apply any checkbox-selected record filters
            if ($_SESSION[$Parameters['module']]['RECORDLIST'] && !empty($_SESSION[$Parameters['module']]['RECORDLIST']->FlaggedRecords)) {
                if ($WhereClause) {
                    $WhereClause = '(' . $WhereClause . ') AND ';
                }

                $WhereClause .= (new SqlInClauseHelper())->inClause('recordid', $_SESSION[$Parameters['module']]['RECORDLIST']->FlaggedRecords);
            }

            $recordsToDelete = DatixDBQuery::PDO_fetch_all('SELECT recordid as num FROM ' . $moduleDefs[$Parameters['module']]->getDbReadObj() . ($WhereClause ? ' WHERE ' . $WhereClause : ''), [], PDO::FETCH_COLUMN);
        }

        $NumRecords = count($recordsToDelete);

        if ($NumRecords > 0) {
            $recordsWithLinkedContacts = $this->getRecordsWithLinkedContacts($recordsToDelete, $moduleDefs);
            $confirmText = _fdtk('batch_delete_default_text');
            if (!empty($recordsWithLinkedContacts)) {
                $conRecordsString = implode(', ', $recordsWithLinkedContacts);

                $confirmText = _fdtk('batch_delete_info') . ' <br/> ' .
                            sprintf(_fdtk('batch_delete_identifier_text'), $conRecordsString) . ' <br/> ' .
                            _fdtk('batch_delete_cancel_text') . '<br/>';
            }
            $_SESSION[$Parameters['module']]['BATCH_DELETE_RECORDS'] = $recordsToDelete;

            echo '<form method="POST" action="' . $registry->getScriptUrl() . '?service=BatchDelete&event=executefrompost&module=' . $Parameters['module'] . '">';

            echo '<div class="padded_div">';

            echo '<div>' . $confirmText . '</div>';
            echo '<input type="hidden" name="batchDeleteRecords" value="' . implode(',', $recordsToDelete) . '">';
            echo '<div><input type="checkbox" name="doc_delete" value="1"> ' . _fdtk('delete_all_documents') . '</div>';
            echo '<div>' . sprintf(_fdtk('batch_about_to_delete'), $NumRecords) . '</div></div>';

            echo '
        <div class="dtx-button-wrapper">
        <button type="button" class="dtx-button button-clear" onclick="SendTo(\'' . $registry->getScriptUrl() . '?action=home&module=' . $Parameters['module'] . '\');">
            <span>' . _fdtk('btn_cancel') . '</span>
        </button>
        <button type="button" class="dtx-button button-warning" onclick="if(confirm(\'' . sprintf(_fdtk('batch_permanent_delete'), $NumRecords) . '\')){this.form.submit()}">
            <span>' . sprintf(_fdtk('batch_delete_text'), $NumRecords) . '</span>
        </button>
        </div>';

            echo '</form>';
        }

        footer($template);
        obExit();
    }

    /**
     * @desc Executes a batch update operation using data passed from the setup form in the $_POST variable.
     *
     * @param array $Parameters array of parameters
     */
    public function ExecuteFromPost($Parameters)
    {
        if (!(new DatixConfigFactory())->getInstance()->getBatchUpdateDeleteEnabled()) {
            throw new \URLNotFoundException();
        }
        $this->VerifyBatchDeleteCredentials($Parameters);

        $Parameters['delete_docs'] = ($_POST['doc_delete'] == 1);

        $this->ExecuteAndReport($Parameters);
    }

    /**
     * @desc Returns records to be deleted for batch
     *
     * @param $table the records are being returned from
     * @param $where
     *
     * @return array records
     */
    public function getRecordsToDelete($table, $where)
    {
        // to prevent additional searches within the same session from affecting this operation.
        $query = (new Query())
            ->select([$table . '.recordid as num'])
            ->from($table)
            ->where($where);
        [$sql, $parameters] = Container::get(SqlWriter::class)->writeStatement($query);

        $db = (new DatixDBQuery());
        $db->setSQL($sql);
        $db->prepareAndExecute($parameters);

        return $db->PDOStatement->fetchAll(\PDO::FETCH_COLUMN);
    }

    // @codeCoverageIgnoreEnd

    /**
     * @desc Checks that the current user is permitted to run the batch delete operation.
     *
     * @param array $Parameters array of parameters
     */
    protected function VerifyBatchDeleteCredentials($Parameters): void
    {
        LoggedIn();

        $module = $Parameters['module'] ?? '';

        if ((new UserSessionFactory())->create()->isBatchDeleteAdmin()
            && $module
            && ModIsLicensed($module)
            && !in_array($module, [Module::USERS, Module::CONTACTS], true)
        ) {
            return;
        }

        $location = Container::get(DatixConfig::class)->getScriptUrl();
        Container::get(Response::class)->redirect($location);
    }

    /**
     * @desc Executes the batch update operation based on parameters recieved.
     *
     * @return array{deleted: int[], not_deleted: int[]}
     */
    protected function Execute($Parameters)
    {
        $moduleDefs = Container::get(ModuleDefs::class);

        $this->VerifyBatchDeleteCredentials($Parameters);

        $RecordList = new RecordLists_ModuleRecordList();
        $RecordList->Module = $Parameters['module'];
        $RecordList->Paging = false;
        // We need to set the table first otherwise Batch delete can't delete the records if a view is set.
        $RecordList->Table = $moduleDefs[$RecordList->Module]->getDbReadObj();

        $userInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;

        if ($moduleDefs[$Parameters['module']]['VIEW'] && $Parameters['module'] != 'PAY') {
            if (isset($_SESSION[$Parameters['module']]['BATCH_DELETE_WHERE'])) {
                $RecordList->WhereClause = MakeSecurityWhereClause(
                    $_SESSION[$Parameters['module']]['BATCH_DELETE_WHERE'],
                    $Parameters['module'],
                    $userInitials,
                    '',
                    '',
                    false,
                    false,
                );
            } else {
                $RecordList->WhereClause = MakeSecurityWhereClause(
                    '',
                    $Parameters['module'],
                    $userInitials,
                    '',
                    '',
                    false,
                    false,
                );
            }
        } elseif ($moduleDefs[$Parameters['module']]['VIEW'] && $Parameters['module'] == 'PAY') {
            $RecordList->WhereClause = MakeSecurityWhereClause(
                $_SESSION[$Parameters['module']]['WHERE'],
                $Parameters['module'],
                $userInitials,
            );
        } else {
            if (isset($_SESSION[$Parameters['module']]['BATCH_DELETE_WHERE'])) {
                $RecordList->WhereClause = MakeSecurityWhereClause(
                    $_SESSION[$Parameters['module']]['BATCH_DELETE_WHERE'],
                    $Parameters['module'],
                    $userInitials,
                );
            } else {
                $RecordList->WhereClause = MakeSecurityWhereClause('', $Parameters['module'], $userInitials);
            }
        }

        $recordsToDelete = explode(',', $_POST['batchDeleteRecords']);

        if (empty($recordsToDelete)) {
            throw new \Exception(_fdtk('search_errors'));
        }

        $recordsWhereClause = (new SqlInClauseHelper())->inClause('recordid', $recordsToDelete);

        // We need a sub-select to deal with modules that uses a view.
        if (!empty($RecordList->WhereClause) && $moduleDefs[$Parameters['module']]['VIEW']) {
            $RecordList->WhereClause = $recordsWhereClause . ' AND recordid IN (SELECT recordid FROM ' . $moduleDefs[$Parameters['module']]['VIEW'] . ' WHERE ' . $RecordList->WhereClause . ')';
        } elseif (!empty($RecordList->WhereClause)) {
            $RecordList->WhereClause = '(' . $RecordList->WhereClause . ') AND ';
        }

        if ($moduleDefs[$Parameters['module']]['VIEW'] == '' || $moduleDefs[$Parameters['module']]['VIEW'] == null) {
            $RecordList->WhereClause .= $recordsWhereClause;
        }

        $Result = $RecordList->BatchDelete($Parameters['delete_docs']);

        return $Result;
    }

    /**
     * @desc Executes the batch update operation based on parameters recieved and then outputs the result to the browser.
     * Can be called from POST via ExecuteFromPost(), or from within the application.
     *
     * @codeCoverageIgnoreStart
     * No unit test, since it deals with constructing HTML
     */
    protected function ExecuteAndReport($Parameters)
    {
        global $dtxtitle, $ModuleDefs;

        $this->VerifyBatchDeleteCredentials($Parameters);

        $Result = $this->Execute($Parameters);

        $dtxtitle = 'Batch delete: Results';

        Container::get(PageTitleProvider::class)
            ->preGeneratePageTitleHtml(
                '',
                $Parameters['module'] ?? '',
                $dtxtitle,
            );

        GetSideMenuHTML(['module' => $Parameters['module']]);

        $template = new Template(['noPadding' => true]);

        template_header($template, null);

        echo '<div class="padded_div">';

        if (count($Result['deleted']) > 0) {
            echo count($Result['deleted']) . ' records have been deleted.';

            if (count($Result['not_deleted']) > 0) {
                echo 'Some records could not be deleted (' . implode(', ', $Result['not_deleted']) . ')';
            }
        } elseif (count($Result['deleted']) == 0) {
            echo 'Problems occurred during this process. No records were deleted.';
        }

        echo '</div>';

        footer($template);
        obExit();
    }

    /**
     * @return array
     */
    private function getRecordsWithLinkedContacts(array $recordIds, ModuleDefs $moduleDefs)
    {
        if (empty($recordIds)) {
            return [];
        }

        $isMainModule = $moduleDefs[$this->Parameters['module']]['IS_MAIN_MODULE'];

        if (empty($isMainModule)) {
            return [];
        }

        $foreignKey = $moduleDefs[$this->Parameters['module']]['FK'];

        return DatixDBQuery::PDO_fetch_all("SELECT DISTINCT {$foreignKey} FROM link_contacts WHERE " . (new SqlInClauseHelper())->inClause($foreignKey, $recordIds), [], PDO::FETCH_COLUMN);
    }

    // @codeCoverageIgnoreEnd
}
