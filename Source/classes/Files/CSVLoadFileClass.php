<?php
/**
 * CSV load file class.
 */
class Files_CSVLoadFile extends Files_CSV
{
    protected $fileName;

    /**
     * Constructor: sets file name and delimiter to |.
     *
     * @param string $tmpName The filename in the tmp upload dir
     * @param string $fileName The original filename before upload
     */
    public function __construct($tmpName, $fileName = '')
    {
        parent::__construct($tmpName, '|');

        $this->fileName = $fileName;
    }

    /**
     * Gets the header info from the load file, defined on the first line.
     *
     * @return array $header
     */
    public function getHeader()
    {
        if ($this->lineNumber != 0) {
            $this->resetPointer();
        }

        $header = $this->getNextLine();
        if ($header[0][0] != '!') {
            // if header line does not begin with ! then assume we are importing a set of codes - the tablename will be the original filename in this case.
            $header = [explode('.', $this->fileName)[0], 'OVERWRITE'];
        } else {
            // remove ! from header
            $header[0] = \UnicodeString::substr($header[0], 1);
        }

        return $header;
    }

    /**
     * Gets the field names from the load file, defined on the second line unless there is no header line.
     *
     * @return array
     *
     * @see Files_CSVLoadFile::getNextLine()
     */
    public function getFieldNames()
    {
        $this->resetPointer();
        $firstLine = $this->getNextLine();

        if ($firstLine[0][0] != '!') {
            return $firstLine;
        }

        return $this->getNextLine();
    }
}
