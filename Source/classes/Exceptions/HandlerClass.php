<?php

use app\models\framework\config\InvalidConfigurationException;
use src\contacts\Exceptions\InvalidContactAuditingConfigException;
use src\framework\registry\Registry;
use src\logger\Facade\Log;
use src\security\Escaper;
use src\system\container\facade\Container;

class Exceptions_Handler
{
    /**
     * @desc Handles any exceptions that get through other error-catching.
     *
     * @param Exception $e the exception object that was just thrown
     */
    public static function handle($e)
    {
        Log::critical('An uncaught exception was found.', [
            'exception' => $e,
        ]);

        $errorMessage = Escaper::escapeForHTML($e->getMessage());
        $registry = Container::get(Registry::class);
        $seeDetailedErrors = $registry->getParm('DATIX_SUPPORT_ACCOUNT', 'N')->isTrue();

        switch (get_class($e)) {
            case URLNotFoundException::class:
                http_response_code(404);
                fatal_error('404: Not Found');

                break;
            case InvalidConfigurationException::class:
                echo $errorMessage;

                break;
            case PermissionDeniedException::class:
                fatal_error('You do not have permission to access this page.');

                break;
            case InvalidContactAuditingConfigException::class:
                http_response_code(500);
                $msg = 'Cannot process submission due to Contact Auditing configuration error.'; // replace with _fdtk

                if ($seeDetailedErrors) {
                    $errorArray = self::getTraceArray($e->getTrace());
                    array_unshift($errorArray, 'A system error has occurred: ' . $errorMessage . ' in ' . $e->getFile() . ' line ' . $e->getLine());
                    if ($e->detailedReason) {
                        array_unshift($errorArray, Sanitize::SanitizeHtml($e->detailedReason));
                    }
                    array_unshift($errorArray, $msg);
                    fatal_error($errorArray);
                } else {
                    // default message
                    $title = _fdtk('common_error');

                    if ($e->vagueReason) {
                        $msg .= '<br />' . Sanitize::SanitizeHtml($e->vagueReason);
                    }

                    // if exception has userVisible flag, display it
                    if (!empty($e->userVisible)) {
                        $msg = $errorMessage;
                    }

                    fatal_error($msg, $title);
                }

                break;
            default:
                $errorArray = self::getTraceArray($e->getTrace());
                array_unshift($errorArray, 'A system error has occurred: ' . $errorMessage . ' in ' . $e->getFile() . ' line ' . $e->getLine());

                http_response_code(500);
                if ($seeDetailedErrors) {
                    fatal_error($errorArray);
                } else {
                    Log::error(
                        'Fatal Error, logging full error details before creating non-detailed message.',
                        $errorArray,
                    );

                    // default message
                    $msg = _fdtk('system_error');
                    $title = _fdtk('common_error');

                    // if exception has userVisible flag, display it
                    if (!empty($e->userVisible)) {
                        $msg = $errorMessage;
                    }

                    fatal_error($msg, $title);
                }

                break;
        }
    }

    public static function getTraceArray($trace): array
    {
        $ErrorArray = [];

        foreach ($trace as $traceelement) {
            $ErrorHTML = '';

            if (isset($traceelement['file'], $traceelement['line'])) {
                $ErrorHTML .= $traceelement['file'] . ' (' . $traceelement['line'] . ') ';
            }

            if (isset($traceelement['class'])) {
                $ErrorHTML .= $traceelement['class'] . ' ' . $traceelement['type'];
            }

            $ErrorHTML .= $traceelement['function'];

            $ErrorArray[] = $ErrorHTML;
        }

        return $ErrorArray;
    }
}
