<?php

namespace Source\classes\Exceptions;

use Exception;
use Throwable;

class InvalidRequestParameterException extends Exception
{
    private array $errors;

    public function __construct($message = '', array $errors = [], $code = 0, ?Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $errors;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function setErrors(array $errors): void
    {
        $this->errors = $errors;
    }
}
