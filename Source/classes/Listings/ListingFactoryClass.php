<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\treeFields\TreeFieldCodeRetrieverFactory;
use src\contacts\service\ContactIdNumbersServiceFactory;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageServiceFactory;
use src\system\language\LanguageSessionFactory;

/**
 * Factory class for listings.
 *
 * @codeCoverageIgnore
 */
class Listings_ListingFactory
{
    /**
     * Returns the correct Listing object for this module.
     *
     * @param string $module the module code
     *
     * @return Listing
     */
    public static function getListing($module, $listingId = null)
    {
        $languageService = (new LanguageServiceFactory())->create();
        $languageSession = LanguageSessionFactory::getInstance();
        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
        $treeFieldCodeRetrieverFactory = new TreeFieldCodeRetrieverFactory();
        $registry = Container::get(Registry::class);
        $contactIdNumberService = (new ContactIdNumbersServiceFactory())->create();

        require_once 'Source/libs/ListingClass.php';

        $listing = new \Listing(Sanitize::getModule($module), $listingId, $languageService, $languageSession, $codeInfoRetriever, $treeFieldCodeRetrieverFactory, $registry, $contactIdNumberService);
        $listing->EmptyMessage = _fdtk('no_records_found');

        return $listing;
    }

    public function getContactListing($listingId)
    {
        $languageService = (new LanguageServiceFactory())->create();
        $languageSession = LanguageSessionFactory::getInstance();
        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
        $treeFieldCodeRetrieverFactory = new TreeFieldCodeRetrieverFactory();
        $registry = Container::get(Registry::class);
        $contactIdNumberService = (new ContactIdNumbersServiceFactory())->create();

        require_once 'Source/libs/ContactListingClass.php';

        $listing = new \ContactListing('CON', $listingId, $languageService, $languageSession, $codeInfoRetriever, $treeFieldCodeRetrieverFactory, $registry, $contactIdNumberService);
        $listing->EmptyMessage = _fdtk('no_records_found');

        return $listing;
    }
}
