<?php

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * Organisation listings when searching for matching organisations on level one forms.
 */
class Listings_LevelOneOrgMatchListing extends Listings_ModuleListingDesign
{
    /**
     * Returns either user defined (if set) or default columns.
     *
     * @param string $module The module listing to return
     *
     * @return array An array of columns
     */
    public static function GetListingColumns($module): array
    {
        $Listing = new self(['module' => Module::ORGANISATIONS, 'listing_id' => self::GetDefaultListing($module)]);
        $Listing->LoadColumnsFromDB();

        return $Listing->Columns;
    }

    /**
     * Returns the default listing for a given module.
     *
     * @param $module
     *
     * @return int|string the id of the default listing for this module
     */
    public static function GetDefaultListing($module): int
    {
        return Container::get(Registry::class)
            ->getParm($module . '_L1_ORG_MATCH_LISTING_ID', '0')
            ->toInt();
    }
}
