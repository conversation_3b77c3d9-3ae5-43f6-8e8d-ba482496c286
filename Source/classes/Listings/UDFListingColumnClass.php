<?php

use app\services\listing\ListingColumnInterface;

/**
 * @desc Object representing a listing column (and hence really a database field).
 */
class Listings_UDFListingColumn extends Fields_ExtraField implements ListingColumnInterface
{
    protected $Width;
    protected $Descending = true;

    public function setWidth($width)
    {
        $this->Width = $width;
    }

    public function setDescending()
    {
        $this->Descending = true;
    }

    public function setAscending()
    {
        $this->Descending = false;
    }

    public function getOrder()
    {
        if ($this->Descending) {
            return 'DESC';
        }

        return 'ASC';
    }
}
