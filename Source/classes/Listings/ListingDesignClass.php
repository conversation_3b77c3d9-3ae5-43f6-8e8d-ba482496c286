<?php

use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @desc Class representing the design of a record listing.
 * This class is module-independent.
 *
 * Used to create listings not associated with a particular module.
 * This class should not contain any methods to display listings, or to collect data.
 */
class Listings_ListingDesign
{
    /** Constants used when adding additional columns. */
    public const APPEND = 1;
    public const PREPEND = 2;

    /**
     * @var string
     *
     * @desc The 3 letter code for module that the records in this listing belong to.
     */
    public $Module;

    /**
     * @var string
     *
     * @desc The Table that the records in this listing belong to.
     */
    public $Table;

    /**
     * @var Listings_ListingColumn[]
     *
     * @desc Array of Listings_ListingColumn objects, representing the columns of the listing.
     */
    public $Columns = []; // Array of Listings_ListingColumn objects, representing the columns of the listing.

    /**
     * @var string
     *
     * @desc the name of the form on the listings listing page.
     */
    public $Title;

    /**
     * @var bool
     *
     * @desc controls whether the design can be edited.
     */
    public $ReadOnly = false;
    protected Registry $registry;

    /**
     * @var array
     *
     * @desc Array of option links for each record
     */
    private $options = [];

    /**
     * @var int
     *
     * @desc maximum number of options
     */
    private $maxNumberOptions = 0;

    /**
     * @desc Constructor - sets a couple of basic variables.
     *
     * @param array{table: string|null, module: string|null, columns: array|null} $Parameters Array of parameters
     */
    public function __construct($Parameters)
    {
        $this->Table = $Parameters['table'] ?? null;
        $this->Module = $Parameters['module'] ?? null;

        if (!empty($Parameters['columns']) && is_array($Parameters['columns'])) {
            $this->LoadColumnsFromArray($Parameters['columns']);
        }

        $this->registry = Container::get(Registry::class);
    }

    /**
     * @desc Returns the contents of the Table parameter
     */
    public function getColumnObjects()
    {
        return $this->Columns;
    }

    /**
     * @desc Sets the array of columns on this object (using this to set null in some confusing circumstances)
     */
    public function SetColumnArray($newColumns)
    {
        $this->Columns = $newColumns;
    }

    /**
     * @desc Loads column definitions directly from an array
     *
     * @param array $Columns array of Listings_ListingColumn objects
     */
    public function LoadColumnsFromArray($Columns)
    {
        self::CheckColumns($Columns);

        $this->Columns = $Columns;
    }

    /**
     * @desc Appends additional (probably hard coded) columns to the end of the existing column list.
     *
     * @param array $AdditionalColumns an array of Fields_DummyField objects
     * @param int $option whether to add the column to the front or the end of the listing
     */
    public function AddAdditionalColumns(array $AdditionalColumns, $option = self::APPEND)
    {
        self::CheckColumns($AdditionalColumns);

        if ($option == self::PREPEND) {
            $this->Columns = SafelyMergeArrays([$AdditionalColumns, $this->Columns]);
        } else {
            $this->Columns = SafelyMergeArrays([$this->Columns, $AdditionalColumns]);
        }
    }

    /**
     * @desc Similar to the listing HTML generated when viewing a listing, this generates HTML for use in the listing designer,
     * representing a preview of the columns, with fields to set their widths.
     *
     * @return string HTML representing a preview of the listing
     */
    public function GetListingPreviewHTML()
    {
        $fieldLabels = $this->registry->getFieldLabels();

        $HTML = '<table id="listing_preview_table" class="gridlines" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">';

        $HTML .= '<tr name="element_section_row" id="element_section_row">'; // title row
        if (is_array($this->Columns)) {
            // Table headers
            foreach ($this->Columns as $Column) {
                $HTML .= '<th id="col_heading_' . $Column->getTable() . '-' . $Column->getName() . '" class="windowbg"' . ($Column->getWidth() ? ' width="' . $Column->getWidth() . '%"' : '') . '>';

                if ($Column->getName() == 'mod_title') {
                    $currentCols['mod_title'] = 'Module';
                } elseif ($Column->getName() == 'recordid') {
                    if ($col_info_extra['colrename']) {
                        $currentCols[$Column->getName()] = $col_info_extra['colrename'];
                    } else {
                        // todo: need to define local getModule method
                        $currentCols[$Column->getName()] = $fieldLabels->getLabel($Column->getTable(), $Column->getName());
                    }
                } else {
                    $currentCols[$Column->getName()] = $fieldLabels->getLabel($Column->getTable(), $Column->getName());
                }
                $HTML .= $currentCols[$Column->getName()];

                $HTML .= '</th>';
            }

            $HTML .= '</tr>';
            $HTML .= '<tr>';

            foreach ($this->Columns as $Column) {
                // $HTML .= '<th class="windowbg"' . ($col_info['width'] ? 'width="' . $col_info['width'] . '%"' : '') . '>';

                if ($this->ReadOnly) {
                    $HTML .= '<td class="windowbg" style="text-align:center">' . $Column->getWidth() . '%</td>';
                } else {
                    $HTML .= '<td class="windowbg" style="text-align:center"><input id="col_width_' . $Column->getTable() . '-' . $Column->getName() . '" name="col_width_' . $Column->getTable() . '-' . $Column->getName() . '" type="string" size="2" maxlength="3" value="' . $Column->getWidth() . '" onchange="jQuery(\'#col_heading_' . $Column->getTable() . '-' . $Column->getName() . '\').width(this.value.toString()+\'%\')"></td>';
                }

                // $HTML .= '</th>';
            }
        }

        $HTML .= '</tr>';

        $HTML .= '</table>';

        return $HTML;
    }

    // @codeCoverageIgnoreEnd
    public function getColumns($includeTablePrefix = false)
    {
        $colArray = [];

        foreach ($this->Columns as $column) {
            if ($includeTablePrefix) {
                $colArray[] = $column->getTable() . '.' . $column->getName();
            } else {
                $colArray[] = $column->getName();
            }
        }

        return $colArray;
    }

    public function setOptions($recordid, array $options)
    {
        if (empty($options)) {
            return;
        }

        $this->options[$recordid] = $options;

        // keep the max number of options based on the highest array key
        $numOptions = max(array_keys($options));
        $this->maxNumberOptions = max($numOptions, $this->maxNumberOptions);
    }

    public function getOptions($recordid): array
    {
        return $this->options[$recordid] ?? [];
    }

    public function getMaxNumOfOptions()
    {
        return $this->maxNumberOptions;
    }

    /**
     * @desc Gets an array of columns in the style of the old file-based listing design. Used while not all code is converted to use the new classes.
     *
     * @return array<string, array{width: int}>
     */
    public function GetColumnArray(): array
    {
        $columnArray = [];
        foreach ($this->Columns as $column) {
            $columnArray[$column->getName()] = ['width' => $column->getWidth()];
        }

        return $columnArray;
    }

    /**
     * @desc Returns the contents of the Table parameter
     */
    protected function getTable()
    {
        return $this->Table;
    }

    /**
     * @desc Returns the contents of the Module parameter
     */
    protected function getModule()
    {
        return $this->Module;
    }

    /**
     * @desc Checks that the objects contained by an array are all of the correct type to be used as columns in this listing.
     */
    protected static function CheckColumns($ColumnArray)
    {
        foreach ($ColumnArray as $Column) {
            if (!$Column instanceof Fields_DummyField) {
                throw new IncorrectColumnClassException('Object passed of type ' . get_class($Column) . ' rather than Fields_DummyField');
            }
        }
    }
}
