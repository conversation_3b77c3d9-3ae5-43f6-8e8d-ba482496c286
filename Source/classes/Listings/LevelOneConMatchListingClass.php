<?php

use app\models\generic\valueObjects\Module;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * Contact listings when searching for matching contacts on level one forms.
 */
class Listings_LevelOneConMatchListing extends Listings_ModuleListingDesign
{
    /**
     * Returns either user defined (if set) or default columns.
     *
     * @param string $module The module listing to return
     *
     * @return Listings_ListingColumn[]
     */
    public static function GetListingColumns($module): array
    {
        $Listing = new self(['module' => Module::CONTACTS, 'listing_id' => self::GetDefaultListing($module)]);
        $Listing->LoadColumnsFromDB();

        return $Listing->Columns;
    }

    /**
     * @desc Returns the default listing for a given module.
     *
     * @return int the id of the default listing for this module
     */
    public static function GetDefaultListing($module): int
    {
        return Container::get(Registry::class)
            ->getParm($module . '_L1_CON_MATCH_LISTING_ID', '0')
            ->toInt();
    }
}
