<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\models\forms\inlineListings\InlineListingLinkUrlProviderFactory;
use app\models\generic\valueObjects\Module;
use src\framework\model\Entity;
use src\framework\model\RecordEntity;
use src\framework\registry\Registry;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

/**
 * @desc Object to display a list of records. Uses the RecordList object as a data source.
 */
class Listings_ListingDisplay
{
    public const COLUMN_TYPE_DATE_TIME = 'DT';
    public const COLUMN_TYPE_STRING = 'S';

    /**
     * @var obj
     *
     * @desc Object representing the list of records to display in this listing.
     */
    public $RecordList;

    /**
     * @var obj
     *
     * @desc Object representing the design of this listing.
     */
    public $ListingDesign;

    /**
     * @var bool
     *
     * @desc If true, this listing will be readonly.
     */
    public $ReadOnly = false;

    /**
     * @var bool
     *
     * @desc used for SABS (for example) where we need a checkbox next to every item.
     */
    public $Checkbox = false;

    /**
     * @var string
     *
     * @desc the "action" that these listing items should link to. Only required if no module provided by RecordList.
     */
    public $Action;

    /**
     * @var string
     *
     * @desc the "service" and "event" that these listing items should link to, if items on the list are interpreted by a service class.
     */
    public $Service;
    public $Event;

    /**
     * @var array
     *
     * @desc array of keys that need to be included in the url that each item links to. By default, this will be "recordid"
     */
    public $URLParameterArray;

    /**
     * The "parent" module that the records being listed are linked to.
     *
     * @var string
     */
    public $LinkModule;

    /**
     * Message to display if no rows are found.
     *
     * @var string
     */
    public $EmptyMessage = 'No records found.';

    /** If set with a string value, then delete links will appear */
    public $deleteAction = false;

    /**
     * String to add to the end of the url for a listed record when constructing the link.
     *
     * @param $RecordList
     * @param $ListingDesign
     * @param $LinkModule
     * @param bool $ReadOnly
     *
     * @var string
     */
    public $RecordURLSuffix;

    /**
     * The direction in which this listing is sorted.
     *
     * @var string
     */
    private $_direction = 'ASC';

    /**
     * The column name the listing is being sorted by.
     *
     * @var string
     */
    private $_sortField;

    /**
     * The value of the action query parameter to be used when the listed is sorted.
     *
     * @var string
     */
    private $_sortAction;

    public function __construct($RecordList, $ListingDesign, $LinkModule = '', $ReadOnly = false)
    {
        $this->RecordList = $RecordList;
        $this->ListingDesign = $ListingDesign;
        $this->LinkModule = $LinkModule;
        $this->ReadOnly = $ReadOnly;
        $this->EmptyMessage = _fdtk('no_records_found');

        // If the user doesn't have access to the listed records, the listing should be readonly.
        if ($this->ListingDesign->Module && $this->ListingDesign->Module != 'USE' && !GetAccessLevel($this->ListingDesign->Module)) {
            $this->ReadOnly = true;
        }

        if (!is_bool($this->ReadOnly)) {
            throw new \InvalidArgumentException('ReadOnly must be a Boolean value');
        }
    }

    public function getColumns()
    {
        if (!empty($this->ListingDesign->Columns)) {
            return $this->ListingDesign->getColumnObjects();
        }

        if (!empty($this->RecordList->Columns)) {
            return $this->RecordList->Columns;
        }

        return [];
    }

    public function setEmptyMessage($message)
    {
        $this->EmptyMessage = $message;
    }

    /**
     * @desc Top level function to call - generates and returns the HTML for this listing.
     */
    public function GetListingHTML()
    {
        if (count($this->getColumns()) == 0) {
            return '<div class="padded_div">' . _fdtk('no_column_listing') . '</div>';
        }

        if (count($this->RecordList->Records) == 0) {
            return '<div class="padding_bot windowbg2">' . $this->EmptyMessage . '</div>';
        }

        $HTML = '<table class="dtx-table">';

        $HTML .= $this->getHeaderRowHTML();

        $HTML .= $this->getDataRowsHTML();

        $HTML .= '</table>';

        // Add event to delete links, prompting user, are they sure they want to delete this?
        if ($this->deleteAction !== false) {
            $HTML .= '<script>';
            $HTML .= 'jQuery(\'document\').ready(function(){';
            // Ensure that a user does not delete a record by accident
            $HTML .= 'jQuery(\'.delete\').click(function(event){';
            $HTML .= '    event.preventDefault();';
            $HTML .= '    if (confirm(\'Are you sure you want to delete this?\')) {';
            $HTML .= '        document.location = jQuery(this).attr(\'href\');';
            $HTML .= '    }';
            $HTML .= '})';
            $HTML .= '});';
            $HTML .= '</script>';
        }

        return $HTML;
    }

    /**
     * @desc Constructs the url to link the user to a particular record referenced in this listing.
     *
     * @param array $datarow Array of data from this record
     *
     * @return string the url of the record
     */
    public function getRecordUrl(array $datarow): string
    {
        global $ModuleDefs, $scripturl;

        if (isset($datarow['module']) && $datarow['module'] === Module::CONTACTS) {
            $carltonLinkProvider = (new InlineListingLinkUrlProviderFactory())->createFromModule(Module::CONTACTS);

            return $carltonLinkProvider->getContactUrlToEditFrom($datarow['recordid']);
        }

        if (!empty($this->URLParameterArray)) {
            $ParamString = '';
            foreach ($this->URLParameterArray as $key => $Param) {
                if (!is_int($key)) {
                    $ParamString .= '&' . $key . '=' . $Param;
                } else {
                    $ParamString .= '&' . $Param . '=' . $this->getField($datarow, $Param);
                    if ($Param == 'frommainrecord') {
                        $ParamString .= '1';
                    }
                }
            }
        } else {
            $ParamString = '&recordid=' . $this->getField($datarow, 'recordid');
        }

        if ($this->LinkModule) { // linking to a linked record.
            if ($this->RecordList->Module == 'AST') {
                $URL = $scripturl . '?' . http_build_query([
                        'action' => 'linkequipment',
                        'module' => $this->LinkModule,
                        'main_recordid' => $this->getField($datarow, 'main_recordid'),
                        'recordid' => $this->getField($datarow, 'recordid'),
                        'link_recordid' => $this->getField($datarow, 'link_recordid'),
                    ]);
            } else {
                $URL = $scripturl . '?action=' . $ModuleDefs[$this->RecordList->Module]['ACTION'] . '&link_module=' . $this->LinkModule . '&recordid=' . $this->getField($datarow, $ModuleDefs[$this->RecordList->Module]['LINKED_MODULE']['link_recordid']) . '&main_recordid=' . $this->getField($datarow, $ModuleDefs[$this->RecordList->Module]['LINKED_MODULE']['parent_ids'][$this->LinkModule]) . '&from_parent_record=1';
            }
        } elseif ($this->RecordList->Module) {
            // create link to edit record
            $urlProvider = (new InlineListingLinkUrlProviderFactory())->createFromModule($this->RecordList->Module);
            $extraParams = [];

            if ($this->RecordList->Module == 'ACT' && $GLOBALS['FormDesigns']['linked_actions']['linked_actions'] > 0) {
                $extraParams['formId'] = $GLOBALS['FormDesigns']['linked_actions']['linked_actions'];
            }

            $newActionUrl = $urlProvider->getUrlToEditForm(
                $this->getField($datarow, 'recordid'),
                $datarow['main_recordid'] ?? '',
                $this->RecordList->Module,
                $extraParams,
            );

            $URL = $newActionUrl . (!empty($this->URLParameterArray) ? $ParamString : '');
        } elseif ($this->Service && $this->Event) {
            $URL = $scripturl . '?service=' . $this->Service . '&event=' . $this->Event . $ParamString;
        } else {
            $URL = $scripturl . '?action=' . $this->Action . $ParamString;
        }

        if ($this->RecordURLSuffix) {
            $URL .= $this->RecordURLSuffix;
        }

        return $URL;
    }

    public static function getRecordsPerPage(): int
    {
        return (int) GetParm('LISTING_DISPLAY_NUM', 20);
    }

    public function getPageNumbersHTML()
    {
        $perPage = self::getRecordsPerPage();

        if ($perPage < $this->RecordList->NumRecords) {
            $pageStart = 0;

            while ($pageStart < $this->RecordList->NumRecords) {
                $pages[] = $pageStart;
                $pageStart += $perPage;

                if (!isset($startPage) && $this->RecordList->StartAt < $pageStart) {
                    $startPage = count($pages) - 1;
                }
            }

            $URL = new URL();


            $HTML .= '<table width="100%" class="page-numbers">
                <tr>
                    <td class="windowbg" width="5%" nowrap="nowrap">';
            if ($startPage != 0) {
                $newURL = clone $URL;
                $newURL->AddToQueryString('start', $this->RecordList->StartAt - $perPage);

                $HTML .= '<a href="' . $newURL->GetURL() . '">' . _fdtk('previous_page') . '</a>';
            }
            $HTML .= '
                    </td>
                    <td class="windowbg" width="90%">';

            if ($startPage > 10) {
                $newURL = clone $URL;
                $newURL->AddToQueryString('start', $pages[$startPage - 11]);

                $HTML .= '<a href="' . $newURL->GetURL() . '">&lt;&lt;</a>';
            }

            foreach ($pages as $pagenumber => $startAt) {
                if (abs($startPage - $pagenumber) <= 10) {
                    if ($pagenumber == $startPage) {
                        $HTML .= '<span class="current-page">' . ($pagenumber + 1) . ' </span>';
                    } else {
                        $newURL = clone $URL;
                        $newURL->AddToQueryString('start', $startAt);

                        $HTML .= '<a href="' . $newURL->GetURL() . '">' . ($pagenumber + 1) . '</a> ';
                    }
                }
            }
            if ($startPage < count($pages) - 11) {
                $newURL = clone $URL;
                $newURL->AddToQueryString('start', $pages[$startPage + 11]);

                $HTML .= '<a href="' . $newURL->GetURL() . '">&gt;&gt;</a>';
            }
            $HTML .= '</td>';
            $HTML .= '<td class="windowbg" width="5%" nowrap="nowrap">';
            if ($startPage < count($pages) - 1) {
                $newURL = clone $URL;
                $newURL->AddToQueryString('start', $this->RecordList->StartAt + $perPage);

                $HTML .= '<a href="' . $newURL->GetURL() . '">' . _fdtk('next_page') . '</a>';
            }
            $HTML .= '
                    </td>
                </tr>';
            $HTML .= '</table>';
        }

        return $HTML;
    }

    /**
     * Sets the column name by which this listing is sorted.
     *
     * @param string $columnName The name of the field the display is being sorted by
     */
    public function setOrder($columnName)
    {
        $this->_sortField = $columnName;
    }

    /**
     * Sets the direction in which the listing has been sorted (either ASC or DESC).
     *
     * @param string $dir The direction. Either ASC or DESC
     *
     * @throws Exception if the direction is anything but ASC or DESC
     */
    public function setDirection($dir)
    {
        if (!in_array($dir, ['ASC', 'DESC'])) {
            throw new Exception('Direction needs to be either ASC or DESC');
        }

        $this->_direction = $dir;
    }

    /**
     * Sets the value of the action query parameter when the user sorts the listing.
     *
     * @param string $action The value to use for the action query parameter
     */
    public function setSortAction($action)
    {
        $this->_sortAction = $action;
    }

    /**
     * @desc Gets the HTML for a single record row in the listing.
     */
    protected function GetDataRowHTML($Record)
    {
        $HTML = '
            <tr>';

        if ($this->Checkbox) {
            $HTML .= '<td width="1%">
                    <input type="checkbox" id="checkbox_id_' . $this->CheckboxType . '" name="checkbox_include_' . $Record->Data[$this->CheckboxIDField] . '" onclick="" />
                    </td>';
        }

        foreach ($this->getColumns() as $Column) {
            $HTML .= $this->GetDataCellHTML($Record, $Column);
        }

        // options

        $recordid = $Record->Data instanceof RecordEntity ? $Record->Data->recordid : $Record->Data['recordid'] ?? 0;
        $options = $this->ListingDesign->getOptions($recordid);
        $maxNumOptions = $this->ListingDesign->getMaxNumOfOptions();

        if (!empty($options) || $maxNumOptions > 0) {
            // add extra fields depending on what options exist for other reports
            if ($maxNumOptions > count($options)) {
                // go through the options array and add a blank option for any options not present that are for other reports
                for ($i = 1; $i <= $maxNumOptions; ++$i) {
                    if (empty($options[$i])) {
                        $options[$i] = '&nbsp;';
                    }
                }
            }

            // sort the array by key high to low so that the first item generated will be the one used to set the max number of options needed
            krsort($options);

            $HTML = $this->addOptionsToHtml($options, $HTML);
        }

        if ($this->deleteAction !== false) {
            $HTML .= '<td width="5%" style="text-align:center; white-space:nowrap;"><a class="delete" href="?action=' . $this->deleteAction . '&recordid=' . $recordid . '">Delete</a></td>';
        }

        $HTML .= '</tr>';

        return $HTML;
    }

    /**
     * @desc Gets the HTML for a single cell in the listing.
     */
    protected function getDataCellHTML($Record, $PassedColumn)
    {
        $Column = clone $PassedColumn;

        $col_field = $Column->getName();
        $colTable = $Column->getTable();
        $FieldType = $Column->getFieldType();
        $hasFdrSetup = $Column->hasFdrSetup();
        $row = $Record->Data;

        if ($Column->usesControllerAction()) {
            $value = $Column->getContents($row);
        } elseif ($Column instanceof Listings_UDFListingColumn && $row instanceof src\framework\model\Entity) { // workaround while udf data is not accessible though entities.
            $value = $this->RecordList->udfData[$row->recordid][$Column->GetID()] ?? '';
        } else {
            $value = $row instanceof src\framework\model\Entity ? $row->{$col_field} : $row[$col_field] ?? null;
        }

        $Column->setValue($value);

        if ($FieldType == 'C' && (!$Column instanceof Fields_DummyField || $hasFdrSetup)) {
            $fullFieldName = $colTable . '.' . $col_field;
            $codeInfo = (new CodeInfoRetrieverFactory())->create()->retrieve($fullFieldName, $value);
        }

        if (isset($codeInfo)) {
            $Column->setDescription($codeInfo->getDescription());
            $Column->setColour($codeInfo->getCodWebColour());
        } else {
            $Column->getDisplayInfo();
        }

        $HtmlClass = ($this->_sortField && $this->_sortField == $col_field) ? 'sort' : 'wrap-text';
        $HTML = '<td class="' . $HtmlClass . '" valign="top"';

        if ($Record->RowNumber > 1) {
            $HTML .= ' rowspan="' . $Record->RowNumber . '"';
        }

        if ($Column->getColour()) {
            $HTML .= ' style="background-color:#' . $Column->getColour() . '"';
        }

        $HTML .= '>';

        if (isset($value) && !$this->ReadOnly && $FieldType != 'X') {
            $recordData = $Record->Data ?? [];
            if ($recordData instanceof Entity) {
                $recordData = $Record->Data->getVars();
            }

            $isReadOnly = $recordData['isReadOnly'] ?? false;
            if (!$isReadOnly) {
                // Encode single and double quotes
                $url = str_replace("'", '\\&#39;', $this->getRecordUrl($recordData));
                $url = str_replace('"', '\\&#34;', $url);

                $HTML .= '<a href="javascript:if(CheckChange()){SendTo(\'' . $url . '\');}" >';
            }
        }

        if ($FieldType == 'C') {
            $value = $Column->getDescription();
        }

        if ($FieldType != 'X') {
            $HTML .= CompatEscaper::encodeCharacters($this->getFormattedData($colTable, $col_field, $FieldType, $value));
        } else {
            $HTML .= $this->getFormattedData($colTable, $col_field, $FieldType, $value);
        }

        if (isset($value) && !$this->ReadOnly && $FieldType != 'X') {
            $HTML .= '</a>';
        }

        $HTML .= '</td>';

        return $HTML;
    }

    /**
     * Bit of a hack this - because I want RecordLists to consume EntityCollections as well as raw data arrays, the argument
     * passed to Listings_ListingDisplay::getRecordUrl() will be either an array or an instance of Entity.  So this method
     * accesses the desired field from this argument be it of either type.
     *
     * @param array|Entity $data the variable we're getting the field from
     * @param string $name the name of the field we want
     *
     * @return mixed
     *
     * @throws InvalidArgumentException if data is not an array or an instance of Entity
     */
    protected function getField($data, $name)
    {
        if (!is_array($data) && !($data instanceof src\framework\model\Entity)) {
            throw new \InvalidArgumentException('data must be an array or an instance of Entity');
        }

        return is_array($data) ? ($data[$name] ?? '') : ($data->{$name}) ?? '';
    }

    /**
     * @desc Gets the HTML for the main data area of the listing.
     */
    private function getDataRowsHTML(): string
    {
        $HTML = '<tbody>';
        foreach ($this->RecordList->Records as $Record) {
            try {
                $HTML .= $this->GetDataRowHTML($Record);
            } catch (Exception $e) {
                $HTML .= '<tr><td>Error loading row</td></tr>';

                continue;
            }
        }

        $HTML .= '</tbody>';

        return $HTML;
    }

    /**
     * Format the 'description of a field passed to the listing display in an appropirate way for listings.
     *
     * @param $colTable         Used for fields of type 'C' and 'T' (ff_select and multiselect)
     * @param $colField         Used for fields of type 'C' and 'T' (ff_select and multiselect)
     * @param $FieldType        Depending on the type of field, the returned data will be formatted differently
     * @param $fieldDescription The data being formatted. Note that this is the field's value passed through the getDisplayInfo() function (so i's already been chanaged in some way)
     *
     * @return string
     *
     * @throws InvalidDataException
     */
    private function getFormattedData($colTable, $colField, $FieldType, $fieldDescription)
    {
        if (!is_numeric($fieldDescription) && empty($fieldDescription) && $FieldType !== 'Y') {
            return '';
        }

        // UDF field which has already been formatted
        if (!isset($colTable)) {
            return $fieldDescription;
        }

        switch ($FieldType) {
            case 'M': // money
                return FormatMoneyVal($fieldDescription, false);
            case 'DO':
            case 'D':
                return formatDateForDisplay($fieldDescription, false);
            case self::COLUMN_TYPE_DATE_TIME:
                return formatDateForDisplay($fieldDescription, true);
            case 'PCT':
                return is_numeric($fieldDescription) ? $fieldDescription . '%' : '';
            case 'CO':
            case 'TO':
                if ($colTable === null) {
                    throw new \InvalidArgumentException('ff_select and multilistbox fields require that the field\'s table is passed as an arguement to the Fields_DummyField representing it.');
                }
                $fieldDefs = Container::get(Registry::class)->getFieldDefs();
                $fieldObj = $fieldDefs[$colTable . '.' . $colField];
                $lookupCodes = $fieldObj->getCodes();

                $fieldDescriptions = [];
                foreach (explode(' ', $fieldDescription) as $id) {
                    $fieldDescriptions[] = $lookupCodes[$id]->description; // This is the difference
                }

                return implode(', ', $fieldDescriptions);
            default:
                return $fieldDescription;
        }
    }

    /**
     * @desc Gets the HTML for the header row of the listing, containing the field labels.
     */
    private function getHeaderRowHTML()
    {
        global $FieldDefsExtra, $scripturl, $ModuleDefs, $FieldDefs;

        $HTML = '<thead><tr name="element_section_row" id="element_section_row">'; // title row

        if ($this->Checkbox) {
            $HTML .= '<th width="1%">
                <input type="checkbox" id = "check_all_checkbox" name="check_all_checkbox" onclick="ToggleCheckAll(\'checkbox_id_' . $this->CheckboxType . '\', this.checked)"/>
            </th>';
        }

        foreach ($this->getColumns() as $Column) {
            $HTML .= '<th' . ($Column->getWidth() ? ' width="' . $Column->getWidth() . '%"' : '') . '>';

            $col_field = $Column->getName();

            if ($Column->GetFieldType() != 'T' && $this->_sortField && empty($col_info['custom'])) {
                $HtmlClass = ($this->_sortField && $this->_sortField == $Column->getName()) ? 'sort ' . \UnicodeString::strtolower($this->_direction) : '';
                $sortAction = $this->_sortAction ?? Sanitize::SanitizeURL($_GET['action']);
                $sortUrl = $scripturl . '?action=' . $sortAction . '&orderby=' . $Column->getName() . '&order=' . (($this->_direction == 'ASC') ? 'DESC' : 'ASC');

                $HTML .= '<a class="' . $HtmlClass . '" href="' . $sortUrl . '">';
            }

            $columnLabel = $Column->getLabel($this->ListingDesign->Module);

            if (preg_match('/^.*\((\D*)\)/iu', $columnLabel, $result)) {
                if (isset($FieldDefsExtra[$this->ListingDesign->Module][$col_field]['SubmoduleSuffix'])) {
                    $submoduleSuffix = \UnicodeString::substr($FieldDefsExtra[$this->ListingDesign->Module][$col_field]['SubmoduleSuffix'], 0, -1);
                    $submoduleSuffix = \UnicodeString::substr($submoduleSuffix, 1);

                    if (strcmp($submoduleSuffix, $this->ListingDesign->Module) != 0) {
                        $columnLabel = str_replace($result[1], $submoduleSuffix, $columnLabel);
                    }
                } else {
                    if (strcmp($result[1], $ModuleDefs[$this->ListingDesign->Module]['NAME']) == 0) {
                        $columnLabel = str_replace($result[1], '', $columnLabel);
                        $columnLabel = \UnicodeString::substr($columnLabel, 0, -2);
                    }
                }
            }

            $HTML .= $columnLabel;

            if ($FieldDefs[$Column->getModule()][$col_field]['Type'] != 'textarea' && $this->_sortField && !$col_info['custom']) {
                $HTML .= '</a>';
            }

            $HTML .= '</th>';
        }

        // Bit of header for the delete column
        if ($this->deleteAction !== false) {
            $HTML .= '<th>&nbsp;</th>';
        }

        // add extra columns for options
        $numOptions = $this->ListingDesign->getMaxNumOfOptions();
        if ($numOptions) {
            $HTML .= '<th colspan="' . $numOptions . '">&nbsp;</th>';
        }

        $HTML .= '</tr></thead>';

        return $HTML;
    }

    private function addOptionsToHtml(array $options, string $HTML): string
    {
        if (!empty($options['error'])) {
            $HTML .= '<td colspan="3" width="15%" style="text-align:center; white-space:nowrap;">Error loading report</td>';

            return $HTML;
        }

        foreach ($options as $option) {
            $HTML .= '<td width="5%" style="text-align:center; white-space:nowrap;">' . $option . '</td>';
        }

        return $HTML;
    }
}
