<?php

use app\services\listing\ListingColumnInterface;
use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageService;
use src\system\language\LanguageSession;

/**
 * @desc Object representing a listing column (and hence really a database field).
 */
class Listings_ListingColumn extends Fields_Field implements ListingColumnInterface
{
    protected $Width;
    protected $Descending = true;

    /** @var LanguageSession */
    private $languageSession;

    /** @var LanguageService */
    private $languageService;

    public function __construct(LanguageSession $languageSession, LanguageService $languageService, $Name, $Table = '', $long = false)
    {
        $this->languageSession = $languageSession;
        $this->languageService = $languageService;

        parent::__construct($Name, $Table, $long);
    }

    public function setWidth($width)
    {
        $this->Width = $width;
    }

    public function setDescending()
    {
        $this->Descending = true;
    }

    public function setAscending()
    {
        $this->Descending = false;
    }

    public function getOrder()
    {
        if ($this->Descending) {
            return 'DESC';
        }

        return 'ASC';
    }

    public function getLabel($module = null)
    {
        $registry = Container::get(Registry::class);
        $moduleDefs = $registry->getModuleDefs();
        $fieldLabels = $registry->getFieldLabels();
        $fieldDef = $registry->getFieldDefsExtra()[$module][$this->Name];

        $fieldDomain = $this->languageService->getModuleDomain($module);
        $fieldTable = $fieldDef['Table']['DEFAULT'] ?? $fieldDef['Table'] ?? $moduleDefs[$module]->getDbReadObj();

        return $fieldLabels->getLabel($fieldTable, $this->Name, $fieldDomain, null, $fieldDef['Title']);
    }
}
