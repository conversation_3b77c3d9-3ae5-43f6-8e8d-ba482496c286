<?php

use src\system\language\LanguageServiceFactory;
use src\system\language\LanguageSessionFactory;

/**
 * Factory class for Listings_ListingColumn classes.
 */
class Listings_ListingColumnFactory
{
    /**
     * Returns the correct ListingColumn object for this module.
     *
     * @param string $field the field name
     * @param string $table the table name
     */
    public static function getColumn($field, $table = '', $long = false)
    {
        $languageService = (new LanguageServiceFactory())->create();
        $languageSession = LanguageSessionFactory::getInstance();

        if (preg_match('/^UDF_([0-9]+)$/ui', $field, $matches) != 0) {
            $column = new Listings_UDFListingColumn($matches[1]);
        } else {
            $column = new Listings_ListingColumn($languageSession, $languageService, $field, $table, $long);
        }

        return $column;
    }
}
