<?php

use app\models\document\valueObjects\LinkedRecord;
use app\services\document\DocumentDeleteServiceFactory;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\RequestOptions;
use src\helpers\SqlInClauseHelper;
use src\logger\Facade\Log;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

/**
 * @desc Object to construct and hold lists of records linked to specific modules.
 * Can be used in the same way as vanilla recordlists, but can handle a lot of processes independently
 * because the module is known.
 */
class RecordLists_ModuleRecordList extends RecordLists_RecordList
{
    /**
     * @var array
     *
     * @desc If we're storing a collection of records that do not yet support extra field data, we need an additional place to hold that data.
     * This can be removed once these entities can hold udf data
     */
    public $udfData;

    /**
     * @desc Replaces the generic getTable() method in RecordLists_RecordList with one checking for the
     * module as a fallback and using the ModuleDefs default table.
     *
     * @return string The name of a database table
     */
    public function getTable()
    {
        $moduleDefs = Container::get(ModuleDefs::class);

        if ($this->Table) {
            return $this->Table;
        }

        if ($this->Module) {
            return $moduleDefs[$this->Module]->getDbReadObj();
        }

        return '';
    }

    /**
     * @desc Deletes all records included in this list from the database. Deletes any links and any orphaned linked records.
     *
     * @param bool $DeleteDocs if true, the documents linked to this record will be deleted, otherwise the documents will remain, and only the links will be deleted
     *
     * @return array{deleted: int[], not_deleted: int[]}
     */
    public function BatchDelete($DeleteDocs = false)
    {
        global $ModuleDefs;

        $this->RetrieveRecords();

        $NoErrors = true;

        if ($DeleteDocs) {
            $documentDeleteService = (new DocumentDeleteServiceFactory())->create();
        }

        $result = [
            'deleted' => [],
            'not_deleted' => [],
        ];

        foreach ($this->Records as $Record) {
            DatixDBQuery::static_beginTransaction();

            InsertFullAuditRecord($this->Module, $Record->Data['recordid'], 'DELETE', 'WEB: Batch Delete [' . $this->Module . '] (' . $this->getTable() . ')');

            if ($ModuleDefs[$this->Module]['CAN_LINK_DOCS']) {
                if ($DeleteDocs) {
                    $record = new LinkedRecord($this->Module, $Record->Data['recordid']);
                    $documentDeleteService->delete($record);
                }
            }

            if ($NoErrors && $ModuleDefs[$this->Module]['CAN_LINK_NOTES']) {
                // Delete Notepad
                try {
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM notepad WHERE ' . $ModuleDefs[$this->Module]['FK'] . ' = ' . $Record->Data['recordid']);
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }
            }

            if ($NoErrors) {
                // Delete Actions
                try {
                    $ActionsToDelete = DatixDBQuery::PDO_fetch_all('SELECT recordid FROM ca_actions WHERE act_module = \'' . $this->Module . '\' AND act_cas_id = ' . $Record->Data['recordid'], [], PDO::FETCH_COLUMN);
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM ca_actions WHERE act_module = \'' . $this->Module . '\' AND act_cas_id = ' . $Record->Data['recordid']);
                    if (!empty($ActionsToDelete) && $NoErrors) {
                        DatixDBQuery::PDO_query('DELETE FROM ca_actions_user WHERE ' . (new SqlInClauseHelper())->inClause('action_id', $ActionsToDelete));
                        $this->deleteCarltonActions($ActionsToDelete);
                    }
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }

                if (!empty($ActionsToDelete)) {
                    foreach ($ActionsToDelete as $ActionID) {
                        InsertFullAuditRecord('ACT', $ActionID, 'DELETE', 'WEB: Batch Delete [' . $this->Module . '] (ca_actions)');
                    }
                }
            }

            if ($NoErrors && $ModuleDefs[$this->Module]['CAN_LINK_CONTACTS']) {
                // Delete contact links
                try {
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM link_contacts WHERE ' . $ModuleDefs[$this->Module]['FK'] . ' = ' . $Record->Data['recordid']);
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }
            }

            if ($NoErrors && $ModuleDefs[$this->Module]['CAN_LINK_MODULES']) {
                // Delete module links
                try {
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM link_modules WHERE ' . $ModuleDefs[$this->Module]['FK'] . ' = ' . $Record->Data['recordid']);
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }
            }

            if ($NoErrors && $ModuleDefs[$this->Module]['CAN_LINK_MODULES']) {
                // Delete extra field data
                try {
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM udf_values WHERE mod_id = :mod_id AND cas_id = :recordid', ['recordid' => $Record->Data['recordid'], 'mod_id' => ModuleCodeToID($this->Module)]);
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }
            }

            if ($NoErrors) {
                // Delete main record
                try {
                    $NoErrors = DatixDBQuery::PDO_query('DELETE FROM ' . $this->getTable() . ' WHERE recordid = ' . $Record->Data['recordid']);
                } catch (DatixDBQueryException $e) {
                    $NoErrors = false;
                }
            }

            // If we've had any errors, then don't commit this deletion, and mark the record as one that hasn't been deleted.
            // Otherwise commit the change.
            if ($NoErrors) {
                DatixDBQuery::static_commit();
                $result['deleted'][] = $Record->Data['recordid'];
            } else {
                DatixDBQuery::static_rollBack();
                $result['not_deleted'][] = $Record->Data['recordid'];
            }
        }

        return $result;
    }

    /**
     * @param int[] $actions
     */
    private function deleteCarltonActions(array $actions): void
    {
        /** @var Client $httpClient */
        $httpClient = Container::get('http_client.carlton');

        try {
            $httpClient->request('POST', 'actions/batch-delete', [
                RequestOptions::JSON => [
                    'actionIds' => $actions,
                ],
            ]);
        } catch (RequestException $e) {
            Log::error('Actions batch delete API error', [
                'exception' => $e,
            ]);
        }
    }
}
