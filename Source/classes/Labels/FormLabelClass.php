<?php

use src\framework\registry\Registry;
use src\system\container\facade\Container;
use src\system\language\LanguageService;
use src\system\language\LanguageSession;

/**
 * @desc Object representing a Form label
 */
class Labels_FormLabel
{
    protected $LabelText;

    public function __construct($FieldName, $FormFieldName, $Module, $table = '', $useFormDesignLanguage = false)
    {
        $languageService = Container::get(LanguageService::class);
        $domain = $languageService->getModuleDomain($Module);

        // set the field label to the system-wide default
        $tmpLabel = Container::get(Registry::class)->getFieldLabels()->getLabel($table, $FieldName, $domain, null, '', $useFormDesignLanguage);

        // override with the form design label, if set
        $tmpLabel = $this->getFormLabel($FieldName, $FormFieldName) ?: $tmpLabel;

        if (!empty($tmpLabel)) {
            $this->setLabel($tmpLabel);
        }
    }

    /**
     * Retrieves the LabelText set.
     *
     * @return string
     */
    public function getLabel()
    {
        return $this->LabelText;
    }

    /**
     * Sets the LabelText property.
     *
     * @param string $NewLabelText Text for field label
     */
    public function setLabel($NewLabelText)
    {
        $this->LabelText = (string) $NewLabelText;
    }

    /**
     * Check for form design label loaded into $GLOBALS["UserLabels"].
     *
     * @param string $FieldName Field name
     * @param string $FormFieldName Form field name
     */
    public static function getFormLabel($FieldName, $FormFieldName = '')
    {
        $title = $FormFieldName
            ? $GLOBALS['UserLabels'][$FormFieldName] ?? null
            : $GLOBALS['UserLabels'][$FieldName] ?? null;

        if (is_array($title)) {
            $languageSession = Container::get(LanguageSession::class);
            $language = $languageSession->getLanguage();

            $title = $title[$language] ?? null;
        }

        return $title;
    }

    /**
     * Replacement function for old GetFormFieldLabel found in subs.php, which still reamins as a wrapper function
     * for this static method.
     *
     * @param string $FieldName Field name
     * @param string $DefaultTitle Default title if no label found
     * @param string $module Module short name
     * @param string $FormFieldName Form field name
     * @param string $table
     * @param bool $useFormDesignLanguage
     */
    public static function GetFormFieldLabel($FieldName, $DefaultTitle = '', $module = '', $FormFieldName = '', $table = '', $useFormDesignLanguage = false)
    {
        if (substr($FieldName, 0, 4) === 'UDF_') {
            $fieldNameArray = explode('_', $FieldName);
            $fieldId = $fieldNameArray[3];
            $groupId = $fieldNameArray[2];
            $ExtraField = new \Fields_ExtraField($fieldId, $groupId);
            $Label = $ExtraField->getLabel();
        } else {
            $FormLabelObj = new self($FieldName, $FormFieldName, $module, $table, $useFormDesignLanguage);
            $Label = $FormLabelObj->getLabel();
        }

        return $Label ?: $DefaultTitle;
    }
}
