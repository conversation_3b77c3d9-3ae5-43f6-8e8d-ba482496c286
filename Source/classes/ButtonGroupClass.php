<?php

use app\services\records\RecordsFactory;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @desc Models a group of buttons, as you might see at the bottom of a form.
 */
class ButtonGroup
{
    public const ACTION_SAVE = 'SAVE';
    public const ACTION_CANCEL = 'CANCEL';

    // Array of Button{} objects representing the buttons in this group
    protected $Buttons = [];

    /**
     * Button objects used for Next/Previous links.
     *
     * @var array
     */
    protected $NavigationButtons = [];

    /**
     * @desc Used to add a button object to the Button array
     *
     * @param array $Parameters array of parameters to use when creating the new button object
     */
    public function AddButton($Parameters)
    {
        $this->Buttons[] = new Button($Parameters);
    }

    /**
     * @desc Checks whether navigation buttons exist - needed to determine height of the toolbar.
     *
     * @return bool
     */
    public function NavigationButtonsExist()
    {
        return !empty($this->NavigationButtons);
    }

    /**
     * @desc Used to add navigation buttons (First/Previous/Next/Last) to the object.
     *
     * @param RecordLists_RecordList $RecordList list of records to model
     */
    public function AddNavigationButtons($RecordList, $recordid, $module = '')
    {
        global $ModuleDefs;

        $CurrentRecordIndex = $RecordList->getRecordIndex(['recordid' => $recordid]);

        if ($CurrentRecordIndex !== false) {
            require_once 'StepButton.php';

            $href = 'javascript:'
                . 'if(CheckChange()){'
                    . 'UnlockRecord('
                        . "'" . $ModuleDefs[$module]['TABLE'] . "', "
                        . $recordid
                    . ');'
                    . 'SendTo('
                        . "'"
                            . getRecordURL(['module' => $module, 'recordid' => $RecordList->getRecordidByIndex(0)])
                            . (!empty($_GET['from_parent_record']) ? '&from_parent_record=1' : '')
                            . (!empty($_REQUEST['fromsearch']) ? '&fromsearch=1' : '')
                            . (!empty($_GET['from_report']) ? '&from_report=1' : '')
                        . "'"
                    . ');'
                . '}';
            $this->NavigationButtons[] = new StepButton(
                $href,
                [
                    'label' => _fdtk('btn_first'),
                    'action' => 'first',
                    'module' => $module,
                    'recordid' => $recordid,
                ],
                ($CurrentRecordIndex != 0) ? true : false,
            );

            $PreviousRecordid = $RecordList->getRecordidByIndex($CurrentRecordIndex - 1);
            $href = 'javascript:'
                . 'if(CheckChange()){'
                    . 'UnlockRecord('
                        . "'" . $ModuleDefs[$module]['TABLE'] . "', "
                        . $recordid
                    . ');'
                    . 'SendTo('
                        . "'"
                            . getRecordURL(['module' => $module, 'recordid' => $PreviousRecordid])
                            . (!empty($_GET['from_parent_record']) ? '&from_parent_record=1' : '')
                            . (!empty($_REQUEST['fromsearch']) ? '&fromsearch=1' : '')
                            . (!empty($_GET['from_report']) ? '&from_report=1' : '')
                        . "'"
                    . ');'
                . '}';
            $this->NavigationButtons[] = new StepButton(
                $href,
                [
                    'label' => _fdtk('btn_previous'),
                    'action' => 'prev',
                    'module' => $module,
                    'recordid' => $recordid,
                ],
                (isset($PreviousRecordid)) ? true : false,
            );

            $NextRecordid = $RecordList->getRecordidByIndex($CurrentRecordIndex + 1);
            $href = 'javascript:'
                . 'if(CheckChange()){'
                    . 'UnlockRecord('
                        . "'" . $ModuleDefs[$module]['TABLE'] . "', "
                        . $recordid
                    . ');'
                    . 'SendTo('
                        . "'"
                            . getRecordURL(['module' => $module, 'recordid' => $NextRecordid])
                            . (!empty($_GET['from_parent_record']) ? '&from_parent_record=1' : '')
                            . (!empty($_REQUEST['fromsearch']) ? '&fromsearch=1' : '')
                            . (!empty($_GET['from_report']) ? '&from_report=1' : '')
                        . "'"
                    . ');'
                . '}';
            $this->NavigationButtons[] = new StepButton(
                $href,
                [
                    'label' => _fdtk('btn_next'),
                    'action' => 'next',
                    'module' => $module,
                    'recordid' => $recordid,
                ],
                (isset($NextRecordid)) ? true : false,
            );

            $href = 'javascript:if(CheckChange()){'
                . "UnlockRecord('"
                    . $ModuleDefs[$module]['TABLE'] . "', "
                    . $recordid
                . ');'
                . "SendTo('"
                    . getRecordURL(['module' => $module, 'recordid' => $RecordList->getRecordidByIndex(count($RecordList->Records) - 1)])
                    . (!empty($_GET['from_parent_record']) ? '&from_parent_record=1' : '')
                    . (!empty($_REQUEST['fromsearch']) ? '&fromsearch=1' : '')
                    . (!empty($_GET['from_report']) ? '&from_report=1' : '') . "'"
                . ');'
            . '}';
            $this->NavigationButtons[] = new StepButton(
                $href,
                [
                    'label' => _fdtk('btn_last'),
                    'action' => 'last',
                    'module' => $module,
                    'recordid' => $recordid,
                ],
                ($CurrentRecordIndex != count($RecordList->Records) - 1) ? true : false,
            );
        }
    }

    /**
     * @desc Gets the HTML for a div containing HTML button elements. Used at the bottom of forms.
     *
     * @return string the HTML for a div containing the buttons from this group
     */
    public function getHTML()
    {
        $HTML = '<div class="dtx-button-wrapper">';

        foreach ($this->Buttons as $Button) {
            $HTML .= $Button->getHTML();
        }

        $HTML .= '</div>';

        if (!empty($this->NavigationButtons)) {
            $HTML .= '<div class="stepped_wrapper"><ul class="stepped">';

            foreach ($this->NavigationButtons as $Button) {
                $HTML .= '<li>' . $Button->getHTML() . '</li>';
            }

            $HTML .= '</ul></div>
            <script type="text/javascript">
            jQuery(document).ready(function()
            {
                jQuery(".stepped > li > a > img").hover(
                    function()
                    {
                        jQuery(this).css("border", "1px solid #828282");
                        if (jQuery(this).closest("li").next().length)
                        {
                            jQuery(this).closest("li").next().find("img").css("border-left", "none");
                        }
                        if (jQuery(this).closest("li").prev().length)
                        {
                            jQuery(this).closest("li").prev().find("img").css("border-right", "none");
                        }
                    },
                    function()
                    {
                        jQuery(this).css("border", "1px solid #bfbebe");
                    }
                );
            });
            </script>';
        }

        return $HTML;
    }

    /**
     * @desc Gets the HTML for a collection of buttons represented by clickable images. Used in the menu floating to the left of forms.
     *
     * @return string the HTML for a set of images representing the buttons in this group
     */
    public function getSideMenuHTML()
    {
        $HTML = '';
        foreach ($this->Buttons as $Button) {
            $HTML .= $Button->getSideMenuHTML();
        }

        if (!empty($this->NavigationButtons)) {
            $HTML .= '<span style="margin-left: 10px;">';

            foreach ($this->NavigationButtons as $Button) {
                $HTML .= $Button->getSideMenuHTML();
            }

            $HTML .= '</span>';
        }

        return $HTML;
    }

    /**
     * @desc Generic function used to create a standard button group for standard forms based on the formtype and module. Returns nothing, but
     * populates the Buttons array for this object.
     *
     * @param array $aParams Array of parameters
     */
    public function PopulateWithStandardFormButtons($aParams)
    {
        global $ModuleDefs, $scripturl;

        $registry = Container::get(Registry::class);

        $FormIdentifier = ($aParams['form_id'] ? '\'#' . $aParams['form_id'] . '\'' : 'document.forms[0]');

        $module = $aParams['module'];
        $aParams['perms'] = $registry->getParm($ModuleDefs[$module]['PERM_GLOBAL'], '')->toScalar();
        $statusCode = $aParams['data']['rep_approved'];

        $recordsService = (new RecordsFactory())->create();
        $allowsStatusEdit = $recordsService->allowsReadonlyApprovalStatusEditing($module, $statusCode);
        $rejectedStatus = $statusCode == 'REJECT';

        if (($rejectedStatus || $allowsStatusEdit)
            && $aParams['formtype'] != 'Search'
            && (CanEditRecord($aParams) || CanMoveRecord($aParams))) {
            // not an ideal solution, but some proper work needs to be done on re-structuring the
            // formtype/formmode options available, since they are not currently particularly useful.
            $aParams['formtype'] = 'Edit';
        }

        switch ($aParams['formtype']) {
            case 'ReadOnly':
            case 'Locked':
                if ($ModuleDefs[$aParams['module']]['LINKED_MODULE']['parent_ids']) {
                    $this->AddButton([
                        'label' => _fdtk('back_to') . ' ' . $ModuleDefs[$aParams['link_module']]['REC_NAME'],
                        'name' => 'btnCancel',
                        'id' => 'btnCancel',
                        'onclick' => 'SendTo(\'' . getRecordURL(['module' => $aParams['link_module'], 'recordid' => $aParams['main_recordid'], 'panel' => $ModuleDefs[$aParams['module']]['LINKED_MODULE']['panel']]) . '\')',
                        'action' => 'BACK',
                        'class' => 'button-clear',
                    ]);
                }
                $this->AddButton([
                    'label' => _fdtk('form_cancel'),
                    'name' => 'btnCancel',
                    'id' => 'btnCancel',
                    'onclick' => 'submitClicked=true;jQuery(\'#rbWhat\').val(\'Cancel\');jQuery(' . $FormIdentifier . ').submit();',
                    'action' => 'CANCEL',
                    'class' => 'button-clear',
                ]);

                break;
            case 'Search':
                $this->AddButton([
                    'label' => _fdtk('form_cancel'),
                    'name' => 'btnCancel',
                    'id' => 'btnCancel',
                    'onclick' => 'submitClicked=true;jQuery(\'#rbWhat\').val(\'Cancel\');jQuery(' . $FormIdentifier . ').submit();',
                    'action' => 'CANCEL',
                    'class' => 'button-clear',
                ]);
                $this->AddButton([
                    'label' => _fdtk('form_search'),
                    'name' => 'btnSearch',
                    'id' => 'btnSearch',
                    'onclick' => 'submitClicked=true;selectAllMultiCodes();jQuery(\'#rbWhat\').val(\'Search\');jQuery(' . $FormIdentifier . ').submit();',
                    'action' => 'SEARCH',
                    'class' => 'button-primary',
                ]);

                break;
            case 'Print': // no buttons
                break;
            default:
                $recordSubmitDuration = (
                    $aParams['formtype'] == 'New'
                    && !empty($ModuleDefs[$aParams['module']]['RECORD_SUBMIT_DURATION'])
                ) ? 'true' : 'false';

                if (!empty($ModuleDefs[$aParams['module']]['LINKED_MODULE']['parent_ids'])) {
                    if (isset($_GET['from_parent_record']) && $_GET['from_parent_record'] === '1') {
                        $this->AddButton([
                            'label' => _fdtk('form_cancel'),
                            'name' => 'btnCancel',
                            'id' => 'btnCancel',
                            'onclick' => 'if(confirm(\'Press \\\'OK\\\' to confirm or \\\'Cancel\\\' to return to the form\')){SendTo(\'' . getRecordURL(['module' => $aParams['link_module'], 'recordid' => $aParams['main_recordid'], 'panel' => $ModuleDefs[$aParams['module']]['LINKED_MODULE']['panel']]) . '\')}',
                            'action' => 'CANCEL',
                            'class' => 'button-clear',
                        ]);
                    } else {
                        if ($this->hasKey($aParams, 'query_string')) {
                            $queryString = http_build_query($aParams['cancel']['query_string']);
                            $this->AddButton([
                                'label' => _fdtk('form_cancel'),
                                'name' => 'btnCancel',
                                'id' => 'btnCancel',
                                'onclick' => 'if(confirm(\'Press \\\'OK\\\' to confirm or \\\'Cancel\\\' to return to the form\')){SendTo(\'' . $scripturl . "?{$queryString}" . '\')}',
                                'action' => 'CANCEL',
                                'class' => 'button-clear',
                            ]);
                        } else {
                            $this->AddButton([
                                'label' => _fdtk('form_cancel'),
                                'name' => 'btnCancel',
                                'id' => 'btnCancel',
                                'onclick' => 'if(confirm(\'Press \\\'OK\\\' to confirm or \\\'Cancel\\\' to return to the form\')){SendTo(\'' . $scripturl . '?action=list&module=' . $aParams['module'] . '&listtype=search\')}',
                                'action' => 'CANCEL',
                                'class' => 'button-clear',
                            ]);
                        }
                    }

                    if (CanDeleteRecord($aParams['module']) && $aParams['formtype'] != 'New') {
                        $this->AddButton([
                            'label' => 'Delete ' . $ModuleDefs[$aParams['module']]['REC_NAME'],
                            'name' => 'btnDelete',
                            'id' => 'btnDelete',
                            'onclick' => 'if(confirm(\'Are you sure you want to delete this record?\')){SendTo(\'' . $scripturl . '?action=deleterecord&module=' . $aParams['module'] . '&recordid=' . $aParams['data']['recordid'] . '&link_module=' . $aParams['link_module'] . '&main_recordid=' . $aParams['main_recordid'] . '\')}',
                            'action' => 'DELETE',
                            'class' => 'button-warning',
                        ]);
                    }
                    $this->AddButton([
                        'label' => _fdtk('btn_go_to') . $ModuleDefs[$aParams['link_module']]['REC_NAME'],
                        'name' => 'btnGoTo',
                        'id' => 'btnGoTo',
                        'onclick' => 'javascript:if(CheckChange()){SendTo(\'' . getRecordURL(['module' => $aParams['link_module'], 'recordid' => $aParams['main_recordid'], 'panel' => $ModuleDefs[$aParams['module']]['LINKED_MODULE']['panel']]) . '\');}',
                        'action' => 'BACK',
                        'class' => 'button-clear',
                    ]);
                } else {
                    $this->AddButton([
                        'label' => _fdtk('form_cancel'),
                        'name' => 'btnCancel',
                        'id' => 'btnCancel',
                        'onclick' => getConfirmCancelJavascript($aParams['form_id']),
                        'action' => 'CANCEL',
                        'class' => 'button-clear',
                    ]);
                }

                if (!bYN(GetParm('DIF_1_NO_PRINT')) && !$_SESSION['logged_in'] && !$registry->getDeviceDetector()->isTablet() && !$ModuleDefs[$aParams['module']]['NO_SUBMIT_AND_PRINT']) {
                    $this->AddButton([
                        'label' => _fdtk('form_submit_print'),
                        'name' => 'btnSubmitPrint',
                        'id' => 'btnSubmitPrint',
                        'onclick' => 'submitDtxForm(' . $recordSubmitDuration . ',' . $FormIdentifier . ', true);',
                        'action' => 'SAVE',
                        'class' => 'button-primary',
                    ]);
                }

                $this->AddButton([
                    'label' => self::GetSaveLabel($aParams),
                    'name' => 'btnSave',
                    'id' => 'btnSave',
                    'onclick' => 'submitDtxForm(' . $recordSubmitDuration . ',' . $FormIdentifier . ');',
                    'action' => 'SAVE',
                    'class' => 'button-primary',
                ]);

                break;
        }

        if (!empty($_GET['fromlisting'])) {
            $this->AddButton([
                'label' => _fdtk('btn_back_to_report'),
                'name' => 'btnBack',
                'id' => 'btnBack',
                'onclick' => 'submitClicked=true;selectAllMultiCodes();jQuery(\'#rbWhat\').val(\'BackToListing\');jQuery(' . $FormIdentifier . ').submit();',
                'action' => 'BACK',
                'class' => 'button-clear',
            ]);
        }
    }

    public static function GetSaveLabel($Parameters)
    {
        if ($Parameters['level'] == 1) {
            $SaveLabel = _fdtk('form_submit');
        } else {
            $SaveLabel = _fdtk('btn_save');
        }

        if (isset($Parameters['formname'])) {
            return $SaveLabel . ' ' . $Parameters['formname'];
        }

        return $SaveLabel;
    }

    // This returns a JS function that can dynamically change the label of the submit button for dif1 forms
    public static function GetSaveLabelJS(string $formID, bool $nextStatusSTLC): string
    {
        $SAVE_LABEL = _fdtk('btn_save');
        $SUBMIT_LABEL = self::GetSaveLabel([
            'level' => 1,
        ]);
        $stringNextStatusSTCL = $nextStatusSTLC ? 'true' : 'false';

        return '
        jQuery(document).ready(function()
        {
            function updateSubmitBtnLabel() {
                var $approvalStatusInput = $("' . $formID . ' #rep_approved_row #rep_approved").val();
                var isSubmitButton = $approvalStatusInput && $approvalStatusInput !== "STCL";
                if ($approvalStatusInput) {
                    var buttonLabel = isSubmitButton ? "' . $SUBMIT_LABEL . '" : "' . $SAVE_LABEL . '";
                }
                else if (' . $stringNextStatusSTCL . ') {
                    var buttonLabel = "' . $SAVE_LABEL . '"
                }
                else {
                    var buttonLabel = "' . $SUBMIT_LABEL . '"
                }
                $("' . $formID . ' .dtx-button-wrapper #btnSave").text(buttonLabel);
                $("#site-bottom-bar-content #icon_link_btnSave").prop("title", buttonLabel);
            }
            updateSubmitBtnLabel();
            var $approvalStatusInput = $("' . $formID . ' #rep_approved_row input[data-field-id=\'rep_approved\']");
            $approvalStatusInput.change(updateSubmitBtnLabel);
        });';
    }

    private function hasKey(array $array, $key)
    {
        if (array_key_exists($key, $array)) {
            return true;
        }
        foreach ($array as $k => $v) {
            if (!is_array($v)) {
                continue;
            }
            if (array_key_exists($key, $v)) {
                return true;
            }
        }

        return false;
    }
}
