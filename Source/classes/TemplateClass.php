<?php


/**
 * @desc Models a template used to output HTML. At the moment a lot of functionality is still
 * handled procedurally, but it can be brought into this class over time.
 */
class Template
{
    // bool: True if no menu div should be drawn
    protected $noMenu = false;

    // bool: True if no padding required in the main area of the screen.
    protected $noPadding = false;

    // bool: True if no header div should be drawn
    protected $noHeader = false;

    // bool: True if no title div should be drawn
    protected $noTitle = false;

    /**
     * @desc Constructor: Assigns parameter values to object properties.
     *
     * @param array $Parameters the array of parameters to be assigned
     */
    public function __construct($Parameters = [])
    {
        if ($Parameters['noMenu'] ?? false) {
            $this->noMenu = true;
        }
        if ($Parameters['noPadding'] ?? false) {
            $this->noPadding = true;
        }
        if ($Parameters['noHeader'] ?? false) {
            $this->noHeader = true;
        }
        if ($Parameters['noTitle'] ?? false) {
            $this->noTitle = true;
        }
    }

    /**
     * @return bool
     */
    public function hasMenu()
    {
        return !$this->noMenu;
    }

    /**
     * @return bool
     */
    public function hasPadding()
    {
        return !$this->noPadding;
    }

    /**
     * @return bool
     */
    public function hasHeader()
    {
        return !$this->noHeader;
    }

    public function hasTitle()
    {
        return !$this->noTitle;
    }
}
