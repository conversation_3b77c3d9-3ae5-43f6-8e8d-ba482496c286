<?php

use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Source\classes\Exceptions\SanitizationException;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

/**
 * @desc Class which can be used for sanitizing data for security checks etc.
 */
class Sanitize
{
    /**
     * Static method to check if data is numeric and returns the data cast as an int.
     *
     * @param int $IntToCheck Data to cast as int
     *
     * return int
     */
    public static function SanitizeInt($IntToCheck)
    {
        return is_numeric($IntToCheck) ? (int) $IntToCheck : '';
    }

    public static function IntOrDefault(?string $intToCheck, int $default): int
    {
        if ($intToCheck === null) {
            return $default;
        }

        return ctype_digit($intToCheck) ? (int) $intToCheck : $default;
    }

    /**
     * @throws SanitizationException
     */
    public static function IntOrThrow(string $intToCheck): int
    {
        if (ctype_digit($intToCheck)) {
            return (int) $intToCheck;
        }

        throw new SanitizationException();
    }

    /**
     * Wrapper function for htmlentities, but always encodes once.
     *
     * @param string $string the input string
     * @param int $quote_style quote_style Constant
     * @param string $charset
     */
    public static function htmlentities_once($string, $quote_style = ENT_COMPAT, $charset = 'UTF-8')
    {
        return CompatEscaper::encodeCharacters($string, $quote_style, $charset, false);
    }

    /**
     * Sanitize function for FilePaths. (In dev. Extra logic can be added).
     *
     * @param string $File file path to sanitize
     *
     * @return string File path
     */
    public static function SanitizeFilePath($File)
    {
        return filter_var($File, FILTER_SANITIZE_URL);
    }

    /**
     * Sanitize function for strings
     * Strip tags, optionally strip or encode special characters.
     *
     * @param ?string|mixed $string data to sanitize
     */
    public static function SanitizeString($string): ?string
    {
        if ($string === null) {
            return null;
        }
        if (!is_string($string)) {
            $string = (string) $string;
        }

        return htmlspecialchars($string, ENT_NOQUOTES | ENT_SUBSTITUTE);
    }

    public static function sanitizeBool(?string $string): ?bool
    {
        if ($string === null) {
            return null;
        }

        return filter_var($string, FILTER_VALIDATE_BOOLEAN);
    }

    /**
     * Sanitize function for string arrays.
     *
     * @deprecated due to self::SanitizeString() deprecation
     *
     * @param array $StringArray array data to sanitize
     *
     * @return mixed Sanitized array
     */
    public static function SanitizeStringArray($StringArray)
    {
        if (!is_iterable($StringArray)) {
            return $StringArray;
        }

        foreach ($StringArray as $key => $val) {
            if (is_array($val)) {
                $StringArray[$key] = self::SanitizeStringArray($val);
            } else {
                $StringArray[$key] = self::SanitizeString($val);
            }
        }

        return $StringArray;
    }

    public static function getModule($moduleName)
    {
        $modules = GetModuleCodeIdList();
        $modules = array_flip($modules);

        $key = array_search($moduleName, $modules);
        if ($key !== false) {
            return $modules[$key];
        }
    }

    /**
     * Sanitize function for Email addresses.
     * Remove all characters except letters, digits and !#$%&'*+-/=?^_`{|}~@.[].
     *
     * @param string $EmailAddress E-mail address to sanitize
     *
     * @return string E-mail address
     */
    public static function SanitizeEmail($EmailAddress)
    {
        // FIXME: I believe this method should be refactored to throw an error
        // when the email is not valid (or contains non-valid characters), instead
        // of simply removing those characters as it currently does

        return filter_var($EmailAddress, FILTER_SANITIZE_EMAIL);
    }

    /**
     * Sanitize function for URL.
     * Remove all characters except letters, digits and $-_.+!*'(),{}|\\^~[]`<>#%";/?:@&=.
     *
     * @param string $Url to sanitize
     *
     * @return string $Url
     */
    public static function SanitizeURL($Url)
    {
        // FIXME: I believe this method should be refactored to throw an error
        // when the email is not valid (or contains non-valid characters), instead
        // of simply removing those characters as it currently does

        return filter_var($Url, FILTER_SANITIZE_URL);
    }

    /**
     * @psalm-taint-escape html
     *
     * @throws NotFoundExceptionInterface if HTMLPurifier_Config was not found
     * @throws ContainerExceptionInterface if there is some other exception fetching an instance of HTMLPurifier_Config
     */
    public static function SanitizeHtml(string $taintedHtml): string
    {
        $config = Container::get(HTMLPurifier_Config::class);
        $purifier = new HTMLPurifier($config);

        return $purifier->purify($taintedHtml);
    }
}
