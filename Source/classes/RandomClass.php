<?php
/**
 * @desc Class which can be used for random data etc.
 */
class Random
{
    /**
     * Replacement for rand() using OpenSSL as your PRNG.
     *
     * @param int $min Minimum value
     * @param int $max Maximum value
     *
     * return int
     */
    public static function openssl_rand($min, $max)
    {
        $range = $max - $min;
        if ($range == 0) {
            return $min;
        }
        $length = (int) (log($range, 2) / 8) + 1;

        return $min + (hexdec(bin2hex(openssl_random_pseudo_bytes($length, $s))) % $range);
    }

    /**
     * Random number generator for non-security related code. Required because openssl_random_pseudo_bytes is extremely slow.
     *
     * @param int $min Minimum value
     * @param int $max Maximum value
     *
     * return int
     */
    public static function unsecure_rand($min, $max)
    {
        return mt_rand($min, $max);
    }
}
