<?php

/**
 * Represents a button in a stepped navigation UI element.
 */
class StepButton extends Button
{
    /**
     * Whether the button is active or disabled.
     *
     * @var bool
     */
    private $_active;

    /**
     * Destination url.
     *
     * @var string
     */
    private $_href;

    /**
     * Constructor.
     *
     * @param string href Destination url for the button
     * @param array Construction options. See parent class
     * @param bool Whether the button is active
     */
    public function __construct($href, array $params = [], $active = true)
    {
        $this->_href = $href;
        $this->_active = (bool) $active;

        parent::__construct($params);
    }

    /**
     * Returns the HTML used to render this button on a record details page.
     *
     * @return string
     */
    public function getHTML()
    {
        $return = '';

        if ($this->_active) {
            $return = '<a href="' . $this->_href . '" title="' . $this->Label . '"><img style="width:22px; height:22px;" class="' . $this->Action . '" src="images/nav_' . $this->Action . '.png" alt="' . $this->Action . ' record" /></a>';
        } else {
            $return = '<span title="' . $this->Label . ' (disabled)"><img style="width:22px; height:22px;" class="' . $this->Action . '" src="images/nav_' . $this->Action . '_disabled.png" alt="' . $this->Action . ' record (disabled)" /></span>';
        }

        return $return;
    }

    /**
     * Renders the HTML used in a floating menu.
     *
     * @return string
     */
    public function getSideMenuHTML()
    {
        if (!$this->_active) {
            return '';
        }

        $return = '<button type="button" class="prince-button button-inline" onclick="' . $this->_href . '" title="' . $this->Label . '" id="icon_link_' . $this->ID . '"><a>';
        $return .= $this->getIcon();
        $return .= '</a></button>';

        return $return;
    }

    /**
     * Returns the relative path to the icon that should be used to represent this button.
     *
     * @return string|void
     */
    public function getIcon()
    {
        if ($this->Icon) {
            return $this->Icon;
        }

        switch (\UnicodeString::strtolower($this->Action)) {
            case 'first':
                return '<span class="glyphicon glyphicon-step-backward" aria-hidden="true"></span>';
            case 'prev':
                return '<span class="glyphicon glyphicon-triangle-left" aria-hidden="true"></span>';
            case 'next':
                return '<span class="glyphicon glyphicon-triangle-right" aria-hidden="true"></span>';
            case 'last':
                return '<span class="glyphicon glyphicon-step-forward" aria-hidden="true"></span>';
        }
    }
}
