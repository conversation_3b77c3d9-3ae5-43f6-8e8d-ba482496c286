<?php

use app\models\admin\GenerateFromMappings;
use app\models\document\valueObjects\LinkedRecord;
use app\models\generic\RecordSources;
use app\models\generic\valueObjects\Module;
use app\services\copyRecord\CopyRecordLinkInterface;
use app\services\idGenerator\IncidentReferenceGenerator;
use app\services\idGenerator\RecordIdGeneratorFactory;
use app\services\records\RecordSourceServiceFactory;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\logger\Facade\Log;
use src\mortality\model\MortalityFields;
use src\system\container\facade\Container;
use src\system\moduledefs\ModuleDefs;

/**
 * Class used to copy Datix module records and associated data.
 */
class CopyRecords
{
    /** Some modules aren't required to support source_of_record, they should be referenced here */
    private const SKIP_SOURCE_OF_RECORD_FOR_THESE_MODULES = [
        Module::POLICIES,
    ];

    /** @var Registry */
    protected $registry;
    private $CopyOptions;

    /** @var array */
    private $copyActions = [];

    /**
     * @desc Constructor: Assigns parameter values to object properties.
     *
     * @param array $parameters the array of parameters to be assigned
     */
    public function __construct(array $parameters)
    {
        $this->registry = Container::get(Registry::class);
        $this->CopyOptions = new CopyOptions($parameters);
    }

    public function addCopyAction(CopyRecordLinkInterface $copyAction): void
    {
        $this->copyActions[] = $copyAction;
    }

    /**
     * @desc Selects main module record data for copying and copies other data based on options chosen.
     */
    public function CopyMainTable()
    {
        $ModuleDefs = $this->registry->getModuleDefs();

        $Module = $this->CopyOptions->Module;

        if (!empty($ModuleDefs[$Module]['ADDITIONAL_COPY_LIB'])) {
            require_once $ModuleDefs[$Module]['ADDITIONAL_COPY_LIB'];
        }

        $ModID = $ModuleDefs[$Module]['MOD_ID'];

        $sql = 'SELECT recordid, ' . implode(', ', $ModuleDefs[$Module]['FIELD_ARRAY']) . ' FROM ' . $ModuleDefs[$Module]['TABLE'];

        if (!empty($this->CopyOptions->join)) {
            $sql .= ' ' . $this->CopyOptions->join;
        }

        if (!empty($this->CopyOptions->where)) {
            $sql .= ' WHERE ' . $this->CopyOptions->where;
        }

        $RecordsToCopy = \DatixDBQuery::PDO_fetch_all($sql, $this->CopyOptions->parameters);

        $skipSourceOfRecord = in_array($Module, self::SKIP_SOURCE_OF_RECORD_FOR_THESE_MODULES, true);

        foreach ($RecordsToCopy as $data) {
            $recordSourceService = (new RecordSourceServiceFactory())->create();
            $generateFromSourceCode = $recordSourceService->getCodeForSource(RecordSources::COPY);
            if (!$skipSourceOfRecord) {
                $data['source_of_record'] = $generateFromSourceCode;
            }

            // Remove data for any excluded fields set up on the module
            $data = $this->removeExcludedFieldData($Module, $data);

            $sourceRecord = new LinkedRecord($Module, $data['recordid']);

            for ($i = 0; $i < $this->CopyOptions->copies; ++$i) {
                if (self::InsertRecords($Module, $NewRecordID, $data)) {
                    $destinationRecord = new LinkedRecord($Module, $NewRecordID);

                    foreach ($this->copyActions as $copyAction) {
                        $copyAction->copy($sourceRecord, $destinationRecord);
                    }

                    if ($this->CopyOptions->copy_extra_fields == 'Y') {
                        $this->CopyUDFData($ModID, $data['recordid'], $NewRecordID);
                    }
                    if ($this->CopyOptions->copy_actions == 'Y') {
                        $this->CopyActions($Module, $data['recordid'], $NewRecordID, $this->CopyOptions->copy_extra_fields);
                    }
                    if ($this->CopyOptions->copy_linked_contacts == 'Y') {
                        $linkIds = $this->CopyLinkedContacts($Module, $data['recordid'], $NewRecordID, $Module);
                        $this->CopyLostAndRestrictedTime($Module, $data['recordid'], $linkIds);
                        if ($Module == 'INC' || $Module == 'CLA') {
                            $this->CopyInjuries($Module, $data['recordid'], $Module, $NewRecordID, [], $linkIds);
                        }
                    }
                    if ($this->CopyOptions->copy_notepad == 'Y') {
                        $this->CopyNotepad($Module, $data['recordid'], $NewRecordID);
                    }
                    if ($this->CopyOptions->copy_link_to_master == 'Y') {
                        $this->LinkToMaster($Module, $data['recordid'], $NewRecordID, 'Linked via copy option');
                    }

                    if (!empty($ModuleDefs[$Module]['ADDITIONAL_COPY_FUNCTION_CALLS'])) {
                        foreach ($ModuleDefs[$Module]['ADDITIONAL_COPY_FUNCTION_CALLS'] as $additional_func) {
                            $additional_func($Module, $Module, $data['recordid'], $NewRecordID, $this->CopyOptions);
                        }
                    }

                    InsertFullAuditRecord($Module, $NewRecordID, 'COPY', 'Copied from record: ' . $data['recordid'] . " By: {$_SESSION['initials']}");
                }
            }
        }
    }

    /**
     * @desc Performs insert into main module table with data for each field passed as an array of values.
     *
     * @param string $Module module to insert into
     * @param int $recordid return value of new record's recordid
     * @param array $data contains values for fields for inserted record
     *
     * @return bool
     *
     * @throws \Doctrine\DBAL\Exception
     */
    public static function InsertRecords($Module, &$recordid, $data)
    {
        global $scripturl;

        $registry = Container::get(Registry::class);
        $tableDefs = $registry->getTableDefs();
        $ModuleDefs = $registry->getModuleDefs();

        $refFields = [
            Module::INCIDENTS => 'inc_ourref',
            Module::FEEDBACK => 'com_ourref',
            Module::CLAIMS => 'cla_ourref',
            Module::MORTALITY_REVIEW => MortalityFields::REFERENCE,
        ];

        $refPrefix = [
            Module::INCIDENTS => $registry->getParm('DIF_1_REF_PREFIX', '')->toScalar(),
            Module::FEEDBACK => $registry->getParm('COM_1_REF_PREFIX', '')->toScalar(),
            Module::CLAIMS => $registry->getParm('CLA_1_REF_PREFIX', '')->toScalar(),
            Module::MORTALITY_REVIEW => $registry->getParm('MOR_1_REF_PREFIX', '')->toScalar(),
        ];

        $ModuleTable = $ModuleDefs[$Module]['TABLE'];

        $currentUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->getInitials();

        if ($tableDefs[$ModuleTable]->is_identity == 1) {
            if ($data['recordid']) {
                unset($data['recordid']);
            }

            $sql = GeneratePDOInsertSQLFromArrays(
                [
                    'FieldArray' => $ModuleDefs[$Module]['FIELD_ARRAY'],
                    'DataArray' => $data,
                    'Module' => $Module,
                    'end_comma' => false,
                ],
                $PDOParams,
            );

            $sql = "INSERT INTO {$ModuleTable} " . $sql;

            $recordid = DatixDBQuery::PDO_insert($sql, $data);
        } else {
            $recordIdGenerator = (new RecordIdGeneratorFactory())->create($ModuleTable);
            $recordid = $recordIdGenerator->generateRecordId();

            $referenceMethod = $registry->getParm('CROSS_MODULE_REFERENCE', 'COPY')->toScalar();

            $recordIdGenerator->createBlankRecordWithApprovalStatus($recordid, $currentUserInitials, $data['rep_approved']);

            if ($refFields[$Module]) {
                switch ($referenceMethod) {
                    case 'NEW':
                        if ($Module === Module::INCIDENTS) {
                            $incidentReferenceGenerator = Container::get(IncidentReferenceGenerator::class);
                            $data[$refFields[$Module]] = $incidentReferenceGenerator->generateReference($recordid);

                            break;
                        }
                        $data[$refFields[$Module]] = $refPrefix[$Module] . $recordid;

                        break;
                    case 'NULL':
                        $data[$refFields[$Module]] = null;

                        break;
                    case 'COPY':
                    default:
                        // In this case we can map as normal, so just break
                        break;
                }
            }

            $data['updatedby'] = $_SESSION['initials'];
            $data['updateddate'] = date('d-M-Y H:i:s');

            // Populate 'First received' and 'Opened' dates for Complaints
            if ($Module == 'COM') {
                if (!in_array('com_dreceived', $ModuleDefs[$Module]['FIELD_ARRAY'])) {
                    $ModuleDefs[$Module]['FIELD_ARRAY'][] = 'com_dreceived';
                }

                if (!in_array('com_dopened', $ModuleDefs[$Module]['FIELD_ARRAY'])) {
                    $ModuleDefs[$Module]['FIELD_ARRAY'][] = 'com_dopened';
                }

                $data['com_dreceived'] = GetTodaysDate();
                $data['com_dopened'] = GetTodaysDate();
            }

            $sql = "UPDATE {$ModuleTable} SET ";

            $sql .= GeneratePDOSQLFromArrays([
                'FieldArray' => $ModuleDefs[$Module]['FIELD_ARRAY'],
                'DataArray' => $data,
                'Module' => $Module,
                'end_comma' => false,
            ], $PDOParams);
            $sql .= " WHERE recordid = {$recordid}";

            $result = DatixDBQuery::PDO_query($sql, $PDOParams);
        }

        if (($tableDefs[$ModuleTable]->is_identity == 1 && !$recordid) || ($tableDefs[$ModuleTable]->is_identity != 1 && !$result)) {
            $error = "An error has occurred when trying to save the record.  Please report the following to the Datix administrator: {$sql}";
        }

        if ($error != '') {
            SaveError($error);

            return false;
        }

        return true;
    }

    /**
     * @desc Copies UDF data from one module record to another.
     *
     * @param int $ModID module ID
     * @param int $SourceCasID
     * @param int $DestCasID
     *
     * @return bool
     */
    public static function CopyUDFData($ModID, $SourceCasID, $DestCasID)
    {
        $sql = "insert into UDF_VALUES (MOD_ID, cas_id, GROUP_ID, FIELD_ID, UDV_STRING, UDV_NUMBER, UDV_DATE, udv_money, udv_text)
                (
                select MOD_ID, {$DestCasID}, GROUP_ID, FIELD_ID, UDV_STRING, UDV_NUMBER, UDV_DATE, udv_money, udv_text from UDF_VALUES where MOD_ID = {$ModID} and CAS_ID = {$SourceCasID}
                )";
        $result = DatixDBQuery::PDO_query($sql);

        if (!$result) {
            return false;
        }

        return true;
    }

    /**
     * Copies Actions from one module record to another.
     *
     * @param string $Act_module module short name
     * @param $SourceCasID
     * @param $DestCasID
     * @param string $copy_extra_fields
     *
     * @return bool
     */
    public static function CopyActions($Act_module, $SourceCasID, $DestCasID, $copy_extra_fields = 'N')
    {
        $ModuleDefs = Container::get(ModuleDefs::class);
        $currentUserInitials = (new UserSessionFactory())->create()->getCurrentUser()->initials;

        $select = implode(', ', $ModuleDefs['ACT']['FIELD_ARRAY']);
        // Get record actions
        $sql = "SELECT recordid, act_chain_id, act_chain_instance, act_step_no, {$select}
                FROM ca_actions
                WHERE act_module = :act_module and act_cas_id = :act_cas_id
                ORDER BY recordid";

        $result = DatixDBQuery::PDO_fetch_all($sql, [
            'act_module' => $Act_module,
            'act_cas_id' => $SourceCasID,
        ]);

        $httpClient = Container::get('http_client.carlton');

        // Copy record actions
        foreach ($result as $data) {
            $OldActionRecordId = $data['recordid'];
            $data['act_cas_id'] = $DestCasID;

            try {
                $response = $httpClient->request('POST', "actions/action/{$OldActionRecordId}/copy", [
                    RequestOptions::FORM_PARAMS => [
                        'recordId' => $DestCasID,
                        'moduleCode' => $Act_module,
                    ],
                ]);
            } catch (GuzzleException $exception) {
                Log::error('Error when sending request to copy Action', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                    'exception' => $exception,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            try {
                $content = json_decode((string) $response->getBody(), true, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $exception) {
                Log::error('Could not decode json response while copying Action.', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                    'exception' => $exception,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            if (!isset($content['id'])) {
                Log::error('Action ID not found when copying Action.', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }
            $recordId = $content['id'];

            $data['recordid'] = $recordId;

            $InsertSql = GeneratePDOInsertSQLFromArrays(
                [
                    'FieldArray' => array_merge(
                        ['recordid', 'act_chain_id', 'act_chain_instance', 'act_step_no'],
                        $ModuleDefs[Module::ACTIONS]['FIELD_ARRAY'],
                    ),
                    'DataArray' => $data,
                    'Module' => Module::ACTIONS,
                    'Extras' => ['updatedby' => $currentUserInitials, 'updateddate' => date('d-M-Y H:i:s')],
                ],
                $PDOParams,
            );

            $sql = "INSERT INTO ca_actions {$InsertSql}";

            $db = new DatixDBQuery();
            $db->beginTransaction();
            $db->setSQL($sql);

            try {
                $result = $db->prepareAndExecute($PDOParams);
            } catch (Throwable $exception) {
                $db->rollBack();

                Log::error('Error while copying action and inserting into ca_actions', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            if (!$result) {
                $db->rollBack();

                Log::error('Error while copying action and inserting into ca_actions', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            $db->setSQL(
                "INSERT INTO ca_actions_user (action_id, assigned_by_id, assigned_to_id)
                SELECT {$recordId}, assigned_by_id, assigned_to_id FROM ca_actions_user WHERE action_id = :oldId",
            );

            try {
                $result = $db->prepareAndExecute(['oldId' => $OldActionRecordId]);
            } catch (Throwable $exception) {
                $db->rollBack();

                Log::error('Error while copying action and inserting into ca_actions_user', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            if (!$result) {
                $db->rollBack();

                Log::error('Error while copying action and inserting into ca_actions_user', [
                    'actModule' => $Act_module,
                    'sourceCasID' => $SourceCasID,
                    'destCasId' => $DestCasID,
                ]);

                fatal_error('Cannot copy Actions data');

                return false;
            }

            $db->commit();

            if ($copy_extra_fields === 'Y') {
                self::CopyUDFData(MOD_ACTIONS, $OldActionRecordId, $recordId);
            }
        }

        return true;
    }

    /**
     * Copies Notepad data from one module record to another.
     *
     * @param string $Module module short name
     * @param $SourceCasID
     * @param $DestCasID
     *
     * @return bool
     */
    public static function CopyNotepad($Module, $SourceCasID, $DestCasID)
    {
        $ModuleDefs = Container::get(ModuleDefs::class);

        $FK_field = $ModuleDefs[$Module]['FK'];

        $sql = "insert into NOTEPAD  ({$FK_field} ,REMINDER_DATE ,REMINDER_NOTE ,NOTES)
                (
                    select {$DestCasID}, REMINDER_DATE, REMINDER_NOTE, NOTES from NOTEPAD where {$FK_field} = {$SourceCasID}
                )";

        $result = DatixDBQuery::PDO_Query($sql);

        if (!$result) {
            fatal_error('Cannot copy Notepad data');

            return false;
        }

        return true;
    }

    /**
     * @desc Copies linked contacts data from one module record to another.
     *
     * @param string $module module short name
     * @param int $sourceCasID Recordid of source record
     * @param int $destCasID Recordid of destination record
     * @param int $destModule destination Module short name
     *
     * @return array|false
     */
    public static function CopyLinkedContacts($module, $sourceCasID, $destCasID, $destModule)
    {
        $moduleDefs = Container::get(ModuleDefs::class);

        $FK_field = $moduleDefs[$module]['FK'];
        $dest_FK_field = $moduleDefs[$destModule]['FK'];

        $sql = "insert into LINK_CONTACTS  ({$dest_FK_field}
          ,[CON_ID]
          ,[LINK_TYPE]
          ,[LINK_ROLE]
          ,[LINK_DEAR]
          ,[LINK_REF]
          ,[LINK_RESP]
          ,[LINK_STATUS]
          ,[LINK_MARRIAGE]
          ,[LINK_NDEPENDENTS]
          ,[LINK_AGEDEPENDENTS]
          ,[LINK_OCCUPATION]
          ,[LINK_PLAPAT]
          ,[LINK_DECEASED]
          ,[LINK_PATRELATION]
          ,[LINK_LIP]
          ,[LINK_RIDDOR]
          ,[LINK_DAYSAWAY]
          ,[LINK_IS_RIDDOR]
          ,[LINK_TREATMENT]
          ,[LINK_INJURY1]
          ,[LINK_BODYPART1]
          ,[LINK_LEGALAID]
          ,[LINK_INJURIES]
          ,[UPDATEDDATE]
          ,[UPDATEDBY]
          ,[LINK_NOTES]
          ,[link_age]
          ,[link_npsa]
          ,[link_mhact_section]
          ,[link_mhcpa]
          ,[link_npsa_role]
          ,[link_abs_start]
          ,[link_abs_end]
          ,[link_become_unconscious]
          ,[link_req_resuscitation]
          ,[link_hospital_24hours]
          ,[link_worked_alone]
          ,[link_pprop_damaged]
          ,[link_clin_factors]
          ,[link_direct_indirect]
          ,[link_injury_caused]
          ,[link_attempted_assault]
          ,[link_discomfort_caused]
          ,[link_public_disorder]
          ,[link_harassment]
          ,[link_police_pursue]
          ,[link_police_persue_reason]
          ,[link_verbal_abuse]
          ,[link_notify_progress]
          ,[link_date_admission]
          ,[link_age_band]
          ,[link_illness]
          ,[show_illness]
          ,[show_injury]
          ,[show_restricted_time]
          ,[total_lost_time]
          ,[total_restricted_time]
          ,[time_employee_began_work]
          ,[full_pay_injury_day]
          ,[salary_continued]
          ,[mmsea_relationship_to_beneficiary]
          ,[mmsea_type_of_representative]
          ,[icd_classification]
          ,[icd_diagnosis_codes]
          ,[icd_procedure_codes]
          )
          (
            select {$destCasID}
          ,[CON_ID]
          ,[LINK_TYPE]
          ,[LINK_ROLE]
          ,[LINK_DEAR]
          ,[LINK_REF]
          ,[LINK_RESP]
          ,[LINK_STATUS]
          ,[LINK_MARRIAGE]
          ,[LINK_NDEPENDENTS]
          ,[LINK_AGEDEPENDENTS]
          ,[LINK_OCCUPATION]
          ,[LINK_PLAPAT]
          ,[LINK_DECEASED]
          ,[LINK_PATRELATION]
          ,[LINK_LIP]
          ,[LINK_RIDDOR]
          ,[LINK_DAYSAWAY]
          ,[LINK_IS_RIDDOR]
          ,[LINK_TREATMENT]
          ,[LINK_INJURY1]
          ,[LINK_BODYPART1]
          ,[LINK_LEGALAID]
          ,[LINK_INJURIES]
          ,[UPDATEDDATE]
          ,[UPDATEDBY]
          ,[LINK_NOTES]
          ,[link_age]
          ,[link_npsa]
          ,[link_mhact_section]
          ,[link_mhcpa]
          ,[link_npsa_role]
          ,[link_abs_start]
          ,[link_abs_end]
          ,[link_become_unconscious]
          ,[link_req_resuscitation]
          ,[link_hospital_24hours]
          ,[link_worked_alone]
          ,[link_pprop_damaged]
          ,[link_clin_factors]
          ,[link_direct_indirect]
          ,[link_attempted_assault]
          ,[link_injury_caused]
          ,[link_discomfort_caused]
          ,[link_public_disorder]
          ,[link_harassment]
          ,[link_police_pursue]
          ,[link_police_persue_reason]
          ,[link_verbal_abuse]
          ,[link_notify_progress]
          ,[link_date_admission]
          ,[link_age_band]
          ,[link_illness]
          ,[show_illness]
          ,[show_injury]
          ,[show_restricted_time]
          ,[total_lost_time]
          ,[total_restricted_time]
          ,[time_employee_began_work]
          ,[full_pay_injury_day]
          ,[salary_continued]
          ,[mmsea_relationship_to_beneficiary]
          ,[mmsea_type_of_representative]
          ,[icd_classification]
          ,[icd_diagnosis_codes]
          ,[icd_procedure_codes]
          from LINK_CONTACTS where {$FK_field} = {$sourceCasID}
          )";

        $result = DatixDBQuery::PDO_query($sql);
        $success = $result > 0;

        $sql = "SELECT link_recordid, con_id, link_role FROM link_contacts WHERE {$dest_FK_field} = :linkRecordId";
        $linkData = DatixDBQuery::PDO_fetch_all($sql, ['linkRecordId' => $destCasID], PDO::FETCH_ASSOC);
        $linkIds = array_column($linkData, 'con_id');

        self::updateContactTypes($module, $destModule, $dest_FK_field, $destCasID);

        $reporterRole = Container::get(Registry::class)->getParm('REPORTER_ROLE', 'REP', true)->toScalar();

        foreach ($linkData as $link) {
            if ($link['link_role'] === $reporterRole) {
                $sql = 'UPDATE incidents_main
                        SET inc_repname = c.fullname, inc_rep_tel = c.con_tel1, inc_rep_email = c.con_email
                        FROM
                            (SELECT fullname, con_tel1, con_email FROM contacts_main WHERE recordid = :conid) AS c
                        WHERE
                            incidents_main.recordid = :casid';

                DatixDBQuery::PDO_query($sql, ['conid' => $link['con_id'], 'casid' => $destCasID]);
            }
        }

        if (!$success) {
            fatal_error('Cannot copy linked contacts data');

            return false;
        }

        return $linkIds;
    }

    /**
     * @desc Creates link from one module record to another.
     *
     * @param string $Module module short name
     * @param int $SourceCasID Recordid of master record
     * @param int $DestCasID Recordid of copied record
     * @param string $Link_notes link notes
     *
     * @return bool
     */
    public static function LinkToMaster($Module, $SourceCasID, $DestCasID, $Link_notes = '')
    {
        $PDOParams['lnk_modid1'] = $Module;
        $PDOParams['lnk_id1'] = $SourceCasID;
        $PDOParams['lnk_modid2'] = $Module;
        $PDOParams['lnk_id2'] = $DestCasID;
        $PDOParams['link_notes'] = $Link_notes;

        $sql = 'INSERT INTO links (lnk_mod1 ,lnk_id1 , lnk_mod2 , lnk_id2, link_notes)
                    VALUES (:lnk_modid1, :lnk_id1, :lnk_modid2, :lnk_id2, :link_notes)';

        $result = DatixDBQuery::PDO_Query($sql, $PDOParams);

        if (!$result) {
            fatal_error('Cannot create link to master record');

            return false;
        }

        return true;
    }

    /**
     * Creates link from one module record to another record in a different module.
     *
     * @param string $Module module short name
     * @param int $SourceCasID Recordid of master record
     * @param $DestModule
     * @param int $DestCasID Recordid of copied record
     * @param string $Link_notes link notes
     *
     * @return bool
     */
    public static function LinkToMasterCrossModule($Module, $SourceCasID, $DestModule, $DestCasID, $Link_notes = '')
    {
        $ModuleDefs = Container::get(ModuleDefs::class);

        $FK_field = $ModuleDefs[$Module]['FK'];
        $Dest_FK_field = $ModuleDefs[$DestModule]['FK'];

        $PDOParams['FK_field'] = $SourceCasID;
        $PDOParams['Dest_FK_field'] = $DestCasID;
        $PDOParams['link_notes'] = $Link_notes;

        $sql = "INSERT INTO link_modules ({$FK_field}, {$Dest_FK_field}, link_notes)
                    VALUES (:FK_field, :Dest_FK_field, :link_notes)";

        $result = DatixDBQuery::PDO_Query($sql, $PDOParams);

        if (!$result) {
            fatal_error('Cannot create cross module link to master record');

            return false;
        }

        return true;
    }

    /**
     * @desc Copies injury links from one incident record to another.
     *
     * @param int $sourceRecordId
     * @param int $destinationRecordId
     *
     * @return bool
     */
    public static function CopyInjuries($sourceModule, $sourceRecordId, $destinationModule, $destinationRecordId, $injuriesToCopy, $linkIds)
    {
        $ModuleDefs = Container::get(ModuleDefs::class);
        $sourceFKField = $ModuleDefs[$sourceModule]['FK'];
        $injuries = [];
        $InjuriesFieldArrayForLinkedInjuries = ['link_id', 'injury', 'bodypart', 'listorder', 'death_result_injury', 'permanent_impairment_percentage'];
        $InjuriesFieldArrayForIncidents = ['recordid', 'inc_id', 'con_id', 'inc_injury', 'inc_bodypart', 'listorder', 'death_result_injury', 'permanent_impairment_percentage'];

        $getContactLinksSql = "
                SELECT con_id, link_recordid
                FROM link_contacts
                WHERE {$sourceFKField} = :{$sourceFKField}";

        $linkedContacts = DatixDBQuery::PDO_fetch_all($getContactLinksSql, [":{$sourceFKField}" => $sourceRecordId]);

        // Get Injury records for linked contacts
        if ($sourceModule == 'INC') {
            $sql = '
                SELECT ' . implode(', ', $InjuriesFieldArrayForIncidents) . '
                FROM inc_injuries
                WHERE inc_id = :inc_id';

            $injuriesRawData = DatixDBQuery::PDO_fetch_all($sql, ['inc_id' => $sourceRecordId]);

            if ($destinationModule == 'INC') {
                foreach ($injuriesRawData as $injury) {
                    $injury['inc_id'] = $destinationRecordId;

                    $recordIdGenerator = (new RecordIdGeneratorFactory())->create('inc_injuries');
                    $injury['recordid'] = $recordIdGenerator->generateRecordId();

                    $InsertSql = GeneratePDOInsertSQLFromArrays([
                        'FieldArray' => $InjuriesFieldArrayForIncidents,
                        'DataArray' => $injury,
                    ], $PDOParams);

                    $sql = "INSERT INTO inc_injuries {$InsertSql}";

                    if (!DatixDBQuery::PDO_query($sql, $PDOParams)) {
                        fatal_error('Cannot copy Injuries data');

                        return false;
                    }
                }
            } else {
                // store injury data keyed on the contact id so it can be used in the next foreach
                foreach ($injuriesRawData as $injury) {
                    $injuries[$injury['con_id']][$injury['listorder']]['recordid'] = $injury['recordid'];
                    $injuries[$injury['con_id']][$injury['listorder']]['injury'] = $injury['inc_injury'];
                    $injuries[$injury['con_id']][$injury['listorder']]['bodypart'] = $injury['inc_bodypart'];
                    $injuries[$injury['con_id']][$injury['listorder']]['listorder'] = $injury['listorder'];
                    $injuries[$injury['con_id']][$injury['listorder']]['death_result_injury'] = $injury['death_result_injury'];
                    $injuries[$injury['con_id']][$injury['listorder']]['permanent_impairment_percentage'] = $injury['permanent_impairment_percentage'];
                }

                foreach ($linkedContacts as $contact) {
                    $destinationNotClaims = $destinationModule != 'CLA';
                    $destinationClaims = $destinationModule == 'CLA';

                    foreach ($injuries[$contact['con_id']] as $injury) {
                        $injuryShouldBeCopied = empty($injuriesToCopy) || in_array($injury['recordid'], $injuriesToCopy);

                        if ($destinationNotClaims || ($destinationClaims && $injuryShouldBeCopied)) {
                            $conLinkId = array_search($contact['con_id'], $linkIds);

                            if ($conLinkId === false) {
                                continue;
                            }

                            $injury['link_id'] = $conLinkId;

                            $InsertSql = GeneratePDOInsertSQLFromArrays([
                                'FieldArray' => $InjuriesFieldArrayForLinkedInjuries,
                                'DataArray' => $injury,
                            ], $PDOParams);

                            $sql = "INSERT INTO link_injuries {$InsertSql}";

                            if (!DatixDBQuery::PDO_query($sql, $PDOParams)) {
                                fatal_error('Cannot copy Injuries data');

                                return false;
                            }
                        }
                    }
                }
            }
        } else {
            foreach ($linkedContacts as $contact) {
                $sql = 'SELECT ' . implode(', ', $InjuriesFieldArrayForLinkedInjuries) .
                    ' FROM link_injuries
                WHERE link_id = :link_id';

                $injuries[$contact['link_recordid']] = DatixDBQuery::PDO_fetch_all($sql, ['link_id' => $contact['link_recordid']]);
            }

            if ($destinationModule == 'INC') {
                foreach ($linkedContacts as $contact) {
                    foreach ($injuries[$contact['link_recordid']] as $injury) {
                        $injury['inc_id'] = $destinationRecordId;
                        $injury['con_id'] = $contact['con_id'];
                        $injury['inc_injury'] = $contact['injury'];
                        $injury['inc_bodypart'] = $contact['bodypart'];

                        $recordIdGenerator = (new RecordIdGeneratorFactory())->create('inc_injuries');
                        $injury['recordid'] = $recordIdGenerator->generateRecordId();

                        $InsertSql = GeneratePDOInsertSQLFromArrays([
                            'FieldArray' => $InjuriesFieldArrayForIncidents,
                            'DataArray' => $injury,
                        ], $PDOParams);

                        $sql = "INSERT INTO inc_injuries {$InsertSql}";

                        if (!DatixDBQuery::PDO_query($sql, $PDOParams)) {
                            fatal_error('Cannot copy Injuries data');

                            return false;
                        }
                    }
                }
            } else {
                foreach ($linkedContacts as $contact) {
                    foreach ($injuries as $injury) {
                        $conLinkId = array_search($contact['con_id'], $linkIds);

                        if ($conLinkId === false) {
                            continue;
                        }

                        $injury['link_id'] = $conLinkId;

                        $InsertSql = GeneratePDOInsertSQLFromArrays([
                            'FieldArray' => $InjuriesFieldArrayForLinkedInjuries,
                            'DataArray' => $injury,
                        ], $PDOParams);

                        $sql = "INSERT INTO link_injuries {$InsertSql}";

                        if (!DatixDBQuery::PDO_query($sql, $PDOParams)) {
                            fatal_error('Cannot copy Injuries data');

                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    public static function CopyLostAndRestrictedTime($sourceModule, $sourceRecordId, $linkIds)
    {
        $ModuleDefs = Container::get(ModuleDefs::class);

        $sourceFKField = $ModuleDefs[$sourceModule]['FK'];

        $getContactLinksSql = "
          SELECT con_id, link_recordid
          FROM link_contacts
          WHERE {$sourceFKField} = :{$sourceFKField}";

        $linkedContacts = DatixDBQuery::PDO_fetch_all($getContactLinksSql, [":{$sourceFKField}" => $sourceRecordId]);

        foreach ($linkedContacts as $contact) {
            // fields to be copied to the new contact linked data
            $LostRestrictedTimeFieldArray = ['link_id', 'start_date', 'end_date', 'total'];

            // Get the lost time values for the original contact link record
            $getContactLostTimeSql = '
                SELECT ' . implode(', ', $LostRestrictedTimeFieldArray) . '
                FROM lost_time
                WHERE link_id = :link_id';

            $lostTime = DatixDBQuery::PDO_fetch_all($getContactLostTimeSql, [':link_id' => $contact['link_recordid']]);

            // Copy each lost time record and attach to the new contact link record
            foreach ($lostTime as $data) {
                $conLinkId = array_search($contact['con_id'], $linkIds);

                if ($conLinkId === false) {
                    continue;
                }
                $data['link_id'] = $conLinkId;

                $InsertSql = GeneratePDOInsertSQLFromArrays([
                    'FieldArray' => $LostRestrictedTimeFieldArray,
                    'DataArray' => $data,
                ], $PDOParams);

                $insertNewLostTimeSql = "INSERT INTO lost_time {$InsertSql}";

                if (!DatixDBQuery::PDO_query($insertNewLostTimeSql, $PDOParams)) {
                    Log::error('Unable to copy lost time data when copying a record');
                    fatal_error('Cannot copy Lost Time data');

                    return false;
                }
            }

            // Get the restricted time values for the original contact link record
            $getContactRestrictedTimeSql = '
                SELECT ' . implode(', ', $LostRestrictedTimeFieldArray) . '
                FROM restricted_time
                WHERE link_id = :link_id';

            $restrictedTime = DatixDBQuery::PDO_fetch_all($getContactRestrictedTimeSql, [':link_id' => $contact['link_recordid']]);

            // Copy each restricted time record and attach to the new contact link record
            foreach ($restrictedTime as $data) {
                $conLinkId = array_search($contact['con_id'], $linkIds);

                if ($conLinkId === false) {
                    continue;
                }

                $data['link_id'] = $conLinkId;

                $InsertSql = GeneratePDOInsertSQLFromArrays([
                    'FieldArray' => $LostRestrictedTimeFieldArray,
                    'DataArray' => $data,
                ], $PDOParams);

                $insertNewRestrictedTimeSql = "INSERT INTO restricted_time {$InsertSql}";

                if (!DatixDBQuery::PDO_query($insertNewRestrictedTimeSql, $PDOParams)) {
                    Log::error('Unable to copy restricted time data when copying a record');
                    fatal_error('Cannot copy Restricted Time data');

                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Copy additional fields to a new Progress Note.
     */
    public static function CopyToProgressNotes(string $module, int $recordid, array $data): bool
    {
        $output = 'ADDITIONAL DATA FROM FEEDBACK RECORD:' . PHP_EOL;
        foreach ($data as $key => $value) {
            $title = \Labels_FormLabel::GetFormFieldLabel($key);
            $output .= '- ' . $title . ': ' . $value . PHP_EOL;
        }

        $user = (new UserSessionFactory())->create()->getCurrentUser();

        $sql = 'INSERT INTO progress_notes
                    (pno_link_id, pno_link_module, pno_createdby, pno_createddate, pno_updateddate, pno_progress_notes)
                    VALUES
                        (:recordid, :module, :user, :cdate, :ddate, :note)';

        $PDOParams = [
            'recordid' => $recordid,
            'module' => $module,
            'user' => $user,
            'cdate' => GetTodaysDate(),
            'ddate' => GetTodaysDate(),
            'note' => $output,
        ];

        $db = new DatixDBQuery();
        $db->beginTransaction();
        $db->setSQL($sql);

        if (!$db->prepareAndExecute($PDOParams)) {
            $db->rollBack();
            fatal_error('Cannot copy additional data');
            fatal_error('Cannot copy Documents data');

            return false;
        }

        $db->commit();

        return true;
    }

    public static function copyDocuments(string $sourceModule, int $sourceId, string $destModule, int $destId): bool
    {
        $sql = 'INSERT INTO link_documents (main_record_module, main_record_id, description, [type], document_id, user_id, link_date)
                (
                    SELECT :module, :recordid, description, [type], document_id, user_id, :date FROM link_documents
                    WHERE main_record_id = :srecordid AND main_record_module = :smodule
                )';

        $PDOParams = [
            'module' => $destModule,
            'recordid' => $destId,
            'date' => GetTodaysDate(),
            'srecordid' => $sourceId,
            'smodule' => $sourceModule,
        ];

        $db = new DatixDBQuery();
        $db->beginTransaction();
        $db->setSQL($sql);

        if (!$db->prepareAndExecute($PDOParams)) {
            $db->rollBack();
            fatal_error('Cannot copy additional data');
            fatal_error('Cannot copy Documents data');

            return false;
        }

        $db->commit();

        return true;
    }

    public static function updateContactTypes(string $module, string $destModule, $dest_FK_field, $destCasID): void
    {
        if (!isset(GenerateFromMappings::CONTACT_MAPPINGS[$module][$destModule])) {
            return;
        }

        foreach (GenerateFromMappings::CONTACT_MAPPINGS[$module][$destModule] as $fromCode => $toCode) {
            $sql = "UPDATE link_contacts
                    SET link_type = '{$toCode}'
                    WHERE {$dest_FK_field} = {$destCasID} AND
                          link_type = '{$fromCode}'";

            DatixDBQuery::PDO_query($sql);
        }
    }

    /**
     * Returns the $data array after removing any fields set in AppVars under COPY_EXCLUDE_FIELDS.
     */
    private function removeExcludedFieldData(string $module, array $data): array
    {
        $moduleDef = $this->registry->getModuleDefs()->getModuleData($module);

        $fieldsToExcludeFromCopy = $moduleDef['COPY_EXCLUDE_FIELDS'];

        // Remove any excluded fields from the data array so that they aren't saved to the copied record
        if (!empty($fieldsToExcludeFromCopy)) {
            foreach ($fieldsToExcludeFromCopy as $fieldToExclude) {
                unset($data[$fieldToExclude]);
            }
        }

        return $data;
    }
}
