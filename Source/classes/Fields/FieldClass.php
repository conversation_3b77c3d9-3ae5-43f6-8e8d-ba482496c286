<?php
/**
 * TODO: Replace this with the new Field object.
 */

use Source\classes\Exceptions\FieldNotFoundException;
use src\framework\registry\Registry;
use src\system\container\facade\Container;

/**
 * @desc Object representing a database field
 */
class Fields_Field extends Fields_DummyField
{
    protected $Table;

    /** @var array|null */
    protected $Data = null;
    protected $Long;
    protected $Module;

    /**
     * @desc Looks in FIELD_DIRECTORY table to find field meta-data.
     */
    public function __construct($Name, $Table = '', $long = false)
    {
        $this->Name = \UnicodeString::strtolower($Name);
        $this->Long = $long;

        $Table = $this->getTableAndSetData($Table);

        $this->Table = \UnicodeString::strtolower($Table);

        if (empty($this->getData())) {
            throw new FieldNotFoundException('Field "' . $Name . '" not found in field_directory');
        }

        $this->setFieldLabel();
    }

    public function getName()
    {
        return $this->Name;
    }

    public function getFieldType()
    {
        return $this->getData()['fdr_data_type'] ?? null;
    }

    public function getTable()
    {
        return $this->Table;
    }

    public function getLabel($module = null)
    {
        return $this->getData()['fdr_label'] ?? null;
    }

    public static function getFieldLabel($Name, $Table = '')
    {
        $field = new self($Name, $Table);

        return $field->getLabel();
    }

    public function getCodeDescription()
    {
        return $this->getData()['fdr_code_descr'] ?? null;
    }

    /**
     * @desc Returns the module this field belongs to (if any).
     */
    public function getModule()
    {
        global $ModuleDefs;

        if ($this->Module == null) {
            foreach ($ModuleDefs as $module => $details) {
                if ($details['TABLE'] == $this->getTable()) {
                    $this->Module = $module;
                }
            }

            if ($module == null) {
                $this->Module = '';
            }
        }

        return $this->Module;
    }

    public function setValue($value)
    {
        $this->Value = $value;
        $this->Description = null;
        $this->Colour = null;
    }

    public function getCodeTable()
    {
        return \UnicodeString::strtolower($this->getData()['fdr_code_table']);
    }

    public function getCodeDescriptionTable()
    {
        return \UnicodeString::strtolower($this->getData()['fdr_code_table'] . '_descr');
    }

    public function getCodeWhere()
    {
        return \UnicodeString::strtolower($this->getData()['fdr_code_where']);
    }

    public function getCodeTableField()
    {
        return \UnicodeString::strtolower($this->getData()['fdr_code_field']);
    }

    /**
     * Getter for the Colour property.
     *
     * @codeCoverageIgnore
     */
    public function getCodeColour()
    {
        return \UnicodeString::strtolower($this->Colour);
    }

    protected function getData()
    {
        return $this->Data;
    }

    protected function getTableAndSetData($table): ?string
    {
        if ($table) {
            $this->Data = $_SESSION['field_directory:' . $this->Name . ':' . $table] ?? null;

            if ($this->getData() === null) {
                $this->getDataFromTable($table);
                $_SESSION['field_directory:' . $this->Name . ':' . $table] = $this->getData();
            }

            return $table;
        }

        $this->Data = $_SESSION['field_directory:' . $this->Name] ?? null;

        if ($this->getData() === null) {
            $this->getDataFromName();
            $_SESSION['field_directory:' . $this->Name] = $this->getData();
        }

        return $this->getData()['fdr_table'] ?? null;
    }

    private function getDataFromTable($Table): void
    {
        $sql = 'SELECT fdr_name, fdr_label, fdr_table, fdr_code_table, fdr_code_descr, fdr_code_field, fdr_data_type, fdr_data_length, fdr_format, fdr_code_where, fdr_code_parent, fdr_code_order, fdr_code_parent2, fdr_colour, fdr_setup, fdr_custom_code FROM FIELD_DIRECTORY WHERE FDR_NAME = :Name AND FDR_TABLE = :Table';
        $this->Data = DatixDBQuery::PDO_fetch($sql, ['Name' => $this->Name, 'Table' => $Table]);
    }

    private function getDataFromName(): void
    {
        $sql = 'SELECT fdr_name, fdr_label, fdr_table, fdr_code_table, fdr_code_descr, fdr_code_field, fdr_data_type, fdr_data_length, fdr_format, fdr_code_where, fdr_code_parent, fdr_code_order, fdr_code_parent2, fdr_colour, fdr_setup, fdr_custom_code from FIELD_DIRECTORY WHERE FDR_NAME = :Name';
        $this->Data = DatixDBQuery::PDO_fetch($sql, ['Name' => $this->Name]);
    }

    private function setFieldLabel()
    {
        $fieldLabels = Container::get(Registry::class)->getFieldLabels();
        $fieldLabel = $fieldLabels->getLabel($this->getTable(), $this->getName());

        $this->getData()['fdr_label'] = $this->Long ? $fieldLabel->withTableSuffix() : $fieldLabel;
    }
}
