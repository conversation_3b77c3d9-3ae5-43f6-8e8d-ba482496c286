<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\system\moduledefs\ModuleDefs;

/**
 * @desc Object representing a dummy field (e.g. one without a database table)
 */
class Fields_DummyField
{
    public const KEY_NAME = 'name';
    public const KEY_LABEL = 'label';
    public const KEY_TYPE = 'type';
    protected $Name;
    protected $Type;
    protected $Label;
    protected $CodeArray;
    protected $CodeField;
    protected $Value;
    protected $Description;
    protected $Colour;
    protected $Width;
    protected $table;

    /**
     * Defines the controller action responsible for rendering the output for this field.
     *
     * @var array
     */
    protected $action;

    public function __construct($Parameters)
    {
        $this->Name = $Parameters[self::KEY_NAME] ?? null;
        $this->Type = $Parameters[self::KEY_TYPE] ?? null;
        $this->Label = $Parameters[self::KEY_LABEL] ?? null;
        $this->CodeArray = $Parameters['codes'] ?? null;
        $this->CodeField = $Parameters['codefield'] ?? null;
        $this->Width = $Parameters['width'] ?? null;
        $this->table = $Parameters['table'] ?? null;
    }

    public function getName()
    {
        return \UnicodeString::strtolower($this->Name);
    }

    public function getFieldType()
    {
        return $this->Type;
    }

    public function getLabel($module = null)
    {
        return $this->Label;
    }

    public function getDescription()
    {
        return $this->Description;
    }

    public function getTable()
    {
        return $this->table;
    }

    public function hasFdrSetup()
    {
        return isset($this->Data) && $this->Data['fdr_setup'] == 'Y';
    }

    public function setValue($value)
    {
        if ($this->getFieldType() === 'Y' && is_bool($value)) {
            $value = $value ? 'Y' : 'N';
        }

        $this->Value = $value;
        $this->Description = null;
        $this->Colour = null;
    }

    /**
     * @desc Calculates and populates the "Description" and "Colour" properties with display-ready values.
     */
    public function getDisplayInfo(): void
    {
        // $this->Value may be an object, e.g. GlobalValue. GlobalValue has a __toString method
        // however, if we don't cast to ensure it is a string, when using $this->value as an array
        // key, PHP will error with Illegal Offset type as it will not auto-cast to a string.
        $value = (string) $this->Value;

        if ($value == '') {
            return;
        }

        if (!empty($_SESSION['CachedValues']['code_description']['custom'][$this->getName()][$value])) {
            $this->Description = $_SESSION['CachedValues']['code_description']['custom'][$this->getName()][$value];
            $this->Colour = $_SESSION['CachedValues']['code_colour']['custom'][$this->getName()][$value];
        } elseif ($this->Name === 'act_module') {
            $moduleDefs = Container::get(ModuleDefs::class);
            $this->Description = $moduleDefs[$value]['NAME'] ?? $this->Description;
        } else {
            switch ($this->getFieldType()) {
                case FieldInterface::CODE_CODE:
                case FieldInterface::MULTI_SELECT_CODE:
                case 'CO': // just what on earth is this..
                    $table = $this->table ?? $this->Table ?? null; // because the child class uses the capitalised one, for.. reasons..
                    $codeField = $this->CodeField ?? $this->Name ?? null;

                    if (!empty($this->CodeArray[$value])) {
                        $this->Description = $this->CodeArray[$value];
                    } elseif ($table && $codeField) {
                        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
                        $values = explode(' ', rtrim($value));
                        $descriptions = [];

                        foreach ($values as $thisValue) {
                            $code = $codeInfoRetriever->retrieve($table . '.' . $codeField, $thisValue);
                            $descriptions[] = $code->getDescription();
                        }

                        $this->Description = implode(', ', $descriptions);

                        if (count($values) === 1 && !empty($code)) {
                            $this->Colour = $code->getCodWebColour();
                        }
                    } elseif ($codeField) {
                        $CodeInfo = get_code_info(getModuleFromField($codeField), $codeField, $value);
                        $this->Description = $CodeInfo['description'];
                        $this->Colour = $CodeInfo['cod_web_colour'];
                    } else {
                        $this->Description = $value;
                    }

                    break;
                case FieldInterface::DATE_CODE:
                    $this->Description = formatDateForDisplay($value);

                    break;
                case FieldInterface::YESNO_CODE:
                    $YNArray = ['Y' => _fdtk('yes'), 'N' => _fdtk('no')];
                    $this->Description = $YNArray[$value];

                    break;
                case FieldInterface::TIME_CODE:
                    if (\UnicodeString::strlen($value) == 4) {
                        $this->Description = $value[0] . $value[1] . ':' . $value[2] . $value[3];
                    } else {
                        $this->Description = $value;
                    }

                    break;
                default:
                    $this->Description = $value;
            }

            $_SESSION['CachedValues']['code_description']['custom'][$this->getName()][$value] = $this->Description;
            $_SESSION['CachedValues']['code_colour']['custom'][$this->getName()][$value] = $this->Colour;
        }
    }

    public function getWidth()
    {
        return $this->Width;
    }

    public function getColour()
    {
        return $this->Colour;
    }

    public function getModule()
    {
        return 'custom';
    }

    public static function getFieldTypesArray()
    {
        return [
            'D' => 'Date',
            'C' => 'Coded',
            'Y' => 'Yes/No',
            'T' => 'Multicode',
            'S' => 'String',
            'N' => 'Number',
            'M' => 'Money',
            'L' => 'Text',
        ];
    }

    /**
     * Define a controller action responsible for rendering the output for this listing column.
     *
     * @param string $controller controller class name
     * @param string $action controller action (method) name
     *
     * @throws \InvalidArgumentException
     */
    public function setControllerAction($controller, $action, ?array $data = null)
    {
        if (!class_exists($controller)) {
            throw new \InvalidArgumentException($controller . ' is not a valid controller');
        }

        if (!method_exists($controller, $action)) {
            throw new \InvalidArgumentException($action . ' is not a valid action of controller ' . $controller);
        }

        $this->action = ['controller' => $controller, 'action' => $action, 'data' => $data];
    }

    /**
     * Fetches the contents of a field that uses a controller action.
     *
     * @throws \Exception If the field doesn't use a controller action
     */
    public function getContents($data)
    {
        if (!$this->usesControllerAction()) {
            throw new \Exception('getContents() is not supported by fields that don\'t use a controller action');
        }

        $loader = new src\framework\controller\Loader();
        $controller = $loader->getController(['controller' => $this->action['controller']]);
        $controller->setRequestParameter('data', $data);
        $controller->setRequestParameter('additional_data', $this->action['data']);

        return $controller->doAction($this->action['action']);
    }

    /**
     * Whether this field relies on a separate controller action to render its contents.
     *
     * @return bool
     */
    public function usesControllerAction()
    {
        return $this->getFieldType() == 'X' && is_array($this->action);
    }

    /**
     * @param mixed $Description
     *
     * @return Fields_DummyField
     */
    public function setDescription($Description)
    {
        $this->Description = $Description;

        return $this;
    }

    /**
     * @param mixed $Colour
     *
     * @return Fields_DummyField
     */
    public function setColour($Colour)
    {
        $this->Colour = $Colour;

        return $this;
    }
}
