<?php

use app\models\codefield\services\CodeInfoRetrieverFactory;
use app\services\idGenerator\RecordIdGeneratorFactory;
use Source\classes\Fields\UdfProfileService;
use src\admin\services\CapturePermissionService;
use src\admin\services\CapturePermissionServiceFactory;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\framework\session\SessionFactory;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;
use src\system\language\LanguageSession;
use src\system\language\LanguageSessionFactory;

/**
 * @desc Object representing a database field
 */
class Fields_ExtraField extends Fields_DummyField
{
    protected $ID;
    protected $GroupID;
    protected $Format;
    protected $Length;
    protected $CodeLike;
    protected $CodeLikeTable;
    protected $DesignLabel; // In design mode, extra fields have different labels.
    protected $CentralLocked;
    protected $QueryID;
    protected $CodeLikeModule;
    protected $profiles;
    protected $languageId;
    protected $isNewUiLanguage;
    protected Request $request;

    /** @desc Array of modules that this extra field will appear in. */
    protected ?array $Modules = null;

    /**
     * @desc Looks in FIELD_DIRECTORY table to find field meta-data.
     */
    public function __construct($ID, $GroupID = '', $languageId = null)
    {
        $this->ID = $ID;
        $this->GroupID = $GroupID;

        $this->request = new Request();

        if (!$this->ID) {
            return;
        }

        $this->languageId = $this->getLanguageId($languageId);
        $this->isNewUiLanguage = $this->isNewUiLanguage($this->languageId);

        $data = $this->getUdfFieldData();
        $this->Type = $data['fld_type'];
        $this->Format = $data['fld_format'];
        $this->Length = $data['fld_length'];
        $this->Label = $data['description'] ?? $data['fld_name_basic'];
        $this->DesignLabel = $data['description'] ?? $data['fld_name'];
        $this->CodeLike = $data['fld_code_like'];
        $this->CodeLikeTable = $data['fld_code_like_table'];
        $this->CentralLocked = $data['central_locked'];
        $this->CodeLikeModule = $data['fld_code_like_module'];
    }

    public function GetDesignLabel()
    {
        return $this->DesignLabel;
    }

    public function GetValue()
    {
        return $this->Value;
    }

    public function GetID()
    {
        return $this->ID;
    }

    public function getName()
    {
        return 'UDF_' . $this->Type . '_' . (int) $this->GroupID . '_' . $this->ID;
    }

    public function getLabel($module = null): string
    {
        return $this->Label ?? '';
    }

    /**
     * Returns a list of all labels for this field, keyed by language.
     *
     * @return array
     */
    public function getAllLabels()
    {
        $labels = DatixDBQuery::PDO_fetch_all('
            SELECT language, description from udf_fields_descr WHERE fieldId = ?
        ', [$this->ID], PDO::FETCH_KEY_PAIR);

        if (empty($labels)) {
            $labels[LanguageSession::ENGLISH_UK] = $this->getLabel();
        }

        return $labels;
    }

    public function getFormat()
    {
        return $this->Format;
    }

    public function getLength()
    {
        return $this->Length;
    }

    public function getCodeLike()
    {
        return $this->CodeLike;
    }

    public function getCentralLocked()
    {
        return $this->CentralLocked;
    }

    public function getModules(): array
    {
        if ($this->Modules === null) {
            $this->Modules = $this->getUdfModuleLinks();
        }

        return $this->Modules;
    }

    public function getMapFieldModule()
    {
        return $this->CodeLikeModule;
    }

    /**
     * @desc Returns whether or not this field is in a given table.
     */
    public function inTable($Table)
    {
        return \UnicodeString::strtolower($this->getTable()) == \UnicodeString::strtolower($Table);
    }

    /**
     * @desc Returns the module this field belongs to (if any).
     */
    public function getModule()
    {
        global $ModuleDefs;

        foreach ($ModuleDefs as $module => $details) {
            if ($details['TABLE'] == $this->getTable() && $details['TABLE'] !== null && $this->getTable() !== null) {
                return $module;
            }
        }

        return '';
    }

    public function setValue($value)
    {
        $this->Value = $value;
        $this->Description = null;
        $this->Colour = null;
    }

    /**
     * @desc Calculates and populates the "Description" and "Colour" properties with display-ready values.
     */
    public function getDisplayInfo(): void
    {
        $module = $this->getModule();
        $field = $this->getName();
        $value = $this->Value;

        if ($value == '') {
            return;
        }

        if ($_SESSION['CachedValues']['cod_info'][$module][$field][$value] && !$GLOBALS['devNoCache']) {
            $this->Description = $_SESSION['CachedValues']['cod_info'][$module][$field][$value]['description'];
            $this->Colour = $_SESSION['CachedValues']['cod_info'][$module][$field][$value]['cod_web_colour'];
        } else {
            switch ($this->getFieldType()) {
                case FieldInterface::CODE_CODE:
                    $this->GetCodeDisplayInfo();

                    break;
                case FieldInterface::MULTI_SELECT_CODE:
                    $values = explode(' ', $value);
                    foreach ($values as $mcValue) {
                        $this->GetCodeDisplayInfo($mcValue);
                        $mcDescriptions[] = $this->Description;
                    }
                    $this->Description = implode(', ', $mcDescriptions);

                    break;
                case FieldInterface::DATE_CODE:
                    $this->Description = formatDateForDisplay($this->Value);

                    break;
                case FieldInterface::YESNO_CODE:
                    $YNArray = ['Y' => _fdtk('yes'), 'N' => _fdtk('no')];
                    $this->Description = $YNArray[$this->Value];

                    break;
                case FieldInterface::NUMBER_CODE:
                    // If format specified in UDF setup, then try and emulate. Otherwise use default format.
                    if ($this->getFormat() != '') {
                        $this->Description = GuptaFormatEmulate($this->Value, $this->getFormat());
                    } else {
                        $this->Description = number_format((float) $this->Value, 2, '.', '');
                    }

                    break;
                case FieldInterface::MONEY:
                    $this->Description = $this->Value;

                    break;
                default:
                    $this->Description = $this->Value;
            }

            $_SESSION['CachedValues']['cod_info'][$module][$field][$value]['description'] = $this->Description;
            $_SESSION['CachedValues']['cod_info'][$module][$field][$value]['cod_web_colour'] = $this->Colour;
        }
    }

    public function UpdateFromPost()
    {
        LoggedIn();

        $userCanAccessAtLeastOneModule = (new CapturePermissionServiceFactory())->create()->userCanAccessAtLeastOneModule(CapturePermissionService::PERMISSION_EXTRA_FIELD);
        if (!$userCanAccessAtLeastOneModule) {
            throw new PermissionDeniedException();
        }

        $data = $this->request->getParameters();

        $recordId = (int) $data['recordid'];

        $this->Label = $data['label'];
        $this->Type = $data['type'];

        UdfProfileService::removeProfileLinks($recordId);
        $profiles = $data['profile_select'] ?: [];
        UdfProfileService::saveProfileLinks($recordId, $profiles);

        if ($data['fld_code_like'] !== null && $data['fld_code_like_module'] === null) {
            $data['fld_code_like_module'] = getModuleFromField($data['fld_code_like']);
            $this->request->setParameter('fld_code_like_module', $data['fld_code_like_module']);
        } elseif ($data['fld_code_like'] === null && $data['fld_code_like_module'] === null) {
            $this->CodeLike = null;
            $this->CodeLikeTable = null;
            $this->CodeLikeModule = null;
        }

        $this->SaveConfigurationToDatabase();
    }

    /**
     * Returns the name of the table that stores extra field codes.
     *
     * @return string
     */
    public function getCodeTable()
    {
        return 'udf_codes';
    }

    public function getCodeDescriptionTable()
    {
        return 'udf_codes_descr';
    }

    /**
     * Returns the column name for codes in the extra field code table.
     *
     * @return string
     */
    public function getCodeTableField()
    {
        return 'udc_code';
    }

    /**
     * Returns the column name for descriptions in the extra field code table.
     *
     * @return
     */
    public function getCodeDescription()
    {
        return 'udc_description';
    }

    /**
     * @desc Determines the correct code table for a coded field and pulls out the "Description" and "Colour" values.
     */
    protected function GetCodeDisplayInfo($value = '')
    {
        if ($value == '') {
            $value = $this->Value;
        }

        $codeInfoRetriever = (new CodeInfoRetrieverFactory())->create();
        $code = $codeInfoRetriever->retrieve('UDF_' . $this->ID, $value);
        $this->Description = $code->getDescription();
    }

    protected function SaveConfigurationToDatabase(): void
    {
        LoggedIn();
        $userCanAccessAtLeastOneModule = (new CapturePermissionServiceFactory())->create()->userCanAccessAtLeastOneModule(CapturePermissionService::PERMISSION_EXTRA_FIELD);
        if (!$userCanAccessAtLeastOneModule) {
            throw new PermissionDeniedException();
        }

        DatixDBQuery::static_beginTransaction();
        if (!$this->ID) {
            $recordIdGenerator = (new RecordIdGeneratorFactory())->create('udf_fields');
            $recordId = $recordIdGenerator->generateRecordId();
            $this->createBlankRecord($recordId);
            $this->ID = $recordId;
        }

        try {
            $this->updateMainTableData();
            $this->updateLabels();
            $this->clearModuleRecordLinks();
            $this->addModuleLinks($_POST['fld_modules']);
        } catch (Exception $exception) {
            DatixDBQuery::static_rollBack();

            throw $exception;
        }

        DatixDBQuery::static_commit();
    }

    /**
     * Updates the cached values for this UDF stored in the session (e.g. when updating the field).
     *
     * @param array $labels all labels of the field
     */
    protected function updateCache(array $labels): void
    {
        $languageUpdated = $_POST['language'];
        $fieldLabel = $labels[$languageUpdated] ?? '';

        $newValues = [
            'fld_name' => $_POST['fld_name'],
            'fld_name_basic' => $_POST['fld_name'],
            'fld_type' => $_POST['fld_type'],
            'fld_format' => $_POST['fld_format'],
            'fld_length' => $_POST['fld_length'],
            'central_locked' => $_POST['central_locked'],
            'fld_code_like_module' => $_POST['fld_code_like_module'],
            'fld_code_like' => $_POST['fld_code_like'],
        ];

        $udfFromCache = (new SessionFactory())->create()->get('udf_fields', $this->ID);

        if ($this->GroupID) {
            if ($udfFromCache['GROUP'][$this->GroupID]['language'] === $languageUpdated) {
                $newValues['description'] = $fieldLabel;
            }

            $_SESSION['udf_fields'][$this->ID]['GROUP'][$this->GroupID] =
                array_merge($udfFromCache['GROUP'][$this->GroupID], $newValues);

            return;
        }

        if (is_array($udfFromCache['NO_GROUP'])) {
            if ($udfFromCache['NO_GROUP']['language'] === $languageUpdated) {
                $newValues['description'] = $fieldLabel;
            }

            $_SESSION['udf_fields'][$this->ID]['NO_GROUP'] = array_merge($udfFromCache['NO_GROUP'], $newValues);

            return;
        }

        $newValues['description'] = $fieldLabel;
        $_SESSION['udf_fields'][$this->ID]['NO_GROUP'] = $newValues;
    }

    private function getFieldTable($module, $fieldName)
    {
        $registry = Container::get(Registry::class);
        $fieldDefs = $registry->getFieldDefs();
        $moduleDefs = $registry->getModuleDefs();

        $tableOrView = $moduleDefs[$module]->getDbReadObj();

        $fieldDef = $fieldDefs[$tableOrView . '.' . $fieldName];

        return $fieldDef->getCodeTable();
    }

    /**
     * @return array
     */
    private function getMultiLingualDescriptions()
    {
        $sql = '
            SELECT
              language, description
            FROM
              udf_fields_descr
            WHERE
              fieldId = ?
        ';

        return DatixDBQuery::PDO_fetch_all($sql, [$this->ID], PDO::FETCH_KEY_PAIR);
    }

    private function updateMultiLingualDescriptions(array $parameters)
    {
        $sql = '
              UPDATE
                udf_fields_descr
              SET
                description = :description
              WHERE
                fieldId = :fieldId
              AND
                language = :language
        ';

        DatixDBQuery::PDO_query($sql, $parameters);
    }

    private function insertMultilingualDescription(array $parameters)
    {
        $sql = '
              INSERT INTO
                udf_fields_descr (fieldId, language, description)
              VALUES
                (:fieldId, :language, :description)
        ';

        DatixDBQuery::PDO_query($sql, $parameters);
    }

    private function getLanguageId(?int $languageId): int
    {
        $languageFactory = LanguageSessionFactory::getInstance();

        if (!$languageId || !in_array($languageId, $languageFactory->getAvailableLanguages(), true)) {
            return $languageFactory->getLanguage();
        }

        return $languageId;
    }

    private function getUdfFieldData(): ?array
    {
        $this->updateSessionCacheWithUdfFieldData();

        $session = (new SessionFactory())->create();

        return $session->get('udf_fields', $this->ID)['NO_GROUP'];
    }

    private function updateSessionCacheWithUdfFieldData(): void
    {
        if (isset($_SESSION['udf_fields'][$this->ID]['NO_GROUP'])
            && !$this->isNewUiLanguage
        ) {
            return;
        }

        foreach ($this->getUdfFields() as $udfField) {
            $_SESSION['udf_fields'][$udfField['recordid']]['NO_GROUP'] = $udfField;
        }
    }

    private function isNewUiLanguage(int $languageId): bool
    {
        if ($_SESSION['udf_fields']['ui_language'] === $languageId) {
            return false;
        }

        $_SESSION['udf_fields']['ui_language'] = $languageId;

        return true;
    }

    private function getUdfFields(): array
    {
        $sql = '
            SELECT
                f.recordid,
                f.fld_name as fld_name_basic,
                f.fld_name,
                f.fld_type,
                f.fld_format,
                f.fld_code_like,
                f.fld_code_like_table,
                f.fld_length,
                f.central_locked,
                f.fld_code_like_module,
                COALESCE (d.description COLLATE Latin1_General_CI_AI, f.fld_name COLLATE Latin1_General_CI_AI) AS description,
                d.language
            FROM
                udf_fields f
            LEFT JOIN
                udf_fields_descr d
            ON
                f.recordid = d.fieldId
            WHERE
                d.language = :language
        ';

        return DatixDBQuery::PDO_fetch_all($sql, ['language' => $this->languageId]);
    }

    private function getUdfModuleLinks(): array
    {
        $sql = '
            SELECT
                uml_module
            FROM
                udf_mod_link
            WHERE
                uml_id = :umlId
        ';

        $results = DatixDBQuery::PDO_fetch_all($sql, ['umlId' => $this->ID]);

        $data = [];

        foreach ($results as $link) {
            $data[] = $link['uml_module'];
        }

        return $data;
    }

    private function updateMainTableData()
    {
        $sql = '
            UPDATE
                udf_fields
            SET
                fld_type = :fld_type,
                fld_format = :fld_format,
                fld_length = :fld_length,
                central_locked = :central_locked,
                queryid = :queryid,
                fld_code_like = :fld_code_like,
                fld_code_like_table = :fld_code_like_table,
                fld_code_like_module = :fld_code_like_module
            WHERE
                recordid = :recordid
        ';

        if (
            isset($_POST['fld_code_like_module'], $_POST['fld_code_like'])
            && $_POST['fld_code_like_module'] != ''
            && $_POST['fld_code_like'] != ''
        ) {
            $fieldTable = $this->getFieldTable($_POST['fld_code_like_module'], $_POST['fld_code_like']);
        }

        $PDOParams['recordid'] = $this->ID;
        $PDOParams['fld_type'] = empty($_POST['fld_type']) ? FieldInterface::TEXT_CODE : $_POST['fld_type'];
        $PDOParams['fld_format'] = $_POST['fld_format'];
        $PDOParams['fld_length'] = ($_POST['fld_length'] != '' ? $_POST['fld_length'] : null);
        $PDOParams['central_locked'] = ($_POST['central_locked'] != '' ? $_POST['central_locked'] : null);
        $PDOParams['queryid'] = $this->queryid;
        $PDOParams['fld_code_like'] = $_POST['fld_code_like'] ?: '';
        $PDOParams['fld_code_like_table'] = $fieldTable ?: '';
        $PDOParams['fld_code_like_module'] = $_POST['fld_code_like_module'] ?: '';

        DatixDBQuery::PDO_query($sql, $PDOParams);
    }

    private function updateLabels()
    {
        $currentLabels = $this->getMultiLingualDescriptions();
        $labels = json_decode($_POST['labels'], true);

        if (!is_array($labels)) {
            return;
        }

        $labels = array_filter($labels, static function ($value, $key): bool {
            return !empty($value) && !empty($key);
        }, ARRAY_FILTER_USE_BOTH);

        $parameters['fieldId'] = $this->ID;
        foreach ($labels as $languageId => $description) {
            $parameters['language'] = $languageId;
            $parameters['description'] = $description;

            if (!isset($currentLabels[$languageId])) {
                $this->insertMultilingualDescription($parameters);

                continue;
            }

            $this->updateMultiLingualDescriptions($parameters);
        }

        if (!empty($labels)) {
            $this->updateCache($labels);
        }
    }

    private function clearModuleRecordLinks()
    {
        $sql = '
            DELETE FROM
                udf_mod_link
            WHERE
                uml_id = :recordid
        ';

        DatixDBQuery::PDO_query($sql, ['recordid' => $this->ID]);
    }

    private function addModuleLinks($fldModules)
    {
        if (!is_array($fldModules)) {
            return;
        }

        foreach ($fldModules as $module) {
            DatixDBQuery::PDO_build_and_insert('udf_mod_link', [
                'uml_id' => $this->ID,
                'uml_module' => $module,
            ]);
        }
    }

    private function createBlankRecord(int $recordId): void
    {
        DatixDBQuery::PDO_build_and_insert('udf_fields', [
            'recordid' => $recordId,
            'fld_name' => $this->request->getParameter('fld_name'),
            'fld_type' => FieldInterface::TEXT_CODE,
        ]);
    }
}
