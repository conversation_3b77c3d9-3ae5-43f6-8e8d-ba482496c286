<?php

namespace Source\classes\Fields;

use DatixDBQuery;
use PDO;

class UdfProfileService
{
    public static function saveProfileLinks(int $udfId, array $profileIds): void
    {
        foreach ($profileIds as $profileId) {
            $sql = 'INSERT INTO udf_profile VALUES (:udfId, :profileId)';

            DatixDBQuery::PDO_query($sql, ['udfId' => $udfId, 'profileId' => $profileId]);
        }
    }

    public static function removeProfileLinks(int $udfId): void
    {
        $sql = 'DELETE FROM udf_profile WHERE udf_id = :udfId';
        DatixDBQuery::PDO_query($sql, ['udfId' => $udfId]);
    }

    public static function getProfileLinks(int $udfId): string
    {
        $sql = 'SELECT profile_id FROM udf_profile WHERE udf_id = :udfId';
        $profileLinks = DatixDBQuery::PDO_fetch_all($sql, ['udfId' => $udfId], PDO::FETCH_COLUMN);

        if (empty($profileLinks)) {
            return '';
        }

        return implode(' ', $profileLinks);
    }

    public static function getProfileLinksArray(int $udfId): array
    {
        $sql = 'SELECT profile_id FROM udf_profile WHERE udf_id = :udfId';

        return DatixDBQuery::PDO_fetch_all($sql, ['udfId' => $udfId], PDO::FETCH_COLUMN) ?: [];
    }
}
