<?php

use app\models\framework\config\DatixConfig;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\valueObjects\Module;
use app\services\menus\SubMenuFactory;
use app\services\psims\PsimsMandatoryQuestionsService;
use src\admin\services\CapturePermissionService;
use src\admin\services\CapturePermissionServiceFactory;
use src\component\form\FormTable;
use src\framework\controller\Request;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\system\container\facade\Container;
use src\system\language\LanguageSession;

/**
 * @desc Models the left hand menu on forms.
 */
class LeftMenu
{
    public const ADMIN_MODULES = ['ADM', 'USE', 'DEL'];
    public const ADMIN_ACTIONS = ['designabasereport', 'reportsadminlist', 'listlistingreports', 'excludefields', 'reportsadminaction'];

    // Additional custom links
    public $ExtraLinks;

    // The module this menu belongs to
    protected $Module;

    // The module to actually display
    protected $MenuModule;

    // The FormTable object representing the form that this menu serves. Used to find panels.
    protected $Table;

    // Array of panels if this is a non-standard menu and so does not have a FormTable object to reference.
    protected $Panels;

    // Key identifying the currently selected panel for use when drawing the menu.
    protected $CurrentPanel;

    // Key identifying a field that is erroring and needs focus.
    protected $ErrorField;

    // Array containing config information about the form - used for default panel names.
    protected $FormArray;

    // ButtonGroup object used when creating the floating left hand menu, which contains button icons.
    protected $Buttons;

    // Array of menu items
    protected $MenuArray;

    // ID of the current record (if applicable)
    protected $Recordid;

    // Bool storing whether a print link should be displayed
    protected $PrintLink;

    // String storing the url to go to when the "Print" link is clicked
    protected $printLinkUrl;

    // Bool storing whether an audit link should be displayed
    protected $AuditLink;

    // Bool storing whether the current page is associated with a record listing
    protected $Listing;

    // Bool storing whether an NPSA export link should be displayed
    protected $NPSAExportLink;

    // Is this record a linked record?
    protected $isLinkedRecord = false;
    protected $showAdminBackLink = false;
    private $showBackLink;
    private $registry;

    // Stores the approval status of the current record, if available
    private $approvalStatus;
    private $checkPerModuleBatchUpdateAccess = false;
    private LanguageSession $languageSession;

    /**
     * @desc Constructor: Assigns parameter values to object properties.
     *
     * @param array $Parameters the array of parameters to be assigned
     */
    public function __construct($Parameters)
    {
        $this->registry = Container::get(Registry::class);

        $this->Module = $Parameters['module'];
        $this->approvalStatus = $Parameters['approvalStatus'] ?? null;
        $this->MenuModule = $Parameters['menumodule'] ?? null;
        $this->Table = $Parameters['table'] ?? null;
        $this->Panels = $Parameters['panels'] ?? null;
        $this->CurrentPanel = $Parameters['current_panel'] ?? null;
        $this->ErrorField = $Parameters['error_field'] ?? null;
        $this->FormArray = $Parameters['formarray'] ?? null;
        $this->Buttons = $Parameters['buttons'] ?? null;
        $this->MenuArray = $Parameters['menu_array'] ?? null;
        $this->PrintLink = (!($Parameters['no_print'] ?? false) && !$this->registry->getDeviceDetector()->isTablet() && $this->registry->getParm('ENABLE_PRINT', 'N')->isTrue());
        $this->printLinkUrl = $Parameters['printLinkUrl'] ?? null;
        $this->PrintLinkParams = array_key_exists('print_link_params', $Parameters) ? $Parameters['print_link_params'] : false;
        $this->AuditLink = !($Parameters['no_audit'] ?? false);
        $this->Listing = $Parameters['listing'] ?? null;
        $this->Action = $Parameters['action'] ?? null;
        $this->show_excluded = $_SESSION['reports_administration:show_excluded'] ?? null;
        $this->isLinkedRecord = $Parameters['isLinkedRecord'] ?? null;
        $this->ExtraLinks = $Parameters['extra_links'] ?? null;
        $this->showMenuLinks = $Parameters['showMenuLinks'] ?? true;

        // Because the module get changed when in the admin section if working with module specific data, some admin pages need to be found using their action instead of the module
        $permissionService = (new CapturePermissionServiceFactory())->create();
        $userIsAdmin = $permissionService->userHasAnAdminPermission();

        $this->showAdminBackLink = $userIsAdmin
            && (in_array($this->Module, self::ADMIN_MODULES) || in_array($this->Action, self::ADMIN_ACTIONS));
        $isPrinceModule = Container::get(ModuleRepository::class)->princeModuleExists($this->Module);
        $shouldShowBackLink = $Parameters['showBackLink'] ?? true;
        $this->showBackLink = $isPrinceModule && $shouldShowBackLink && !$this->showAdminBackLink;

        if (isset($_GET['hide_linked_sections'])) {
            $this->showBackLink = false;
            $this->showMenuLinks = false;
        }
        $this->SetCurrentPanel();

        // tries to find the current recordid
        $this->Recordid = $Parameters['recordid'] ?? $_GET['recordid'] ?? null;

        $permissionService = (new CapturePermissionServiceFactory())->create();
        $this->checkPerModuleBatchUpdateAccess = !$permissionService->isReadOnly($this->Module, CapturePermissionService::PERMISSION_BATCH_UPDATE);

        $this->languageSession = Container::get(LanguageSession::class);
    }

    public function SetCurrentPanel()
    {
        global $FieldSectionReference;

        if ($this->ErrorField) {
            $this->CurrentPanel = $FieldSectionReference[$this->ErrorField];
        }

        if (!isset($this->CurrentPanel) && isset($_GET['panel'])) {
            $this->CurrentPanel = $_GET['panel'];
        }

        $PanelArray = $this->GetPanelArray();

        $ValidPanel = false;
        if (is_array($PanelArray)) {
            foreach ($PanelArray as $PanelName => $Sections) {
                if (in_array($this->CurrentPanel, $Sections, true) || $this->CurrentPanel == $PanelName) {
                    $ValidPanel = true;
                    $this->CurrentPanel = $PanelName;
                }
            }
        }

        if (!$ValidPanel) {
            $this->CurrentPanel = null;
        }
    }

    /**
     * @desc Gets the HTML for the left hand side menu (not the floating one).
     *
     * @return string HTML for the left hand side menu
     */
    public function GetHTML(array $userLabels): string
    {
        if (empty($this->Module)) {
            return '';
        }

        $panelLinks = '';
        $otherLinks = '';
        $columnPadding = '';
        $enhancedAccessibility = '';

        // Use table object to generate panel links as well as creating the page back link
        if ($this->Table || $this->Panels || $this->FormArray || $this->showAdminBackLink) {
            $panelLinks = $this->GetPanelMenuHTML($userLabels);
        }

        // Construct static module-based links.
        if ($this->showMenuLinks) {
            $otherLinks = $this->GetOtherLinkHTML();
        }

        if ($this->registry->getParm('ENHANCED_ACCESSIBILITY', 'N')->isTrue()) {
            $enhancedAccessibility = '<a class="skip-nav" href="#main-content">Skip side navigation</a>';
        }

        return '<div id="sidebar-section" class="col collapse">' .
            $enhancedAccessibility .
            $panelLinks .
            $otherLinks .
        '</div>';
    }

    /**
     * @desc Constructs and returns the static links that appear below the panels (e.g. Print)
     *
     * @return string HTML for these links
     */
    public function GetOtherLinkHTML()
    {
        global $scripturl, $ModuleDefs;

        $isFullAdmin = (new UserSessionFactory())->create()->isFullAdmin();

        $LinkArray = [];

        if ($this->Table && $this->Table->FormMode != 'New' && $this->Table->FormMode != 'Link' && $this->Table->FormMode != 'Search' && $this->Table->FormMode != 'Design') {
            if ($this->PrintLink) {
                if ($this->printLinkUrl) {
                    $LinkArray[] = [
                        [
                            'label' => _fdtk('print'),
                            'onclick' => 'SendTo(\'' . $this->printLinkUrl . '\', \'_blank\')',
                        ], ];
                } elseif ($this->Module == 'AST') {
                    $LinkArray[] = [
                        [
                            'label' => _fdtk('print'),
                            'onclick' => 'SendTo('
                                . "'"
                                    . getRecordURL(['module' => $this->Module, 'recordid' => $this->Recordid, 'linkMode' => $this->isLinkedRecord])
                                    . '&print=1'
                                    . (!empty($_GET['full_audit']) ? '&full_audit=1' : '')
                                    . (!empty($_GET['fromsearch']) ? '&fromsearch=1' : '')
                                    . ($this->PrintLinkParams ? '&' . http_build_query($this->PrintLinkParams) : '')
                                    . '&token=' . CSRFGuard::getCurrentToken()
                                . "',"
                                . "'_blank'"
                            . ')',
                        ],
                    ];
                } else {
                    $LinkArray[] = [
                        [
                            'label' => _fdtk('print'),
                            'onclick' => 'SendTo('
                                . "'"
                                    . getRecordURL(['module' => $this->Module, 'recordid' => $this->Recordid])
                                    . '&print=1'
                                    . (!empty($_GET['full_audit']) ? '&full_audit=1' : '')
                                    . (!empty($_GET['fromsearch']) ? '&fromsearch=1' : '')
                                    . ($this->PrintLinkParams ? '&' . http_build_query($this->PrintLinkParams) : '')
                                    . '&token=' . CSRFGuard::getCurrentToken()
                                . "',"
                                . "'_blank'"
                            . ')',
                        ],
                    ];
                }
            }

            if ($this->Module == Module::INCIDENTS && $this->registry->getParm('SAVE_DIF1_SNAPSHOT', 'N')->toBool() && $isFullAdmin) {
                // need to check whether a snapshot exists for this record.
                $snapshotId = DatixDBQuery::PDO_fetch('SELECT count(*) as num FROM inc_submission_snapshot WHERE recordid = :recordid', ['recordid' => $this->Recordid], PDO::FETCH_COLUMN);

                if ($snapshotId == 1) {
                    $AuditArray[] = ['label' => _fdtk('show_dif1_snapshot'), 'new_window' => true, 'link' => 'action=showsnapshot&recordid=' . $this->Recordid];
                }
            }

            if (($this->Module == 'DEL' || $this->registry->getParm($this->Module . '_SHOW_AUDIT', 'Y')->toBool()) && $this->AuditLink) {
                if ($this->Module == 'INC' && $this->approvalStatus !== 'STCL') {
                    $AuditArray[] = ['label' => _fdtk('show_dif1_values'), 'external_link' => true, 'link' => getRecordURL(['module' => $this->Module, 'recordid' => $this->Recordid]) . '&show_dif1_values=1'];
                }

                $baseLink = getRecordURL(['module' => $this->Module, 'recordid' => $this->Recordid]) . ($_REQUEST['fromsearch'] ? '&fromsearch=1' : '') . (isset($_REQUEST['frommainrecord']) && $_REQUEST['frommainrecord'] != '' ? '&frommainrecord=' . $_REQUEST['frommainrecord'] : '') . (isset($_REQUEST['main_recordid']) && $_REQUEST['main_recordid'] != '' ? '&main_recordid=' . $_REQUEST['main_recordid'] : '') . (isset($_REQUEST['module']) && $_REQUEST['module'] != '' ? '&module=' . $_REQUEST['module'] : '') . (isset($_REQUEST['action']) && $_REQUEST['action'] == 'linkequipment' ? '&LinkMode=' . $_REQUEST['action'] : '');

                if ($_GET['full_audit'] ?? false) {
                    $AuditArray[] = [
                        'label' => _fdtk('close_audit_trail'),
                        'external_link' => true,
                        'link' => $baseLink,
                    ];
                } else {
                    $AuditArray[] = [
                        'label' => _fdtk('audit_trail'),
                        'external_link' => true,
                        'link' => $baseLink . '&full_audit=1',
                    ];
                }

                $LinkArray[] = $AuditArray;
            }
        }

        if ($this->MenuArray) {
            $menuArray = $this->MenuArray;
        } else {
            $module = $this->MenuModule ?: $this->Module;
            $menuArray = (new SubMenuFactory())->create($module, 'leftMenu', CanAccessRecord($module))->getItems();
        }

        if (!empty($menuArray)) {
            $LinkArray[] = $menuArray;
        }

        if ($this->Module === Module::FEEDBACK) {
            if (
                isset($_GET['action']) && $_GET['action'] === 'home'
                && $this->registry->getParm('KO41_ENABLED', 'N')->isTrue()
                && (
                    $isFullAdmin
                    || $this->registry->getUserParm((new UserSessionFactory())->create()->getCurrentLogin(), 'COM_KO41_CAN_GENERATE_REPORT', 'N')->isTrue()
                )
            ) {
                $scripturl = $this->registry->getScriptUrl();
                $KO41Title = _fdtk('ko41_export_title');
                $KO41Action = 'index.php?action=httprequest&type=ko41export';
                $KO41Link = "javascript:PopupDivFromURL(
                    'ko41export',
                    '" . $KO41Title . "',
                    '" . $KO41Action . "',
                    '',
                    [
                        {'value':'" . _fdtk('btn_export') . "','onclick':'var year = jQuery(\'input#ko41Year\').val(); var quarter = jQuery(\'input#ko41Quarter\').val(); window.open(\'" . $scripturl . "?action=ko41generate&year=\'+year+\'&quarter=\'+quarter, \'_self\'); GetFloatingDiv(\'ko41export\').CloseFloatingControl();'},
                        {'value':'" . _fdtk('btn_cancel') . "','onclick':'GetFloatingDiv(\'ko41export\').CloseFloatingControl();'}
                    ],
                    '360px',
                    '',
                    '');";

                $LinkArray[] = [
                    [
                        'label' => _fdtk('generate_ko41_report'),
                        'onclick' => $KO41Link,
                    ],
                ];
            }

            if ($this->registry->getParm('ENABLE_EDIT_FEEDBACK_CHAIN_HISTORY', 'N')->isTrue()
                && (new Request())->getParameter('action') === 'home') {
                $LinkArray[] = [
                    [
                        'label' => _fdtk('edit_feedback_chain_history'),
                        'link' => 'action=editfeedbackchainhistory&module=' . $this->Module,
                    ],
                ];
            }
        }

        // add batch delete link
        if ($this->Listing) {
            if (!($this->Module == 'CON' && $_SESSION['CON']['DUPLICATE_SEARCH'])) {
                if ((new UserSessionFactory())->create()->isBatchDeleteAdmin()) {
                    $LinkArray[] = [['label' => _fdtk('batch_delete'), 'link' => 'service=BatchDelete&event=collectparameters&module=' . $this->Module]];
                }
                if ($this->checkPerModuleBatchUpdateAccess || $this->registry->getParm('ENABLE_BATCH_UPDATE', 'N')->toBool()) {
                    $LinkArray[] = [['label' => _fdtk('batch_update'), 'link' => 'action=batchupdate&module=' . $this->Module]];
                }
            }
            if (($isFullAdmin || $this->registry->getParm('CON_ALLOW_MERGE_DUPLICATES', 'N')->toBool()) && ($_SESSION['CON']['DUPLICATE_SEARCH'] ?? false) && $this->Module == 'CON') {
                $LinkArray[] = [['label' => _fdtk('batch_merge'), 'link' => 'service=BatchMerge&event=collectparameters&module=' . $this->Module]];
            }
        }

        if (!empty($this->ExtraLinks)) {
            $LinkArray[] = $this->ExtraLinks;
        }

        if ($this->Action == 'designabasereport') {
            parse_str($_SERVER['QUERY_STRING'], $query_output);

            $query_output['show_excluded'] = $_SESSION['reports_administration:show_excluded'] == '1' ? '0' : '1';
            unset($query_output['token']);

            $LinkArray[] = [['label' => ($_SESSION['reports_administration:show_excluded'] == '1' ? _fdtk('hide_excluded_forms_fields') : _fdtk('show_excluded_forms_fields')), 'link' => http_build_query($query_output)]];
        }

        if ($this->Action === 'formdesignsetup') {
            $LinkArray[] = [['label' => _fdtk('adm_back_to_form_design_list'), 'link' => 'action=listformdesigns', 'bold' => true]];
        }

        if ($this->Action === 'codesetupsaction') {
            $LinkArray[] = [['label' => _fdtk('adm_back_to_code_setups'), 'link' => 'action=codesetupslist', 'bold' => true]];
        }

        if (($this->Module === Module::INCIDENTS)
            && ($this->Table !== null)
            && ($this->Table->FormMode === 'New')
            && Container::get(DatixConfig::class)->getPsimsEnabled()
            && $this->Table->getFormDesign()->isLfpseFieldsHidable()
        ) {
            $LinkArray[] = [['html' => Container::get(PsimsMandatoryQuestionsService::class)->getMandatoryQuestionsHtml()]];
        }

        $HTML = '';

        if (is_array($LinkArray)) {
            foreach ($LinkArray as $LinkList) {
                if (is_array($LinkList)) {
                    $ItemHTML = '';

                    // TODO: These changes are temporary just to keep the left hand menu working, this eventually will be
                    // removed due to the hamburger dropdown menu
                    foreach ($LinkList as $id => $menuitem) {
                        if (is_array($menuitem)) {
                            if (($menuitem['condition'] ?? null) !== false) {
                                if (isset($menuitem['menu_name'])) {
                                    foreach ($menuitem['items'] as $Item) {
                                        if (\UnicodeString::strpos($Item['link'], $this->Module) || (isset($Item['display_always']) && $Item['display_always'])) {
                                            $ItemHTML .= $this->buildMenuItems($Item, $id);
                                        }
                                    }
                                } else {
                                    $ItemHTML .= $this->buildMenuItems($menuitem, $id);
                                }
                            }
                        } else {
                            if ($menuitem->getCondition()) {
                                if ($menuitem->getLink() && $this->Module !== 'ADM') {
                                    $classes = '';
                                    if ($menuitem->getClassName() != '') {
                                        $classes .= ' ' . $menuitem->getClassName();
                                    }
                                    if ($menuitem->isLinkSelected()) {
                                        $classes .= ' selected';
                                    }
                                    $ItemHTML .= '<li class="list-item' . $classes . '" id="' . $id . '"><a href="javascript:if(CheckChange()){SendTo(\'' . ($menuitem->getExternalLink() ? '' : $scripturl . '?') . $menuitem->getLink() . '\', \'' . ($menuitem->getNewWindow() ? '_blank' : '') . '\');}"><span class="sidebar-menu-name">' .
                                        $menuitem->getLabel() .
                                        '</span></a></li>';
                                }
                            }
                        }
                    }

                    if ($ItemHTML != '') {
                        $HTML .= '
                            <div class="option-nav">
                                <ul>
                        ';

                        $HTML .= $ItemHTML;

                        $HTML .= '
                                </ul>
                            </div>
                        ';
                    }
                }
            }
        }

        return $HTML;
    }

    /**
     * @desc Constructs the HTML for the "Current record" section of the side menu, which contains links to the form panels.
     *
     * @return string the HTML of the menu options
     */
    public function GetPanelMenuHTML(array $userLabels): string
    {
        global $Show_all_section;

        $panelArray = $this->GetPanelArray();

        $menu = '<div class="form-nav">';

        if ($this->showBackLink || $this->showAdminBackLink) {
            $menu .= $this->addBackLink();
        }

        $firstPanelDone = false;

        if (is_array($panelArray) && !empty($panelArray) && (!$this->Table || $this->Table->getFormLevel() != 1)) {
            foreach ($panelArray as $panelName => $panelDetails) {
                $classes = ['list-item'];

                if (!$firstPanelDone) {
                    $classes[] = 'top';
                    $firstPanelDone = true;

                    if (!isset($this->CurrentPanel)) {
                        $classes[] = 'selected';
                    }
                }

                if (isset($this->CurrentPanel) && $this->CurrentPanel === $panelName) {
                    $classes[] = 'selected';
                }

                $menu .= '<div id="menu-' . $panelName . '" class="' . implode(' ', $classes) . '">';

                if (bYN($Show_all_section)) {
                    $queryString = $_SERVER['QUERY_STRING'];

                    // Check for a modal popup to use JS for the scroll to section
                    $menu .= ($queryString !== 'action=generateLinkedDataModal')
                        ? "<a href=\"?{$queryString}#panel-{$panelName}\">"
                        : "<a data-modal-scroll-element='{$panelName}'>";
                } else {
                    $module = isset($panelDetails['Stateful']) ? "data-module=\"{$panelName}\"" : '';
                    $menu .= '<a data-panel-id="panel-' . $panelName . '" data-show-panel="true" ' . $module . '><span class="sidebar-menu-name">';
                }

                $menu .= $this->addPanelLabel($panelName, $panelDetails, $userLabels);
            }
        }

        $menu .= '</div>';

        return $menu;
    }

    /**
     * @desc Constructs and returns the HTML for the floating left hand menu.
     *
     * @return string the HTML for this menu
     */
    public function GetFloatingMenuHTML()
    {
        global $Show_all_section, $UserLabels, $JSFunctions;

        if ($this->Table) {
            $FormArray = $this->Table->getFormArray();
        }

        if ($_GET['action'] != 'newdif1') {
            $PanelArray = $this->GetPanelArray();
        }
        $FloatingMenu = '
        <div id="site-bottom-bar" class="fixed-position">
            <div id="site-bottom-bar-frame">
                <div id="site-bottom-bar-content">';

        if ($this->Buttons) {
            $FloatingMenu .= $this->Buttons->getSideMenuHTML();
        }

        $FloatingMenu .= '
                    <div id="menu-2">
                        <ul>
        ';

        $FloatingMenu .= '
                           </ul>
                    </div>

                </div>

            </div>
        </div>';

        return $FloatingMenu;
    }

    /**
     * @desc Builds a menu item for left hand side menu.
     *
     * @param $Item Menu item with all parameters
     * @param $ItemId Menu item id
     *
     * @return string HTML for this menu item
     */
    protected function buildMenuItems($Item, $ItemId)
    {
        global $scripturl;

        $html = '';

        if (($Item['condition'] ?? null) !== false) {
            if (isset($Item['html'])) {
                $html .= '<li id="' . $ItemId . '">' . $Item['html'] . '</li>';
            } elseif (isset($Item['link'])) {
                $stateful = isset($Item['stateful']) ? ' data-stateful="true"' : '';
                $target = ($Item['new_window'] ?? false) ? '_blank' : '';
                $html .= '<li id="' . $ItemId . '"><a href="javascript:if(CheckChange()){SendTo(\'' . (($Item['external_link'] ?? false) ? '' : $scripturl . '?') . $Item['link'] . '\', \'' . $target . '\');}"' . $stateful . '><span class="sidebar-menu-name">' .
                    (($Item['icon_blue'] ?? false) ? '<img src="' . $Item['icon_blue'] . '" alt="' . $Item['alt'] . '" /> ' : '') .
                    (($Item['bold'] ?? false) ? '<b>' . (!empty($Item['lhsm_label']) ? $Item['lhsm_label'] : $Item['label']) . '</b>' : (!empty($Item['lhsm_label']) ? $Item['lhsm_label'] : $Item['label'])) .
                    '</span></a></li>';
            } elseif (isset($Item['onclick'])) {
                $html .= '<li id="' . $ItemId . '"><a href="javascript:if(CheckChange()){' . $Item['onclick'] . ';}"><span class="sidebar-menu-name">' .
                    (isset($Item['icon_blue']) ? '<img src="' . $Item['icon_blue'] . '" alt="' . $Item['alt'] . '" /> ' : '') .
                    (isset($Item['bold']) ? '<b>' . (!empty($Item['lhsm_label']) ? $Item['lhsm_label'] : $Item['label']) . '</b>' : (!empty($Item['lhsm_label']) ? $Item['lhsm_label'] : $Item['label'])) .
                    '</span></a></li>';
            }
        }

        return $html;
    }

    /**
     * @desc Finds the array of panels to use for the panel displays on both left hand menus.
     *
     * @return array an array of panels
     */
    protected function GetPanelArray()
    {
        $PanelArray = [];

        if ($this->Table) {
            $PanelArray = $this->Table->getPanelArray();
            $FormArray = $this->Table->getFormArray();
        } elseif ($this->FormArray) {
            foreach ($this->FormArray as $key => $details) {
                if ($details['NewPanel']) {
                    $PanelArray[$key] = ['Label' => $details['Title']];
                }
            }
        } elseif ($this->Panels) {
            $PanelArray = $this->Panels;
        }

        return $PanelArray;
    }

    /**
     * Sets module and label values for admin links as admin pages may not have the main admin module set.
     */
    private function addBackLink(): string
    {
        $linkModule = $this->showAdminBackLink ? 'ADM' : $this->Module;
        $linkLabel = $this->showAdminBackLink ? _fdtk('back_to_admin') : _fdtk('back_to_dashboard');

        return '<div id="form-go-back" class="list-item">
                    <a href="index.php?action=home&module=' . $linkModule . '">
                        <span class="fa fa-caret-left"></span><span class="sidebar-menu-name">' . $linkLabel . '
                    </span></a>
                </div>';
    }

    private function addPanelLabel(string $panelName, array $panelDetails, array $userLabels): string
    {
        $UserLabels = [];
        if ($this->Table instanceof FormTable) {
            $UserLabels = $this->Table->getFormDesign()->getUserLabels();
        }

        $label = '';
        $formArray = $this->Table ? $this->Table->getFormArray() : [];
        $language = $this->languageSession->getLanguage();

        // We cannot always assume the global is up to date, and so we should use already calculated labels if possible
        if (!empty($userLabels)
            && array_key_exists($panelName, $userLabels)
        ) {
            $label = $userLabels[$panelName];
        }

        // Make sure $UserLabels exists, that $PanelName exists in $UserLabels and a value for the current language exists for the Panel
        if (empty($label)
            && is_array($UserLabels)
            && array_key_exists($panelName, $UserLabels)
            && array_key_exists($language, $UserLabels[$panelName])
        ) {
            $label = $UserLabels[$panelName][$language];
        }

        // If $label is empty and MenuTitle parameter exists for current panel
        if (empty($label) && !empty($formArray[$panelName]['MenuTitle'])) {
            $label = $formArray[$panelName]['MenuTitle'];
        }

        // If $label is empty and Title parameter exists for current panel
        if (empty($label) && !empty($formArray[$panelName]['Title'])) {
            $label = $formArray[$panelName]['Title'];
        }

        // If the $label is still empty use default label from form design file
        if (empty($label)) {
            $label = $panelDetails['Label'];
        }

        $label = trim(strip_tags($label));

        return $label . '</span></a>
                    </div>';
    }
}
