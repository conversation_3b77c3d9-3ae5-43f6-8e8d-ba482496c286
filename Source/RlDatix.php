<?php

namespace Source;

use app\models\framework\config\DatixConfig;
use app\models\framework\routing\specifications\AuthenticationRequiredForActionSpecification;
use app\models\generic\valueObjects\Module;
use app\services\licence\LicensedModuleManager;
use CSRFGuard;
use DatixDBQuery;
use Exception;
use Firebase\JWT\JWT;
use PermissionDeniedException;
use Psr\Log\LoggerInterface;
use Sanitize;
use src\admin\services\factories\MaintenanceModeFilterExecutorFactory;
use src\framework\controller\Controller;
use src\framework\controller\Loader;
use src\framework\model\LegacyRoute;
use src\framework\model\Route;
use src\framework\model\RouteInterface;
use src\framework\registry\Registry;
use src\framework\session\UserSessionFactory;
use src\generic\services\DefaultModuleProviderFactory;
use src\logger\Facade\Log;
use src\Router;
use src\system\container\facade\Container;
use src\users\model\UserModelFactory;

use function checkGeneralMigrationNeeded;
use function header;
use function MigrateForms;
use function obExit;
use function SetSessionActive;
use function ini_get;
use function is_array;
use function in_array;
use function strlen;

class RlDatix
{
    public static function main(): void
    {
        $registry = Container::get(Registry::class);
        $logger = Container::get(LoggerInterface::class);

        $action = $_GET['action'] ?? '';

        if (!ini_get('max_input_vars') || ini_get('max_input_vars') === '1000') {
            fatal_error(_fdtk('max_input_vars_error'));
        }

        self::migrateForm($action);

        if ($action !== 'httprequest' && !isset($_SESSION['licensedModules'])) {
            $_SESSION['licensedModules'] = Container::get(LicensedModuleManager::class)
                ->getLicencedModulesByNumericalCode();
        }

        // =================================================
        //  Check if upload doesn't exceed maximum limit
        // =================================================
        $uploadTooLarge = ($_SERVER['CONTENT_LENGTH'] ?? 0) > self::convertToBytes(ini_get('upload_max_filesize'));
        if ($uploadTooLarge) {
            $logger->warning('Document failed to upload as it exceeded the maximum allowed filesize');
        }

        self::csrfProtection($uploadTooLarge);

        // Whitelist logged out pages
        if ((new AuthenticationRequiredForActionSpecification())->isSatisfiedBy($action)) {
            LoggedIn();
        }

        /**
         * Redirects the request if in maintenance mode.
         *
         * @see MaintenanceModeFilterExecutor::execute()
         * @see MaintenanceModeFilter::shouldRedirect()
         * @see MaintenanceModeController::maintenanceMode()
         */
        (new MaintenanceModeFilterExecutorFactory())
            ->create()
            ->execute($_GET['action'] ?? null, $_GET['type'] ?? null);

        $loader = new Loader();

        $userSession = (new UserSessionFactory())->create();

        $router = new Router($userSession);
        $route = $router->get($action);

        if (self::shouldCallRoute($route)) {
            self::callRoute($route, $action, $loader);
        }

        if (self::shouldCallLegacyRoute($route)) {
            self::callLegacyRoute($route);
        }

        self::legacyServiceRouting();

        if ($userSession->isLoggedIn()) {
            $controller = $loader->getController($router->get('home')->getControllerInfo());
            echo $controller->doAction('home');
            obExit();
        }

        self::loggedOutForms($loader, $router);
    }

    /**
     * @desc Gets all the parent/child combinations from the database and stores them in the session as
     * $_SESSION['CachedValues']['ParentChild'][<module>][<field>] = array of children.
     * and $_SESSION['CachedValues']['ChildParent'][<module>][<field>] = array of parents.
     *
     * @todo find a better place
     * @todo refactor
     * @todo user file cache
     *
     * @global array $ModuleDefs
     */
    public static function cacheParents(): void
    {
        global $ModuleDefs;

        if (
            isset($_SESSION['CachedValues']['ParentChild'])
            && is_array($_SESSION['CachedValues']['ParentChild'])
            && isset($_SESSION['CachedValues']['ChildParent'])
            && is_array($_SESSION['CachedValues']['ChildParent'])
        ) {
            return;
        }

        $sql = 'SELECT cmb_module, cmb_parent, cmb_child, cmb_table
        FROM combo_links
        WHERE cmb_parent is not null AND cmb_parent != \'\'
        AND cmb_child is not null AND cmb_child != \'\'
        ORDER BY recordid';

        $parents = DatixDBQuery::PDO_fetch_all($sql);

        foreach ($parents as $parent) {
            if ($parent['cmb_table'] != $ModuleDefs[$parent['cmb_module']]['TABLE']) {
                $parent['cmb_parent'] = CheckFieldMappings($parent['cmb_module'], $parent['cmb_parent']);
                $parent['cmb_child'] = CheckFieldMappings($parent['cmb_module'], $parent['cmb_child']);
            } else {
                // workaround required because subjects fields are referenced as com_, but referenced for parenting as csu_
                if (in_array($parent['cmb_child'], ['com_subject', 'com_subsubject', 'com_stafftype'], true)) {
                    $parent['cmb_child'] = CheckFieldMappings($parent['cmb_module'], $parent['cmb_child']);
                }
            }

            $_SESSION['CachedValues']['ParentChild'][$parent['cmb_module']][$parent['cmb_parent']][] = $parent['cmb_child'];
            $_SESSION['CachedValues']['ChildParent'][$parent['cmb_module']][$parent['cmb_child']][] = $parent['cmb_parent'];
        }

        // get children from field formats too
        $sql =
            "SELECT DISTINCT fmt_field, fmt_module, fmt_code_parent, fmt_code_parent2, fmt_table
        FROM field_formats
        WHERE
            ((fmt_code_parent != '' AND fmt_code_parent IS NOT NULL) OR (fmt_code_parent2 != '' AND fmt_code_parent2 IS NOT NULL))
            AND fmt_field NOT IN (
                SELECT cmb_child
                FROM combo_links
                WHERE
                    cmb_module = fmt_module
                    AND cmb_table = fmt_table
                    AND cmb_child is not null
                    AND cmb_child != ''
                )";

        $children = DatixDBQuery::PDO_fetch_all($sql);

        foreach ($children as $child) {
            if ($child['fmt_table'] != $ModuleDefs[$child['fmt_module']]['TABLE']) {
                $child['fmt_field'] = CheckFieldMappings($child['fmt_module'], $child['fmt_field']);
                $child['fmt_code_parent'] = CheckFieldMappings($child['fmt_module'], $child['fmt_code_parent']);
                $child['fmt_code_parent2'] = CheckFieldMappings($child['fmt_module'], $child['fmt_code_parent2']);
            }

            if ($child['fmt_code_parent']) {
                $_SESSION['CachedValues']['ParentChild'][$child['fmt_module']][$child['fmt_code_parent']][] = $child['fmt_field'];
                $_SESSION['CachedValues']['ChildParent'][$child['fmt_module']][$child['fmt_field']][] = $child['fmt_code_parent'];
            }

            if ($child['fmt_code_parent2']) {
                $_SESSION['CachedValues']['ParentChild'][$child['fmt_module']][$child['fmt_code_parent2']][] = $child['fmt_field'];
                $_SESSION['CachedValues']['ChildParent'][$child['fmt_module']][$child['fmt_field']][] = $child['fmt_code_parent2'];
            }
        }
    }

    public static function setHeaders(): void
    {
        /** @global DatixConfig $config */
        global $config;
        // Ensure that this page request can only be loaded into an iframe from the same domain. (prevents XFS attacks)
        header('X-Frame-Options:SAMEORIGIN');

        $corsAllowedOrigins = $config->getCorsAllowedOrigins();
        foreach ($corsAllowedOrigins as $allowedOrigin) {
            header('Access-Control-Allow-Origin: ' . $allowedOrigin, false);
        }
    }

    public static function startSession(): void
    {
        /**
         * @var DatixConfig $config
         */
        global $config;

        $logger = Container::get(LoggerInterface::class);
        // Get user and session ids from JWT token stored on cookie
        $jwtUserId = null;
        $jwtAuthenticatedUserId = null;

        if (!empty($_COOKIE['datix_jwtToken'])) {
            try {
                $jwtToken = JWT::decode(
                    $_COOKIE['datix_jwtToken'],
                    $config->getJWTSecret(),
                );
                $jwtUserId = $jwtToken->userId;
                $jwtAuthenticatedUserId = $jwtToken->authenticatedUserId;

                // remove logged out session
                setcookie('unauthenticated_session', '', time() - 3600, '', '', true);

                if (empty($jwtToken->sessionId)) {
                    $logger->warning('Invalid token (missing session id)');

                    header('Clear-Site-Data: "cache","cookies","storage"');
                    header("Location: {$config->getLogoutUrl()}");

                    exit;
                }

                $session_id = $jwtToken->sessionId;
            } catch (Exception $e) {
                // we shouldn't have an expired cookie here unless kong has missed something. Log and return to login screen
                Log::error('JWT stored in cookie datix_jwtToken could not be processed.', [
                    'exception' => $e,
                    'token' => $_COOKIE['datix_jwtToken'],
                ]);

                $from = urlencode($config->getScriptUrl() . '?' . $_SERVER['QUERY_STRING']);

                header('Clear-Site-Data: "cache","cookies","storage"');
                header("Location: {$config->getLoginUrl()}?from={$from}");
                obExit();
            }
        } else {
            // logged out user also have a session
            $session_id = $_COOKIE['unauthenticated_session'] ?? null;
            if (empty($session_id)) {
                $session_id = session_create_id();
                setcookie('unauthenticated_session', session_create_id(), time() + 3600, '', '', true);
            }
        }

        session_id($session_id);
        session_start();

        if (!isset($_SESSION['Globals'])) {
            getParms();
        }

        $userSession = (new UserSessionFactory())->create();

        if ($jwtUserId !== null) {
            if ($userSession->getCurrentUser() === null) {
                $userMapper = (new UserModelFactory())->getMapper();
                $user = $userMapper->findById($jwtUserId);

                if (!$user) {
                    setcookie('datix_jwtToken', '', time() - 3600, '/', null, true);
                    $logger->error('Session User ID: no corresponding user found for the User ID from JWT');

                    header('Clear-Site-Data: "cache","cookies","storage"');
                    header("Location: {$config->getLogoutUrl()}");

                    exit;
                }

                $userSession->initSession($user, $jwtAuthenticatedUserId);

                DatixDBQuery::CallStoredProcedure(
                    [
                        'procedure' => 'NewSession',
                        'parameters' => [
                            ['@application', 'WEB', 'SQLVARCHAR'],
                            ['@userid', $_SESSION['contact_login_id'], 'SQLINT4'],
                            ['@session_id', &$_SESSION['session_id'], 'SQLINT4', true],
                            ['@hostname', getenv('REMOTE_ADDR'), 'SQLVARCHAR'],
                        ],
                    ],
                );
            }

            if ((string) $userSession->getCurrentUser()->recordid !== (string) $jwtUserId) {
                setcookie('datix_jwtToken', '', time() - 3600, '/', null, true);
                $logger->error('Session User ID: does not match the User ID from JWT', [
                    'session_user_id' => $userSession->getCurrentUser()->recordid,
                    'token_user_id' => $jwtUserId,
                ]);

                header('Clear-Site-Data: "cache","cookies","storage"');
                header("Location: {$config->getLogoutUrl()}");

                exit;
            }
        }
    }

    public static function detectTimezone(): void
    {
        /**
         * @global DatixConfig $config
         */
        global $config;
        $logger = Container::get(LoggerInterface::class);
        $registry = Container::get(Registry::class);
        $xhrHeader = getallheaders()['X-Requested-With'] ?? null;
        if (($_GET['action'] ?? null) !== 'httprequest'
            && $registry->getParm('DETECT_TIMEZONES', 'N', true)->isTrue()
            && !isset($_SESSION['Timezone'])
            && $xhrHeader !== 'XMLHttpRequest'
        ) {
            $logger->info('Timezone is not set in session. Redirecting to timezone detection...');

            $addMinExtension = $registry->getDatixConfig()->isMinifierOn() ? '.min' : '';

            // If we don't reload after setting this timezone variable,
            // the date value won't be available until the next pageload.
            echo '
    <!--Timezone detection-->
    <script type="text/javascript" src="js_functions/jquery/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="js_functions/jquery/jquery-migrate-3.3.2.min.js"></script>
    <script type="text/javascript">var scripturl="' . $config->getScriptUrl() . '";</script>
    <script type="text/javascript" src="js_functions/timeZones' . $addMinExtension . '.js"></script>';

            exit;
        }
    }

    public static function removeHelpText(): void
    {
        if (!in_array($_GET['action'] ?? '', ['httprequest', 'fieldhelp'], true)) {
            // Ensure help text from previous pages isn't retained.
            unset($_SESSION['HelpTexts']);
        }
    }

    /**
     * The use of $excludeActionType allows routes that come from ajax requests to bypass a lot of the guff required for
     * template controllers so that they can return a clean response that can be used for json etc.
     *
     * @todo this is starting to get a bit silly as you need to add any action type that comes from a jquery ajax request that returns json or it'll fail
     */
    public static function xhrRequest(): void
    {
        $logger = Container::get(LoggerInterface::class);
        $registry = Container::get(Registry::class);
        $userSession = (new UserSessionFactory())->create();

        $action = $_GET['action'] ?? null;
        $excludedActionTypes = [
            'getparents',
            'codelist',
            'selectfunction',
            'copytemplatedoctoclient',
            'checkmergecontactprompt',
            'returnmergesql',
            'duplicaterecordcheck',
            'getdashboardhtml',
            'generatedatixtablerows',
            'exportdatixtablerows',
            'generatedatixtablecell',
            'datixtablesavechanges',
            'resettimer',
            'browsedomain',
            'getdynamicfielddata',
            'getlosttimerowhtml',
            'getcontactnumberhtml',
            'getrestrictedtimerowhtml',
            'classificationfieldfilter',
            'pendingusers',
        ];

        if (!($action === 'httprequest' && in_array($_GET['type'], $excludedActionTypes, true))) {
            DatixDBQuery::PDO_query('SET DATEFORMAT ymd');

            if (empty($_SESSION['Globals']) || !$userSession->isLoggedIn()) {
                GetParms($_SESSION['user'] ?? '');
            }

            AddCustomTextToJSGlobal();
            addGlobalsToJSGlobal();

            if ($userSession->isLoggedIn()) {
                DatixDBQuery::CallStoredProcedure(['procedure' => 'ClearExpiredRecords', 'parameters' => []]);

                if ($_SESSION['session_id'] && $action != 'logout' && ($action != 'httprequest' && ($_GET['type'] ?? null) != 'getservertimeout')) {
                    $sql = 'SELECT count(*) as total FROM SESSIONS WHERE RECORDID = :recordid';
                    $result = DatixDBQuery::PDO_fetch($sql, [':recordid' => $_SESSION['session_id']]);

                    DatixDBQuery::CallStoredProcedure(
                        [
                            'procedure' => 'SetSessionActive',
                            'parameters' => [
                                ['@session_id', $_SESSION['session_id'], 'SQLINT4'],
                                ['@lock_id', null, 'SQLINT4'],
                            ],
                        ],
                    );

                    if ($result['total'] == '0') {
                        $_GET['action'] = 'logout';
                        $_GET['no_session'] = '1';
                        $logger->debug('Automatic logout. Reason: No Datix session found');
                    }
                }
            }

            $_SESSION['DATE_FORMAT'] = $registry->getParm('FMT_DATE_WEB', 'GB')->is('US') ? 'mm/dd/yyyy' : 'dd/mm/yyyy';

            // Set the timezone using the global value if available
            date_default_timezone_set($registry->getParm('SYSTEM_TIMEZONE', 'Europe/London')->toScalar());
        }
    }

    public static function bufferOutput(): void
    {
        if (
            (!isset($_GET['action']) || $_GET['action'] !== 'document')
            && isset($_SERVER['HTTP_ACCEPT_ENCODING'])
            && substr_count($_SERVER['HTTP_ACCEPT_ENCODING'], 'gzip')
        ) {
            // GZIP HTML output if browser support it.
            ob_start('ob_gzhandler');
        } else {
            ob_start();
        }
    }

    private static function migrateForm(string $action): void
    {
        if ($action !== 'httprequest'
            && checkGeneralMigrationNeeded()
            && SetSessionActive()
            && !empty($_SESSION['logged_in'])
            && !empty($_SESSION['AdminUser'])
        ) {
            // hack to prevent page reload for dod.
            $_GET['action'] = 'formmigrate';
            MigrateForms();
            obExit();
        }
    }

    private static function csrfProtection(bool $uploadTooLarge): void
    {
        $registry = Container::get(Registry::class);

        $CSRFWhitelist = [
            'httprequest',
            'list',
            'reports',
            'reports2',
            'home',
            'dashboard',
            'codelist',
            'password',
            'drillin',
            'fieldhelp',
            'record',
            'incident',
            'risk',
            'sessionlost',
            'executequery',
            'gettagsbyid',
            'treefieldsuggestions',
            'nrlspending',
            'classificationfieldfilter',
            'pollpendingnrlsreport',
        ];

        // When file uploads exceed the maximum upload limit, the request is broken
        // and we don't get a token, but we still need to display a "file upload
        // exceeded" message, so we have to skip token verification (otherwise we
        // get a misleading "invalid token" error message)
        if ($registry->getParm('CSRF_PREVENTION', 'N')->isTrue() && !empty($_REQUEST['action']) && !$uploadTooLarge) {
            $CSRFGuard = new CSRFGuard($CSRFWhitelist);
            if (!$CSRFGuard->validateAuth($_REQUEST['action'], $_REQUEST['token'])) {
                throw new PermissionDeniedException('Invalid token.');
            }
        }
    }

    private static function legacyServiceRouting(): void
    {
        // call class method directly. This requires less overhead and doesn't require large arrays mapping to different functions.
        // provide $_GET parameters as local parameters to allow same execution whether called locally or via url.
        if (empty($_GET['action'])
            && !empty($_GET['service'])
            && !empty($_GET['event'])
            && method_exists('Service_' . $_GET['service'], $_GET['event'])
        ) {
            $Service = 'Service_' . $_GET['service'];
            $ServiceObj = new $Service($_GET);
            $event = $_GET['event'];
            $ServiceObj->{$event}($_GET);
            obExit();
        }
    }

    private static function shouldCallRoute(?RouteInterface $route): bool
    {
        if ($route === null) {
            return false;
        }

        if ($route->isLegacy()) {
            return false;
        }

        if (!class_exists($route->getController())) {
            return false;
        }

        if (!in_array(Controller::class, class_parents($route->getController()), true)) {
            return false;
        }

        return true;
    }

    /**
     * @return never
     */
    private static function callRoute(Route $route, string $action, Loader $loader): void
    {
        $controller = $loader->getController($route->getControllerInfo());
        echo $controller->doAction($action);
        obExit();
    }

    /**
     * @return never
     */
    private static function loggedOutForms(Loader $loader, Router $router): void
    {
        $module = Sanitize::getModule($_GET['module']) ?? '';
        $Level1Module = (new DefaultModuleProviderFactory())->create()->provide($module);
        $_GET['module'] = $Level1Module; // newrecord functions look at $_GET

        switch ($Level1Module) {
            case Module::CLAIMS:
            case Module::FEEDBACK:
            case Module::MORTALITY_REVIEW:
            case Module::REDRESS:
            case Module::SAFEGUARDING:
                // warning: cannot autoload, multi definitions of ListContacts()
                require_once 'Source/generic/MainRecord.php';
                NewRecord();

                break;
            case Module::INCIDENTS:
                // Use new Controller class structure
                $controller = $loader->getController($router->get('newdif1')->getControllerInfo());
                echo $controller->doAction('newdif1');

                break;
            default:
                LoggedIn();
        }
        obExit();
    }

    /**
     * @return false|int|string
     */
    private static function convertToBytes(string $from)
    {
        $val = trim($from);

        if (is_numeric($val)) {
            return $val;
        }

        $last = strtolower($val[strlen($val) - 1]);
        $val = substr($val, 0, -1); // necessary since PHP 7.1; otherwise optional

        switch ($last) {
            // The 'G' modifier is available since PHP 5.1.0
            case 'g':
                $val *= 1024;
            case 'm':
                $val *= 1024;
            case 'k':
                $val *= 1024;
        }

        return $val;
    }

    private static function callLegacyRoute(LegacyRoute $route)
    {
        $includeFile = $route->getFile();
        $executeFunction = $route->getFunction();

        if (!$route->getPassesConditions()) {
            fatal_error(_fdtk('no_perform_action'));
        }

        require_once $includeFile;
        $executeFunction();
    }

    private static function shouldCallLegacyRoute(?RouteInterface $route): bool
    {
        if ($route === null) {
            return false;
        }

        if ($route->isLegacy()) {
            return true;
        }

        return false;
    }
}
