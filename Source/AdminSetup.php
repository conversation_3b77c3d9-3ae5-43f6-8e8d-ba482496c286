<?php

use src\component\field\SelectFieldFactory;

function ChangeFieldSection()
{
    $Sections = ($_SESSION['FieldAdditionsSectionsSetup'] ?: []);

    if ($Sections[$_POST['originalsection']] && !$_POST['udf']) {
        $Sections[$_POST['originalsection']] .= ' [Default section]';
    }

    if ($Sections[$_POST['section']]) {
        $Sections[$_POST['section']] .= ' [Current section]';
    }

    foreach ($Sections as $SectionKey => $SectionName) {
        if ($_SESSION['RecursionLimitations']['movement'][$_POST['inputfield']] && in_array($SectionKey, $_SESSION['RecursionLimitations']['movement'][$_POST['inputfield']])) {
            // cannot move to this section because it could cause a recursion error.
            unset($Sections[$SectionKey]);
        }
    }

    // if the field has display locked than we need to clear sections from dropdown that have sections actions
    if (isset($_GET['lockdisplay']) && $_GET['lockdisplay'] == '1' && is_array($_SESSION['ExpandSections'])) {
        foreach ($_SESSION['ExpandSections'] as $Field) {
            foreach ($Field as $row) {
                $SectionToExpand = $row['section'];
                if (isset($Sections[$SectionToExpand])) {
                    unset($Sections[$SectionToExpand]);
                }
            }
        }
    }

    $TargetDropdown = SelectFieldFactory::createSelectField('sections', '', $_POST['section'], '');

    $TargetDropdown->setCustomCodes($Sections);

    // The following vars are used when generating the javascript for the page
    $inputfield = Sanitize::SanitizeString($_POST['inputfield']);
    $section = Sanitize::SanitizeString($_POST['section']);
    $originalsection = Sanitize::SanitizeString($_POST['originalsection']);

    echo '<script language="JavaScript" type="text/javascript">
function setReturns()
{
    var sectionListBox = document.getElementById("sections");

    var valuesReturn = "";

    if(sectionListBox.value || sectionListBox.selectedIndex)
    {
        jQuery("#CHANGEDSECTION-' . $originalsection . '-' . $inputfield . '").val(sectionListBox.value || sectionListBox.options[sectionListBox.selectedIndex].value);
        var NewSection = sectionListBox.value || sectionListBox.options[sectionListBox.selectedIndex].value;
    }
    else
    {
        jQuery("#CHANGEDSECTION-' . $originalsection . '-' . $inputfield . '").val("");
        var NewSection = "' . $originalsection . '";
    }

    jQuery("#NEWCHANGEDSECTION-' . $originalsection . '-' . $inputfield . '").val("1");

    jQuery("#FIELDORDER-' . $section . '-' . $inputfield . '").attr({
        \'name\' : \'FIELDORDER-"+NewSection+"-' . $inputfield . '\',
        \'id\' : \'FIELDORDER-"+NewSection+"-' . $inputfield . '\'
    });

    document.forms[0].btnSaveDesign.click();
    return true;
}

function returnToDefault()
{
    jQuery("#FIELDORDER-' . $section . '-' . $inputfield . '").attr({
        \'name\' : \'FIELDORDER-' . $originalsection . '-' . $inputfield . '\',
        \'id\' : \'FIELDORDER-' . $originalsection . '-' . $inputfield . '\'
    });

    jQuery("#CHANGEDSECTION-' . $originalsection . '-' . $inputfield . '").val("");
    jQuery("#NEWCHANGEDSECTION-' . $originalsection . '-' . $inputfield . '").val("1");

    document.forms[0].btnSaveDesign.click();
    return true;
}
</script>
';

    echo '
<table width="600px">
    <tr>
        <td>
            <table class="bordercolor" cellspacing="1" cellpadding="4" width="100%" align="center" border="0">
                <tr>
                    <td class="windowbg2" width="200px">
                    Move to section:
                    </td>
                    <td class="windowbg2">
                    ' . $TargetDropdown->GetField()
                    . '
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>';
}
