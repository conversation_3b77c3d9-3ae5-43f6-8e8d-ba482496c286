<?php

use app\models\contact\ContactTypes;
use app\models\framework\modules\ModuleRepository;
use app\models\generic\Tables;
use app\models\generic\valueObjects\Module;
use app\models\globals\ProgNotesEditGlobal;
use Source\generic_modules\FieldDefKeys;
use src\contacts\model\ContactsFields;
use src\framework\registry\Registry;
use src\incidents\model\IncidentsFields;
use src\incidents\model\PSIMSIncidentCodedFields;
use src\incidents\model\PSIMSIncidentEquipmentFields;
use src\incidents\model\PSIMSIncidentFields;
use src\incidents\model\PSIMSIncidentMedicationFields;
use src\incidents\model\PSIMSLinkContactsFields;
use src\redress\models\RedressFields;
use src\safeguarding\models\SafeguardingAdminFields;
use src\system\container\facade\Container;
use src\system\database\FieldInterface;

$registry ??= Container::get(Registry::class);
$moduleRepository ??= Container::get(ModuleRepository::class);
$investigationsIsLicensed = $moduleRepository->getModuleByCode('INV')->isEnabled();

try {
    $ribIsLicensed = $moduleRepository->getModuleByCode('RIB')->isEnabled();
} catch (Throwable $e) {
    $ribIsLicensed = false;
}

$IsAdminUser = $_SESSION['AdminUser'] ?? false;

$FieldDefs['INC'] = [
    'uuid' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'UUID',
    ],
    'location_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Location admitted',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'location_id_tag' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Location admitted tag',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'location_id',
    ],
    'service_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Service admitted',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'service_id_tag' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Service admitted tag',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'service_id',
    ],
    'other_location' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Other location',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'other_location_tag' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Other location tag',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'location',
        FieldDefKeys::RELATED_TREE_FIELD => 'other location',
    ],
    'exact_location' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Exact location',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'Width' => 50,
        'MaxLength' => 160,
    ],
    'confirm_location_id' => [
        FieldDefKeys::TYPE => FieldInterface::CHECKBOX_DB,
        FieldDefKeys::TITLE => 'Confirm Exact Location?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'confirm_service_id' => [
        FieldDefKeys::TYPE => FieldInterface::CHECKBOX_DB,
        FieldDefKeys::TITLE => 'Confirm Exact Service?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'other_service' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Other service',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'other_service_tag' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Other service tag',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::IS_TAG_FIELD => true,
        FieldDefKeys::MAPPER_TYPE => 'service',
        FieldDefKeys::RELATED_TREE_FIELD => 'other_service',
    ],
    'flag_for_investigation' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Flag for investigation?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'NoListCol' => !$investigationsIsLicensed,
        'BlockFromReports' => !$investigationsIsLicensed,
    ],
    'flag_for_rib' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Flag for RIB?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'NoListCol' => !$ribIsLicensed,
        'BlockFromReports' => !$ribIsLicensed,
    ],
    'inc_dincident' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Incident date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_time' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_time_band' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Time Band',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Description of incident',
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_actiontaken' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Immediate action taken',
        'Rows' => 7,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_outcome' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Outcome of investigation',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_dstart' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['inc_dincident'],
        FieldDefKeys::TITLE => 'Date investigation started',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_dcomp' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['inc_inv_dstart'],
        FieldDefKeys::TITLE => 'Date investigation completed',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_lessons' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Lessons learned',
        'Rows' => 7,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_lessons_sub_category' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Lessons learned sub category',
        'MaxLength' => 128,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inv_action' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Action taken',
        'Rows' => 7,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_action_code' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Action taken codes',
        'MaxLength' => 128,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_lessons_code' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Lessons learned codes',
        'MaxLength' => 128,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_root_causes' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Causes',
        'MaxLength' => 248,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_name' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Name',
        'Width' => 30,
        'MaxLength' => 128,
        'UpperCase' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_mgr' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Handler',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'StaffField' => true,
    ],
    'inc_head' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Manager',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'StaffField' => true,
    ],
    'inc_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type',
        'Child' => 'inc_category',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_causal_factors_linked' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were there any causal factors?',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_category' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Category',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_subcategory' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Subcategory',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_carestage' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Stage of care',
        'Child' => 'inc_clin_detail',
        'Child2' => 'inc_result',
        'DropdownWidth' => -1,
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_causal_factors' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were there any causal factors?',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_clin_detail' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Detail',
        'Parent' => 'inc_carestage',
        'Child' => 'inc_clintype',
        'DropdownWidth' => -1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_clintype' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Adverse event',
        'Parent' => 'inc_clin_detail',
        'DropdownWidth' => -1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_inquiry' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Further inquiry?',
        'DropdownWidth' => -1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_result' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Result',
        'Parent' => 'inc_carestage',
        'DropdownWidth' => -1,
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_severity' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Severity',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'sac_score' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'SAC Score',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'approved_by' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Finally approved by',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'StaffField' => true,
    ],
    'inc_result_initial' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Result (Initial)',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_severity_initial' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Severity (Initial)',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'sac_score_initial' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'SAC Score (Initial)',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SAC_DATE_DIFF_DAYS => [
        FieldDefKeys::TYPE => FieldInterface::NUMBER_DB,
        FieldDefKeys::TITLE => 'Days between Reported Date/Time and Confirmed SAC Date/Time',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SAC_DATE_DIFF_HOURS => [
        FieldDefKeys::TYPE => FieldInterface::NUMBER_DB,
        FieldDefKeys::TITLE => 'Hours between Reported Date/Time and Confirmed SAC Date/Time',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SAC_SCORE_DATE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Date SAC score first assigned',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SAC_SCORE_TIME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Time SAC score first assigned',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'anzco_coding' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Anzco coding',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'breakdown_agency' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Breakdown agency',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'agency_of_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Agency of injury',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'mech_of_injury_1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Mechanism of injury 1',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'mech_of_injury_2' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Mechanism of injury 2',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'nature_of_injury_1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Nature of injury 1',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'nature_of_injury_2' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Nature of injury 2',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'toocs_1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'TOOCS Body Part Grouping 1',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'toocs_2' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'TOOCS Body Part Grouping 2',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'health_service_site' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Health Service Site',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'rep_feedback_codes' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Feedback to reporter (codes)',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'rep_feedback_notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Feedback to reporter (notes)',
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_equipment' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was any equipment involved in the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_cost' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Cost',
        'Width' => 15,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_document' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Are there any documents to be attached to this record?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_person' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was any person involved in the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_employee' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was any employee involved in the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_witness' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Were there any witnesses to the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_other_contacts' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was any other contact involved in the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_medication' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was this a medication incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_repname' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Full name',
        'Width' => 32,
        'MaxLength' => 64,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_reportedby' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Job role/grade',
        'OldCodes' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_stage' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Stage of medication error',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_error' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Medication error',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_drug' => [
        'Type' => 'string_search',
        FieldDefKeys::TITLE => 'Drug administered',
        'Width' => 40,
        'MaxLength' => 64,
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_drug_rt' => [
        'Type' => 'string_search',
        FieldDefKeys::TITLE => 'Correct drug',
        'Width' => 40,
        'MaxLength' => 64,
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_form' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Form administered',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_form_rt' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Correct form',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_route' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Route administered',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_route_rt' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Correct route',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_dose' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Dose and strength administered',
        'Width' => 20,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_med_dose_rt' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Correct dose and strength',
        'Width' => 20,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_is_riddor' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'RIDDOR?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_dsched' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['inc_dopened'],
        FieldDefKeys::TITLE => 'Closed date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_dopened' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Opened date',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_submittedtime' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Submitted time',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_dreported' => [
        'Type' => 'date',
        'SetOnce' => true,
        'NotFuture' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['inc_dincident'],
        FieldDefKeys::TITLE => 'Reported date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'dum_inc_grading' => [
        'Type' => 'riskregister',
        FieldDefKeys::TITLE => 'Risk grading',
        'RiskRow' => 'grading',
        // Replaces Paramaters
        'NoListCol' => true,
        'MandatoryField' => 'inc_grade',
    ],
    'dum_inc_grading_initial' => [
        'Type' => 'riskregister',
        FieldDefKeys::TITLE => _fdtk('initial_risk_grading'),
        'RiskRow' => 'initial',
        // Replaces Paramaters
        'NoListCol' => true,
        'MandatoryField' => 'inc_grade_initial',
    ],
    'inc_ourref' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Reference',
        'Width' => 50,
        'MaxLength' => 32,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_cnstitype' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Codes',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_dnotified' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'RIDDOR Notified',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ridloc' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Location',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_localauth' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Local authority',
        'Width' => 64,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_acctype' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Accident type',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_riddor_ref' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'RIDDOR Ref',
        'Width' => 64,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_riddorno' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Disease/occurrence no.',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_further_inv' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Further investigation?',
        'DropdownWidth' => -1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_rc_required' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Cause analysis required?',
        'DropdownWidth' => -1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_report_npsa' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'DropdownWidth' => -1,
        FieldDefKeys::TITLE => 'Report to NRLS?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_dnpsa' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date exported',
        'ReadOnly' => true,
        'NoSave' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'recordid' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_rep_tel' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Telephone no.',
        'Width' => 15,
        'MaxLength' => 90,
        'isTelNumber' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_rep_email' => [
        'Type' => 'email',
        FieldDefKeys::TITLE => 'Your e-mail address',
        'Width' => 40,
        'MaxLength' => 120,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fbk_body' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Feedback to reporter',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
    ],
    'fbk_dsent' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date feedback sent to reporter',
        'NoListCol' => true,
    ],
    'fbk_email' => [
        'Type' => 'email',
        FieldDefKeys::TITLE => 'Send feedback to e-mail address',
        'NoListCol' => true,
    ],
    'fbk_subject' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Subject',
        'NoListCol' => true,
    ],
    'fbk_attachments' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Attachments',
        'NoListCol' => true,
    ],
    'inc_agg_issues' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Aggravating factors',
        'MaxLength' => 248,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_user_action' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Immediate action taken?',
        'MaxLength' => 248,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pol_crime_no' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Police crime reference number',
        'Width' => 40,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pol_called' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Police called?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'learnings_to_share' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Any Learnings or Outcomes to share?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'learnings_title' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TITLE => 'Learning Title',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'key_learnings' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Key Learnings and Outcomes',
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'RecordLinkBtn' => [
            'moduleTo' => 'LEA',
        ],
    ],
    'inc_pol_call_time' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time police called',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pol_attend' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Police attended?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pol_att_time' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time police attended',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pol_action' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Action taken by police',
        'MaxLength' => 248,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Notes',
        'Rows' => 10,
        'Columns' => 70,
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'notepad',
    ],
    'inc_pars_clinical' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were there clinical factors?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_address' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Address',
        'Rows' => 5,
        'Columns' => 50,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'NoSpellcheck' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pars_address' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Incident address',
        'Rows' => 5,
        'Columns' => 50,
        'MaxLength' => 60,
        'NoSpellcheck' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pars_pri_type' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'What type of incident was the perpetrator involved in?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pars_sec_type' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Non-physical assault sub-categories',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_tprop_damaged' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was trust property damaged/stolen?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    // link fields for equipment
    'link_damage_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of damage or loss',
        'NoListCol' => true,
    ],
    'link_other_damage_info' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'If other please describe',
        'Rows' => 5,
        'Columns' => 50,
        'NoListCol' => true,
    ],
    'link_replaced' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were item(s) replaced?',
        'NoListCol' => true,
    ],
    'link_replace_cost' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Cost of replacement',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'link_repaired' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were item(s) repaired?',
        'NoListCol' => true,
    ],
    'link_repair_cost' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Cost of repair',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'link_written_off' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were item(s) written off?',
        'NoListCol' => true,
    ],
    'link_disposal_cost' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Cost of disposal',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'link_sold' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were item(s) sold?',
        'NoListCol' => true,
    ],
    'link_sold_price' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Price sold for',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'link_decommissioned' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Were item(s) decommissioned?',
        'NoListCol' => true,
    ],
    'link_decommission_cost' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Cost of decommission',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'link_residual_value' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Residual value of item(s) at time of loss or damage',
        'NoListCol' => true,
        'MaxLength' => 11,
    ],
    'inc_pars_first_dexport' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'SIRS original export date',
        'ReadOnly' => true,
        'NoDefault' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pars_dexport' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'SIRS updated export date',
        'ReadOnly' => true,
        'NoDefault' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_postcode' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Postcode',
        'Width' => 10,
        'MaxLength' => 10,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_investigator' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Investigator',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'StaffField' => true,
    ],
    'inc_consultants' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Consultants',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'StaffField' => true,
    ],
    'inc_notify' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Notify',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Loss/damage',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_bodypart' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Item',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_unassisted_assisted' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the fall unassisted or assisted?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_observed_by' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Fall observed by?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_physical_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Did the patient sustain a physical injury as a result of the fall?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_patient_doing' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the patient doing or trying to do?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_patient_doing_other' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Other - What was the patient doing or trying to do?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'Rows' => 7,
        'Columns' => 70,
    ],
    'fall_risk_assessment' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was a fall risk assessment performed?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_protocols_in_place' => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'What protocols/interventions were in place, or being used, to prevent falls for this patient?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'fall_score' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Score or assessment',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_medication_risk' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'At time of the fall, was the patient on medication known to increase the risk of a fall?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_fell_from' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Fell from',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_post_fall_action' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Post Fall Action',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fall_risk_assessment_score' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Fall risk assessment score',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fire_detected' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'How Was the Fire Detected?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fire_cause_known' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Fire Cause Known?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fire_action_taken' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Action taken (fire)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'MaxLength' => 255,
    ],
    'fire_department_response' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Did The Local Fire Department Respond?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'fire_department_time_taken' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Time taken to reach location of the fire?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_date_transfusion_started' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date Transfusion Started',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_date_reaction_started' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date Apparent Reaction Started',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_time_transfusion_started' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time Transfusion Started',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_time_transfusion_stopped' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time Transfusion Stopped',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_time_reaction_started' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time Apparent Reaction started',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'blood_amount_transfused' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Amount Transfused',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_international_goals' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'International Patient Safety Goals',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'MaxLength' => 255,
    ],
    'spsc_national_goals' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'National Patient Safety Goals',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'MaxLength' => 255,
    ],
    'spsc_surgery_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of surgery',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_morbidity_anticipated' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Were terminal events/morbidity anticipated?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_reason_for_lama' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Reasons for LAMA',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_recovery_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Recovery Date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_visit_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of visit',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_hazardous_substance_type' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Type of hazardous substance',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'MaxLength' => 255,
    ],
    'spsc_injury_level' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Injury Level',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_code_white_gender' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Code White - Gender',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'spsc_code_white_person_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Code White - Person Injury',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'time_taken_to_submit' => [
        'Type' => 'duration',
        FieldDefKeys::TITLE => 'Time Taken To Submit',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'hospital' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Hospital/center where pressure ulcer is discovered?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'location' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Location of Pressure Ulcer',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'nature' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Nature of Pressure Ulcer',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'advanced_stage' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the most advanced stage of the pressure ulcer or suspected Deep Tissue Injury being reported?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'admission_status' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the status of the suspected Deep Tissue Injury on admission?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'admission_status_3_4' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the status of the Stage 3, 4, or unstageable pressure ulcer on admission?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'skin_inspection' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'On admission to this facility, was a skin inspection documented?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'risk_assessment' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'When was the first pressure ulcer risk assessment performed?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'risk_assessment_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What type of risk assessment was performed?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'increased_risk' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'As a result of the assessment, was the patient documented to be at increased risk for pressure ulcer?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'preventative_intervention_yn' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was any preventive intervention implemented?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'preventative_intervention' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'What intervention(s) was used?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
        'MaxLength' => 255,
    ],
    'preventative_intervention_detail' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Other Interventions : Please specify',
        FieldDefKeys::TABLE => 'pressure_ulcers',
        'Rows' => 7,
        'Columns' => 70,
    ],
    'device' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the use of a device or appliance involved in the development or advancement of the pressure ulcer?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'device_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the type of device or appliance?',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'device_detail' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Other type of device or appliance: Please specify',
        FieldDefKeys::TABLE => 'pressure_ulcers',
        'Rows' => 7,
        'Columns' => 70,
        'MaxLength' => 255,
    ],
    'secondary_morbidity' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => "During the patient's stay at this facility, did the patient develop a secondary morbidity?",
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'risk_assessment_score' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Pressure ulcer risk assessment score',
        FieldDefKeys::TABLE => 'pressure_ulcers',
    ],
    'site' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_skin_lesions_site'),
        FieldDefKeys::TABLE => 'skin_lesions',
    ],
    'development_level' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Development level',
        FieldDefKeys::TABLE => 'skin_lesions',
    ],
    'nature_of_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Nature of sharps injury',
        FieldDefKeys::TABLE => 'sharps',
    ],
    'type_of_tool' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of injurious tool',
        FieldDefKeys::TABLE => 'sharps',
    ],
    'contamination_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of contamination',
        FieldDefKeys::TABLE => 'sharps',
    ],
    'contamination' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Is the needle/sharp contaminated?',
        FieldDefKeys::TABLE => 'sharps',
    ],
    'contamination_other' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Other type of contamination',
        FieldDefKeys::TABLE => 'sharps',
        'Rows' => 7,
        'Columns' => 70,
        'MaxLength' => 255,
    ],
    'link_type' => [
        'Type' => 'array_select',
        FieldDefKeys::TITLE => 'Type of contact',
        'NoListCol' => true,
        'NoListCol_Override' => true,
        'CustomCodes' => [
            ContactTypes::PERSON_AFFECTED => 'Person affected',
            ContactTypes::EMPLOYEE => 'Member of staff/employee',
            ContactTypes::OTHER_CONTACT => 'Other type of contact',
        ],
    ],
    'link_notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Link Notes',
        'NoListCol' => true,
        'Rows' => 5,
        'Columns' => 70,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
            'O' => 'link_respondents',
            'G' => 'link_respondents',
        ],
    ],
    'link_status' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Status',
        'Parent' => 'con_type',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_treatment' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Treatment',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'link_position' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Position',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'link_daysaway' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'No of days',
        'Width' => 3,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_abs_start' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Absence start',
        'NoListCol' => true,
        'onChangeExtra' => 'getAbsenceDays()',
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_abs_end' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Absence end',
        FieldDefKeys::NOT_EARLIER_THAN => ['link_abs_start'],
        'NoListCol' => true,
        'onChangeExtra' => 'getAbsenceDays()',
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_is_riddor' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'RIDDOR?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_riddor' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'RIDDOR injury type',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_occupation' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Occupation',
        'Width' => 30,
        'MaxLength' => 64,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_age' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Age',
        'Width' => 3,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_age_band' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Age Band',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    ContactsFields::LINK_HEIGHT => [
        FieldDefKeys::TYPE => FieldInterface::DECIMAL_DB,
        FieldDefKeys::TITLE => _fdtk('link_height'),
        'Width' => 3,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    ContactsFields::LINK_WEIGHT => [
        FieldDefKeys::TYPE => FieldInterface::DECIMAL_DB,
        FieldDefKeys::TITLE => _fdtk('link_weight'),
        'Width' => 3,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_deceased' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Deceased?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_npsa_role' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('staff_role_in_incident'),
        'NoListCol' => true,
        'NoListCol_Override' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_role' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Contact role',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
            'O' => 'link_respondents',
            'G' => 'link_respondents',
        ],
    ],
    'link_mhact_section' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'MHA Section',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_mhcpa' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'CPA',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_dear' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Dear',
        'Width' => 30,
        'MaxLength' => 64,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_ref' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Your ref',
        'Width' => 30,
        'MaxLength' => 64,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_lip' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Litigant in person?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_worked_alone' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was the staff member lone working?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_become_unconscious' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Did the individual become unconscious?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_req_resuscitation' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Did the individual need resuscitation?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_hospital_24hours' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Did the individual remain in hospital for more than 24 hours?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_pprop_damaged' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Personal property lost/damaged/stolen?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_clin_factors' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Clinical factors involved',
        'NoListCol' => true,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_direct_indirect' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Direct / indirect contact made?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_injury_caused' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was physical Injury caused?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_attempted_assault' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was there an attempted assault?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_discomfort_caused' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was personal discomfort caused?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_public_disorder' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was there public disorder?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_verbal_abuse' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was there verbal abuse?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_harassment' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was there harassment/malicious communications?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_police_pursue' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Does the individual want the police to pursue the matter?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_police_persue_reason' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'If no answered to the above question please state why',
        'Rows' => 5,
        'Columns' => 50,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_date_admission' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date of admission',
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_notify_progress' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Do you require progress updates on this incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_injury1' => [
        'BlockFromReports' => true,
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Injury',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_bodypart1' => [
        'BlockFromReports' => true,
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Body part',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'show_injury' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was the person injured in the incident?',
        'NoOrder' => true,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'show_pars' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Is this incident reportable to SIRS?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'show_assailant' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Is there an alleged assailant for the incident?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pasno1' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'PAS no. 1',
        'Width' => 30,
        'MaxLength' => 6,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pasno2' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'PAS no. 2',
        'Width' => 30,
        'MaxLength' => 6,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_pasno3' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'PAS no. 3',
        'Width' => 30,
        'MaxLength' => 6,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'rep_approved' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Approval status',
        'Width' => 32,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_consequence' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Consequence',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_consequence_initial' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Consequence (Initial)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_grade' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Grade',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_grade_initial' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Grade (Initial)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_likelihood' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Likelihood of recurrence',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_likelihood_initial' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Likelihood of recurrence (Initial)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    // Issues
    'caf_level_1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Level 1',
        'FieldFormatsName' => 'caf_level_1',
        'NoListCol' => true,
        'OldCodes' => true,
        FieldDefKeys::TABLE => 'causal_factors',
    ],
    'caf_level_2' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Level 2',
        'FieldFormatsName' => 'caf_level_2',
        'NoListCol' => true,
        'OldCodes' => true,
        FieldDefKeys::TABLE => 'causal_factors',
    ],
    // CCS2
    'inc_affecting_tier_zero' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Incident affecting',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_type_tier_one' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Incident type tier 1',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_type_tier_two' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Incident type tier 2',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_type_tier_three' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Incident type tier 3',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_level_intervention' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Level of intervention',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_level_harm' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Level of harm',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_never_event' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was the incident a Never Event?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'rea_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Reason for rejection',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'aud_reasons',
    ],
    'rea_text' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Further Details',
        'NoListCol' => true,
        'Rows' => 7,
        'Columns' => 70,
    ],
    'rea_con_name' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'NoListCol' => true,
    ],
    'INC_SAVED_QUERIES' => [
        'Type' => 'multilistbox',
        'MaxLength' => 70,
    ],
    'inc_last_updated' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Last updated',
        'Width' => 32,
        'Computed' => true,
        'NoSearch' => true,
    ],
    'inc_ot_q1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q1_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q2' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => _fdtk('inc_ot_q2_title'),
        'NotFuture' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q3' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q3_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q4' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => _fdtk('inc_ot_q4_title'),
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q5' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => _fdtk('inc_ot_q5_title'),
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q6' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q6_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q7' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q7_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q8' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q8_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q9' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q9_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q10' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q10_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q11' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => _fdtk('inc_ot_q11_title'),
        'NotFuture' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q12' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q12_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q13' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q13_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q14' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q14_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q15' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q15_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q16' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q16_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q17' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q17_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q18' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q18_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q19' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => _fdtk('inc_ot_q19_title'),
        'Rows' => 10,
        'Columns' => 70,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q20' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('inc_ot_q20_title'),
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q21' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Has the open disclosure process commenced?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'inc_ot_q22' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Commencement date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'NotFuture' => true,
    ],
    'anon_reporting' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Report this incident anonymously?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SOURCE_OF_RECORD => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Source of record',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'listorder' => [
        FieldDefKeys::TITLE => 'List Order',
        'NoListCol_Override' => true,
        'Type' => 'number',
    ],
    'dum_fbk_to' => [FieldDefKeys::TITLE => _fdtk('staff_contacts')],
    'inc_recommend' => [FieldDefKeys::TITLE => 'Recommendations'],
    'inc_imprstrats' => [FieldDefKeys::TITLE => 'Improvement strategies'],
    'inc_extrainfo' => [FieldDefKeys::TITLE => 'Extra information'],
    'inc_dsched-inc_dopened' => [
        'BlockFromReports' => true,
        FieldDefKeys::TITLE => 'Days opened to closed',
        'NoListCol_Override' => true,
    ],
    'inc_dreported-inc_dincident' => [
        'BlockFromReports' => true,
        FieldDefKeys::TITLE => 'Days to report',
        'NoListCol_Override' => true,
    ],
    'inc_dsched-inc_dincident' => [
        'BlockFromReports' => true,
        FieldDefKeys::TITLE => 'Incident date to closed',
        'NoListCol_Override' => true,
    ],
    'inc_clinoutcome2' => [
        FieldDefKeys::TITLE => 'Outcome (long term)',
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
    ],
    'ipp_value' => [
        FieldDefKeys::TITLE => 'Value of personal property',
        'NoListCol_Override' => true,
    ],
    'ipp_description' => [
        FieldDefKeys::TITLE => 'Description of personal property',
        'NoListCol_Override' => true,
    ],
    'ipp_damage_type' => [
        FieldDefKeys::TITLE => 'Type of damage/loss',
        'NoListCol_Override' => true,
    ],
    'link_npsa' => [
        FieldDefKeys::TITLE => 'NPSA',
        'NoListCol_Override' => true,
    ],
    'inc_com_count' => [
        FieldDefKeys::TITLE => 'No. of Linked Feedback',
        'NoListCol_Override' => true,
    ],
    'inc_cla_count' => [
        FieldDefKeys::TITLE => 'No. of Linked Claims',
        'NoListCol_Override' => true,
    ],
    'cst_subtype' => [
        FieldDefKeys::TITLE => 'Subtype',
        'NoListCol_Override' => true,
    ],
    'cst_act_est' => [
        FieldDefKeys::TITLE => 'Actual/estimate',
        'NoListCol_Override' => true,
    ],
    'doc_dcreated' => [
        FieldDefKeys::TITLE => 'Date Document Created',
        'NoListCol_Override' => true,
    ],
    'doc_notes' => [
        FieldDefKeys::TITLE => 'Document Description',
        'NoListCol_Override' => true,
    ],
    'rea_type' => [
        FieldDefKeys::TITLE => 'Type',
        'BlockFromReports' => true,
        'NoListCol_Override' => true,
    ],
    'con_id' => ['NoListCol_Override' => true],
    'rea_dlogged' => [
        FieldDefKeys::TITLE => 'Date Rejected',
        'NoListCol_Override' => true,
    ],
    'sum_sev_cost' => [
        FieldDefKeys::TITLE => 'Severity Cost',
        'NoListCol_Override' => true,
    ],
    'sum_oth_cost' => [
        FieldDefKeys::TITLE => 'Other Cost',
        'NoListCol_Override' => true,
    ],
    'sum_icon_cost' => [
        FieldDefKeys::TITLE => 'Staff absence cost',
        'NoListCol_Override' => true,
    ],
    'sum_sev_cost+sum_icon_cost+sum_oth_cost' => [
        FieldDefKeys::TITLE => 'Total cost',
        'NoListCol_Override' => true,
    ],
    '(con_name + " " + con_forenames)' => [
        FieldDefKeys::TITLE => 'Name (Contacts)',
        'NoListCol_Override' => true,
    ],
    'link_injuries' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'BlockFromReports' => true,
        FieldDefKeys::TITLE => 'Injuries',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'pno_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => _fdtk('progress_notes_type_title'),
        FieldDefKeys::TABLE => 'progress_notes',
    ],
    'hours_worked' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Hours worked per week',
    ],
    'hourly_rate' => [
        'Type' => 'decimal',
        FieldDefKeys::TITLE => 'Hourly rate',
        'MaxValue' => '999999999.999999',
        'MinValue' => '-999999999.999999',
    ],
    'weekly_rate' => [
        'Type' => 'decimal',
        FieldDefKeys::TITLE => 'Weekly rate',
        'MaxValue' => '999999999.999999',
        'MinValue' => '-999999999.999999',
        'ReadOnly' => true,
        'CalculatedField' => true,
        'Computed' => true,
        'alwaysShowBox' => true,
    ],
    'monthly_rate' => [
        'Type' => 'decimal',
        FieldDefKeys::TITLE => 'Monthly rate',
        'MaxValue' => '999999999.999999',
        'MinValue' => '-999999999.999999',
        'ReadOnly' => true,
        'CalculatedField' => true,
        'Computed' => true,
        'alwaysShowBox' => true,
    ],
    // OSHA
    'osha_recordable' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'OSHA recordable',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    // FROI
    'incident_occurred_on_employer_premises' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Did injury/illness/exposure occur on employer\'s premises?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'safeguard_provided' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Safeguards provided?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    'safeguard_used' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Safeguards used?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    // EDI
    'edi_cause_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Cause code',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    // SPSC
    'hro_characteristics' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'HRO characteristics',
        'MaxLength' => 128,
        'NoListCol' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::CLASSIFICATION_TREE => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => _fdtk('classification_tree'),
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'NoMandatory' => true,
        'NoDefault' => true,
        'NoReadOnly' => true,
        'NoTags' => true,
        'NoSectionActions' => true,
        'NoFieldActions' => true,
        'mapperType' => 'classification',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_SPECIALTY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Specialty',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WHICH_OCCURRED => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Which of the following occurred?',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_LOCATION_DVT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the location of the DVT?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CONFIRMED_DVT => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Which diagnostic test confirmed the DVT?',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_VTE_RISK_ASSESSMENT_DOCUMENTED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Prior to the onset of the VTE incident, was a formal VTE risk assessment documented?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_PATIENTS_DOCUMENTED_RISK_OF_VTE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was the patient\'s documented risk of VTE?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_DOCUMENTED_RISK_OF_BLEEDING => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Prior to the onset of the VTE incident, what was the documented risk of bleeding',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_PHARMACOLOGICAL_MECHANICAL_PROPHYLAXIS => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Prior to the onset of the VTE incident, was any Pharmacological or mechanical prophylaxis?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_PARMACOLOGICAL_ANTICOAGULANT_ADMINISTERED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Prior to the onset of the VTE incident, was any pharmacological anticoagulant prophylaxis administered?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WHY_ANTICOAGULANT_NOT_GIVE => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Which of the following best describes why the pharmacologic anticoagulant prophylaxis was not given?',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_MECHANICAL_PROPHYLAXIS_APPLIED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Prior to the onset of the VTE incident, was any mechanical prophylaxis applied?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WHICH_DIAGNOSTIC_CORFIRMED_PE => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Which diagnostic test confirmed the PE?',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_VTE_OUTCOME => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'VTE Outcome',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_VTE_TYPE => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'VTE Type',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_VTE_SUB_TYPE => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'VTE Sub type',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_VTE_FEMALE => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'VTE Female',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_NARANJO_TOTAL_SCORE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Naranjo Total Score',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_ADR_PROBABILITY_SCALE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'ADR Probability Scale',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_ADR_ACTION_TAKEN => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'ADR - Action Taken',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_ADR_ORGAN_SYSTEM_FIELDS => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'ADR Organ System Fields',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_ADR_TOTAL_SCORE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'ADR - Total Score',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_BLOOD_TYPE_PRODUCT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Blood Type of Product',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_RED_CODE_ACTIVATED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was Red Code Activated?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_FIRE_ALARM_ACTIVATED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the Fire Alarm Activated?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_BUILDING_EVACUATED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the Building Evacuated?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_PORTABLE_FIRE_EXTINGUISHERS_USED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the Portable extinguisher(s) used?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WAS_LOCAL_FIRE_DEPARTMENT_INFORMED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was the Local Fire Department Informed?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_WERE_ANY_DAMAGES_TO_PROPERTY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Were there any damages to the property/Facility?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_MORBIDITY_RECORD_IS => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Morbidity record is',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_IS_TRIGGER_RELATED_TO_IMPROPER_ASSESSMENT_OF_PATIENT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is this trigger related to improper assessment of the patient?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SPSC_ADDITIONAL_MORBIDITY_TRIGGERS => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Additional Morbidity triggers?',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::SEND_TO_SFDA => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Export to SFDA?',
        'MaxLength' => 1,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_REP_ID => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Reporter Contact Id',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_GRADE_RATING => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Risk Rating',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_GRADE_INITIAL_RATING => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Risk Rating (Initial)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::DIAGNOSTIC => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Diagnostic',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::PROCEDURES => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Procedures',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::ALLERGY_REACTION => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Allergy Reaction Code',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::ALLERGY_SEVERITY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Allergy Severity Code',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::ALLERGEN_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Allergen Type Code',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::ALLERGY_CLINICAL_STATUS => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Allergy Clinical Status Code',
        'MaxLength' => 128,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::ONSET_DATE => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Onset Date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::DATE_OF_ADMISSION => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of Admission',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::RECOVERY_DATE => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Recovery Date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::OUTBREAK_IMPACT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Was this impacted by an infection outbreak?',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::OUTBREAK_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Outbreak type',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_DRUG_DOCUMENTED_DATE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'The date on which the serious adverse drug reaction was first documented',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_DRUG_REACTION_EFFECT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => "The effect of the serious adverse drug reaction on the patient's health",
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_MEDICAL_CONDITION => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'Any medical condition of the patient that directly relates to the serious adverse drug reaction',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'Rows' => 3,
        'Columns' => 50,
        'MaxLength' => 1300,
    ],
    IncidentsFields::VLC_HEALTH_RESTORED_DATE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => "The date on which the patient's health was restored to its state prior to the reaction(if applicable)",
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_CONCOMITANT_USED => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'Any concomitant therapeutic products used by the patient',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'Rows' => 3,
        'Columns' => 50,
    ],
    IncidentsFields::VLC_DEVICE_DOCUMENTED_DATE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'The date on which the medical device incident was first documented',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_DEVICE_EFFECT_HEALTH => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => "Effect of the medical device incident on the patient's health",
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_DEVICE_CONTRIBUTION => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Contributing factors to the medical device incident including any medical condition of the patient that directly relates to the medical device incident',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::VLC_HOSPITAL_REP_EMAIL_ID => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Hospital Representative\'s email ID',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    IncidentsFields::VLC_HEALTH_CANADA_INST_ID => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Health Canada Institutional ID (HCID)',
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    PSIMSIncidentMedicationFields::DRUG_INSUFFICIENT_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was too little medication prescribed/dispensed/administered?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_INVOLVEMENT_FACTORS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was a medication involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_REACTION => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Do you think this incident involved any of these problems with medicines or medical devices',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_REACTION_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If problem with medicine not found, please specify.',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_USED_TOO_MUCH => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was too much medication prescribed/dispensed/administered?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which of the following processes were involved in what went wrong',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_INVOLVED_PROCESSES_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If process not found, please specify.',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::DRUG_GIVEN_INCORRECTLY => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the medication prescribed/dispensed/administered incorrectly?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::PROBLEM_MEDS_PACKAGING => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'What was wrong with the medication and/or its packaging?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentMedicationFields::PROBLEM_DESCRIPTION_DRUGS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with the medication?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_medication_fields',
    ],
    PSIMSIncidentEquipmentFields::PSIMS_DEVICE_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'What kind of medical device was involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_TYPE_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If type of medical device not found, please specify.',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_BROKEN_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'What was wrong with the device?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_NOT_ENOUGH_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the device not used when it should have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_USAGE_FACTORS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the device involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_USED_UNNECESSARILY => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the device used when it should not have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_USAGE_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the device used incorrectly?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentEquipmentFields::DEVICE_PROBLEM_DESCRIPTION => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with devices?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'psims_incident_equipment_fields',
    ],
    PSIMSIncidentFields::DUTY_OF_CANDOUR => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does this event meet the requirements for Duty of Candour?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentCodedFields::PSIMS_EVENT_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What kind of event do you want to record?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_SAFETY_INCIDENT_OCCURRED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is there anything in what you know at this point that makes you worry or suspect that a patient safety incident has occurred?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTIMATED_TIME => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Do you think any of the below were relevant to the incident occurring?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_OUTCOME_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which of the following are you recording?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_AGENTS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which things were involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_MEDICATION_ADMIN => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was a device used to give medication in this instance?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_LEVEL_OF_CONCERN => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'How concerned are you about this event (incident/risk/outcome) and its implications?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_DETECTION_POINT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'At what point was the event (incident/outcome) detected?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_SAFETY_CHALLENGES => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Does the incident appear to relate to any of these known safety challenges?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RESPONSIBLE_SPECIALTY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Which specialty does the event (incident/risk/outcome) relate to?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_SERVICE_AREA => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which service areas were involved?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_THEME => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'What type of risk to patient safety are you recording?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_SERVICE_AREA => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which service areas are at risk?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_IMMINENT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is there imminent risk of severe harm or death?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_TIME_FRAME => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'When do you think this risk will cause harm?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_CARE_DETECTION_FACTOR => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'How did this example of good practice come to your attention?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_RISK_IDENTIFIED_LOCATION => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Where was the risk identified?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_LOCATION_AT_RISK => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Where does the risk exist?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_LOCATION_WITHIN_SERVICE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Where did the incident happen?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_FURNITURE_FITTINGS => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What furniture or fittings were involved?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_IT_INVOLVEMENT_FACTORS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were IT systems or software involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were blood or blood products not used when they should have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_DEFICIENT_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were too few/little tissues or organs for transplant used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_INVOLVEMENT_FACTOR => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were tissue or organs for transplant involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were tissues or organs for transplant not used when they should have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_USED_TOO_MUCH => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were too much/many tissues or organs for transplant used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_WRONG_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were the wrong tissues or organs for transplant used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TISSUE_DAMAGED_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were tissues or organs for transplant available but damaged or unfit for use?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_INVOLVED_PERSONS_ACTIONS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which people’s actions differed from what was expected or intended?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_FACTORS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How did people’s actions differ from what was expected or intended?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_TOO_MUCH => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How did people do something too much?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_ACTION_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How did people do something too little?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_INVOLVEMENT_FACTOR => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was people’s availability involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_OMITTED_ACTION => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why wasn’t the right action taken?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_UNAVAILABLE_DETAIL => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were required people absent?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PEOPLE_WRONG_ACTION => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How did the wrong action get taken?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_INVOLVED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was blood involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_INVOLVED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were blood products involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_PRODUCTS_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were blood products not used when they should have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BLOOD_WAS_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why was blood not used when it should have been?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which of the following were involved?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_INVOLVED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were buildings or infrastructure involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_BUILDINGS_INFRASTRUCTURE_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were the correct buildings or infrastructure not used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was blood damaged or not fit for use?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_DAMAGED_BLOOD_PRODUCTS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were blood products damaged or not fit for use?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Which of the following were involved?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_INVOLVED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were estates services involved in what went wrong?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_ESTATES_SERVICES_NOT_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Why were the correct estates services not used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_NEVER_EVENT_TYPE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Which Never Event type are you declaring?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_WRONG_ESTATES_SERVICES => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were the wrong estates services involved?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_WRONG_BUILDINGS_INFRASTRUCTURE => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were the wrong buildings or infrastructure involved?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD_PRODUCTS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were the wrong blood products used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_WRONG_BLOOD => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was the wrong blood used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was too much blood used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TOO_MUCH_BLOOD_PRODUCTS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were too great an amount of blood products used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_USED => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How was too little blood used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_TOO_LITTLE_BLOOD_PRODUCTS => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'How were too small an amount of blood products used?',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_CQC_CRITERIA => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Which of the following criteria does it meet?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_INCIDENT_FRAMEWORK => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Which incident framework is your organisation operating under?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_RESPONSE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is there a national or regulatory requirement to conduct an investigation in response to this event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPOND => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'How will you respond?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_FINDINGS => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'Findings',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::ROWS => 3,
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::MAX_LENGTH => 12000,
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'What areas for improvement have been agreed as a result of this response?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::ROWS => 3,
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::MAX_LENGTH => 12000,
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_DEVELOPED => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Have safety actions been developed?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_SAFETY_ACTION_IMPLEMENTED => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'What safety actions have been made to address this area for improvement?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::ROWS => 3,
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::MAX_LENGTH => 12000,
    ],
    PSIMSIncidentFields::PSIMS_PSIRF_IMPROVEMENT_ADDRESSED => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'How will areas for improvement be addressed (e.g. wider review)?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::ROWS => 3,
        FieldDefKeys::COLUMNS => 70,
        FieldDefKeys::MAX_LENGTH => 12000,
    ],
    PSIMSIncidentCodedFields::PSIMS_PSIRF_HOW_RESPONDED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Confirm how you responded',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentFields::PSIMS_CQC_NOTIFY => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does this event require statutory notification to CQC?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_DEPRIVATION_OF_LIBERTY => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Was the service user subject to Deprivation of Liberty Safeguards at the time of the event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_DESIGNATIONS_MENTAL_HEALTH => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Was the service user detained under the Mental Health Act at the time of the event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_HSIB_NOTIFY => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does this incident relate to a baby and/or mother and require notification to HSIB under the defined criteria for maternity investigations?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_LOCAL_AUTHORITY_SAFEGUARDING => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Has this been referred to the Local Authority Safeguarding team?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_NEVER_EVENT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does this event meet the national definition of a Never Event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_SERIOUS_INCIDENT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does this event meet the national definition of a Serious Incident?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BUILDINGS_INFRASTRUCTURE_PROBLEM => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with buildings or infrastructure?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_ESTATES_SERVICES_PROBLEM => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with estates services?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BLOOD_PROBLEM => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with the blood?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BATCH => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the batch number of the blood product involved?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_BRAND => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the brand of the blood product involved?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_DETAILS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Which blood products were involved?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BLOOD_PRODUCTS_PROBLEM => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with the blood products?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_MARVIN_REFERENCE_NUMBER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Marvin reference number',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_NHSBT_REPORT_NUMBER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'NHSBT report numbers',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_SABRE_REPORT_NUMBER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'SABRE report numbers (for blood incidents)',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_SHOT_REPORT_NUMBER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'SHOT report numbers (for blood incidents)',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_YELLOW_CARD_REFERENCE => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If you have already reported this event to the Yellow Card scheme, please include your reference number here:',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_ROLE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Which of these best describes your role?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_ROLE_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If reporter role not found, please specify.',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What was your relationship to the incident?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_REPORTER_INVOLVEMENT_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'If relationship to incident not found, please specify.',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_ACTIONS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'How were actions or behaviours involved in what went wrong?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_INVOLVEMENT => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => "What was the problem with people's involvement or availability?",
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_DETECTION_POINT_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_WENT_WELL => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Do you have any ideas for what could be done to reduce the risk or impact of this happening again?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_RADIOTHERAPY_INCIDENT_CODE => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What is the radiotherapy incident code?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_RESPONSIBLE_SPECIALTY_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_RISK_THEME_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],

    PSIMSIncidentFields::PSIMS_RISK_POPULATION => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Which groups of patients are at risk?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],

    PSIMSIncidentFields::PSIMS_RISK_TIMEFRAME_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],

    PSIMSIncidentFields::PSIMS_RISK_DESCRIPTION => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Describe the risk',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_GOOD_CARE_DETECTION_FACTOR_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_HOW_FUTURE_OCCURRENCE => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'How could this excellence be amplified or recreated in the future?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_BUILT_ENVIRONMENT_INVOLVED => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What elements of the built environment were involved?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_FURNITURE_FITTINGS_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_SYSTEMS_SOFTWARE => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with IT systems or software?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_PROBLEM_DESCRIPTION_TISSUES_ORGANS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the problem with tissues or organs for transplant?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::PSIMS_INVOLVED_PERSONS_ACTIONS_OTHER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Please specify',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV1 => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Are you reporting a patient safety event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::INC_PSIMS_RECORD_LV2 => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => "Is the reporter's assessment of whether this a patient safety event correct?",
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
        FieldDefKeys::TREAT_EMPTY_AS_N_IN_TRIGGERS => false,
    ],
    PSIMSIncidentFields::PSIMS_PATIENT_EVENT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Were patients involved in this event?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentFields::PSIMS_PEOPLE_AVAILABILITY => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Was the availability of people a factor in this incident?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT,
    ],
    PSIMSIncidentCodedFields::PSIMS_LOCATION_KNOWN => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Did the event occur whilst the patient was under your organisation’s care?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
    ],
    PSIMSIncidentCodedFields::PSIMS_ORGANISATION => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Under which organisations care did the event occur?',
        FieldDefKeys::TABLE => Tables::PSIMS_INCIDENT_CODED_FIELDS,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    IncidentsFields::MAX_PHYSICAL_HARM_PERSON => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Maximum level of physical harm incurred by persons affected in event',
        FieldDefKeys::COMPUTED => true,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::MAX_PSYCHOLOGICAL_HARM_PERSON => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Maximum level of psychological harm incurred by persons affected in event',
        FieldDefKeys::COMPUTED => true,
        FieldDefKeys::READ_ONLY => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
    ],
    IncidentsFields::INC_DATE_ORG_AWARE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Date organisation became aware',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
    ],
    IncidentsFields::INC_DATE_IN_PERSON_NOTIFICATION_MADE_DUE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => "Date 'in-person' notification made - Due Date",
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
        FieldDefKeys::NOT_EARLIER_THAN => [IncidentsFields::INC_DATE_ORG_AWARE],
    ],
    IncidentsFields::INC_DATE_IN_PERSON_NOTIFICATION_MADE_COMPLETED => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => "Date 'in-person' notification made - Completed Date",
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
        FieldDefKeys::NOT_EARLIER_THAN => [IncidentsFields::INC_DATE_ORG_AWARE],
    ],
    IncidentsFields::INC_DATE_WRITTEN_NOTIFICATION_MADE_DUE => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Date written notification sent - Due Date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
        FieldDefKeys::NOT_EARLIER_THAN => [IncidentsFields::INC_DATE_ORG_AWARE],
    ],
    IncidentsFields::INC_DATE_WRITTEN_NOTIFICATION_MADE_COMPLETED => [
        FieldDefKeys::TYPE => FieldInterface::DATE_DB,
        FieldDefKeys::TITLE => 'Date written notification sent - Completed Date',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
        FieldDefKeys::NOT_EARLIER_THAN => [IncidentsFields::INC_DATE_ORG_AWARE],
    ],
    IncidentsFields::INC_DAYS_ELAPSED_IN_PERSON => [
        FieldDefKeys::TYPE => FieldInterface::HIDDEN_DB,
        FieldDefKeys::TITLE => 'Working days elapsed between \'Date organization become aware\' and the \'Completed date \'in-person\' notification made\'',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
    ],
    IncidentsFields::INC_DAYS_ELAPSED_WRITTEN => [
        FieldDefKeys::TYPE => FieldInterface::HIDDEN_DB,
        FieldDefKeys::TITLE => 'Working days elapsed between \'Completed date \'in-person\' notification made\' and the \'Completed date written notification sent\'',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
    ],
    IncidentsFields::INC_TOTAL_TIME_TO_HANDLE => [
        FieldDefKeys::TYPE => FieldInterface::NUMBER_DB,
        FieldDefKeys::TITLE => 'Total time to handle',
        FieldDefKeys::TABLE => Tables::INCIDENTS_TIME_CHAIN,
        FieldDefKeys::NO_DEFAULT => true,
    ],
    IncidentsFields::REFERENCE_NUMBER => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Reference Number',
        FieldDefKeys::TABLE => Tables::INCIDENT_PSIMS_RESPONSE,
        FieldDefKeys::MAX_LENGTH => 64,
    ],
    IncidentsFields::SUBMISSION_DATE_TIME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Submission Date/Time',
        FieldDefKeys::TABLE => Tables::INCIDENT_PSIMS_RESPONSE,
    ],
    IncidentsFields::STATUS => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Status',
        FieldDefKeys::TABLE => Tables::INCIDENT_PSIMS_RESPONSE,
        FieldDefKeys::MAX_LENGTH => 64,
    ],
    IncidentsFields::WARNINGS => [
        FieldDefKeys::TYPE => FieldInterface::TEXT_DB,
        FieldDefKeys::TITLE => 'Warnings',
        FieldDefKeys::TABLE => Tables::INCIDENT_PSIMS_RESPONSE_WARNINGS,
        FieldDefKeys::ROWS => 10,
        FieldDefKeys::COLUMNS => 70,
    ],
    IncidentsFields::TIME_FIELD_1 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field 1',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_2 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field 2',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_3 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field 3',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_4 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field 4',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_5 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field 5',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_1 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 1',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_2 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 2',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_3 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 3',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_4 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 4',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_5 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 5',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_6 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 6',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_FROM_7 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field From 7',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_1 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 1',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_2 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 2',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_3 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 3',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_4 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 4',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_5 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 5',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_6 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 6',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
    IncidentsFields::TIME_FIELD_TO_7 => [
        FieldDefKeys::TYPE => FieldInterface::TIME_DB,
        FieldDefKeys::TITLE => 'Time Field To 7',
        FieldDefKeys::TABLE => Tables::INCIDENT_TIME_FIELDS,
    ],
];

$FieldDefs['CON'] = [
    'con_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type',
        'Child' => 'con_subtype',
        'OldCodes' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_remote_id' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'con_remote_id',
    ],
    'con_subtype' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Subtype',
        'Parent' => 'con_type',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Patient/staff number',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_staff_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Staff number',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_title' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Title',
        'Width' => 5,
        'MaxLength' => 10,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_forenames' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'First names',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_surname' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Surname',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_gender' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Gender',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_dob' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date of birth',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_dod' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date of death',
        FieldDefKeys::NOT_EARLIER_THAN => ['con_dob'],
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_city' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'City',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_line1' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Line 1',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_line2' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Line 2',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_line3' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Line 3',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_country' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Country',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_state' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'State',
        'Width' => 2,
        'MaxLength' => 2,
        'UpperCase' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_county' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'County',
        'Width' => 254,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_postcode' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Postcode',
        'Width' => 10,
        'MaxLength' => 10,
        'UpperCase' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_tel1' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Telephone no. 1',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'isTelNumber' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_tel2' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Telephone no. 2',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'isTelNumber' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_email' => [
        'Type' => 'email',
        FieldDefKeys::TITLE => 'E-mail',
        'Width' => 40,
        'MaxLength' => 128,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_ethnicity' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Ethnicity',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_language' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Language',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_empl_grade' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Staff status',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Notes',
        'Rows' => 5,
        'Columns' => 50,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_nhsno' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'NHS number',
        'Width' => 30,
        'MaxLength' => bYN(GetParm('VALID_NHSNO', 'Y')) ? 12 : 20,
        'eventExtra' => bYN(GetParm('VALID_NHSNO', 'Y')) ? 'onkeyup="formatNhsNo(jQuery(this))"' : '',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_police_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Police officer shoulder/collar number(s)',
        'Width' => 30,
        'MaxLength' => 128,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_disability' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Disabilities',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_religion' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Religion',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_sex_orientation' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Sexual orientation',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_work_alone_assessed' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Has staff member been risk assessed as a lone worker?',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    PSIMSLinkContactsFields::PSIMS_AGE_YEARS => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Estimate the patient\'s age',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    PSIMSLinkContactsFields::PSIMS_CLINICAL_OUTCOME => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'What was the clinical outcome for the patient?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    PSIMSLinkContactsFields::PSIMS_PHYSICAL_HARM => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'To what extent was the patient physically harmed (including pain) in this incident?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    PSIMSLinkContactsFields::PSIMS_PSYCHOLOGICAL_HARM => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'To what extent was the patient psychologically harmed in this incident?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    PSIMSLinkContactsFields::PSIMS_STRENGTH_OF_ASSOCIATION => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'How much did the incident contribute to the outcome for the patient?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    PSIMSLinkContactsFields::PSIMS_GENDER => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What is the patient\'s sex?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    PSIMSLinkContactsFields::PSIMS_PATIENT_ETHNICITY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'What is the patient\'s self-identified ethnicity?',
        FieldDefKeys::TABLE => Tables::PSIMS_CONTACT_FIELDS,
    ],
    'rep_approved' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Approval status',
        'Width' => 32,
        'CodeColours' => [
            'UN' => 'FF0000',
            'FA' => '00FF00',
            'REJECT' => 'E0E0E0',
        ],
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'recordid' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'ID',
        'Width' => 5,
        'ReadOnly' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'tax_id' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Tax ID',
        'MaxLength' => 9,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    ContactsFields::NATIONALITY => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Nationality',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    ContactsFields::JOB_TITLE => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Job Title',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    // needed for mandatory field on registration screen
    'con_action' => [
        FieldDefKeys::TITLE => 'Action',
        'NoListCol' => true,
    ],
    // complaints link fields - not sure if they should be here or in "COM", but if we move them to "COM" the validation will stop working.
    'lcom_dreceived' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date received',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dack' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date acknowledged',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dactioned' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date actioned',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dhold1' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date holding 1 done',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dresponse' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date responded',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dholding' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date holding',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_dreplied' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date replied',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_ddueack' => [
        FieldDefKeys::TITLE => 'Acknowledged due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_ddueact' => [
        FieldDefKeys::TITLE => 'Actioned due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_dduehold1' => [
        FieldDefKeys::TITLE => 'Holding 1 due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_ddueresp' => [
        FieldDefKeys::TITLE => 'Response due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_dduehold' => [
        FieldDefKeys::TITLE => 'Holding 1 due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_dduerepl' => [
        FieldDefKeys::TITLE => 'Replied due (Person Providing Feedback)',
        'Type' => 'date',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
    ],
    'lcom_dreopened' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Re-opened (Person Providing Feedback)',
        'NoListCol' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['lcom_dreceived'],
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_reopen' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Do you want to re-open the feedback record?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'lcom_reason_for_reopen' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Reason for re-open',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'link_compl',
    ],
    'inc_injury' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Injury',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'inc_injuries',
    ],
    'inc_bodypart' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'inc_injuries',
    ],
    'death_result_injury' => [
        'Type' => 'yesno',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'inc_injuries',
    ],
    'permanent_impairment_percentage' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Permanent Impairment Percentage',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'inc_injuries',
    ],
    'link_injury1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Injury (Primary)',
    ],
    'link_bodypart1' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Body part (Primary)',
    ],
    'death_result_injury1' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Death Result of Injury Code (Primary)',
    ],
    'permanent_impairment_percentage1' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Permanent Impairment Percentage (Primary)',
    ],
    'con_number_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Number Type',
        FieldDefKeys::TABLE => 'con_numbers',
    ],
    'con_id_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'ID Number',
        FieldDefKeys::TABLE => 'con_numbers',
        'NoListCol' => true,
    ],
    'con_id_numbers' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'ID Numbers',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'show_illness' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Was the person affected by an illness?',
        'NoOrder' => true,
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'link_illness' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Illness',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'link_sedation' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Sedation',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'show_document' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Are there any documents to be attached to this record?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Indemnity incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
        FieldDefKeys::TABLE => 'link_respondents',
    ],
    'expenses_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Expenses incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
        FieldDefKeys::TABLE => 'link_respondents',
    ],
    'fin_medical_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Medical incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_legal_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Legal incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_temporary_indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Temporary indemnity incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_permanent_indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Permanent indemnity incurred',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'remaining_indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Indemnity reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
        FieldDefKeys::TABLE => 'link_respondents',
    ],
    'remaining_expenses_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Expenses reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
        FieldDefKeys::TABLE => 'link_respondents',
    ],
    'fin_remaining_medical_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Medical reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_remaining_legal_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Legal reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_remaining_temporary_indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Temporary indemnity reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'fin_remaining_permanent_indemnity_reserve_assigned' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Permanent indemnity reserve assigned',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
    ],
    'resp_total_paid' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'Total Paid',
        'NoListCol' => true,
        'ReadOnly' => true,
        'CalculatedField' => true,
        FieldDefKeys::TABLE => 'link_respondents',
    ],
    // ICD
    'icd_classification' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'ICD classification',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'icd_diagnosis_codes' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Diagnosis ICD codes',
        FieldDefKeys::TABLE => 'link_contacts',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'requireParentValue' => true,
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
    ],
    'icd_procedure_codes' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Procedure ICD codes',
        FieldDefKeys::TABLE => 'link_contacts',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'requireParentValue' => true,
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
    ],
    // MMSEA
    'claimant_medicare_beneficiary' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Is claimant a Medicare Beneficiary',
        FieldDefKeys::TABLE => 'link_contacts',
        'allowUnknownValue' => true,
    ],
    'claimant_payments_non_medical' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Are all payments for this Claimant non-medical',
        FieldDefKeys::TABLE => 'link_contacts',
        'allowUnknownValue' => true,
    ],
    'date_medicare_confirmed' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date Medicare confirmed as beneficiary',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'claimant_medicare_claim_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Claimants Medicare Health Insurance Claim Number',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'date_mmsea_last_reported' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'MMSEA last reporting date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'delete_if_field_with_medicate' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'If Filed with Medicare - Delete this Record',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'no_fault' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'No fault indicator',
        FieldDefKeys::TABLE => 'link_contacts',
        'allowUnknownValue' => true,
    ],
    'no_fault_insurance_limit' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'No fault insurance limit',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'no_fault_exhaust_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'No fault exhaust date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'msp_effective_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'MSP (Medicare secondary payer) effective date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'msp_termination_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'MSP Termination Date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'msp_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'MSP Type Indicator',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'disposition_code' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Disposition Code',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'crd_start_time' => [
        FieldDefKeys::TITLE => 'Start time (Attended)',
        'NoListCol_Override' => true,
    ],
    'fullname' => [FieldDefKeys::TITLE => 'Full name'],
    'link_datetime' => [
        FieldDefKeys::TITLE => 'Date/Time',
        'NoListCol_Override' => true,
    ],
    'con_cla_count' => [
        FieldDefKeys::TITLE => 'No. of Linked Claims',
        'NoListCol_Override' => true,
    ],
    'con_cla_open' => [
        FieldDefKeys::TITLE => 'No. of Linked Open Claims',
        'NoListCol_Override' => true,
    ],
    'con_com_count' => [
        FieldDefKeys::TITLE => 'No. of Linked Feedback',
        'NoListCol_Override' => true,
    ],
    'con_com_open' => [
        FieldDefKeys::TITLE => 'No. of Linked Open Feedback',
        'NoListCol_Override' => true,
    ],
    'con_inc_count' => [
        FieldDefKeys::TITLE => 'No. of Linked Incidents',
        'NoListCol_Override' => true,
    ],
    'con_inc_open' => [
        FieldDefKeys::TITLE => 'No. of Linked Open Incidents',
        'NoListCol_Override' => true,
    ],
    'con_rfi_count' => [
        FieldDefKeys::TITLE => 'No. of Linked RFIs',
        'NoListCol_Override' => true,
    ],
    'con_rfi_open' => [
        FieldDefKeys::TITLE => 'No. of Linked Open RFIs',
        'NoListCol_Override' => true,
    ],
    'crs_name' => [
        FieldDefKeys::TITLE => 'Name (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_synopsis' => [
        FieldDefKeys::TITLE => 'Description (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_organisation' => [FieldDefKeys::TITLE => 'Trust (Attended)'],
    'crd_date' => [
        FieldDefKeys::TITLE => 'Start date (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_type' => [
        FieldDefKeys::TITLE => 'Type (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_frequency' => [
        FieldDefKeys::TITLE => 'Frequency (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_dopened' => [
        FieldDefKeys::TITLE => 'Opened date (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_dclosed' => [
        FieldDefKeys::TITLE => 'Closed date (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_manager' => [
        FieldDefKeys::TITLE => 'Manager (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_tutor' => [FieldDefKeys::TITLE => 'Tutor (Attended)'],
    'crs_subtype' => [FieldDefKeys::TITLE => 'Subtype (Attended)'],
    'crs_mandatory' => [
        FieldDefKeys::TITLE => 'Mandatory? (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_cost' => [
        FieldDefKeys::TITLE => 'Cost (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_min_attend' => [
        FieldDefKeys::TITLE => 'Min. Attendees (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_max_attend' => [
        FieldDefKeys::TITLE => 'Max. Attendees (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_mark' => [
        FieldDefKeys::TITLE => 'Pass Mark (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_grade' => [
        FieldDefKeys::TITLE => 'Pass Grade (Attended)',
        'NoListCol_Override' => true,
    ],
    'crd_location' => [
        FieldDefKeys::TITLE => 'Location (Attended)',
        'NoListCol_Override' => true,
    ],
    'crd_dexpiry' => [
        FieldDefKeys::TITLE => 'Expiry date (Attended)',
        'NoListCol_Override' => true,
    ],
    'crd_end_time' => [
        FieldDefKeys::TITLE => 'End time (Attended)',
        'NoListCol_Override' => true,
    ],
    'crd_comments' => [
        FieldDefKeys::TITLE => 'Comments (Attended)',
        'NoListCol_Override' => true,
    ],
    'crd_end_date' => [
        FieldDefKeys::TITLE => 'End date (Attended)',
        'NoListCol_Override' => true,
    ],
    'crs_name' => [
        FieldDefKeys::TITLE => 'Name (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_synopsis' => [
        FieldDefKeys::TITLE => 'Description (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_organisation' => [FieldDefKeys::TITLE => 'Trust (Missed)'],
    'crs_type' => [FieldDefKeys::TITLE => 'Type (Missed)'],
    'crs_frequency' => [
        FieldDefKeys::TITLE => 'Frequency (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_dopened' => [
        FieldDefKeys::TITLE => 'Opened date (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_dclosed' => [
        FieldDefKeys::TITLE => 'Closed date (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_manager' => [
        FieldDefKeys::TITLE => 'Manager (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_tutor' => [FieldDefKeys::TITLE => 'Tutor (Missed)'],
    'crs_subtype' => [
        FieldDefKeys::TITLE => 'Subtype (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_mandatory' => [
        FieldDefKeys::TITLE => 'Mandatory? (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_cost' => [
        FieldDefKeys::TITLE => 'Cost (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_min_attend' => [
        FieldDefKeys::TITLE => 'Min. Attendees (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_max_attend' => [
        FieldDefKeys::TITLE => 'Max. Attendees (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_mark' => [
        FieldDefKeys::TITLE => 'Pass Mark (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_grade' => [
        FieldDefKeys::TITLE => 'Pass Grade (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_start_time' => [
        FieldDefKeys::TITLE => 'Start time (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_date' => [
        FieldDefKeys::TITLE => 'Start date (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_location' => [
        FieldDefKeys::TITLE => 'Location (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_dexpiry' => [
        FieldDefKeys::TITLE => 'Expiry date (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_end_time' => [
        FieldDefKeys::TITLE => 'End time (Missed)',
        'NoListCol_Override' => true,
    ],
    'crd_comments' => [
        FieldDefKeys::TITLE => 'Comments (Missed)',
        'NoListCol_Override' => true,
    ],
    'crs_name' => [
        FieldDefKeys::TITLE => 'Name (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_synopsis' => [
        FieldDefKeys::TITLE => 'Description (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_organisation' => [
        FieldDefKeys::TITLE => 'Trust (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_type' => [
        FieldDefKeys::TITLE => 'Type (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_frequency' => [
        FieldDefKeys::TITLE => 'Frequency (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_dopened' => [
        FieldDefKeys::TITLE => 'Opened date (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_dclosed' => [
        FieldDefKeys::TITLE => 'Closed date (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_manager' => [
        FieldDefKeys::TITLE => 'Manager (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_tutor' => [
        FieldDefKeys::TITLE => 'Tutor (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_subtype' => [
        FieldDefKeys::TITLE => 'Subtype (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_mandatory' => [
        FieldDefKeys::TITLE => 'Mandatory? (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_cost' => [
        FieldDefKeys::TITLE => 'Cost (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_min_attend' => [
        FieldDefKeys::TITLE => 'Min. Attendees (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_max_attend' => [
        FieldDefKeys::TITLE => 'Max. Attendees (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_mark' => [
        FieldDefKeys::TITLE => 'Pass Mark (Pending)',
        'NoListCol_Override' => true,
    ],
    'crs_pass_grade' => [
        FieldDefKeys::TITLE => 'Pass Grade (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_start_time' => [
        FieldDefKeys::TITLE => 'Start time (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_date' => [
        FieldDefKeys::TITLE => 'Start date (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_location' => [
        FieldDefKeys::TITLE => 'Location (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_dexpiry' => [
        FieldDefKeys::TITLE => 'Expiry date (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_end_time' => [
        FieldDefKeys::TITLE => 'End time (Pending)',
        'NoListCol_Override' => true,
    ],
    'crd_comments' => [
        FieldDefKeys::TITLE => 'Comments (Pending)',
        'NoListCol_Override' => true,
    ],
    'location_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Location',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'location',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'service_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Service',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'service',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'inc_mob_severity' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Severity',
        'Width' => 30,
        'MaxLength' => 64,
        'UpperCase' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'ReadOnly' => true,
    ],
    'inc_mob_category' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Category',
        'Width' => 30,
        'MaxLength' => 128,
        'UpperCase' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'ReadOnly' => true,
    ],
    'inc_mob_anonymous' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Submitted anonymously',
        'Width' => 3,
        'MaxLength' => 3,
        'UpperCase' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'ReadOnly' => true,
    ],
    'inc_mob_location' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Location (MOB)',
        'Width' => 30,
        'MaxLength' => 64,
        'UpperCase' => true,
        FieldDefKeys::TABLE => Tables::INCIDENTS_MAIN,
        'ReadOnly' => true,
    ],
    'mobile_contacts' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Contacts',
        'ReadOnly' => true,
        'Rows' => 7,
        'Columns' => 70,
    ],
    // OSHA
    'osha_date_hired' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date hired',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    // Lost and restricted time
    'absence_start' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Lost time start date',
        'NoListCol' => true,
        'NotFuture' => true,
        FieldDefKeys::TABLE => 'lost_time',
    ],
    'absence_end' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Lost time return date',
        'NoListCol' => true,
        'NotFuture' => true,
        FieldDefKeys::TABLE => 'lost_time',
    ],
    'absence_total' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Lost time number of days',
        'NoListCol' => true,
        'Computed' => true,
        FieldDefKeys::TABLE => 'lost_time',
    ],
    'total_lost_time' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Total days away from work',
        'Width' => 3,
        'NoListCol' => true,
        'ReadOnly' => true,
        'alwaysShowBox' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'restriction_start' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Restricted time start date',
        'NoListCol' => true,
        'NotFuture' => true,
        FieldDefKeys::TABLE => 'restricted_time',
    ],
    'restriction_end' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Restricted time end date',
        'NoListCol' => true,
        'NotFuture' => true,
        FieldDefKeys::TABLE => 'restricted_time',
    ],
    'restriction_total' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Restricted time number of days',
        'NoListCol' => true,
        'Computed' => true,
        FieldDefKeys::TABLE => 'restricted_time',
    ],
    'total_restricted_time' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Total days restricted time',
        'Width' => 3,
        'NoListCol' => true,
        'ReadOnly' => true,
        'alwaysShowBox' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    'show_restricted_time' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Was employee on restricted time?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => [
            'DEFAULT' => 'link_contacts',
        ],
    ],
    // EDI
    'edi_wage_period' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Wage period',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_employment_status' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Employment status',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_employee_id_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Employee ID type',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_work_loss_list' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Work loss list',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_physical_restrictions' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Physical restrictions indicator',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_disability_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Disability type',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_diagnosis' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Diagnosis',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_agency_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Agency code',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_ncci_class' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'NCCI class',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_type_loss' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type loss',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_reporting_period' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Reporting period',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_date_disability_known_employer' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date employer knew of initial disability',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'first_day_of_disability' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'First day of disability after the waiting period',
        FieldDefKeys::TABLE => 'link_contacts',
        'MinDate' => [
            'startDateField' => 'claims_main.cla_dincident',
            'dateOffset' => '+4 day',
        ],
    ],
    'initial_rtw_same_employer' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Initial RTW with same employer indicator',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'verification_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Verification number',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_accident_premises' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Accident premises code',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_return_to_work_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Return to work type',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_return_to_work_qualif' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Return to work qualifier',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'edi_drug_screen_summary' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Drug screen summary',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    // MMSEA
    'mmsea_relationship_to_beneficiary' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Relationship to beneficiary',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'mmsea_type_of_representative' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type of representative',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'time_employee_began_work' => [
        'Type' => 'time',
        FieldDefKeys::TITLE => 'Time employee began work',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    // TPOC
    'link_recordid' => [
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'include_in_tpoc' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Include in TPOC',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'tpoc_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'TPOC Date',
        FieldDefKeys::TABLE => 'link_tpoc',
        'NotFuture' => true,
    ],
    'tpoc_amount' => [
        'Type' => 'money',
        FieldDefKeys::TITLE => 'TPOC Amount',
        FieldDefKeys::TABLE => 'link_tpoc',
        'MaxValue' => '999999999.99',
        'MinValue' => '-999999999.99',
    ],
    'tpoc_date_delayed' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Funding Delayed Beyond TPOC Start Date',
        FieldDefKeys::TABLE => 'link_tpoc',
        'NotFuture' => true,
    ],
    'tpoc_date_filed' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date filed',
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'tpoc_date_court_approval' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of court approval',
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'tpoc_date_payment_issued' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date payment Issued',
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'tpoc_deleted' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'TPOC Deleted?',
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'tpoc_listorder' => [
        FieldDefKeys::TABLE => 'link_tpoc',
    ],
    'con_social_security_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Social Security Number (SSN)',
        FieldDefKeys::TABLE => 'contacts_main',
        'MaxLength' => 11,
    ],
    'con_middle_name' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Middle name',
        FieldDefKeys::TABLE => 'contacts_main',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    // FROI
    'employee_state_hired' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Employee State of Hire',
        'Width' => 2,
        'MaxLength' => 2,
        'UpperCase' => true,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'employment_termination_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Termination Date',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'full_pay_injury_day' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Full pay for day of injury?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'salary_continued' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Did salary continue?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],

    'con_employment_status_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Employment Status Code',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_process_level' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Process Level',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_job_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Job code',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_supervisor_name' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Supervisor Name',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_department' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Department',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_location_code' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Location Code',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_fte' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'FTE',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'con_lawson_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Lawson Number',
        FieldDefKeys::TABLE => 'contacts_main',
        'MaxLength' => 255,
    ],
    // SROI
    'employer_paid_salary_as_compensation' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Employer Paid Salary in Lieu of Compensation',
        FieldDefKeys::TABLE => 'link_contacts',
        'allowUnknownValue' => true,
    ],
    'date_maximum_medical_improvement' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of Maximum Medical Improvement',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'date_claim_admin_knew_lost_time' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date Claim Administrator Had Knowledge of Lost Time',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'maintenance_type_code_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Maintenance Type Code Date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'maintenance_type_correction_code_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Maintenance Type Correction Code Date',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    'employee_id_assigned_by_jurisdiction' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Employee ID Assigned by Jurisdiction',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'dum_injury' => [
        FieldDefKeys::TABLE => 'link_injuries',
    ],
    'dum_bodypart' => [
        FieldDefKeys::TABLE => 'link_injuries',
    ],
    'dum_death_result' => [
        FieldDefKeys::TABLE => 'link_injuries',
    ],
    'dum_impaired_percent' => [
        FieldDefKeys::TABLE => 'link_injuries',
    ],
    'dum_treatment' => [
        FieldDefKeys::TABLE => 'link_injuries',
    ],
    // LINK
    'link_marriage' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Marital status',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    RedressFields::IS_PERSON_AFFECTED_ALSO_CLAIMANT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Is the person affected also the claimant?',
        FieldDefKeys::TABLE => 'redress_main',
    ],
    // SAFEGUARDING
    ContactsFields::APC_HAS_EMPLOYMENT_CHILD_CONTACT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does the alleged person of concern have any contact with children in any employment role?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::APC_AWARE_REPORT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Is the alleged person of concern aware of the report?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::APC_AAR => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Is alleged person of concern an adult at risk?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::APC_EMPLOYMENT_ADULT_CONTACT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => 'Does the alleged person of concern have any contact with adults in any employment role?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::REFERRAL_SUBJECT_CHILD => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is the subject of the referral a child?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::INTERPRETER_USED => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Was an interpreter used?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::CHILD_WITNESS => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is the witness a child?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::WITNESS_AWARE_REPORT => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Is witness aware of report?',
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::LINK_ORGANISATION => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => 'Organisation/Employer',
        'MaxLength' => 256,
        FieldDefKeys::TABLE => 'link_contacts',
    ],
    ContactsFields::SOURCE_OF_RECORD => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Source Of Record',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    ContactsFields::API_SOURCE => [
        FieldDefKeys::TYPE => FieldInterface::HIDDEN_DB,
        FieldDefKeys::TITLE => 'API Source',
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    // CONTACT MATCHING
    ContactsFields::CON_KNOW_ID_FIELD => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        FieldDefKeys::TITLE => "Do you have the contact's ID number?",
        FieldDefKeys::TABLE => 'link_contacts',
        'NoSearch' => true,
        'NoListCol' => true,
    ],
];

$FieldDefs['ACT'] = [
    'act_dstart' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Start date',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'act_ddue' => [
        'Type' => 'date',
        FieldDefKeys::NOT_EARLIER_THAN => ['act_dstart'],
        FieldDefKeys::TITLE => 'Due date',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'act_priority' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Priority',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Action Type',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'act_descr' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Title',
        'MaxLength' => 4000,
        'Width' => 47,
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'status' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Status',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'act_synopsis' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Description',
        'Rows' => 5,
        'Columns' => 70,
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'act_module' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Module',
        'NoMandatory' => true,
        'ReadOnly' => true,
        'NoListCol' => true,
    ],
    'act_cas_id' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Module ID',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'recordid' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'location_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Location',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'location',
    ],
    'service_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Service',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'service',
    ],
    'act_from_inits' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Assigned by',
        FieldDefKeys::TABLE => 'ca_actions',
        'StaffField' => true,
    ],
    'act_to_inits' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => 'Assigned to',
        FieldDefKeys::TABLE => 'ca_actions',
        'StaffField' => true,
    ],
    'completed_date' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Completed Date',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'completed_by' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Completed By',
        FieldDefKeys::TABLE => 'ca_actions',
    ],
    'name' => [
        FieldDefKeys::TITLE => 'Record name',
        'NoListCol_Override' => true,
    ],
];

$FieldDefs['DOC'] = [
    'doc_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Document type',
    ],
    'doc_notes' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Description',
        'Width' => 50,
        'MaxLength' => 128,
    ],
];

$limitForMmsea = $registry->getParm('SHOW_MMSEA_FIELDS', 'N')->isTrue();

$FieldDefs['AST'] = [
    'ast_name' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Brand name',
        'Width' => 70,
        'MaxLength' => ($limitForMmsea ? 40 : 254),
    ],
    'ast_ourref' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Ref.',
        'Width' => 32,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'location_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Location',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'location',
    ],
    'service_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Service',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'mapperType' => 'service',
    ],
    'ast_type' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Type',
    ],
    'ast_model' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Model',
    ],
    'ast_manufacturer' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Manufacturer',
    ],
    'ast_supplier' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Supplier',
    ],
    'ast_catalogue_no' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Catalogue no.',
        'Width' => 32,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'ast_batch_no' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Batch no.',
        'Width' => 32,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'ast_serial_no' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Serial no.',
        'Width' => 32,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'ast_dmanufactured' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of manufacture',
        'NotFuture' => true,
    ],
    'ast_dputinuse' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::TITLE => 'Date put in use',
    ],
    'ast_dlastservice' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of last service',
        'NotFuture' => true,
    ],
    'ast_dnextservice' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Date of next service',
    ],
    'ast_cemarking' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'CE marking?',
    ],
    'ast_quantity' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'Quantity',
        'Width' => 5,
    ],
    'ast_descr' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Description',
        'Rows' => 7,
        'Columns' => 70,
    ],
    'ast_category' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Property category',
    ],
    'notes' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Notes',
        'Rows' => 14,
        'Columns' => 70,
        'NoListCol_Override' => true,
    ],
    'recordid' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
    ],
    'rep_approved' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Approval status',
        'Width' => 32,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'link_product_liability_indicator' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Product liability indicator',
    ],
    'ast_generic_name' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Generic name',
        'Width' => 70,
        'MaxLength' => 40,
    ],
    'link_product_alleged_harm' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Product alleged harm',
        'Width' => 70,
        'MaxLength' => 200,
    ],
];

$FieldDefs['USE'] = [      // used for user forms
    'recordid' => [
        'Type' => 'number',
        FieldDefKeys::TITLE => 'ID',
        'ReadOnly' => true,
        'Width' => 5,
    ],
    'service_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Service(s)',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'MaxLength' => 128,
        'mapperType' => 'service',
    ],
    'location_id' => [
        FieldDefKeys::TYPE => FieldInterface::TREE_DB,
        FieldDefKeys::TITLE => 'Location(s)',
        FieldDefKeys::REQUIRE_MIN_CHARS => true,
        'MaxLength' => 128,
        'mapperType' => 'location',
    ],
    'show_document' => [
        'Type' => 'checkbox',
        FieldDefKeys::TITLE => 'Are there any documents to be attached to this record?',
        'NoListCol' => true,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_language' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        FieldDefKeys::TITLE => 'Language',
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_email' => [
        'Type' => 'email',
        FieldDefKeys::TITLE => 'E-mail',
        'Width' => 40,
        'MaxLength' => 128,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_number' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Patient/staff number',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'contacts_main',
    ],
    'use_surname' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Surname',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_forenames' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'First names',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_title' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Title',
        'Width' => 5,
        'MaxLength' => 10,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_tel1' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Telephone no. 1',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'isTelNumber' => true,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_tel2' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Telephone no. 2',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'isTelNumber' => true,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'positions' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Positions',
        'ReadOnly' => true,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'login' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Login name',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'NoListCol_Override' => true,
        'AlwaysMandatory' => true,
    ],
    'login_no_domain' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Login name',
        'Width' => 30,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'initials' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Initials',
        'UpperCase' => true,
        'Width' => 5,
        'MaxLength' => 6,
        'AlwaysMandatory' => true,
    ],
    'sta_title' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Title',
        'Width' => 30,
        'MaxLength' => 64,
    ],
    'sta_forenames' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Forenames',
        FieldDefKeys::TABLE => 'users_main',
        'Width' => 30,
        'MaxLength' => 64,
    ],
    'sta_surname' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Surname',
        'Width' => 30,
        'MaxLength' => 64,
        FieldDefKeys::TABLE => 'users_main',
    ],
    'use_jobtitle' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Job title',
        FieldDefKeys::TABLE => 'users_main',
        'Width' => 30,
        'MaxLength' => 64,
    ],
    'email' => [
        'Type' => 'email',
        FieldDefKeys::TITLE => 'E-mail',
        'Width' => 30,
        'MaxLength' => 128,
    ],
    'use_staff_include' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => 'Include in staff fields?',
        FieldDefKeys::TABLE => 'users_main',
    ],
    'sta_last_login' => [
        'Type' => 'date',
        FieldDefKeys::TITLE => 'Last login date',
    ],
    'sta_profile' => [
        'NullZeros' => true,
        FieldDefKeys::TITLE => 'Profile',
    ],
    'use_dopened' => [
        'Type' => 'date',
        'NotFuture' => true,
        'ReadOnly' => true,
        FieldDefKeys::TITLE => 'Opened',
    ],
    'use_dclosed' => [
        'Type' => 'date',
        'NotFuture' => true,
        FieldDefKeys::NOT_EARLIER_THAN => ['use_dopened'],
        FieldDefKeys::TITLE => 'Closed',
    ],
];

$FieldDefs['ADM'] = [
    // profile fields
    'recordid' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'ReadOnly' => true,
        FieldDefKeys::TITLE => 'ID',
        FieldDefKeys::TABLE => 'profiles',
    ],
    'pfl_name' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        FieldDefKeys::TITLE => 'Name',
        'Width' => 70,
        'NoListCol_Override' => true,
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        FieldDefKeys::TABLE => 'profiles',
    ],
    'pfl_description' => [
        'Type' => 'textarea',
        FieldDefKeys::TITLE => 'Description',
        'Rows' => 7,
        'NoListCol_Override' => true,
        'Columns' => 70,
        FieldDefKeys::TABLE => 'profiles',
    ],
    'profile_select' => [
        'Type' => 'multilistbox',
        FieldDefKeys::TITLE => _fdtk('profile_select_title'),
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
        'TitleExtra' => _fdtk('profile_select_title_extra'),
    ],
    'LOGOUT_DEFAULT_MODULE' => [
        'Type' => 'moduleselect',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('module_at_logout'),
        'Exclude' => [
            'CON',
            'ACT',
            'AST',
            'DAS',
            'MED',
            'PAY',
            'ADM',
            'ORG',
            'POL',
        ],
        'GroupModules' => true,
    ],
    'CUSTOM_REPORT_BUILDER' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('give_user_access_to_report_builder'),
    ],
    ProgNotesEditGlobal::GLOBAL_NAME => [
        'Type' => 'formfield',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('set_permissions_for_editing_progress_notes'),
    ],
    'ENABLE_GENERATE' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_cross_module_generation_of_records'),
    ],
    'ENABLE_BATCH_UPDATE' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_user_to_batch_update_records'),
    ],
    'AUTOPOPULATE_USER_LOCATION' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('autopopulate_location_field_with_reporters_location'),
    ],
    'ACT_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('profile_act_own_only'),
    ],
    'COM_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Restrict user to approving and viewing their own ' . _fdtk('COMNames'),
    ],
    'CLA_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Restrict user to approving and viewing their own ' . _fdtk('CLANames'),
    ],
    'RISK_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Restrict user to approving and viewing their own risks',
    ],
    'POL_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Restrict user to approving and viewing their own ' . _fdtk('POLNames'),
    ],
    'DIF1_ONLY_FORM' => [
        'Type' => 'formdesign',
        'FormModule' => 'INC',
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => 'DIF1 form for this user',
    ],
    'DIF2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'INC',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'DIF2 form for this user',
    ],
    'MOR2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'MOR',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'MOR2 form for this user',
    ],
    'POL2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'POL',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('pol2_user'),
    ],
    'COM2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'COM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com2_user'),
    ],
    'COM1_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => Module::FEEDBACK,
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => _fdtk('com1_user_form'),
    ],
    'CLAIM2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'CLA',
        'NoListCol_Override' => true,
        'FormLevel' => 2,
        FieldDefKeys::TITLE => _fdtk('cla2_user'),
    ],
    'RED2_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => Module::REDRESS,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red2_user'),
    ],
    'CLAIM1_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'CLA',
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => _fdtk('cla1_user'),
    ],
    'MOR1_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'MOR',
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => _fdtk('mor1_user'),
    ],
    'ADM_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'ADM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'User Admin form design',
    ],
    'RED1_DEFAULT' => [
        'Type' => 'formdesign',
        'FormModule' => 'RED',
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => _fdtk('red1_user'),
    ],
    'FULL_ADMIN' => [
        'Type' => 'yesno',
        'FormModule' => 'ADM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('full_administrator'),
    ],
    'DIF2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'INC',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'DIF2 search form for this user',
    ],
    'MOR2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'MOR',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'MOR2 search form for this user',
    ],
    'ACT_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'ACT',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Actions search form for this user',
    ],
    'POL2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'POL',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('pol2_search_form_user'),
    ],
    'COM2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'COM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com2_search_form_user'),
    ],
    'CLAIM2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'CLA',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('cla2_search_form_user'),
    ],
    'RED2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => Module::REDRESS,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red2_search_form'),
    ],
    'INC_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'INC',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('INC_listing_design_user'),
    ],
    'MOR_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'MOR',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('MOR_listing_design_user'),
    ],
    'POL_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'POL',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('POL_listing_design_user'),
    ],
    'COM_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'COM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('COM_listing_design_user'),
    ],
    'CLA_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'CLA',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('CLA_listing_design_user'),
    ],

    'ADM_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'ADM',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('ADM_listing_design_user'),
    ],
    'MED_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => 'MED',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('MED_listing_design_user'),
    ],
    'RED_LISTING_ID' => [
        'Type' => 'listingdesign',
        'FormModule' => Module::REDRESS,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('RED_listing_design_user'),
    ],
    'INC_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to see audited incident data?',
    ],
    'MOR_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to see audited mortality review data?',
    ],
    'POL_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to see audited ' . _fdtk('POLName') . ' data?',
    ],
    'COM_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to see audited feedback data?',
    ],
    'CLA_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to see audited claims data?',
    ],
    'RED_SHOW_AUDIT' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red_show_audit'),
    ],
    'ADM_GROUP_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to create and edit security groups?',
    ],
    'ADM_CAN_DELEGATE_PERMISSIONS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('adm_can_delegate_permissions'),
    ],
    'ADM_CAN_DELEGATE_ACCOUNTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('adm_can_delegate_accounts'),
    ],
    'ADM_NO_ADMIN_REPORTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('adm_no_admin_reports'),
    ],
    'DIF_SHOW_REJECT_BTN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_reject_button'),
    ],
    \app\consts\Globals::INC_BYPASS_MANDATORY_FIELDS => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('inc_bypass_mandatory_fields'),
    ],
    'DIF2_HIDE_CONTACTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('hide_contacts_tab'),
    ],
    'DIF_OWN_ONLY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Restrict user to approving and viewing their own incidents',
    ],
    'CON_SHOW_REJECT_BTN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Enable the reject status for this user',
    ],
    'CON_ALLOW_MERGE_DUPLICATES' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Enable this user to merge duplicate ' . _fdtk('CONNames'),
    ],
    'INC_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'MOR_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'POL_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'COM_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'CLA_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'ACT_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'AST_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'CON_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'MED_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to set up codes for this module?',
    ],
    'RED_SETUP' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red_setup'),
    ],
    'PAY2_SEARCH_DEFAULT' => [
        'Type' => 'searchformdesign',
        'FormModule' => 'PAY',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'PAY2 search form for this user',
    ],
    'DELETE_PAY' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete records in this module?',
    ],
    'COPY_INCIDENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'COPY_COM' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'COPY_CLA' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'COPY_MOR' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'COPY_RED' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'COPY_POL' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('allow_copy_option'),
    ],
    'DELETE_ACT_CHAIN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete action plans?',
    ],
    'CLA_FINANCE_UDFS' => [
        'Type' => 'multilistbox',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'CON_PAS_CHK_FIELDS' => [
        'Type' => 'multilistbox',
        'MaxLength' => 91,
    ],
    // restricted to 13 codes, for some reason... (see Source/AdminSetup.php)
    'EXTERNAL_CONTACTS_REQUEST_FIELDS' => [
        'Type' => 'multilistbox',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'EXTERNAL_CONTACTS_RESPONSE_FIELDS' => [
        'Type' => 'multilistbox',
        FieldDefKeys::MAX_LENGTH => FieldInterface::DEFAULT_STRING_MAX_LENGTH,
    ],
    'EXTERNAL_CONTACTS_REMOTE_ID' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'MaxLength' => 128,
    ],
    'EXTERNAL_CONTACTS_AUTH_STRATEGY' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'MaxLength' => 128,
    ],
    'EXTERNAL_CONTACTS_PASSWORD' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'Password' => true,
        'MaxLength' => 256,
    ],
    'SPSC_WEB_PORTAL_API_URL' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'SPSC_WEB_PORTAL_API_PASSWORD' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'Password' => true,
        'MaxLength' => 256,
    ],
    'SPSC_WEB_PORTAL_API_USERNAME' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'SPSC_PATIENT_APP_API_URL' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'SPSC_PATIENT_APP_API_PASSWORD' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'Password' => true,
        'MaxLength' => 256,
    ],
    'SPSC_PATIENT_APP_API_USERNAME' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'SPSC_SFDA_EXPORT_API_URL' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'SPSC_SFDA_EXPORT_API_PASSWORD' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'Password' => true,
        'MaxLength' => 256,
    ],
    'SPSC_SFDA_EXPORT_API_USERNAME' => [
        FieldDefKeys::TYPE => FieldInterface::STRING_DB,
        'MaxLength' => 256,
    ],
    'INC_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete Incidents documents?',
    ],
    'COM_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete Feedback documents?',
    ],
    'CLA_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete Claims documents?',
    ],
    'MOR_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('mor_delete_documents'),
    ],
    'AST_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete Equipment documents?',
    ],
    'POL_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => 'Allow user to delete ' . _fdtk('POLNameTitle') . ' documents?',
    ],
    'RED_DELETE_DOCS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red_delete_documents'),
    ],
    'INC_SAVED_QUERIES_HOME_SCREEN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    'COM_SAVED_QUERIES_HOME_SCREEN' => [
        'Type' => 'yesno',
        'MaxLength' => 70,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    'CLA_SAVED_QUERIES_HOME_SCREEN' => [
        'Type' => 'yesno',
        'MaxLength' => 70,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    'MOR_SAVED_QUERIES_HOME_SCREEN' => [
        'Type' => 'yesno',
        'MaxLength' => 70,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    'RED_SAVED_QUERIES_HOME_SCREEN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    'COM_SHOW_ADD_NEW' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com_show_add_new'),
    ],
    'COM_KO41_CAN_GENERATE_REPORT' => [
        'Type' => 'yesno',
        FieldDefKeys::TITLE => _fdtk('com_ko41_can_generate_report'),
    ],
    'rule_delegate_id' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'MaxLength' => 6,
        FieldDefKeys::TITLE => _fdtk('delegation_delegated_to'),
    ],
    'rule_created_by' => [
        FieldDefKeys::TYPE => FieldInterface::CODE_DB,
        'MaxLength' => 6,
        FieldDefKeys::TITLE => _fdtk('delegation_created_by'),
    ],
    'rule_module_id' => [
        'Type' => 'multilistbox',
        'MaxLength' => 8000,
        FieldDefKeys::TITLE => _fdtk('delegation_modules'),
    ],
    'COM_SHOW_REJECT_BTN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_reject_button'),
    ],
    'INC_CAN_DELETE_OWN_DOCUMENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('inc_can_delete_own_documents'),
    ],
    'COM_CAN_DELETE_OWN_DOCUMENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com_can_delete_own_documents'),
    ],
    'CLA_CAN_DELETE_OWN_DOCUMENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('cla_can_delete_own_documents'),
    ],
    'MOR_CAN_DELETE_OWN_DOCUMENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('mor_can_delete_own_documents'),
    ],
    'RED_CAN_DELETE_OWN_DOCUMENTS' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('red_can_delete_own_documents'),
    ],
    'SHOW_REOPEN' => [
        'Type' => 'yesno',
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com_can_reopen_feedback_chain'),
    ],
    'EDITABLE_CHAIN_DUE_DATES' => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('com_editable_chain_due_dates'),
    ],
    SafeguardingAdminFields::DEFAULT_LEVEL_1_FORM => [
        FieldDefKeys::TYPE => FieldInterface::FORM_DESIGN,
        'FormModule' => Module::SAFEGUARDING,
        'NoListCol_Override' => true,
        'FormLevel' => 1,
        FieldDefKeys::TITLE => _fdtk('sfg1_default'),
    ],
    SafeguardingAdminFields::DEFAULT_LEVEL_2_FORM => [
        FieldDefKeys::TYPE => FieldInterface::FORM_DESIGN,
        'FormModule' => Module::SAFEGUARDING,
        'NoListCol_Override' => true,
        'FormLevel' => 2,
        FieldDefKeys::TITLE => _fdtk('sfg2_default'),
    ],
    SafeguardingAdminFields::DEFAULT_SEARCH_FORM => [
        FieldDefKeys::TYPE => FieldInterface::SEARCH_FORM_DESIGN,
        'FormModule' => Module::SAFEGUARDING,
        'NoListCol_Override' => true,
        'FormLevel' => 2,
        FieldDefKeys::TITLE => _fdtk('search_form_design'),
    ],
    SafeguardingAdminFields::ENABLE_PINNED_QUERIES => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('enable_saved_queries_home_screen'),
    ],
    SafeguardingAdminFields::SELECT_PINNED_QUERIES => [
        FieldDefKeys::TYPE => FieldInterface::MULTI_SELECT_DB,
        FieldDefKeys::TITLE => _fdtk('choose_saved_queries'),
        'MaxLength' => 70,
    ],
    SafeguardingAdminFields::ENABLE_RECORD_COPY => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('sfg_copy_record'),
    ],
    SafeguardingAdminFields::SHOW_AUDIT => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('sfgconfig_audit'),
    ],
    SafeguardingAdminFields::SHOW_ADD_NEW => [
        FieldDefKeys::TYPE => FieldInterface::YESNO_DB,
        'NoListCol_Override' => true,
        FieldDefKeys::TITLE => _fdtk('sfg_show_add_new'),
    ],
];

loadGenericModuleFieldDefs();

foreach ($FieldDefs as $main_module => $module_fields) {
    foreach ($module_fields as $field_name => $field_def) {
        $FieldDefs[$main_module][$field_name]['Module'] = $main_module;
    }
}

// Ensure FieldDefsExtra has at least all the information FieldDefs does.
$FieldDefsExtra = $FieldDefs;
// Add linked module fields to main module array definitions for searching etc.
$FieldDefsExtra['INC'] = mergeLinkedFieldDefs($FieldDefs['INC'], [
    $FieldDefs['MED'],
    $FieldDefs['CON'],
    $FieldDefs['PAY'],
    $FieldDefs['DOC'],
    $FieldDefs['EQU'],
]);
$FieldDefsExtra['CLA'] = mergeLinkedFieldDefs($FieldDefs['CLA'], [
    $FieldDefs['CON'],
    $FieldDefs['PAY'],
    $FieldDefs['DOC'],
]);
$FieldDefsExtra['COM'] = mergeLinkedFieldDefs($FieldDefs[Module::FEEDBACK], [
    $FieldDefs[Module::PAYMENTS],
    $FieldDefs['DOC'],
    $FieldDefs[Module::CONTACTS],
    $FieldDefs[Module::MEDICATIONS],
]);
$FieldDefsExtra['MOR'] = mergeLinkedFieldDefs($FieldDefs['MOR'], [
    $FieldDefs['MED'],
    $FieldDefs['DOC'],
]);
$FieldDefsExtra['CON'] = mergeLinkedFieldDefs($FieldDefs['CON'], [
    $FieldDefs['INC'],
    $FieldDefs['ADM'],
    $FieldDefs['COM'],
    $FieldDefs['CLA'],
    $FieldDefs['DOC'],
]);
$FieldDefsExtra['AST'] = mergeLinkedFieldDefs($FieldDefs['AST'], [
    $FieldDefs['INC'],
    $FieldDefs['DOC'],
]);
$FieldDefsExtra['POL'] = mergeLinkedFieldDefs($FieldDefs['POL'], [
    $FieldDefs['DOC'],
]);
$FieldDefsExtra['USE'] = mergeLinkedFieldDefs($FieldDefs['USE'], [
    $FieldDefs['INC'],
    $FieldDefs['ADM'],
    $FieldDefs['COM'],
    $FieldDefs['CLA'],
]);
$FieldDefsExtra['ADM'] = mergeLinkedFieldDefs($FieldDefs['ADM'], [
    $FieldDefs['CON'],
    $FieldDefs['USE'],
]);
$FieldDefsExtra[Module::REDRESS] = mergeLinkedFieldDefs($FieldDefs[Module::REDRESS], [
    $FieldDefs[Module::CONTACTS],
    $FieldDefs[Module::PAYMENTS],
    $FieldDefs['DOC'],
]);
