<?php

declare(strict_types=1);

namespace Source\dashboards\Services;

use RuntimeException;
use Source\dashboards\Repositories\WidgetRepository;
use src\framework\session\UserSession;

use function in_array;

class WidgetService
{
    private WidgetRepository $repository;
    private UserSession $userSession;

    public function __construct(WidgetRepository $repository, UserSession $userSession)
    {
        $this->repository = $repository;
        $this->userSession = $userSession;
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function userHasAccess(int $userId, int $widgetId): bool
    {
        if (in_array($widgetId, $this->repository->getOwnersFromUsersDashboards($userId), true)) {
            return true;
        }

        return in_array($widgetId, $this->repository->getOwnersByUserId($userId), true);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function delete(int $id): void
    {
        if (!$this->userHasAccess($this->userSession->getUserId(), $id)) {
            throw new RuntimeException('Unauthorised delete action');
        }

        $this->repository->delete($id);
    }
}
