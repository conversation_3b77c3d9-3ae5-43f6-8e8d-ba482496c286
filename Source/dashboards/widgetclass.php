<?php

use src\framework\controller\Loader;
use src\framework\registry\Registry;
use src\reports\controllers\ReportWriter;
use src\reports\model\packagedreport\PackagedReport;
use src\reports\model\packagedreport\PackagedReportModelFactory;
use src\reports\model\report\Report;
use src\security\CompatEscaper;
use src\system\container\facade\Container;

class Widget
{
    public $Recordid;
    public $Type;
    public $Title;
    public $Data;
    public $ContentId;

    /** @var PackagedReport|null */
    public $Report;
    public ?Dashboard $DashboardObj = null;
    private bool $isInitialised = false;

    /**
     * @desc Basic constructor - seperate from the initialise method in case we
     * want to use a method without calling a load of uneccesary setup functions.
     *
     * @param int $Recordid The recordid of the record that this object represents in reports_dash_links
     */
    public function __construct($Recordid)
    {
        $this->Recordid = $Recordid;
    }

    public function setRecordId(int $id): void
    {
        $this->Recordid = $id;
    }

    /**
     * @desc Populates parameters from database and passed values.
     *
     * @param Dashboard $DashboardObj Reference to the dashboard object in which this widget is contained. A bit messy, but we want to be able to get "parent" parameters from the dashboard object
     *
     * @throws MapperException
     */
    public function InitialiseWidget(Dashboard $DashboardObj): void
    {
        if ($this->isInitialised) {
            return;
        }

        $data = $DashboardObj->Data['widgetData'][$this->Recordid] ?? null;
        if ($data === null) {
            $sql = 'SELECT recordid, dlink_dash_id, dlink_title, dlink_package_id, dlink_row_limit, dlink_col_limit, dlink_collapse_headings FROM reports_dash_links WHERE recordid = ' . $this->Recordid;

            $data = DatixDBQuery::PDO_fetch($sql);
        }

        $this->Data = $data;

        $this->Title = $data['dlink_title'];
        $this->ContentId = $data['dlink_package_id'];

        $this->DashboardObj = $DashboardObj;

        $packagedReport = null;
        if ($this->ContentId) {
            $factory = new PackagedReportModelFactory();
            $packagedReport = $factory->getMapper()->find($this->ContentId);

            if ($packagedReport->name) {
                $this->Title = $packagedReport->name;
            }
        }

        if (!$this->Title) {
            $this->Title = '&lt;untitled&gt;';
        }

        $this->Report = $packagedReport;
        $this->isInitialised = true;
    }

    /**
     * @desc Checks whether the currently logged in user is the creator of the report held by this widget
     *
     * @return bool true if the current user is the creator, false otherwise
     */
    public function CurrentUserIsOwner()
    {
        return $this->Report->createdby == $_SESSION['initials'];
    }

    /**
     * @desc Constructs the HTML for the frame of the widget and returns it.
     *
     * @return string the widget HTML
     */
    public function GetWidgetHTML()
    {
        $height = 'auto';
        $reportTypeClass = false;

        if (isset($this->Report) && $this->Report->getReport() instanceof Report) {
            // Change the widget height depending on the report type
            $reportType = $this->Report->getReport()->getReportType();
            switch ($reportType) {
                case Report::TRAFFIC:
                    $height = '130px';

                    break;
                case Report::LISTING:
                    $height = '600px';

                    break;
                default:
                    $height = '300px';
            }

            $spcCharts = [
                Report::SPC_C_CHART,
                Report::SPC_I_CHART,
                Report::SPC_MOVING_RANGE,
                Report::SPC_RUN_CHART,
            ];

            $reportTypeClass = 'spc_chart';
            if (!in_array($reportType, $spcCharts, true)) {
                $reportTypeClass = $this->Report->getReport()->getReportClassName();
            }
        }

        $HTML = '<div class="widget' . ($reportTypeClass ? ' ' . $reportTypeClass : '') . '" id="widget_' . $this->Recordid . '">
            <div class="widget_header" id="widget_header_' . $this->Recordid . '">

            <span style="display:block; width:90%; margin: 0 auto;" class="widget_title" id="widget_title_' . $this->Recordid . '">' . CompatEscaper::encodeCharacters($this->Title) . '</span>';

        $HTML .= $this->GetSettingsMenuHTML();

        $HTML .= '
            </div>
            <div class="widget-viewport">
                <div class="widget_wait" id="widget_wait_' . $this->Recordid . '">
                ' . _fdtk('dashboard_please_wait') . '
                </div>
                <div class="widget_body" id="widget_body_' . $this->Recordid . '" style="height: ' . $height . ';">';

        // use AJAX to populate the body of the widget.
        // TODO this could be done by sending the various parameters as an array of each widget through to JS using the asset manager and then using that to initiate each widget in one place rather than rendering each on on the page, as that's icky.
        $HTML .= '
                <script language="JavaScript" type="text/javascript">
                    function addWidget_' . $this->Recordid . '() {

                        var newjQueryWidget = jQuery(\'#widget_' . $this->Recordid . '\').datixWidget({

                            id: ' . ($this->Recordid ?: 'null') . ',
                            reportId: ' . ($this->Report->recordid ?: 'null') . ',
                            dashboardId: ' . ($this->DashboardObj->Recordid ?: 'null') . ',
                            title: "' . addslashes(CompatEscaper::encodeCharacters($this->Title)) . '",
                            editable: ' . ($this->widgetIsEditable() ? 'true' : 'false') . ',
                            module: "' . (isset($this->Report) ? $this->Report->getReport()->module : '') . '"
                        });

                        DashboardObjects[' . $this->DashboardObj->Recordid . '].addWidget(newjQueryWidget);
                    }
                </script>
            ';

        // We could use this line instead if we didn't want to use ajax for population.
        // $HTML .= $this->GetWidgetBodyHTML();

        $HTML .= '</div></div></div>';

        return $HTML;
    }

    public function GetSettingsMenuHTML()
    {
        $dashboardUserPermission = $this->DashboardObj->getUserHasPermission();

        if ($this->Report instanceof PackagedReport) {
            $reportType = null;
            $baseReport = $this->Report->getReport();
            if ($baseReport !== null) {
                $reportType = $baseReport->getReportType();
            }

            $canExport = !in_array($reportType, [Report::TRAFFIC, Report::GAUGE], true)
                && !Container::get(Registry::class)->getDeviceDetector()->isTablet();

            switch ($reportType) {
                case Report::CROSSTAB:
                    $exportMode = 'crosstab';

                    break;
                case Report::LISTING:
                    $exportMode = 'listing';

                    break;
                default:
                    $exportMode = 'graph';

                    break;
            }

            $ExportButtonLink = "index.php?action=httprequest&type=getExportFormHTML&exportmode={$exportMode}&recordid={$this->Report->recordid}";

            // We need to pass to function exportReport both the reportId and the widgetId because when we are
            // exporting to PDF we use the widgetId and when we are exporting to Excel we use the reportId.
            // TODO: Make a consistent way of exporting to PDF and Excel from the dashboard and from the full screen views
            $btn_export = _fdtk('btn_export');
            $btn_cancel = _fdtk('btn_cancel');
            $module = $this->Report->report->module;

            $exportJS = <<<js
                var buttons = new Array();
                var rArray = new Array();
                buttons[0]={'value':'{$btn_export}','onclick':'exportReport({$reportType},{$this->Report->recordid},{$this->Recordid},\'{$module}\');'};
                buttons[1]={'value':'{$btn_cancel}','onclick':'GetFloatingDiv(\'exportoptions\').CloseFloatingControl();'};

                PopupDivFromURL('exportoptions', 'Export', '{$ExportButtonLink}', '', buttons, '');
                js;

            $HTML =
                '<span class="settings_link">
                    <ul class="dropdown">
                        <li><img alt="' . _fdtk('dashboard_settings') . '" src="images/wrench.png">
                            <ul class="sub_menu">';


            $isCustomReportBuilderEnabled = Container::get(Registry::class)->getParm('CUSTOM_REPORT_BUILDER', 'Y')->isTrue();

            // only show the edit button if the user has access to custom report builder
            if ($this->DashboardObj->CurrentUserIsOwner() && (GetParm('DAS_PERMS') == 'DAS_FULL' || $dashboardUserPermission) && $isCustomReportBuilderEnabled && !$this->DashboardObj->ImportId) {
                $HTML .= '<li onClick="WidgetObjects[' . $this->Recordid . '].Edit(); jQuery(this).trigger(\'mouseleave\');">' . _fdtk('dashboard_menu_edit') . '</li>';
            }

            $HTML .= '
                            <li onclick="WidgetObjects[' . $this->Recordid . '].Initialise(); jQuery(this).trigger(\'mouseleave\');">' . _fdtk('dashboard_menu_refresh') . '</li>';

            // don't want to give this option when looking at other user's dashboards.
            if ($this->DashboardObj->CurrentUserIsOwner() && (GetParm('DAS_PERMS') == 'DAS_FULL' || $dashboardUserPermission) && !$this->DashboardObj->ImportId) {
                $HTML .= '<li onclick="WidgetObjects[' . $this->Recordid . '].Delete(); jQuery(this).trigger(\'mouseleave\');">' . _fdtk('dashboard_menu_delete') . '</li>';
            }

            if ($canExport) {
                $HTML .= '<li onclick="' . $exportJS . '">' . _fdtk('dashboard_menu_export') . '</li>';
            }

            // don't want to give this option when looking at other user's dashboards.
            if ($this->DashboardObj->CurrentUserIsOwner() && (GetParm('DAS_PERMS') == 'DAS_FULL' || $dashboardUserPermission)) {
                // Get user's own dashboards
                if (empty($_SESSION['dashboards.user_dashboards'])) {
                    $sql = <<<'SQL'
                        SELECT rd.recordid, rd.dash_name
                        FROM reports_dashboards rd
                        INNER JOIN reports_dashboards_owners rdo ON rd.recordid = rdo.dash_id
                        WHERE rdo.owner_id = ?
                        SQL;

                    $_SESSION['dashboards.user_dashboards'] = DatixDBQuery::PDO_fetch_all($sql, [$_SESSION['contact_login_id']]);
                }

                $UserDashboards = $_SESSION['dashboards.user_dashboards'];

                if (count($UserDashboards) > 1 && !$this->DashboardObj->ImportId) {
                    $HTML .= '<li>' . _fdtk('dashboard_menu_moveto') . '
                                    <ul>';

                    foreach ($UserDashboards as $UserDashboard) {
                        if ($UserDashboard['recordid'] != $this->DashboardObj->Recordid) {
                            $HTML .= '<li onclick="WidgetObjects[' . $this->Recordid . '].Move(' . $UserDashboard['recordid'] . '); jQuery(this).trigger(\'mouseleave\');">' . $UserDashboard['dash_name'] . '</li>';
                        }
                    }

                    $HTML .= '</ul>
                            </li>';
                }
            }

            $HTML .= '
                            </ul>
                        </li>
                    </ul>
                </span>';
        }
        // Give ability to delete an item that has no associated package report if user is owner and has full permissions
        elseif ($this->Report == null && $this->DashboardObj->CurrentUserIsOwner() && GetParm('DAS_PERMS') == 'DAS_FULL') {
            $HTML = '
<span class="settings_link">
    <ul class="dropdown">
        <li><img alt="' . _fdtk('dashboard_settings') . '" src="images/wrench.png">
            <ul class="sub_menu">
                <li onclick="WidgetObjects[' . $this->Recordid . '].Delete()">' . _fdtk('dashboard_menu_delete') . '</li>
            </ul>
        </li>
    </ul>
</span>';
        }

        return $HTML;
    }

    /**
     * @desc Constructs the HTML for the contents of the widget (with potential for different types of content here).
     *
     * @return string the HTML of the body of the message
     */
    public function GetWidgetBodyHTML()
    {
        global $scripturl;

        $HTML = '';
        $reportType = null;
        $baseReport = null;
        if ($this->Report instanceof PackagedReport) {
            $baseReport = $this->Report->getReport();
            if ($baseReport !== null) {
                $reportType = $baseReport->getReportType();
            }
        }

        // Isolate the lazy load of packaged report query object and query where object.
        // We need to access these properties because they are lazy loaded on the packaged report class.
        // TODO: Remove this when we are refactoring the packaged report class to load upfront the lazy loaded properties.
        $this->Report->query;
        $this->Report->query->where;

        switch ($reportType) {
            case src\reports\model\report\Report::LISTING:
                // We need to store the title of the report on the session to be added to the stream to export to PDF
                $_SESSION['listing_title'] = $baseReport->title ?? '';

                $loader = new src\framework\controller\Loader();
                $controller = $loader->getController(['controller' => src\reports\controllers\ReportController::class]);
                $controller->setRequestParameter('packagedReport', $this->Report);
                $controller->setRequestParameter('context', ReportWriter::DASHBOARD);

                $HTML = $controller->doAction('displayReport');

                // Restructuring Session variables for Exporting from Dashboard
                $ListingReport = $_SESSION['listingreportstream'] ?? null;

                if (!is_array($ListingReport)) {
                    $_SESSION['listingreportstream'] = null;
                }

                $_SESSION['listingreportstream_dashboard'][$this->Recordid] = serialize($ListingReport);

                $crosstabtablePHPExcel = $_SESSION['crosstabtablePHPExcel'] ?? null;

                if (!is_array($crosstabtablePHPExcel)) {
                    $_SESSION['crosstabtablePHPExcel'] = null;
                }

                $_SESSION['crosstabtablePHPExcel'][$this->Recordid] = $crosstabtablePHPExcel;

                break;
            default: // standard content: a packaged graphical report.
                if (!$baseReport) {
                    $HTML .= 'Report not found.';
                } elseif ($this->Report->query->hasAtPrompt()) {
                    $HTML .= '<div style="text-anchor: middle; font-family: Verdana; font-size: 10px; text-align: center; cursor: pointer; width: auto; height: 150px; padding-top: 150px; color: #666666;" onclick="SendTo(\'' . $scripturl . '?action=reportdesigner&designer_state=closed&from_my_reports=1&recordid=' . $this->ContentId . '\');">No data to display.</div>';
                } else {
                    $isCrosstab = ($reportType == src\reports\model\report\Report::CROSSTAB ? true : false);

                    if ($isCrosstab) {
                        $HTML .= '<div style="cursor:pointer" onclick="SendTo(\'' . $scripturl . '?action=reportdesigner&designer_state=closed&recordid=' . $this->ContentId . '&from_dashboard=' . $this->DashboardObj->Recordid . '\');">';
                    }

                    $loader = new src\framework\controller\Loader();
                    $controller = $loader->getController(
                        ['controller' => src\reports\controllers\ReportController::class],
                    );
                    $controller->setRequestParameter('recordid', $this->ContentId);

                    if ($isCrosstab) {
                        $controller->setRequestParameter('isCrosstab', true);
                    }

                    $controller->setRequestParameter('context', ReportWriter::DASHBOARD);

                    $HTML .= $controller->doAction('displayReport');

                    if ($isCrosstab) {
                        $HTML .= '</div>';
                    }
                }

                break;
        }

        return $HTML;
    }

    /**
     * @desc Provides an HTML form allowing the user to edit aspects of the widget (and attached report).
     */
    public function GetEditForm()
    {
        $baseReport = $this->Report->getReport();

        $HTML = '<ol style="border:#094367 1px solid">';
        $HTML .= '<form id="designReport">';
        $HTML .= '<input type="hidden" id="module" value="' . ($baseReport->module ?? '') . '">';

        $loader = new Loader();

        $controller = $loader->getController(
            ['controller' => src\reports\controllers\ReportDesignFormController::class],
        );

        $controller->setRequestParameter('context', 'dashboard');
        $controller->setRequestParameter('module', $baseReport->module ?? '');
        $controller->setRequestParameter('packagedReport', $this->Report);

        $controller->setOutputAssets(true);

        $HTML .= $controller->doAction('reportDesignerForm');

        $HTML .= '</form>';

        $HTML .= '</ol>';

        return $HTML;
    }

    /**
     * Whether or not the widget is "editable" (i.e. the user has access to the "Save" button on the Edit Widget form).
     *
     * @return bool
     */
    protected function widgetIsEditable()
    {
        if (!$this->CurrentUserIsOwner()) {
            return false;
        }

        if (isset($this->Report) && $this->Report->getReport() instanceof Report && $this->Report->report->getReportType() == Report::LISTING) {
            return false;
        }

        return true;
    }
}
