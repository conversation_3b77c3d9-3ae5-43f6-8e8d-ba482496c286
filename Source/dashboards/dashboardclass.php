<?php

use app\models\generic\valueObjects\Module;
use app\models\help\HelpUrlGenerator;
use app\Repository\DashboardRepository;
use app\services\dashboard\DashboardService;
use src\admin\services\CapturePermissionService;
use src\framework\registry\Registry;
use src\framework\session\UserSession;

class Dashboard
{
    public ?int $Recordid = null;
    public array $Data = [];
    public ?int $ImportId = null;
    public ?int $NumCols = null;
    public ?int $ScreenWidth;
    public ?int $ScreenHeight;

    /** @var array<int,Widget> */
    public array $ColumnWidgets = [];
    public bool $EditMode = false; // bool: if true then the logged in user can edit aspects of this dashboard.

    /** @var array<int,Widget> */
    public array $WidgetArray = [];
    public array $LinkedProfiles = []; // array of profiles, the members of which will see this dashboard automatically.
    public array $LinkedGroups = []; // array of groups, the members of which will see this dashboard automatically.
    public array $LinkedUsers = []; // array of users who will see this dashboard automatically.
    public int $ColWidth = 0; // width in pixels of the columns on this dashboard - depends on screen size and number of columns.
    public bool $Hidden = true; // bool: if true, then this dashboard should not be displayed by default on pageload
    private array $owners = [];
    private ?int $contactLoginId = null;
    private DashboardService $dashboardService;
    private UserSession $userSession;
    private CapturePermissionService $permissionService;
    private Registry $registry;
    private bool $userHasPermission = false;
    private bool $isInitialised = false;

    /**
     * @desc Basic constructor - seperate from the initialise method in case we
     * want to use a method without calling a load of uneccesary setup functions.
     */
    public function __construct(
        DashboardService $dashboardService,
        UserSession $userSession,
        CapturePermissionService $permissionService,
        Registry $registry
    ) {
        $userId = $userSession->getUserId();
        if ($userId !== null) {
            $this->owners[] = $userId;
            $this->contactLoginId = $userId;
        }

        $this->dashboardService = $dashboardService;
        $this->userSession = $userSession;
        $this->permissionService = $permissionService;
        $this->registry = $registry;
    }

    /**
     * @desc Sets the recordid of the dashboard. If not passed, just picks up the default dashboard for this user.
     *
     * @param int|null $id The recordid of the record that this object represents in reports_dashboards
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function SetRecordid(?int $id = null): void
    {
        $this->Recordid = $id;

        $data = $this->dashboardService->getDashboardData($id, $this->contactLoginId);
        if ($data !== null) {
            $this->Data = $data[0];
            $this->setLinks($data);
            $this->Recordid = (int) $data[0]['recordid'];
            $this->ImportId = isset($data['import_id']) ? (int) $data['import_id'] : null;
        }

        $this->owners = $this->dashboardService->getOwners($this->Recordid);

        $this->NumCols = (int) ($this->Data['dash_graphs_per_row'] ?? $this->registry->getParm('DASH_GRAPHS_PER_ROW', 3)->toScalar());
    }

    /**
     * @desc Populates parameters from database and passed values. Calls function to initialise widgets.
     *
     * @param int|null $ScreenWidth the width of the user's screen
     * @param int|null $ScreenHeight the height of the user's screen
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function InitialiseDashboard(?int $ScreenWidth, ?int $ScreenHeight): void
    {
        if ($this->isInitialised) {
            return;
        }

        $this->ScreenWidth = $ScreenWidth;
        $this->ScreenHeight = $ScreenHeight;
        $this->userHasPermission = $this->permissionService->checkUserDashboardPermission();

        if ($this->CurrentUserIsOwner() && ($this->CurrentUserCanAdd() || $this->userHasPermission)) {
            $this->EditMode = true; // current user can edit this dashboard.
        }

        if (!$this->Recordid) {
            $this->SetRecordid();
        }

        $this->InitialiseWidgets();
        $this->isInitialised = true;
    }

    /**
     * @desc Selects all widgets linked to this dashboard and sorts them into columns according to the dlink_column field. The
     * widgets are stored in the correct order and column in the parameter "ColumnWidgets"
     */
    public function InitialiseWidgets(): void
    {
        $WidgetArray = $this->dashboardService->getWidgetsByDashboard($this->Recordid);

        $this->Data['widgetData'] = [];
        foreach ($WidgetArray as $row) {
            $this->Data['widgetData'][(int) $row['recordid']] = $row;
            if ($row['module'] && !GetAccessLevel($row['module'])) {
                // user doesn't have access to the module - don't show this graph
                continue;
            }


            // by default, will go in the first column if no column set, or if in a column outside of the current dashboard settings.
            if (!$row['dlink_column'] || $row['dlink_column'] >= $this->NumCols) {
                $row['dlink_column'] = 0;
            }

            $widget = new Widget((int) $row['recordid']);
            $this->ColumnWidgets[(int) $row['dlink_column']][] = $widget;
            $this->WidgetArray[(int) $row['recordid']] = $widget;
        }
    }

    // ---------------------
    // @codeCoverageIgnoreStart
    // ---------------------
    /**
     * @desc Constructs the HTML for the dashboard and returns it.
     *
     * @return string the HTML representing this dashboard
     */
    public function GetDashboardHTML()
    {
        $HTML = '<div class="dashboard" id="dashboard_' . $this->Recordid . '"';

        if ($this->Hidden) {
            $HTML .= ' style="display:none"';
        }

        $HTML .= '>';

        for ($i = 0; $i < $this->NumCols; ++$i) {
            $HTML .= $this->GetColumnHTML($i);
        }

        $HTML .= '<div id="empty_dashboard_message_' . $this->Recordid . '" class="empty_dashboard_message"' . (!empty($this->WidgetArray) ? ' style="display:none"' : '') . '>' . _fdtk('empty_dashboard') . '</div>';

        // needed to prevent dashboard collapsing because columns are floated.
        $HTML .= '<div style="clear:both;"></div>';

        $HTML .= '<div class="dashboard_options_link_row">';

        $HTML .= '<span class="dashboard_options_link default_dashboard_link" id="default_dashboard_link_' . $this->Recordid . '" onclick="DashboardObjects[' . $this->Recordid . '].SetAsDefault()" ' . (GetParm('DEFAULT_DASHBOARD', 0) != $this->Recordid ? '' : 'style="display:none"') . '>' . _fdtk('set_as_default_dashboard') . '</span>';

        if ($this->EditMode && !$this->ImportId) {
            $HTML .= '<span id="dashboard_options_' . $this->Recordid . '" class="dashboard_options_link" onclick="disableDashboardOptions(\'' . $this->Recordid . '\'); DashboardObjects[' . $this->Recordid . '].Edit();">' . _fdtk('dashboard_options') . '</span>';
        }

        if ($this->registry->getParm('SHOW_MODULE_HELP', 'Y')->isTrue()) {
            $HTML .= '<span id="dashboard_help" class="dashboard_options_link"><a href="' . HelpUrlGenerator::getRemoteURL('kidH7_53066_c1') . '" target="_blank">' . _fdtk('help') . '</a></span>';
        }

        $HTML .= '</div>';

        return $HTML;
    }
    // ---------------------
    // @codeCoverageIgnoreEnd
    // ---------------------

    /**
     * @desc Takes the information currently set in the dashboard object and updates the database with it. Sets values in the main dashboard table (reports_dashboards) and
     * in the dashboards publishing table (reports_dashboards_links)
     */
    public function UpdateDashboardRecord()
    {
        $Dash_graphs_per_row = ((int) $this->Data['dash_graphs_per_row'] == 0 ? null : (int) ($this->Data['dash_graphs_per_row']));

        DatixDBQuery::PDO_query('UPDATE reports_dashboards SET dash_name = :dash_name, dash_graphs_per_row = :dash_graphs_per_row WHERE recordid = :recordid', ['dash_name' => $this->Data['dash_name'], 'dash_graphs_per_row' => $Dash_graphs_per_row, 'recordid' => $this->Recordid]);

        DatixDBQuery::PDO_query('DELETE FROM reports_dashboards_links WHERE dash_id = :recordid', ['recordid' => $this->Recordid]);

        foreach ($this->LinkedProfiles as $ProfileID) {
            DatixDBQuery::PDO_build_and_insert('reports_dashboards_links', ['dash_id' => $this->Recordid, 'dash_link_type' => 'PROFIL', 'dash_link_id' => $ProfileID]);
        }
        foreach ($this->LinkedGroups as $GroupID) {
            DatixDBQuery::PDO_build_and_insert('reports_dashboards_links', ['dash_id' => $this->Recordid, 'dash_link_type' => 'GROUP', 'dash_link_id' => $GroupID]);
        }
        foreach ($this->LinkedUsers as $UserID) {
            DatixDBQuery::PDO_build_and_insert('reports_dashboards_links', ['dash_id' => $this->Recordid, 'dash_link_type' => 'USER', 'dash_link_id' => $UserID]);
        }
    }

    // ---------------------
    // @codeCoverageIgnoreStart
    // ---------------------
    /**
     * @desc Constructs the HTML for a given column and returns it.
     *
     * @param int $ColumnId The column to be represented - indicated by its key in the ColumnWidgets parameter
     *
     * @return string the HTML representing this dashboard
     */
    public function GetColumnHTML($ColumnId)
    {
        $HTML = '<div class="column" id="column_' . $this->Recordid . '_' . $ColumnId . '" style="width: 32%">';

        if (isset($this->ColumnWidgets[$ColumnId]) && is_array($this->ColumnWidgets[$ColumnId])) {
            foreach ($this->ColumnWidgets[$ColumnId] as $Widget) {
                try {
                    $Widget->InitialiseWidget($this);
                    $HTML .= $Widget->GetWidgetHTML();
                } catch (ReportNotFoundException $exception) {
                    $HTML .= '<div>' . $exception->getMessage() . '</div>';
                }
            }
        }

        $HTML .= '</div>';

        return $HTML;
    }
    // ---------------------
    // @codeCoverageIgnoreEnd
    // ---------------------

    /**
     * @desc Takes an array of widget ids and saves the order and columns back to the reports_dash_links table
     *
     * @param array $Orders an array of widget ids in the form $Orders[column][order] = widget id
     */
    public function SaveWidgetOrders($Orders)
    {
        foreach ($Orders as $Column => $OrderArray) {
            foreach ($OrderArray as $Order => $WidgetId) {
                if ($WidgetId) {
                    $sql = 'UPDATE reports_dash_links SET dlink_order = ' . $Order . ', dlink_column = ' . $Column . ' WHERE recordid = ' . $WidgetId . ' and dlink_dash_id = ' . $this->Recordid;
                    DatixDBQuery::PDO_query($sql);
                }
            }
        }
    }

    /**
     * @desc Saves a new default dashboard for the current user.
     */
    public function SaveAsNewDashboard()
    {
        // make sure we have a name
        if (empty($this->Data['dash_name'])) {
            $this->Data['dash_name'] = _fdtk('dashboard_for_user') . $this->contactLoginId;
        }

        $query = new DatixDBQuery();
        $query->beginTransaction();

        try {
            $this->Recordid = $query->buildAndInsert('reports_dashboards', [
                'dash_name' => $this->Data['dash_name'],
            ]);

            $query->buildAndInsert('reports_dashboards_owners', [
                'dash_id' => $this->Recordid,
                'owner_id' => $this->contactLoginId,
            ]);

            $query->commit();
        } catch (Throwable $exception) {
            $query->rollBack();

            throw $exception;
        }
    }

    /**
     * @desc Saves a new widget onto the current dashboard.
     *
     * @param array $Params Array of parameters
     */
    public function AddWidget($Params)
    {
        // Deal with situation where user does not have a dashboard yet:
        if (!$this->Recordid) {
            $this->SaveAsNewDashboard();
        }

        // check user can add to this dashboard.
        if (!$this->CurrentUserIsOwner() || $this->ImportId) {
            return ['success' => false, 'message' => 'You do not have permission to edit this dashboard'];
        }

        $sql = 'SELECT COALESCE(MAX(dlink_order)+1, 1) as dlink_order FROM reports_dash_links
                WHERE dlink_dash_id = ' . $this->Recordid;

        $report = DatixDBQuery::PDO_fetch($sql);

        // check this widget does not already exist.
        $sql = 'SELECT count(*) as check_dash_link FROM reports_dash_links
                WHERE dlink_dash_id = ' . $this->Recordid . '
                AND dlink_package_id = ' . $Params['report_id'];

        $row = DatixDBQuery::PDO_fetch($sql);

        if ($row['check_dash_link'] != 0) {
            return ['success' => false, 'message' => _fdtk('duplicate_report')];
        }

        $WidgetID = DatixDBQuery::PDO_build_and_insert('reports_dash_links', ['dlink_dash_id' => $this->Recordid, 'dlink_package_id' => $Params['report_id'], 'dlink_title' => ($Params['report_name'] ?? '&lt;untitled&gt;'), 'dlink_order' => $report['dlink_order'], 'dlink_column' => 0]);

        if ($WidgetID) {
            $Widget = new Widget($WidgetID);
            $Widget->InitialiseWidget($this);
            $this->ColumnWidgets[0][] = $Widget;
            $this->WidgetArray[$WidgetID] = $Widget;

            return ['success' => true, 'widget_id' => $WidgetID];
        }

        return ['success' => false, 'message' => 'Dashboard widget could not be added to the database'];
    }

    /**
     * @desc Removes the "default" flag from the user's current dashboard and sets this one as default in its place.
     */
    public function SetAsDefault()
    {
        SetUserParm($this->userSession->getCurrentLogin(), 'DEFAULT_DASHBOARD', $this->Recordid);
        $_SESSION['Globals']['DEFAULT_DASHBOARD'] = $this->Recordid;
    }

    /**
     * @desc Checks whether the currently logged in user is the owner of this dashboard.
     *
     * @return bool true if the current user is the owner, false otherwise
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function CurrentUserIsOwner(): bool
    {
        return in_array($this->contactLoginId, $this->owners, true);
    }

    public function currentUserCanAdd(): bool
    {
        $user = $this->userSession->getCurrentUser();
        if ($user === null) {
            return false;
        }

        return $user->canAddRecords(Module::DASHBOARDS, $this->registry);
    }

    /**
     * @desc Deletes the dashboard from the database and unlinks any widgets that were on it.
     */
    public function DeleteDashboard()
    {
        DatixDBQuery::static_beginTransaction(); // in case of a failure we don't want to end up with orphan data.

        // get rid of widgets
        $sql = 'DELETE FROM reports_dash_links WHERE dlink_dash_id = ' . $this->Recordid;
        $result = DatixDBQuery::PDO_query($sql);

        if ($result !== false) {
            $sql = 'DELETE FROM reports_dashboards_links WHERE dash_id = ' . $this->Recordid;
            $result = DatixDBQuery::PDO_query($sql);
        }

        if ($result !== false) {
            $sql = 'DELETE FROM reports_dashboards WHERE recordid = ' . $this->Recordid;
            $result = DatixDBQuery::PDO_query($sql);
        }

        // todo - reassign default dashboard

        if ($result === false) {
            DatixDBQuery::static_rollBack();

            return false;
        }

        DatixDBQuery::static_commit();

        return true;
    }

    /**
     * Sets the column width of this dashboard based on the screen width/height.
     */
    public function setColWidth(): void
    {
        $this->ColWidth = ($this->ScreenWidth - (60 + (10 * $this->NumCols))) / $this->NumCols; // designed by trial and error, rather than logic - which is why it doesn't work very well
    }

    public function getUserHasPermission(): bool
    {
        return $this->userHasPermission;
    }

    private function setLinks(array $links): void
    {
        foreach ($links as $row) {
            switch ($row['dash_link_type']) {
                case DashboardRepository::TYPE_GROUP:
                    $this->LinkedGroups[] = (int) $row['dash_link_id'];

                    break;
                case DashboardRepository::TYPE_PROFILE:
                    $this->LinkedProfiles[] = (int) $row['dash_link_id'];

                    break;
                case DashboardRepository::TYPE_USER:
                    $this->LinkedUsers[] = (int) $row['dash_link_id'];

                    break;
            }
        }
    }
}
