<?php

declare(strict_types=1);

namespace Source\dashboards\Repositories;

use app\Repository\DashboardRepository;
use Doctrine\DBAL\Connection;

use function GetUserSecurityGroups;

/**
 * Ignore repositories as we don't want to unit test them only mock them.
 *
 * @codeCoverageIgnore
 */
class WidgetRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /**
     * returns a list of widget ID's which the user has access to via profile, groups or direct access.
     *
     * @return int[]
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getOwnersByUserId(int $userId): array
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->addSelect('dl.recordid')
            ->from('reports_dashboards_links', 'rdl')
            ->innerJoin('rdl', 'reports_dash_links', 'dl', 'dl.dlink_dash_id = rdl.dash_id')
            ->setParameters([
                'typeUser' => DashboardRepository::TYPE_USER,
                'userId' => $userId,
            ]);

        $orWhere = ['rdl.dash_link_type = :typeUser AND rdl.dash_link_id = :userId'];
        $profileId = $this->connection->executeQuery(
            'SELECT sta_profile FROM users_main WHERE RECORDID = :id',
            ['id' => $userId],
        )->fetchOne();

        if (!empty($profileId)) {
            $orWhere[] = 'rdl.dash_link_type = :typeProfile AND rdl.dash_link_id = :profileId';
            $qb->setParameters([
                'typeProfile' => DashboardRepository::TYPE_PROFILE,
                'profileId' => $profileId,
            ]);
        }

        $groupIds = GetUserSecurityGroups($userId);
        if (!empty($groupIds)) {
            $orWhere[] = 'rdl.dash_link_type = :typeGroup AND rdl.dash_link_id  IN (:groupIds)';
            $qb->setParameter('typeGroup', DashboardRepository::TYPE_GROUP);
            $qb->setParameter('groupIds', $groupIds, Connection::PARAM_INT_ARRAY);
        }

        $qb->orWhere(...$orWhere);

        return array_map('intval', $qb->executeQuery()->fetchFirstColumn());
    }

    /**
     * returns a list of widget ID's associated to dashboard which the user owns.
     *
     * @return int[]
     *
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function getOwnersFromUsersDashboards(int $userId): array
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->addSelect('dl.recordid')
            ->from('reports_dashboards_owners', 'rdo')
            ->innerJoin('rdo', 'reports_dash_links', 'dl', 'dl.dlink_dash_id = rdo.dash_id')
            ->where('rdo.owner_id = :userId')
            ->setParameter('userId', $userId);

        return array_map('intval', $qb->executeQuery()->fetchFirstColumn());
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     */
    public function delete(int $id): void
    {
        $this->connection->executeStatement('DELETE FROM reports_dash_links WHERE recordid = :id', ['id' => $id]);
    }
}
